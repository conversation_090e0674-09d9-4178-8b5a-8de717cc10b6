# Admin Pages

I would like a new section of the customer (apps/customer) website under the route of /admin that provides administration functions (for those who have is_admin in the profiles table set to TRUE).

The following functions should be provided:

1) Manage the quotas for an organisation/individual

2) Manage feature flags for an organisation

All the feature flag types should be supported and made human useable. So support !x.y.z and x.y.\* etc. Make it friendly and easy to use.

3) Manage the members of an organisation
 -
You should be able to add or delete a user from an organisation as well as listing them. 

4) Manage the virtual entities allowed for an organisation/individual

This should be nice and user friendly with a user picking from a potentially long list of possible virtual entities and building up a list of ones allowed. Use the name not id of the virtual entity.

5) Update profile details for an individual (including is_admin)

Make this as friendly and as easy to use as possible, look in the /custom route for how profiles are handled and use that as a starting point.

6) Manage feature flags for an individual

All the feature flag types should be supported and made human useable. So support !x.y.z and x.y.\* etc. Make it friendly and easy to use.

7) Create a message in acc_messages to send to a user.

You should display an email like interface for the recipients and message to send. The admin should be able to send to multiple recipients include \* for all listed users or @example.com for all users with [example.com](http://example.com) as their email domain.

8) Add an entry in app_changelog

---

All of these should use the human readable information in the interface, such as the "John <PERSON>" not it's entity_xid or id etc. Please make it as simple as possible for someone to manage, especially when accessing multiple tables. Focus on a clean and simple implementation.

The UI should be joined up, so that all these parts are linked together to form simple easy to use interface. So when I list the users in an org I can click on them and edit the user info etc. Linking different functionality into a simple whole.

Please start by creating a new file called ISSUE_PLAN.md in the top directory with your plan of action and update that as you work on this - it's your plan, memory & todo list. Use zen planner to help you with gemini's help.
