"""
Tests for KnowledgeGraphService functionality.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from researcher.services.kg_service import KnowledgeGraphService
from eko.db.data.entity import EntityExtended


class TestKnowledgeGraphService:
    """Test cases for KnowledgeGraphService methods."""

    @patch('researcher.services.kg_service.get_bo_conn')
    @patch('researcher.services.kg_service.EntityData')
    def test_get_entity_by_id_success(self, mock_entity_data, mock_get_bo_conn):
        """Test successful entity retrieval by ID."""
        # Arrange
        mock_conn = Mock()
        mock_get_bo_conn.return_value.__enter__.return_value = mock_conn
        
        mock_entity = EntityExtended(
            id=123,
            name="Test Entity",
            type="company",
            eko_id="TEST_123"
        )
        mock_entity_data.get_by_id.return_value = mock_entity
        
        # Act
        result = KnowledgeGraphService.get_entity_by_id(123)
        
        # Assert
        assert result is not None
        assert result.id == 123
        assert result.name == "Test Entity"
        assert result.eko_id == "TEST_123"
        mock_entity_data.get_by_id.assert_called_once_with(mock_conn, 123)

    @patch('researcher.services.kg_service.get_bo_conn')
    @patch('researcher.services.kg_service.EntityData')
    def test_get_entity_by_id_not_found(self, mock_entity_data, mock_get_bo_conn):
        """Test entity retrieval when entity doesn't exist."""
        # Arrange
        mock_conn = Mock()
        mock_get_bo_conn.return_value.__enter__.return_value = mock_conn
        mock_entity_data.get_by_id.return_value = None
        
        # Act
        result = KnowledgeGraphService.get_entity_by_id(999)
        
        # Assert
        assert result is None
        mock_entity_data.get_by_id.assert_called_once_with(mock_conn, 999)

    @patch('researcher.services.kg_service.get_bo_conn')
    @patch('researcher.services.kg_service.EntityData')
    @patch('researcher.services.kg_service.logger')
    def test_get_entity_by_id_exception(self, mock_logger, mock_entity_data, mock_get_bo_conn):
        """Test entity retrieval when database exception occurs."""
        # Arrange
        mock_conn = Mock()
        mock_get_bo_conn.return_value.__enter__.return_value = mock_conn
        mock_entity_data.get_by_id.side_effect = Exception("Database error")
        
        # Act
        result = KnowledgeGraphService.get_entity_by_id(123)
        
        # Assert
        assert result is None
        mock_logger.exception.assert_called_once_with("Failed to get entity by ID: 123")

    @patch('researcher.services.kg_service.get_bo_conn')
    @patch('researcher.services.kg_service.EntityData')
    def test_update_entity_success(self, mock_entity_data, mock_get_bo_conn):
        """Test successful entity update."""
        # Arrange
        mock_conn = Mock()
        mock_get_bo_conn.return_value.__enter__.return_value = mock_conn
        
        mock_entity = EntityExtended(
            id=123,
            name="Updated Entity",
            type="company",
            eko_id="UPD_123"
        )
        mock_entity_data.update.return_value = mock_entity
        
        # Act
        result = KnowledgeGraphService.update_entity(mock_entity)
        
        # Assert
        assert result is True
        mock_entity_data.update.assert_called_once_with(mock_conn, mock_entity)

    @patch('researcher.services.kg_service.get_bo_conn')
    @patch('researcher.services.kg_service.EntityData')
    @patch('researcher.services.kg_service.logger')
    def test_update_entity_exception(self, mock_logger, mock_entity_data, mock_get_bo_conn):
        """Test entity update when database exception occurs."""
        # Arrange
        mock_conn = Mock()
        mock_get_bo_conn.return_value.__enter__.return_value = mock_conn
        mock_entity_data.update.side_effect = Exception("Database error")
        
        mock_entity = EntityExtended(
            id=123,
            name="Test Entity",
            type="company",
            eko_id="TEST_123"
        )
        
        # Act
        result = KnowledgeGraphService.update_entity(mock_entity)
        
        # Assert
        assert result is False
        mock_logger.exception.assert_called_once_with("Failed to update entity: Test Entity")

    def test_import_kg_service(self):
        """Test that KnowledgeGraphService can be imported without errors."""
        assert KnowledgeGraphService is not None
        assert hasattr(KnowledgeGraphService, 'get_entity_by_id')
        assert hasattr(KnowledgeGraphService, 'update_entity')