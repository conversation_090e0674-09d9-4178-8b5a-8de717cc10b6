"""
# OpenTelemetry Custom Metrics for ADK Entity Crawler Observability System

This module provides **comprehensive custom OpenTelemetry metrics that complement Google ADK's built-in 
observability capabilities** for the EkoIntelligence Entity Crawler system. The module implements 
**domain-specific business metrics focused on entity discovery, relationship mapping, and knowledge graph 
construction** that operate alongside ADK's automatic instrumentation of agent runs, tool calls, and LLM interactions.

## Business Purpose and Strategic Integration

**Primary Business Functions within ESG Analysis Pipeline**:
- **Entity Discovery Tracking**: Comprehensive metrics for corporate entity discovery across SEC, GLEIF, Companies House, and web sources
- **Relationship Mapping Quality**: Quantitative measurement of entity relationship discovery, confidence scoring, and mapping completeness
- **Knowledge Graph Growth**: Real-time tracking of knowledge graph expansion, entity count evolution, and data quality assessment
- **Crawl Performance Analytics**: Completeness scoring, data quality distribution, and comprehensive error categorization for operational excellence
- **ESG Pipeline Integration**: Metrics directly support Environmental, Social, Governance analysis workflows, corporate accountability assessment, and greenwashing detection

### Integration with ADK (Agent Development Kit) Architecture

The metrics system operates within Google's Agent Development Kit framework as a **complementary observability layer**:

**ADK Integration Architecture**:
1. **ADK Automatic Instrumentation**: Google ADK handles agent execution tracing, tool call spans, and LLM interaction telemetry
2. **Custom Domain Metrics** (This Module): Business-specific measurements for entity discovery, relationship quality, and knowledge graph analytics
3. **Phoenix Observability Platform**: Unified observability backend with GoogleADKInstrumentor for comprehensive trace and metric correlation
4. **Multi-Agent Coordination**: Metrics collection across Coordinator, CompanyAnalyzer, DocumentCrawler, RelationshipMapper, and KGUpdater agents

## Technical Architecture and OpenTelemetry Implementation

### Core Components Architecture

#### **CrawlerMetrics Class** (Primary Metrics Collector)
Implements comprehensive OpenTelemetry metrics with proper fallback handling and ADK context integration:

**Domain-Specific Metrics Categories**:
- **Entity Discovery Counter**: `crawler_entities_discovered_total` - Tracks entity discovery by type, source, and confidence buckets
- **Relationship Discovery Counter**: `crawler_relationships_discovered_total` - Measures relationship mapping volume and quality
- **Knowledge Graph Size Counter**: `crawler_knowledge_graph_entities_total` - Real-time tracking of knowledge graph growth
- **Data Quality Histogram**: `crawler_data_quality_score` - Distribution analysis of data quality assessments
- **Crawl Completeness Histogram**: `crawler_completeness_score` - Measurement of entity crawl operation completeness
- **Error Counter**: `crawler_errors_total` - Comprehensive error categorization by type, severity, and entity classification

#### **ADKMetricsIntegration Class** (Context-Aware Recording)
Provides **sophisticated integration with ADK context objects** for enhanced observability correlation:
- **ToolContext Integration**: Automatic metric recording using ADK ToolContext with invocation correlation
- **InvocationContext Correlation**: Session-aware metrics recording with ADK InvocationContext for multi-session analysis
- **Context Information Logging**: ADK context attributes (invocation_id, agent_name, session_id) are extracted for correlation but not currently persisted as metric dimensions due to cardinality management

#### **MetricsContext Manager** (Automatic Lifecycle Management)
**Context manager for automatic metrics collection** with comprehensive error handling and success/failure tracking:
- **Automatic Success/Failure Recording**: Context manager automatically records appropriate metrics based on operation outcomes
- **Exception-Safe Operation**: Proper exception handling ensures metrics recording never interferes with business logic
- **Attribute Management**: Dynamic attribute setting for enhanced metric dimensionality and filtering capabilities

### OpenTelemetry Best Practices Implementation

**Robust Error Handling and Graceful Degradation**:
- **Optional Dependencies**: OpenTelemetry metrics gracefully disabled if not available, preventing application failures
- **ADK Integration Validation**: Automatic detection of GoogleADKInstrumentor availability with warning logs for missing instrumentation
- **Comprehensive Exception Handling**: All metric recording operations wrapped in try-catch blocks with structured logging
- **Silent Degradation**: Metrics failures logged as warnings but never impact core business logic execution

**Performance Optimization Patterns**:
- **Lazy Initialization**: Metrics setup only occurs when explicitly requested, reducing startup overhead
- **Conditional Recording**: Metrics recording only executed when observability components are enabled and available
- **Efficient Bucketing**: Confidence and completeness scores bucketed into discrete ranges for efficient aggregation
- **Minimal Overhead Design**: Metrics collection designed to have negligible impact on core agent performance

## Key Classes and Functions

### CrawlerMetrics
- **`record_entity_discovered(entity_type, source, confidence)`**: Records entity discovery events with type classification and confidence scoring
- **`record_relationship_discovered(relationship_type, confidence, source)`**: Tracks relationship mapping with quality metrics
- **`record_crawl_completeness(completeness_score, entity_type, discovery_method)`**: Measures crawl operation completeness
- **`update_knowledge_graph_size(entity_count)`**: Real-time knowledge graph growth tracking
- **`record_data_quality_score(score, assessment_type)`**: Data quality distribution analysis
- **`record_crawler_error(error_type, entity_type, severity)`**: Comprehensive error categorization and tracking

### ADKMetricsIntegration
- **`record_from_tool_context(tool_context, metric_type, **kwargs)`**: Context-aware metric recording using ADK ToolContext
- **`record_from_invocation_context(invocation_context, metric_type, **kwargs)`**: Session-aware metrics with ADK InvocationContext

### Global Management Functions (from metrics.py)
- **`setup_metrics(meter_name)`**: Initialize global metrics instance with OpenTelemetry meter configuration
- **`get_global_metrics()`**: Access global metrics instance for application-wide metric recording

## Usage Examples

### Basic Metrics Setup
```python
from researcher.observability import setup_adk_observability, get_global_metrics

# Initialize complete ADK observability (tracing + metrics)
setup_adk_observability(project_name="entity-crawler")

# Access metrics for recording
metrics = get_global_metrics()
metrics.record_entity_discovered("COMPANY", "sec_edgar", confidence=0.95)
```

### ADK Tool Integration
```python
from google.adk.tools import ToolContext
from researcher.observability import ADKMetricsIntegration

metrics_integration = ADKMetricsIntegration()

def my_discovery_tool(query: str, tool_context: ToolContext) -> dict:
    # Tool logic here...
    entities = discover_entities(query)
    
    # Record metrics with ADK context
    for entity in entities:
        metrics_integration.record_from_tool_context(
            tool_context, 
            'entity_discovery',
            entity_type=entity.type,
            source='web_crawl',
            confidence=entity.confidence
        )
```

### Context Manager Usage
```python
from researcher.observability import MetricsContext

# Automatic metrics collection with context manager
with MetricsContext('entity_discovery', entity_type='company') as ctx:
    result = discover_company_info(company_name)
    ctx.set_attribute('confidence', 0.85)
    ctx.set_attribute('source', 'gleif_api')
    # Metrics automatically recorded on success/failure
```

## Dependencies and Integration Points

### Core Dependencies
- **OpenTelemetry**: `opentelemetry-api`, `opentelemetry-sdk` for metrics collection and exporters
- **Google ADK**: `google-adk>=1.5.0` for agent framework and automatic instrumentation integration
- **Phoenix Observability**: `arize-phoenix-otel` for distributed tracing backend with ADK correlation
- **GoogleADKInstrumentor**: `openinference-instrumentation-google-adk` for proper ADK agent tracing

### External Integration Points
- **Phoenix Backend**: Configurable support for Phoenix Cloud (`https://app.phoenix.arize.com`) and local Phoenix deployment
- **OpenTelemetry Collectors**: Standard OTLP export compatibility for enterprise observability platforms
- **EkoIntelligence Backend**: Integration with `backoffice` package for database operations and LLM provider access

## See Also

**Related EkoIntelligence Components**:
- **ADK Agent Implementations**: `/researcher/agents/coordinator.py`, `/researcher/agents/company_analyzer.py`
- **Knowledge Graph Service**: `/researcher/services/kg_service.py` for entity and relationship management
- **Entity Discovery Tools**: `/researcher/tools/entity_tools.py`, `/researcher/tools/data_source_tools.py`
- **Phoenix Setup Integration**: `/researcher/observability/phoenix_setup.py` for ADK instrumentation configuration

**External Documentation**:
- **Google ADK Documentation**: https://github.com/google/adk-docs for agent framework best practices
- **OpenTelemetry Python**: https://opentelemetry.io/docs/languages/python/ for metrics API reference
- **Phoenix Observability**: https://docs.arize.com/phoenix for distributed tracing and evaluation
- **OpenTelemetry Metrics Specification**: https://opentelemetry.io/docs/specs/otel/metrics/ for metrics semantic conventions

<AUTHOR>
@updated 2025-08-01
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import time
from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger

# OpenTelemetry metrics imports
try:
    from opentelemetry import metrics
    from opentelemetry.metrics import get_meter
    METRICS_AVAILABLE = True
except ImportError:
    logger.warning("OpenTelemetry metrics not available")
    METRICS_AVAILABLE = False

# Check if ADK instrumentation is available
try:
    from .phoenix_setup import is_adk_instrumented
    ADK_INTEGRATION_AVAILABLE = True
except ImportError:
    logger.debug("ADK integration not available for metrics")
    ADK_INTEGRATION_AVAILABLE = False
    
    def is_adk_instrumented():
        return False


class CrawlerMetrics:
    """Custom metrics for entity crawler operations.
    
    These metrics focus on domain-specific measurements that complement
    ADK's automatic instrumentation of agent interactions, tool calls, etc.
    """
    
    def __init__(self, meter_name: str = "adk.entity.crawler"):
        self.meter = None
        self.metrics = {}
        self.enabled = False
        
        if METRICS_AVAILABLE:
            self._setup_metrics(meter_name)
            
        # Log warning if ADK instrumentation is not active
        if ADK_INTEGRATION_AVAILABLE and not is_adk_instrumented():
            logger.warning("ADK instrumentation not detected - consider setting up proper ADK observability")
    
    def _setup_metrics(self, meter_name: str):
        """Setup custom metrics that complement ADK's built-in tracing."""
        try:
            self.meter = get_meter(meter_name, "1.0.0")
            
            # Create domain-specific metrics that complement ADK's built-in tracing
            # Note: ADK automatically traces agent runs, tool calls, and LLM interactions
            
            # Entity and relationship discovery metrics (domain-specific)
            self.metrics['entity_discovery_counter'] = self.meter.create_counter(
                "crawler_entities_discovered_total",
                description="Total number of entities discovered by the crawler",
                unit="1"
            )
            
            self.metrics['relationship_discovery_counter'] = self.meter.create_counter(
                "crawler_relationships_discovered_total", 
                description="Total number of relationships discovered by the crawler",
                unit="1"
            )
            
            # Knowledge graph specific metrics
            self.metrics['knowledge_graph_size_gauge'] = self.meter.create_up_down_counter(
                "crawler_knowledge_graph_entities_total",
                description="Current number of entities in knowledge graph",
                unit="1"
            )
            
            # Data quality metrics (domain-specific)
            self.metrics['data_quality_gauge'] = self.meter.create_histogram(
                "crawler_data_quality_score",
                description="Data quality score distribution for crawled entities",
                unit="1"
            )
            
            # Crawler-specific error tracking (complements ADK's error tracing)
            self.metrics['crawler_error_counter'] = self.meter.create_counter(
                "crawler_errors_total",
                description="Total crawler-specific errors by type",
                unit="1"
            )
            
            # Business logic metrics
            self.metrics['crawl_completeness_gauge'] = self.meter.create_histogram(
                "crawler_completeness_score",
                description="Completeness score of entity crawl operations",
                unit="1"
            )
            
            self.enabled = True
            logger.info("Custom crawler metrics setup successful")
            
        except Exception as e:
            logger.warning(f"Failed to setup custom metrics: {e}")
    
    def record_entity_discovered(
        self,
        entity_type: str,
        source: str,
        confidence: float = 1.0
    ):
        """Record entity discovery event."""
        if not self.enabled:
            return
            
        try:
            self.metrics['entity_discovery_counter'].add(
                1,
                {
                    "entity_type": entity_type,
                    "source": source,
                    "confidence_bucket": self._get_confidence_bucket(confidence)
                }
            )
        except Exception as e:
            logger.warning(f"Failed to record entity discovery metric: {e}")
    
    def record_relationship_discovered(
        self,
        relationship_type: str,
        confidence: float,
        source: str = "unknown"
    ):
        """Record relationship discovery event."""
        if not self.enabled:
            return
            
        try:
            self.metrics['relationship_discovery_counter'].add(
                1,
                {
                    "relationship_type": relationship_type,
                    "confidence_bucket": self._get_confidence_bucket(confidence),
                    "source": source
                }
            )
        except Exception as e:
            logger.warning(f"Failed to record relationship discovery metric: {e}")
    
    def record_crawl_completeness(
        self,
        completeness_score: float,
        entity_type: str,
        discovery_method: str = "unknown"
    ):
        """Record crawl completeness score."""
        if not self.enabled:
            return
            
        try:
            self.metrics['crawl_completeness_gauge'].record(
                completeness_score,
                {
                    "entity_type": entity_type,
                    "discovery_method": discovery_method,
                    "completeness_bucket": self._get_completeness_bucket(completeness_score)
                }
            )
        except Exception as e:
            logger.warning(f"Failed to record crawl completeness metric: {e}")
    
    def update_knowledge_graph_size(self, entity_count: int):
        """Update knowledge graph size metric."""
        if not self.enabled:
            return
            
        try:
            self.metrics['knowledge_graph_size_gauge'].add(entity_count)
        except Exception as e:
            logger.warning(f"Failed to update knowledge graph size metric: {e}")
    
    def record_data_quality_score(self, score: float, assessment_type: str = "overall"):
        """Record data quality score."""
        if not self.enabled:
            return
            
        try:
            self.metrics['data_quality_gauge'].record(
                score,
                {"assessment_type": assessment_type}
            )
        except Exception as e:
            logger.warning(f"Failed to record data quality metric: {e}")
    
    def record_crawler_error(
        self,
        error_type: str,
        entity_type: str = "unknown",
        severity: str = "error"
    ):
        """Record crawler-specific error (complements ADK's automatic error tracing)."""
        if not self.enabled:
            return
            
        try:
            self.metrics['crawler_error_counter'].add(
                1,
                {
                    "error_type": error_type,
                    "entity_type": entity_type,
                    "severity": severity
                }
            )
        except Exception as e:
            logger.warning(f"Failed to record crawler error metric: {e}")
    
    def _get_confidence_bucket(self, confidence: float) -> str:
        """Get confidence bucket for metrics."""
        if confidence >= 0.8:
            return "high"
        elif confidence >= 0.5:
            return "medium"
        else:
            return "low"
    
    def _get_completeness_bucket(self, score: float) -> str:
        """Get completeness bucket for metrics."""
        if score >= 0.8:
            return "high"
        elif score >= 0.5:
            return "medium"
        else:
            return "low"


class ADKMetricsIntegration:
    """Integration helper for ADK context-aware metrics.
    
    This class provides utilities for integrating custom metrics
    with ADK's context objects like ToolContext, InvocationContext, etc.
    """
    
    def __init__(self, metrics: Optional[CrawlerMetrics] = None):
        self.metrics = metrics or get_global_metrics()
    
    def record_from_tool_context(self, tool_context, metric_type: str, **kwargs):
        """Record metrics using ADK ToolContext information."""
        if not self.metrics or not hasattr(tool_context, 'invocation_id'):
            return
        
        # Add ADK context information to metrics
        kwargs.update({
            'invocation_id': getattr(tool_context, 'invocation_id', 'unknown'),
            'agent_name': getattr(tool_context, 'agent_name', 'unknown')
        })
        
        if metric_type == 'entity_discovery':
            self.metrics.record_entity_discovered(**kwargs)
        elif metric_type == 'relationship_discovery':
            self.metrics.record_relationship_discovered(**kwargs)
        elif metric_type == 'error':
            self.metrics.record_crawler_error(**kwargs)
    
    def record_from_invocation_context(self, invocation_context, metric_type: str, **kwargs):
        """Record metrics using ADK InvocationContext information."""
        if not self.metrics or not hasattr(invocation_context, 'invocation_id'):
            return
        
        # Add ADK context information to metrics  
        kwargs.update({
            'invocation_id': getattr(invocation_context, 'invocation_id', 'unknown'),
            'session_id': getattr(invocation_context.session, 'session_id', 'unknown') if hasattr(invocation_context, 'session') else 'unknown'
        })
        
        if metric_type == 'completeness':
            self.metrics.record_crawl_completeness(**kwargs)
        elif metric_type == 'quality':
            self.metrics.record_data_quality_score(**kwargs)


# Global metrics instance
_global_metrics = None


def setup_metrics(meter_name: str = "adk.entity.crawler") -> CrawlerMetrics:
    """Setup global metrics instance."""
    global _global_metrics
    
    if _global_metrics is None:
        _global_metrics = CrawlerMetrics(meter_name)
    
    return _global_metrics


def get_global_metrics() -> Optional[CrawlerMetrics]:
    """Get the global metrics instance."""
    return _global_metrics


class MetricsContext:
    """Context manager for automatic metrics collection that integrates with ADK patterns."""
    
    def __init__(
        self,
        operation_type: str,
        operation_id: Optional[str] = None,
        metrics: Optional[CrawlerMetrics] = None,
        **attributes
    ):
        self.operation_type = operation_type
        self.operation_id = operation_id or f"{operation_type}_{int(time.time())}"
        self.metrics = metrics or get_global_metrics()
        self.attributes = attributes
        self.start_time = None
        self.success = True
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.success = False
            if self.metrics:
                self.metrics.record_crawler_error(
                    error_type=exc_type.__name__,
                    entity_type=self.attributes.get('entity_type', 'unknown'),
                    severity="error"
                )
        
        # Record completion metrics based on operation type
        if self.metrics and self.success:
            if self.operation_type == 'entity_discovery':
                self.metrics.record_entity_discovered(
                    entity_type=self.attributes.get('entity_type', 'unknown'),
                    source=self.attributes.get('source', 'unknown'),
                    confidence=self.attributes.get('confidence', 1.0)
                )
            elif self.operation_type == 'relationship_discovery':
                self.metrics.record_relationship_discovered(
                    relationship_type=self.attributes.get('relationship_type', 'unknown'),
                    confidence=self.attributes.get('confidence', 1.0),
                    source=self.attributes.get('source', 'unknown')
                )
    
    def set_attribute(self, key: str, value: Any):
        """Set an attribute for the operation."""
        self.attributes[key] = value
    
    def mark_success(self):
        """Mark the operation as successful."""
        self.success = True
    
    def mark_failure(self):
        """Mark the operation as failed."""
        self.success = False