"""Recursive Entity Processing Agent

Implements the core recursive workflow from the pseudo-code in PLAN.md.
This agent follows the "spider by entities" approach, discovering and processing
related entities through their relationships.

NOTE: This is NOT a pure ADK agent but a specialized processor that orchestrates
ADK tools and follows ADK patterns. It could be converted to a custom BaseAgent
implementation in the future if needed.
"""

import asyncio
from typing import Dict, List, Set, Any, Optional, Tuple
from loguru import logger
from datetime import datetime

# Google ADK imports
from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool

# Local imports
from researcher.services.kg_service import KnowledgeGraphService
from researcher.tools.web_tools import intelligent_web_crawler, process_document
from researcher.tools.entity_tools import (
    kg_upsert_entity,
    kg_create_relationship,
    generate_advanced_search_queries
)
from researcher.tools.action_tools import (
    track_entity_action,
    analyze_statement_for_actions,
    track_promise_fulfillment,
    get_entity_action_timeline
)
from researcher.tools.data_source_tools import (
    enhanced_sec_search,
    enhanced_gleif_search,
    enhanced_companies_house_search,
    enhanced_wikipedia_search
)
from researcher.models.entities import EntityDiscovery, EnhancedKGEntity
from researcher.models.relationships import KGRelationship, RelationshipDiscovery
from eko.db.data.entity import EntityExtended
from eko.models.common import EntityType


class RecursiveEntityProcessor:
    """Implements recursive entity discovery workflow following the pseudo-code pattern.
    
    IMPORTANT: This is NOT an ADK agent but a specialized processor that orchestrates
    ADK tools and functions. It follows ADK patterns for consistency but does not inherit
    from BaseAgent or implement ADK agent interfaces.
    
    This class could be converted to a custom BaseAgent implementation if pure ADK
    compliance is required, but currently serves as a utility processor for complex
    recursive workflows that are difficult to express purely through ADK agent composition.
    """
    
    def __init__(self):
        self.processed_entities: Set[str] = set()
        self.discovered_entities: Dict[str, EntityDiscovery] = {}
        self.entity_queue: List[Tuple[str, str, int]] = []  # (entity_id, entity_name, depth)
        self.max_depth = 3
        self.max_entities_per_run = 50
        
        # Track search queries and their effectiveness
        self.search_history: List[Dict[str, Any]] = []
        self.successful_terms: List[str] = []
        self.kg_service = KnowledgeGraphService()
        
        logger.info("Recursive Entity Processor initialized")
    
    async def process_entity_network(
        self,
        initial_entity_name: str,
        entity_type: str = "COMPANY",
        max_depth: int = 3,
        max_entities: int = 50
    ) -> Dict[str, Any]:
        """Process an entity network following the recursive workflow.
        
        This implements the pseudo-code from PLAN.md:
        def process_company_or_org():
            entity, relationships = analyse_company_or_org()
            while relationship in relationships:
                links = get_links_from_gse(relationship.as_gse_query())
                while link in links:
                    page = visit(link)
                    store_learnings_in_kg()
                    relationships.add(page.relationships())
                    if page.is_relevant():
                        downloader.download(page)
                        links.add(page.links())
        
        Args:
            initial_entity_name: Starting entity name
            entity_type: Type of entity (COMPANY, ORGANIZATION, etc.)
            max_depth: Maximum recursion depth
            max_entities: Maximum entities to process
            
        Returns:
            Complete network discovery results
        """
        self.max_depth = max_depth
        self.max_entities_per_run = max_entities
        
        try:
            logger.info(f"Starting recursive entity network processing for: {initial_entity_name}")
            
            # Step 1: Process initial entity
            initial_discovery = await self._process_single_entity(
                initial_entity_name, entity_type, depth=0
            )
            
            if not initial_discovery:
                return {
                    'status': 'failed',
                    'error': f'Failed to process initial entity: {initial_entity_name}'
                }
            
            # Add initial entity to queue and processed set
            initial_entity_id = initial_discovery.entity.id
            self.processed_entities.add(initial_entity_id)
            self.discovered_entities[initial_entity_id] = initial_discovery
            
            # Step 2: Queue related entities for processing
            await self._queue_related_entities(initial_discovery, depth=1)
            
            # Step 3: Process entity queue recursively
            processed_count = 1
            while self.entity_queue and processed_count < max_entities:
                entity_id, entity_name, depth = self.entity_queue.pop(0)
                
                if entity_id in self.processed_entities or depth > max_depth:
                    continue
                
                logger.info(f"Processing entity {processed_count}/{max_entities}: {entity_name} (depth: {depth})")
                
                # Process the entity
                entity_discovery = await self._process_single_entity(
                    entity_name, "COMPANY", depth
                )
                
                if entity_discovery:
                    self.processed_entities.add(entity_id)
                    self.discovered_entities[entity_id] = entity_discovery
                    
                    # Queue newly discovered entities
                    await self._queue_related_entities(entity_discovery, depth + 1)
                    
                    processed_count += 1
                
                # Rate limiting
                await asyncio.sleep(2)
            
            # Step 4: Compile results
            results = await self._compile_network_results()
            
            logger.info(f"Recursive processing completed. Processed {processed_count} entities, "
                       f"discovered {len(self.discovered_entities)} total entities")
            
            return results
            
        except Exception as e:
            logger.exception(f"Recursive entity processing failed for {initial_entity_name}")
            return {
                'status': 'failed',
                'error': str(e),
                'partial_results': await self._compile_network_results()
            }
    
    async def _process_single_entity(
        self,
        entity_name: str,
        entity_type: str,
        depth: int
    ) -> Optional[EntityDiscovery]:
        """Process a single entity following the workflow pattern.
        
        This implements the core of process_company_or_org() from pseudo-code.
        """
        try:
            logger.info(f"Processing entity: {entity_name} at depth {depth}")
            
            # Step 1: Analyze company/org using APIs (analyse_company_or_org())
            entity_analysis = await self._analyze_entity_from_sources(entity_name, entity_type)
            
            if not entity_analysis:
                logger.warning(f"No analysis data found for entity: {entity_name}")
                return None
            
            # Step 2: Create/update entity in knowledge graph
            entity_record = await self._create_or_update_entity(entity_analysis, entity_name, entity_type)
            
            # Step 3: Extract relationships from analysis
            discovered_relationships = await self._extract_relationships_from_analysis(
                entity_record, entity_analysis
            )
            
            # Step 4: Process each relationship (while relationship in relationships)
            all_documents = []
            all_links = []
            
            for relationship in discovered_relationships:
                # Generate refined search queries based on relationship and history
                search_queries = await self._generate_refined_search_queries(
                    relationship, entity_record, entity_analysis
                )
                
                # Execute searches and get links (get_links_from_gse())
                for query in search_queries:
                    links = await self._execute_search_query(query, entity_name)
                    
                    # Process each link (while link in links)
                    for link_data in links:
                        try:
                            # Visit the page (page = visit(link))
                            page_analysis = await self._visit_and_analyze_page(
                                link_data['url'], entity_name
                            )
                            
                            if not page_analysis:
                                continue
                            
                            # Store learnings in KG (store_learnings_in_kg())
                            await self._store_page_learnings(page_analysis, entity_record)
                            
                            # Extract and track entity actions from page content
                            await self._extract_and_track_actions(page_analysis, entity_record)
                            
                            # Extract new relationships (relationships.add(page.relationships()))
                            page_relationships = page_analysis.get('relationships', [])
                            discovered_relationships.extend(page_relationships)
                            
                            # Check if page is relevant (if page.is_relevant())
                            if page_analysis.get('relevance_score', 0) > 0.3:
                                # Download page/documents (downloader.download(page))
                                downloaded_docs = await self._download_relevant_content(
                                    page_analysis, entity_name
                                )
                                all_documents.extend(downloaded_docs)
                                
                                # Get additional links (links.add(page.links()))
                                additional_links = page_analysis.get('additional_links', [])
                                all_links.extend(additional_links)
                        
                        except Exception as e:
                            logger.warning(f"Failed to process link {link_data.get('url', 'unknown')}: {e}")
                            continue
            
            # Create entity discovery result
            entity_discovery = EntityDiscovery(
                entity=entity_record,
                relationships=discovered_relationships,
                documents=all_documents,
                discovery_source="recursive_processor",
                discovery_url=None,
                confidence_score=0.8,
                has_subsidiaries=len([r for r in discovered_relationships if r.get('type') == 'OWNS']) > 0,
                subsidiaries=[r for r in discovered_relationships if r.get('type') == 'OWNS'],
                metadata={
                    'processing_depth': depth,
                    'analysis_sources': list(entity_analysis.keys()),
                    'links_processed': len(all_links),
                    'documents_found': len(all_documents)
                }
            )
            
            return entity_discovery
            
        except Exception as e:
            logger.exception(f"Failed to process entity: {entity_name}")
            return None
    
    async def _analyze_entity_from_sources(
        self,
        entity_name: str,
        entity_type: str
    ) -> Optional[Dict[str, Any]]:
        """Analyze entity using multiple data sources (SEC, GLEIF, Companies House, etc.)."""
        analysis_results = {}
        
        try:
            # SEC search
            try:
                sec_result = await asyncio.to_thread(enhanced_sec_search, entity_name)
                if sec_result and sec_result.get('status') == 'success':
                    analysis_results['sec'] = sec_result
            except Exception as e:
                logger.warning(f"SEC search failed for {entity_name}: {e}")
            
            # GLEIF search
            try:
                gleif_result = await asyncio.to_thread(enhanced_gleif_search, entity_name)
                if gleif_result and gleif_result.get('status') == 'success':
                    analysis_results['gleif'] = gleif_result
            except Exception as e:
                logger.warning(f"GLEIF search failed for {entity_name}: {e}")
            
            # Companies House search
            try:
                ch_result = await asyncio.to_thread(enhanced_companies_house_search, entity_name)
                if ch_result and ch_result.get('status') == 'success':
                    analysis_results['companies_house'] = ch_result
            except Exception as e:
                logger.warning(f"Companies House search failed for {entity_name}: {e}")
            
            # Wikipedia search
            try:
                wiki_result = await asyncio.to_thread(enhanced_wikipedia_search, entity_name)
                if wiki_result and wiki_result.get('status') == 'success':
                    analysis_results['wikipedia'] = wiki_result
            except Exception as e:
                logger.warning(f"Wikipedia search failed for {entity_name}: {e}")
            
            return analysis_results if analysis_results else None
            
        except Exception as e:
            logger.exception(f"Entity analysis failed for {entity_name}")
            return None
    
    async def _create_or_update_entity(
        self,
        analysis_data: Dict[str, Any],
        entity_name: str,
        entity_type: str
    ) -> EnhancedKGEntity:
        """Create or update entity in knowledge graph based on analysis data."""
        try:
            # Extract identifiers from analysis data
            identifiers = {}
            properties = {}
            
            # Process SEC data
            sec_data = analysis_data.get('sec', {}).get('data', {})
            if sec_data:
                if 'cik' in sec_data:
                    identifiers['cik'] = sec_data['cik']
                if 'ticker' in sec_data:
                    identifiers['ticker'] = sec_data['ticker']
                properties.update(sec_data)
            
            # Process GLEIF data
            gleif_data = analysis_data.get('gleif', {}).get('data', {})
            if gleif_data:
                if 'lei' in gleif_data:
                    identifiers['lei'] = gleif_data['lei']
                properties.update(gleif_data)
            
            # Process Companies House data
            ch_data = analysis_data.get('companies_house', {}).get('data', {})
            if ch_data:
                if 'company_number' in ch_data:
                    identifiers['companies_house_number'] = ch_data['company_number']
                properties.update(ch_data)
            
            # Check if entity already exists
            existing_entity = self.kg_service.find_entity_by_identifiers(identifiers)
            
            if existing_entity:
                logger.info(f"Found existing entity: {existing_entity.name}")
                return EnhancedKGEntity(
                    id=str(existing_entity.id),
                    name=existing_entity.name,
                    entity_type=existing_entity.type,
                    identifiers=identifiers,
                    properties=properties,
                    confidence_score=0.9,
                    data_sources=list(analysis_data.keys())
                )
            
            # Create new entity
            new_entity = EnhancedKGEntity(
                id=f"temp_{entity_name.replace(' ', '_').lower()}",
                name=entity_name,
                entity_type=entity_type,
                identifiers=identifiers,
                properties=properties,
                confidence_score=0.8,
                data_sources=list(analysis_data.keys())
            )
            
            # Convert to EntityExtended for database storage
            entity_extended = EntityExtended(
                name=entity_name,
                type=entity_type,
                eko_id=None,  # Will be generated
                canonical=True,
                confidence_score=0.8,
                lei=identifiers.get('lei'),
                ticker=identifiers.get('ticker'),
                description=f"Discovered via recursive crawler from sources: {', '.join(analysis_data.keys())}"
            )
            
            # Create in database
            entity_id = self.kg_service.create_entity(entity_extended)
            new_entity.id = entity_id
            
            logger.info(f"Created new entity: {entity_name} (ID: {entity_id})")
            return new_entity
            
        except Exception as e:
            logger.exception(f"Failed to create/update entity: {entity_name}")
            raise
    
    async def _extract_relationships_from_analysis(
        self,
        entity: EnhancedKGEntity,
        analysis_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract relationships from entity analysis data."""
        relationships = []
        
        try:
            # Extract from SEC data
            sec_data = analysis_data.get('sec', {}).get('data', {})
            if sec_data and 'subsidiaries' in sec_data:
                for subsidiary in sec_data['subsidiaries']:
                    relationships.append({
                        'type': 'OWNS',
                        'target_name': subsidiary.get('name', ''),
                        'source': 'sec',
                        'confidence': 0.9,
                        'properties': subsidiary
                    })
            
            # Extract from Companies House data
            ch_data = analysis_data.get('companies_house', {}).get('data', {})
            if ch_data:
                if 'officers' in ch_data:
                    for officer in ch_data['officers']:
                        relationships.append({
                            'type': 'EMPLOYS',
                            'target_name': officer.get('name', ''),
                            'source': 'companies_house',
                            'confidence': 0.8,
                            'properties': officer
                        })
                
                if 'parent_company' in ch_data and ch_data['parent_company']:
                    relationships.append({
                        'type': 'SUBSIDIARY_OF',
                        'target_name': ch_data['parent_company'],
                        'source': 'companies_house',
                        'confidence': 0.8,
                        'properties': {'parent_company': ch_data['parent_company']}
                    })
            
            logger.info(f"Extracted {len(relationships)} relationships for {entity.name}")
            return relationships
            
        except Exception as e:
            logger.exception(f"Failed to extract relationships for {entity.name}")
            return []
    
    async def _generate_refined_search_queries(
        self,
        relationship: Dict[str, Any],
        entity_record: 'EnhancedKGEntity',
        entity_analysis: Dict[str, Any]
    ) -> List[str]:
        """Generate refined search queries using advanced query generation with historical context."""
        try:
            target_name = relationship.get('target_name', '')
            if not target_name:
                return []
            
            # Use the enhanced search query generation with historical context
            query_result = await generate_advanced_search_queries(
                entity_name=target_name,
                entity_data=entity_analysis,
                relationships=[relationship],
                discovered_patterns=self._extract_discovered_patterns(),
                search_history=self.search_history,
                successful_terms=self.successful_terms
            )
            
            if query_result.get('status') == 'success':
                # Extract top queries from prioritized list
                prioritized_queries = query_result.get('prioritized_queries', [])
                return [q['query'] for q in prioritized_queries[:10]]  # Top 10 queries
            
            # Fallback to simple queries if advanced generation fails
            return [
                f'"{target_name}" company information',
                f'"{target_name}" sustainability report',
                f'"{target_name}" ESG report'
            ]
            
        except Exception as e:
            logger.warning(f"Failed to generate refined search queries: {e}")
            return []
    
    async def _execute_search_query(
        self,
        query: str,
        entity_context: str
    ) -> List[Dict[str, Any]]:
        """Execute search query and return relevant links with effectiveness tracking."""
        try:
            # This would integrate with Google Search API or other search tools
            # For now, return placeholder structure
            search_result = {
                'query': query,
                'entity_context': entity_context,
                'timestamp': datetime.utcnow(),
                'results': [],
                'effectiveness_score': 0.0,
                'documents_found': 0,
                'average_relevance': 0.0
            }
            
            # Placeholder - would implement actual search here
            # When real search is implemented, calculate effectiveness metrics:
            # search_result['effectiveness_score'] = calculated_effectiveness
            # search_result['documents_found'] = len(documents_found)
            # search_result['average_relevance'] = avg_relevance_score
            
            # Track search history for learning
            self.search_history.append(search_result)
            
            # Extract and track successful terms for future use
            if search_result['effectiveness_score'] > 0.7:
                self._update_successful_terms(query)
            
            return []
            
        except Exception as e:
            logger.warning(f"Search query failed: {query} - {e}")
            return []
    
    async def _visit_and_analyze_page(
        self,
        url: str,
        entity_context: str
    ) -> Optional[Dict[str, Any]]:
        """Visit and analyze a web page for entity-relevant content."""
        try:
            # Use existing web crawler
            crawl_result = await intelligent_web_crawler(
                start_url=url,
                entity_context=entity_context,
                max_pages=1,  # Single page analysis
                relevance_threshold=0.1
            )
            
            if crawl_result.get('status') == 'success':
                return crawl_result.get('data', {})
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to analyze page {url}: {e}")
            return None
    
    async def _store_page_learnings(
        self,
        page_analysis: Dict[str, Any],
        entity: EnhancedKGEntity
    ) -> None:
        """Store learnings from page analysis in knowledge graph."""
        try:
            # Extract and store new entities discovered
            entities_discovered = page_analysis.get('entities_discovered', [])
            for entity_data in entities_discovered:
                # Would create entities in KG
                pass
            
            # Extract and store new relationships
            relationships_discovered = page_analysis.get('relationships_discovered', [])
            for rel_data in relationships_discovered:
                # Would create relationships in KG
                pass
            
            logger.debug(f"Stored learnings from page analysis for {entity.name}")
            
        except Exception as e:
            logger.warning(f"Failed to store page learnings: {e}")
    
    async def _download_relevant_content(
        self,
        page_analysis: Dict[str, Any],
        entity_context: str
    ) -> List[Dict[str, Any]]:
        """Download relevant documents and content using enhanced collection system."""
        downloaded_docs = []
        
        try:
            # Get document links from page analysis
            document_links = page_analysis.get('documents_found', [])
            
            if not document_links:
                return []
            
            # Extract URLs for comprehensive collection
            doc_urls = []
            for doc_info in document_links:
                doc_url = doc_info.get('url')
                if doc_url:
                    doc_urls.append(doc_url)
            
            if len(doc_urls) >= 3:
                # Use comprehensive document collection for multiple documents
                try:
                    from researcher.tools.web_tools import comprehensive_document_collection
                    
                    collection_result = await comprehensive_document_collection(
                        entity_context=entity_context,
                        base_urls=[page_analysis.get('url', '')],  # Use page URL as base
                        max_documents=20,  # Limit for performance
                        quality_threshold=0.3
                    )
                    
                    if collection_result.get('status') == 'success':
                        # Convert high-quality documents to expected format
                        for doc_info in collection_result.get('high_quality_documents', []):
                            downloaded_docs.append({
                                'status': 'success',
                                'url': doc_info['url'],
                                'analysis': {
                                    'document_type': doc_info.get('document_category', 'unknown'),
                                    'quality_score': doc_info['quality_score'],
                                    'relevance_score': doc_info['relevance_score'],
                                    'word_count': doc_info.get('word_count', 0),
                                    'content_themes': doc_info.get('content_themes', [])
                                },
                                'metadata': {
                                    'title': doc_info.get('title'),
                                    'entity_mentions': doc_info.get('entity_mentions', []),
                                    'file_size': doc_info.get('file_size', 0)
                                },
                                'enhanced': True,
                                'collection_summary': collection_result.get('collection_summary', {})
                            })
                        
                        logger.info(f"Enhanced document collection found {len(downloaded_docs)} high-quality documents")
                        return downloaded_docs
                        
                except Exception as e:
                    logger.warning(f"Enhanced document collection failed, falling back to individual processing: {e}")
            
            # Fallback to individual document processing
            for doc_info in document_links:
                doc_url = doc_info.get('url')
                if doc_url:
                    try:
                        # Use enhanced document processor
                        doc_result = await process_document(
                            document_url=doc_url,
                            entity_context=entity_context,
                            extract_relationships=True,
                            use_enhanced_collection=True
                        )
                        
                        if doc_result.get('status') == 'success':
                            downloaded_docs.append(doc_result)
                    
                    except Exception as e:
                        logger.warning(f"Failed to download document {doc_url}: {e}")
            
            return downloaded_docs
            
        except Exception as e:
            logger.warning(f"Failed to download relevant content: {e}")
            return []
    
    async def _queue_related_entities(
        self,
        entity_discovery: EntityDiscovery,
        depth: int
    ) -> None:
        """Queue related entities for processing based on discovered relationships."""
        try:
            if depth > self.max_depth:
                return
            
            # Queue entities from ownership relationships (subsidiaries)
            for subsidiary in entity_discovery.subsidiaries:
                entity_name = subsidiary.get('target_name') or subsidiary.get('name')
                if entity_name:
                    entity_id = f"queued_{entity_name.replace(' ', '_').lower()}"
                    if entity_id not in self.processed_entities:
                        self.entity_queue.append((entity_id, entity_name, depth))
            
            # Queue entities from other relationships
            for relationship in entity_discovery.relationships:
                target_name = relationship.get('target_name')
                if target_name and relationship.get('confidence', 0) > 0.7:
                    entity_id = f"queued_{target_name.replace(' ', '_').lower()}"
                    if entity_id not in self.processed_entities:
                        self.entity_queue.append((entity_id, target_name, depth))
            
            logger.info(f"Queued {len(self.entity_queue)} entities for processing at depth {depth}")
            
        except Exception as e:
            logger.warning(f"Failed to queue related entities: {e}")
    
    async def _compile_network_results(self) -> Dict[str, Any]:
        """Compile final results from the recursive processing."""
        try:
            # Calculate network statistics
            total_entities = len(self.discovered_entities)
            total_relationships = sum(
                len(discovery.relationships) 
                for discovery in self.discovered_entities.values()
            )
            total_documents = sum(
                len(discovery.documents)
                for discovery in self.discovered_entities.values()
            )
            
            # Compile entity network
            entity_network = {}
            for entity_id, discovery in self.discovered_entities.items():
                entity_network[entity_id] = {
                    'entity': discovery.entity.dict(),
                    'relationships': discovery.relationships,
                    'documents': discovery.documents,
                    'metadata': discovery.metadata
                }
            
            results = {
                'status': 'completed',
                'network_statistics': {
                    'entities_discovered': total_entities,
                    'relationships_found': total_relationships,
                    'documents_collected': total_documents,
                    'processing_depth_reached': max(
                        discovery.metadata.get('processing_depth', 0)
                        for discovery in self.discovered_entities.values()
                    ) if self.discovered_entities else 0,
                    'search_queries_executed': len(self.search_history)
                },
                'entity_network': entity_network,
                'search_history': self.search_history,
                'processing_summary': {
                    'entities_processed': len(self.processed_entities),
                    'entities_queued': len(self.entity_queue),
                    'max_depth_configured': self.max_depth,
                    'max_entities_configured': self.max_entities_per_run
                }
            }
            
            return results
            
        except Exception as e:
            logger.exception("Failed to compile network results")
            return {
                'status': 'error',
                'error': str(e),
                'partial_entities': len(self.discovered_entities)
            }
    
    def _extract_discovered_patterns(self) -> List[str]:
        """Extract patterns discovered during processing for future searches."""
        patterns = []
        
        try:
            # Extract patterns from successful searches
            for record in self.search_history:
                if record.get('effectiveness_score', 0) > 0.6:
                    query = record.get('query', '')
                    # Extract meaningful terms from successful queries
                    terms = query.replace('"', '').split()
                    for term in terms:
                        if len(term) > 3 and term.lower() not in ['company', 'information', 'report']:
                            patterns.append(term)
            
            # Extract patterns from discovered entities
            for discovery in self.discovered_entities.values():
                entity_props = discovery.entity.properties
                if entity_props:
                    # Extract industry terms
                    if 'industry' in entity_props:
                        patterns.append(entity_props['industry'])
                    # Extract business terms
                    if 'business_type' in entity_props:
                        patterns.append(entity_props['business_type'])
            
            return list(set(patterns))[:10]  # Return unique patterns, max 10
            
        except Exception as e:
            logger.warning(f"Failed to extract discovered patterns: {e}")
            return []
    
    def _update_successful_terms(self, query: str) -> None:
        """Update the list of successful terms based on effective queries."""
        try:
            # Extract meaningful terms from successful query
            terms = query.replace('"', '').lower().split()
            meaningful_terms = []
            
            # Filter out common words and keep meaningful terms
            common_words = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
            for term in terms:
                if len(term) > 3 and term not in common_words:
                    meaningful_terms.append(term)
            
            # Add to successful terms list (avoiding duplicates)
            for term in meaningful_terms:
                if term not in self.successful_terms:
                    self.successful_terms.append(term)
            
            # Keep only the most recent successful terms (max 50)
            if len(self.successful_terms) > 50:
                self.successful_terms = self.successful_terms[-50:]
                
        except Exception as e:
            logger.warning(f"Failed to update successful terms: {e}")
    
    async def _extract_and_track_actions(
        self,
        page_analysis: Dict[str, Any],
        entity: 'EnhancedKGEntity'
    ) -> None:
        """Extract and track entity actions from page content using DEMISE analysis."""
        try:
            text_content = page_analysis.get('text_content', '')
            if not text_content or len(text_content) < 50:
                return
            
            # Analyze text for entity actions
            action_analysis = await analyze_statement_for_actions(
                statement_text=text_content,
                entity_context=entity.name,
                source_url=page_analysis.get('url')
            )
            
            if action_analysis.get('status') == 'success':
                actions_discovered = action_analysis.get('actions_discovered', [])
                
                for action_info in actions_discovered:
                    try:
                        # Track each discovered action
                        track_result = await track_entity_action(
                            entity_id=entity.id,
                            action_type=action_info['action_type'],
                            action_description=action_info['statement_text'][:200],  # Truncate for description
                            evidence_text=text_content[:500],  # First 500 chars as evidence
                            evidence_url=action_info.get('source_url'),
                            confidence=action_info.get('confidence', 0.7),
                            demise_analysis=action_info.get('demise_analysis')
                        )
                        
                        if track_result.get('status') == 'success':
                            logger.info(f"Tracked {action_info['action_type']} for {entity.name}")
                        
                    except Exception as e:
                        logger.warning(f"Failed to track individual action: {e}")
                        continue
            
        except Exception as e:
            logger.warning(f"Failed to extract and track actions for {entity.name}: {e}")


# Export for ADK integration
__all__ = ['RecursiveEntityProcessor']
