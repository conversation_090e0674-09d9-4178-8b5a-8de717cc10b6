"""Simplified ADK Entity Discovery Coordinator

Uses simple ADK patterns while maintaining all the orchestration functionality.
"""
from eko.models.canonical_predicate import CanonicalPredicate
from google.adk.agents import LlmAgent, SequentialAgent, ParallelAgent, LoopAgent

# Import simplified agent factory functions
from researcher.tools import enhanced_sec_search, enhanced_gleif_search, enhanced_companies_house_search, \
    enhanced_wikipedia_search, kg_search_entities, \
    kg_get_entity_relationships
from researcher.tools.entity_database_tools import enhanced_entity_lookup

# Import tools with consistent direct usage
from researcher.tools.entity_tools import (
    kg_upsert_entity,
    kg_create_relationship,
    generate_positive_search_queries,
    generate_negative_search_queries,
    generate_relationship_search_queries,
    generate_gap_analysis_queries,
    kg_get_company_related,
)
from researcher.tools.web_search_tools import (
    execute_web_search_queries,
    find_entity_base_urls
)
from researcher.tools.action_tools import (
    track_entity_action,
    analyze_statement_for_actions,
    track_promise_fulfillment,
    get_entity_action_timeline
)
from researcher.tools.gap_analyzer import adk_comprehensive_gap_analysis
from researcher.tools.web_tools import document_collector

kg_updater = LlmAgent(
    name="KGUpdater",
    model="gemini-2.5-flash",
    description="Updates knowledge graph with discovered entities and relationships",
    instruction="Use the tools to create entities and relationships in the knowledge graph.",
    tools=[kg_upsert_entity, kg_create_relationship],
    output_key="kg_updates"
)

positive_search_generator = LlmAgent(
    name="PositiveSearchGenerator",
    model="gemini-2.5-flash", 
    description="Generates positive search queries focused on achievements, goals, and success stories",
    instruction="Generate search queries to find positive outcomes, achievements, sustainability goals, innovation breakthroughs, awards, successful partnerships, and positive impact stories for entity research.",
    tools=[generate_positive_search_queries, generate_gap_analysis_queries],
    output_key="positive_search_queries",
)

negative_search_generator = LlmAgent(
    name="NegativeSearchGenerator",
    model="gemini-2.5-flash", 
    description="Generates negative search queries focused on controversies, failures, and critical analysis",
    instruction="Generate search queries to find controversies, scandals, regulatory violations, legal issues, failed initiatives, criticism from NGOs and activists, environmental concerns, and negative impact stories for comprehensive entity research.",
    tools=[generate_negative_search_queries],
    output_key="negative_search_queries",
)

relationship_search_generator = LlmAgent(
    name="RelationshipSearchGenerator",
    model="gemini-2.5-flash", 
    description="Generates relationship search queries focused on ownership, supply chains, funding, and business connections",
    instruction="Generate search queries to discover corporate relationships including ownership structures, supply chain partners, investment connections, joint ventures, board member links, customer relationships, and competitive landscape for comprehensive entity mapping.",
    tools=[generate_relationship_search_queries],
    output_key="relationship_search_queries",
)

web_search_executor = LlmAgent(
    name="WebSearchExecutor",
    model="gemini-2.5-flash",
    description="Executes web searches and finds entity information",
    instruction="Execute web searches to find entity-related information and URLs using {positive_search_queries}, {negative_search_queries}, and {relationship_search_queries}.",
    tools=[execute_web_search_queries, find_entity_base_urls],
    output_key="web_search_results"
)

action_tracker = LlmAgent(
    name="ActionTracker", 
    model="gemini-2.5-flash",
    description="Tracks entity actions and promises over time",
    instruction="Track entity actions, promises, and claims from content using DEMISE analysis.",
    tools=[track_entity_action, analyze_statement_for_actions, track_promise_fulfillment, get_entity_action_timeline],
    output_key="action_tracking"
)

gap_analyzer = LlmAgent(
    name="GapAnalyzer",
    model="gemini-2.5-flash", 
    description="Analyzes information gaps in entity data",
    instruction="Perform gap analysis to identify missing information and data quality issues.",
    tools=[adk_comprehensive_gap_analysis],
    output_key="gap_analysis"
)

# Create orchestration workflow using simple ADK patterns
company_analyzer = LlmAgent(name="CompanyAnalyzer", model="gemini-2.5-flash",
                 description="Initial company analysis",
                 instruction="""
 Search for existing relationships using 'kg_get_entity_relationships' first then search external company data 
 sources and extract key information including identifiers, subsidiaries, and officers. 
 Then update the entity with any new information or create a new one using the 'kg_upsert_entity' tool.""",
                 tools=[enhanced_sec_search,
                        enhanced_gleif_search,
                        enhanced_companies_house_search,
                        enhanced_entity_lookup,
                        enhanced_wikipedia_search,
                        kg_upsert_entity,
                        kg_create_relationship,
                        kg_get_entity_relationships
                        ], output_key="company_analysis")


# Create orchestration workflow using simple ADK patterns
linked_entities_analyzer = LlmAgent(name="LinkedEntityAnalyzer", model="gemini-2.5-flash",
                            description="Relationhip analysis for entities",
                            instruction=f"""
 Search for existing relationships using 'kg_get_entity_relationships' 
 use predicates from [{','.join([cp.value for cp in CanonicalPredicate])}]. To find key entities related to this one, 
 we are looking for proper nouns only, such as people, companies. 
  
We are looking for supply chains, customers, competitors, and other business relationships.""",
                            tools=[kg_get_company_related], output_key="entity_relationships")



crawler = LlmAgent(name="DocumentCrawler", model="gemini-2.0-flash-exp",
                 description="Discovers and processes entity-related documents",
                 instruction="Crawl websites, extract document content, and collect relevant documents for entity analysis.",
                 tools=[document_collector],
                 output_key="document_discovery")


relationship_mapper = LlmAgent(name="RelationshipMapper", model="gemini-2.0-flash-exp",
                               description="Discovers and maps entity relationships",
                               instruction="Extract relationships from data sources and update the knowledge graph with entities and their connections provide a list of entities related to the current entity.",
                               tools=[kg_upsert_entity, kg_create_relationship, kg_search_entities, kg_get_entity_relationships],
                               output_key="entity_relationships")

# TODO : Relationships
loop_crawler= LoopAgent(
    name="CrawlerLoop",
    description="Iteratively crawls websites for entity-related documents",
    #TODO: we need to summarise the information from the previous iterations and use it to inform the next iteration - i.e. the crawler needs to be able to give a synopsis of the current state of the entity and the information discovered so far
    sub_agents=[positive_search_generator, negative_search_generator, relationship_search_generator, web_search_executor, crawler],#, relationship_mapper],
    max_iterations=10
)

parallel_analysis = ParallelAgent(
    name="ParallelAnalysis",
    description="Action tracking and gap analysis in parallel", 
    sub_agents=[action_tracker, gap_analyzer]
)

# Main coordinator using simple SequentialAgent
root_agent = SequentialAgent(
    name="EntityDiscoveryCoordinator",
    description="Coordinates comprehensive entity discovery",
    sub_agents=[
        linked_entities_analyzer,
        company_analyzer,      # Company analysis and search
        loop_crawler,     # Document discovery and relationship mapping
        parallel_analysis,      # Action tracking and gap analysis in parallel
        kg_updater             # Knowledge graph updates
    ]
)

# Export for ADK compatibility
__all__ = ["root_agent"]
