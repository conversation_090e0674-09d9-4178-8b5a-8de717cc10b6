"""Web Search Execution Tools

Tools for executing search queries using Google Custom Search API directly.
Separates search query generation from search execution with proper error handling.
"""

import os
from time import sleep
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse
from loguru import logger

import requests

from eko.cache.pg_cache import MultiLevelCache
from eko.scrape.reports import call_cse
from researcher.tools.response_utils import create_success_response, create_error_response, METADATA_FIELDS

# Import ToolContext for session access
from google.adk.tools import ToolContext

cache = MultiLevelCache("agent_web_search")


def _execute_google_custom_search(query: str, pages: int = 1) -> List[Dict]:
    """
    Execute a single Google Custom Search query.
    
    Args:
        query: The search query
        num_results: Number of results to return
        
    Returns:
        List[Dict]: Search results with title, link, and snippet
    """
    all_items = []
    for count in range(0, pages):
        logger.info(f"Searching for {query} on page {count}")
        try:
            items = call_cse(count, "", query)
            all_items.extend(items)
            if len(items) < 10:
                break
        except Exception as e:
            logger.exception(f"Error calling Google CSE API for query '{query}' on page {count}: {e}")
            # Continue with the next page despite this error
            continue
    return all_items


def execute_web_search_queries(
        tool_context: ToolContext,
        search_queries: Optional[List[str]],
        entity_context: Optional[str],
        max_results_per_query: int,
) -> Dict[str, Any]:
    """Execute web search queries using Google Custom Search API directly.
    
    This tool can either take search queries as input or retrieve them from the session
    state where they were stored by generate_and_store_search_queries.
    
    Args:
        search_queries: List of search queries to execute (optional if using session data)
        entity_context: Optional context about the entity being searched
        max_results_per_query: Maximum results to return per query
        tool_context: ToolContext for accessing session data
        
    Returns:
        Dict with search results including URLs, titles, and snippets
    """
    try:
        # If no explicit search queries provided, try to get them from session
        if not search_queries and tool_context:
            logger.info("No explicit search queries provided, attempting to retrieve from session")
            try:
                # ADK stores agent output using output_key - search_generator uses "search_queries"
                session = getattr(tool_context, 'session', None)
                if session:
                    # Try to get combined search queries from both positive and negative generators
                    combined_queries = []
                    
                    # Get positive queries
                    positive_data = session.get('positive_search_queries', {})
                    if isinstance(positive_data, dict) and 'search_queries' in positive_data:
                        positive_queries = positive_data['search_queries']
                        combined_queries.extend(positive_queries)
                        logger.info(f"Retrieved {len(positive_queries)} positive queries from session")
                    
                    # Get negative queries  
                    negative_data = session.get('negative_search_queries', {})
                    if isinstance(negative_data, dict) and 'search_queries' in negative_data:
                        negative_queries = negative_data['search_queries']
                        combined_queries.extend(negative_queries)
                        logger.info(f"Retrieved {len(negative_queries)} negative queries from session")
                    
                    # Get relationship queries
                    relationship_data = session.get('relationship_search_queries', {})
                    if isinstance(relationship_data, dict) and 'search_queries' in relationship_data:
                        relationship_queries = relationship_data['search_queries']
                        combined_queries.extend(relationship_queries)
                        logger.info(f"Retrieved {len(relationship_queries)} relationship queries from session")
                    
                    # Use combined queries if we got any types
                    if combined_queries:
                        search_queries = combined_queries
                        entity_context = positive_data.get('entity_name') or negative_data.get('entity_name') or relationship_data.get('entity_name') or entity_context
                        logger.info(f"Combined {len(search_queries)} total queries (positive & negative & relationship) for entity: {entity_context}")
                    else:
                        # Fallback to old behavior for backward compatibility
                        search_queries_data = session.get('search_queries', {})

                        if isinstance(search_queries_data, dict):
                            # Extract the structured data stored by generate_and_store_search_queries
                            if 'query_data' in search_queries_data:
                                query_data = search_queries_data['query_data']
                                search_queries = query_data.get('search_queries', [])
                                entity_context = query_data.get('entity_name', entity_context)
                                logger.info(
                                    f"Retrieved {len(search_queries)} queries from session query_data for entity: {entity_context}")
                            elif 'search_queries' in search_queries_data:
                                search_queries = search_queries_data['search_queries']
                                entity_context = search_queries_data.get('entity_name', entity_context)
                                logger.info(
                                    f"Retrieved {len(search_queries)} queries from session for entity: {entity_context}")
                        elif isinstance(search_queries_data, list):
                            # Direct list of queries
                            search_queries = search_queries_data
                            logger.info(f"Retrieved {len(search_queries)} queries from session (direct list)")

            except Exception as e:
                logger.warning(f"Failed to retrieve search queries from session: {e}")

        queries_count = len(search_queries) if search_queries else 0
        logger.info(f"Starting web search execution for {queries_count} queries")

        if not search_queries:
            return create_error_response(
                error_message='No search queries provided and none found in session',
                error_code='INVALID_INPUT',
            )

        all_results = []
        unique_urls = set()

        # Execute each search query using Google Custom Search API
        for query in search_queries:
            try:
                logger.info(f"Executing search query: {query}")

                # Use Google Custom Search API directly
                search_result = _execute_google_custom_search(query, int(max_results_per_query / 10) + 1)

                # Process search results
                if search_result:
                    query_results = []

                    for result in search_result[:max_results_per_query]:
                        url = result.get('link', '')
                        logger.info(url)

                        # Deduplicate URLs
                        if url and url not in unique_urls:
                            unique_urls.add(url)
                            query_results.append({
                                'url': url,
                                'title': result.get('title', ''),
                                'snippet': result.get('snippet', ''),
                                'query': query
                            })

                    all_results.extend(query_results)

                    logger.info(f"Search query '{query}' returned {len(query_results)} unique results")

            except Exception as e:
                logger.exception(f"Failed to execute search query '{query}': {e}")
                continue

        # Group results by domain for analysis
        domain_results = {}
        for result in all_results:
            try:
                domain = urlparse(result['url']).netloc
                if domain not in domain_results:
                    domain_results[domain] = []
                domain_results[domain].append(result)
            except Exception:
                continue

        tool_context.state['urls_to_visit'] = unique_urls
        return create_success_response(
            data={
                'results': all_results,
                'domain_distribution': {
                    domain: len(results) for domain, results in domain_results.items()
                },
                'search_queries_executed': search_queries,
                'entity_context': entity_context,
                'urls': list(unique_urls)
            },
            message=f"Executed {len(search_queries)} web search queries successfully",
            metadata={
                METADATA_FIELDS['TOTAL_COUNT']: len(search_queries),
                METADATA_FIELDS['COUNT']: len(all_results),
                METADATA_FIELDS['UNIQUE_COUNT']: len(unique_urls),
                METADATA_FIELDS['EXTRACTION_METHOD']: 'web_search_execution'
            },
        )

    except Exception as e:
        logger.exception(f"Failed to execute web search queries: {e}")
        return create_error_response(
            error_message=f'Failed to execute web search queries: {str(e)}',
            error_code='WEB_SEARCH_ERROR',
        )


async def find_entity_base_urls(
        tool_context: ToolContext,
        entity_name: str,
        entity_type: str,
        max_urls: int
) -> Dict[str, Any]:
    """Find base URLs for an entity using targeted search queries.
    
    This tool specifically searches for the main website and official pages
    of an entity to provide starting URLs for document crawling.
    
    Args:
        entity_name: Name of the entity to search for
        entity_type: Type of entity (organization, company, etc.)
        max_urls: Maximum number of base URLs to return
        
    Returns:
        Dict with base URLs and metadata
    """
    try:
        logger.info(f"Finding base URLs for {entity_type}: {entity_name}")

        # Input validation
        if not entity_name or not entity_name.strip():
            return create_error_response(
                error_message='Entity name is required',
                error_code='INVALID_INPUT',
            )
        # Generate targeted queries for finding base URLs
        base_url_queries = [
            f'"{entity_name}" official website',
            f'"{entity_name}" company website',
            f'"{entity_name}" homepage',
            f'"{entity_name}" corporate site'
        ]

        # Execute search queries
        search_results = execute_web_search_queries(tool_context,
                                                    search_queries=base_url_queries,
                                                    entity_context=f"Finding base URLs for {entity_type}: {entity_name}",
                                                    max_results_per_query=3
                                                    )

        if search_results['status'] != 'success':
            return search_results

        # Extract and rank potential base URLs
        base_urls = []
        seen_domains = set()

        for result in search_results['results']:
            try:
                parsed = urlparse(result['url'])
                domain = parsed.netloc

                # Skip if we already have this domain
                if domain in seen_domains:
                    continue

                # Prefer root domain URLs
                if parsed.path in ['/', '', '/index.html', '/home']:
                    base_urls.insert(0, {
                        'url': result['url'],
                        'domain': domain,
                        'title': result['title'],
                        'snippet': result['snippet'],
                        'query': result['query'],
                        'priority': 'high'
                    })
                else:
                    base_urls.append({
                        'url': result['url'],
                        'domain': domain,
                        'title': result['title'],
                        'snippet': result['snippet'],
                        'query': result['query'],
                        'priority': 'medium'
                    })

                seen_domains.add(domain)

                if len(base_urls) >= max_urls:
                    break

            except Exception as e:
                logger.warning(f"Failed to process URL {result.get('url', '')}: {e}")
                continue

        result = create_success_response(
            data={
                'entity_name': entity_name,
                'entity_type': entity_type,
                'base_urls': base_urls[:max_urls],
                'total_candidates': len(search_results['data']['results']),
                'search_queries_used': base_url_queries
            },
            message=f"Found {len(base_urls)} base URLs for {entity_name}",
            metadata={
                METADATA_FIELDS['COUNT']: len(base_urls),
                METADATA_FIELDS['TOTAL_COUNT']: len(search_results['data']['results']),
                METADATA_FIELDS['EXTRACTION_METHOD']: 'base_url_discovery'
            },
        )
        return result

    except Exception as e:
        logger.exception(f"Failed to find base URLs for {entity_name}: {e}")
        return create_error_response(
            error_message=f'Failed to find base URLs for {entity_name}: {str(e)}',
            error_code='BASE_URL_DISCOVERY_ERROR',
        )
