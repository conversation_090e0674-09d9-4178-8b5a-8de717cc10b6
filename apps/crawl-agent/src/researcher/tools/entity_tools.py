"""Entity Management Tools

ADK tools for creating and managing entities in the knowledge graph.
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from eko.db import get_bo_conn
from eko.db.data.entity_relationship import EntityRelationshipData
from eko.models.canonical_predicate import CanonicalPredicate
from eko.models.entity_relationship_enums import RelationshipTypes
from google.adk.tools import ToolContext
from loguru import logger

from eko.models.entity_relationship import EntityRelationship
from researcher.services.kg_service import KnowledgeGraphService
from eko.db.data.entity import EntityExtended, industry_types, EntityData
from eko.models.common import EntityType, entity_types, name_to_eko_id
from researcher.models.relationships import KGRelationship
from researcher.models.entities import EnhancedKGEntity
from researcher.models.search_queries import SearchQueriesOutput, SearchQuery, GapAnalysisQueriesOutput


async def kg_upsert_entity(
        name: str,
        entity_type: str,
        identifiers: Dict[str, Any],
        common_name: Optional[str],
        description: Optional[str],
        owned_domains: List[str],
        url: Optional[str],
        jurisdiction: Optional[str],
        text_searchable_name: Optional[str],
        industry: Optional[str],
        tool_context: ToolContext

) -> Dict[str, Any]:
    f"""Create a new entity in the knowledge graph or update an existing one.
    
    Args:
        name: Entity name as references in the data
        entity_type: One of {entity_types}
        identifiers: Dictionary of identifiers key is 'lei', 'cik' or 'ticker', value is the identifier value
        common_name: Common name of the entity, this the name that the company is commonly known as, like Coca-Cola, Mars, OpenAI.
        description: Description of the entity, a description you generate to describe the company based on training data and the data in this conversation and/or the session - should be a paragraph long.
        owned_domains: List of owned domains, a list of domain names (google.com, chat.com etc.) that are owned by the entity.
        url: URL of the entity - the URL of the primary website for the entity.
        jurisdiction: Jurisdiction two letter country code for the jurisdiction this entity is based in - this is the legal jurisdiction. If more than one, do not supply a value.
        text_searchable_name: Text searchable name - a name that works well to search this entity within text, 
                so this is the most specific name that the entity is likely to be referred to by. 
                So if a company is called 'Ghost' that is hard to search for as it is a common word, 
                but 'Ghost company' or 'Ghost Ltd' is better. The best choice is the one that most frequently 
                appears in articles but is specific enough not to clash with other brands/companies/orgs or common words.
        industry: One of: {industry_types}
    Returns:
        Dict with entity creation results
    """
    try:
        if entity_type not in entity_types:
            return f"{entity_type} is not a valid entity type, choose one of {entity_types}"
        if industry not in industry_types:
            return f"{industry} is not a valid industry type, choose one of {industry_types}"
        # Check for existing entity
        if identifiers:
            existing = KnowledgeGraphService.find_entity_by_identifiers(identifiers)
            if existing:
                # update entity from params
                entity = existing.model_copy(update={
                    'eko_id': name_to_eko_id(entity_type, name) if not existing.eko_id else existing.eko_id,
                    'name': name if not existing.name else existing.name,
                    'type': entity_type if not existing.type else existing.type,
                    'description': description if not existing.description else existing.description,
                    'industry': industry if not existing.industry else existing.industry,
                    'common_name': common_name if not existing.common_name else existing.common_name,
                    'url': url if not existing.url else existing.url,
                    'owned_domains': owned_domains if not existing.owned_domains else [*existing.owned_domains,
                                                                                       *owned_domains],
                    'jurisdiction': jurisdiction if not existing.jurisdiction else existing.jurisdiction,
                    'text_searchable_name': text_searchable_name if not existing.text_searchable_name else existing.text_searchable_name,
                })
                KnowledgeGraphService.update_entity(entity)
                tool_context.state['last_created_entity'] = entity.model_dump()
                return {
                    'status': 'updated',
                    'entity_id': str(existing.id),
                    'entity_name': existing.name,
                    'entity': entity.model_dump()
                }

        # Create new entity using EntityExtended (compatible with existing database)
        # Extract proper entity information from parameters and identifiers
        entity = EntityExtended(
            id=None,  # Will be set by database
            name=name,
            type=entity_type,
            description=description,
            eko_id=name_to_eko_id(entity_type, name),
            lei=None,
            lei_record=None,
            lei_exact=None,
            ticker=None,
            canonical=False,
            industry=industry,
            legal_name=None,
            region_name=None,
            common_name=common_name,
            owned_domains=owned_domains,
            url=url,
            llm_cleaned_name=None,
            jurisdiction=jurisdiction,
            scope=None,
            text_searchable_name=text_searchable_name
        )

        entity = KnowledgeGraphService.create_entity(entity)
        tool_context.state['last_created_entity'] = entity.model_dump()

        return {
            'status': 'created',
            'entity_id': entity.id,
            'entity': entity.model_dump(),
            'message': f'Created entity: {name}'
        }

    except Exception as e:
        logger.exception(f"Failed to create entity: {name}")
        return {
            'status': 'error',
            'message': f'Failed to create entity: {str(e)}'
        }


async def kg_create_relationship(
        source_entity_id: str,
        target_entity_id: str,
        relationship_type: str,
        weight: float = 0.5,
        properties: Optional[Dict[str, Any]] = None,
        evidence_urls: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create a relationship between two entities.
    
    Args:
        source_entity_id: Source entity eko_id
        target_entity_id: Target entity eko_id
        relationship_type: Type of relationship
        weight: Relationship strength (0-1)
        properties: Additional relationship properties
        evidence_urls: URLs supporting this relationship
    
    Returns:
        Dict with relationship creation results
    """
    try:
        # Validate entities exist
        source_entity = KnowledgeGraphService.get_entity_by_eko_id(source_entity_id)
        target_entity = KnowledgeGraphService.get_entity_by_eko_id(target_entity_id)

        if not source_entity or not target_entity:
            return {
                'status': 'error',
                'message': 'Source or target entity not found'
            }

        # Check for existing relationship
        existing = KnowledgeGraphService.find_relationship(
            source_entity_id, target_entity_id, relationship_type
        )

        if existing:
            return {
                'status': 'exists',
                'relationship_id': existing.get('id'),
                'message': f'Relationship already exists: {relationship_type}'
            }

        # Create new relationship
        relationship = KGRelationship(
            id="",  # Not used in database schema
            source_entity_id=source_entity_id,
            target_entity_id=target_entity_id,
            relationship_type=relationship_type,
            weight=weight,
            properties=properties or {},
            evidence_documents=evidence_urls or [],
            discovery_method='adk_crawler'
        )

        relationship= EntityRelationship(from_entity_id=source_entity.id,
                           to_entity_id=target_entity.id,
                           relationship_category='business',
                           relationship_type=relationship_type,
                           relationship_sub_type=None,
                           relationship_source='adk_crawler',
                           relationship_data=relationship.properties,
                           canonical=True,
                           confidence_score=relationship.weight,
                           discovery_method='adk_crawler',
                           discovered_date=datetime.now().date(),
                           verified=False,
                           verification_source=None,
                           metadata={
                               'evidence_urls': evidence_urls
                           }

                           )

        rel_id = KnowledgeGraphService.create_relationship(relationship)

        return {
            'status': 'created',
            'relationship_id': rel_id,
            'message': f'Created {relationship_type} relationship'
        }

    except Exception as e:
        logger.exception(f"Failed to create relationship: {relationship_type}")
        return {
            'status': 'error',
            'message': f'Failed to create relationship: {str(e)}'
        }


async def kg_search_entities(
        search_term: str,
        entity_type: Optional[str] = None,
        limit: int = 10,
        min_confidence: float = 0.0
) -> Dict[str, Any]:
    """Search for entities in the knowledge graph.
    
    Args:
        search_term: Text to search for
        entity_type: Filter by entity type
        limit: Maximum results to return
        min_confidence: Minimum confidence score
    
    Returns:
        Dict with search results
    """
    try:
        entities = KnowledgeGraphService.search_entities(
            search_term=search_term,
            entity_type=entity_type,
            limit=limit,
            min_confidence=min_confidence
        )

        return {
            'status': 'success',
            'entities': [entity.model_dump() for entity in entities],
            'count': len(entities)
        }

    except Exception as e:
        logger.exception(f"Failed to search entities: {search_term}")
        return {
            'status': 'error',
            'message': f'Failed to search entities: {str(e)}'
        }


async def kg_get_entity_relationships(
        name: str,
        relationship_predicates: List[str]
) -> Dict[str, Any]:
    f"""Get relationships for an entity

    Args:
        name: Name of the entity
        relationship_predicates: List of relationship predicates to include, from [{','.join([cp.value for cp in CanonicalPredicate])}]

    """
    if len(relationship_predicates) == 0:
        return {
            'status': 'error',
            'message': f"No relationship predicates provided, valid predicates are [{','.join([cp.value for cp in CanonicalPredicate])}]"
        }
    relationships = []
    predicates=[]
    for pred in relationship_predicates:
        try:
            predicates.append(CanonicalPredicate(pred))
        except ValueError:
            return {
                'status': 'error',
                'message': f"Invalid relationship predicate: {pred} valid predicates are [{','.join([cp.value for cp in CanonicalPredicate])}]"
            }
    with get_bo_conn() as conn:
        entities = EntityData.fuzzy_search(conn, name, False)
        logger.info(f"Found {len(entities)} entities for {name}")
        for entity in entities:
            names = EntityRelationshipData.get_entity_relationships_with_names(conn, entity.id, predicates)
            if len(names) > 0:
                logger.info(f"Found {len(names)} relationships for {entity.id}:{entity.name} ")
                relationships.extend(names)
        return relationships



async def kg_get_company_related(
        name: str
) -> Dict[str, Any]:
    f"""Get relationships for an entity

    Args:
        name: Name of the entity
"""
    relationships = []
    predicates=CanonicalPredicate.get_ownership_and_funding_predicates()
    with get_bo_conn() as conn:
        entities = EntityData.fuzzy_search(conn, name, False)
        logger.info(f"Found {len(entities)} entities for {name}")
        for entity in entities:
            names = EntityRelationshipData.get_entity_relationships_with_names(conn, entity.id, predicates)
            if len(names) > 0:
                logger.info(f"Found {len(names)} relationships for {entity.id}:{entity.name} ")
                relationships.extend(names)
        return relationships


async def generate_advanced_search_queries(
        entity_name: str,
        entity_data: Optional[Dict[str, Any]] = None,
        relationships: Optional[List[Dict[str, Any]]] = None,
        discovered_patterns: Optional[List[str]] = None,
        search_history: Optional[List[Dict[str, Any]]] = None,
        successful_terms: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Generate sophisticated search queries based on entity analysis and discoveries.
    
    This implements advanced search query generation with refinement that:
    - Creates search terms based on discovered relationships
    - Generates creative queries from entity patterns
    - Uses discovered terminology and executive names
    - Builds industry-specific and regulatory-focused searches
    - Refines search terms based on historical effectiveness
    - Learns from successful and unsuccessful search patterns
    
    Args:
        entity_name: Primary entity name
        entity_data: Analysis data about the entity
        relationships: Known relationships for the entity
        discovered_patterns: Patterns discovered in previous searches
        search_history: Historical search results for learning
        successful_terms: Terms that have yielded good results
        
    Returns:
        Dict with categorized and refined search queries
    """
    try:
        query_categories = {
            'regulatory_queries': [],
            'relationship_queries': [],
            'industry_queries': [],
            'esg_queries': [],
            'financial_queries': [],
            'subsidiary_queries': [],
            'executive_queries': [],
            'document_queries': [],
            'media_queries': [],
            'pattern_queries': []
        }

        # Base entity queries
        base_queries = [
            f'"{entity_name}" company profile',
            f'"{entity_name}" corporate information',
            f'"{entity_name}" business overview'
        ]

        # Regulatory and compliance queries
        query_categories['regulatory_queries'].extend([
            f'"{entity_name}" SEC filings',
            f'"{entity_name}" annual report 10-K',
            f'"{entity_name}" quarterly report 10-Q',
            f'"{entity_name}" proxy statement DEF 14A',
            f'"{entity_name}" 8-K current report',
            f'"{entity_name}" regulatory compliance',
            f'"{entity_name}" legal proceedings',
            f'"{entity_name}" regulatory violations'
        ])

        # ESG and sustainability queries
        query_categories['esg_queries'].extend([
            f'"{entity_name}" sustainability report',
            f'"{entity_name}" ESG report',
            f'"{entity_name}" environmental impact',
            f'"{entity_name}" carbon footprint',
            f'"{entity_name}" climate change',
            f'"{entity_name}" social responsibility',
            f'"{entity_name}" governance practices',
            f'"{entity_name}" diversity inclusion',
            f'"{entity_name}" stakeholder engagement'
        ])

        # Financial performance queries
        query_categories['financial_queries'].extend([
            f'"{entity_name}" earnings call transcript',
            f'"{entity_name}" investor presentation',
            f'"{entity_name}" financial results',
            f'"{entity_name}" earnings report',
            f'"{entity_name}" investor day',
            f'"{entity_name}" analyst coverage',
            f'"{entity_name}" credit rating'
        ])

        # Document-specific queries
        query_categories['document_queries'].extend([
            f'"{entity_name}" filetype:pdf annual report',
            f'"{entity_name}" filetype:pdf sustainability',
            f'"{entity_name}" filetype:pdf investor',
            f'"{entity_name}" filetype:doc policy',
            f'"{entity_name}" filetype:xlsx financial',
            f'site:{entity_name.lower().replace(" ", "")}.com filetype:pdf'
        ])

        # Process entity data for enhanced queries
        if entity_data:
            # Extract industry information
            industry = entity_data.get('industry') or entity_data.get('sector')
            if industry:
                query_categories['industry_queries'].extend([
                    f'"{entity_name}" {industry} industry',
                    f'"{entity_name}" {industry} trends',
                    f'"{entity_name}" {industry} competition',
                    f'"{entity_name}" {industry} regulation'
                ])

            # Extract location information
            headquarters = entity_data.get('headquarters') or entity_data.get('location')
            if headquarters:
                query_categories['regulatory_queries'].extend([
                    f'"{entity_name}" {headquarters} operations',
                    f'"{entity_name}" {headquarters} regulatory',
                    f'"{entity_name}" {headquarters} environmental'
                ])

            # Extract executive information
            executives = entity_data.get('executives', []) or entity_data.get('officers', [])
            for exec_info in executives[:5]:  # Limit to top 5
                exec_name = exec_info.get('name') if isinstance(exec_info, dict) else str(exec_info)
                if exec_name:
                    query_categories['executive_queries'].extend([
                        f'"{exec_name}" "{entity_name}" CEO',
                        f'"{exec_name}" "{entity_name}" leadership',
                        f'"{exec_name}" "{entity_name}" interview',
                        f'"{exec_name}" "{entity_name}" statement'
                    ])

        # Process relationships for targeted queries
        if relationships:
            for relationship in relationships:
                rel_type = relationship.get('type', '').upper()
                target_name = relationship.get('target_name', '')

                if not target_name:
                    continue

                if rel_type == 'OWNS':
                    query_categories['subsidiary_queries'].extend([
                        f'"{entity_name}" subsidiary "{target_name}"',
                        f'"{target_name}" owned by "{entity_name}"',
                        f'"{target_name}" parent company "{entity_name}"',
                        f'"{target_name}" corporate structure',
                        f'"{target_name}" sustainability report',
                        f'"{target_name}" ESG performance'
                    ])
                elif rel_type == 'SUPPLIES':
                    query_categories['relationship_queries'].extend([
                        f'"{entity_name}" supplier "{target_name}"',
                        f'"{target_name}" customer "{entity_name}"',
                        f'"{entity_name}" "{target_name}" supply chain',
                        f'"{entity_name}" "{target_name}" partnership'
                    ])
                elif rel_type == 'PARTNER_OF':
                    query_categories['relationship_queries'].extend([
                        f'"{entity_name}" partner "{target_name}"',
                        f'"{entity_name}" "{target_name}" collaboration',
                        f'"{entity_name}" "{target_name}" joint venture',
                        f'"{entity_name}" "{target_name}" alliance'
                    ])
                elif rel_type == 'FUNDED_BY':
                    query_categories['financial_queries'].extend([
                        f'"{entity_name}" funded by "{target_name}"',
                        f'"{target_name}" investment "{entity_name}"',
                        f'"{entity_name}" "{target_name}" financing'
                    ])

        # Generate pattern-based queries from discovered patterns
        if discovered_patterns:
            for pattern in discovered_patterns:
                query_categories['pattern_queries'].extend([
                    f'"{entity_name}" {pattern}',
                    f'{pattern} "{entity_name}"'
                ])

        # Apply search term refinement based on historical data
        if search_history or successful_terms:
            refined_queries = _refine_queries_from_history(
                query_categories, search_history, successful_terms, entity_name
            )
            query_categories.update(refined_queries)

        # Media and news queries
        query_categories['media_queries'].extend([
            f'"{entity_name}" news latest',
            f'"{entity_name}" press release',
            f'"{entity_name}" media coverage',
            f'"{entity_name}" controversy',
            f'"{entity_name}" scandal',
            f'"{entity_name}" criticism',
            f'"{entity_name}" activist groups',
            f'"{entity_name}" NGO reports',
            f'"{entity_name}" investigation'
        ])

        # Calculate total queries generated
        total_queries = sum(len(queries) for queries in query_categories.values())

        # Prioritize queries by relevance and potential value with historical context
        prioritized_queries = _prioritize_search_queries(query_categories, search_history)

        return {
            'status': 'success',
            'entity_name': entity_name,
            'query_categories': query_categories,
            'prioritized_queries': prioritized_queries,
            'total_queries_generated': total_queries,
            'generation_metadata': {
                'has_entity_data': entity_data is not None,
                'relationships_processed': len(relationships) if relationships else 0,
                'patterns_used': len(discovered_patterns) if discovered_patterns else 0,
                'search_history_used': len(search_history) if search_history else 0,
                'successful_terms_applied': len(successful_terms) if successful_terms else 0,
                'refinement_applied': bool(search_history or successful_terms)
            }
        }

    except Exception as e:
        logger.exception(f"Failed to generate advanced search queries for {entity_name}")
        return {
            'status': 'error',
            'message': f'Failed to generate search queries: {str(e)}'
        }


async def generate_positive_search_queries(
        entity_name: str,
        entity_data: Optional[Dict[str, Any]] = None,
        relationships: Optional[List[Dict[str, Any]]] = None,
        discovered_patterns: Optional[List[str]] = None,
        search_history: Optional[List[Dict[str, Any]]] = None,
        successful_terms: Optional[List[str]] = None,
        tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Generate positive search queries focused on achievements, goals, and positive outcomes.
    
    This generates search queries specifically designed to find:
    - Sustainability achievements and goals
    - Positive impact stories and initiatives
    - Awards and recognition
    - Innovation and progress announcements
    - Successful programs and partnerships
    """
    try:
        # Generate positive-focused search queries directly
        positive_queries = _generate_positive_specific_queries(entity_name, {})
        
        # Enhance with any existing advanced search results if needed
        if entity_data or relationships or discovered_patterns:
            results = await generate_advanced_search_queries(
                entity_name=entity_name,
                entity_data=entity_data,
                relationships=relationships,
                discovered_patterns=discovered_patterns,
                search_history=search_history,
                successful_terms=successful_terms
            )
            
            if results['status'] == 'success':
                # Add positive-leaning queries from existing categories
                # Add positive-leaning queries from existing categories (ESG, financial reports, etc.)
                for category_name, queries in results['query_categories'].items():
                    if category_name in ['esg_queries', 'financial_queries']:
                        for query in queries:
                            if any(positive_term in query.lower() for positive_term in 
                                  ['report', 'strategy', 'framework', 'goals', 'targets', 'achievements']):
                                positive_queries.append(query)
        
        # Store in session with positive marker
        session_data = {
            'entity_name': entity_name,
            'search_queries': positive_queries,
            'query_type': 'positive',
            'total_queries_generated': len(positive_queries),
            'timestamp': '',
        }

        logger.info(f"Generated {len(positive_queries)} positive search queries for session storage")
        
        if tool_context:
            tool_context.state['positive_search_queries'] = session_data
        
        return {
            'status': 'success',
            'message': f"Generated {len(positive_queries)} positive search queries",
            'total_queries': len(positive_queries),
            'query_type': 'positive',
            'search_queries': positive_queries,
            'query_data': session_data,
            'stored_in_session': True
        }

    except Exception as e:
        logger.exception(f"Failed to generate positive search queries for {entity_name}")
        return {
            'status': 'error',
            'message': f'Failed to generate positive search queries: {str(e)}'
        }


async def generate_negative_search_queries(
        entity_name: str,
        entity_data: Optional[Dict[str, Any]] = None,
        relationships: Optional[List[Dict[str, Any]]] = None,
        discovered_patterns: Optional[List[str]] = None,
        search_history: Optional[List[Dict[str, Any]]] = None,
        successful_terms: Optional[List[str]] = None,
        tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Generate negative search queries focused on controversies, failures, and critical analysis.
    
    This generates search queries specifically designed to find:
    - Controversies and scandals
    - Regulatory violations and legal issues
    - Failed initiatives and unmet promises
    - Criticism from NGOs and activists
    - Environmental and social impact concerns
    """
    try:
        # Generate negative-focused search queries directly
        negative_queries = _generate_negative_specific_queries(entity_name, {})
        
        # Enhance with any existing advanced search results if needed
        if entity_data or relationships or discovered_patterns:
            results = await generate_advanced_search_queries(
                entity_name=entity_name,
                entity_data=entity_data,
                relationships=relationships,
                discovered_patterns=discovered_patterns,
                search_history=search_history,
                successful_terms=successful_terms
            )
            
            if results['status'] == 'success':
                # Add negative-leaning queries from existing categories (media, regulatory, etc.)
                for category_name, queries in results['query_categories'].items():
                    if category_name in ['media_queries', 'regulatory_queries']:
                        for query in queries:
                            if any(negative_term in query.lower() for negative_term in 
                                  ['controversy', 'scandal', 'criticism', 'investigation', 'violation', 'proceedings']):
                                negative_queries.append(query)
            
        
        # Store in session with negative marker
        session_data = {
            'entity_name': entity_name,
            'search_queries': negative_queries,
            'query_type': 'negative',
            'total_queries_generated': len(negative_queries),
            'timestamp': '',
        }

        logger.info(f"Generated {len(negative_queries)} negative search queries for session storage")
        
        if tool_context:
            tool_context.state['negative_search_queries'] = session_data
        
        return {
            'status': 'success',
            'message': f"Generated {len(negative_queries)} negative search queries",
            'total_queries': len(negative_queries),
            'query_type': 'negative',
            'search_queries': negative_queries,
            'query_data': session_data,
            'stored_in_session': True
        }

    except Exception as e:
        logger.exception(f"Failed to generate negative search queries for {entity_name}")
        return {
            'status': 'error',
            'message': f'Failed to generate negative search queries: {str(e)}'
        }


async def generate_relationship_search_queries(
        entity_name: str,
        entity_data: Optional[Dict[str, Any]] = None,
        relationships: Optional[List[Dict[str, Any]]] = None,
        discovered_patterns: Optional[List[str]] = None,
        search_history: Optional[List[Dict[str, Any]]] = None,
        successful_terms: Optional[List[str]] = None,
        tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Generate relationship search queries focused on ownership, supply chains, funding, and business connections.
    
    This generates search queries specifically designed to find:
    - Ownership structures and corporate hierarchies
    - Supply chain relationships and partnerships
    - Investment and funding connections
    - Joint ventures and strategic alliances
    - Subsidiary and parent company relationships
    - Board member and executive connections
    """
    try:
        # Generate relationship-focused search queries directly
        relationship_queries = _generate_relationship_specific_queries(entity_name, {})
        
        # Enhance with any existing advanced search results if needed for known relationships
        if entity_data or relationships or discovered_patterns:
            results = await generate_advanced_search_queries(
                entity_name=entity_name,
                entity_data=entity_data,
                relationships=relationships,
                discovered_patterns=discovered_patterns,
                search_history=search_history,
                successful_terms=successful_terms
            )
            
            if results['status'] == 'success':
                # Add relationship-leaning queries from existing categories
                for category_name, queries in results['query_categories'].items():
                    if category_name in ['subsidiary_queries', 'relationship_queries', 'executive_queries']:
                        relationship_queries.extend(queries)
        
        # Store in session with relationship marker
        session_data = {
            'entity_name': entity_name,
            'search_queries': relationship_queries,
            'query_type': 'relationship',
            'total_queries_generated': len(relationship_queries),
            'timestamp': '',
        }

        logger.info(f"Generated {len(relationship_queries)} relationship search queries for session storage")
        
        if tool_context:
            tool_context.state['relationship_search_queries'] = session_data
        
        return {
            'status': 'success',
            'message': f"Generated {len(relationship_queries)} relationship search queries",
            'total_queries': len(relationship_queries),
            'query_type': 'relationship',
            'search_queries': relationship_queries,
            'query_data': session_data,
            'stored_in_session': True
        }

    except Exception as e:
        logger.exception(f"Failed to generate relationship search queries for {entity_name}")
        return {
            'status': 'error',
            'message': f'Failed to generate relationship search queries: {str(e)}'
        }


async def generate_and_store_search_queries(
        entity_name: str,
        entity_data: Optional[Dict[str, Any]] = None,
        relationships: Optional[List[Dict[str, Any]]] = None,
        discovered_patterns: Optional[List[str]] = None,
        search_history: Optional[List[Dict[str, Any]]] = None,
        successful_terms: Optional[List[str]] = None,
        tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """Generate search queries and store structured results in session state.
    
    This is a wrapper around generate_advanced_search_queries that stores
    the structured results in session state for other agents to use.
    """
    try:
        # Generate the search queries
        results = await generate_advanced_search_queries(
            entity_name=entity_name,
            entity_data=entity_data,
            relationships=relationships,
            discovered_patterns=discovered_patterns,
            search_history=search_history,
            successful_terms=successful_terms
        )

        if results['status'] == 'success':
            # Extract all queries into a flat list for easy consumption by execute_web_search_queries
            all_queries = []
            for category_queries in results['query_categories'].values():
                all_queries.extend(category_queries)

            # Also include prioritized queries as they are higher value
            prioritized_query_strings = [pq['query'] for pq in results['prioritized_queries']]

            # Structure the data for session storage and consumption by execute_web_search_queries
            session_data = {
                'entity_name': entity_name,
                'search_queries': all_queries,  # Simple list for execute_web_search_queries
                'prioritized_queries': prioritized_query_strings,
                'query_categories': results['query_categories'],
                'total_queries_generated': results['total_queries_generated'],
                'generation_metadata': results['generation_metadata'],
                'timestamp': results.get('timestamp', ''),
                'prioritized_details': results['prioritized_queries']  # Full prioritized data with scores
            }

            logger.info(f"Generated {results['total_queries_generated']} search queries for session storage")

            tool_context.state['search_queries'] = session_data
            return {
                'status': 'success',
                'message': f"Generated {results['total_queries_generated']} search queries across {len(results['query_categories'])} categories",
                'total_queries': results['total_queries_generated'],
                'categories': list(results['query_categories'].keys()),
                'search_queries': all_queries,  # Direct access for tools
                'query_data': session_data,  # Structured data for ADK session storage
                'stored_in_session': True
            }
        else:
            tool_context.state['search_queries'] = []
        return results

    except Exception as e:
        logger.exception(f"Failed to generate and store search queries for {entity_name}")
        return {
            'status': 'error',
            'message': f'Failed to generate and store search queries: {str(e)}'
        }


def _prioritize_search_queries(
        query_categories: Dict[str, List[str]],
        search_history: Optional[List[Dict[str, Any]]] = None
) -> List[Dict[str, Any]]:
    """Prioritize search queries by potential value and relevance with historical context."""

    # Priority weights for different query types
    priority_weights = {
        'regulatory_queries': 0.9,  # High - official documents
        'subsidiary_queries': 0.85,  # High - direct relationships
        'esg_queries': 0.8,  # High - target domain
        'financial_queries': 0.75,  # Medium-high - transparency
        'document_queries': 0.7,  # Medium-high - direct documents
        'relationship_queries': 0.65,  # Medium - indirect relationships
        'executive_queries': 0.6,  # Medium - leadership insights
        'industry_queries': 0.55,  # Medium-low - broader context
        'media_queries': 0.5,  # Medium-low - external perspective
        'pattern_queries': 0.4  # Low - experimental
    }

    prioritized = []

    for category, queries in query_categories.items():
        weight = priority_weights.get(category, 0.3)

        for query in queries:
            historical_boost = _calculate_historical_boost(query, search_history) if search_history else 0.0
            prioritized.append({
                'query': query,
                'category': category,
                'priority_score': weight + historical_boost,
                'estimated_value': _estimate_query_value(query, category),
                'historical_effectiveness': historical_boost
            })

    # Sort by priority score and estimated value
    prioritized.sort(key=lambda x: (x['priority_score'], x['estimated_value']), reverse=True)

    return prioritized[:100]  # Return top 100 prioritized queries


def _estimate_query_value(query: str, category: str) -> float:
    """Estimate the potential value of a search query."""
    value_score = 0.5  # Base score

    # Boost for specific document types
    if 'filetype:pdf' in query:
        value_score += 0.2

    # Boost for specific document keywords
    high_value_terms = ['annual report', 'sustainability report', 'ESG report',
                        'SEC filing', '10-K', '10-Q', 'proxy statement']
    for term in high_value_terms:
        if term in query.lower():
            value_score += 0.15

    # Boost for relationship queries with specific entities
    if category in ['subsidiary_queries', 'relationship_queries'] and '"' in query:
        value_score += 0.1

    # Boost for regulatory and compliance terms
    regulatory_terms = ['regulatory', 'compliance', 'violation', 'investigation']
    for term in regulatory_terms:
        if term in query.lower():
            value_score += 0.1

    return min(1.0, value_score)


async def generate_gap_analysis_queries(
        entity_name: str,
        discovered_information: Dict[str, Any],
        expected_information_types: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Generate search queries to fill gaps in entity information.
    
    Args:
        entity_name: Entity name
        discovered_information: Information already discovered
        expected_information_types: Types of information expected to exist
        
    Returns:
        Dict with gap analysis and targeted queries
    """
    try:
        # Define expected information categories
        standard_categories = [
            'corporate_structure', 'financial_performance', 'sustainability_reporting',
            'governance_practices', 'regulatory_filings', 'stakeholder_engagement',
            'environmental_impact', 'social_responsibility', 'risk_management',
            'supply_chain', 'innovation', 'market_position'
        ]

        expected_categories = expected_information_types or standard_categories

        # Analyze gaps
        gaps_identified = []
        gap_queries = []

        for category in expected_categories:
            if not _has_information_for_category(discovered_information, category):
                gaps_identified.append(category)

                # Generate targeted queries for this gap
                category_queries = _generate_gap_filling_queries(entity_name, category)
                gap_queries.extend(category_queries)

        return {
            'status': 'success',
            'entity_name': entity_name,
            'gaps_identified': gaps_identified,
            'gap_filling_queries': gap_queries,
            'completeness_score': 1 - (len(gaps_identified) / len(expected_categories)),
            'analysis_summary': {
                'total_categories_expected': len(expected_categories),
                'categories_with_information': len(expected_categories) - len(gaps_identified),
                'categories_missing': len(gaps_identified),
                'queries_generated': len(gap_queries)
            }
        }

    except Exception as e:
        logger.exception(f"Failed to generate gap analysis queries for {entity_name}")
        return {
            'status': 'error',
            'message': f'Failed to generate gap analysis: {str(e)}'
        }


def _has_information_for_category(discovered_info: Dict[str, Any], category: str) -> bool:
    """Check if discovered information covers a specific category."""
    category_keywords = {
        'corporate_structure': ['subsidiary', 'parent', 'ownership', 'corporate'],
        'financial_performance': ['revenue', 'profit', 'earnings', 'financial'],
        'sustainability_reporting': ['sustainability', 'esg', 'environment'],
        'governance_practices': ['governance', 'board', 'directors', 'management'],
        'regulatory_filings': ['sec', '10-k', '10-q', 'filing', 'regulatory'],
        'stakeholder_engagement': ['stakeholder', 'community', 'engagement'],
        'environmental_impact': ['environment', 'carbon', 'emissions', 'climate'],
        'social_responsibility': ['social', 'responsibility', 'community', 'employee'],
        'risk_management': ['risk', 'management', 'compliance', 'audit'],
        'supply_chain': ['supply', 'chain', 'supplier', 'vendor'],
        'innovation': ['innovation', 'research', 'development', 'technology'],
        'market_position': ['market', 'competition', 'industry', 'position']
    }

    keywords = category_keywords.get(category, [])

    # Check if any content contains category keywords
    content_str = str(discovered_info).lower()
    return any(keyword in content_str for keyword in keywords)


def _generate_gap_filling_queries(entity_name: str, category: str) -> List[str]:
    """Generate specific queries to fill information gaps."""
    category_queries = {
        'corporate_structure': [
            f'"{entity_name}" corporate structure',
            f'"{entity_name}" organizational chart',
            f'"{entity_name}" subsidiaries list',
            f'"{entity_name}" parent company',
            f'"{entity_name}" ownership structure'
        ],
        'financial_performance': [
            f'"{entity_name}" financial results latest',
            f'"{entity_name}" quarterly earnings',
            f'"{entity_name}" revenue breakdown',
            f'"{entity_name}" profitability analysis'
        ],
        'sustainability_reporting': [
            f'"{entity_name}" sustainability strategy',
            f'"{entity_name}" environmental goals',
            f'"{entity_name}" ESG framework',
            f'"{entity_name}" sustainability initiatives'
        ],
        'governance_practices': [
            f'"{entity_name}" corporate governance',
            f'"{entity_name}" board composition',
            f'"{entity_name}" executive compensation',
            f'"{entity_name}" governance policies'
        ],
        'regulatory_filings': [
            f'"{entity_name}" recent SEC filings',
            f'"{entity_name}" regulatory submissions',
            f'"{entity_name}" compliance reports',
            f'"{entity_name}" legal disclosures'
        ],
        'stakeholder_engagement': [
            f'"{entity_name}" stakeholder relations',
            f'"{entity_name}" community engagement',
            f'"{entity_name}" shareholder communications',
            f'"{entity_name}" public consultations'
        ],
        'environmental_impact': [
            f'"{entity_name}" environmental performance',
            f'"{entity_name}" carbon emissions',
            f'"{entity_name}" environmental management',
            f'"{entity_name}" climate action'
        ],
        'social_responsibility': [
            f'"{entity_name}" social impact',
            f'"{entity_name}" community programs',
            f'"{entity_name}" employee welfare',
            f'"{entity_name}" social initiatives'
        ],
        'risk_management': [
            f'"{entity_name}" risk assessment',
            f'"{entity_name}" risk mitigation',
            f'"{entity_name}" operational risks',
            f'"{entity_name}" compliance framework'
        ],
        'supply_chain': [
            f'"{entity_name}" supply chain management',
            f'"{entity_name}" supplier relations',
            f'"{entity_name}" procurement practices',
            f'"{entity_name}" supply chain sustainability'
        ],
        'innovation': [
            f'"{entity_name}" innovation strategy',
            f'"{entity_name}" research development',
            f'"{entity_name}" technology initiatives',
            f'"{entity_name}" digital transformation'
        ],
        'market_position': [
            f'"{entity_name}" market share',
            f'"{entity_name}" competitive position',
            f'"{entity_name}" industry leadership',
            f'"{entity_name}" market analysis'
        ]
    }

    return category_queries.get(category, [f'"{entity_name}" {category}'])


def _refine_queries_from_history(
        query_categories: Dict[str, List[str]],
        search_history: Optional[List[Dict[str, Any]]],
        successful_terms: Optional[List[str]],
        entity_name: str
) -> Dict[str, List[str]]:
    """Refine search queries based on historical effectiveness and successful terms."""
    refined_categories = {}

    try:
        # Create new category for refined queries
        refined_categories['refined_queries'] = []

        # Add successful terms as new queries
        if successful_terms:
            for term in successful_terms:
                refined_categories['refined_queries'].extend([
                    f'"{entity_name}" {term}',
                    f'{term} "{entity_name}"',
                    f'"{entity_name}" {term} report',
                    f'"{entity_name}" {term} strategy'
                ])

        # Extract high-performing patterns from search history
        if search_history:
            effective_patterns = _extract_effective_patterns(search_history)

            for pattern in effective_patterns:
                refined_categories['refined_queries'].extend([
                    f'"{entity_name}" {pattern}',
                    f'{pattern} "{entity_name}" analysis',
                    f'"{entity_name}" {pattern} disclosure'
                ])

        # Create adaptive queries based on discovered gaps
        if search_history:
            gap_terms = _identify_information_gaps(search_history)
            refined_categories['gap_filling_queries'] = []

            for gap_term in gap_terms:
                refined_categories['gap_filling_queries'].extend([
                    f'"{entity_name}" {gap_term}',
                    f'"{entity_name}" {gap_term} policy',
                    f'"{entity_name}" {gap_term} initiative'
                ])

        return refined_categories

    except Exception as e:
        logger.warning(f"Failed to refine queries from history: {e}")
        return {}


def _calculate_historical_boost(query: str, search_history: List[Dict[str, Any]]) -> float:
    """Calculate effectiveness boost based on similar historical queries."""
    if not search_history:
        return 0.0

    try:
        boost = 0.0
        query_words = set(query.lower().split())

        for search_record in search_history:
            historical_query = search_record.get('query', '').lower()
            historical_words = set(historical_query.split())

            # Calculate word overlap
            overlap = len(query_words.intersection(historical_words))
            if overlap > 0:
                # Get effectiveness metrics
                effectiveness = search_record.get('effectiveness_score', 0.0)
                documents_found = search_record.get('documents_found', 0)
                relevance_score = search_record.get('average_relevance', 0.0)

                # Calculate boost based on historical performance
                similarity = overlap / len(query_words.union(historical_words))
                performance_score = (effectiveness + (documents_found / 10) + relevance_score) / 3
                boost += similarity * performance_score * 0.1

        return min(0.3, boost)  # Cap boost at 0.3

    except Exception as e:
        logger.warning(f"Failed to calculate historical boost: {e}")
        return 0.0


def _extract_effective_patterns(search_history: List[Dict[str, Any]]) -> List[str]:
    """Extract effective search patterns from historical data."""
    patterns = []

    try:
        # Find high-performing queries
        high_performers = []
        for record in search_history:
            effectiveness = record.get('effectiveness_score', 0.0)
            documents_found = record.get('documents_found', 0)

            if effectiveness > 0.7 or documents_found > 5:
                high_performers.append(record)

        # Extract common terms from high-performing queries
        term_frequency = {}
        for record in high_performers:
            query = record.get('query', '').lower()
            words = query.replace('"', '').split()

            for word in words:
                if len(word) > 3 and word not in ['the', 'and', 'for', 'with']:
                    term_frequency[word] = term_frequency.get(word, 0) + 1

        # Get most frequent effective terms
        sorted_terms = sorted(term_frequency.items(), key=lambda x: x[1], reverse=True)
        patterns = [term for term, freq in sorted_terms[:10] if freq > 1]

        return patterns

    except Exception as e:
        logger.warning(f"Failed to extract effective patterns: {e}")
        return []


def _identify_information_gaps(search_history: List[Dict[str, Any]]) -> List[str]:
    """Identify information gaps based on unsuccessful search patterns."""
    gap_terms = []

    try:
        # Standard information categories we expect to find
        expected_categories = [
            'governance', 'sustainability', 'risk management', 'compliance',
            'stakeholder engagement', 'environmental impact', 'social responsibility',
            'innovation', 'supply chain', 'financial performance'
        ]

        # Find categories with low search success
        category_success = {}
        for category in expected_categories:
            category_success[category] = []

        for record in search_history:
            query = record.get('query', '').lower()
            effectiveness = record.get('effectiveness_score', 0.0)

            for category in expected_categories:
                if category.replace(' ', '') in query.replace(' ', '') or category in query:
                    category_success[category].append(effectiveness)

        # Identify poorly performing categories
        for category, scores in category_success.items():
            if not scores or (scores and sum(scores) / len(scores) < 0.3):
                gap_terms.append(category)

        return gap_terms[:5]  # Return top 5 gaps

    except Exception as e:
        logger.warning(f"Failed to identify information gaps: {e}")
        return []


def _generate_positive_specific_queries(entity_name: str, query_categories: Dict[str, List[str]]) -> List[str]:
    """Generate search queries focused on positive outcomes, achievements, and goals."""
    positive_queries = []
    
    # Positive sustainability and ESG queries
    positive_queries.extend([
        f'"{entity_name}" sustainability achievements',
        f'"{entity_name}" ESG goals met',
        f'"{entity_name}" environmental targets achieved',
        f'"{entity_name}" carbon neutral success',
        f'"{entity_name}" renewable energy progress',
        f'"{entity_name}" sustainability awards',
        f'"{entity_name}" ESG recognition',
        f'"{entity_name}" green initiatives success',
        f'"{entity_name}" climate action achievements',
        f'"{entity_name}" sustainable development goals'
    ])
    
    # Positive innovation and technology queries
    positive_queries.extend([
        f'"{entity_name}" innovation breakthrough',
        f'"{entity_name}" technology advancement',
        f'"{entity_name}" R&D success',
        f'"{entity_name}" patent achievements',
        f'"{entity_name}" digital transformation success',
        f'"{entity_name}" technology awards',
        f'"{entity_name}" innovation recognition'
    ])
    
    # Positive social impact queries
    positive_queries.extend([
        f'"{entity_name}" community impact success',
        f'"{entity_name}" social responsibility achievements',
        f'"{entity_name}" diversity inclusion progress',
        f'"{entity_name}" employee satisfaction high',
        f'"{entity_name}" workplace awards',
        f'"{entity_name}" community partnership success',
        f'"{entity_name}" charitable giving impact'
    ])
    
    # Positive financial and business performance
    positive_queries.extend([
        f'"{entity_name}" financial growth',
        f'"{entity_name}" revenue increase',
        f'"{entity_name}" market leadership',
        f'"{entity_name}" business expansion success',
        f'"{entity_name}" profitability improvement',
        f'"{entity_name}" investor confidence'
    ])
    
    # Positive partnership and collaboration queries
    positive_queries.extend([
        f'"{entity_name}" successful partnerships',
        f'"{entity_name}" strategic alliances',
        f'"{entity_name}" collaboration success',
        f'"{entity_name}" joint venture achievements',
        f'"{entity_name}" partnership impact'
    ])
    
    # Filter existing positive queries from original categories
    for category_name, queries in query_categories.items():
        if category_name in ['esg_queries', 'financial_queries']:
            # Add positive-leaning queries from these categories
            for query in queries:
                if any(positive_term in query.lower() for positive_term in 
                      ['report', 'strategy', 'framework', 'goals', 'targets', 'achievements']):
                    positive_queries.append(query)
    
    return positive_queries


def _generate_negative_specific_queries(entity_name: str, query_categories: Dict[str, List[str]]) -> List[str]:
    """Generate search queries focused on controversies, failures, and critical analysis."""
    negative_queries = []
    
    # Negative controversy and scandal queries
    negative_queries.extend([
        f'"{entity_name}" controversy',
        f'"{entity_name}" scandal',
        f'"{entity_name}" criticism',
        f'"{entity_name}" accusations',
        f'"{entity_name}" conflicts',
        f'"{entity_name}" allegations',
        f'"{entity_name}" backlash',
        f'"{entity_name}" boycott',
        f'"{entity_name}" protests against',
        f'"{entity_name}" activist criticism',
        f'"{entity_name}" negative publicity'
    ])
    
    # Negative regulatory and legal queries
    negative_queries.extend([
        f'"{entity_name}" regulatory violations',
        f'"{entity_name}" legal proceedings',
        f'"{entity_name}" conflict of interest',
        f'"{entity_name}" lawsuits against',
        f'"{entity_name}" fines penalties',
        f'"{entity_name}" compliance failures',
        f'"{entity_name}" regulatory sanctions',
        f'"{entity_name}" investigation SEC',
        f'"{entity_name}" enforcement action',
        f'"{entity_name}" legal settlement',
        f'"{entity_name}" court case'
    ])
    
    # Negative environmental and social impact queries
    negative_queries.extend([
        f'"{entity_name}" environmental damage',
        f'"{entity_name}" pollution incidents',
        f'"{entity_name}" carbon emissions high',
        f'"{entity_name}" greenwashing accusations',
        f'"{entity_name}" environmental violations',
        f'"{entity_name}" toxic waste',
        f'"{entity_name}" climate impact negative',
        f'"{entity_name}" sustainability failures',
        f'"{entity_name}" environmental fines',
        f'"{entity_name}" ecological damage'
    ])
    
    # Negative labor and social queries
    negative_queries.extend([
        f'"{entity_name}" labor violations',
        f'"{entity_name}" worker exploitation',
        f'"{entity_name}" safety incidents',
        f'"{entity_name}" discrimination lawsuit',
        f'"{entity_name}" employee complaints',
        f'"{entity_name}" workplace harassment',
        f'"{entity_name}" union disputes',
        f'"{entity_name}" layoffs controversy',
        f'"{entity_name}" working conditions poor',
        f'"{entity_name}" human rights violations'
    ])
    
    # Negative financial and business failure queries
    negative_queries.extend([
        f'"{entity_name}" financial losses',
        f'"{entity_name}" revenue decline',
        f'"{entity_name}" profit warnings',
        f'"{entity_name}" business failures',
        f'"{entity_name}" project cancelled',
        f'"{entity_name}" investment losses',
        f'"{entity_name}" market share decline',
        f'"{entity_name}" bankruptcy risk',
        f'"{entity_name}" debt problems',
        f'"{entity_name}" failed initiatives'
    ])
    
    # Negative supply chain and partnership queries
    negative_queries.extend([
        f'"{entity_name}" supply chain issues',
        f'"{entity_name}" supplier problems',
        f'"{entity_name}" partnership ended',
        f'"{entity_name}" contract disputes',
        f'"{entity_name}" vendor issues',
        f'"{entity_name}" supply chain violations',
        f'"{entity_name}" sourcing problems'
    ])
    
    # NGO and activist criticism queries
    negative_queries.extend([
        f'"{entity_name}" NGO reports critical',
        f'"{entity_name}" Greenpeace criticism',
        f'"{entity_name}" Amnesty International',
        f'"{entity_name}" watchdog reports',
        f'"{entity_name}" transparency issues',
        f'"{entity_name}" accountability problems',
        f'"{entity_name}" stakeholder criticism'
    ])
    
    # Filter existing negative queries from original categories
    for category_name, queries in query_categories.items():
        if category_name in ['media_queries']:
            # Add negative-leaning queries from media category
            for query in queries:
                if any(negative_term in query.lower() for negative_term in 
                      ['controversy', 'scandal', 'criticism', 'investigation']):
                    negative_queries.append(query)
    
    return negative_queries


def _generate_relationship_specific_queries(entity_name: str, query_categories: Dict[str, List[str]]) -> List[str]:
    """Generate search queries focused on corporate relationships, ownership, and business connections."""
    relationship_queries = []
    
    # Ownership and corporate structure queries
    relationship_queries.extend([
        f'"{entity_name}" parent company',
        f'"{entity_name}" subsidiary companies',
        f'"{entity_name}" owned by',
        f'"{entity_name}" owns companies',
        f'"{entity_name}" corporate structure',
        f'"{entity_name}" organizational chart',
        f'"{entity_name}" ownership structure',
        f'"{entity_name}" holding company',
        f'"{entity_name}" corporate hierarchy',
        f'"{entity_name}" business units',
        f'"{entity_name}" divisions subsidiaries'
    ])
    
    # Investment and funding relationships
    relationship_queries.extend([
        f'"{entity_name}" investors list',
        f'"{entity_name}" funding rounds',
        f'"{entity_name}" venture capital',
        f'"{entity_name}" private equity',
        f'"{entity_name}" investment portfolio',
        f'"{entity_name}" backed by',
        f'"{entity_name}" funded by',
        f'"{entity_name}" invests in',
        f'"{entity_name}" portfolio companies',
        f'"{entity_name}" shareholder list',
        f'"{entity_name}" major shareholders',
        f'"{entity_name}" institutional investors'
    ])
    
    # Supply chain and business partnerships
    relationship_queries.extend([
        f'"{entity_name}" 3PL partners',
        f'"{entity_name}" alliances',
        f'"{entity_name}" approved suppliers',
        f'"{entity_name}" approved vendor list',
        f'"{entity_name}" business partners',
        f'"{entity_name}" channel partners',
        f'"{entity_name}" component vendors',
        f'"{entity_name}" contract manufacturers',
        f'"{entity_name}" distribution partners',
        f'"{entity_name}" joint ventures',
        f'"{entity_name}" key suppliers',
        f'"{entity_name}" logistics partners',
        f'"{entity_name}" OEM list',
        f'"{entity_name}" preferred suppliers',
        f'"{entity_name}" procurement due diligence',
        f'"{entity_name}" procurement partners',
        f'"{entity_name}" procurement',
        f'"{entity_name}" raw material suppliers',
        f'"{entity_name}" reseller network',
        f'"{entity_name}" responsible sourcing policy',
        f'"{entity_name}" shipping providers',
        f'"{entity_name}" strategic partnerships',
        f'"{entity_name}" subcontractors',
        f'"{entity_name}" supplier audit report',
        f'"{entity_name}" supplier audit',
        f'"{entity_name}" supplier compliance checklist',
        f'"{entity_name}" supplier compliance',
        f'"{entity_name}" supplier database',
        f'"{entity_name}" supplier diversity initiatives',
        f'"{entity_name}" supplier diversity',
        f'"{entity_name}" supplier ESG performance',
        f'"{entity_name}" supplier performance metrics',
        f'"{entity_name}" supplier performance',
        f'"{entity_name}" supplier qualification criteria',
        f'"{entity_name}" supplier qualifications',
        f'"{entity_name}" supplier scorecard',
        f'"{entity_name}" supplier sustainability record',
        f'"{entity_name}" supplier traceability',
        f'"{entity_name}" supplier',
        f'"{entity_name}" suppliers list',
        f'"{entity_name}" suppliers',
        f'"{entity_name}" supplies',
        f'"{entity_name}" supply chain governance framework',
        f'"{entity_name}" supply chain map',
        f'"{entity_name}" supply chain partners',
        f'"{entity_name}" supply chain risk assessment',
        f'"{entity_name}" supply chain risk',
        f'"{entity_name}" supply chain sustainability',
        f'"{entity_name}" supply chain transparency report',
        f'"{entity_name}" supply chain transparency',
        f'"{entity_name}" supply chain',
        f'"{entity_name}" supplying',
        f'"{entity_name}" sustainable sourcing strategy',
        f'"{entity_name}" sustainable sourcing',
        f'"{entity_name}" third-party logistics',
        f'"{entity_name}" tier 1 suppliers',
        f'"{entity_name}" tier 2 suppliers',
        f'"{entity_name}" upstream suppliers',
        f'"{entity_name}" vendor compliance audit',
        f'"{entity_name}" vendor compliance',
        f'"{entity_name}" vendor list',
        f'"{entity_name}" vendor relationships',
        f'"{entity_name}" vendors',
    ])
    
    # Customer and client relationships
    relationship_queries.extend([
        f'"{entity_name}" major customers',
        f'"{entity_name}" client list',
        f'"{entity_name}" customer base',
        f'"{entity_name}" key clients',
        f'"{entity_name}" enterprise customers',
        f'"{entity_name}" serves companies',
        f'"{entity_name}" works with'
    ])
    
    # Board and executive connections
    relationship_queries.extend([
        f'"{entity_name}" board of directors',
        f'"{entity_name}" board members',
        f'"{entity_name}" executive team',
        f'"{entity_name}" leadership team',
        f'"{entity_name}" CEO',
        f'"{entity_name}" CTO',
        f'"{entity_name}" COO',
        f'"{entity_name}" CIO',
        f'"{entity_name}" CFO',
        f'"{entity_name}" CHRO',
        f'"{entity_name}" CISO',
        f'"{entity_name}" CMO',
        f'"{entity_name}" CPO',
        f'"{entity_name}" CDO',
        f'"{entity_name}" CTO',
        f'"{entity_name}" founder',
        f'"{entity_name}" trustee',
        f'"{entity_name}" managing director',
        f'"{entity_name}" managing trustee',
        f'"{entity_name}" president',
        f'"{entity_name}" chairman',
        f'"{entity_name}" chairwoman',
        f'"{entity_name}" chairperson',
        f'"{entity_name}" vice-president',
        f'"{entity_name}" advisory board',
        f'"{entity_name}" director connections',
        f'"{entity_name}" executive backgrounds',
        f'"{entity_name}" board connections'
    ])
    
    # Merger and acquisition relationships
    relationship_queries.extend([
        f'"{entity_name}" acquisitions made',
        f'"{entity_name}" companies acquired',
        f'"{entity_name}" merger history',
        f'"{entity_name}" acquired by',
        f'"{entity_name}" merger with',
        f'"{entity_name}" buyout history',
        f'"{entity_name}" acquisition strategy'
    ])
    
    # Industry and competitive relationships
    relationship_queries.extend([
        f'"{entity_name}" competitors',
        f'"{entity_name}" industry peers',
        f'"{entity_name}" competitive landscape',
        f'"{entity_name}" market rivals',
        f'"{entity_name}" industry connections',
        f'"{entity_name}" trade associations',
        f'"{entity_name}" industry groups'
    ])
    
    # Government and regulatory relationships
    relationship_queries.extend([
        f'"{entity_name}" government contracts',
        f'"{entity_name}" regulatory bodies',
        f'"{entity_name}" lobbying activities',
        f'"{entity_name}" government relations',
        f'"{entity_name}" public sector clients',
        f'"{entity_name}" regulatory partnerships'
    ])
    
    # Financial institution relationships
    relationship_queries.extend([
        f'"{entity_name}" banking relationships',
        f'"{entity_name}" lenders',
        f'"{entity_name}" credit facilities',
        f'"{entity_name}" financial partners',
        f'"{entity_name}" debt providers',
        f'"{entity_name}" bond holders'
    ])
    
    return relationship_queries
