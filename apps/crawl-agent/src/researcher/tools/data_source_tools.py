"""Enhanced Data Source Tools

Enhanced versions of existing tools with relationship discovery capabilities.
Follows ADK best practices with ToolContext integration and proper error handling.
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Tuple

from google.adk.tools import ToolContext
from loguru import logger

# Note: ToolContext not needed for simple ADK function tools

# Import existing functions to enhance them
from eko.entities.sec import get_comprehensive_company_data
from eko.entities.gleif import get_comprehensive_gleif_data
from eko.entities.companies_house import search_companies_by_name, get_company_profile, get_company_relationships, \
    get_company_ownership, CompanySearchResult, CompanyProfile, CompanyRelationships, OwnershipLink, \
    get_owned_companies, PSC, Officer
from eko.entities.wikipedia import get_company_description, search_wikipedia

# Import response utilities for ADK compliance
from researcher.tools.response_utils import create_success_response, create_error_response
from researcher.tools.entity_utils import determine_entity_type, sanitize_entity_name

# Forward declarations for type hints
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from eko.db.data.entity import EntityExtended
    from eko.models.entity_relationship import EntityRelationship


async def enhanced_sec_search(
    company_name: Optional[str],
    cik: Optional[str],
    ticker: Optional[str],
    include_subsidiaries: bool,
    filing_types: Optional[List[str]],
    tool_context: Optional[ToolContext]
) -> Dict[str, Any]:
    """Enhanced SEC EDGAR search with EntityExtended object creation.
    
    Uses the comprehensive SEC API to gather maximum company information and creates
    EntityExtended objects using the EntityResolutionStrategy with GLEIF as foundation.
    
    Args:
        company_name: Company name to search for
        cik: Central Index Key for direct lookup
        ticker: Stock ticker symbol
        include_subsidiaries: Whether to extract subsidiary information
        filing_types: Types of filings to search (10-K, 10-Q, 8-K, etc.)
    
    Returns:
        Dict containing EntityExtended object and extracted relationships
    """
    try:
        logger.info(f"Starting enhanced SEC search for company: {company_name or cik or ticker}")
        
        if filing_types is None:
            filing_types = ['10-K', '10-Q', '8-K', 'DEF 14A']
        
        # Get comprehensive company data using the new method
        comprehensive_data = await asyncio.to_thread(
            get_comprehensive_company_data,
            company_name=company_name,
            cik=cik,
            ticker=ticker,
            include_filings=True,
            include_subsidiaries=include_subsidiaries,
            include_directors=True,
            filing_types=filing_types,
            max_filings=20
        )
        
        if not comprehensive_data:
            return create_error_response(
                error_message='Company not found in SEC databases',
                error_code='COMPANY_NOT_FOUND'
            )
        
        # Transform SEC data into search format for EntityResolutionStrategy
        search_data = {
            'name': comprehensive_data.name,
            'cik': comprehensive_data.cik,
            'ticker': comprehensive_data.ticker,
            'legal_name': comprehensive_data.name,
            'description': comprehensive_data.entity_details.business_description if comprehensive_data.entity_details else None,
            'jurisdiction': comprehensive_data.entity_details.state_of_incorporation if comprehensive_data.entity_details else None,
            'sic': comprehensive_data.entity_details.sic if comprehensive_data.entity_details else None,
            'industry': comprehensive_data.mapping_info.industry if comprehensive_data.mapping_info else None,
            'sector': comprehensive_data.mapping_info.sector if comprehensive_data.mapping_info else None,
            'exchange': comprehensive_data.mapping_info.exchange if comprehensive_data.mapping_info else None,
            'currency': comprehensive_data.mapping_info.currency if comprehensive_data.mapping_info else None,
            'location': comprehensive_data.entity_details.business_address if comprehensive_data.entity_details else None,
            'category': comprehensive_data.mapping_info.category if comprehensive_data.mapping_info else None,
            'is_delisted': comprehensive_data.mapping_info.is_delisted if comprehensive_data.mapping_info else False,
            'sic_sector': comprehensive_data.mapping_info.sic_sector if comprehensive_data.mapping_info else None,
            'sic_industry': comprehensive_data.mapping_info.sic_industry if comprehensive_data.mapping_info else None,
            'sec_internal_id': comprehensive_data.mapping_info.sec_internal_id if comprehensive_data.mapping_info else None,
            'subsidiaries': [sub.model_dump() for sub in comprehensive_data.subsidiaries],
            'directors': [director.model_dump() for director in comprehensive_data.directors_board_members],
            'latest_accession_no': comprehensive_data.recent_filings[0].accession_no if comprehensive_data.recent_filings else None,
            'latest_form_type': comprehensive_data.recent_filings[0].form_type if comprehensive_data.recent_filings else None,
            'latest_filed_at': comprehensive_data.recent_filings[0].filed_at if comprehensive_data.recent_filings else None,
            'filing_links': [filing.link_to_html or filing.link_to_filing_details for filing in comprehensive_data.recent_filings],
            'xbrl_data': None  # TODO: Add XBRL data extraction if available
        }
        
        # Get database connection and create EntityExtended object using resolution strategy
        from eko.db import get_bo_conn
        from eko.entities.resolution import EntityResolutionStrategy
        
        with get_bo_conn() as conn:
            resolution_strategy = EntityResolutionStrategy(conn)
            
            # Resolve entity using SEC data
            entity, relationships = resolution_strategy.resolve_entity(
                search_data=search_data,
                source_type='sec',
                create_if_not_found=True
            )
            
            # Set tool context state for downstream tools
            if tool_context:
                tool_context.state['entity_legal_name'] = entity.legal_name
                tool_context.state['entity_name'] = entity.name
                tool_context.state['entity_id'] = entity.id
                tool_context.state['entity_eko_id'] = entity.eko_id
                tool_context.state['entity_cik'] = search_data['cik']
                tool_context.state['entity_ticker'] = search_data['ticker']
                tool_context.state['sec_info'] = {
                    'cik': search_data['cik'],
                    'ticker': search_data['ticker'],
                    'name': entity.name,
                    'data_retrieved_at': comprehensive_data.data_retrieved_at,
                    'apis_used': comprehensive_data.apis_used
                }
                tool_context.state['sec_filings'] = [
                    {
                        'form': filing.form_type,
                        'filing_date': filing.filed_at.split('T')[0],
                        'accession_number': filing.accession_no,
                        'url': filing.link_to_html or filing.link_to_filing_details,
                        'description': filing.description,
                        'items': filing.items
                    } for filing in comprehensive_data.recent_filings
                ]
                tool_context.state['sec_subsidiaries'] = search_data['subsidiaries']
                tool_context.state['sec_directors'] = search_data['directors']
                
                if comprehensive_data.entity_details:
                    tool_context.state['sec_business_address'] = comprehensive_data.entity_details.business_address
                    tool_context.state['sec_state_of_incorporation'] = comprehensive_data.entity_details.state_of_incorporation
                    tool_context.state['sec_sic'] = comprehensive_data.entity_details.sic
                    tool_context.state['sec_business_description'] = comprehensive_data.entity_details.business_description
                
                if comprehensive_data.mapping_info:
                    tool_context.state['sec_sector'] = comprehensive_data.mapping_info.sector
                    tool_context.state['sec_industry'] = comprehensive_data.mapping_info.industry
                    tool_context.state['sec_exchange'] = comprehensive_data.mapping_info.exchange
            
            # Return EntityExtended object as JSON dict along with relationships
            return create_success_response(
                data={
                    'entity': entity.model_dump(),
                    'relationships': [rel.model_dump() for rel in relationships],
                    'source_data': {
                        'filings': [
                            {
                                'form': filing.form_type,
                                'filing_date': filing.filed_at.split('T')[0],
                                'accession_number': filing.accession_no,
                                'url': filing.link_to_html or filing.link_to_filing_details,
                                'description': filing.description,
                                'items': filing.items
                            } for filing in comprehensive_data.recent_filings
                        ],
                        'subsidiaries': search_data['subsidiaries'],
                        'directors': search_data['directors'],
                        'apis_used': comprehensive_data.apis_used
                    }
                },
                message=f"Enhanced SEC search completed for {entity.name}",
                metadata={
                    'entity_id': entity.id,
                    'entity_eko_id': entity.eko_id,
                    'relationships_found': len(relationships),
                    'filings_found': len(comprehensive_data.recent_filings),
                    'subsidiaries_found': len(search_data['subsidiaries']),
                    'directors_found': len(search_data['directors']),
                    'apis_used': comprehensive_data.apis_used,
                    'extraction_method': 'enhanced_sec_search_with_entity_resolution'
                }
            )
        
    except Exception as e:
        logger.exception(f"Enhanced SEC search failed")
        return create_error_response(
            error_message=f'Enhanced SEC search failed: {str(e)}',
            error_code='SEC_SEARCH_ERROR'
        )


async def enhanced_gleif_search(
    lei: Optional[str],
    entity_name: Optional[str],
    include_hierarchy: bool,
    tool_context: Optional[ToolContext]
) -> Dict[str, Any]:
    """Enhanced GLEIF search with comprehensive organizational hierarchy traversal.
    
    Uses comprehensive GLEIF API to gather maximum entity information including:
    - Complete LEI record with legal address and business details
    - Direct parent and child relationships
    - Ultimate parent entities (full hierarchy traversal)
    - Complete organizational structure mapping
    - Jurisdiction and legal form information
    
    Args:
        lei: Legal Entity Identifier
        entity_name: Entity name for search
        include_hierarchy: Whether to traverse ownership hierarchy
        tool_context: Tool context for state management
    
    Returns:
        Dict containing comprehensive entity info and relationship hierarchy
    """
    try:
        logger.info(f"Starting enhanced GLEIF search for: {entity_name or lei}")
        
        # Get comprehensive GLEIF data using the new method
        comprehensive_data = await asyncio.to_thread(
            get_comprehensive_gleif_data,
            lei=lei,
            company_name=entity_name,
            include_hierarchy=include_hierarchy,
            max_hierarchy_depth=5
        )
        
        if not comprehensive_data:
            return create_error_response(
                error_message='Entity not found in GLEIF database',
                error_code='ENTITY_NOT_FOUND'
            )
        
        # Structure results for tool context and response
        results = {
            'entity_info': {},
            'parent_entities': [],
            'child_entities': [],
            'ultimate_parents': [],
            'full_hierarchy': {},
            'relationship_hierarchy': {},
            'legal_address': {},
            'legal_form': {},
            'registration_data': {}
        }
        
        # Populate entity information
        entity_record = comprehensive_data.entity_record
        results['entity_info'] = entity_record.model_dump()
        results['legal_address'] = entity_record.legal_address.model_dump()
        results['legal_form'] = entity_record.legal_form.model_dump()
        results['registration_data'] = entity_record.registration_data.model_dump()
        
        # Add relationship data
        results['parent_entities'] = [rel.model_dump() for rel in comprehensive_data.direct_parents]
        results['child_entities'] = [rel.model_dump() for rel in comprehensive_data.direct_children]
        results['ultimate_parents'] = [parent.model_dump() for parent in comprehensive_data.ultimate_parents]
        results['full_hierarchy'] = comprehensive_data.full_hierarchy
        
        # Build simplified relationship hierarchy structure for backwards compatibility
        results['relationship_hierarchy'] = {
            'parents': results['parent_entities'],
            'children': results['child_entities'],
            'ultimate_parents': results['ultimate_parents']
        }
        
        # Set tool context state for downstream tools
        if tool_context:
            tool_context.state['lei_info'] = results['entity_info']
            tool_context.state['entity_lei'] = entity_record.lei
            tool_context.state['entity_legal_name'] = entity_record.company_name
            tool_context.state['entity_address'] = results['legal_address']
            tool_context.state['entity_jurisdiction'] = entity_record.entity_jurisdiction
            tool_context.state['entity_status'] = entity_record.entity_status
            tool_context.state['entity_legal_form'] = results['legal_form']
            tool_context.state['entity_registration_data'] = results['registration_data']
            
            tool_context.state['lei_parents'] = results['parent_entities']
            tool_context.state['lei_children'] = results['child_entities']
            tool_context.state['lei_ultimate_parents'] = results['ultimate_parents']
            tool_context.state['lei_full_hierarchy'] = results['full_hierarchy']
            
            # Set ultimate parent (first one if available)
            if results['ultimate_parents']:
                tool_context.state['lei_ultimate_parent'] = results['ultimate_parents'][0]
        
        return create_success_response(
            data=results,
            message=f"Enhanced GLEIF search completed for {entity_record.company_name}",
            metadata={
                'hierarchy_depth': comprehensive_data.hierarchy_depth,
                'parent_entities_found': len(results['parent_entities']),
                'child_entities_found': len(results['child_entities']),
                'ultimate_parents_found': len(results['ultimate_parents']),
                'total_relationships': comprehensive_data.relationships_found,
                'data_retrieved_at': comprehensive_data.data_retrieved_at,
                'extraction_method': 'enhanced_gleif_search_comprehensive'
            }
        )
        
    except Exception as e:
        logger.exception(f"Enhanced GLEIF search failed")
        return create_error_response(
            error_message=f'Enhanced GLEIF search failed: {str(e)}',
            error_code='GLEIF_SEARCH_ERROR'
        )


async def enhanced_companies_house_search(
    company_name: Optional[str],
    company_number: Optional[str],
    include_officers: bool,
    include_filings: bool,
    include_subsidiaries: bool,
    include_persons_of_significant_control: bool,
    tool_context: ToolContext
) -> Dict[str, Any]:
    """Enhanced Companies House search with EntityExtended object creation.
    
    Uses comprehensive Companies House API to gather maximum company information and creates
    EntityExtended objects using the EntityResolutionStrategy with GLEIF as foundation.
    
    Args:
        company_name: Company name to search
        company_number: Direct company number lookup
        include_officers: Include officer and director information
        include_filings: Include recent filing information
        include_subsidiaries: Discover subsidiary relationships
        include_persons_of_significant_control: Include PSC information

    Returns:
        Dict containing EntityExtended object and extracted relationships
    """
    try:
        logger.info(f"Starting enhanced Companies House search for: {company_name or company_number}")
        
        # Step 1: Get company data
        company_data = await _get_company_data(company_name, company_number)
        if not company_data:
            return create_error_response(
                error_message='Company not found in Companies House',
                error_code='COMPANY_NOT_FOUND'
            )
        
        # Step 2: Get additional relationship data
        relationships, ownership_data, subsidiaries = await _get_relationship_data(
            company_data.company_number,
            include_officers,
            include_subsidiaries,
            include_persons_of_significant_control
        )
        
        # Step 3: Create EntityExtended object and relationships
        from eko.db import get_bo_conn
        
        with get_bo_conn() as conn:
            entity = _create_entity_from_company_data(conn, company_data)
            entity_relationships = _create_entity_relationships(
                conn, entity, relationships, ownership_data, subsidiaries, company_data,
                include_officers, include_subsidiaries, include_persons_of_significant_control
            )
            
            # Step 4: Set tool context state
            _set_tool_context_state(tool_context, entity, company_data, relationships, ownership_data, subsidiaries)
            
            # Step 5: Return success response
            return _create_companies_house_success_response(
                entity, entity_relationships, company_data, relationships, ownership_data, subsidiaries
            )
        
    except Exception as e:
        logger.exception(f"Enhanced Companies House search failed")
        return create_error_response(
            error_message=f'Enhanced Companies House search failed: {str(e)}',
            error_code='COMPANIES_HOUSE_SEARCH_ERROR'
        )


async def _get_company_data(company_name: Optional[str], company_number: Optional[str]) -> Optional[CompanyProfile]:
    """Get company data from Companies House API."""
    if company_number:
        return await asyncio.to_thread(get_company_profile, company_number)
    elif company_name:
        search_results = await asyncio.to_thread(search_companies_by_name, company_name)
        if search_results:
            first_company: CompanySearchResult = search_results[0]
            return await asyncio.to_thread(get_company_profile, first_company.company_number)
    else:
        raise ValueError('Must provide company_name or company_number')
    
    return None


async def _get_relationship_data(
    company_number: str,
    include_officers: bool,
    include_subsidiaries: bool,
    include_persons_of_significant_control: bool
) -> Tuple[Optional[CompanyRelationships], List[OwnershipLink], List[OwnershipLink]]:
    """Get relationship data from Companies House API."""
    relationships: Optional[CompanyRelationships] = None
    ownership_data: List[OwnershipLink] = []
    subsidiaries: List[OwnershipLink] = []
    
    try:
        if include_officers or include_subsidiaries or include_persons_of_significant_control:
            relationships = await asyncio.to_thread(get_company_relationships, company_number)
            
        if include_subsidiaries:
            ownership_data = await asyncio.to_thread(get_company_ownership, company_number, 3)
            subsidiaries = await asyncio.to_thread(get_owned_companies, company_number, 3)
            
    except Exception as e:
        logger.warning(f"Failed to get company relationships: {e}")
    
    return relationships, ownership_data, subsidiaries


def _create_entity_from_company_data(conn, company_data: CompanyProfile):
    """Create EntityExtended object from company data."""
    from eko.db.data.entity import EntityData, EntityExtended
    from eko.db.data.entity_companies_house import EntityCompaniesHouseData
    from eko.models.entity_companies_house import EntityCompaniesHouse
    from eko.models import name_to_eko_id
    
    # Determine entity type using utility function
    entity_type = determine_entity_type(
        sanitize_entity_name(company_data.company_name),
        source_data={
            'company_type': getattr(company_data, 'company_type', ''),
            'description': f"Company from Companies House registry"
        }
    )
    
    # Create entity
    entity = EntityExtended(
        name=company_data.company_name,
        type=entity_type,
        eko_id=name_to_eko_id(entity_type, company_data.company_name),
        legal_name=company_data.company_name,
        description=f"Company from Companies House registry",
        jurisdiction=getattr(company_data, 'jurisdiction', None),
        status='active'
    )
    
    # Create entity in database
    entity = EntityData.create(conn, entity)
    
    # Store Companies House specific data
    if entity.id:
        ch_data = EntityCompaniesHouse(
            entity_id=entity.id,
            company_number=company_data.company_number,
            company_name=company_data.company_name,
            company_status=company_data.company_status,
            company_type=getattr(company_data, 'company_type', None),
            jurisdiction=getattr(company_data, 'jurisdiction', None),
            date_of_creation=getattr(company_data, 'date_of_creation', None),
            registered_office_address_line_1=company_data.registered_office_address.get('address_line_1') if company_data.registered_office_address else None,
            registered_office_address_line_2=company_data.registered_office_address.get('address_line_2') if company_data.registered_office_address else None,
            registered_office_country=company_data.registered_office_address.get('country') if company_data.registered_office_address else None,
            registered_office_locality=company_data.registered_office_address.get('locality') if company_data.registered_office_address else None,
            registered_office_region=company_data.registered_office_address.get('region') if company_data.registered_office_address else None,
            registered_office_postal_code=company_data.registered_office_address.get('postal_code') if company_data.registered_office_address else None,
            accounting_reference_date_day=None,
            accounting_reference_date_month=None,
            last_accounts_made_up_to=None,
            last_accounts_period_start_on=None,
            last_accounts_period_end_on=None,
            last_accounts_type=None,
            next_accounts_due_on=None,
            next_accounts_overdue=False,
            confirmation_statement_last_made_up_to=None,
            confirmation_statement_next_due=None,
            confirmation_statement_overdue=False,
            has_charges=getattr(company_data, 'has_charges', False),
            has_insolvency_history=getattr(company_data, 'has_insolvency_history', False),
            can_file=getattr(company_data, 'can_file', True),
            sic_codes=company_data.sic_codes,
            links=getattr(company_data, 'links', None),
            branch_company_details=None,
            corporate_annotation=None,
            etag=getattr(company_data, 'etag', None)
        )
        
        EntityCompaniesHouseData.upsert_by_entity_id(conn, ch_data)
    
    return entity


def _create_entity_relationships(
    conn,
    entity: 'EntityExtended',
    relationships: Optional[CompanyRelationships],
    ownership_data: List[OwnershipLink],
    subsidiaries: List[OwnershipLink],
    company_data: CompanyProfile,
    include_officers: bool,
    include_subsidiaries: bool,
    include_persons_of_significant_control: bool
) -> List['EntityRelationship']:
    """Create entity relationships from Companies House data."""
    entity_relationships = []
    
    # Process officers
    if relationships and include_officers:
        entity_relationships.extend(
            _create_officer_relationships(conn, entity, relationships.officers, company_data.company_name)
        )
    
    # Process subsidiaries
    if include_subsidiaries:
        entity_relationships.extend(
            _create_subsidiary_relationships(conn, entity, subsidiaries, company_data.company_name)
        )
    
    # Process PSCs
    if relationships and include_persons_of_significant_control:
        entity_relationships.extend(
            _create_psc_relationships(conn, entity, relationships.pscs, company_data.company_name)
        )
    
    # Process parent companies
    if include_subsidiaries:
        entity_relationships.extend(
            _create_parent_relationships(conn, entity, ownership_data, company_data.company_name)
        )
    
    return entity_relationships


def _create_officer_relationships(conn, entity: 'EntityExtended', officers: List[Officer], company_name: str) -> List['EntityRelationship']:
    """Create officer relationships."""
    from eko.db.data.entity import EntityData, EntityExtended
    from eko.db.data.entity_relationship import EntityRelationshipData
    from eko.models.entity_relationship import EntityRelationship
    from eko.models import name_to_eko_id
    from eko.models.common import generate_eko_id_structured
    from datetime import datetime
    
    relationships = []
    
    for officer in officers:
        if officer.name:
            # Create person entity for officer
            officer_entity = EntityExtended(
                name=officer.name,
                type='person',
                eko_id=name_to_eko_id('person', officer.name),
                legal_name=officer.name,
                description=f"Officer of {company_name}",
                status='active'
            )
            officer_entity = EntityData.create(conn, officer_entity)
            
            # Create relationship with eko_id
            if officer_entity.id and entity.id:
                # Generate eko_id for structured data relationship
                relationship_eko_id = generate_eko_id_structured(
                    'companies_house',
                    {'name': officer.name, 'type': 'person'},
                    {'name': company_name, 'type': entity.type},
                    'officer_of'
                )
                
                relationship = EntityRelationship(
                    from_entity_id=officer_entity.id,
                    to_entity_id=entity.id,
                    relationship_category='governance',
                    relationship_type='officer_of',
                    relationship_sub_type=officer.role,
                    relationship_source='companies_house',
                    eko_id=relationship_eko_id,
                    canonical=True,
                    confidence_score=0.8,
                    discovery_method='companies_house_officer_analysis',
                    discovered_date=datetime.now().date()
                )
                relationships.append(EntityRelationshipData.create(conn, relationship))
    
    return relationships


def _create_subsidiary_relationships(conn, entity: 'EntityExtended', subsidiaries: List[OwnershipLink], company_name: str) -> List['EntityRelationship']:
    """Create subsidiary relationships."""
    from eko.db.data.entity import EntityData, EntityExtended
    from eko.db.data.entity_relationship import EntityRelationshipData
    from eko.models.entity_relationship import EntityRelationship
    from eko.models import name_to_eko_id
    from eko.models.common import generate_eko_id_structured
    from datetime import datetime
    
    relationships = []
    
    for subsidiary in subsidiaries:
        if subsidiary.child_name:
            # Determine subsidiary entity type
            subsidiary_type = determine_entity_type(
                sanitize_entity_name(subsidiary.child_name)
            )
            
            # Create subsidiary entity
            sub_entity = EntityExtended(
                name=subsidiary.child_name,
                type=subsidiary_type,
                eko_id=name_to_eko_id(subsidiary_type, subsidiary.child_name),
                legal_name=subsidiary.child_name,
                description=f"Subsidiary of {company_name}",
                jurisdiction=subsidiary.child_country,
                status='active'
            )
            sub_entity = EntityData.create(conn, sub_entity)
            
            # Create relationship with eko_id
            if entity.id and sub_entity.id:
                # Generate eko_id for structured data relationship
                relationship_eko_id = generate_eko_id_structured(
                    'companies_house',
                    {'name': company_name, 'type': entity.type},
                    {'name': subsidiary.child_name, 'type': subsidiary_type},
                    'subsidiary_of'
                )
                
                relationship = EntityRelationship(
                    from_entity_id=entity.id,
                    to_entity_id=sub_entity.id,
                    relationship_category='ownership',
                    relationship_type='subsidiary_of',
                    relationship_source='companies_house',
                    eko_id=relationship_eko_id,
                    canonical=True,
                    confidence_score=0.9,
                    discovery_method='companies_house_ownership_analysis',
                    discovered_date=datetime.now().date()
                )
                relationships.append(EntityRelationshipData.create(conn, relationship))
    
    return relationships


def _create_psc_relationships(conn, entity: 'EntityExtended', pscs: List[PSC], company_name: str) -> List['EntityRelationship']:
    """Create PSC relationships."""
    from eko.db.data.entity import EntityData, EntityExtended
    from eko.db.data.entity_relationship import EntityRelationshipData
    from eko.models.entity_relationship import EntityRelationship
    from eko.models import name_to_eko_id
    from eko.models.common import generate_eko_id_structured
    from datetime import datetime
    
    relationships = []
    
    for psc in pscs:
        if psc.name and psc.kind == 'corporate-entity-person-with-significant-control':
            # Determine PSC entity type
            psc_type = determine_entity_type(
                sanitize_entity_name(psc.name)
            )
            
            # Create corporate PSC entity
            psc_entity = EntityExtended(
                name=psc.name,
                type=psc_type,
                eko_id=name_to_eko_id(psc_type, psc.name),
                legal_name=psc.name,
                description=f"PSC of {company_name}",
                status='active'
            )
            psc_entity = EntityData.create(conn, psc_entity)
            
            # Create relationship with eko_id
            if psc_entity.id and entity.id:
                # Generate eko_id for structured data relationship
                relationship_eko_id = generate_eko_id_structured(
                    'companies_house',
                    {'name': psc.name, 'type': psc_type},
                    {'name': company_name, 'type': entity.type},
                    'significant_control_of'
                )
                
                relationship = EntityRelationship(
                    from_entity_id=psc_entity.id,
                    to_entity_id=entity.id,
                    relationship_category='ownership',
                    relationship_type='significant_control_of',
                    relationship_source='companies_house',
                    eko_id=relationship_eko_id,
                    canonical=True,
                    confidence_score=0.85,
                    discovery_method='companies_house_psc_analysis',
                    discovered_date=datetime.now().date()
                )
                relationships.append(EntityRelationshipData.create(conn, relationship))
    
    return relationships


def _create_parent_relationships(conn, entity: 'EntityExtended', ownership_data: List[OwnershipLink], company_name: str) -> List['EntityRelationship']:
    """Create parent company relationships."""
    from eko.db.data.entity import EntityData, EntityExtended
    from eko.db.data.entity_relationship import EntityRelationshipData
    from eko.models.entity_relationship import EntityRelationship
    from eko.models import name_to_eko_id
    from eko.models.common import generate_eko_id_structured
    from datetime import datetime
    
    relationships = []
    
    for parent in ownership_data:
        if parent.parent_name:
            # Determine parent entity type
            parent_type = determine_entity_type(
                sanitize_entity_name(parent.parent_name)
            )
            
            # Create parent entity
            parent_entity = EntityExtended(
                name=parent.parent_name,
                type=parent_type,
                eko_id=name_to_eko_id(parent_type, parent.parent_name),
                legal_name=parent.parent_name,
                description=f"Parent of {company_name}",
                jurisdiction=parent.parent_country,
                status='active'
            )
            parent_entity = EntityData.create(conn, parent_entity)
            
            # Create relationship with eko_id
            if parent_entity.id and entity.id:
                # Generate eko_id for structured data relationship
                relationship_eko_id = generate_eko_id_structured(
                    'companies_house',
                    {'name': parent.parent_name, 'type': parent_type},
                    {'name': company_name, 'type': entity.type},
                    'parent_of'
                )
                
                relationship = EntityRelationship(
                    from_entity_id=parent_entity.id,
                    to_entity_id=entity.id,
                    relationship_category='ownership',
                    relationship_type='parent_of',
                    relationship_source='companies_house',
                    eko_id=relationship_eko_id,
                    canonical=True,
                    confidence_score=0.9,
                    discovery_method='companies_house_ownership_analysis',
                    discovered_date=datetime.now().date()
                )
                relationships.append(EntityRelationshipData.create(conn, relationship))
    
    return relationships


def _set_tool_context_state(
    tool_context: ToolContext,
    entity: 'EntityExtended',
    company_data: CompanyProfile,
    relationships: Optional[CompanyRelationships],
    ownership_data: List[OwnershipLink],
    subsidiaries: List[OwnershipLink]
):
    """Set tool context state for downstream tools."""
    if tool_context:
        tool_context.state['entity_legal_name'] = entity.legal_name
        tool_context.state['entity_name'] = entity.name
        tool_context.state['entity_id'] = entity.id
        tool_context.state['entity_eko_id'] = entity.eko_id
        tool_context.state['entity_address'] = company_data.registered_office_address
        tool_context.state['ch_company_number'] = company_data.company_number
        tool_context.state['ch_company_profile'] = company_data.model_dump()
        tool_context.state['ch_company_status'] = company_data.company_status
        tool_context.state['ch_sic_codes'] = company_data.sic_codes
        
        if relationships:
            tool_context.state['ch_officers'] = [officer.model_dump() for officer in relationships.officers]
            tool_context.state['ch_psc'] = [psc.model_dump() for psc in relationships.pscs]
        else:
            tool_context.state['ch_officers'] = []
            tool_context.state['ch_psc'] = []
            
        tool_context.state['ch_subsidiaries'] = [sub.model_dump() for sub in subsidiaries]
        tool_context.state['ch_parents'] = [parent.model_dump() for parent in ownership_data]


def _create_companies_house_success_response(
    entity: 'EntityExtended',
    entity_relationships: List['EntityRelationship'],
    company_data: CompanyProfile,
    relationships: Optional[CompanyRelationships],
    ownership_data: List[OwnershipLink],
    subsidiaries: List[OwnershipLink]
) -> Dict[str, Any]:
    """Create success response for Companies House search."""
    return create_success_response(
        data={
            'entity': entity.model_dump(),
            'relationships': [rel.model_dump() for rel in entity_relationships],
            'source_data': {
                'company_profile': company_data.model_dump(),
                'officers': [officer.model_dump() for officer in relationships.officers] if relationships else [],
                'persons_with_significant_control': [psc.model_dump() for psc in relationships.pscs] if relationships else [],
                'subsidiaries': [sub.model_dump() for sub in subsidiaries],
                'parent_companies': [parent.model_dump() for parent in ownership_data]
            }
        },
        message=f"Enhanced Companies House search completed for {entity.name}",
        metadata={
            'entity_id': entity.id,
            'entity_eko_id': entity.eko_id,
            'relationships_found': len(entity_relationships),
            'officers_found': len(relationships.officers) if relationships else 0,
            'subsidiaries_found': len(subsidiaries),
            'pscs_found': len(relationships.pscs) if relationships else 0,
            'parent_companies_found': len(ownership_data),
            'extraction_method': 'enhanced_companies_house_search_with_daos'
        }
    )


async def enhanced_wikipedia_search(
    entity_name: str,
    include_relationships: bool = True,
    max_links: int = 50
) -> Dict[str, Any]:
    """Enhanced Wikipedia search with entity relationship extraction.
    
    Args:
        entity_name: Entity name to search for
        include_relationships: Whether to extract entity relationships
        max_links: Maximum number of links to analyze
    
    Returns:
        Dict containing entity info and extracted relationships
    """
    try:
        logger.info(f"Starting enhanced Wikipedia search for: {entity_name}")
        
        results = {
            'entity_info': {},
            'summary': '',
            'related_entities': [],
            'relationships': [],
            'categories': []
        }
        
        # Use existing Wikipedia functions
        search_results = None
        entity_description = None
        
        # Search for entity
        try:
            search_results = await asyncio.to_thread(search_wikipedia, entity_name)
            if not search_results:
                return create_error_response(
                    error_message='Entity not found on Wikipedia',
                    error_code='ENTITY_NOT_FOUND'
                )
            
            # Get detailed description
            entity_description = await asyncio.to_thread(get_company_description, entity_name)
            
        except Exception as e:
            logger.warning(f"Wikipedia search failed: {e}")
            return create_error_response(
                error_message=f'Wikipedia search failed: {str(e)}',
                error_code='WIKIPEDIA_SEARCH_ERROR'
            )
        
        # Structure the results
        results['entity_info'] = search_results
        results['summary'] = entity_description or ''
        
        # For now, mock relationship extraction - TODO: implement proper NLP extraction
        if include_relationships:
            results['relationships'] = []  # TODO: implement relationship extraction from Wikipedia text
            results['related_entities'] = []  # TODO: implement entity extraction from Wikipedia links
        
        return create_success_response(
            data=results,
            message=f"Enhanced Wikipedia search completed for {entity_name}",
            metadata={
                'entities_discovered': len(results['related_entities']) + len(results['relationships']),
                'related_entities_found': len(results['related_entities']),
                'relationships_found': len(results['relationships']),
                'extraction_method': 'enhanced_wikipedia_search'
            }
        )
        
    except Exception as e:
        logger.exception(f"Enhanced Wikipedia search failed")
        return create_error_response(
            error_message=f'Enhanced Wikipedia search failed: {str(e)}',
            error_code='WIKIPEDIA_SEARCH_ERROR'
        )
