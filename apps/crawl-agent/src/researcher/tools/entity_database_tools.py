"""Entity Database Tools - Database integration for entity information"""

import os
import sys
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from eko.db import get_bo_conn
from loguru import logger


@dataclass
class EntityInfo:
    """Entity information from database"""
    id: int
    eko_id: str
    name: str
    type: str
    status: str
    common_name: Optional[str] = None
    legal_name: Optional[str] = None
    lei: Optional[str] = None
    jurisdiction: Optional[str] = None
    description: Optional[str] = None
    ticker: Optional[str] = None
    url: Optional[str] = None


@dataclass
class EntityDomain:
    """Domain information for entity"""
    domain: str
    entity_id: int
    exact: bool
    credibility: int
    domain_role: Optional[str] = None
    domain_category: Optional[str] = None
    description: Optional[str] = None


@dataclass
class EntityRelationship:
    """Entity relationship information"""
    from_entity_id: int
    to_entity_id: int
    domain: str
    type: str
    confidence_score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


def search_entities_by_name(entity_name: str, limit: int = 10) -> List[EntityInfo]:
    """
    Search for entities by name in the database.
    
    Args:
        entity_name: Name to search for
        limit: Maximum number of results to return
        
    Returns:
        List of EntityInfo objects matching the search
        
    Raises:
        ValueError: If entity_name is empty or limit is invalid
    """
    # Input validation
    if not entity_name or not entity_name.strip():
        raise ValueError("Entity name cannot be empty")
    
    if limit <= 0 or limit > 1000:
        raise ValueError("Limit must be between 1 and 1000")
    
    logger.debug(f"Searching for entities with name pattern: '{entity_name}', limit: {limit}")
    
    try:
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                # Use fuzzy search with ILIKE for partial matches
                query = """
                    SELECT id, eko_id, name, type, status, common_name, legal_name, 
                           lei, jurisdiction, description, ticker, url
                    FROM kg_base_entities 
                    WHERE (name ILIKE %s OR common_name ILIKE %s OR legal_name ILIKE %s)
                      AND status = 'active'
                      AND canonical = true
                    ORDER BY 
                        CASE 
                            WHEN LOWER(name) = LOWER(%s) THEN 1
                            WHEN LOWER(common_name) = LOWER(%s) THEN 2
                            WHEN LOWER(legal_name) = LOWER(%s) THEN 3
                            ELSE 4
                        END,
                        name
                    LIMIT %s
                """
                search_pattern = f"%{entity_name}%"
                cur.execute(query, (search_pattern, search_pattern, search_pattern, 
                                  entity_name, entity_name, entity_name, limit))
                
                results = []
                for row in cur.fetchall():
                    results.append(EntityInfo(
                        id=row[0],
                        eko_id=row[1],
                        name=row[2],
                        type=row[3],
                        status=row[4],
                        common_name=row[5],
                        legal_name=row[6],
                        lei=row[7],
                        jurisdiction=row[8],
                        description=row[9],
                        ticker=row[10],
                        url=row[11]
                    ))
                
                return results
                
    except Exception as e:
        logger.exception(f"Error searching entities by name '{entity_name}': {e}")
        raise


def get_entity_by_lei(lei: str) -> Optional[EntityInfo]:
    """
    Get entity by Legal Entity Identifier (LEI).
    
    Args:
        lei: Legal Entity Identifier
        
    Returns:
        EntityInfo object or None if not found
        
    Raises:
        ValueError: If LEI is empty or invalid format
    """
    # Input validation
    if not lei or not lei.strip():
        raise ValueError("LEI cannot be empty")
    
    lei = lei.strip().upper()
    if len(lei) != 20:
        raise ValueError("LEI must be exactly 20 characters")
    
    logger.debug(f"Looking up entity by LEI: {lei}")
    
    try:
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                query = """
                    SELECT id, eko_id, name, type, status, common_name, legal_name, 
                           lei, jurisdiction, description, ticker, url
                    FROM kg_base_entities 
                    WHERE lei = %s AND status = 'active' AND canonical = true
                """
                cur.execute(query, (lei,))
                row = cur.fetchone()
                
                if row:
                    return EntityInfo(
                        id=row[0],
                        eko_id=row[1],
                        name=row[2],
                        type=row[3],
                        status=row[4],
                        common_name=row[5],
                        legal_name=row[6],
                        lei=row[7],
                        jurisdiction=row[8],
                        description=row[9],
                        ticker=row[10],
                        url=row[11]
                    )
                return None
                
    except Exception as e:
        logger.exception(f"Error getting entity by LEI '{lei}': {e}")
        raise


def get_entity_domains(entity_id: int) -> List[EntityDomain]:
    """
    Get domains associated with an entity.
    
    Args:
        entity_id: Entity ID to get domains for
        
    Returns:
        List of EntityDomain objects
        
    Raises:
        ValueError: If entity_id is invalid
    """
    # Input validation
    if not isinstance(entity_id, int) or entity_id <= 0:
        raise ValueError("Entity ID must be a positive integer")
    
    logger.debug(f"Getting domains for entity ID: {entity_id}")
    
    try:
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                query = """
                    SELECT domain, entity_id, exact, credibility, domain_role, 
                           domain_category, description
                    FROM kg_domains 
                    WHERE entity_id = %s
                    ORDER BY credibility DESC, domain
                """
                cur.execute(query, (entity_id,))
                
                results = []
                for row in cur.fetchall():
                    results.append(EntityDomain(
                        domain=row[0],
                        entity_id=row[1],
                        exact=row[2],
                        credibility=row[3],
                        domain_role=row[4],
                        domain_category=row[5],
                        description=row[6]
                    ))
                
                return results
                
    except Exception as e:
        logger.exception(f"Error getting domains for entity {entity_id}: {e}")
        raise


def get_entity_relationships(entity_id: int, limit: int = 20) -> List[EntityRelationship]:
    """
    Get relationships for an entity.
    
    Args:
        entity_id: Entity ID to get relationships for
        limit: Maximum number of relationships to return
        
    Returns:
        List of EntityRelationship objects
        
    Raises:
        ValueError: If entity_id or limit is invalid
    """
    # Input validation
    if not isinstance(entity_id, int) or entity_id <= 0:
        raise ValueError("Entity ID must be a positive integer")
    
    if limit <= 0 or limit > 1000:
        raise ValueError("Limit must be between 1 and 1000")
    
    logger.debug(f"Getting relationships for entity {entity_id}, limit: {limit}")
    
    try:
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                query = """
                    SELECT from_entity_id, to_entity_id, domain, 
                           type, confidence_score, metadata
                    FROM kg_entity_relations_map 
                    WHERE (from_entity_id = %s OR to_entity_id = %s)
                      AND canonical = true
                    ORDER BY confidence_score DESC NULLS LAST
                    LIMIT %s
                """
                cur.execute(query, (entity_id, entity_id, limit))
                
                results = []
                for row in cur.fetchall():
                    results.append(EntityRelationship(
                        from_entity_id=row[0],
                        to_entity_id=row[1],
                        domain=row[2],
                        type=row[3],
                        confidence_score=row[4],
                        metadata=row[5]
                    ))
                
                return results
                
    except Exception as e:
        logger.exception(f"Error getting relationships for entity {entity_id}: {e}")
        raise


def get_ownership_and_funding_relationships(entity_id: int, limit: int = 20) -> List[Dict[str, Any]]:
    """
    Get ownership and funding relationships for an entity.
    
    Uses dynamic filtering based on canonical predicate system to identify
    ownership, investment, funding, and financial control relationships.
    
    Args:
        entity_id: Entity ID to get relationships for
        limit: Maximum number of relationships to return
        
    Returns:
        List of relationship dictionaries with entity names
        
    Raises:
        ValueError: If entity_id or limit is invalid
    """
    # Input validation
    if not isinstance(entity_id, int) or entity_id <= 0:
        raise ValueError("Entity ID must be a positive integer")
    
    if limit <= 0 or limit > 1000:
        raise ValueError("Limit must be between 1 and 1000")
    
    logger.debug(f"Getting ownership/funding relationships for entity {entity_id}, limit: {limit}")
    
    try:
        # Import here to avoid circular imports
        sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../backoffice/src'))
        from eko.models.canonical_predicate import CanonicalPredicate
        
        # Get ownership and funding predicates dynamically
        ownership_predicates = CanonicalPredicate.get_ownership_and_funding_predicates()
        predicate_values = [pred.value for pred in ownership_predicates]
        
        # Create IN clause with proper parameterization
        placeholders = ','.join(['%s'] * len(predicate_values))
        
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                query = f"""
                    SELECT 
                        r.from_entity_id, 
                        r.to_entity_id, 
                        r.canonical_predicate, 
                        r.type,
                        r.predicate,
                        r.confidence_score,
                        r.metadata,
                        from_entity.name as from_entity_name,
                        from_entity.eko_id as from_entity_eko_id,
                        to_entity.name as to_entity_name,
                        to_entity.eko_id as to_entity_eko_id
                    FROM kg_entity_relations_map r
                    JOIN kg_base_entities from_entity ON r.from_entity_id = from_entity.id
                    JOIN kg_base_entities to_entity ON r.to_entity_id = to_entity.id
                    WHERE (r.from_entity_id = %s OR r.to_entity_id = %s)
                      AND r.canonical = true
                      AND r.canonical_predicate IN ({placeholders})
                    ORDER BY r.confidence_score DESC NULLS LAST
                    LIMIT %s
                """
                cur.execute(query, (entity_id, entity_id) + tuple(predicate_values) + (limit,))
                
                results = []
                for row in cur.fetchall():
                    is_outgoing = row[0] == entity_id
                    results.append({
                        'from_entity_id': row[0],
                        'to_entity_id': row[1],
                        'canonical_predicate': row[2],
                        'type': row[3],
                        'predicate': row[4],
                        'confidence_score': row[5],
                        'metadata': row[6],
                        'from_entity_name': row[7],
                        'from_entity_eko_id': row[8],
                        'to_entity_name': row[9],
                        'to_entity_eko_id': row[10],
                        'direction': 'outgoing' if is_outgoing else 'incoming',
                        'other_entity_name': row[9] if is_outgoing else row[7],
                        'other_entity_eko_id': row[10] if is_outgoing else row[8]
                    })
                
                return results
                
    except Exception as e:
        logger.exception(f"Error getting ownership/funding relationships for entity {entity_id}: {e}")
        raise


def get_entity_statements_summary(entity_id: int, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get summary of statements mentioning an entity.
    
    Args:
        entity_id: Entity ID to get statements for
        limit: Maximum number of statements to return
        
    Returns:
        List of statement summaries
        
    Raises:
        ValueError: If entity_id or limit is invalid
    """
    # Input validation
    if not isinstance(entity_id, int) or entity_id <= 0:
        raise ValueError("Entity ID must be a positive integer")
    
    if limit <= 0 or limit > 1000:
        raise ValueError("Limit must be between 1 and 1000")
    
    logger.debug(f"Getting statement summary for entity {entity_id}, limit: {limit}")
    
    try:
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                query = """
                    SELECT s.statement_text, s.statement_category, s.impact, 
                           s.start_year, s.end_year, d.name as doc_name, d.url as doc_url
                    FROM kg_statements s
                    JOIN kg_documents d ON s.doc_id = d.id
                    WHERE s.subject_entity_id = %s OR s.object_entity_id = %s OR s.company_id = %s
                    ORDER BY s.start_year DESC NULLS LAST, s.created_at DESC
                    LIMIT %s
                """
                cur.execute(query, (entity_id, entity_id, entity_id, limit))
                
                results = []
                for row in cur.fetchall():
                    results.append({
                        'statement_text': row[0],
                        'statement_category': row[1],
                        'impact': row[2],
                        'start_year': row[3],
                        'end_year': row[4],
                        'doc_name': row[5],
                        'doc_url': row[6]
                    })
                
                return results
                
    except Exception as e:
        logger.exception(f"Error getting entity statements for entity {entity_id}: {e}")
        raise


def enhanced_entity_lookup(entity_name: str) -> str:
    """
    Enhanced entity lookup tool for company analysis.
    
    This tool searches the internal knowledge graph database for information
    about companies and entities, including identifiers, relationships, domains,
    and recent statements.
    
    Args:
        entity_name: Name of the entity/company to look up
        
    Returns:
        Formatted string with entity information from the database
    """
    # Search for entities by name
    entities = search_entities_by_name(entity_name, limit=5)
    
    if not entities:
        return f"No entities found in database for '{entity_name}'"
    
    result_parts = [f"Database lookup results for '{entity_name}':\n"]
    
    for i, entity in enumerate(entities, 1):
        result_parts.append(f"\n{i}. {entity.name}")
        result_parts.append(f"   EKO ID: {entity.eko_id}")
        result_parts.append(f"   Type: {entity.type}")
        
        if entity.legal_name and entity.legal_name != entity.name:
            result_parts.append(f"   Legal Name: {entity.legal_name}")
        
        if entity.common_name and entity.common_name != entity.name:
            result_parts.append(f"   Common Name: {entity.common_name}")
        
        if entity.lei:
            result_parts.append(f"   LEI: {entity.lei}")
        
        if entity.jurisdiction:
            result_parts.append(f"   Jurisdiction: {entity.jurisdiction}")
        
        if entity.ticker:
            result_parts.append(f"   Ticker: {entity.ticker}")
        
        if entity.url:
            result_parts.append(f"   URL: {entity.url}")
        
        if entity.description:
            result_parts.append(f"   Description: {entity.description[:200]}...")
        
        # Get detailed info for first 10 results
        if i <= 10:
            domains = get_entity_domains(entity.id)
            if domains:
                result_parts.append(f"   Domains ({len(domains)}):")
                for domain in domains[:3]:  # Show top 3 domains
                    result_parts.append(f"     - {domain.domain} (credibility: {domain.credibility})")
            
            # Get ownership/funding relationships
            ownership_relationships = get_ownership_and_funding_relationships(entity.id, limit=5)
            if ownership_relationships:
                result_parts.append(f"   Ownership/Funding Relationships ({len(ownership_relationships)}):")
                for rel in ownership_relationships:
                    direction = "→" if rel['direction'] == 'outgoing' else "←"
                    result_parts.append(f"     {direction} {rel['canonical_predicate']} {rel['other_entity_name']} ({rel['other_entity_eko_id']})")
            
            # Get recent statements for first 3 results only
            if i <= 3:
                statements = get_entity_statements_summary(entity.id, limit=2)
                if statements:
                    result_parts.append(f"   Recent Statements ({len(statements)}):")
                    for stmt in statements:
                        year_info = f" ({stmt['start_year']})" if stmt['start_year'] else ""
                        result_parts.append(f"     - {stmt['statement_text'][:100]}...{year_info}")
    
    return "\n".join(result_parts)
