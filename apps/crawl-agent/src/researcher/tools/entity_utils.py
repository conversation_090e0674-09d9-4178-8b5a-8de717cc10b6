"""Entity utility functions for data source tools.

Shared utility functions for entity type determination and validation.
"""

from typing import Optional
from loguru import logger


def determine_entity_type(entity_name: str, source_data: Optional[dict] = None) -> str:
    """Determine entity type based on name and source data.
    
    Args:
        entity_name: The name of the entity
        source_data: Optional source-specific data for enhanced determination
    
    Returns:
        Either "organisation" or "company" based on analysis
    """
    if not entity_name:
        logger.warning("Empty entity name provided to determine_entity_type")
        return "company"
    
    # Check for non-profit indicators in name
    non_profit_indicators = [
        'foundation', 'charity', 'trust', 'association', 'society', 'institute',
        'council', 'board', 'committee', 'union', 'federation', 'alliance',
        'consortium', 'cooperative', 'co-op', 'mutual', 'fund', 'endowment'
    ]
    
    entity_name_lower = entity_name.lower()
    
    # Check entity name for non-profit indicators
    if any(indicator in entity_name_lower for indicator in non_profit_indicators):
        return "organisation"
    
    # Check source-specific data if provided
    if source_data:
        # Companies House specific checks
        company_type = source_data.get('company_type', '').lower()
        if 'charitable' in company_type or 'community-interest' in company_type:
            return "organisation"
        
        # SEC specific checks
        sic_code = source_data.get('sic', '')
        if sic_code and sic_code.startswith('8'):  # Services sector often includes non-profits
            industry = source_data.get('industry', '').lower()
            if any(indicator in industry for indicator in ['education', 'healthcare', 'social', 'civic']):
                return "organisation"
        
        # Description analysis
        description = source_data.get('description', '').lower()
        if any(indicator in description for indicator in non_profit_indicators):
            return "organisation"
    
    # Default to company for commercial entities
    return "company"


def sanitize_entity_name(entity_name: str) -> str:
    """Sanitize entity name for database operations.
    
    Args:
        entity_name: Raw entity name
    
    Returns:
        Sanitized entity name safe for database operations
    """
    if not entity_name:
        return ""
    
    # Strip whitespace
    sanitized = entity_name.strip()
    
    # Remove potentially dangerous characters
    dangerous_chars = [';', '--', '/*', '*/', 'DROP', 'DELETE', 'UPDATE', 'INSERT']
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')
    
    # Limit length to reasonable size
    if len(sanitized) > 500:
        sanitized = sanitized[:500]
        logger.warning(f"Entity name truncated to 500 characters: {sanitized[:50]}...")
    
    return sanitized