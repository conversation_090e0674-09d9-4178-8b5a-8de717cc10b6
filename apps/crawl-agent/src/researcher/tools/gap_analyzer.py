"""Systematic Gap Analysis System

Comprehensive gap analysis system that identifies missing information, relationships,
and entities in the knowledge graph to guide targeted data collection and research.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from loguru import logger
from pydantic import BaseModel, Field
from collections import defaultdict

from eko.models.vector.demise.demise_model import DEMISEModel
from eko.llm import LLMModel
from eko.llm.main import call_llms_typed, LLMOptions
from eko.db import get_bo_conn
from eko.db.data.entity import EntityExtended, EntityData
from eko.db.data.entity_relationship import EntityRelationship, EntityRelationshipData


class GapType(BaseModel):
    """Classification of different types of gaps."""
    
    gap_id: str
    gap_category: str  # entity, relationship, temporal, coverage, quality
    gap_type: str
    description: str
    
    # Priority and impact
    priority: int = Field(ge=1, le=10)  # 1 = highest priority
    impact_score: float = Field(ge=0, le=1)  # 0-1 scale
    
    # Gap specifics
    missing_element: str
    context: str
    suggested_sources: List[str] = Field(default_factory=list)
    
    # Discovery tracking
    discovered_date: datetime = Field(default_factory=datetime.utcnow)
    discovery_method: str = "gap_analysis"


class EntityGap(GapType):
    """Specific gap related to missing entity information."""
    
    entity_id: int
    entity_name: str
    missing_attributes: List[str] = Field(default_factory=list)
    incomplete_relationships: List[str] = Field(default_factory=list)
    
    # Context entities that reference this missing entity
    referencing_entities: List[int] = Field(default_factory=list)
    expected_relationship_types: List[str] = Field(default_factory=list)


class RelationshipGap(GapType):
    """Specific gap related to missing relationships."""
    
    subject_entity_id: int
    object_entity_id: int
    expected_relationship_type: str
    
    # Evidence that suggests this relationship should exist
    supporting_evidence: List[str] = Field(default_factory=list)
    confidence: float = Field(ge=0, le=1)
    
    # Temporal context
    expected_timeframe: Optional[str] = None
    last_verified: Optional[datetime] = None


class TemporalGap(GapType):
    """Specific gap related to missing temporal information."""
    
    entity_id: int
    time_period_start: Optional[str] = None
    time_period_end: Optional[str] = None
    
    # What's missing in this time period
    missing_data_types: List[str] = Field(default_factory=list)
    last_known_data_point: Optional[str] = None
    next_known_data_point: Optional[str] = None


class CoverageGap(GapType):
    """Specific gap related to incomplete coverage of entity aspects."""
    
    entity_id: int
    coverage_category: str  # financial, operational, governance, environmental, social
    completeness_percentage: float = Field(ge=0, le=1)
    
    # What specific aspects are missing
    missing_aspects: List[str] = Field(default_factory=list)
    benchmark_entities: List[int] = Field(default_factory=list)  # Similar entities with better coverage


class QualityGap(GapType):
    """Specific gap related to data quality issues."""
    
    affected_entities: List[int] = Field(default_factory=list)
    quality_issue_type: str  # outdated, conflicting, unverified, incomplete
    
    # Quality metrics
    confidence_level: float = Field(ge=0, le=1)
    last_updated: Optional[datetime] = None
    verification_sources: List[str] = Field(default_factory=list)


class GapAnalysisResult(BaseModel):
    """Comprehensive gap analysis results."""
    
    analysis_id: str
    target_entity_id: int
    target_entity_name: str
    analysis_date: datetime = Field(default_factory=datetime.utcnow)
    
    # Gap classifications
    entity_gaps: List[EntityGap] = Field(default_factory=list)
    relationship_gaps: List[RelationshipGap] = Field(default_factory=list)
    temporal_gaps: List[TemporalGap] = Field(default_factory=list)
    coverage_gaps: List[CoverageGap] = Field(default_factory=list)
    quality_gaps: List[QualityGap] = Field(default_factory=list)
    
    # Summary statistics
    total_gaps: int = 0
    high_priority_gaps: int = 0
    critical_gaps: int = 0
    
    # Recommendations
    immediate_actions: List[str] = Field(default_factory=list)
    research_priorities: List[str] = Field(default_factory=list)
    data_collection_targets: List[str] = Field(default_factory=list)


class SystematicGapAnalyzer:
    """Comprehensive gap analysis system for knowledge graph completeness."""
    
    def __init__(self):
        self.known_entities: Dict[int, EntityExtended] = {}
        self.known_relationships: List[EntityRelationship] = []
        self.gap_history: List[GapAnalysisResult] = []
        
        # Analysis configuration
        self.relationship_expectations = self._initialize_relationship_expectations()
        self.coverage_benchmarks = self._initialize_coverage_benchmarks()
        self.quality_standards = self._initialize_quality_standards()
        
        logger.info("Systematic Gap Analyzer initialized")
    
    def _initialize_relationship_expectations(self) -> Dict[str, List[str]]:
        """Initialize expected relationship patterns for different entity types."""
        return {
            "company": [
                "OWNS", "SUBSIDIARY_OF", "PARTNER_OF", "COMPETITOR_OF",
                "EMPLOYS", "SUPPLIES", "CLIENT_OF", "FUNDED_BY",
                "PUBLISHES", "DID", "PROMISED", "CLAIMED"
            ],
            "person": [
                "EMPLOYS", "EMPLOYED_BY", "OWNS", "INVESTED_IN",
                "DID", "PROMISED", "CLAIMED", "ANNOUNCED"
            ],
            "organisation": [
                "PARTNER_OF", "FUNDS", "PUBLISHES", "REPORTS_ON",
                "DID", "PROMISED", "CLAIMED"
            ]
        }
    
    def _initialize_coverage_benchmarks(self) -> Dict[str, Dict[str, float]]:
        """Initialize coverage benchmarks for different entity categories."""
        return {
            "PUBLIC_COMPANY": {
                "financial": 0.9,
                "governance": 0.8,
                "environmental": 0.7,
                "social": 0.6,
                "operational": 0.8
            },
            "PRIVATE_COMPANY": {
                "financial": 0.6,
                "governance": 0.5,
                "environmental": 0.4,
                "social": 0.4,
                "operational": 0.6
            },
            "NONPROFIT": {
                "financial": 0.7,
                "governance": 0.8,
                "environmental": 0.5,
                "social": 0.9,
                "operational": 0.7
            }
        }
    
    def _initialize_quality_standards(self) -> Dict[str, Any]:
        """Initialize quality standards for data assessment."""
        return {
            "max_age_days": {
                "financial": 365,
                "governance": 730,
                "environmental": 365,
                "social": 365,
                "operational": 180
            },
            "min_confidence": 0.7,
            "min_sources": 2,
            "required_verification": True
        }
    
    async def analyze_entity_gaps(
        self,
        target_entity: EntityExtended,
        context_entities: List[EntityExtended],
        existing_relationships: List[EntityRelationship]
    ) -> List[EntityGap]:
        """Analyze gaps related to entity information completeness."""
        gaps = []
        
        # Check for missing basic entity attributes
        missing_attributes = []
        if not target_entity.description or len(target_entity.description.strip()) < 50:
            missing_attributes.append("detailed_description")
        
        if not target_entity.industry:
            missing_attributes.append("industry_classification")
        
        if not target_entity.jurisdiction:
            missing_attributes.append("jurisdiction")
        
        if not target_entity.legal_name:
            missing_attributes.append("legal_name")
        
        if missing_attributes:
            gaps.append(EntityGap(
                gap_id=f"entity_attrs_{target_entity.id}",
                gap_category="entity",
                gap_type="missing_attributes",
                description=f"Missing basic attributes for {target_entity.name}",
                priority=3,
                impact_score=0.6,
                missing_element=", ".join(missing_attributes),
                context=f"Entity {target_entity.name} lacks essential attribute information",
                entity_id=target_entity.id,
                entity_name=target_entity.name,
                missing_attributes=missing_attributes,
                suggested_sources=["company_website", "sec_filings", "wikipedia", "companies_house"]
            ))
        
        # Check for expected but missing relationships
        entity_type = target_entity.type or "company"
        expected_relationships = self.relationship_expectations.get(entity_type, [])
        
        existing_rel_types = {rel.relationship_type for rel in existing_relationships}
        missing_rel_types = set(expected_relationships) - existing_rel_types
        
        if missing_rel_types:
            # Analyze which missing relationships are high priority
            high_priority_missing = []
            for rel_type in missing_rel_types:
                if rel_type in ["OWNS", "SUBSIDIARY_OF", "EMPLOYS", "PARTNER_OF"]:
                    high_priority_missing.append(rel_type)
            
            if high_priority_missing:
                gaps.append(EntityGap(
                    gap_id=f"entity_rels_{target_entity.id}",
                    gap_category="entity",
                    gap_type="missing_relationships",
                    description=f"Missing expected relationships for {target_entity.name}",
                    priority=2,
                    impact_score=0.7,
                    missing_element=", ".join(high_priority_missing),
                    context=f"Entity {target_entity.name} missing critical relationship types",
                    entity_id=target_entity.id,
                    entity_name=target_entity.name,
                    incomplete_relationships=list(missing_rel_types),
                    expected_relationship_types=high_priority_missing,
                    suggested_sources=["annual_reports", "investor_relations", "business_databases"]
                ))
        
        # Check for entities mentioned in context that should be connected
        potential_connections = []
        for context_entity in context_entities:
            # Use simple name similarity to suggest potential connections
            if context_entity.description and target_entity.name.lower() in context_entity.description.lower():
                potential_connections.append(context_entity.id)
        
        if potential_connections:
            gaps.append(EntityGap(
                gap_id=f"entity_connections_{target_entity.id}",
                gap_category="entity",
                gap_type="potential_connections",
                description=f"Potential unrecorded connections for {target_entity.name}",
                priority=4,
                impact_score=0.5,
                missing_element="relationship_connections",
                context=f"Found potential connections that need verification",
                entity_id=target_entity.id,
                entity_name=target_entity.name,
                referencing_entities=potential_connections,
                suggested_sources=["cross_reference_analysis", "document_mining"]
            ))
        
        return gaps
    
    async def analyze_relationship_gaps(
        self,
        target_entity: EntityExtended,
        context_entities: List[EntityExtended],
        existing_relationships: List[EntityRelationship],
        text_content: str = ""
    ) -> List[RelationshipGap]:
        """Analyze gaps in relationship coverage using pattern recognition."""
        gaps = []
        
        # Build relationship matrix
        relationship_matrix = defaultdict(set)
        for rel in existing_relationships:
            relationship_matrix[rel.from_entity_id].add(rel.to_entity_id)
            relationship_matrix[rel.to_entity_id].add(rel.from_entity_id)  # Bidirectional
        
        # Analyze missing symmetric relationships
        for rel in existing_relationships:
            if rel.relationship_type in ["PARTNER_OF", "COMPETITOR_OF", "AFFILIATED_WITH"]:
                # These should be symmetric
                reverse_exists = any(
                    r.from_entity_id == rel.to_entity_id and 
                    r.to_entity_id == rel.from_entity_id and
                    r.relationship_type == rel.relationship_type
                    for r in existing_relationships
                )
                
                if not reverse_exists:
                    gaps.append(RelationshipGap(
                        gap_id=f"rel_symmetric_{rel.id}",
                        gap_category="relationship",
                        gap_type="missing_symmetric",
                        description=f"Missing symmetric relationship for {rel.relationship_type}",
                        priority=3,
                        impact_score=0.6,
                        missing_element=f"symmetric_{rel.relationship_type}",
                        context=f"Relationship {rel.relationship_type} should be bidirectional",
                        subject_entity_id=rel.to_entity_id,
                        object_entity_id=rel.from_entity_id,
                        expected_relationship_type=rel.relationship_type,
                        confidence=0.8,
                        supporting_evidence=[f"Existing {rel.relationship_type} relationship"],
                        suggested_sources=["relationship_verification", "cross_reference"]
                    ))
        
        # Analyze transitive relationship opportunities
        ownership_chains = []
        for rel in existing_relationships:
            if rel.relationship_type == "OWNS":
                # Look for A owns B, B owns C -> A controls C patterns
                for other_rel in existing_relationships:
                    if (other_rel.from_entity_id == rel.to_entity_id and 
                        other_rel.relationship_type == "OWNS"):
                        
                        # Check if A -> C control relationship exists
                        control_exists = any(
                            r.from_entity_id == rel.from_entity_id and
                            r.to_entity_id == other_rel.to_entity_id and
                            r.relationship_type in ["CONTROLS", "INDIRECTLY_OWNS"]
                            for r in existing_relationships
                        )
                        
                        if not control_exists:
                            ownership_chains.append((rel.from_entity_id, other_rel.to_entity_id))
        
        for source_id, target_id in ownership_chains:
            gaps.append(RelationshipGap(
                gap_id=f"rel_transitive_{source_id}_{target_id}",
                gap_category="relationship",
                gap_type="missing_transitive",
                description="Missing transitive ownership/control relationship",
                priority=4,
                impact_score=0.5,
                missing_element="transitive_control",
                context="Ownership chain suggests indirect control relationship",
                subject_entity_id=source_id,
                object_entity_id=target_id,
                expected_relationship_type="CONTROLS",
                confidence=0.7,
                supporting_evidence=["Transitive ownership chain"],
                suggested_sources=["ownership_analysis", "corporate_structure_docs"]
            ))
        
        # Use LLM to identify potential relationships from text content
        if text_content and len(text_content.strip()) > 100:
            try:
                potential_relationships = await self._identify_text_relationship_gaps(
                    target_entity, context_entities, text_content, existing_relationships
                )
                gaps.extend(potential_relationships)
            except Exception as e:
                logger.warning(f"Failed to analyze text relationship gaps: {e}")
        
        return gaps
    
    async def _identify_text_relationship_gaps(
        self,
        target_entity: EntityExtended,
        context_entities: List[EntityExtended],
        text_content: str,
        existing_relationships: List[EntityRelationship]
    ) -> List[RelationshipGap]:
        """Use LLM to identify potential relationships mentioned in text but not captured."""
        
        context_entity_names = [e.name for e in context_entities[:5]]  # Limit context
        existing_rel_summary = [
            f"{rel.relationship_type}: {rel.from_entity_id} -> {rel.to_entity_id}"
            for rel in existing_relationships[:10]  # Limit context
        ]
        
        prompt = f"""Analyze the following text to identify potential relationships involving {target_entity.name} that may not be explicitly captured.

TARGET ENTITY: {target_entity.name}
CONTEXT ENTITIES: {', '.join(context_entity_names)}

EXISTING RELATIONSHIPS: {'; '.join(existing_rel_summary)}

TEXT CONTENT: {text_content[:2000]}

Identify potential relationships that are suggested or implied in the text but not listed in existing relationships. Focus on:
1. Business partnerships or collaborations
2. Ownership or investment relationships  
3. Supply chain or vendor relationships
4. Competitive relationships
5. Financial relationships
6. Actions, promises, or claims made

For each potential relationship, provide:
- Subject entity name
- Relationship type (use standard types like OWNS, PARTNER_OF, SUPPLIES, etc.)
- Object entity name
- Evidence text snippet
- Confidence level (0.1-1.0)

Format as JSON list of objects with fields: subject, relationship_type, object, evidence, confidence"""

        try:
            messages = [
                {"role": "system", "content": "You are an expert relationship extraction analyst. Provide concise, accurate analysis in valid JSON format."},
                {"role": "user", "content": prompt}
            ]
            
            response = await call_llms_typed(
                [LLMModel.NORMAL_CHEAP],
                messages,
                4000,
                response_model=None,  # Raw response
                options=LLMOptions(temperature=0.3)
            )
            
            # Parse the response as JSON
            potential_rels = json.loads(response)
            
            gaps = []
            for rel_data in potential_rels[:5]:  # Limit to top 5
                if rel_data.get('confidence', 0) > 0.5:  # Filter by confidence
                    # Try to find the object entity in context
                    object_entity_id = None
                    for entity in context_entities:
                        if entity.name.lower() in rel_data.get('object', '').lower():
                            object_entity_id = entity.id
                            break
                    
                    if object_entity_id:
                        gaps.append(RelationshipGap(
                            gap_id=f"text_rel_{target_entity.id}_{len(gaps)}",
                            gap_category="relationship",
                            gap_type="text_implied",
                            description=f"Text-implied relationship: {rel_data.get('relationship_type')}",
                            priority=5,
                            impact_score=rel_data.get('confidence', 0.5),
                            missing_element=f"text_relationship_{rel_data.get('relationship_type')}",
                            context="Relationship suggested by text analysis",
                            subject_entity_id=target_entity.id,
                            object_entity_id=object_entity_id,
                            expected_relationship_type=rel_data.get('relationship_type', 'UNKNOWN'),
                            confidence=rel_data.get('confidence', 0.5),
                            supporting_evidence=[rel_data.get('evidence', '')],
                            suggested_sources=["document_verification", "relationship_confirmation"]
                        ))
            
            return gaps
            
        except Exception as e:
            logger.warning(f"Failed to parse LLM relationship analysis: {e}")
            return []
    
    async def analyze_temporal_gaps(
        self,
        target_entity: EntityExtended,
        existing_relationships: List[EntityRelationship],
        time_range_start: datetime,
        time_range_end: datetime
    ) -> List[TemporalGap]:
        """Analyze gaps in temporal coverage of entity data."""
        gaps = []
        
        # Create timeline of known data points
        data_timeline = []
        for rel in existing_relationships:
            if rel.discovered_date:
                data_timeline.append(rel.discovered_date)
        
        data_timeline.sort()
        
        if not data_timeline:
            # No temporal data available
            gaps.append(TemporalGap(
                gap_id=f"temporal_no_data_{target_entity.id}",
                gap_category="temporal",
                gap_type="no_temporal_data",
                description=f"No temporal data available for {target_entity.name}",
                priority=2,
                impact_score=0.8,
                missing_element="temporal_coverage",
                context="Entity has no dated information",
                entity_id=target_entity.id,
                time_period_start=time_range_start.isoformat(),
                time_period_end=time_range_end.isoformat(),
                missing_data_types=["all_data_types"],
                suggested_sources=["historical_documents", "annual_reports", "news_archives"]
            ))
            return gaps
        
        # Identify large gaps in timeline
        min_gap_days = 90  # 3 months
        for i in range(len(data_timeline) - 1):
            current_date = data_timeline[i]
            next_date = data_timeline[i + 1]
            gap_days = (next_date - current_date).days
            
            if gap_days > min_gap_days:
                gaps.append(TemporalGap(
                    gap_id=f"temporal_gap_{target_entity.id}_{i}",
                    gap_category="temporal",
                    gap_type="timeline_gap",
                    description=f"Data gap of {gap_days} days for {target_entity.name}",
                    priority=4,
                    impact_score=min(gap_days / 365.0, 1.0),  # Higher impact for longer gaps
                    missing_element="temporal_continuity",
                    context=f"Gap from {current_date.date()} to {next_date.date()}",
                    entity_id=target_entity.id,
                    time_period_start=current_date.isoformat(),
                    time_period_end=next_date.isoformat(),
                    missing_data_types=["relationships", "actions", "statements"],
                    last_known_data_point=current_date.isoformat(),
                    next_known_data_point=next_date.isoformat(),
                    suggested_sources=["news_archives", "regulatory_filings", "press_releases"]
                ))
        
        # Check for recent data currency
        if data_timeline:
            most_recent = data_timeline[-1]
            days_since_update = (datetime.utcnow() - most_recent).days
            
            if days_since_update > 30:  # More than 30 days old
                gaps.append(TemporalGap(
                    gap_id=f"temporal_stale_{target_entity.id}",
                    gap_category="temporal",
                    gap_type="stale_data",
                    description=f"Data for {target_entity.name} is {days_since_update} days old",
                    priority=3,
                    impact_score=min(days_since_update / 365.0, 1.0),
                    missing_element="current_data",
                    context=f"Most recent data from {most_recent.date()}",
                    entity_id=target_entity.id,
                    time_period_start=most_recent.isoformat(),
                    time_period_end=datetime.utcnow().isoformat(),
                    missing_data_types=["current_status", "recent_activities"],
                    last_known_data_point=most_recent.isoformat(),
                    suggested_sources=["recent_news", "company_updates", "social_media"]
                ))
        
        return gaps
    
    async def analyze_coverage_gaps(
        self,
        target_entity: EntityExtended,
        existing_relationships: List[EntityRelationship]
    ) -> List[CoverageGap]:
        """Analyze gaps in coverage completeness across different categories."""
        gaps = []
        
        # Determine entity category for benchmarking
        entity_category = "PRIVATE_COMPANY"  # Default
        if target_entity.type == "company":
            # Simple heuristic: if we have many relationships, likely public
            if len(existing_relationships) > 20:
                entity_category = "PUBLIC_COMPANY"
        elif target_entity.type == "organisation":
            entity_category = "NONPROFIT"
        
        benchmarks = self.coverage_benchmarks.get(entity_category, {})
        
        # Categorize existing relationships
        relationship_categories = {
            "financial": ["FUNDS", "FUNDED_BY", "INVESTED_IN", "LENDS_TO", "BORROWS_FROM"],
            "governance": ["OWNS", "CONTROLLED_BY", "EMPLOYS", "PARENT_OF", "SUBSIDIARY_OF"],
            "environmental": ["ENVIRONMENTAL_IMPACT", "SUSTAINABILITY_INITIATIVE"],
            "social": ["SOCIAL_IMPACT", "COMMUNITY_ENGAGEMENT", "EMPLOYS"],
            "operational": ["SUPPLIES", "PARTNER_OF", "CLIENT_OF", "VENDOR_OF", "OPERATES"]
        }
        
        actual_coverage = {}
        for category, expected_rel_types in relationship_categories.items():
            category_rels = [
                rel for rel in existing_relationships 
                if rel.relationship_type in expected_rel_types
            ]
            # Simple coverage calculation based on relationship diversity
            actual_coverage[category] = min(len(set(rel.relationship_type for rel in category_rels)) / len(expected_rel_types), 1.0)
        
        # Identify coverage gaps
        for category, actual_score in actual_coverage.items():
            benchmark_score = benchmarks.get(category, 0.5)
            
            if actual_score < benchmark_score * 0.7:  # Significantly below benchmark
                coverage_gap = benchmark_score - actual_score
                gaps.append(CoverageGap(
                    gap_id=f"coverage_{category}_{target_entity.id}",
                    gap_category="coverage",
                    gap_type="insufficient_coverage",
                    description=f"Insufficient {category} coverage for {target_entity.name}",
                    priority=2 if coverage_gap > 0.4 else 3,
                    impact_score=coverage_gap,
                    missing_element=f"{category}_relationships",
                    context=f"Coverage {actual_score:.1%} vs benchmark {benchmark_score:.1%}",
                    entity_id=target_entity.id,
                    coverage_category=category,
                    completeness_percentage=actual_score,
                    missing_aspects=relationship_categories[category],
                    suggested_sources=[
                        f"{category}_reports", f"{category}_databases", 
                        f"{category}_disclosures", "annual_reports"
                    ]
                ))
        
        return gaps
    
    async def analyze_quality_gaps(
        self,
        target_entity: EntityExtended,
        existing_relationships: List[EntityRelationship]
    ) -> List[QualityGap]:
        """Analyze data quality issues and gaps."""
        gaps = []
        
        # Check for outdated data
        cutoff_date = datetime.utcnow() - timedelta(days=365)
        outdated_relationships = [
            rel for rel in existing_relationships 
            if rel.discovered_date and rel.discovered_date < cutoff_date
        ]
        
        if len(outdated_relationships) > len(existing_relationships) * 0.3:  # More than 30% outdated
            gaps.append(QualityGap(
                gap_id=f"quality_outdated_{target_entity.id}",
                gap_category="quality", 
                gap_type="outdated_data",
                description=f"Significant portion of data is outdated for {target_entity.name}",
                priority=3,
                impact_score=len(outdated_relationships) / len(existing_relationships),
                missing_element="current_data",
                context=f"{len(outdated_relationships)} of {len(existing_relationships)} relationships are outdated",
                affected_entities=[target_entity.id],
                quality_issue_type="outdated",
                confidence_level=0.8,
                suggested_sources=["data_refresh", "recent_documents", "current_databases"]
            ))
        
        # Check for low confidence relationships
        low_confidence_rels = [
            rel for rel in existing_relationships 
            if rel.confidence_score < 0.6
        ]
        
        if len(low_confidence_rels) > len(existing_relationships) * 0.2:  # More than 20% low confidence
            gaps.append(QualityGap(
                gap_id=f"quality_confidence_{target_entity.id}",
                gap_category="quality",
                gap_type="low_confidence",
                description=f"Many relationships have low confidence for {target_entity.name}",
                priority=4,
                impact_score=len(low_confidence_rels) / len(existing_relationships),
                missing_element="verification",
                context=f"{len(low_confidence_rels)} relationships need verification",
                affected_entities=[target_entity.id],
                quality_issue_type="unverified",
                confidence_level=0.6,
                verification_sources=["primary_sources", "multiple_confirmations"],
                suggested_sources=["verification_documents", "authoritative_sources"]
            ))
        
        # Check for conflicting relationships
        relationship_conflicts = []
        for i, rel1 in enumerate(existing_relationships):
            for rel2 in existing_relationships[i+1:]:
                if (rel1.from_entity_id == rel2.from_entity_id and 
                    rel1.to_entity_id == rel2.to_entity_id):
                    
                    # Check for conflicting relationship types
                    conflicting_pairs = [
                        ("OWNS", "OWNED_BY"),
                        ("CONTROLS", "CONTROLLED_BY"),
                        ("SUPPLIES", "SUPPLIED_BY")
                    ]
                    
                    for pair in conflicting_pairs:
                        if (rel1.relationship_type == pair[0] and rel2.relationship_type == pair[1]) or \
                           (rel1.relationship_type == pair[1] and rel2.relationship_type == pair[0]):
                            relationship_conflicts.append((rel1, rel2))
        
        if relationship_conflicts:
            gaps.append(QualityGap(
                gap_id=f"quality_conflicts_{target_entity.id}",
                gap_category="quality",
                gap_type="conflicting_data",
                description=f"Conflicting relationships found for {target_entity.name}",
                priority=2,
                impact_score=len(relationship_conflicts) / len(existing_relationships),
                missing_element="conflict_resolution",
                context=f"{len(relationship_conflicts)} relationship conflicts detected",
                affected_entities=[target_entity.id],
                quality_issue_type="conflicting",
                confidence_level=0.5,
                suggested_sources=["conflict_resolution", "authoritative_verification"]
            ))
        
        return gaps
    
    async def comprehensive_gap_analysis(
        self,
        target_entity: EntityExtended,
        context_entities: List[EntityExtended],
        existing_relationships: List[EntityRelationship],
        text_content: str = "",
        time_range_start: Optional[datetime] = None,
        time_range_end: Optional[datetime] = None
    ) -> GapAnalysisResult:
        """Perform comprehensive gap analysis across all categories."""
        
        # Set default time range if not provided
        if not time_range_start:
            time_range_start = datetime.utcnow() - timedelta(days=1825)  # 5 years ago
        if not time_range_end:
            time_range_end = datetime.utcnow()
        
        logger.info(f"Starting comprehensive gap analysis for {target_entity.name}")
        
        # Run all gap analysis types concurrently
        entity_gaps_task = self.analyze_entity_gaps(target_entity, context_entities, existing_relationships)
        relationship_gaps_task = self.analyze_relationship_gaps(target_entity, context_entities, existing_relationships, text_content)
        temporal_gaps_task = self.analyze_temporal_gaps(target_entity, existing_relationships, time_range_start, time_range_end)
        coverage_gaps_task = self.analyze_coverage_gaps(target_entity, existing_relationships)
        quality_gaps_task = self.analyze_quality_gaps(target_entity, existing_relationships)
        
        # Wait for all analyses to complete
        entity_gaps, relationship_gaps, temporal_gaps, coverage_gaps, quality_gaps = await asyncio.gather(
            entity_gaps_task,
            relationship_gaps_task,
            temporal_gaps_task,
            coverage_gaps_task,
            quality_gaps_task
        )
        
        # Calculate summary statistics
        all_gaps = entity_gaps + relationship_gaps + temporal_gaps + coverage_gaps + quality_gaps
        total_gaps = len(all_gaps)
        high_priority_gaps = len([gap for gap in all_gaps if gap.priority <= 2])
        critical_gaps = len([gap for gap in all_gaps if gap.priority == 1])
        
        # Generate recommendations
        immediate_actions = []
        research_priorities = []
        data_collection_targets = []
        
        # Prioritize recommendations based on gap analysis
        for gap in sorted(all_gaps, key=lambda x: (x.priority, -x.impact_score)):
            if gap.priority <= 2:
                immediate_actions.append(f"Address {gap.gap_type}: {gap.description}")
            elif gap.priority <= 3:
                research_priorities.append(f"Research {gap.gap_category}: {gap.missing_element}")
            
            # Add data collection targets
            for source in gap.suggested_sources[:2]:  # Top 2 sources
                if source not in data_collection_targets:
                    data_collection_targets.append(source)
        
        # Limit recommendations to top items
        immediate_actions = immediate_actions[:5]
        research_priorities = research_priorities[:8]
        data_collection_targets = data_collection_targets[:10]
        
        result = GapAnalysisResult(
            analysis_id=f"gap_analysis_{target_entity.id}_{int(datetime.utcnow().timestamp())}",
            target_entity_id=target_entity.id,
            target_entity_name=target_entity.name,
            entity_gaps=entity_gaps,
            relationship_gaps=relationship_gaps,
            temporal_gaps=temporal_gaps,
            coverage_gaps=coverage_gaps,
            quality_gaps=quality_gaps,
            total_gaps=total_gaps,
            high_priority_gaps=high_priority_gaps,
            critical_gaps=critical_gaps,
            immediate_actions=immediate_actions,
            research_priorities=research_priorities,
            data_collection_targets=data_collection_targets
        )
        
        # Store the analysis in history
        self.gap_history.append(result)
        
        logger.info(f"Gap analysis completed: {total_gaps} total gaps, {critical_gaps} critical, {high_priority_gaps} high priority")
        
        return result
    
    def get_gap_priorities(self, analysis_result: GapAnalysisResult) -> List[str]:
        """Get prioritized list of gaps to address based on analysis."""
        all_gaps = (
            analysis_result.entity_gaps + 
            analysis_result.relationship_gaps + 
            analysis_result.temporal_gaps + 
            analysis_result.coverage_gaps + 
            analysis_result.quality_gaps
        )
        
        # Sort by priority (lower number = higher priority) and impact score
        sorted_gaps = sorted(all_gaps, key=lambda x: (x.priority, -x.impact_score))
        
        return [
            f"[P{gap.priority}] {gap.gap_category.upper()}: {gap.description} "
            f"(Impact: {gap.impact_score:.2f})"
            for gap in sorted_gaps[:10]  # Top 10 priorities
        ]
    
    def get_data_collection_strategy(self, analysis_result: GapAnalysisResult) -> Dict[str, List[str]]:
        """Generate data collection strategy based on gap analysis."""
        strategy = defaultdict(list)
        
        all_gaps = (
            analysis_result.entity_gaps + 
            analysis_result.relationship_gaps + 
            analysis_result.temporal_gaps + 
            analysis_result.coverage_gaps + 
            analysis_result.quality_gaps
        )
        
        # Group by suggested sources
        source_gaps = defaultdict(list)
        for gap in all_gaps:
            for source in gap.suggested_sources:
                source_gaps[source].append(gap)
        
        # Prioritize sources by total impact
        for source, gaps in source_gaps.items():
            total_impact = sum(gap.impact_score for gap in gaps)
            gap_descriptions = [gap.description for gap in gaps[:3]]  # Top 3 descriptions
            
            strategy[source] = {
                "total_impact": total_impact,
                "gap_count": len(gaps),
                "key_gaps": gap_descriptions,
                "priority": min(gap.priority for gap in gaps)
            }
        
        # Sort sources by priority and impact
        sorted_strategy = dict(sorted(
            strategy.items(), 
            key=lambda x: (x[1]["priority"], -x[1]["total_impact"])
        ))
        
        return sorted_strategy


# ADK-compatible wrapper function
async def adk_comprehensive_gap_analysis(
    target_entity_eko_id: str,
    analysis_scope: str = "comprehensive",
    text_content: str = "",
    max_context_entities: int = 10,
    max_relationships: int = 50
) -> Dict[str, Any]:
    """ADK-compatible wrapper for comprehensive gap analysis.
    
    Args:
        target_entity_eko_id: EKO ID of the target entity
        analysis_scope: Scope of analysis (basic, standard, comprehensive)
        text_content: Optional text content for analysis
        max_context_entities: Maximum number of context entities to analyze
        max_relationships: Maximum number of relationships to analyze
    
    Returns:
        Dict with gap analysis results
    """
    try:
        # Create analyzer instance
        analyzer = SystematicGapAnalyzer()
        
        # Get database connection
        with get_bo_conn() as conn:
            # Retrieve target entity
            target_entity = EntityData.get_by_eko_id(conn, target_entity_eko_id)
            if not target_entity:
                raise ValueError(f"Entity with eko_id {target_entity_eko_id} not found")
            
            # Get existing relationships for the target entity
            existing_relationships = EntityRelationshipData.get_entity_relationships(conn, target_entity.id)
            
            # Limit relationships for performance
            if len(existing_relationships) > max_relationships:
                existing_relationships = existing_relationships[:max_relationships]
            
            # Get context entities from relationships
            context_entity_ids = set()
            for rel in existing_relationships:
                if rel.from_entity_id != target_entity.id:
                    context_entity_ids.add(rel.from_entity_id)
                if rel.to_entity_id != target_entity.id:
                    context_entity_ids.add(rel.to_entity_id)
            
            # Limit context entities for performance
            context_entity_ids = list(context_entity_ids)[:max_context_entities]
            
            # Retrieve context entities
            context_entities = []
            for entity_id in context_entity_ids:
                entity = EntityData.get_by_id(conn, entity_id)
                if entity:
                    context_entities.append(entity)
        
        # Perform gap analysis
        analysis_result = await analyzer.comprehensive_gap_analysis(
            target_entity=target_entity,
            context_entities=context_entities,
            existing_relationships=existing_relationships,
            text_content=text_content
        )
        
        # Format results for ADK
        return {
            "status": "success",
            "analysis_id": analysis_result.analysis_id,
            "target_entity": target_entity.name,
            "target_entity_id": target_entity.id,
            "gap_summary": {
                "total_gaps": analysis_result.total_gaps,
                "critical_gaps": analysis_result.critical_gaps,
                "high_priority_gaps": analysis_result.high_priority_gaps
            },
            "gap_categories": {
                "entity_gaps": len(analysis_result.entity_gaps),
                "relationship_gaps": len(analysis_result.relationship_gaps),
                "temporal_gaps": len(analysis_result.temporal_gaps),
                "coverage_gaps": len(analysis_result.coverage_gaps),
                "quality_gaps": len(analysis_result.quality_gaps)
            },
            "top_priorities": analyzer.get_gap_priorities(analysis_result)[:5],
            "immediate_actions": analysis_result.immediate_actions,
            "research_priorities": analysis_result.research_priorities,
            "data_collection_targets": analysis_result.data_collection_targets,
            "collection_strategy": analyzer.get_data_collection_strategy(analysis_result),
            "analysis_scope": analysis_scope,
            "analysis_timestamp": analysis_result.analysis_date.isoformat()
        }
        
    except Exception as e:
        logger.exception(f"Gap analysis failed for {target_entity_eko_id}")
        return {
            "status": "error",
            "error": str(e),
            "target_entity_eko_id": target_entity_eko_id,
            "analysis_scope": analysis_scope
        }