from google.adk.tools import ToolContext
from psycopg.rows import dict_row
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger

from eko.db import get_bo_conn
from eko.models import ResearchType
from typing import Dict, List, Any

from eko.scrape.reports import process_webpage


def document_collector(tool_context: ToolContext, research_types: List[ResearchType], entity_name: str) -> \
        Dict[str, Any]:
    """Execute web searches to find entity-related information and URLs."""
    urls_to_visit = tool_context.state.get('urls_to_visit', [])
    
    def process_url_safe(url: str) -> tuple[set, set]:
        """Process a single URL and return visited URLs and doc IDs."""
        local_visited = set()
        local_doc_ids = set()
        
        process_webpage(url, research_types, entity_name, depth=2, 
                       visited_urls=local_visited, doc_ids=local_doc_ids)
        
        return local_visited, local_doc_ids
    
    # Process URLs in parallel using thread pool
    visited_urls = set()
    doc_ids = set()
    
    with ThreadPoolExecutor() as executor:
        futures = [executor.submit(process_url_safe, url) for url in urls_to_visit]
        
        # Aggregate results from all completed tasks
        for future in as_completed(futures):
            try:
                local_visited, local_doc_ids = future.result()
                visited_urls.update(local_visited)
                doc_ids.update(local_doc_ids)
            except Exception:
                # Log error but continue processing other URLs  
                logger.exception("Error processing URL in thread pool")
                continue

    with get_bo_conn() as conn:
        with conn.cursor(row_factory=dict_row) as cur:
            cur.execute(
                "SELECT title,extract,authors,year FROM kg_documents WHERE kg_documents.id = ANY(%s)",
                (list(doc_ids),))
            docs = cur.fetchall()

    # TODO: Extract Entity Relationships from Docs
    tool_context.state['doc_ids'] = tool_context.state.get('doc_ids', []).extend(doc_ids)
    tool_context.state['urls_visited'] = tool_context.state.get('urls_visited', []).extend(visited_urls)

    return {
        '_doc_ids': list(doc_ids),
        'urls_visited': list(visited_urls),
        'documents_created': docs
    }
