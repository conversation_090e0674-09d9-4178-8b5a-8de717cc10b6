"""Knowledge Graph Service

Core service for managing entities and relationships in the knowledge graph.
Integrates with existing kg_base_entities and kg_entity_relations_map tables.
"""

import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
from loguru import logger

from eko.db import get_bo_conn
from eko.db.data.entity import EntityData, EntityExtended
from eko.db.data.entity_relationship import EntityRelationshipData
from eko.models.entity_relationship import EntityRelationship
from eko.models.common import name_to_eko_id, EntityType
from researcher.models.relationships import KGRelationship, RelationshipDiscovery


class KnowledgeGraphService:
    """Core knowledge graph operations service."""
    
    @staticmethod
    def create_entity(entity: EntityExtended) -> EntityExtended:
        """Create new entity in knowledge graph.
        
        Args:
            entity: EntityExtended model to create
            
        Returns:
            Created entity ID
        """
        with get_bo_conn() as conn:
            try:
                # Check if entity exists by eko_id first
                if entity.eko_id:
                    existing = EntityData.get_by_eko_id(conn, entity.eko_id)
                    if existing:
                        logger.info(f"Entity already exists: {existing.name}")
                        return existing
                
                # Use EntityData to create the entity
                created_entity = EntityData.create(conn, entity)
                
                logger.info(f"Created entity: {entity.name} (ID: {created_entity.id}, eko_id: {created_entity.eko_id})")
                return created_entity
                
            except Exception as e:
                logger.exception(f"Failed to create entity: {entity.name}")
                raise
    
    @staticmethod
    def create_relationship(entity_relationship: EntityRelationship) -> KGRelationship:
        """Create new relationship in knowledge graph.
        
        Args:
            relationship: Relationship model to create
            
        Returns:
            Created relationship ID
        """
        with get_bo_conn() as conn:
            try:
                # Check if relationship exists using eko_id (primary method) with fallback to legacy lookup
                existing = None

                # First try eko_id lookup if available (primary method - fastest and most reliable)
                if entity_relationship.eko_id:
                    existing = EntityRelationshipData.find_relationship_by_eko_id(
                        conn, entity_relationship.eko_id
                    )

                # If not found by eko_id, fall back to composite key lookup for legacy relationships
                if not existing:
                    existing = EntityRelationshipData.find_relationship_by_type(
                        conn,
                        entity_relationship.from_entity_id,
                        entity_relationship.to_entity_id,
                        entity_relationship.relationship_type,
                    )
                
                if existing:
                    # Update existing relationship
                    KnowledgeGraphService._update_existing_relationship(conn, existing, entity_relationship)
                    return existing
                
                # Create new relationship
                created_relationship = EntityRelationshipData.create(conn, entity_relationship)
                
                logger.info(f"Created relationship: {entity_relationship.relationship_type} between {entity_relationship.from_entity_id} and {entity_relationship.to_entity_id}")
                return created_relationship
                
            except Exception as e:
                logger.exception(f"Failed to create relationship: {entity_relationship.relationship_type}")
                raise
    
    @staticmethod
    def search_entities(
        search_term: str,
        entity_type: Optional[str] = None,
        limit: int = 10,
        min_confidence: float = 0.0
    ) -> List[EntityExtended]:
        """Search entities by name or identifiers.
        
        Args:
            search_term: Text to search for
            entity_type: Filter by entity type
            limit: Maximum results to return
            min_confidence: Minimum confidence score
            
        Returns:
            List of matching EntityExtended objects
        """
        with get_bo_conn() as conn:
            try:
                # Use EntityData fuzzy search functionality
                entity_results = list(EntityData.fuzzy_search(conn, search_term, canonical=True))
                
                # Filter by entity type if specified
                if entity_type:
                    entity_results = [e for e in entity_results if e.type == entity_type]
                
                # Limit results
                entity_results = entity_results[:limit]
                
                return entity_results
                
            except Exception as e:
                logger.exception(f"Failed to search entities: {search_term}")
                raise
    
    @staticmethod
    def get_entity_relationships(
        entity_id: int,
        relationship_types: Optional[List[str]] = None,
        max_depth: int = 2
    ) -> List[Dict[str, Any]]:
        """Get relationships for an entity with optional traversal.
        
        Args:
            entity_id: Entity to get relationships for
            relationship_types: Filter by relationship types
            max_depth: How deep to traverse relationships
            
        Returns:
            List of relationships with entity names
        """
        with get_bo_conn() as conn:
            try:
                return EntityRelationshipData.get_entity_relationships_with_names(
                    conn, entity_id, relationship_types
                )
            except Exception as e:
                logger.exception(f"Failed to get entity relationships: {entity_id}")
                raise
    
    @staticmethod
    def get_entity_by_id(entity_id: int) -> Optional[EntityExtended]:
        """Get entity by ID.
        
        Args:
            entity_id: Entity ID to retrieve
            
        Returns:
            EntityExtended if found
        """
        with get_bo_conn() as conn:
            try:
                entity = EntityData.get_by_id(conn, entity_id)
                if entity:
                    logger.info(f"Retrieved entity: {entity.name} (ID: {entity_id})")
                return entity
                
            except Exception as e:
                logger.exception(f"Failed to get entity by ID: {entity_id}")
                return None

    @staticmethod
    def get_entity_by_eko_id(eko_id: str) -> Optional[EntityExtended]:
        """Get entity by ID.

        Args:
            eko_id: Entity eko_id to retrieve

        Returns:
            EntityExtended if found
        """
        with get_bo_conn() as conn:
            try:
                entity = EntityData.get_by_eko_id(conn, eko_id)
                if entity:
                    logger.info(f"Retrieved entity: {entity.name} (ID: {eko_id})")
                return entity

            except Exception as e:
                logger.exception(f"Failed to get entity by ID: {eko_id}")
                return None


    @staticmethod
    def update_entity(entity: EntityExtended) -> bool:
        """Update existing entity in knowledge graph.
        
        Args:
            entity: EntityExtended model to update
            
        Returns:
            True if successful, False otherwise
        """
        with get_bo_conn() as conn:
            try:
                # Use EntityData to update the entity
                updated_entity = EntityData.update(conn, entity)
                
                logger.info(f"Updated entity: {entity.name} (ID: {entity.id})")
                return True
                
            except Exception as e:
                logger.exception(f"Failed to update entity: {entity.name}")
                return False

    @staticmethod
    def find_entity_by_identifiers(identifiers: Dict[str, str]) -> Optional[EntityExtended]:
        """Find entity by external identifiers, ensuring LEI-based entities exist.
        
        Args:
            identifiers: Dictionary of identifier types and values
            
        Returns:
            EntityExtended if found
        """
        if not identifiers:
            return None
            
        with get_bo_conn() as conn:
            try:
                # Check by LEI first and ensure LEI-based entity exists
                if 'lei' in identifiers:
                    lei = identifiers['lei']
                    # Use centralized function to ensure LEI-based entity exists
                    lei_entity_id = EntityData.ensure_lei_entity_exists(conn, lei)
                    if lei_entity_id:
                        entity = EntityData.get_by_id(conn, lei_entity_id)
                        if entity:
                            logger.info(f"Found/ensured LEI-based entity for {lei}: {entity.id}")
                            return entity
                
                # Check by ticker
                if 'ticker' in identifiers:
                    entities = EntityData.find_by_ticker(conn, identifiers['ticker'])
                    if entities:
                        return entities[0]
                
                # Check by CIK in description
                if 'cik' in identifiers:
                    entities = EntityData.find_by_cik(conn, identifiers['cik'])
                    if entities:
                        return entities[0]
                
                return None
                
            except Exception as e:
                logger.exception(f"Failed to find entity by identifiers: {identifiers}")
                return None
    
    @staticmethod
    def find_relationship(
        source_entity_id: str,
        target_entity_id: str,
        relationship_type: str
    ) -> Optional[EntityRelationship]:
        """Find existing relationship between entities.
        
        Args:
            source_entity_id: Source entity eko_id
            target_entity_id: Target entity eko_id
            relationship_type: Type of relationship
            
        Returns:
            EntityRelationship if found
        """
        with get_bo_conn() as conn:
            try:
                return EntityRelationshipData.find_relationship_by_eko_ids_and_type(
                    conn,
                    source_entity_id,
                    target_entity_id,
                    relationship_type
                )
            except Exception as e:
                logger.exception(f"Failed to find relationship: {source_entity_id} -> {target_entity_id}")
                return None
    
    @staticmethod
    def update_relationship(relationship: EntityRelationship) -> bool:
        """Update an existing relationship.
        
        Args:
            relationship: EntityRelationship model to update
            
        Returns:
            True if successful, False otherwise
        """
        with get_bo_conn() as conn:
            try:
                # Save changes
                EntityRelationshipData.update(conn, relationship)
                logger.info(f"Updated relationship: {relationship.relationship_type}")
                return True
                
            except Exception as e:
                logger.exception(f"Failed to update relationship: {relationship.relationship_type}")
                return False
    
    @staticmethod
    def find_connected_entities(
        root_entity_id: str,
        max_depth: int = 3,
        relationship_types: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Find entities connected to the root entity up to max_depth.
        
        Args:
            root_entity_id: Starting entity ID
            max_depth: Maximum traversal depth
            relationship_types: Filter by relationship types
            
        Returns:
            List of connected entities with path information
        """
        with get_bo_conn() as conn:
            try:
                return EntityRelationshipData.find_connected_entities(
                    conn,
                    int(root_entity_id),
                    max_depth,
                    relationship_types
                )
            except Exception as e:
                logger.exception(f"Failed to find connected entities: {root_entity_id}")
                return []
    
    @staticmethod
    def _kg_to_entity_relationship(kg_rel: KGRelationship) -> EntityRelationship:
        """Convert KGRelationship to EntityRelationship for database operations."""
        return EntityRelationship(
            id=kg_rel.id,
            from_entity_id=int(kg_rel.source_entity_id),
            to_entity_id=int(kg_rel.target_entity_id),
            relationship_category=EntityRelationshipData.get_relationship_category(kg_rel.relationship_type),
            relationship_type=kg_rel.relationship_type,
            relationship_sub_type=kg_rel.properties.get('sub_type'),
            relationship_source=kg_rel.discovery_method,
            relationship_data=kg_rel.properties,
            canonical=True,
            confidence_score=kg_rel.confidence,
            discovery_method=kg_rel.discovery_method,
            discovered_date=kg_rel.discovered_date,
            verified=kg_rel.verified,
            verification_source=kg_rel.verification_source,
            metadata={
                'evidence_documents': kg_rel.evidence_documents,
                'source_urls': kg_rel.source_urls,
                'weight': kg_rel.weight,
                **kg_rel.properties
            }
        )
    
    @staticmethod
    def _update_existing_relationship(conn, existing: EntityRelationship, new_relationship: EntityRelationship):
        """Update existing relationship with new information."""
        try:
            # Update confidence score to maximum
            existing.confidence_score = max(existing.confidence_score, new_relationship.confidence_score)
            
            # Merge evidence documents
            existing_evidence = existing.metadata.get('evidence_documents', []) if existing.metadata else []
            new_evidence = new_relationship.metadata.get('evidence_documents', []) if new_relationship.metadata else []
            merged_evidence = list(set(existing_evidence + new_evidence))

            # Update metadata
            if not existing.metadata:
                existing.metadata = {}
            existing.metadata.update(new_relationship.metadata)
            existing.metadata['evidence_documents'] = merged_evidence
            if 'weight' in new_relationship.metadata:
                existing.metadata['weight'] = max(
                    existing.metadata.get('weight', 0),
                    new_relationship.metadata['weight']
                )
            
            # Update verification if new relationship is verified
            if new_relationship.verified and not existing.verified:
                existing.verified = new_relationship.verified
                existing.verification_source = new_relationship.verification_source
            
            # Save changes
            EntityRelationshipData.update(conn, existing)
            logger.info(f"Updated existing relationship: {existing.relationship_type}")
            
        except Exception as e:
            logger.exception(f"Failed to update existing relationship: {existing.relationship_type}")
            raise
