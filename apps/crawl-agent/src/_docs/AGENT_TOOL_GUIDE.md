# ADK Entity Crawler Implementation Guide

## Summary

The ADK Entity Crawler is a multi-agent system implemented using Google ADK framework for intelligent entity discovery and relationship mapping. This guide documents the implementation patterns, agent architecture, and tool usage.

## Current Implementation Status

✅ **IMPLEMENTED**: Core ADK entity crawler with multi-agent delegation
✅ **IMPLEMENTED**: Recursive entity processor following pseudo-code workflow
✅ **IMPLEMENTED**: Knowledge graph service integration
✅ **IMPLEMENTED**: Enhanced tool ecosystem with observability
✅ **IMPLEMENTED**: CLI interface for entity crawling
✅ **IMPLEMENTED**: Phoenix observability integration

## Core Agent Architecture

### Main Coordinator Agent

Located in: `apps/crawl-agent/src/researcher/agents/coordinator.py`

```python
from researcher.agents.coordinator import adk_crawler, root_agent

# Main ADK Entity Crawler instance
adk_crawler = ADKEntityCrawler()
root_agent = adk_crawler.root_agent  # Export for ADK compatibility
```

### Specialized Agents

The system uses seven specialized agents coordinated by the main coordinator:

1. **CompanyAnalyzer** - Analyzes company data from SEC, GLEIF, Companies House, Wikipedia
2. **DocumentCrawler** - Discovers and processes relevant documents with comprehensive collection
3. **RelationshipMapper** - Maps relationships using enhanced discovery and traditional extraction
4. **KnowledgeGraphUpdater** - Updates knowledge graph with discovered entities and relationships
5. **SearchQueryGenerator** - Generates sophisticated search queries with historical learning
6. **ActionTracker** - Tracks entity actions, promises, and claims using DEMISE analysis
7. **GapAnalyzer** - Performs systematic gap analysis and data completeness assessment

## Correct Import Patterns

### Core ADK Imports

```python
from google.adk.agents import LlmAgent, SequentialAgent, ParallelAgent
from google.adk.tools import FunctionTool
from google.adk.tools.agent_tool import AgentTool  # Note: separate import
from google.adk.runners import InMemoryRunner
from google.adk.sessions import InMemorySessionService
```

### Agent Tool Delegation Pattern

```python
# Create specialized agents
company_analyzer = LlmAgent(
    name="CompanyAnalyzer",
    model="gemini-2.0-flash",
    description="Analyzes company data from multiple sources",
    instruction="Use tools to search SEC, GLEIF, Companies House...",
    tools=[enhanced_sec_search, enhanced_gleif_search, enhanced_companies_house_search]
)

# Wrap as tool for delegation
company_analyzer_tool = AgentTool(agent=company_analyzer)

# Use in coordinator agent
coordinator_agent = LlmAgent(
    name="EntityDiscoveryCoordinator",
    model="gemini-2.0-flash",
    description="Coordinates comprehensive entity discovery",
    instruction="Delegate to specialized agents...",
    tools=[company_analyzer_tool, document_crawler_tool, relationship_mapper_tool]
)
```

## Enhanced Tool Ecosystem

### Knowledge Graph Tools

Located in: `apps/crawl-agent/src/researcher/tools/entity_tools.py`

```python
from researcher.tools.entity_tools import (
    kg_upsert_entity,
    kg_create_relationship,
    kg_search_entities,
    kg_get_entity_relationships,
    generate_advanced_search_queries,
    generate_gap_analysis_queries
)
```

### Data Source Tools

Located in: `apps/crawl-agent/src/researcher/tools/data_source_tools.py`

```python
from researcher.tools.data_source_tools import (
    enhanced_sec_search,
    enhanced_gleif_search,
    enhanced_companies_house_search,
    enhanced_wikipedia_search
)
```

### Web Crawling Tools

Located in: `apps/crawl-agent/src/researcher/tools/web_tools.py`

```python
from researcher.tools.web_tools import (
    intelligent_web_crawler,
    process_document,
    adk_comprehensive_document_collection
)
```

## CLI Usage

Located in: `apps/crawl-agent/src/researcher/cli/adk_crawler.py`

```bash
# Basic entity crawling
python -m researcher.cli.adk_crawler crawl-entity --entity "Microsoft Corporation"

# Advanced crawling with custom settings
python -m researcher.cli.adk_crawler crawl-entity \
    --entity "Microsoft Corporation" \
    --entity-type "COMPANY" \
    --depth 3 \
    --observability

# Recursive network discovery
python -m researcher.cli.adk_crawler crawl-network \
    --entity "Microsoft Corporation" \
    --max-depth 3 \
    --max-entities 50
```

## Recursive Processing Workflow

The recursive processor (`apps/crawl-agent/src/researcher/agents/recursive_processor.py`) implements the pseudo-code workflow:

```python
# Process entity network following recursive pattern
async def process_network():
    network_results = await recursive_processor.process_entity_network(
        initial_entity_name="Microsoft Corporation",
        entity_type="COMPANY",
        max_depth=3,
        max_entities=50
    )
    return network_results
```

## Observability Integration

Phoenix observability is integrated for comprehensive monitoring:

```python
from researcher.observability.phoenix_setup import setup_phoenix_observability
from researcher.observability.metrics import setup_metrics

# Enable observability
setup_phoenix_observability()
setup_metrics()
```

## Service Integration

### Knowledge Graph Service

Located in: `apps/crawl-agent/src/researcher/services/kg_service.py`

```python
from researcher.services.kg_service import KnowledgeGraphService

# Create entities and relationships
entity_id = KnowledgeGraphService.create_entity(entity_data)
relationship_id = KnowledgeGraphService.create_relationship(relationship_data)
```

### Analytics Integration

Located in: `apps/crawl-agent/src/researcher/services/analytics_service.py`

```python
from researcher.services.analytics_service import AnalyticsIntegrationService

# Sync with analytics database
async def sync_data():
    await AnalyticsIntegrationService.sync_discoveries()
```

## Model Integration

### Entity Models

Located in: `apps/crawl-agent/src/researcher/models/entities.py`

```python
from researcher.models.entities import EnhancedKGEntity, EntityDiscovery
```

### Relationship Models

Located in: `apps/crawl-agent/src/researcher/models/relationships.py`

```python
from researcher.models.relationships import KGRelationship, RelationshipDiscovery
```

## Testing and Validation

The implementation includes comprehensive test coverage:

- Unit tests for individual agents
- Integration tests for the full workflow
- Performance benchmarks
- Data quality validation

## Migration from CrewAI

The ADK implementation replaces the previous CrewAI-based system with:

- Better agent coordination and delegation
- Enhanced observability and monitoring
- Improved tool integration patterns
- More sophisticated recursive processing
- Better integration with existing analytics infrastructure

## Performance Metrics

The system tracks comprehensive metrics:

- Entities discovered per hour
- Relationship discovery accuracy
- Document processing throughput
- Search query effectiveness
- Knowledge graph completeness

All metrics are tracked through Phoenix observability for real-time monitoring and optimization.
