/**
 * # Node.js Browser Compatibility Type Definitions for EkoIntelligence Platform
 *
 * This TypeScript module declaration file provides essential Node.js API type definitions that enable
 * browser-compatible polyfills for core Node.js modules in the EkoIntelligence ESG (Environmental, Social,
 * Governance) analysis platform. Built for Next.js 15 and React 19, this file bridges the gap between
 * Node.js server-side APIs and browser environments, allowing the platform to process ESG documents,
 * handle cryptographic operations, and manage binary data seamlessly across both server and client contexts.
 *
 * ## Core Functionality
 * - **Buffer Polyfill**: Browser-compatible Buffer implementation extending Uint8Array for binary data processing
 * - **Crypto Operations**: Essential cryptographic functions for document hashing and data integrity verification
 * - **Cross-Environment Compatibility**: Enables seamless data processing between server-side document analysis and client-side visualizations
 * - **Type Safety**: Full TypeScript support for Node.js APIs in browser environments with proper type checking
 * - **Global Buffer Support**: Extends NodeJS global namespace to provide Buffer functionality in module contexts
 *
 * ## ESG Platform Integration
 *
 * ### Document Processing Pipeline
 * - **Binary Data Handling**: Processes PDF documents, Excel files, and other binary ESG reports using Buffer APIs
 * - **Document Hashing**: Uses crypto functions for document integrity verification and deduplication in the analytics pipeline
 * - **Cross-Platform Analysis**: Enables consistent data processing between Python backend analytics and React frontend visualizations
 * - **File Upload Processing**: Handles document uploads with proper binary data encoding and validation
 *
 * ### Technology Stack Integration
 * - **Next.js 15 App Router**: Seamless server/client boundary crossing for ESG data processing workflows
 * - **React 19**: Enhanced concurrent features for processing large ESG datasets with proper type safety
 * - **Supabase Integration**: Compatible with Supabase blob storage for ESG document management and retrieval
 * - **TipTap Editor**: Enables document editing features with binary data support for embedded media and attachments
 * - **AI/ML Processing**: Supports document preprocessing for LLM analysis using Anthropic Claude, OpenAI GPT-4, and Google Gemini
 *
 * ## Buffer Module Declaration
 *
 * ### Browser-Compatible Buffer Implementation
 * Provides a Buffer interface that extends Uint8Array with Node.js-compatible methods:
 * - **slice()**: Creates new Buffer views for efficient memory management in large document processing
 * - **Buffer.from()**: Converts strings, ArrayBuffers, and arrays to Buffer instances for cross-platform compatibility
 * - **Buffer.alloc()**: Allocates zero-filled buffers for secure data initialization
 * - **Buffer.isBuffer()**: Type checking utility for runtime buffer validation
 *
 * ### Global Buffer Context
 * Extends NodeJS.Global interface to provide Buffer in module contexts, ensuring compatibility with:
 * - Document processing libraries that expect global Buffer availability
 * - ESG data analysis tools requiring consistent buffer handling
 * - Cross-platform utilities operating in both browser and Node.js environments
 *
 * ## Crypto Module Declaration
 *
 * ### Document Integrity Operations
 * Provides essential cryptographic functions for ESG document processing:
 * - **createHash()**: Generates SHA-256 hashes for document deduplication and integrity verification
 * - **Hash Interface**: Supports incremental hashing for large ESG reports and multi-part documents
 * - **Encoding Support**: Handles hex, base64, and other encodings for data interchange between platform components
 *
 * ### Security & Data Processing
 * - **Document Fingerprinting**: Creates unique identifiers for ESG documents to prevent duplicate processing
 * - **Data Integrity**: Ensures uploaded documents maintain integrity throughout the analytics pipeline
 * - **Cross-Platform Consistency**: Provides consistent hashing between Python backend and TypeScript frontend
 *
 * ## Browser Environment Compatibility
 *
 * ### Polyfill Strategy
 * - **Minimal Implementation**: Provides only essential APIs needed for ESG document processing
 * - **Performance Optimized**: Lightweight declarations focused on platform-specific use cases
 * - **Type-Safe Fallbacks**: Ensures type safety when Node.js APIs are used in browser contexts
 * - **Vite Integration**: Compatible with Vite's Node.js polyfill system for development and production builds
 *
 * ### Next.js Integration
 * - **Server/Client Boundary**: Enables seamless data flow between server-side document processing and client-side visualization
 * - **Static Generation**: Supports Next.js static generation for ESG reports with proper type checking
 * - **Edge Runtime**: Compatible with Vercel Edge Runtime for global ESG document processing
 *
 * ## Related Platform Components
 * - **Python Analytics Backend**: Processes ESG documents and generates analysis results
 * - **Supabase Customer Database**: Stores document metadata and processing results
 * - **TipTap Document Editor**: Rich text editing with document attachment support
 * - **AI-Powered Analysis**: LLM integration for ESG content analysis and report generation
 * - **Real-time Collaboration**: Multi-user document editing with binary data synchronization
 *
 * ## Usage Examples
 *
 * ### Document Processing
 * ```typescript
 * // Hash ESG document for deduplication
 * const hash = createHash('sha256');
 * hash.update(documentBuffer);
 * const documentId = hash.digest('hex');
 *
 * // Convert uploaded file to Buffer
 * const fileBuffer = Buffer.from(fileArrayBuffer);
 * const documentSlice = fileBuffer.slice(0, 1024); // Header processing
 * ```
 *
 * ### Cross-Platform Data Exchange
 * ```typescript
 * // Prepare data for analytics backend
 * const analysisData = Buffer.from(JSON.stringify(esgMetrics), 'utf8');
 * const encodedData = analysisData.toString('base64');
 * ```
 *
 * @see https://nodejs.org/api/buffer.html Node.js Buffer Documentation
 * @see https://nodejs.org/api/crypto.html Node.js Crypto Documentation
 * @see https://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility Vite Browser Compatibility
 * @see https://nextjs.org/docs/app/building-your-application/optimizing/bundle-analyzer Next.js Bundle Analysis
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Node.js browser compatibility type definitions for ESG document processing and cryptographic operations in the EkoIntelligence platform
 * @example ```typescript
 * // Document hashing for integrity verification
 * const documentHash = createHash('sha256').update(Buffer.from(document)).digest('hex');
 *
 * // Binary data processing for ESG reports
 * const reportBuffer = Buffer.from(uploadedFileData);
 * const headerSlice = reportBuffer.slice(0, 512);
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

// Custom Node.js types that work in browser environment
declare module "buffer" {
  export interface Buffer extends Uint8Array {
    slice(start?: number, end?: number): Buffer;
  }
  
  export const Buffer: {
    from(data: string | ArrayBufferLike | ArrayLike<number>, encoding?: string): Buffer;
    alloc(size: number): Buffer;
    isBuffer(obj: any): boolean;
  };
  
  // Fix global Buffer type in module context
  global {
    namespace NodeJS {
      interface Global {
        Buffer: {
          from(data: string | ArrayBufferLike | ArrayLike<number>, encoding?: string): Uint8Array;
          alloc(size: number): Uint8Array;
          isBuffer(obj: any): boolean;
        };
      }
    }
  }
}

declare module "crypto" {
  export function createHash(algorithm: string): {
    update(data: string | Buffer): any;
    digest(encoding: string): string;
  };
}
