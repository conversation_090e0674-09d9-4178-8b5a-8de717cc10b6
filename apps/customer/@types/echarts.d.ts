/**
 * # Apache ECharts TypeScript Declarations for EkoIntelligence ESG Platform
 *
 * This TypeScript declaration file provides comprehensive type definitions for Apache ECharts and
 * the echarts-for-react wrapper library within the EkoIntelligence ESG (Environmental, Social,
 * Governance) analysis platform. These declarations resolve critical module export and JSX namespace
 * compatibility issues that prevent proper TypeScript compilation while enabling rich data visualization
 * capabilities throughout the customer application.
 *
 * ## Core Purpose & Problem Resolution
 *
 * **Primary Issues Resolved:**
 * - **Module Export Conflicts**: Apache ECharts v5 changed from default exports to named exports, breaking TypeScript compatibility
 * - **JSX Namespace Missing**: echarts-for-react expects JSX namespace declarations that conflict with @types/react
 * - **Import Style Flexibility**: Supports both default import and named import patterns for maximum compatibility
 * - **React Integration**: Provides seamless TypeScript support for React-based chart components
 * - **Type Safety**: Enables proper TypeScript intellisense and error checking for chart implementations
 *
 * ## System Integration
 *
 * **EkoIntelligence ESG Platform Integration:**
 * This declaration file enables sophisticated data visualization capabilities across the platform:
 * - **Document Editor Charts**: TipTap editor extension for embedding interactive charts in ESG reports
 * - **Markdown Rendering**: Chart support in markdown content for collaborative document editing
 * - **HTML Processing**: Legacy chart compatibility in processed HTML content from external sources
 * - **Dashboard Visualizations**: Financial metrics, environmental scores, and governance indicators
 * - **Report Generation**: Dynamic chart creation for AI-powered ESG analysis reports
 *
 * **Frontend Architecture:**
 * - **Next.js 15 App Router**: Modern React framework with TypeScript-first development
 * - **Dual Chart Strategy**: Primary Recharts with ECharts fallback for complex visualizations
 * - **Glass-morphism UI**: Charts integrate with platform's translucent design language
 * - **Responsive Design**: Adaptive chart sizing across desktop and mobile devices
 * - **Theme Support**: Light/dark theme compatibility with dynamic color schemes
 *
 * ## Chart Implementation Patterns
 *
 * **Modern Recharts Integration**: Primary charting library with fallback to ECharts
 * - **Type Safety**: Full TypeScript support with proper interfaces and props
 * - **Performance**: SVG-based rendering with optimized React components
 * - **Accessibility**: Built-in ARIA support and screen reader compatibility
 * - **Customization**: Theme-aware styling with CSS custom properties
 *
 * **Legacy ECharts Support**: Maintained for backward compatibility and advanced features
 * - **Migration Path**: Seamless transition from existing ECharts implementations
 * - **Canvas Rendering**: High-performance rendering for complex datasets
 * - **Feature Coverage**: Advanced visualizations not yet available in Recharts
 * - **Serialization**: Robust JSON handling for complex chart configurations
 *
 * ## Technical Architecture
 *
 * **Module Declaration Strategy:**
 * - **Flexible Imports**: Supports both `import echarts from 'echarts'` and `import * as echarts from 'echarts'`
 * - **Named Exports**: Provides essential ECharts functions as named exports for compatibility
 * - **Interface Definitions**: Core ECharts instance methods with proper TypeScript signatures
 * - **Global JSX Fix**: Resolves JSX namespace conflicts without breaking React types
 * - **React Integration**: Full TypeScript support for echarts-for-react component props
 *
 * **Performance Considerations:**
 * - **Tree Shaking**: Compatible with ECharts v5 tree-shaking for optimized bundle sizes
 * - **Module Loading**: Supports both full and modular ECharts loading strategies
 * - **Memory Management**: Proper component lifecycle with dispose() method support
 * - **Event Handling**: Type-safe event binding with TypeScript intellisense
 *
 * @see https://echarts.apache.org/en/api.html Apache ECharts API Documentation
 * @see https://github.com/hustcc/echarts-for-react echarts-for-react React Wrapper
 * @see https://recharts.org/en-US/ Recharts Primary Chart Library
 * @see {@link ../../components/editor/extensions/chart-extension.tsx} TipTap Chart Extension
 * @see {@link ../../components/ui/chart.tsx} Recharts UI Components
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Custom TypeScript declarations resolving ECharts module export and JSX namespace compatibility issues for ESG data visualization
 * @example ```typescript
 * // Default import style (legacy compatibility)
 * import ReactECharts from 'echarts-for-react';
 *
 * // Component usage with full TypeScript support
 * <ReactECharts
 *   option={{
 *     title: { text: 'ESG Score Trends' },
 *     xAxis: { type: 'category', data: ['2020', '2021', '2022'] },
 *     yAxis: { type: 'value' },
 *     series: [{ type: 'line', data: [85, 88, 92] }]
 *   }}
 *   style={{ height: '400px' }}
 *   theme="dark"
 *   onChartReady={(chart) => console.log('Chart ready:', chart)}
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

// Custom echarts types to fix export assignment and JSX namespace issues
declare module "echarts" {
  const echarts: any;
  export default echarts;
  
  // Also provide named exports to work with different import styles
  export const init: any;
  export const connect: any;
  export const dispose: any;
  export const getInstanceByDom: any;
  export const use: any;
  export const registerTheme: any;
  export const registerMap: any;
  export const getMap: any;
  export const setPlatformAPI: any;
  export interface ECharts {
    setOption(option: any): void;
    getOption(): any;
    resize(): void;
    dispose(): void;
  }
  
  // Fix JSX namespace issue by providing it in the module
  global {
    namespace JSX {
      interface IntrinsicElements {
        [elemName: string]: any;
      }
      interface Element extends React.ReactElement<any, any> {}
    }
  }
}

declare module "echarts-for-react" {
  import React from 'react'

  interface EChartsReactProps {
    option: any;
    notMerge?: boolean;
    lazyUpdate?: boolean;
    style?: React.CSSProperties;
    className?: string;
    theme?: string;
    onChartReady?: (chart: any) => void;
    showLoading?: boolean;
    loadingOption?: any;
    onEvents?: Record<string, (param: any) => void>;
    opts?: any;
  }
  
  export default class ReactECharts extends React.Component<EChartsReactProps> {}
}
