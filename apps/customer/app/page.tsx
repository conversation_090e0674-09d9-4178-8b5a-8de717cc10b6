/**
 * # EkoIntelligence Platform Authentication Gate and Routing Component
 *
 * This Next.js 15 App Router page component serves as the primary authentication gateway and routing
 * controller for the EkoIntelligence ESG (Environmental, Social, Governance) analysis platform.
 * Acting as the root landing page at `/`, it implements server-side authentication validation using
 * Supabase Auth and performs intelligent routing based on user authentication status, ensuring that
 * all platform access is properly secured and users are directed to appropriate application sections.
 *
 * Built for enterprise-grade ESG analysis platforms serving financial services and sustainability-focused
 * organizations, this component provides the critical authentication checkpoint that protects sensitive
 * ESG data, corporate analysis results, claims verification systems, and collaborative document editing
 * features from unauthorized access while maintaining optimal user experience through seamless redirects.
 *
 * ## Core Functionality & Authentication Flow
 *
 * ### **Server-Side Authentication Validation**
 * Implements Supabase's recommended server-side authentication pattern using Next.js App Router:
 * 1. **Supabase Client Creation**: Instantiates authenticated server client using cookie-based session management
 * 2. **User Session Retrieval**: Calls `supabase.auth.getUser()` to validate current authentication state
 * 3. **Authentication Decision**: Determines routing destination based on user authentication status
 * 4. **Secure Redirection**: Performs server-side redirects to appropriate application sections
 * 5. **Session Persistence**: Maintains authentication context across redirects via secure HTTP-only cookies
 *
 * ### **Intelligent Routing Logic**
 * - **Unauthenticated Users**: Redirected to `/login` with return URL parameter for post-authentication routing
 * - **Authenticated Users**: Redirected directly to `/customer` dashboard for immediate ESG platform access
 * - **Return URL Handling**: Preserves user intent by encoding destination URL for post-login redirection
 * - **No Rendering**: Returns `null` as component never renders - purely a routing controller
 *
 * ## ESG Platform Integration & Security Architecture
 *
 * ### **Supabase Authentication Integration**
 * Leverages the comprehensive Supabase authentication system configured for the ESG platform:
 * - **Customer Database Access**: Authenticates users against customer database with Row Level Security (RLS) policies
 * - **Session Management**: Integrates with Supabase session middleware for persistent authentication state
 * - **Multi-Provider Support**: Compatible with OAuth providers (Google, GitHub) configured in Supabase dashboard
 * - **Enterprise Security**: Supports organization-based access controls and admin privilege validation
 * - **Token Refresh**: Automatic session token refresh handled by Supabase client configuration
 *
 * ### **ESG Data Protection Framework**
 * This authentication gate protects access to sensitive ESG platform features:
 * - **Entity Analysis**: Corporate ESG scoring, risk assessment, and benchmarking data
 * - **Claims Verification**: Corporate sustainability claims analysis against historical evidence
 * - **Promises Tracking**: Long-term corporate commitment monitoring and trend analysis
 * - **Document Collaboration**: TipTap-based collaborative editing of ESG reports and analysis
 * - **Analytics Dashboards**: Real-time ESG metrics, flags, and greenwashing detection results
 * - **Administrative Functions**: Organization management, user quotas, and system configuration
 *
 * ## Next.js 15 App Router Optimization Features
 *
 * ### **Force Dynamic Rendering**
 * Exports `dynamic = 'force-dynamic'` to ensure server-side authentication validation on every request:
 * - **Security Enforcement**: Prevents static generation that could bypass authentication checks
 * - **Fresh Authentication**: Ensures authentication state is validated against current database state
 * - **Session Validation**: Guarantees that user sessions are verified on each page load
 * - **Real-time Access Control**: Enables immediate response to user permission changes
 * - **Compliance Requirements**: Meets enterprise security requirements for authenticated ESG data access
 *
 * ### **Server Component Architecture**
 * Implements Next.js App Router server component pattern for optimal security and performance:
 * - **Server-Side Execution**: Authentication logic runs on server, protecting credentials and session data
 * - **Zero Client-Side Exposure**: No authentication tokens or user data exposed to browser JavaScript
 * - **Reduced Round Trips**: Eliminates client-side authentication checks and additional API requests
 * - **Improved Performance**: Server-side redirects are faster than client-side navigation
 * - **SEO Optimization**: Search engines see proper redirects rather than client-side authentication flows
 *
 * ## Database and Backend System Integration
 *
 * ### **Customer Database Authentication Context**
 * Integrates with the Supabase PostgreSQL customer database containing:
 * - **User Profiles**: `profiles` table with user information, organization membership, and preferences
 * - **Organization Access**: `acc_organisations` and related tables for multi-tenant ESG platform access
 * - **Document Permissions**: User access controls for collaborative ESG documents and reports
 * - **Admin Privileges**: Administrative flags for platform management and organization oversight
 * - **Feature Flags**: User-specific feature access and beta program participation
 *
 * ### **Analytics Database Connection**
 * While this component doesn't directly access the analytics database, the authenticated user context
 * enables downstream access to:
 * - **ESG Analytics**: Processed corporate data, entity scoring, and sustainability metrics
 * - **Claims Analysis**: Historical claims verification and evidence matching systems
 * - **Promises Tracking**: Corporate commitment monitoring and outcome analysis
 * - **Effect Flags**: Automated greenwashing detection and corporate impact assessments
 * - **Synchronized Data**: Data synchronized from analytics to customer database via `xfer_` tables
 *
 * ## Security Considerations & Compliance
 *
 * ### **Enterprise-Grade Authentication**
 * Implements security patterns required for financial services and ESG compliance:
 * - **Server-Side Validation**: All authentication checks performed on secure server environment
 * - **Session Security**: HTTP-only cookies prevent XSS attacks on authentication tokens
 * - **Request Integrity**: Every request validates authentication state against database
 * - **Audit Trail**: User authentication events logged for compliance and security monitoring
 * - **Access Control**: Row Level Security policies automatically filter data based on user context
 *
 * ### **Data Protection & Privacy**
 * Ensures compliance with data protection regulations for ESG platform users:
 * - **User Consent**: Authentication validates user agreement to platform terms and privacy policies
 * - **Data Minimization**: Only necessary user authentication data processed during validation
 * - **Secure Transmission**: All authentication data transmitted over HTTPS with proper headers
 * - **Session Isolation**: User sessions isolated to prevent cross-user data exposure
 * - **Automatic Cleanup**: Expired sessions automatically cleaned up by Supabase session management
 *
 * ## Route-Specific Behavior & User Experience
 *
 * ### **Login Flow Integration**
 * Coordinates with the login system to provide seamless authentication experience:
 * - **Return URL Preservation**: Encodes `/customer` as destination for post-authentication redirect
 * - **State Management**: Maintains user intent across authentication flow
 * - **Error Handling**: Graceful fallback if authentication state is indeterminate
 * - **Loading States**: Immediate redirect prevents loading state visibility to users
 * - **Mobile Compatibility**: Works seamlessly with mobile browsers and progressive web app features
 *
 * ### **Customer Dashboard Integration**
 * Successful authentication routes users to the comprehensive ESG platform dashboard:
 * - **Immediate Access**: Direct routing to `/customer` provides instant platform access
 * - **Context Preservation**: User authentication context available to all downstream components
 * - **Navigation Readiness**: Customer layout and navigation components have full user context
 * - **Feature Availability**: All ESG analysis features immediately accessible post-authentication
 * - **Personalization**: User-specific settings and preferences immediately available
 *
 * ## Performance Optimization & Monitoring
 *
 * ### **Efficient Authentication Validation**
 * Optimized for minimal latency while maintaining security:
 * - **Single Database Query**: Authentication validation requires only one database round trip
 * - **Connection Pooling**: Leverages Supabase connection pooling for optimal database performance
 * - **Caching Strategy**: User authentication state cached appropriately for performance
 * - **Error Recovery**: Graceful handling of database connectivity issues
 * - **Monitoring Integration**: Authentication metrics tracked for performance and security analysis
 *
 * ### **Scalability Considerations**
 * Designed to handle enterprise-scale authentication loads:
 * - **Stateless Architecture**: No server-side state storage beyond database sessions
 * - **Horizontal Scaling**: Compatible with multiple Next.js server instances
 * - **Edge Compatibility**: Works with Next.js Edge Runtime for global distribution
 * - **Load Distribution**: Authentication load distributed across Supabase infrastructure
 * - **Performance Monitoring**: Real-time monitoring of authentication performance and success rates
 *
 * ## Related Components & System Dependencies
 *
 * ### **Authentication Infrastructure**
 * - Supabase server client factory (`app/supabase/server.ts`) for secure database connection
 * - Authentication middleware (`app/supabase/middleware.ts`) for session management
 * - Login page component (`app/login/page.tsx`) for user authentication interface
 * - Customer layout (`app/customer/layout.tsx`) for authenticated user experience
 * - OAuth callback handler (`app/auth/callback/route.ts`) for external provider integration
 *
 * ### **ESG Platform Components**
 * - Customer dashboard (`app/customer/page.tsx`) for main ESG analysis interface
 * - Navigation system with user context and feature flag integration
 * - Document editing system with collaborative features and user permissions
 * - Administrative interface with organization management and user oversight
 * - Real-time analytics dashboards with user-specific data filtering
 *
 * @see https://nextjs.org/docs/app/api-reference/file-conventions/page Next.js App Router Page Component
 * @see https://nextjs.org/docs/app/building-your-application/rendering/server-components Next.js Server Components
 * @see https://supabase.com/docs/guides/auth/server-side/nextjs Supabase Next.js Server-Side Auth
 * @see https://nextjs.org/docs/app/api-reference/functions/redirect Next.js Redirect Function
 * @see {@link ./supabase/server.ts} Supabase Server Client Factory for Authentication
 * @see {@link ./login/page.tsx} Login Page Component for User Authentication
 * @see {@link ./customer/page.tsx} Customer Dashboard Landing Page
 * @see {@link ./customer/layout.tsx} Authenticated User Layout Component
 * @see {@link ./auth/callback/route.ts} OAuth Callback Handler for External Providers
 * <AUTHOR>
 * @updated 2025-07-25
 * @description EkoIntelligence platform authentication gateway that validates user sessions and routes to appropriate application sections for secure ESG analysis platform access
 * @example
 * ```tsx
 * // This component is automatically rendered by Next.js App Router when users access "/"
 * // Example authentication flow:
 *
 * // 1. Unauthenticated user visits "/" 
 * // Result: Redirected to "/login?next=%2Fcustomer"
 *
 * // 2. Authenticated user visits "/"
 * // Result: Redirected to "/customer" dashboard
 *
 * // 3. User completes login and returns to this component
 * // Result: Now authenticated, redirected to "/customer"
 *
 * // The component implements this logic:
 * const supabase = await createClient();
 * const { data: { user } } = await supabase.auth.getUser();
 *
 * if (!user) {
 *   // Route to login with return destination
 *   redirect("/login?next=" + encodeURIComponent("/customer"));
 * } else {
 *   // User authenticated - route to main application
 *   redirect("/customer");
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import './globals.css'
import { createClient } from '@/app/supabase/server'
import { redirect } from 'next/navigation'

export const dynamic = 'force-dynamic';

export default async function Index() {
    const supabase =  await createClient();
    const {
        data: {user},
    } = await supabase.auth.getUser();

    if (!user) {
        redirect("/login?next="+encodeURIComponent("/customer"));
    } else {
        redirect("/customer");
    }

    return null;
}
