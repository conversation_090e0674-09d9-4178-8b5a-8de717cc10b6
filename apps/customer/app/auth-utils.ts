/**
 * # Server-Side Authentication & Authorization Utilities for ESG Analysis Platform
 *
 * This utility module provides server-side authentication and administrative access control for the EkoIntelligence
 * ESG (Environmental, Social, Governance) analysis platform. It implements secure user authentication verification
 * and email domain-based administrative access control within Next.js Server Components, API routes, and
 * server-side operations. The module serves as a critical security layer ensuring proper authentication state
 * management and administrative privilege verification throughout the customer application.
 *
 * ## Core Functionality
 * - **Authentication Verification**: Server-side user authentication checks with automatic login redirects
 * - **Administrative Access Control**: Email domain-based admin privilege verification for company personnel
 * - **Redirect Management**: Intelligent login redirect handling with deep-link preservation
 * - **Supabase SSR Integration**: Seamless integration with Supabase server-side authentication patterns
 * - **Session State Management**: Robust user session validation and authentication state handling
 *
 * ## Authentication Flow & Security Model
 *
 * ### **User Authentication Process**
 * The `checkAuth()` function implements a comprehensive authentication verification workflow:
 * 1. **Session Retrieval**: Fetches current user session from Supabase server client with cookie-based auth
 * 2. **User Validation**: Verifies that a valid, authenticated user exists in the session
 * 3. **Redirect Handling**: Automatically redirects unauthenticated users to login with return URL preservation
 * 4. **Deep-Link Support**: Maintains original destination URL for seamless post-login navigation
 * 5. **Server-Side Security**: Ensures authentication checks occur server-side for maximum security
 *
 * ### **Administrative Access Model**
 * The `checkAdmin()` function provides **company domain-based administrative verification**:
 * - **Email Domain Validation**: Grants administrative privileges to verified `@ekointelligence.com` email addresses
 * - **Company Personnel Access**: Automatic admin access for all authenticated company employees
 * - **Client-Server Consistency**: Mirrors client-side admin checks for consistent authorization behavior
 * - **Session-Based Verification**: Real-time admin status based on current authenticated user session
 *
 * ## System Architecture Integration
 * This utility integrates with the broader EkoIntelligence platform security architecture:
 * - **Authentication Layer**: Supabase Auth provides JWT-based authentication with secure session management
 * - **Authorization Layer**: Server-side utilities enforce access control; database RLS policies provide data security
 * - **Customer Database**: User profiles, organizations, and ESG data protected by comprehensive RLS policies
 * - **Analytics Backend**: Python system with separate authentication for data processing operations
 * - **Next.js Integration**: Server Components and API routes use these utilities for secure access control
 *
 * ## Database Security Integration
 *
 * ### **Row Level Security (RLS) Policies**
 * The server-side admin checks correspond to database-level security implementations:
 * - **`public.is_admin()` Function**: PostgreSQL function for database-level administrative verification
 * - **RLS Policy Enforcement**: Database policies use `is_admin()` for fine-grained access control
 * - **Data Protection**: Sensitive ESG data protected by user-based and role-based access policies
 * - **Audit Compliance**: Administrative actions tracked through database audit logs and triggers
 *
 * ### **Database Schema Security**
 * ```sql
 * -- Example RLS policies using is_admin() function
 * CREATE POLICY "Admins can manage entities" ON public.xfer_entities 
 *   FOR ALL USING (public.is_admin());
 *
 * CREATE POLICY "Admins can delete analysis data" ON public.xfer_flags 
 *   FOR DELETE USING (public.is_admin());
 * ```
 *
 * ## Next.js Server-Side Integration
 *
 * ### **Server Component Usage**
 * ```typescript
 * // Protect entire server component
 * export default async function ProtectedPage() {
 *   await checkAuth('/protected-page');
 *   // Component renders only for authenticated users
 *   return <div>Protected Content</div>;
 * }
 *
 * // Conditional admin features
 * export default async function Dashboard() {
 *   const isAdmin = await checkAdmin();
 *   return (
 *     <div>
 *       <UserDashboard />
 *       {isAdmin && <AdminControls />}
 *     </div>
 *   );
 * }
 * ```
 *
 * ### **API Route Protection**
 * ```typescript
 * // API route with authentication
 * export async function GET() {
 *   await checkAuth('/api/protected');
 *   // API logic for authenticated users only
 *   return Response.json({ data: 'protected' });
 * }
 *
 * // Admin-only API endpoint
 * export async function DELETE() {
 *   const isAdmin = await checkAdmin();
 *   if (!isAdmin) {
 *     return Response.json({ error: 'Forbidden' }, { status: 403 });
 *   }
 *   // Admin-only operations
 * }
 * ```
 *
 * ## Redirect Strategy & User Experience
 *
 * ### **Intelligent Login Redirects**
 * The authentication system implements sophisticated redirect handling:
 * - **Original URL Preservation**: Maintains user's intended destination across login flow
 * - **Query Parameter Encoding**: Safely encodes complex URLs with query parameters and fragments
 * - **Login Flow Integration**: Seamless integration with Supabase Auth login pages and flows
 * - **Post-Login Navigation**: Automatic return to original destination after successful authentication
 *
 * ### **Deep-Link Support**
 * ```typescript
 * // User visits: /customer/dashboard/reports?entity=abc123&view=details
 * // Unauthenticated → Redirects to: /login?next=%2Fcustomer%2Fdashboard%2Freports%3Fentity%3Dabc123%26view%3Ddetails
 * // After login → Returns to: /customer/dashboard/reports?entity=abc123&view=details
 * ```
 *
 * ## Security Considerations & Best Practices
 *
 * ### **Server-Side Security Enforcement**
 * - **Authentication Required**: All sensitive operations protected by server-side authentication checks
 * - **Session Validation**: Real-time session verification prevents token replay and session hijacking
 * - **Database-Level Security**: RLS policies provide defense-in-depth beyond application-level checks
 * - **Admin Verification**: Administrative privileges verified both client-side (UI) and server-side (data)
 *
 * ### **Error Handling & Security**
 * - **Automatic Redirects**: Unauthenticated access automatically redirects to secure login flow
 * - **No Sensitive Data Exposure**: Authentication failures handled gracefully without information leakage
 * - **Session Expiry Handling**: Expired sessions automatically trigger re-authentication workflow
 * - **Error Logging**: Authentication failures logged for security monitoring and audit compliance
 *
 * ## Related Security Components
 * - Supabase server client factory in `app/supabase/server.ts` for authenticated database access
 * - Client-side admin utilities in `app/auth-utils-client.ts` for UI enhancement
 * - Session middleware in `app/supabase/middleware.ts` for authentication persistence
 * - Database RLS policies using `public.is_admin()` function for data access control
 * - User profile management system in customer database for account administration
 *
 * @see https://supabase.com/docs/guides/auth/server-side/nextjs Supabase Next.js Server-Side Authentication
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security Guide
 * @see https://nextjs.org/docs/app/building-your-application/authentication Next.js Authentication Patterns
 * @see https://supabase.com/docs/reference/javascript/auth-getuser Supabase Auth getUser() Documentation
 * @see {@link ./supabase/server.ts} Supabase server client factory for authenticated database access
 * @see {@link ./auth-utils-client.ts} Client-side authentication utilities for UI enhancement
 * @see {@link ../../database.types.ts} Generated TypeScript database types
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Server-side authentication verification and administrative access control utilities for ESG analysis platform security
 * @example ```typescript
 * import { checkAuth, checkAdmin } from '@/app/auth-utils'
 *
 * // Protect server component
 * export default async function ProtectedComponent() {
 *   await checkAuth('/protected-route');
 *
 *   const isAdmin = await checkAdmin();
 *
 *   return (
 *     <div>
 *       <h1>Protected Content</h1>
 *       {isAdmin && (
 *         <AdminPanel>
 *           <button>Manage ESG Data</button>
 *           <button>User Administration</button>
 *         </AdminPanel>
 *       )}
 *     </div>
 *   );
 * }
 *
 * // Protect API route
 * export async function GET(request: Request) {
 *   await checkAuth('/api/protected');
 *
 *   // Fetch ESG data for authenticated user
 *   const supabase = await createClient();
 *   const { data: entities } = await supabase
 *     .from('xfer_entities')
 *     .select('*')
 *     .limit(10);
 *
 *   return Response.json({ entities });
 * }
 *
 * // Admin-only API endpoint
 * export async function DELETE(request: Request) {
 *   const isAdmin = await checkAdmin();
 *   if (!isAdmin) {
 *     return Response.json({ 
 *       error: 'Administrative privileges required' 
 *     }, { status: 403 });
 *   }
 *
 *   // Perform admin-only operations
 *   return Response.json({ success: true });
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createClient } from './supabase/server'
import { redirect } from 'next/navigation'

/**
 * Verifies user authentication and redirects to login if unauthenticated.
 *
 * This function performs server-side authentication verification by checking
 * the current user session via Supabase server client. If no authenticated
 * user is found, it automatically redirects to the login page with the current
 * URL preserved as a return destination for seamless post-login navigation.
 *
 * **Security Note**: This function provides server-side authentication
 * enforcement and should be called at the beginning of protected Server
 * Components and API routes to ensure only authenticated users can access
 * sensitive content and operations.
 *
 * @param {string} redirectUrl - The current page URL to return to after login
 * @returns {Promise<boolean>} Promise resolving to false if authenticated (doesn't throw)
 *
 * @throws {Error} Automatically redirects via Next.js `redirect()` if user is not authenticated
 *
 * @example
 * ```typescript
 * // Protect entire server component
 * export default async function ProtectedPage() {
 *   await checkAuth('/customer/dashboard');
 *   // Only authenticated users reach this point
 *   return <div>Protected dashboard content</div>;
 * }
 *
 * // Protect API route
 * export async function GET() {
 *   await checkAuth('/api/protected-data');
 *   // Fetch sensitive data for authenticated users
 *   const data = await fetchUserData();
 *   return Response.json(data);
 * }
 *
 * // With complex URLs and query parameters
 * const currentUrl = '/customer/reports?entity=abc123&view=details';
 * await checkAuth(currentUrl);
 * // Redirects to: /login?next=%2Fcustomer%2Freports%3Fentity%3Dabc123%26view%3Ddetails
 * ```
 *
 * @see {@link https://supabase.com/docs/reference/javascript/auth-getuser} Supabase Auth getUser() Documentation
 * @see {@link https://nextjs.org/docs/app/api-reference/functions/redirect} Next.js redirect() Function
 */
export async function checkAuth(redirectUrl: string): Promise<boolean> {
    const supabase = await createClient();
    const {
        data: {user},
    } = await supabase.auth.getUser();

    if (!user) {
        redirect("/login?next=" + encodeURIComponent(redirectUrl));
    } else {
        return false;
    }
}

/**
 * Verifies if the currently authenticated user has administrative privileges
 * by checking if their email address belongs to the company domain.
 *
 * This function performs server-side administrative access verification using
 * email domain validation against the company domain (@ekointelligence.com).
 * It fetches the current user's authentication session from Supabase server
 * client and determines administrative status based on email domain matching.
 * This mirrors the client-side `checkAdmin()` function for consistent
 * authorization behavior across the application.
 *
 * **Security Note**: This provides server-side admin verification for API
 * routes and server components. All database-level administrative operations
 * are additionally protected by PostgreSQL RLS policies using the
 * `public.is_admin()` function for defense-in-depth security.
 *
 * @returns {Promise<boolean>} Promise resolving to true if user is admin, false otherwise
 *
 * @example
 * ```typescript
 * // Conditional admin features in server component
 * export default async function AdminDashboard() {
 *   const isAdmin = await checkAdmin();
 *
 *   return (
 *     <div>
 *       <UserDashboard />
 *       {isAdmin && (
 *         <AdminPanel>
 *           <button>Manage ESG Entities</button>
 *           <button>User Administration</button>
 *           <button>System Configuration</button>
 *         </AdminPanel>
 *       )}
 *     </div>
 *   );
 * }
 *
 * // Admin-only API route protection
 * export async function DELETE() {
 *   const isAdmin = await checkAdmin();
 *
 *   if (!isAdmin) {
 *     return Response.json({ 
 *       error: 'Administrative privileges required for this operation' 
 *     }, { status: 403 });
 *   }
 *
 *   // Perform admin-only database operations
 *   await deleteESGEntity(entityId);
 *   return Response.json({ success: true });
 * }
 *
 * // Combined authentication and authorization check
 * export async function GET() {
 *   await checkAuth('/api/admin/data');
 *   const isAdmin = await checkAdmin();
 *
 *   if (!isAdmin) {
 *     return Response.json({ error: 'Forbidden' }, { status: 403 });
 *   }
 *
 *   const adminData = await fetchAdminData();
 *   return Response.json(adminData);
 * }
 * ```
 *
 * @throws {Error} May throw if Supabase server client fails to initialize or auth service is unavailable
 *
 * @see {@link https://supabase.com/docs/reference/javascript/auth-getuser} Supabase Auth getUser() Documentation
 * @see {@link ./auth-utils-client.ts} Client-side admin verification utility
 */
export async function checkAdmin(): Promise<boolean> {
    const supabase = await createClient()
    const user = (await supabase.auth.getUser()).data
    return !!user.user?.email?.endsWith("@ekointelligence.com");
}
