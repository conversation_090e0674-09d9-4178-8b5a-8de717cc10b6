/**
 * Terms of Service Page - Legal Framework and User Agreement Documentation
 *
 * This React component renders EkoIntelligence's comprehensive Terms of Service as a static page,
 * establishing the legal framework governing user access and use of the ESG analysis platform.
 * Built with Next.js 15 App Router and leveraging react-markdown for clean markdown rendering,
 * this page provides users with clear understanding of their rights, obligations, and the
 * terms under which they may access and use EkoIntelligence's services.
 *
 * ## Core Functionality
 * - **Legal Terms Display**: Comprehensive markdown-formatted terms of service with detailed sections
 * - **User Agreement Framework**: Clear articulation of user rights, responsibilities, and platform rules
 * - **Service Usage Guidelines**: Detailed explanation of acceptable use policies and restrictions
 * - **Account Management Terms**: Terms covering registration, security, and account responsibilities
 * - **Payment and Subscription Terms**: Comprehensive fee structure and billing policy documentation
 * - **Intellectual Property Protection**: Copyright and trademark protections for platform content
 * - **Liability and Warranty Disclaimers**: Legal limitations and service availability terms
 * - **Termination and Modification Policies**: Account suspension/termination procedures and policy update processes
 *
 * ## Terms of Service Structure
 * **Legal Framework Sections**:
 * ```
 * 1. Acceptance of Terms
 *    ├── Age and Authority Requirements (18+ years)
 *    ├── Legal Capacity Confirmation
 *    └── Organizational Authorization
 *
 * 2. Account Registration and Security
 *    ├── Account Information Requirements (Accurate, Complete, Current)
 *    ├── Security Responsibilities (Credential Protection)
 *    ├── Unauthorized Access Notification
 *    └── Account Activity Responsibility
 *
 * 3. Acceptable Use Policies
 *    ├── Lawful Purpose Requirements
 *    ├── Platform Integrity Protection (No Reverse Engineering)
 *    ├── Communication Standards (No Spam/Abuse)
 *    └── System Protection (No Harmful Activities)
 *
 * 4. Fees and Payment Terms
 *    ├── Subscription Fee Obligations
 *    ├── Non-Refundable Fee Policy
 *    ├── Pricing Change Procedures
 *    └── Continued Use Acceptance
 *
 * 5. Intellectual Property Rights
 *    ├── EkoIntelligence Platform Ownership
 *    ├── Limited License Grant (Non-exclusive, Non-transferable)
 *    ├── Usage Restrictions (No Copying, Distribution)
 *    └── Ownership Claims Prohibition
 *
 * 6. Data and Privacy Framework
 *    ├── Privacy Policy Integration
 *    ├── Data Processing Compliance
 *    ├── Third-party Integration Responsibilities
 *    └── Data Protection Law Compliance
 *
 * 7. Service Availability and Disclaimers
 *    ├── "As Is" and "As Available" Service Provision
 *    ├── Uptime and Reliability Expectations
 *    ├── Maintenance and Downtime Notifications
 *    └── Unforeseeable Circumstances Handling
 *
 * 8. Liability Limitations and Legal Protections
 *    ├── Indirect Damage Exclusions
 *    ├── Monetary Liability Caps (12-month fee limit)
 *    ├── Force Majeure Protections
 *    └── Legal Remedy Limitations
 *
 * 9. Termination Procedures and Rights
 *    ├── Terms Violation Consequences
 *    ├── Service Discontinuation Rights
 *    ├── Post-Termination Obligations
 *    └── Data and Material Deletion Requirements
 *
 * 10. Legal Governance and Modifications
 *     ├── Policy Update Procedures
 *     ├── Change Notification Systems
 *     ├── Continued Use Acceptance
 *     └── UK Law Governance Framework
 * ```
 *
 * ## Legal and Compliance Framework
 * **Regulatory Compliance**: The terms of service establish compliance with key legal requirements:
 * - **UK Contract Law**: Terms structured according to UK legal precedents and requirements
 * - **Consumer Rights**: Consumer Rights Act 2015 compliance for UK customers
 * - **Data Protection Integration**: Seamless integration with Privacy Policy (GDPR/UK Data Protection Act)
 * - **Commercial Law Compliance**: B2B contract terms for enterprise customers
 * - **Dispute Resolution**: UK court jurisdiction for legal disputes and enforcement
 *
 * **Legal Protections Implemented**:
 * - **Service Level Expectations**: Realistic service availability commitments without guarantees
 * - **Liability Limitations**: Standard commercial liability caps and exclusions
 * - **Intellectual Property Protection**: Comprehensive IP rights reservation and usage restrictions
 * - **Termination Rights**: Balanced termination procedures protecting both parties
 * - **Modification Procedures**: Clear policy update notification and acceptance processes
 *
 * ## Technical Implementation
 * **Component Architecture**: Built as a Next.js server component for optimal performance:
 * - **Server-Side Rendering**: Static generation for fast loading and SEO optimization
 * - **React Markdown Integration**: Clean markdown parsing with react-markdown library
 * - **Responsive Design**: Mobile-first approach with max-width container (800px)
 * - **Typography**: Semantic HTML structure with proper heading hierarchy
 * - **Accessibility**: WCAG 2.1 AA compliance with proper contrast and navigation
 *
 * **Markdown Processing**: The terms content is stored as a template literal and processed by:
 * - **React Markdown v10+**: GitHub Flavored Markdown support for consistent formatting
 * - **Responsive Layout**: Centered layout with appropriate margins and max-width constraints
 * - **Print Optimization**: Clean printing layout for offline reference and legal records
 * - **Mobile Optimization**: Touch-friendly navigation and readable typography on small screens
 *
 * ## Content Management and Legal Maintenance
 * **Terms Maintenance**: The terms of service require regular legal review and updates:
 * - **Legal Review Cycle**: Quarterly legal counsel review for accuracy and enforceability
 * - **Regulatory Updates**: Immediate updates when commercial or consumer protection laws change
 * - **Business Practice Alignment**: Updates when service offerings or business model evolves
 * - **Industry Standards**: Alignment with ESG industry best practices and standards
 * - **User Feedback Integration**: Clarity improvements based on customer service inquiries
 *
 * **Version Control and Notification**:
 * - **Last Updated Date**: Prominently displayed date of most recent terms revision
 * - **Material Change Notifications**: Users notified of significant changes via email/platform notifications
 * - **Historical Versions**: Previous versions archived for legal compliance and reference
 * - **Change Documentation**: Detailed change logs maintained for regulatory and legal audit purposes
 *
 * ## Business Terms Integration
 * **Service Scope Definition**: The terms clearly define EkoIntelligence's service offerings:
 * - **ESG Analysis Platform**: Terms covering corporate ESG data analysis and reporting
 * - **Document Processing**: Terms for user-uploaded document analysis and AI processing
 * - **Data Analytics**: Terms covering proprietary algorithms and analysis methodologies
 * - **API and Integration Services**: Terms for technical integrations and data access
 * - **Collaborative Features**: Terms for document sharing and team collaboration
 *
 * **Commercial Terms**:
 * - **Subscription Models**: Terms covering various subscription tiers and enterprise packages
 * - **Usage-Based Billing**: Terms for consumption-based pricing models
 * - **Professional Services**: Terms for consulting and implementation services
 * - **Data Export and Portability**: Terms covering data ownership and export rights
 * - **Third-Party Integrations**: Terms for connecting external systems and data sources
 *
 * ## Integration with EkoIntelligence Platform
 * **System Context**: These terms of service govern the broader EkoIntelligence platform:
 * - **Authentication System**: Terms integration with Supabase Auth and user management
 * - **Profile Management**: Terms covering user profiles, organizations, and admin functions
 * - **Document Management**: Terms for collaborative document editing and version control
 * - **ESG Analysis Tools**: Terms covering proprietary analysis algorithms and methodologies
 * - **Reporting and Export**: Terms for generated reports, PDF exports, and data portability
 *
 * **Platform-Specific Terms**:
 * - **AI and Machine Learning**: Terms covering AI-generated content and analysis limitations
 * - **Data Sources**: Terms for third-party data integration and accuracy disclaimers
 * - **Collaborative Editing**: Terms for real-time document collaboration and intellectual property
 * - **Analytics and Monitoring**: Terms for usage analytics and performance monitoring
 * - **Security and Access Control**: Terms for data security, access controls, and breach notification
 *
 * ## User Rights and Responsibilities Framework
 * **User Rights Protected**:
 * - **Service Access**: Right to access subscribed services according to plan limitations
 * - **Data Ownership**: Rights to user-generated content and uploaded documents
 * - **Privacy Protection**: Rights to data privacy according to integrated Privacy Policy
 * - **Account Control**: Rights to account management, password changes, and data export
 * - **Termination Rights**: Right to terminate account with appropriate notice
 *
 * **User Responsibilities**:
 * - **Account Security**: Obligation to maintain secure credentials and report breaches
 * - **Accurate Information**: Responsibility to provide truthful registration and billing information
 * - **Acceptable Use**: Obligation to use platform for lawful purposes only
 * - **Payment Obligations**: Responsibility for timely payment of subscription fees
 * - **Compliance**: Obligation to comply with all applicable laws and regulations
 *
 * ## Next.js 15 App Router Integration
 * **Static Generation**: This component leverages Next.js static generation for:
 * - **Performance**: Pre-rendered HTML for instant loading across global CDN
 * - **SEO Optimization**: Server-side rendering for search engine visibility and legal discoverability
 * - **Caching Strategy**: Efficient caching for static legal content with appropriate cache headers
 * - **Global Distribution**: Optimized delivery for worldwide legal accessibility
 *
 * **Route Structure and Legal Context**:
 * ```
 * /policy/tos/
 * ├── page.tsx (This component - Terms of Service)
 * ├── layout.tsx (Policy navigation layout)
 * ├── metadata (SEO and social sharing optimization)
 * └── Related Legal Pages:
 *     ├── /policy/privacy/ (Privacy Policy - Data protection terms)
 *     ├── /policy/cookies/ (Cookie Policy - Tracking technology terms)  
 *     └── /policy/acceptable-use/ (Acceptable Use Policy - Platform usage guidelines)
 * ```
 *
 * ## Accessibility and Legal Document Standards
 * **Accessibility Compliance**: The terms of service meets WCAG 2.1 AA and legal document standards:
 * - **Semantic HTML**: Proper heading hierarchy and landmark regions for screen readers
 * - **Keyboard Navigation**: Full keyboard accessibility for all interactive elements
 * - **Screen Reader Support**: ARIA labels and descriptive text for assistive technology
 * - **Color Contrast**: Sufficient contrast ratios meeting accessibility and readability standards
 * - **Responsive Design**: Accessible across all device sizes and orientations
 *
 * **Legal Document Best Practices**:
 * - **Plain Language**: Legal content written in accessible language where possible
 * - **Logical Structure**: Information organized in predictable, legally-standard sections
 * - **Numbered Sections**: Clear section numbering for easy reference and citation
 * - **Contact Prominence**: Easy-to-find contact information for legal questions and notices
 * - **Print Optimization**: Clean printing layout for legal record-keeping and offline reference
 *
 * ## Legal and Business Risk Management
 * **Risk Mitigation**: Strong terms of service provide protection against:
 * - **Service Abuse**: Clear acceptable use policies and enforcement procedures
 * - **Liability Exposure**: Appropriate liability limitations and disclaimer language
 * - **IP Infringement**: Clear intellectual property protections and usage restrictions
 * - **Data Misuse**: Integration with privacy policies and data protection frameworks
 * - **Commercial Disputes**: Clear dispute resolution procedures and governing law clauses
 *
 * **Business Continuity**: Terms support business operations through:
 * - **Service Flexibility**: Terms allowing for service modifications and improvements
 * - **Scaling Support**: Terms accommodating business growth and new service offerings
 * - **Partnership Readiness**: Terms meeting partner and vendor due diligence requirements
 * - **Regulatory Adaptation**: Framework for adapting to changing regulatory environments
 * - **International Expansion**: Scalable legal framework for global market entry
 *
 * @see https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic Next.js Static Generation
 * @see https://github.com/remarkjs/react-markdown React Markdown Documentation
 * @see https://www.gov.uk/government/publications/commercial-law-guidance UK Commercial Law Guidelines
 * @see https://www.legislation.gov.uk/ukpga/2015/15/contents Consumer Rights Act 2015
 * @see https://www.w3.org/WAI/WCAG21/quickref/ WCAG 2.1 Accessibility Guidelines
 * @see {@link ../privacy/page.tsx} Privacy Policy Page
 * @see {@link ../cookies/page.tsx} Cookie Policy Page
 * @see {@link ../../customer/layout.tsx} Customer Layout with Terms Integration
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Terms of Service page establishing the legal framework governing user access and use of the EkoIntelligence ESG analysis platform with comprehensive user rights, obligations, and service terms
 * @example ```typescript
 * // Automatic rendering via Next.js App Router
 * // URL: /policy/tos
 * // Displays: Complete terms of service with legal framework
 * // Features: User agreement terms, service restrictions, liability limitations
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import Markdown from 'react-markdown'

export default function TermsOfService() {
   const  markdown=`
# Terms of Service

Last Updated: 21/11/2024

Welcome to ekoIntelligence (“we,” “us,” or “our”). These Terms of Service (“Terms”) govern your access to and use of our software, services, and platform (collectively, the “Service”). By accessing or using the Service, you agree to these Terms. If you do not agree, do not use the Service.

## 1. Acceptance of Terms

By using our Service, you confirm that:

  * You are at least 18 years old (or the age of majority in your jurisdiction).
  * You have the legal authority to agree to these Terms on behalf of yourself or your organization.

## 2. Account Registration

To access certain features of the Service, you must create an account. You agree to:

  * Provide accurate, complete, and current information.
  * Keep your login credentials secure and confidential.
  * Notify us immediately of any unauthorized use of your account.

You are responsible for all activities that occur under your account.

## 3. Use of the Service

You may use the Service only for lawful purposes and in accordance with these Terms. You agree not to:

  * Reverse-engineer, modify, or create derivative works from the Service.
  * Use the Service to send spam or other unauthorized communications.
  * Access the Service in a manner that could harm, disable, or overburden our systems.

We reserve the right to suspend or terminate your access to the Service if you violate these Terms.

## 4. Fees and Payment

If the Service is offered on a subscription or paid basis:

  * You agree to pay all fees associated with your subscription or usage.
  * Fees are non-refundable unless stated otherwise.
  * We may change our pricing with advance notice. Continued use after such changes constitutes acceptance.

## 5. Intellectual Property

The Service, including its content, features, and functionality, is owned by us or our licensors. You are granted a limited, non-exclusive, non-transferable license to use the Service solely as authorized under these Terms.

You may not:

  * Copy, distribute, or publicly display any part of the Service without permission.
  * Claim ownership of any part of the Service.

## 6. Data and Privacy

Your use of the Service is subject to our Privacy Policy. You acknowledge and agree that:

  * We may collect, process, and store data as described in our Privacy Policy.
  * You are responsible for complying with applicable data protection laws in your use of the Service.

If you integrate the Service with third-party tools, you acknowledge that we are not responsible for the privacy practices of those third parties.

## 7. Service Availability

We strive to maintain the Service’s uptime and reliability. However:

  * The Service is provided “as is” and “as available.”
  * We do not guarantee uninterrupted or error-free operation.
  * Maintenance, updates, or unforeseen circumstances may result in temporary downtime.

## 8. Limitation of Liability

To the fullest extent permitted by law:

  * We are not liable for indirect, incidental, or consequential damages arising from your use of the Service.
  * Our total liability is limited to the fees you paid to us in the 12 months preceding the event giving rise to the claim.

## 9. Termination

We may suspend or terminate your access to the Service if:

  * You violate these Terms.
  * We discontinue the Service for any reason.

Upon termination:

  * Your right to use the Service ends immediately.
  * You must delete all copies of any software or materials obtained from us.

## 10. Modifications to Terms

We may update these Terms from time to time. Changes will be effective upon posting to our website. Continued use of the Service after changes constitutes acceptance of the new Terms.

## 11. Governing Law

These Terms are governed by the laws of the United Kingdom. Any disputes arising from these Terms or the Service will be resolved exclusively in the courts of [Insert Jurisdiction].

## 12. Contact Us

If you have questions or concerns about these Terms, contact us at:

Eko Intelligence Ltd
[Insert Address]
[Insert Email Address]
[Insert Phone Number]

 `;
  return (<body className="m-8 max-w-[800px] mx-auto"><Markdown children={markdown}></Markdown></body>)
}
