/**
 * # Next.js Global Error Boundary Component for EkoIntelligence Platform
 *
 * This application-level global error boundary component serves as the ultimate safety net for the EkoIntelligence
 * ESG analysis platform's customer application. Built specifically for Next.js 15 App Router architecture, this
 * component provides comprehensive error handling for the most critical unhandled JavaScript errors that occur at
 * the root application level, including errors in the Root Layout, global middleware failures, and other high-level
 * system errors that could completely break the user experience during critical ESG data analysis workflows.
 *
 * The component implements a professional error handling strategy that balances technical error reporting with
 * enterprise-grade user experience standards required for financial services applications. It integrates with Sentry
 * for comprehensive error monitoring while providing users with clear recovery options that maintain platform reliability
 * during complex ESG analysis tasks, entity scoring operations, and document editing workflows.
 *
 * ## Core Functionality & Global Error Handling Strategy
 *
 * ### **Application-Level Error Safety Net**
 * The global error boundary component serves as the final line of defense for the EkoIntelligence platform, capturing
 * and handling all unhandled JavaScript errors that occur at the root application level and cannot be caught by
 * page-level error boundaries. This includes critical system failures in the Root Layout, global middleware errors,
 * authentication system failures, and other fundamental infrastructure errors that could completely disable the
 * platform's ESG analysis capabilities.
 *
 * ### **Critical Error Monitoring and Reporting**
 * Implements enterprise-grade error reporting strategy with both local console logging for development debugging
 * and Sentry cloud-based error monitoring for production incident tracking. The console.error() call provides
 * immediate developer feedback during development, while Sentry.captureException() ensures critical production errors
 * are tracked, aggregated, and monitored with highest priority for the operations team responsible for platform
 * reliability and financial services compliance.
 *
 * ### **Professional Global Error UI Design**
 * Features a minimalist, professional design suitable for critical error states in financial services environments.
 * The interface emphasizes clarity and immediate action options without overwhelming users during system-level failures.
 * The design maintains professional appearance while clearly communicating the severity of global-level errors and
 * providing appropriate recovery mechanisms for enterprise ESG analysis workflows.
 *
 * ## Next.js Global Error Boundary Integration
 *
 * ### **Root-Level Error Boundary Architecture**
 * Leverages Next.js 15 App Router's global error boundary system to provide automatic error catching and recovery
 * for the most critical client-side errors that occur at the root application level. The 'use client' directive
 * ensures this component runs in the browser environment where React error boundaries can effectively catch global
 * JavaScript runtime errors, Root Layout component failures, and other system-level errors that would otherwise
 * cause complete application crashes.
 *
 * ### **HTML and Body Tag Requirements**
 * Unlike page-level error boundaries, global error boundaries in Next.js App Router must include both `<html>` and
 * `<body>` tags to properly replace the entire application UI when critical errors occur. This architectural requirement
 * ensures that even errors in the Root Layout can be properly handled and presented to users with a complete,
 * functional HTML document structure that maintains accessibility and professional appearance.
 *
 * ### **Global Error Props Interface**
 * Receives standardized error props from Next.js App Router runtime for global-level errors:
 * - **error**: Error object containing message, stack trace, and optional digest for error identification
 * - **reset**: Callback function that attempts to re-render the entire application from the root level
 *
 * The error object includes a `digest` property used by Next.js for server-side error correlation and
 * error ID tracking, enabling comprehensive error tracking across both client and server boundaries in the
 * full-stack ESG analysis application with enhanced traceability for critical system failures.
 *
 * ### **Root-Level Recovery Mechanism**
 * The reset function provided by Next.js attempts to recover from global errors by re-mounting the entire
 * application component tree from the root level. This is particularly critical for ESG platform contexts
 * where fundamental system failures, authentication errors, or Root Layout issues require complete application
 * restart without requiring full page reload or user session termination, maintaining workflow continuity
 * for critical financial analysis tasks.
 *
 * ## Sentry Error Monitoring Integration
 *
 * ### **Critical Error Tracking and Alerting**
 * Integrates with Sentry cloud-based error monitoring service to provide comprehensive critical error tracking,
 * real-time alerting, and analysis capabilities for the highest severity errors. The Sentry.captureException() call
 * ensures all global-level errors are automatically reported with maximum priority including full context such as
 * user session information, browser environment details, and complete stack traces necessary for immediate incident
 * response and platform reliability maintenance.
 *
 * ### **Enhanced Error Context for Global Failures**
 * Sentry integration automatically captures comprehensive error context for global failures including:
 * - **Critical User Session Information**: User ID, organization context, and session metadata for impact analysis
 * - **System Environment Details**: Browser version, device type, and viewport information for compatibility analysis
 * - **Application State Snapshot**: React context data, global state, and route information for debugging
 * - **Infrastructure Context**: Network connectivity, API status, and system resource information
 * - **Business Impact Assessment**: Active ESG analysis sessions, critical document editing, and financial workflows
 *
 * ### **Critical Error Alerting and Response**
 * Sentry provides intelligent error grouping, real-time alerting, and incident response capabilities that enable the
 * EkoIntelligence operations team to:
 * - **Immediate Incident Response**: Real-time alerts for critical errors affecting platform availability
 * - **Business Continuity Assessment**: Rapid impact analysis for ESG analysis workflow interruptions
 * - **System Health Monitoring**: Track critical error rates and platform reliability metrics
 * - **Compliance and Audit Support**: Maintain error logs for financial services compliance requirements
 *
 * ## User Interface Design & Critical Error Experience
 *
 * ### **Professional Critical Error Design**
 * Implements a clean, professional design approach suitable for critical error states in financial services
 * applications. The interface uses standard background colors, clear typography, and professional spacing that
 * maintains visual consistency with the broader ESG analysis platform while clearly communicating the severity
 * of global-level system failures that require immediate attention.
 *
 * ### **Clear Error Communication Strategy**
 * Presents critical error information in a clear, professional manner:
 * 1. **Critical Error Indication**: AlertOctagon icon and "Critical Error" heading communicate severity appropriately
 * 2. **Professional Messaging**: Non-technical explanation suitable for business users and technical users alike
 * 3. **System Acknowledgment**: Clear indication that technical teams have been automatically notified
 * 4. **Technical Context**: Conditional display of error message and digest for debugging and support purposes
 * 5. **Recovery Actions**: Clear options for global error recovery and safe navigation
 *
 * ### **Accessibility and Enterprise Usability**
 * Designed with comprehensive accessibility support for critical error states including:
 * - **ARIA Semantics**: AlertOctagon icon marked with aria-hidden="true" to prevent screen reader duplication
 * - **Clear Visual Hierarchy**: Large, readable typography with high contrast for critical error visibility
 * - **Keyboard Navigation**: Fully keyboard-accessible buttons for global error recovery actions
 * - **Screen Reader Support**: Semantic HTML structure that provides clear context for assistive technologies
 * - **Professional Language**: Enterprise-appropriate messaging suitable for financial services users
 *
 * ## Component Architecture & Critical Dependencies
 *
 * ### **React Hooks Integration for Global Errors**
 * Utilizes React's useEffect hook for critical error processing side effects, ensuring global error reporting
 * occurs exactly once per error instance while respecting React's strict mode and concurrent rendering
 * capabilities. The effect dependency on the error object ensures proper cleanup and prevents duplicate
 * error reports during global component re-renders or recovery attempts.
 *
 * ### **UI Component Dependencies for Critical States**
 * Leverages the EkoIntelligence component library for critical error states:
 * - **Button Component**: Consistent interactive elements with professional styling and accessibility support
 * - **Lucide React Icons**: Professional icon library providing the AlertOctagon critical warning icon
 * - **Tailwind CSS Classes**: Utility-first CSS framework for responsive design and consistent spacing
 * - **Global HTML Structure**: Complete HTML document with lang attribute for accessibility compliance
 *
 * ### **Global Error Recovery Options**
 * Provides multiple recovery paths for different critical error scenarios:
 * - **Global Reset Button**: Attempts complete application recovery by re-mounting from root level
 * - **Safe Navigation Home**: Guaranteed fallback to main dashboard for complete platform restart
 * - **Error Context Display**: Optional technical details for support teams and advanced users
 * - **Error ID Tracking**: Digest display for correlation with monitoring systems and support tickets
 *
 * ## Integration with ESG Analysis Platform Architecture
 *
 * ### **Business Continuity for Critical Failures**
 * Understanding that global errors in ESG analysis workflows can have severe business impact, the component
 * is designed to minimize disruption to critical business processes and provide clear paths to recovery:
 * - **Platform Availability**: Maintains basic platform functionality even during critical system failures
 * - **Data Integrity Protection**: Ensures users understand the scope of potential data loss or corruption
 * - **Workflow Recovery**: Clear guidance for resuming interrupted ESG analysis and reporting workflows
 * - **Enterprise Communication**: Professional messaging suitable for enterprise financial services environments
 *
 * ### **Performance and Reliability for Critical States**
 * Optimized for the reliability requirements of professional financial applications during crisis scenarios:
 * - **Minimal Dependencies**: Lightweight global error boundary with minimal external dependencies
 * - **Guaranteed Functionality**: Reliable rendering even when core application systems have failed
 * - **Memory Safety**: Proper cleanup and garbage collection of error objects and global handlers
 * - **Network Resilience**: Optimized Sentry error reporting that functions even with degraded connectivity
 *
 * ## Usage Patterns & Critical Error Scenarios
 *
 * ### **Global Error Categories**
 * The component handles various critical error categories that affect the entire application:
 * - **Root Layout Failures**: Errors in the fundamental application structure or global providers
 * - **Authentication System Failures**: Critical authentication or authorization system breakdowns
 * - **Global State Corruption**: Fundamental application state errors that affect all components
 * - **Infrastructure Failures**: Network, API, or third-party service failures affecting core functionality
 * - **Memory and Resource Errors**: Critical resource exhaustion or memory allocation failures
 * - **Security Violations**: Critical security errors that require immediate application-level response
 *
 * ### **Recovery Success Patterns for Global Errors**
 * The global reset functionality is most effective for:
 * - **Transient System Issues**: Temporary infrastructure problems that resolve with application restart
 * - **Global State Recovery**: Application-level state corruption that clears with complete re-initialization
 * - **Authentication Recovery**: Authentication token or session issues that resolve with fresh initialization
 * - **Resource Cleanup**: Memory or resource allocation issues that clear with complete application reset
 *
 * ## Security and Compliance for Critical Errors
 *
 * ### **Critical Error Information Security**
 * Carefully balances error transparency with security requirements for global-level failures:
 * - **Sanitized Error Display**: Critical error messages avoid exposing sensitive system information
 * - **Controlled Technical Detail**: Error information displayed appropriately for different user contexts
 * - **User Privacy Protection**: Global error reporting respects user privacy and data protection requirements
 * - **Audit Compliance**: Critical error logging supports financial services compliance and audit requirements
 *
 * ### **Enterprise Security for Global Failures**
 * Implements security best practices for critical production financial applications:
 * - **Secure Error Messaging**: Production error messages avoid exposing implementation or infrastructure details
 * - **Rate-Limited Reporting**: Sentry error reporting includes built-in rate limiting for critical error scenarios
 * - **Data Minimization**: Critical error reports include only necessary context for incident response
 * - **Access Control**: Global error monitoring access restricted to authorized operations personnel
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/error-handling#global-errorjs Next.js Global Error Handling
 * @see https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary React Error Boundaries
 * @see https://docs.sentry.io/platforms/javascript/guides/nextjs/ Sentry Next.js Integration
 * @see https://docs.sentry.io/platforms/javascript/enriching-events/context/ Sentry Error Context
 * @see https://nextjs.org/docs/app/api-reference/file-conventions/error Next.js Error File Conventions
 * @see {@link ../components/ui/button.tsx} Button Component Documentation
 * @see {@link ./error.tsx} Page-Level Error Boundary Component
 * @see {@link ./not-found.tsx} 404 Error Page Component
 * @see {@link ./layout.tsx} Root Layout Component
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Global error boundary component for Next.js App Router providing comprehensive critical error reporting via Sentry integration and professional recovery options for application-level failures
 * @example ```tsx
 * // This component is automatically used by Next.js App Router
 * // when any critical global-level error occurs in the application
 *
 * // Critical error scenarios that trigger this component:
 * // 1. Root Layout component rendering errors or provider failures
 * // 2. Global middleware failures or authentication system errors
 * // 3. Fundamental application state corruption or context provider errors
 * // 4. Critical infrastructure errors affecting core platform functionality
 * // 5. Unhandled errors that propagate to the root application level
 *
 * // Global error recovery usage:
 * <Button onClick={reset}>
 *   Try Again
 * </Button>
 *
 * // Safe navigation fallback:
 * <Button asChild variant="outline">
 *   <a href="/">Go back home</a>
 * </Button>
 *
 * // Error context display:
 * {error.message && (
 *   <p className="mt-2 p-2 bg-muted rounded-md text-sm">
 *     Error details: {error.message}
 *   </p>
 * )}
 *
 * // Error ID for support correlation:
 * {error.digest && (
 *   <p className="mt-2 text-sm text-muted-foreground">
 *     Error ID: {error.digest}
 *   </p>
 * )}
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import * as Sentry from '@sentry/nextjs'
import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AlertOctagon } from 'lucide-react'

export default function GlobalError({
                                        error,
                                        reset,
                                    }: {
    error: Error & { digest?: string }
    reset: () => void
}) {
    useEffect(() => {
        // Log the error to an error reporting service
        console.error(error)
        Sentry.captureException(error);
    }, [error])

    return (
        <html lang="en">
        <body className="bg-background">
        <div className="min-h-screen flex items-center justify-center px-4">
            <div className="max-w-md w-full space-y-8 text-center">
                <div className="space-y-2">
                    <AlertOctagon className="mx-auto h-12 w-12 text-destructive" aria-hidden="true" />
                    <h1 className="text-4xl font-bold tracking-tight">Critical Error</h1>
                    <p className="text-xl font-semibold text-muted-foreground">We're experiencing technical difficulties</p>
                </div>
                <div className="text-muted-foreground">
                    <p>We apologize for the inconvenience. Our team has been notified and is working on resolving the issue.</p>
                    {error.message && (
                        <p className="mt-2 p-2 bg-muted rounded-md text-sm">
                            Error details: {error.message}
                        </p>
                    )}
                    {error.digest && (
                        <p className="mt-2 text-sm text-muted-foreground">
                            Error ID: {error.digest}
                        </p>
                    )}
                </div>
                <div className="pt-4 space-y-4">
                    <Button onClick={() => reset()} variant="default">
                        Try again
                    </Button>
                    <div>
                        <Button asChild variant="outline">
                            <a href="/">Go back home</a>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
        </body>
        </html>
    )
}
