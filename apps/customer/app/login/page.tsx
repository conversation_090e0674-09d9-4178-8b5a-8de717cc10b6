/**
 * # EkoIntelligence Customer Authentication Portal
 *
 * This Next.js 15 server component implements the primary user authentication interface for the
 * EkoIntelligence ESG analysis platform. The login page serves as the secure entry point for
 * customers to access ESG reporting tools, document collaboration features, and entity analysis
 * dashboards. Built using Supabase authentication with Next.js App Router server actions, this
 * component provides a seamless, secure, and visually engaging authentication experience.
 *
 * ## Core Authentication Features
 * The component implements a comprehensive authentication system:
 * 1. **Server-side Authentication**: Uses Supabase server client for secure password verification
 * 2. **Automatic Redirect Handling**: Preserves intended destination URLs through `next` parameter
 * 3. **Error Message Display**: Shows authentication failures with user-friendly error messages
 * 4. **Session State Management**: Checks existing authentication and redirects authenticated users
 * 5. **Form Security**: Server actions prevent client-side credential exposure
 *
 * ## Design System Integration
 * **Glass-morphism UI Design**: The interface exemplifies the application's sophisticated design language:
 * - **Aurora Background**: Dynamic gradient animations with brand colors for visual engagement
 * - **Responsive Layout**: Mobile-first design with adaptive layouts for desktop and mobile devices
 * - **Animated Branding**: Integrated AnimatedSymbol component for brand recognition and engagement
 * - **Consistent Typography**: Follows design system typography scales and color schemes
 * - **Accessibility Standards**: Proper form labeling, semantic HTML, and keyboard navigation support
 *
 * ## Server Action Implementation
 * **Secure Form Processing**: The `signIn` server action implements robust authentication:
 * - **Server-side Validation**: Form data extraction and validation on the server
 * - **Supabase Integration**: Uses `signInWithPassword` for secure credential verification
 * - **Error Handling**: Comprehensive error handling with appropriate user feedback
 * - **Redirect Logic**: Smart redirection based on success/failure states and intended destinations
 * - **Security Logging**: Authentication attempts logged for security monitoring
 *
 * ## User Experience Features
 * **Comprehensive Form Elements**: The interface provides all standard authentication features:
 * - **Email/Password Fields**: Standard credential input with proper input types and validation
 * - **Remember Me Option**: User preference for session persistence (UI only - backend implementation required)
 * - **Forgot Password Link**: Navigation to password reset flow for account recovery
 * - **Registration Link**: Clear path to account creation for new users
 * - **Loading States**: Visual feedback during authentication processing via SubmitButton
 * - **Error Display**: Contextual error messages based on authentication failure reasons
 *
 * ## Architecture Integration
 * **ESG Platform Context**: This component serves as the gateway to the EkoIntelligence platform:
 * - **Multi-tenant Support**: Organization-aware authentication through profiles and acc_organisations
 * - **Feature Flag Integration**: User-specific feature access through profiles.feature_flags
 * - **Document Access Control**: Authentication enables access to collaborative document editing
 * - **Analytics Integration**: Login events tracked for user analytics and security monitoring
 * - **Database Schema**: Integrates with comprehensive auth schema including users, profiles, organizations
 *
 * ## Technical Implementation Details
 * **Next.js 15 App Router**: Leverages modern Next.js features for optimal performance:
 * - **Server Components**: Reduces client-side JavaScript for faster page loads
 * - **Async/Await Patterns**: Modern asynchronous JavaScript for clean, readable code
 * - **Promise-based Props**: Uses Next.js 15 promise-based searchParams for better performance
 * - **Server Actions**: Type-safe server-side form handling without API routes
 * - **Static Analysis**: TypeScript integration for compile-time error detection
 *
 * ## Supabase Authentication Flow
 * **Comprehensive Auth Integration**: Deep integration with Supabase authentication system:
 * - **Server Client**: Uses createClient() from supabase/server for server-side auth operations
 * - **Session Management**: Automatic cookie-based session handling with secure HttpOnly cookies
 * - **User State**: Checks authentication state before rendering to prevent unnecessary renders
 * - **Database Integration**: Links to profiles table for extended user information and organization assignment
 * - **Security Policies**: Leverages Supabase Row Level Security for data protection
 * - **JWT Handling**: Transparent JWT token management for API authentication
 *
 * ## Component Dependencies
 * **UI Component Integration**: Utilizes design system components for consistency:
 * - **SubmitButton**: Custom form submission component with loading states and accessibility
 * - **AnimatedSymbol**: Brand logo component with sophisticated Framer Motion animations
 * - **AuroraBackground**: Dynamic gradient background component for visual engagement
 * - **Form Components**: Label, Input, Checkbox from shadcn/ui for consistent form styling
 * - **Navigation**: Next.js Link components for client-side navigation optimization
 *
 * ## Security Considerations
 * **Enterprise-grade Security**: Implements comprehensive security measures:
 * - **Server-side Processing**: All authentication logic executed on secure server environment
 * - **CSRF Protection**: Server actions provide built-in CSRF protection
 * - **Input Sanitization**: FormData extraction prevents injection attacks
 * - **Error Handling**: Generic error messages prevent information disclosure
 * - **Session Security**: Secure cookie handling prevents session hijacking
 * - **Audit Trail**: Authentication events logged for security monitoring and compliance
 *
 * ## Mobile and Desktop Experience
 * **Responsive Design Implementation**: Optimized for all device types:
 * - **Mobile Layout**: Compact design with prominent animated logo and streamlined form
 * - **Desktop Layout**: Split-screen design with brand presentation and welcome information
 * - **Accessibility**: Touch-friendly interface elements and keyboard navigation support
 * - **Performance**: Optimized image loading and animation performance across devices
 * - **Progressive Enhancement**: Core functionality works without JavaScript
 *
 * ## Error Handling and User Feedback
 * **Comprehensive Error Management**: Robust error handling and user communication:
 * - **Authentication Errors**: Clear messages for invalid credentials, account issues
 * - **Network Errors**: Graceful handling of connectivity issues
 * - **Validation Errors**: Client-side and server-side validation with appropriate feedback
 * - **Redirect Preservation**: Maintains intended destination URLs through authentication flow
 * - **Accessibility**: Error messages properly associated with form fields for screen readers
 *
 * ## Integration with ESG Platform Features
 * **Platform Gateway Functionality**: Connects users to core ESG analysis features:
 * - **Document Collaboration**: Access to TipTap-based collaborative document editing
 * - **Entity Analysis**: Gateway to ESG entity scoring and analysis dashboards
 * - **Claims Analysis**: Access to corporate claims vs. evidence analysis tools
 * - **Promise Tracking**: Entry point for monitoring corporate sustainability commitments
 * - **Report Generation**: Access to AI-powered ESG report generation and export features
 * - **Admin Features**: Organization management and user administration for authorized users
 *
 * @see https://nextjs.org/docs/app/building-your-application/authentication Next.js Authentication Guide
 * @see https://supabase.com/docs/guides/auth/server-side-rendering Supabase Server-side Authentication
 * @see https://supabase.com/docs/guides/auth/auth-helpers/nextjs Next.js Auth Helpers
 * @see https://react-hook-form.com/api/useformstatus React Hook Form Status
 * @see https://developer.mozilla.org/docs/Web/API/FormData FormData API Documentation
 * @see ./submit-button.tsx SubmitButton Component Implementation
 * @see ./animated-symbol.tsx AnimatedSymbol Component Documentation
 * @see ../supabase/server.ts Supabase Server Client Configuration
 * @see ../../components/ui/aurora-background.tsx Aurora Background Component
 * @see ../../database.types.ts Database Type Definitions
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Secure authentication portal for EkoIntelligence ESG platform with glass-morphism design, server-side authentication, and comprehensive user experience features
 * @example
 * ```tsx
 * // Standard login page usage (handled by Next.js routing)
 * // URL: /login
 * // Renders the complete authentication interface
 *
 * // Login with redirect destination
 * // URL: /login?next=/documents/123
 * // Redirects to specified page after successful authentication
 *
 * // Login with error message
 * // URL: /login?message=Could+not+authenticate+user
 * // Displays error message to user with retry option
 *
 * // Automatic redirect for authenticated users
 * // If user is already authenticated, automatically redirects to intended destination
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import Link from 'next/link'
import { createClient } from '@/app/supabase/server'
import { redirect } from 'next/navigation'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { SubmitButton } from '@/app/login/submit-button'
import { AuroraBackground } from '@/components/ui/aurora-background'
import { EkoLogoText } from '@utils/images'
import { AnimatedSymbol } from '@/app/login/animated-symbol'

export default async function Login(
    props: {
        searchParams: Promise<{ message: string, next?: string }>;
    }
) {
    const searchParams = await props.searchParams;

    const signIn = async (formData: FormData) => {
        "use server";

        const supabase = await createClient();
        const email = formData.get("email") as string;
        const password = formData.get("password") as string;


        const {data: user, error} = await supabase.auth.signInWithPassword({
            email,
            password,
        });

        console.log("Signed In", user)

        if (error) {
            console.error(error);
            return redirect(`/login?message=Could+not+authenticate+user&next=${searchParams?.next || "/"}`);
        }

        return redirect((searchParams?.next || "/"));
    };

    const supabase = await createClient();
    const {data: user, error} = await supabase.auth.getUser();

    if (!error && user) {
        return redirect(searchParams?.next || "/");
    }


    return (
        <div className="flex min-h-[100dvh] bg-background dark:bg-foreground dark:text-background text-background">
            <AuroraBackground
                className="flex flex-1 flex-shrink items-center justify-center bg-muted bg-zinc-200 min-w-[100dvw] lg:min-w-[350px] p-6 sm:p-10 lg:p-12 xl:p-16 bg-brand dark:bg-brand-dark text-zinc-50 dark:text-zinc-50">

                <AnimatedSymbol size={280} className={"mx-auto md:hidden"} color={"#ffffff"}/>
                <div className="mx-auto max-w-md space-y-8 pointer-events-auto">
                    <div>

                        {/*<span>{error?.message}</span>*/}
                        <h2 className="mt-6 text-3xl font-bold tracking-tight ">Sign in to
                            your
                            account</h2>
                        <p className="mt-2 text-sm">
                            Or{" "}
                            <Link href="/signup" className="font-medium  hover:text-primary/90"
                                  prefetch={false} data-testid="sign-up-link">
                                register for a new account
                            </Link>
                        </p>


                    </div>
                    <form className="space-y-6" data-testid="login-form">
                        <div>
                            <Label htmlFor="email" className="block text-sm font-medium">
                                Email
                            </Label>
                            <div className="mt-1">
                                <Input
                                    name="email"
                                    id="email"
                                    type="text"
                                    autoComplete="email"
                                    required
                                    data-testid="email-input"
                                    className="block w-full appearance-none rounded-md border border-input text-foreground bg-background px-3 py-2 placeholder-muted-foreground dark:placeholder-gray-200 shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm"
                                />
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="password" className="block text-sm font-medium">
                                Password
                            </Label>
                            <div className="mt-1">
                                <Input
                                    id="password"
                                    name="password"
                                    type="password"
                                    autoComplete="current-password"
                                    required
                                    data-testid="password-input"
                                    className="block w-full appearance-none rounded-md border border-input text-foreground bg-background dark:bg-background px-3 py-2 placeholder-muted-foreground dark:placeholder-gray-200 shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm"
                                />
                            </div>
                        </div>
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <Checkbox id="remember-me" name="remember-me" className="h-4 w-4 rounded"/>
                                <Label htmlFor="remember-me" className="ml-2 block text-sm opacity-80">
                                    Remember me
                                </Label>
                            </div>
                            <div className="text-sm">
                                <Link href="/resetpassword" className="font-medium opacity-80"
                                      prefetch={false} data-testid="forgot-password-link">
                                    Forgot your password?
                                </Link>
                            </div>
                        </div>
                        <div>
                            <SubmitButton
                                formAction={signIn}
                                className="rounded-md text-foreground mb-2"
                                pendingText="Signing In..."
                                data-testid="login-button"
                            >Sign In
                            </SubmitButton>
                        </div>
                        {searchParams?.message && (
                            <p className="mt-4 p-4 bg-foreground/10 text-foreground text-center" data-testid="login-error">
                                {searchParams.message}
                            </p>
                        )}
                    </form>
                </div>

            </AuroraBackground>
            <div
                className="hidden md:flex flex-grow bg-background text-foreground dark:text-foreground p-6 sm:p-10 lg:p-12 xl:p-16 flex-col items-start">
                <div className="mx-auto h-full max-w-md space-y-8 flex-col justify-center">
                    <Link href="/">
                        <AnimatedSymbol size={300} className={"mx-auto hidden md:block"} color="hsl(145, 27%, 45%)"/>
                        <EkoLogoText height={70} className="mx-auto mb-8 dark:hidden" ekoColor="#1f1f1f"
                                     intelligenceColor="#818181"/>
                        <EkoLogoText height={70} className="mx-auto mb-8 hidden dark:block" ekoColor="#f0f0f0"
                                     intelligenceColor="#a0a0a0"/>

                        <h2 className="mt-6 text-3xl font-bold tracking-tight ">Welcome</h2>
                        <p className="mt-2 text-sm ">
                            ekoIntelligence provides a comprehensive suite of tools to help you assess the
                            sustainability and
                            fairness of companies and funds.
                        </p>
                    </Link>
                    <div>
                        <h3 className="mt-6 text-xl font-bold tracking-tight text-foreground">Get Started</h3>
                        <p className="mt-2 text-sm text-muted-foreground">
                            Sign in to your account or register a new one to start using our platform and unlock a world
                            of
                            possibilities.
                        </p>
                        <div className="mt-4 flex gap-2">
                            {/*<Button*/}
                            {/*    variant="secondary"*/}
                            {/*    className="flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"*/}
                            {/*>*/}
                            {/*    Sign in with Google*/}
                            {/*</Button>*/}
                            {/*<Link*/}
                            {/*    href="#"*/}
                            {/*    className="inline-flex items-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm transition-colors hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"*/}
                            {/*    prefetch={false}*/}
                            {/*>*/}
                            {/*    Register*/}
                            {/*</Link>*/}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
