/**
 * # Intelligent Form Submit Button with Loading States
 *
 * This React client component provides an enhanced submit button implementation specifically designed
 * for Server Actions in Next.js 15 applications. The component intelligently manages loading states,
 * accessibility features, and user feedback during form submission processes. Built with <PERSON>act's
 * `useFormStatus` hook, it automatically detects when the associated form is being processed and
 * provides appropriate visual feedback to users.
 *
 * ## Core Functionality
 * - **Automatic Loading Detection**: Uses <PERSON>act's `useFormStatus` hook to detect form submission state
 * - **Action-Specific Matching**: Only shows loading state for the specific form action being executed
 * - **Customizable Pending Text**: Allows custom loading messages during form submission
 * - **Accessibility Integration**: Proper ARIA attributes for screen readers and assistive technologies
 * - **Glass-morphism Design**: Integrates with EkoIntelligence platform's signature glass-morphism UI
 * - **Type Safety**: Full TypeScript support with proper component prop typing
 *
 * ## Form Status Integration
 * **React useFormStatus Hook**: The component leverages React's experimental `useFormStatus` hook:
 * - **Pending State Detection**: Automatically detects when any form action is executing
 * - **Action Matching**: Compares the current action with the button's `formAction` prop
 * - **Smart State Management**: Only activates loading state for the specific form action
 * - **Server Action Compatibility**: Designed specifically for Next.js 15 Server Actions
 *
 * ## Authentication System Integration
 * **Login Page Context**: This component is primarily used in the authentication system:
 * - **Sign-in Form**: Main usage in `/login/page.tsx` for user authentication
 * - **Server Action Processing**: Handles authentication server actions with loading feedback
 * - **Error State Management**: Works with authentication error handling and retry flows
 * - **User Experience**: Prevents multiple submissions and provides clear feedback during auth
 *
 * ## Design System Integration
 * **Button Component Foundation**: Built on top of the platform's design system:
 * - **shadcn/ui Button**: Uses the standardized Button component with consistent styling
 * - **Variant Support**: Supports all button variants including 'outline' default for forms
 * - **Glass-morphism**: Integrates with the platform's glass-morphism design language
 * - **Responsive Design**: Consistent appearance across mobile and desktop form factors
 * - **Accessibility Standards**: Maintains platform accessibility standards and keyboard navigation
 *
 * ## Component Architecture
 * **Props Interface**: Extends standard button props with form-specific enhancements:
 * - **children**: Button content displayed in normal state
 * - **pendingText**: Optional custom text shown during form submission
 * - **formAction**: Server action function reference for state matching
 * - **ComponentProps<"button">**: All standard HTML button attributes supported
 * - **Type Safety**: Full TypeScript integration with proper prop validation
 *
 * ## Loading State Management
 * **Intelligent State Detection**: Advanced loading state logic:
 * - **Pending Check**: Monitors form's pending state through useFormStatus
 * - **Action Matching**: Compares current action with button's formAction prop
 * - **State Isolation**: Multiple submit buttons can coexist without state conflicts
 * - **Visual Feedback**: Clear indication when specific action is processing
 * - **Accessibility**: Proper ARIA-disabled state for assistive technologies
 *
 * ## Accessibility Features
 * **WCAG Compliance**: Comprehensive accessibility implementation:
 * - **ARIA Attributes**: `aria-disabled` attribute properly set during loading states
 * - **Screen Reader Support**: Loading text announced to screen readers
 * - **Keyboard Navigation**: Maintains keyboard accessibility during all states
 * - **Focus Management**: Proper focus handling during state transitions
 * - **Semantic HTML**: Uses semantic button element with proper type attribute
 *
 * ## Server Actions Integration
 * **Next.js 15 Server Actions**: Designed specifically for modern Next.js patterns:
 * - **Server Action Matching**: Compares formAction prop with current executing action
 * - **Type Safety**: Server action functions properly typed and validated
 * - **Error Handling**: Integrates with server action error handling patterns
 * - **Progressive Enhancement**: Works without JavaScript as standard submit button
 * - **Security**: Leverages server action CSRF protection and validation
 *
 * ## User Experience Enhancements
 * **Form Submission Feedback**: Comprehensive user experience features:
 * - **Loading Indicators**: Clear visual feedback during form processing
 * - **Prevent Double Submission**: Automatically disabled during submission
 * - **Custom Loading Messages**: Contextual messages like "Signing In..." or "Processing..."
 * - **Smooth Transitions**: Seamless state transitions without layout shifts
 * - **Error Recovery**: Graceful handling when submissions fail
 *
 * ## Platform Integration Context
 * **EkoIntelligence ESG Platform**: Integration with broader platform architecture:
 * - **Authentication System**: Primary usage in login and registration flows
 * - **Document Editor**: Used in document save and publishing workflows
 * - **Report Generation**: Handles report generation and export form submissions
 * - **Database Operations**: Provides feedback for data persistence operations
 * - **API Integration**: Works with both Server Actions and traditional API endpoints
 *
 * ## Technical Implementation
 * **React Client Component**: Modern React patterns and hooks:
 * - **"use client" directive**: Enables client-side React hooks usage
 * - **useFormStatus hook**: Experimental React hook for form state management
 * - **TypeScript Integration**: Full type safety with proper interface definitions
 * - **Component Composition**: Extends Button component with additional functionality
 * - **Props Spreading**: Maintains all standard button props and behaviors
 *
 * ## Usage Patterns
 * **Common Implementation Scenarios**:
 * - **Authentication Forms**: Login, registration, password reset submissions
 * - **Document Operations**: Save, publish, export document actions
 * - **Data Entry Forms**: Create, update, delete operations with loading feedback
 * - **Report Generation**: Long-running operations requiring user feedback
 * - **File Uploads**: Multi-step operations with progress indication
 *
 * @see https://react.dev/reference/react-dom/hooks/useFormStatus React useFormStatus Hook Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations Next.js Server Actions Guide
 * @see https://developer.mozilla.org/docs/Web/Accessibility/ARIA/Attributes/aria-disabled ARIA Disabled Attribute
 * @see https://web.dev/articles/forms-best-practices Web Forms Best Practices
 * @see ../../components/ui/button.tsx Button Component Implementation
 * @see ./page.tsx Login Page Implementation Using SubmitButton
 * @see ./actions.tsx Authentication Server Actions
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Intelligent form submit button with automatic loading states, accessibility features, and Server Action integration for Next.js 15 applications
 * @example
 * ```tsx
 * // Basic usage in authentication form
 * <SubmitButton
 *   formAction={signIn}
 *   pendingText="Signing In..."
 *   data-testid="login-button"
 * >
 *   Sign In
 * </SubmitButton>
 *
 * // Usage in document save form
 * <SubmitButton
 *   formAction={saveDocument}
 *   pendingText="Saving..."
 *   variant="outline"
 * >
 *   Save Document
 * </SubmitButton>
 *
 * // Usage with custom styling
 * <SubmitButton
 *   formAction={generateReport}
 *   pendingText="Generating Report..."
 *   className="w-full bg-brand text-white"
 * >
 *   Generate ESG Report
 * </SubmitButton>
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";

import { useFormStatus } from 'react-dom'
import { type ComponentProps } from 'react'
import { Button } from '@/components/ui/button'

type Props = ComponentProps<"button"> & {
    pendingText?: string;
};

export function SubmitButton({children, pendingText, ...props}: Props) {
    const {pending, action} = useFormStatus();

    const isPending = pending && action === props.formAction;

    return (
        <Button {...props} type="submit" aria-disabled={pending} variant="outline">
            {isPending ? pendingText : children}
        </Button>
    );
}
