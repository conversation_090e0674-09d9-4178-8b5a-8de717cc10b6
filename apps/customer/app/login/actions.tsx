/**
 * Next.js App Router Server Actions for Supabase Authentication
 *
 * This module provides server-side authentication actions using Next.js 15 App Router's server actions
 * feature combined with Supabase Auth for secure user authentication within the EkoIntelligence ESG
 * (Environmental, Social, Governance) analysis platform. Server actions enable form handling and
 * authentication processing directly on the server without requiring API routes.
 *
 * ## Core Functionality
 * - **Server-Side Form Processing**: Handles login and signup forms using Next.js server actions
 * - **Supabase Authentication Integration**: Seamless integration with Supabase Auth for user management
 * - **Secure Session Management**: Server-side session creation with automatic cookie handling
 * - **Error Handling & Redirection**: Comprehensive error logging with appropriate user redirection
 * - **Cache Revalidation**: Automatic layout cache invalidation for immediate authentication state updates
 *
 * ## Authentication Architecture
 * **Next.js 15 Server Actions**:
 * Server actions in this module run exclusively on the server, providing secure form data processing
 * without exposing sensitive authentication logic to the client. The 'use server' directive ensures
 * these functions execute in the server environment with full access to:
 * - Secure environment variables and API keys
 * - Server-side Supabase client configuration
 * - Direct database access for user authentication
 * - Server-side error logging and debugging capabilities
 *
 * **Supabase Authentication Flow**:
 * ```
 * Form Submission → Server Action → Supabase Auth → Session Creation → Redirect
 * ```
 * 1. User submits login/signup form with email and password
 * 2. Server action extracts FormData and validates input
 * 3. Supabase client processes authentication request
 * 4. Successful auth creates secure session cookies automatically
 * 5. User redirected to main application with active session
 *
 * ## Database Integration & User Management
 * **Supabase Auth Schema Integration**:
 * The authentication system integrates with multiple database layers within the EkoIntelligence platform:
 *
 * **Core Authentication Tables (Supabase Auth Schema)**:
 * - `auth.users`: Primary user account storage with email, encrypted passwords, and authentication metadata
 * - `auth.sessions`: Active session management with JWT tokens and refresh token rotation
 * - `auth.refresh_tokens`: Secure token refresh mechanism for maintaining persistent authentication
 * - `auth.identities`: OAuth provider linkage for external authentication (Google, GitHub, etc.)
 *
 * **Extended User Profile System (Customer Database)**:
 * - `public.profiles`: Extended user data including admin privileges, organization membership, and feature flags
 * - `acc_organisations`: Organizational hierarchy and team management for enterprise features
 * - Row Level Security (RLS) policies ensure users only access their authorized data and organizational context
 *
 * ## Security Implementation
 * **Authentication Security Features**:
 * - **Password Security**: Bcrypt hashing with configurable rounds handled by Supabase Auth
 * - **JWT Token Management**: Short-lived access tokens with automatic refresh token rotation
 * - **Session Security**: HTTP-only cookies with secure flag for production environments
 * - **CSRF Protection**: Server-side token validation and origin checking
 * - **Rate Limiting**: Built-in Supabase rate limiting for authentication attempts
 * - **Email Verification**: Optional email confirmation flow for enhanced security
 *
 * **Fail-Fast Error Handling**:
 * Following the project's fail-fast philosophy, authentication errors are immediately logged and result
 * in user redirection to the error page rather than silent failures or degraded experiences.
 *
 * ## Integration Points
 * **Frontend Integration**:
 * - HTML forms with progressive enhancement for JavaScript-disabled environments
 * - Client-side form validation with server-side security validation
 * - Seamless integration with Next.js App Router navigation and caching
 *
 * **Backend Integration**:
 * - Supabase client configuration for server-side authentication
 * - Integration with customer database profiles for extended user data
 * - Real-time authentication state synchronization across application components
 *
 * ## Related Components
 * - Login/signup forms in the authentication UI components
 * - User profile management system with organizational context
 * - Admin interface for user management and permissions
 * - OAuth callback handler for external authentication providers
 * - Session management and logout functionality throughout the application
 *
 * @fileoverview Server actions for authentication using Next.js App Router and Supabase Auth
 * <AUTHOR>
 * @updated 2025-07-24
 * @see {@link https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions|Next.js Server Actions Documentation}
 * @see {@link https://supabase.com/docs/guides/auth|Supabase Authentication Documentation}
 * @see {@link https://supabase.com/docs/guides/auth/server-side/nextjs|Supabase Next.js SSR Guide}
 * @copyright 2025 EkoIntelligence. All rights reserved.
 * @docgen doc-by-claude
 */

'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'

import { createClient } from '@/app/supabase/server'

/**
 * Server action for user authentication login using Supabase Auth
 *
 * This server action processes login form submissions by extracting user credentials,
 * authenticating against Supabase Auth, and establishing a secure user session. The
 * function implements the project's fail-fast error handling approach, immediately
 * redirecting users to an error page if authentication fails while logging the error
 * for debugging purposes.
 *
 * ## Authentication Process
 * 1. **Form Data Extraction**: Extracts email and password from the submitted FormData
 * 2. **Supabase Authentication**: Uses `signInWithPassword` for credential verification
 * 3. **Session Management**: Supabase automatically creates secure HTTP-only session cookies
 * 4. **Cache Revalidation**: Invalidates layout cache to reflect new authentication state
 * 5. **User Redirection**: Redirects authenticated users to the main application dashboard
 *
 * ## Security Features
 * - **Server-Side Processing**: All authentication logic runs securely on the server
 * - **Automatic Session Cookies**: Supabase handles secure cookie creation and management
 * - **Password Hashing**: Bcrypt password verification handled by Supabase Auth
 * - **Error Logging**: Authentication failures are logged with full error details
 * - **Fail-Fast Behavior**: Authentication errors immediately redirect to error page
 *
 * ## Database Integration
 * Upon successful authentication, the user's session is linked to:
 * - `auth.users` table for core authentication data
 * - `auth.sessions` table for active session management
 * - `public.profiles` table for extended user profile information
 * - Row Level Security policies for data access control based on user context
 *
 * @param {FormData} formData - Form data containing email and password fields
 * @throws {Error} Redirects to '/error' if authentication fails
 * @returns {Promise<void>} Redirects to '/' on successful authentication
 *
 * @example
 * ```tsx
 * // HTML form that calls this server action
 * <form action={login}>
 *   <input type="email" name="email" required />
 *   <input type="password" name="password" required />
 *   <button type="submit">Login</button>
 * </form>
 * ```
 *
 * @see {@link https://supabase.com/docs/reference/javascript/auth-signinwithpassword|Supabase signInWithPassword}
 * @see {@link https://nextjs.org/docs/app/api-reference/functions/revalidatePath|Next.js revalidatePath}
 * @see {@link https://nextjs.org/docs/app/api-reference/functions/redirect|Next.js redirect}
 */
export async function login(formData: FormData) {
    const supabase = await createClient();

    // type-casting here for convenience
    // in practice, you should validate your inputs
    const data = {
        email: formData.get('email') as string,
        password: formData.get('password') as string,
    }

    const {error} = await supabase.auth.signInWithPassword(data)

    if (error) {
        console.error(error);
        redirect('/error')
    }

    revalidatePath('/', 'layout')
    redirect('/')
}

/**
 * Server action for user account registration and signup using Supabase Auth
 *
 * This server action processes new user registration form submissions by extracting user
 * credentials, creating a new account through Supabase Auth, and establishing a secure user
 * session. The function follows the same fail-fast error handling approach as the login
 * function, providing consistent authentication behavior across the platform.
 *
 * ## Registration Process
 * 1. **Form Data Extraction**: Extracts email and password from the submitted FormData
 * 2. **Account Creation**: Uses Supabase `signUp` to create new user account with credential verification
 * 3. **Profile Creation**: Supabase triggers automatically create corresponding profile records
 * 4. **Session Management**: Successful signup immediately creates secure HTTP-only session cookies
 * 5. **Cache Revalidation**: Invalidates layout cache to reflect new authentication state
 * 6. **User Redirection**: Redirects new users to the main application dashboard
 *
 * ## Security Features
 * - **Server-Side Processing**: All account creation logic runs securely on the server
 * - **Password Validation**: Supabase enforces password strength requirements and hashing
 * - **Email Verification**: Optional email confirmation flow can be enabled in Supabase settings
 * - **Automatic Profile Creation**: Database triggers create extended profile records automatically
 * - **Error Logging**: Registration failures are logged with full error details for debugging
 * - **Fail-Fast Behavior**: Registration errors immediately redirect to error page
 *
 * ## Database Integration
 * Upon successful registration, the following database records are created:
 * - `auth.users` table entry with encrypted password and email verification status
 * - `public.profiles` table entry via database trigger with default values and organization assignment
 * - Initial session records in `auth.sessions` for immediate authentication
 * - Row Level Security policies automatically apply to new user context
 *
 * ## Extended Profile Management
 * The signup process integrates with the EkoIntelligence organizational system:
 * - Admin privileges are automatically assigned based on email domain (@ekointelligence.com)
 * - Default organization membership is established for enterprise users
 * - Feature flags and quota settings are initialized with default values
 * - User preferences and notification settings are set to platform defaults
 *
 * @param {FormData} formData - Form data containing email and password fields for new account
 * @throws {Error} Redirects to '/error' if account creation fails
 * @returns {Promise<void>} Redirects to '/' on successful account creation and authentication
 *
 * @example
 * ```tsx
 * // HTML form that calls this server action
 * <form action={signup}>
 *   <input type="email" name="email" required />
 *   <input type="password" name="password" required minLength={6} />
 *   <button type="submit">Create Account</button>
 * </form>
 * ```
 *
 * @see {@link https://supabase.com/docs/reference/javascript/auth-signup|Supabase signUp}
 * @see {@link https://supabase.com/docs/guides/auth/managing-user-data|Supabase User Data Management}
 * @see {@link https://nextjs.org/docs/app/api-reference/functions/revalidatePath|Next.js revalidatePath}
 * @see {@link https://nextjs.org/docs/app/api-reference/functions/redirect|Next.js redirect}
 */
export async function signup(formData: FormData) {
    const supabase = await createClient();

    // type-casting here for convenience
    // in practice, you should validate your inputs
    const data = {
        email: formData.get('email') as string,
        password: formData.get('password') as string,
    }

    const {error} = await supabase.auth.signUp(data)

    if (error) {
        console.error(error);
        redirect('/error')
    }

    revalidatePath('/', 'layout')
    redirect('/')
}
