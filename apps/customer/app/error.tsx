/**
 * # Next.js App Router Error Boundary Component for EkoIntelligence Platform
 *
 * This client-side error boundary component serves as the global error handling interface for the EkoIntelligence
 * ESG analysis platform's customer application. Built specifically for Next.js 15 App Router architecture, this
 * component provides comprehensive error reporting, user-friendly error displays, and recovery mechanisms for
 * any unhandled JavaScript errors that occur during ESG data analysis, document editing, report generation,
 * or other critical platform operations.
 *
 * The component implements a sophisticated error handling strategy that balances technical error reporting with
 * professional user experience standards required for financial services applications. It integrates with Sentry
 * for comprehensive error monitoring while providing users with clear recovery options that maintain workflow
 * continuity during complex ESG analysis tasks.
 *
 * ## Core Functionality & Error Handling Strategy
 *
 * ### **Comprehensive Error Monitoring**
 * The component serves as a critical safety net for the EkoIntelligence platform, capturing and handling all
 * unhandled JavaScript errors that occur within the Next.js App Router application context. This includes errors
 * from ESG data processing, chart rendering, document editing operations, API communication failures, and
 * third-party library integration issues that could disrupt critical financial analysis workflows.
 *
 * ### **Dual Error Reporting Pipeline**
 * Implements a comprehensive error reporting strategy with both local console logging for development debugging
 * and Sentry cloud-based error monitoring for production incident tracking. The console.error() call provides
 * immediate developer feedback during development, while Sentry.captureException() ensures production errors
 * are tracked, aggregated, and monitored for the operations team responsible for platform reliability.
 *
 * ### **Professional Error UI Design**
 * Features a glass-morphism design consistent with the EkoIntelligence platform's visual identity, presenting
 * error information in a professional manner suitable for financial services environments. The interface
 * balances transparency about errors with maintaining user confidence in the platform's reliability and
 * professional capabilities for ESG analysis and reporting.
 *
 * ## Next.js App Router Integration
 *
 * ### **Client-Side Error Boundary Architecture**
 * Leverages Next.js 15 App Router's error boundary system to provide automatic error catching and recovery
 * for client-side React component errors. The 'use client' directive ensures this component runs in the
 * browser environment where React error boundaries can effectively catch JavaScript runtime errors, component
 * lifecycle errors, and rendering failures that occur during user interactions.
 *
 * ### **Error Props Interface**
 * Receives standardized error props from Next.js App Router runtime:
 * - **error**: Error object containing message, stack trace, and optional digest for error identification
 * - **reset**: Callback function that attempts to re-render the error-causing component tree
 *
 * The error object may include a `digest` property used by Next.js for server-side error correlation,
 * enabling comprehensive error tracking across both client and server boundaries in the full-stack
 * ESG analysis application.
 *
 * ### **Recovery Mechanism Integration**
 * The reset function provided by Next.js attempts to recover from errors by re-mounting the component
 * tree that caused the error. This is particularly valuable in ESG dashboard contexts where temporary
 * data loading failures, network timeouts, or chart rendering issues can be resolved through component
 * re-initialization without requiring full page reload or user session restart.
 *
 * ## Sentry Error Monitoring Integration
 *
 * ### **Production Error Tracking**
 * Integrates with Sentry cloud-based error monitoring service to provide comprehensive production error
 * tracking, alerting, and analysis capabilities. The Sentry.captureException() call ensures all client-side
 * errors are automatically reported with full context including user session information, browser environment
 * details, and stack traces necessary for rapid incident resolution.
 *
 * ### **Error Context and Metadata**
 * Sentry integration automatically captures rich error context including:
 * - **User Session Information**: User ID, organization context, and session metadata for user impact analysis
 * - **Browser Environment**: Browser version, device type, and viewport information for compatibility tracking
 * - **Application State**: React component state, Redux/context data, and route information for debugging
 * - **Network Context**: API call status, loading states, and data synchronization information
 * - **ESG Analysis Context**: Active entities, analysis runs, and document editing sessions for business impact assessment
 *
 * ### **Error Aggregation and Alerting**
 * Sentry provides intelligent error grouping, trend analysis, and alerting capabilities that enable the
 * EkoIntelligence operations team to:
 * - **Monitor Platform Health**: Track error rates, user impact, and system reliability metrics
 * - **Prioritize Bug Fixes**: Identify high-impact errors affecting ESG analysis workflows
 * - **Performance Optimization**: Detect performance regressions and optimization opportunities
 * - **User Experience Monitoring**: Track errors that impact critical user journeys and business processes
 *
 * ## User Interface Design & Experience
 *
 * ### **Glass-Morphism Visual Design**
 * Implements the EkoIntelligence platform's signature glass-morphism design language with translucent
 * surfaces, heavily rounded corners (rounded-md styling), and professional color schemes that maintain
 * visual consistency with the broader ESG analysis platform. The design strikes a balance between
 * modern aesthetics and professional financial services UI standards.
 *
 * ### **Progressive Error Disclosure**
 * Presents error information in a layered approach:
 * 1. **Primary Message**: Clear, non-technical explanation suitable for business users
 * 2. **Context Information**: Acknowledgment that the technical team has been notified
 * 3. **Technical Details**: Conditional display of error message for users who need debugging information
 * 4. **Recovery Actions**: Clear options for error recovery and workflow continuation
 *
 * ### **Accessibility and Usability**
 * Designed with comprehensive accessibility support including:
 * - **ARIA Semantics**: AlertTriangle icon marked with aria-hidden="true" to prevent screen reader duplication
 * - **Clear Visual Hierarchy**: Large, readable typography with sufficient color contrast for visual accessibility
 * - **Keyboard Navigation**: Fully keyboard-accessible buttons for error recovery actions
 * - **Screen Reader Support**: Semantic HTML structure that provides clear error context for assistive technologies
 *
 * ## Component Architecture & Dependencies
 *
 * ### **React Hooks Integration**
 * Utilizes React's useEffect hook for error processing side effects, ensuring error reporting occurs
 * exactly once per error instance while respecting React's strict mode and concurrent rendering
 * capabilities. The effect dependency on the error object ensures proper cleanup and prevents
 * duplicate error reports during component re-renders.
 *
 * ### **UI Component Dependencies**
 * Leverages the EkoIntelligence component library:
 * - **Button Component**: Consistent interactive elements with glass-morphism styling and accessibility support
 * - **Lucide React Icons**: Professional icon library providing the AlertTriangle warning icon
 * - **Tailwind CSS Classes**: Utility-first CSS framework for responsive design and consistent spacing
 *
 * ### **Error Recovery Options**
 * Provides multiple recovery paths for different user scenarios:
 * - **Try Again Button**: Attempts error recovery by re-mounting failed components
 * - **Home Navigation**: Safe fallback to main dashboard for complete workflow restart
 * - **Technical Context**: Optional error details for technically sophisticated users
 *
 * ## Integration with ESG Analysis Platform
 *
 * ### **Business Context Integration**
 * Understanding that errors in ESG analysis workflows can have significant business impact, the component
 * is designed to minimize disruption to critical business processes including:
 * - **Report Generation**: Error recovery that preserves document editing progress where possible
 * - **Entity Analysis**: Graceful handling of data processing errors without losing analysis context
 * - **Dashboard Interactions**: Error boundaries that isolate component failures to prevent full dashboard crashes
 * - **Data Synchronization**: Error handling that maintains data integrity during sync operations
 *
 * ### **Performance and Reliability**
 * Optimized for the performance requirements of professional financial applications:
 * - **Minimal Runtime Overhead**: Lightweight error boundary with efficient error processing
 * - **Fast Error Recovery**: Quick component remounting without full application restart
 * - **Memory Management**: Proper cleanup and garbage collection of error objects and handlers
 * - **Network Efficiency**: Optimized Sentry error reporting that batches and compresses error data
 *
 * ## Usage Patterns & Error Scenarios
 *
 * ### **Typical Error Scenarios**
 * The component handles various error categories common in ESG analysis applications:
 * - **Data Processing Errors**: Failed API calls, malformed data responses, or calculation failures
 * - **Rendering Errors**: Chart library failures, large dataset rendering issues, or component lifecycle errors
 * - **Network Errors**: Connection timeouts, server errors, or data synchronization failures
 * - **Integration Errors**: Third-party service failures, authentication timeouts, or external API issues
 * - **Memory Errors**: Large dataset processing failures or memory allocation issues
 *
 * ### **Recovery Success Patterns**
 * The reset functionality is most effective for:
 * - **Temporary Network Issues**: Transient connection problems that resolve on retry
 * - **Component State Corruption**: Local state issues that clear on component remount
 * - **Race Condition Errors**: Timing-related issues that resolve with fresh component initialization
 * - **Caching Issues**: Stale data problems that clear with component refresh
 *
 * ## Security and Compliance Considerations
 *
 * ### **Error Information Security**
 * Carefully balances error transparency with security requirements:
 * - **No Sensitive Data Exposure**: Error messages are sanitized to prevent data leakage
 * - **Controlled Technical Detail**: Technical error information is conditionally displayed
 * - **User Context Protection**: Error reporting respects user privacy and data protection requirements
 * - **Audit Trail Compliance**: Error logging supports compliance and audit requirements
 *
 * ### **Production Security**
 * Implements security best practices for production financial applications:
 * - **Error Message Sanitization**: Production error messages avoid exposing implementation details
 * - **Rate Limiting**: Sentry error reporting includes built-in rate limiting to prevent abuse
 * - **Data Minimization**: Error reports include only necessary context for debugging
 * - **Access Control**: Error monitoring access is restricted to authorized technical personnel
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/error-handling Next.js App Router Error Handling
 * @see https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary React Error Boundaries
 * @see https://docs.sentry.io/platforms/javascript/guides/nextjs/ Sentry Next.js Integration
 * @see https://docs.sentry.io/platforms/javascript/enriching-events/context/ Sentry Error Context
 * @see {@link ../components/ui/button.tsx} Button Component Documentation
 * @see {@link ./global-error.tsx} Global Error Boundary for App-Level Errors
 * @see {@link ./not-found.tsx} 404 Error Page Component
 * @see {@link ../supabase/client.ts} Supabase Client Error Handling
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Client-side error boundary component for Next.js App Router providing comprehensive error reporting via Sentry integration and user-friendly recovery options in glass-morphism design
 * @example ```tsx
 * // This component is automatically used by Next.js App Router
 * // when any client-side error occurs in the application
 *
 * // Error scenarios that trigger this component:
 * // 1. React component rendering errors
 * // 2. JavaScript runtime errors in event handlers
 * // 3. Async operation failures in useEffect
 * // 4. Third-party library integration errors
 * // 5. Chart rendering failures or data processing errors
 *
 * // Manual error triggering for testing:
 * const triggerError = () => {
 *   throw new Error("Test error for error boundary");
 * };
 *
 * // Error recovery usage:
 * <Button onClick={reset}>
 *   Try Again
 * </Button>
 *
 * // Navigation fallback:
 * <Button asChild variant="outline">
 *   <a href="/">Go back home</a>
 * </Button>
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AlertTriangle } from 'lucide-react'
import * as Sentry from '@sentry/nextjs'

export default function Error({
                                  error,
                                  reset,
                              }: {
    error: Error & { digest?: string }
    reset: () => void
}) {
    useEffect(() => {
        // Log the error to an error reporting service
        console.error(error)
        Sentry.captureException(error);
    }, [error])

    return (
        <body>
        <div className="min-h-screen bg-background flex items-center justify-center px-4">
            <div className="max-w-md w-full space-y-8 text-center">
                <div className="space-y-2">
                    <AlertTriangle className="mx-auto h-12 w-12 text-destructive" aria-hidden="true" />
                    <h1 className="text-4xl font-bold tracking-tight">Oops! Something went wrong</h1>
                    <p className="text-xl font-semibold text-muted-foreground">We apologize for the inconvenience</p>
                </div>
                <div className="text-muted-foreground">
                    <p>An unexpected error has occurred. Our team has been notified and is working on a solution.</p>
                    {error.message && (
                        <p className="mt-2 p-2 bg-muted rounded-md text-sm">
                            Error details: {error.message}
                        </p>
                    )}
                </div>
                <div className="pt-4 space-y-4">
                    <Button onClick={() => reset()} variant="default">
                        Try again
                    </Button>
                    <div>
                        <Button asChild variant="outline">
                            <a href="/">Go back home</a>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
        </body>
    )
}
