/**
 * # Application Navigation Configuration and Mapping System
 *
 * This TypeScript module defines the complete navigation structure for the EkoIntelligence ESG analysis
 * platform's customer-facing application. It provides a centralized, declarative configuration system
 * for managing complex hierarchical navigation, feature flag integration, role-based access control,
 * and URL-to-title mapping throughout the Next.js 15 application.
 *
 * ## Core Functionality
 * - **Hierarchical Navigation**: Multi-level navigation structure supporting nested menu items and sub-navigation
 * - **Feature Flag Integration**: Conditional navigation visibility based on user permissions and feature availability
 * - **Role-Based Access Control**: Admin-only navigation items with proper access restrictions
 * - **URL Mapping System**: Bidirectional mapping between URLs and navigation items for dynamic title resolution
 * - **Icon System Integration**: Lucide React icons embedded throughout navigation for consistent visual design
 * - **Glass-morphism Design**: Navigation configuration supports glass-morphism UI design system with visual icons
 *
 * ## Navigation Architecture
 * **Primary Navigation Structure** (`navigationTree.navMain`):
 * - **Dashboard**: Core ESG analysis dashboard with flags, claims, promises, cherry-picking, and prediction analytics
 * - **Documents**: Document management system with creation and viewing capabilities
 * - **Analysis**: Company analysis, document analysis, and usage reporting sections
 * - **Models**: ESG framework implementations (Doughnut Economics, UN SDG, Plant Based Treaty)
 * - **Documentation**: User help system with tutorials, guides, and changelog
 * - **Admin**: Administrative functions for user management, organization control, and system configuration
 *
 * **Secondary Navigation** (`navigationTree.navSecondary`):
 * - **Support**: Customer support contact and help systems
 * - **Feedback**: User feedback collection and feature request systems
 *
 * **Account Navigation** (`navigationTree.navAccount`):
 * - **Billing**: Subscription management and payment systems
 * - **Contact**: Direct customer communication channels
 * - **Notifications**: User notification preferences and settings
 *
 * ## Feature Flag System Integration
 * **Conditional Navigation Display**: Navigation items support `requires` property for feature flag validation:
 * - **Dashboard Features**: `dashboard.flags`, `dashboard.greenwashing`, `dashboard.prediction` flags control visibility
 * - **Document Features**: `document.create`, `document.view` flags manage document access permissions
 * - **Administrative Features**: `navigation.help.docs` controls documentation access
 * - **Dynamic Filtering**: Navigation items hidden automatically when feature flags are disabled
 *
 * **Role-Based Access Control**: `adminOnly` property restricts navigation to administrative users:
 * - **User Management**: Admin-only access to user accounts, organizations, and permissions
 * - **System Configuration**: Feature flag management, virtual entities, and system messages
 * - **Analytics Access**: Administrative analytics and system health monitoring
 *
 * ## URL Mapping and Resolution System
 * **Bidirectional Navigation Maps**:
 * - **Forward Mapping** (`navigationMap`): Maps navigation titles to `NavigationItem` objects for quick lookups
 * - **Reverse Mapping** (`reverseNavigationMap`): Maps URL paths to `NavigationItem` objects for dynamic title resolution
 * - **Dynamic Title Resolution**: Used by `NavigationProvider` context for automatic page title extraction
 * - **Breadcrumb Generation**: Supports hierarchical breadcrumb construction for complex navigation paths
 *
 * **Map Construction Process**:
 * 1. **Flattening**: Hierarchical navigation structure flattened to extract all navigation items
 * 2. **Deduplication**: Ensures unique titles and URLs across navigation maps
 * 3. **URL Normalization**: Strips query parameters for consistent URL matching
 * 4. **Cross-Reference**: Both forward and reverse maps maintained for efficient lookups
 *
 * ## ESG-Specific Navigation Features
 * **Greenwashing Analysis Navigation**:
 * - **Claims Analysis**: `/customer/dashboard/gw/claims` - Corporate claim verification and tracking
 * - **Promises Tracking**: `/customer/dashboard/gw/promises` - Commitment monitoring and fulfillment analysis
 * - **Cherry Picking Detection**: `/customer/dashboard/gw/cherry` - Selective data presentation analysis
 * - **Vague Terms Analysis**: `/customer/dashboard/gw/vague` - Language ambiguity and clarity assessment
 *
 * **ESG Model Integration**:
 * - **Doughnut Economics**: `/customer/models/doughnut` - Sustainable development framework analysis
 * - **UN SDG Mapping**: `/customer/models/sdg` - United Nations Sustainable Development Goals alignment
 * - **Plant Based Treaty**: `/customer/models/plant_based_treaty` - Climate commitment framework analysis
 *
 * **Entity Analysis Workflows**:
 * - **Company Analysis**: `/customer/analysis/companies` - Corporate ESG performance assessment
 * - **Document Analysis**: `/customer/analysis/documents` - ESG document processing and insights
 * - **Usage Analytics**: `/customer/analysis/usage` - Platform usage monitoring and optimization
 *
 * ## Technical Implementation
 * **TypeScript Type Safety**:
 * - **NavigationItem Interface**: Strongly typed navigation item structure with optional properties
 * - **Icon Integration**: `LucideIcon` type ensures consistent icon usage throughout navigation
 * - **URL Validation**: String literal types for critical navigation paths
 * - **Feature Flag Types**: Typed feature flag names for compile-time validation
 *
 * **Performance Optimizations**:
 * - **Static Configuration**: Navigation structure defined at module level for optimal performance
 * - **Efficient Lookups**: Map-based data structures provide O(1) lookup performance
 * - **Lazy Loading**: Navigation items loaded on-demand for large hierarchical structures
 * - **Memory Management**: Efficient object creation and reference management
 *
 * ## Integration with Next.js App Router
 * **Route Alignment**: Navigation URLs designed to align with Next.js 15 App Router file-based routing:
 * - **Dynamic Routes**: Support for parameterized routes like `/customer/dashboard/flags/[id]`
 * - **Route Groups**: Organized route groupings for admin, customer, and public access patterns
 * - **Nested Layouts**: Navigation structure supports nested layout patterns for complex UIs
 * - **Middleware Integration**: Compatible with Next.js middleware for authentication and authorization
 *
 * **SEO and Metadata Integration**:
 * - **Dynamic Titles**: Navigation titles used for automatic page title generation
 * - **Breadcrumb Structured Data**: Navigation hierarchy supports structured data for search engines
 * - **URL Canonicalization**: Consistent URL patterns for improved search engine indexing
 * - **Social Media Metadata**: Navigation titles integrated with Open Graph and Twitter Card metadata
 *
 * ## Database and Backend Integration
 * **Feature Flag Resolution**: Navigation integrates with database-stored feature flags:
 * - **User-Level Flags**: Individual user feature flag overrides stored in `profiles.feature_flags`
 * - **Organization-Level Flags**: Organization-wide feature flags in `acc_organisations.feature_flags`
 * - **Default Flag Inheritance**: Hierarchical feature flag resolution from organization to user level
 * - **Real-time Updates**: Feature flag changes reflected immediately in navigation visibility
 *
 * **Administrative Data Integration**:
 * - **User Management**: Navigation items link to user administration interfaces with proper data binding
 * - **Organization Management**: Administrative navigation connects to organization management workflows
 * - **System Configuration**: Navigation provides access to system-level configuration and monitoring tools
 *
 * ## Related Components and Systems
 * **Navigation Context Integration**:
 * - **NavigationProvider**: Uses `reverseNavigationMap` for automatic title resolution and breadcrumb generation
 * - **Sidebar Component**: Consumes `navigationTree` for hierarchical menu rendering with feature flag filtering
 * - **Page Headers**: Integrates with navigation system for consistent page title and breadcrumb display
 * - **Admin Navigation**: Specialized admin navigation component with role-based filtering and access control
 *
 * **UI Component Integration**:
 * - **Lucide Icons**: All navigation items use consistent Lucide React icon system for visual coherence
 * - **Glass-morphism Design**: Navigation styling supports glass-morphism design system with proper visual hierarchy
 * - **Mobile Responsiveness**: Navigation structure optimized for mobile and desktop responsive design patterns
 * - **Accessibility**: Navigation items include proper ARIA labels and keyboard navigation support
 *
 * ## System Architecture Context
 * This navigation system sits at the intersection of multiple platform layers:
 * - **Frontend Layer**: Provides structure for React component navigation and routing
 * - **Authentication Layer**: Integrates with user authentication and role-based access control systems
 * - **Database Layer**: Connects to feature flag and user permission systems in Supabase
 * - **Analytics Layer**: Navigation usage tracked for user experience optimization and platform analytics
 * - **Content Management**: Navigation structure supports content management workflows and document organization
 *
 * @see https://lucide.dev/guide/packages/lucide-react Lucide React Icon Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing Next.js App Router Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security
 * @see {@link ../../../components/context/nav/nav-context.tsx} Navigation Context Provider
 * @see {@link ../layout.tsx} Customer Application Layout
 * @see {@link ../../../components/admin/AdminNavigation.tsx} Administrative Navigation System
 * @see {@link ../../../utils/feature-flags.ts} Feature Flag Management System
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Application Navigation Configuration and Mapping System for EkoIntelligence ESG Analysis Platform
 * @example
 * ```tsx
 * // Basic navigation item access
 * import { navigationTree, navigationMap, reverseNavigationMap } from './navigation';
 *
 * // Access main navigation structure
 * const dashboardItems = navigationTree.navMain.find(item => item.title === 'Dashboard')?.items;
 *
 * // Lookup navigation item by title
 * const flagsNavItem = navigationMap.get('Flags');
 *
 * // Reverse lookup navigation item by URL
 * const currentNavItem = reverseNavigationMap.get('/customer/dashboard/flags');
 *
 * // Feature flag conditional rendering
 * function NavigationMenu() {
 *   const { hasFeature } = useFeatureFlags();
 *
 *   return (
 *     <nav>
 *       {navigationTree.navMain
 *         .filter(item => !item.requires || hasFeature(item.requires))
 *         .map(item => (
 *           <NavigationLink key={item.title} item={item} />
 *         ))}
 *     </nav>
 *   );
 * }
 *
 * // Dynamic title resolution in page component
 * function PageHeader() {
 *   const pathname = usePathname();
 *   const navItem = reverseNavigationMap.get(pathname);
 *
 *   return (
 *     <header>
 *       <h1>{navItem?.title || 'EkoIntelligence'}</h1>
 *       <Breadcrumbs path={pathname} />
 *     </header>
 *   );
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import {
    BarChart2Icon,
    BookOpen,
    Building2Icon,
    CherryIcon,
    DonutIcon,
    FilePlusIcon,
    FileTextIcon,
    FlagIcon,
    Frame,
    GlobeIcon,
    LayoutDashboardIcon,
    LifeBuoy,
    LineChartIcon,
    List,
    LucideIcon,
    MapIcon,
    MessageCircleX,
    PieChart,
    Send,
    Settings,
    Unlink2Icon,
    VeganIcon,
} from 'lucide-react'

type NavigationItem = {
    title: string,
    url: string,
    icon?: LucideIcon,
    isActive?: boolean,
    items?: NavigationItem[]
    hidden?: boolean
    requires?: string // Feature flag name required to show this item
    adminOnly?: boolean // Only show to admin users
}

export const navigationTree:{[key:string]:NavigationItem[]} = {
    navMain: [
        {
            title: "Dashboard",
            url: "/customer/dashboard",
            icon: LayoutDashboardIcon,
            isActive: true,
            items: [
                {
                    title: "Dashboard",
                    url: "/customer/dashboard",
                    icon: LayoutDashboardIcon
                },

                {
                    title: "Flags",
                    url: "/customer/dashboard/flags",
                    icon: FlagIcon,
                    requires: "dashboard.flags"

                },
                {
                    title: "Cherry Picking",
                    url: "/customer/dashboard/gw/cherry",
                    icon: CherryIcon,
                    requires: "dashboard.greenwashing"

                },
                {
                    title: "Claims",
                    url: "/customer/dashboard/gw/claims",
                    icon: MessageCircleX,
                    requires: "dashboard.greenwashing"

                },
                {
                    title: "Promises",
                    url: "/customer/dashboard/gw/promises",
                    icon: Unlink2Icon,
                    requires: "dashboard.greenwashing"
                },
                {
                    title: 'Prediction',
                    url: '/customer/dashboard/prediction-v2',
                    icon: LineChartIcon,
                    requires: 'dashboard.prediction',
                },
                // {
                //     title: "Vague Terms",
                //     url: "/customer/dashboard/gw/vague",
                //     icon: MessageSquareDashedIcon
                //
                // },
            ],
        },
        {
            title: 'Documents',
            url: '/customer/documents',
            icon: FileTextIcon,
            items: [{
                title: 'Create',
                url: '/customer/documents/new',
                icon: FilePlusIcon,
                items: [],
                requires: "document.create"

            },
                {
                    title: 'View',
                    url: '/customer/documents',
                    icon: List,
                    items: [],
                    requires: "document.view"

                }],

        },

        {
            title: "Analyse",
            url: "",
            icon: PieChart,
            items: [
                {
                    title: "Company Analysis",
                    url: "/customer/analysis/companies",
                    icon: Building2Icon,
                },
                {
                    title: "Document Analysis",
                    url: "/customer/analysis/documents",
                    icon: FileTextIcon,
                    hidden: !false,
                },
                {
                    title: "Usage",
                    url: "/customer/analysis/usage",
                    icon: BarChart2Icon,
                },
            ],
        },
        {
            title: "Models",
            url: "#",
            icon: DonutIcon,
            items: [
                {
                    title: "Doughnut Economics",
                    url: "/customer/models/doughnut",
                    icon: DonutIcon,
                },
                {
                    title: "UN SDG",
                    url: "/customer/models/sdg",
                    icon: GlobeIcon,
                },
                {
                    title: "Plant Based Treaty",
                    url: "/customer/models/plant_based_treaty",
                    icon: VeganIcon
                },
            ],
        },
        {
            title: "Documentation",
            url: "/customer/help/docs",
            icon: BookOpen,
            requires: 'navigation.help.docs',
            items: [
                {
                    title: "Introduction",
                    url: "/customer/help/docs/introduction",
                },
                {
                    title: "Get Started",
                    url: "/customer/help/docs/get-started",
                },
                {
                    title: "Tutorials",
                    url: "/customer/help/docs/tutorials",
                },
                {
                    title: "Changelog",
                    url: "/customer/help/docs/changelog",
                },
            ],
        },
        {
            title: "Admin",
            url: "/admin",
            icon: Settings,
            adminOnly: true,
            items: [
                {
                    title: "Dashboard",
                    url: "/admin",
                },
                {
                    title: "Organizations",
                    url: "/admin/organizations",
                },
                {
                    title: "Users",
                    url: "/admin/users",
                },
                {
                    title: "Feature Flags",
                    url: "/admin/flags",
                },
                {
                    title: "Virtual Entities",
                    url: "/admin/entities",
                },
                {
                    title: "Messages",
                    url: "/admin/messages",
                },
                {
                    title: "Changelog",
                    url: "/admin/changelog",
                },
            ],
        },
        // {
        //     title: "Settings",
        //     url: "#",
        //     icon: Settings2,
        //     items: [
        //         {
        //             title: "General",
        //             url: "#",
        //             icon: null
        //
        //         },
        //         {
        //             title: "Team",
        //             url: "#",
        //             icon: null
        //
        //         },
        //         {
        //             title: "Usage",
        //             url: "/customer/analysis/usage",
        //             icon: null
        //
        //         },
        //         {
        //             title: "Limits",
        //             url: "#",
        //             icon: null
        //
        //         },
        //     ],
        // },
    ],
    navSecondary: [
        {
            title: "Support",
            url: "/customer/account/contact/support",
            icon: LifeBuoy,
        },
        {
            title: "Feedback",
            url: "/customer/account/contact/feedback",
            icon: Send,
        },
    ],
    navAccount: [
        {
            title: "Billing",
            url: "/customer/account/billing",
            icon: LifeBuoy,
        },
        {
            title: "Contact",
            url: "/customer/account/contact/contact",
            icon: Send,
        },
        {
            title: "Notifications",
            url: "/customer/account/notifications",
            icon: Send,
        },
    ],
    projects: [
        {
            title: "Design Engineering",
            url: "#",
            icon: Frame,
        },
        {
            title: "Sales & Marketing",
            url: "#",
            icon: PieChart,
        },
        {
            title: "Travel",
            url: "#",
            icon: MapIcon,
        },
    ],
}

export const navigationMap: Map<string, NavigationItem> = new Map<string, NavigationItem>();
navigationTree.navMain.flatMap(item=>item.items).filter(i=>!!i).forEach(item => navigationMap.set(item!.title, item!));
navigationTree.navSecondary.forEach(item => navigationMap.set(item.title, item!));
navigationTree.navAccount.forEach(item => navigationMap.set(item.title, item!));
navigationTree.projects.forEach(item => navigationMap.set(item.title, item!));
export const reverseNavigationMap: Map<string, NavigationItem> = new Map<string, NavigationItem>();
navigationTree.navMain.flatMap(item=>item.items).forEach(item => reverseNavigationMap.set(item!.url, item!));
navigationTree.navSecondary.forEach(item => reverseNavigationMap.set(item.url.split("?")[0], item!));
navigationTree.navAccount.forEach(item => reverseNavigationMap.set(item.url.split("?")[0], item!));
navigationTree.projects.forEach(item => reverseNavigationMap.set(item.url.split("?")[0], item!));
