# ESG Document Upload Module

The ESG Document Upload Module provides secure, scalable file upload capabilities for the EkoIntelligence platform, enabling users to upload ESG documents for analysis through dual methods: direct URL input and file upload. This module serves as the primary document ingestion interface for the ESG analytics platform.

## Overview

This module implements a comprehensive document upload system that bridges the gap between user document submission and backend ESG analysis processing. It provides both URL-based document processing and direct file uploads with real-time status tracking, queue management, and enterprise-grade error handling. The module integrates seamlessly with AWS S3 for secure file storage and the EkoIntelligence analytics pipeline for document processing.

## Specification

The file upload module follows a dual-path architecture:

1. **URL Upload Path**: Users provide document URLs → Backend processes via API queue → Real-time status updates
2. **File Upload Path**: Users upload files → AWS S3 presigned URLs → Direct S3 storage → Backend processing

### Core Requirements

- **Security**: User-scoped file organization with authenticated access
- **Performance**: Direct browser-to-S3 uploads eliminating server bottlenecks
- **Real-time Updates**: WebSocket-based status tracking and queue monitoring
- **Error Handling**: Comprehensive error reporting with user-friendly notifications
- **File Validation**: MIME type validation, size limits, and file type restrictions

### Processing Pipeline

```mermaid
sequenceDiagram
    participant User
    participant Component
    participant NextJS as Next.js API
    participant S3 as AWS S3
    participant Backend as Python Analytics
    participant DB as Supabase DB

    Note over User,DB: URL Upload Flow
    User->>Component: Enter URL + Select Entity
    Component->>NextJS: callBackofficeAsync("single_url")
    NextJS->>DB: Create api_queue entry
    Component->>Backend: Real-time listener setup
    Backend->>Backend: Process URL document
    Backend->>DB: Update api_queue status
    DB->>Component: Real-time status update
    Component->>User: Toast notification

    Note over User,DB: File Upload Flow
    User->>Component: Select/Drop files
    Component->>NextJS: Request presigned URL
    NextJS->>S3: Generate presigned URL
    NextJS->>Component: Return presigned URL
    Component->>S3: Direct file upload
    Component->>Component: Generate S3 URL
    Component->>Component: Call uploadUrl() with S3 URL
```

## Key Components

### DocumentUpload Component (`document-upload.tsx`)
**Role**: Main upload interface providing dual upload methods
- **URL Input Processing**: Text input with validation for document URLs
- **File Upload Integration**: Embeds FileUpload component for drag-and-drop uploads
- **Entity Association**: EntitySelector integration for document-entity linking
- **Real-time Tracking**: Supabase WebSocket subscriptions for processing status
- **Queue Management**: Display of recent upload attempts with status indicators
- **Known Critical Bug**: URL upload functionality is broken - `clickHandler()` extracts URL but never calls `uploadUrl()` function

### FileUpload Component (`file-upload.tsx`)
**Role**: Secure file upload with AWS S3 integration
- **Drag & Drop Interface**: React Dropzone-powered file selection
- **AWS S3 Integration**: Presigned URL-based direct uploads
- **File Validation**: PDF-focused with 100MB size limit and MIME type checking
- **Multi-File Support**: Bulk upload capability (up to 100 files)
- **Glass-morphism UI**: Consistent with application design system
- **Progress Feedback**: Real-time upload status and user notifications

### Upload Action (`upload-action.tsx`)
**Role**: Next.js server action for AWS S3 presigned URL generation
- **Presigned URL Generation**: Secure, time-limited AWS S3 upload URLs (1-hour expiry)
- **User-Scoped Storage**: Organizes files with `{userId}/{fileName}` structure
- **Security Controls**: IAM-based access with authenticated presigned URL generation
- **Scalable Architecture**: Direct browser-to-S3 uploads bypass server bottlenecks
- **Content-Type Enforcement**: Validates and enforces MIME type handling

## Dependencies

### Internal Dependencies
- **Entity Selector**: `@/components/entity-selector` - Entity selection dropdown
- **File Uploader Components**: `@/components/extension/file-uploader` - Custom drag-and-drop components
- **Auth Context**: `@/components/context/auth/auth-context` - User authentication
- **Backend Integration**: `@/components/backoffice` - API queue communication
- **Toast System**: `@/hooks/use-toast` - User notifications
- **Supabase Client**: `@/app/supabase/client` - Database and real-time subscriptions
- **UI Components**: Shadcn/UI components with glass-morphism styling

### External Dependencies
- **React Dropzone**: Advanced drag-and-drop file selection with validation
- **AWS SDK v3**: S3 client for presigned URL generation (`@aws-sdk/client-s3`, `@aws-sdk/s3-request-presigner`)
- **Next.js 15**: App Router with server actions for backend functionality
- **Supabase**: Real-time database client for queue management and authentication
- **Lucide React**: Icon library for UI elements (Link, Paperclip icons)

### Backend Services
- **AWS S3**: Document storage bucket (`eko-analysis-cus-document-upload`, eu-west-2 region)
- **Python Analytics Backend**: ESG document processing pipeline
- **Supabase Database**: Customer database with `api_queue` table for request tracking

## Usage Examples

### Basic Document Upload Integration
```tsx
import { DocumentUpload } from '@/app/customer/files/document-upload';

function DocumentManagementPage() {
  const handleUploadComplete = (response: any) => {
    console.log('Document processed successfully:', response);
    // Refresh document list, update analytics dashboard
  };

  const handleStatusChange = (status: string, payload: any, error: any) => {
    console.log(`Processing Status: ${status}`, payload, error);
    // Update progress indicators, handle errors
  };

  return (
    <DocumentUpload 
      onComplete={handleUploadComplete}
      onChange={handleStatusChange}
    />
  );
}
```

### File Upload Component Usage
```tsx
import { FileUpload } from '@/app/customer/files/file-upload';

function FileUploadSection() {
  return (
    <FileUpload 
      onComplete={(urls: string[]) => {
        console.log('Files uploaded to S3:', urls);
        // Process uploaded file URLs for ESG analysis
        urls.forEach(url => processDocumentUrl(url));
      }} 
    />
  );
}
```

### Server Action Integration
```typescript
// Usage in upload component
const uploadUrl = await presignedUrl(file.name, file.type, currentUser.id);
await fetch(uploadUrl, {
  method: 'PUT',
  body: file,
  headers: { 'Content-Type': file.type }
});
```

## Architecture Notes

### Security Model
- **User Authentication**: All operations require authenticated Supabase session
- **Row Level Security**: Database RLS policies ensure user data isolation
- **File Organization**: User-scoped S3 storage prevents cross-user access
- **Time-Limited Access**: 1-hour presigned URL expiry minimizes security exposure
- **Input Validation**: Client-side validation with server-side verification

### Performance Characteristics
- **Direct S3 Uploads**: Eliminates server bandwidth bottlenecks
- **Concurrent Processing**: Multiple file uploads limited only by browser/S3 limits
- **Efficient Queue Management**: Displays only 5 most recent requests per user
- **Memory Management**: Proper cleanup of Supabase subscriptions and event listeners
- **Real-time Updates**: WebSocket-based status monitoring with selective filtering

### Integration Points
```mermaid
graph TB
    A[Document Upload UI] --> B[Entity Selection]
    A --> C[URL Processing]
    A --> D[File Upload]
    
    C --> E[API Queue]
    D --> F[AWS S3]
    
    E --> G[Python Backend]
    F --> H[S3 Storage]
    
    G --> I[ESG Analytics]
    I --> J[Customer Database Sync]
    
    K[Real-time Updates] --> A
    E --> K
    G --> K
```

## Known Issues

### Critical Issues
1. **URL Upload Non-functional** (High Priority)
   - **Description**: `clickHandler()` function in `document-upload.tsx` extracts URL from input but never calls `uploadUrl()` function
   - **Impact**: URL upload workflow completely broken, preventing document processing via URL
   - **Location**: Lines 352-370 in `document-upload.tsx`
   - **Fix Required**: Add `uploadUrl(url)` call after URL extraction and validation

2. **Input State Management**
   - **Description**: URL input field not cleared after successful submission
   - **Impact**: User confusion about submission status
   - **Solution**: Clear input field after successful `uploadUrl()` execution

### Minor Issues
- **Unused File State**: `files` state declared but not utilized in URL upload workflow
- **Status Text Updates**: Upload status text not properly managed during URL processing
- **Toast Notification Timing**: Multiple simultaneous toasts during batch uploads

## Future Work

### Short-term Enhancements (Next Sprint)
- **Fix URL Upload Bug**: Implement missing `uploadUrl()` call in `clickHandler()` 
- **Input Validation**: Enhanced URL format validation with real-time feedback
- **Error Recovery**: Improved retry mechanisms for failed uploads
- **Status Indicators**: Enhanced visual feedback for upload progress

### Medium-term Features (Next Quarter)
- **Batch URL Processing**: Support for multiple URL uploads in single request
- **Upload Resumption**: Ability to resume interrupted uploads
- **Advanced File Validation**: OCR-based content validation for uploaded documents
- **Drag-and-Drop URLs**: Direct URL drag-and-drop support from browsers

### Long-term Vision (6+ Months)
- **Real-time Collaboration**: Multi-user document upload sessions
- **Advanced Analytics**: Upload success rates and performance metrics
- **Integration Expansion**: Support for Google Drive, Dropbox, and other cloud storage
- **AI-Powered Preprocessing**: Automatic document classification and metadata extraction

## Troubleshooting

### Common Issues

**URL Upload Not Working**
```bash
# Symptoms: No processing starts when clicking URL upload button
# Cause: Missing uploadUrl() call in clickHandler()
# Solution: Fix clickHandler() implementation (see Known Issues)
```

**File Upload Fails**
```bash
# Check AWS credentials and bucket permissions
# Verify presigned URL generation is working
# Check browser console for network errors
# Ensure file meets size/type restrictions (PDF, <100MB)
```

**Real-time Updates Not Working**
```bash
# Check Supabase WebSocket connection
# Verify user authentication status
# Check RLS policies on api_queue table
# Monitor network tab for WebSocket traffic
```

**S3 Upload Errors**
```bash
# Verify AWS credentials in environment variables
# Check S3 bucket CORS configuration
# Ensure bucket exists in eu-west-2 region
# Validate file content-type headers
```

### Development Debugging

**Enable Debug Logging**
```typescript
// Add to upload components for detailed logging
console.log('Upload status:', status, payload, error);
console.log('Supabase subscription:', subscription);
console.log('AWS presigned URL:', presignedUrl);
```

**Database Queries**
```sql
-- Check api_queue entries for user
SELECT * FROM api_queue 
WHERE requester = 'user-id' 
ORDER BY created_at DESC 
LIMIT 10;

-- Monitor real-time subscriptions
SELECT * FROM pg_stat_subscription;
```

## FAQ

### User-Centric Questions

**Q: Why is my URL upload not working?**
A: There's currently a known bug where URL uploads don't process. The system accepts the URL but doesn't start processing. This is a critical issue being addressed in the next release.

**Q: What file types are supported?**
A: Currently, the system supports PDF files up to 100MB in size. Support for additional document formats is planned for future releases.

**Q: How long does document processing take?**
A: Processing time varies by document size and complexity, typically 2-5 minutes for standard ESG reports. You'll receive real-time status updates throughout the process.

**Q: Can I upload multiple files at once?**
A: Yes, the file upload component supports up to 100 files simultaneously via drag-and-drop interface.

**Q: Why do I need to select an entity before uploading?**
A: Document uploads must be associated with an ESG entity for proper analysis and categorization. This ensures your documents are processed within the correct business context.

**Q: Are my uploaded documents secure?**
A: Yes, all documents are stored in user-scoped AWS S3 storage with authenticated access. Only you and authorized system processes can access your uploaded documents.

### Technical Questions

**Q: How are presigned URLs secured?**
A: Presigned URLs expire after 1 hour and are scoped to specific user IDs. They're generated server-side with proper IAM credentials and include content-type validation.

**Q: What happens if my upload is interrupted?**
A: Currently, interrupted uploads must be restarted. Upload resumption capability is planned for a future release.

**Q: How can I monitor upload progress?**
A: The system provides real-time status updates via WebSocket connections and toast notifications. The upload queue shows the status of your recent submissions.

## References

### Documentation Links
- [React Dropzone Documentation](https://react-dropzone.js.org/) - File drag-and-drop functionality
- [AWS S3 Presigned URLs](https://docs.aws.amazon.com/AmazonS3/latest/userguide/PresignedUrlUploadObject.html) - S3 upload security
- [Next.js 15 App Router](https://nextjs.org/docs/app/building-your-application/routing/pages) - Server actions and routing
- [Supabase Real-time](https://supabase.com/docs/guides/realtime) - WebSocket subscriptions
- [AWS SDK v3 Documentation](https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/) - S3 client integration

### Related Code Files
- [`/components/entity-selector.tsx`](../../components/entity-selector.tsx) - Entity selection component
- [`/components/extension/file-uploader`](../../components/extension/file-uploader) - Custom file upload components
- [`/components/backoffice/index.ts`](../../components/backoffice/index.ts) - Backend API communication
- [`/app/supabase/client.ts`](../../app/supabase/client.ts) - Supabase client configuration
- [`/types/index.ts`](../../types/index.ts) - TypeScript type definitions

### Related URLs
- [AWS S3 Request Presigner Package](https://www.npmjs.com/package/@aws-sdk/s3-request-presigner) - NPM package documentation
- [Supabase Row Level Security](https://supabase.com/docs/guides/auth/row-level-security) - Database security model
- [Next.js Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations) - Server-side functionality

### Related README Files
- [`../../README.md`](../../README.md) - Customer application overview
- [`../documents/README.md`](../documents/README.md) - Document management system
- [`../dashboard/README.md`](../dashboard/README.md) - Main dashboard functionality

## Changelog

---

### 2025-07-31
- **Created comprehensive README documentation** for ESG Document Upload Module
- Documented all three components with detailed functionality descriptions
- Added architecture diagrams and workflow specifications
- Identified and documented critical URL upload bug requiring immediate attention
- Provided troubleshooting guide and FAQ section
- Included usage examples and integration patterns
- Added future work roadmap tied to development priorities

---

(c) All rights reserved ekoIntelligence 2025