/**
 * Next.js Server Action for AWS S3 Presigned URL Generation - Secure File Upload Infrastructure
 *
 * This Next.js server action provides secure, scalable file upload capabilities for the EkoIntelligence
 * ESG analysis platform by generating AWS S3 presigned URLs. Built with AWS SDK v3 and Next.js 15 server
 * actions, it enables authenticated users to upload documents directly to AWS S3 without exposing
 * AWS credentials or requiring server-side file handling. The action implements user-scoped storage
 * organization and secure, time-limited upload permissions for enterprise document management.
 *
 * ## Core Functionality
 * - **Presigned URL Generation**: Creates secure, time-limited AWS S3 upload URLs using `@aws-sdk/s3-request-presigner`
 * - **Direct S3 Upload**: Enables browser-to-S3 uploads bypassing application server for improved performance
 * - **User-Scoped Storage**: Organizes uploaded files with user-specific prefixes for access control and organization
 * - **Security Controls**: 1-hour URL expiry, authenticated access, and user-specific storage partitioning
 * - **Content-Type Enforcement**: Validates and enforces proper MIME type handling during upload process
 * - **Scalable Architecture**: Eliminates server-side file processing bottlenecks through direct cloud uploads
 *
 * ## AWS S3 Integration Architecture
 * **Storage Configuration**:
 * - **Target Bucket**: `eko-analysis-cus-document-upload` (customer document storage bucket in eu-west-2)
 * - **Key Structure**: `{userId}/{fileName}` providing user-scoped file organization and conflict prevention
 * - **Region**: eu-west-2 (Europe - London) for optimal performance and data sovereignty compliance
 * - **Security Model**: IAM-based access with environment variable credentials and presigned URL time limits
 * - **Upload Permissions**: PutObjectCommand with 1-hour expiration for secure, temporary upload access
 *
 * ## Server Action Workflow
 * ```
 * Client Request → Server Action Execution → AWS SDK Integration → Presigned URL Generation → Client Response
 *                                     ↓
 *                              S3Client Configuration
 *                                     ↓
 *                              PutObjectCommand Creation
 *                                     ↓
 *                              getSignedUrl (1-hour expiry)
 *                                     ↓
 *                              Secure Upload URL Return
 * ```
 *
 * ## Request Parameters
 * - **fileName** (required, string): Name of the file to be uploaded, used in S3 key construction
 * - **fileType** (required, string): MIME type of the file for proper Content-Type header setting
 * - **userId** (required, string): Authenticated user identifier for user-scoped storage organization
 *
 * ## Response Format
 * Returns a presigned URL string that clients can use for direct S3 uploads:
 * ```javascript
 * const presignedUrl = await presignedUrl(fileName, fileType, userId);
 * // Returns: "https://s3.eu-west-2.amazonaws.com/eko-analysis-cus-document-upload/..."
 * ```
 *
 * ## Security Model
 * **Authentication & Authorization**:
 * - Server action execution requires authenticated Next.js session context
 * - User-scoped file organization prevents cross-user access to uploaded documents
 * - Time-limited URLs (1-hour expiry) minimize exposure window for unauthorized access
 * - Content-Type validation ensures proper file handling and prevents MIME-type attacks
 * - AWS IAM policies restrict bucket access to authorized application instances only
 *
 * ## Integration with File Upload System
 * This server action integrates with the broader EkoIntelligence file upload ecosystem:
 * - **Client Component**: Works with `file-upload.tsx` for drag-and-drop upload interface
 * - **S3 Client**: Utilizes shared S3Client configuration from `@utils/aws/aws` with standardized credentials
 * - **Upload Flow**: Supports bulk uploads through individual presigned URL generation per file
 * - **Error Handling**: Client-side error management through upload component toast notifications
 * - **File Management**: Uploaded files are organized for subsequent ESG analysis processing workflows
 *
 * ## Performance Characteristics
 * **Scalability Benefits**:
 * - Direct browser-to-S3 uploads eliminate server bandwidth bottlenecks
 * - Presigned URL generation is lightweight and fast (sub-100ms typical response time)
 * - No server-side file storage or processing required, reducing infrastructure costs
 * - Supports concurrent uploads limited only by browser and S3 service limits
 * - User-scoped storage enables efficient file organization at scale
 *
 * ## Error Handling & Monitoring
 * **Potential Failure Modes**:
 * - AWS credential configuration errors (missing environment variables)
 * - S3 service availability or network connectivity issues
 * - Invalid file parameters or unsupported content types
 * - URL generation failures or AWS SDK errors
 * - Client-side upload failures after URL generation (handled by upload component)
 *
 * ## Environment Dependencies
 * **Required Environment Variables**:
 * - `AWS_ACCESS_KEY_ID`: AWS IAM access key for S3 service authentication
 * - `AWS_SECRET_ACCESS_KEY`: AWS IAM secret key for S3 service authentication
 * - Region configuration: eu-west-2 (hardcoded in S3Client configuration)
 *
 * @see https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/client/s3/command/PutObjectCommand/ AWS S3 PutObjectCommand Documentation
 * @see https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/Package/-aws-sdk-s3-request-presigner/ AWS S3 Request Presigner Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations Next.js Server Actions Documentation
 * @see {@link ../file-upload.tsx} File Upload Component Integration
 * @see {@link ../../../packages/utils/src/aws/aws.ts} Shared S3Client Configuration
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Next.js server action for generating AWS S3 presigned URLs for secure, direct file uploads to customer document storage bucket
 * @example
 * ```typescript
 * // Client-side usage in upload component
 * const uploadUrl = await presignedUrl(file.name, file.type, currentUser.id);
 * await fetch(uploadUrl, {
 *   method: 'PUT',
 *   body: file,
 *   headers: { 'Content-Type': file.type }
 * });
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use server"
// api/generate-presigned-url.js
import {PutObjectCommand} from '@aws-sdk/client-s3';
import {getSignedUrl} from "@aws-sdk/s3-request-presigner";
import {s3} from "@utils/aws/aws";


export default async function presignedUrl(fileName:string, fileType:string, userId:string) {


    const params = {
        Bucket: "eko-analysis-cus-document-upload",
        Key: userId+"/"+fileName,
        ContentType: fileType,
    };

     // URL expires in 1 hour
    let url = await getSignedUrl(s3 as any, new PutObjectCommand(params) as any, {expiresIn: 3600});
    console.log(url);
    return url;

}
