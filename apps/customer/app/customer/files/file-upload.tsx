/**
 * ESG Document File Upload Component - AWS S3 Presigned URL Upload Interface
 *
 * This React component provides secure, user-friendly file upload functionality for the EkoIntelligence
 * ESG analysis platform. Built with React Dropzone integration and AWS S3 presigned URLs, it enables
 * authenticated users to upload PDF documents for ESG analysis processing. The component implements
 * a glass-morphism design with drag-and-drop support, real-time upload progress, and comprehensive
 * error handling for enterprise document management workflows.
 *
 * ## Core Functionality
 * - **Secure File Upload**: AWS S3 presigned URL generation for secure, direct-to-cloud uploads
 * - **PDF Document Focus**: Optimized for PDF document uploads with 100MB file size limit
 * - **Drag & Drop Interface**: Interactive file drop zone with visual feedback and status indicators
 * - **Multi-File Support**: Bulk upload capability supporting up to 100 files simultaneously
 * - **Authentication Integration**: User-scoped uploads with authenticated access control
 * - **Real-time Feedback**: Toast notifications, upload progress, and error handling
 * - **File Validation**: MIME type validation, file size checking, and duplicate prevention
 *
 * ## Upload Architecture
 * **Secure Upload Flow**:
 * ```
 * User Selects Files → File Validation → Presigned URL Generation → Direct S3 Upload → Success Callback
 *                                      ↓
 *                               Server Action (upload-action.tsx)
 *                                      ↓
 *                               AWS S3 Signed URL (1 hour expiry)
 *                                      ↓
 *                               Direct Browser → S3 Upload
 * ```
 *
 * **File Processing Pipeline**:
 * 1. **Client-Side Validation**: File type, size, and quantity validation using React Dropzone
 * 2. **Presigned URL Generation**: Server-side AWS S3 URL generation with user-specific prefixes
 * 3. **Direct Upload**: Browser-native fetch() API for direct S3 upload bypassing application server
 * 4. **Success Handling**: File URL generation and completion callback execution
 * 5. **Error Management**: Comprehensive error catching with user-friendly toast notifications
 *
 * ## AWS S3 Integration
 * **Storage Configuration**:
 * - **Bucket**: `eko-analysis-cus-document-upload` (customer document storage)
 * - **Key Structure**: `{userId}/{timestamp}-{filename}` for organized, conflict-free storage
 * - **Access Control**: User-scoped uploads with authenticated presigned URL generation
 * - **File URL Pattern**: `https://s3.eu-west-2.amazonaws.com/eko-analysis-cus-document-upload/{userId}/{filename}`
 * - **Security**: 1-hour URL expiry, authenticated access, and user-specific prefixes
 *
 * ## Component Integration
 * **File Uploader Ecosystem**: This component leverages EkoIntelligence's custom file uploader components:
 * - **FileUploader**: Main container with dropzone state management and keyboard navigation
 * - **FileInput**: Drag-and-drop zone with visual feedback and MIME type acceptance
 * - **FileUploaderContent**: File list container with responsive layout and overflow handling
 * - **FileUploaderItem**: Individual file display with remove functionality and accessibility
 *
 * **Design System Integration**:
 * - **Glass-morphism UI**: Translucent containers with backdrop blur and rounded corners
 * - **Interactive States**: Hover effects, drag feedback, and upload status visualization
 * - **Responsive Layout**: Optimized for desktop and mobile document upload workflows
 * - **Accessibility**: Full keyboard navigation, screen reader support, and ARIA attributes
 *
 * ## Authentication and Security
 * **User Authentication**: Integrated with EkoIntelligence's authentication system:
 * - **Auth Context**: `useAuth()` hook provides authenticated user context and session management
 * - **User ID Scoping**: All uploads are scoped to the authenticated user's ID for security
 * - **Session Validation**: Automatic authentication state checking with conditional rendering
 * - **Supabase Integration**: Database client access for potential metadata storage and tracking
 *
 * ## Error Handling and User Experience
 * **Comprehensive Error Management**:
 * - **File Validation Errors**: Size limits, MIME type restrictions, and quantity constraints
 * - **Upload Failures**: Network errors, S3 access issues, and timeout handling
 * - **User Feedback**: Toast notifications with descriptive error messages and success confirmations
 * - **Graceful Degradation**: Component gracefully handles authentication failures and API errors
 *
 * **Upload Status Management**:
 * - **Progress Indication**: Upload button state changes during processing ("Upload Files" → "Uploading...")
 * - **File Management**: Selected files display with remove functionality and visual feedback
 * - **Completion Handling**: Automatic file list clearing and success callback execution
 *
 * ## Configuration and Customization
 * **Dropzone Configuration** (`dropzone` constant):
 * - **File Types**: PDF only (`application/pdf: [".pdf"]`)
 * - **Upload Limits**: 100 files maximum, 100MB per file limit
 * - **Multiple Selection**: Enabled for bulk document processing workflows
 * - **Drag Behavior**: Full drag-and-drop support with visual feedback
 *
 * ## Props and Callbacks
 * **Component Interface**:
 * - **onComplete**: Callback function executed after successful upload with array of S3 URLs
 * - **Return Type**: `string[]` containing direct S3 access URLs for uploaded files
 * - **Integration**: Designed for integration with document management and ESG analysis workflows
 *
 * ## Technical Dependencies
 * - **React Dropzone**: Advanced file drop zone with validation and state management
 * - **AWS SDK**: S3 presigned URL generation and secure upload handling
 * - **Supabase**: Database client for authentication and potential metadata storage
 * - **Lucide React**: Icon library for consistent UI iconography (Paperclip icons)
 * - **Toast System**: User notification system for upload feedback and error communication
 *
 * ## System Architecture Context
 * This component fits within EkoIntelligence's broader document processing pipeline:
 * - **Document Upload**: Initial stage for ESG document ingestion and processing
 * - **Analysis Pipeline**: Uploaded documents feed into Python-based ESG analysis workflows
 * - **Customer Database**: File metadata and references stored in customer-facing database
 * - **Analytics Integration**: Documents become source material for claims, promises, and effect flag analysis
 *
 * @see https://react-dropzone.js.org/ React Dropzone Documentation
 * @see https://docs.aws.amazon.com/AmazonS3/latest/userguide/PresignedUrlUploadObject.html AWS S3 Presigned URLs
 * @see https://supabase.com/docs/guides/auth Supabase Authentication
 * @see {@link ./upload-action.tsx} AWS S3 Presigned URL Generation
 * @see {@link @/components/extension/file-uploader} Custom File Uploader Components
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This component provides secure AWS S3 file upload functionality with drag-and-drop interface for ESG document processing workflows.
 * @example
 * ```tsx
 * <FileUpload onComplete={(urls) => {
 *   console.log('Uploaded files:', urls);
 *   // Process uploaded file URLs for ESG analysis
 * }} />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import {useState} from "react";
import {FileInput, FileUploader, FileUploaderContent, FileUploaderItem} from "@/components/extension/file-uploader";
import {Button} from "@/components/ui/button";
import {DropzoneOptions} from "react-dropzone";
import {Paperclip} from "lucide-react";
import presignedUrl from "./upload-action";
import {runAsync} from "@utils/react-utils";
import {createClient} from "@/app/supabase/client";
import {useAuth} from "@/components/context/auth/auth-context";
import {useToast} from "@/hooks/use-toast";

;

const dropzone = {
    accept: {
        "application/pdf": [".pdf"],
    },
    multiple: true,
    maxFiles: 100,
    maxSize: 100 * 1024 * 1024,
} satisfies DropzoneOptions;


const FileSvgDraw = () => {
    return (
        <>
            <svg
                className="w-8 h-8 mb-3 text-zinc-500 dark:text-zinc-400"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 20 16"
            >
                <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                />
            </svg>
            <p className="mb-1 text-sm text-zinc-500 dark:text-zinc-400">
                <span className="font-semibold">Click to upload</span>
                &nbsp; or drag and drop
            </p>
            <p className="text-xs text-zinc-500 dark:text-zinc-400">
                SVG, PNG, JPG or GIF
            </p>
        </>
    );
};

export function FileUpload({onComplete}:{onComplete: (response: string[]) => any}) {
    const [files, setFiles] = useState<File[] | null>([]);
    const supabase = createClient();
    const [uploadStatus, setUploadStatus] = useState<string | null>("Upload Files");
    const auth= useAuth();
    const {toast} = useToast();


    async function uploadFiles() {
        // upload files
        if (!files) {
            toast({description:"No files selected", variant: 'destructive'});
            return;
        }


        runAsync(async () => {
            setUploadStatus("Uploading...");

            const urls = []
            for (const file of files) {
                const filename = Date.now() + "-" + file.name;
                const url = await presignedUrl(filename, file.type, auth.user?.id!);
                console.log(url);
                const uploadResponse = await fetch(url, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': file.type,
                    },
                    body: file,
                });
                urls.push({
                    filename: filename,
                    type: file.type,
                    url: `https://s3.eu-west-2.amazonaws.com/eko-analysis-cus-document-upload/${auth.user?.id!}/${filename}`
                })
            }
            try {

                toast({description: "Files uploaded successfully"});
                onComplete(urls.map((url) => url.url));
            } catch (error) {
                toast({description: "Failed to upload file", variant: "destructive"});
                console.error(error);
            } finally {
                setUploadStatus("Upload Files");
            }
            setFiles([]);
        });


    }

    if(!auth) return null;

    return (<div className="my-8">
        <FileUploader
            value={files}
            onValueChange={setFiles}
            dropzoneOptions={dropzone}
            className="relative min-w-full"
        >
            <FileInput className="border border-dashed border-zinc-500">
                <div className="flex items-center justify-center flex-col pt-3 pb-4 w-full ">
                    <FileSvgDraw/>
                </div>
            </FileInput>
            <FileUploaderContent className=" min-w-full">
                {files &&
                    files.length > 0 &&
                    files.map((file, i) => (
                        <FileUploaderItem key={i} index={i}>
                            <Paperclip className="h-4 w-4 stroke-current"/>
                            <span>{file.name}</span>
                        </FileUploaderItem>
                    ))}
                <div className="mt-4">
                    {((files && files.length > 0) || uploadStatus?.startsWith("Uploading")) &&
                        <Button onClick={() => uploadFiles()}
                                disabled={uploadStatus?.startsWith("Uploading")}>{uploadStatus}</Button>}
                </div>
            </FileUploaderContent>
        </FileUploader>

    </div>);
}
