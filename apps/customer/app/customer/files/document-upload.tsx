/**
 * # Document Upload Component for ESG Analysis Platform
 *
 * A comprehensive React component that enables users to upload ESG (Environmental, Social, Governance) documents
 * for analysis through dual upload methods: direct URL input and file upload. This component serves as the primary
 * document ingestion interface for the ekoIntelligence platform, handling both user-provided URLs and direct file
 * uploads with real-time processing status tracking, queue management, and enterprise-grade error handling for
 * sophisticated ESG analytics workflows.
 *
 * ## Core Functionality
 *
 * ### **Document Upload Methods**
 * - **URL Input Processing**: Users can paste document URLs for automated processing by the backend ESG analysis pipeline
 * - **File Upload Integration**: Direct file upload capabilities via integrated `FileUpload` component for PDF documents
 * - **Entity Association**: Links uploaded documents to specific ESG entities through `EntitySelector` component integration
 * - **Real-time Processing**: Tracks document processing status through Supabase real-time subscriptions and WebSocket connections
 * - **Queue Monitoring**: Displays live processing queue with comprehensive status updates, error handling, and completion tracking
 *
 * ### **Processing Pipeline Integration**
 * - **Backend Communication**: Uses `callBackofficeAsync` to submit processing requests to the Python analytics backend
 * - **Request Tracking**: Creates entries in `api_queue` table with `single_url` action type for URL processing workflows
 * - **Status Monitoring**: Real-time listeners track processing through `pending` → `processing` → `completed`/`error` state transitions
 * - **Error Handling**: Comprehensive error reporting with user-friendly toast notifications and detailed error messaging
 * - **Progress Feedback**: Multi-stage progress updates from initial submission through final completion with user guidance
 *
 * ## Next.js 15 App Router Architecture
 *
 * ### **Client Component Design**
 * Uses `'use client'` directive to enable advanced client-side capabilities:
 * - **Interactive State Management**: Upload status tracking, queue monitoring, and real-time UI updates
 * - **Real-time Supabase Subscriptions**: WebSocket-based processing status updates and queue state synchronization
 * - **User Input Handling**: URL entry validation, entity selection, and form interaction management
 * - **Toast Notifications**: Non-blocking status updates and error communication system
 * - **Router Integration**: Query parameter handling for entity pre-selection and deep linking support
 *
 * ### **Component Props Interface**
 * ```typescript
 * interface DocumentUploadProps {
 *   onComplete: (response: any) => any;    // Callback fired when upload completes successfully
 *   onChange?: (status: string, payload: any, error: any) => void;  // Optional status change callback for external monitoring
 * }
 * ```
 *
 * ## Database Integration
 *
 * ### **Supabase Real-time Features**
 * **Primary Table**: `api_queue` - Central request tracking and status management
 * ```sql
 * -- Core API queue fields used by this component
 * id: bigint PRIMARY KEY,                    -- Unique request identifier for tracking
 * request_action: text NOT NULL,            -- Always 'single_url' for this component's requests
 * request_data: jsonb,                      -- Contains {url: string, entity: string} payload
 * response_data: jsonb,                     -- Backend processing results or detailed error information
 * status: api_request_status,               -- 'pending' | 'processing' | 'completed' | 'failed'
 * requester: uuid,                          -- Authenticated user ID (auto-populated via RLS)
 * created_at: timestamp,                    -- Request submission timestamp
 * message: text                             -- Status messages and error descriptions
 * ```
 *
 * ### **Real-time Subscription Management**
 * - **Request Listeners**: Individual WebSocket listeners per upload request for precise status tracking
 * - **Queue Updates**: Batch updates to display recent processing history (last 5 requests per user)
 * - **User Filtering**: All database queries filtered by authenticated user ID for secure data isolation
 * - **Connection Cleanup**: Proper subscription cleanup on component unmount to prevent memory leaks
 * - **Error Recovery**: Automatic reconnection handling for network interruptions and connection failures
 *
 * ## Authentication & Security
 *
 * ### **Supabase Authentication Integration**
 * - **User Context**: Requires authenticated user from `useAuth` context for all operations
 * - **Request Authorization**: All API queue entries automatically associated with authenticated user via RLS
 * - **Row Level Security**: Database RLS policies ensure users only access their own requests and data
 * - **Entity Validation**: Selected entities validated against user's accessible entity list from `view_my_companies`
 * - **Input Sanitization**: URL format validation and entity selection validation with error feedback
 *
 * ### **Security Considerations**
 * - **Access Control**: RLS policies prevent unauthorized access to other users' upload queues and processing history
 * - **Input Validation**: Client-side URL format validation and server-side entity existence verification
 * - **Error Handling**: Secure error messages that don't leak system information or internal implementation details
 * - **Resource Protection**: Rate limiting and quota enforcement at backend level for abuse prevention
 *
 * ## User Interface Components
 *
 * ### **Layout Structure**
 * - **Entity Selector**: Dropdown component for choosing the target ESG entity from user's accessible entities
 * - **URL Input Section**: Input field with integrated upload button and real-time validation feedback
 * - **File Upload Area**: Drag-and-drop file upload component integration with S3 presigned URL support
 * - **Processing Queue**: Responsive table displaying recent upload attempts with comprehensive status indicators
 * - **Status Indicators**: Visual feedback system for upload progress, completion states, and error conditions
 *
 * ### **Interactive Elements**
 * - **Entity Selection**: Real-time filtering of available entities based on user permissions and access rights
 * - **URL Validation**: Client-side URL format validation with immediate error feedback and user guidance
 * - **Upload Button**: Contextual state management (enabled/disabled based on input validity and processing status)
 * - **Queue Table**: Live-updating table with sortable columns, status icons, and error message display
 * - **Toast Notifications**: Non-blocking status updates for upload progress, completion, and error communication
 *
 * ## State Management
 *
 * ### **Component State Architecture**
 * ```typescript
 * const [files, setFiles] = useState<File[] | null>([]);           // File upload state (legacy/unused in URL workflow)
 * const [uploadStatus, setUploadStatus] = useState<string | null>("Upload Files");  // Current upload status text display
 * const [listeners, setListeners] = useState<any[]>([]);          // Active Supabase listeners for cleanup management
 * const [company, setCompany] = useState<string | undefined>();   // Selected entity ID from EntitySelector component
 * const [apiQueue, setApiQueue] = useState<APIQueueType[]>([]);   // Recent upload queue history for display
 * ```
 *
 * ### **URL Parameters Integration**
 * - **Entity Pre-selection**: Reads `entity` query parameter for default entity selection on component load
 * - **Deep Linking**: Supports direct links with pre-populated entity selection for improved user experience
 * - **State Persistence**: Maintains entity selection across component re-renders and navigation events
 *
 * ## Performance Optimizations
 *
 * ### **Efficient Data Loading**
 * - **Limited Queue History**: Displays only 5 most recent requests per user for optimal performance and UI clarity
 * - **Selective Updates**: Real-time listeners target specific user requests only to minimize unnecessary updates
 * - **Debounced Inputs**: URL input validation with appropriate timing to balance responsiveness with performance
 * - **Memory Management**: Proper cleanup of Supabase subscriptions and event listeners to prevent memory leaks
 *
 * ### **Error Handling Strategy**
 * - **Network Resilience**: Automatic retry mechanisms for transient failures and connection issues
 * - **User Feedback**: Clear error messages distinguishing between client-side and server-side issues
 * - **Graceful Degradation**: Component remains functional even with partial feature failures or service outages
 * - **Recovery Guidance**: Actionable error messages with specific retry instructions and troubleshooting steps
 *
 * ## Dependencies & Integration
 *
 * ### **Core Dependencies**
 * - **React 18**: Modern React with hooks (`useState`, `useEffect`, `useRef`) for state management and lifecycle handling
 * - **Next.js 15**: App Router with `useSearchParams` for URL parameter handling and routing integration
 * - **Supabase**: Real-time database client for queue management, authentication, and WebSocket subscriptions
 * - **TypeScript**: Full type safety with generated database types (`APIQueueType`) for robust development experience
 *
 * ### **Internal Components**
 * - **EntitySelector**: Entity selection dropdown with user permission filtering and real-time data loading
 * - **FileUpload**: Drag-and-drop file upload component with AWS S3 integration and presigned URL support
 * - **Toast System**: User notification system for status updates, error messages, and success confirmations
 * - **UI Components**: Shadcn/UI components (Button, Input, Card, Table) with glass-morphism styling integration
 *
 * ### **Utility Functions**
 * - **`runAsync`**: Error-safe async operation wrapper from `@utils/react-utils` for robust error handling
 * - **`callBackofficeAsync`**: Backend API communication via `@/components/backoffice` for request submission
 * - **`backOfficeRequestListener`**: Real-time status monitoring setup with WebSocket subscription management
 * - **`conciseDateTime`**: Relative timestamp formatting from `@utils/date-utils` for user-friendly time display
 *
 * ## System Architecture Context
 *
 * ### **ESG Analysis Platform Integration**
 * This component serves as a critical entry point in the broader EkoIntelligence ESG analysis ecosystem:
 * - **Document Ingestion**: Initial stage for ESG document collection and preprocessing workflows
 * - **Analytics Pipeline**: Uploaded documents feed into Python-based ESG analysis, NLP processing, and ML workflows
 * - **Customer Database**: File metadata and processing status stored in customer-facing Supabase database
 * - **Backend Analytics**: Documents become source material for claims analysis, promise tracking, and effect flag generation
 * - **Scoring Systems**: Processed documents contribute to entity risk scoring and greenwashing detection algorithms
 *
 * ### **Data Flow Architecture**
 * ```
 * User Upload Request → Component Validation → API Queue Entry → Backend Processing → Real-time Status Updates → Completion Callback
 *                                                     ↓
 *                                           Python Analytics Backend
 *                                                     ↓
 *                                           Document Processing Pipeline
 *                                                     ↓
 *                                           ESG Analysis & Scoring
 *                                                     ↓
 *                                           Customer Database Sync
 * ```
 *
 * ## Usage Examples
 *
 * ### **Basic URL Upload Workflow**
 * ```typescript
 * // Component automatically handles the complete upload workflow:
 * // 1. User selects target ESG entity via EntitySelector dropdown
 * // 2. User enters document URL in input field with real-time validation
 * // 3. User clicks upload button to initiate processing request
 * // 4. Component submits request to backend via callBackofficeAsync
 * // 5. Real-time listener tracks processing status with WebSocket updates
 * // 6. Toast notifications provide immediate user feedback and progress updates
 * // 7. onComplete callback fired when processing finishes with results
 * ```
 *
 * ### **File Upload Integration**
 * ```typescript
 * // FileUpload component integration for direct file processing:
 * <FileUpload onComplete={(urls: string[]) => 
 *   urls.forEach(url => uploadUrl(url))  // Process each uploaded file URL through analytics pipeline
 * } />
 * ```
 *
 * ### **Parent Component Integration**
 * ```typescript
 * <DocumentUpload 
 *   onComplete={(response) => {
 *     // Handle successful upload completion and processing results
 *     console.log('Document processed:', response);
 *     // Refresh document list, update dashboard, trigger re-analysis, etc.
 *   }}
 *   onChange={(status, payload, error) => {
 *     // Optional: Handle real-time status changes during processing
 *     console.log(`Processing Status: ${status}`, payload, error);
 *     // Update parent component state, show progress indicators, etc.
 *   }}
 * />
 * ```
 *
 * ## Known Issues & Limitations
 *
 * ### **Critical Bug Identified**
 * - **URL Upload Non-functional**: `clickHandler()` function extracts URL from input but never calls `uploadUrl()` function - lines 311-329 require immediate fix
 * - **Missing Functionality**: URL input workflow is completely broken, preventing users from uploading documents via URL
 * - **User Impact**: Users receive no feedback when URL upload fails silently, creating poor user experience
 *
 * ### **Additional Issues**
 * - **Unused File State**: `files` state is declared but not utilized in URL upload workflow, creating code confusion
 * - **Status Text Management**: Upload status text not properly updated during URL processing workflows
 * - **Input Clearing**: URL input field not cleared after successful submission, causing user confusion
 *
 * ### **Future Enhancements**
 * - **Batch URL Processing**: Support for multiple URL uploads in single request for improved efficiency
 * - **Progress Indicators**: Detailed progress bars for long-running processing tasks with time estimates
 * - **Upload History**: Extended history view with search, filtering, and advanced queue management capabilities
 * - **Drag-and-Drop URLs**: Direct URL drag-and-drop support for improved user experience and workflow efficiency
 * - **Upload Resumption**: Ability to resume interrupted uploads and recover from network failures
 *
 * ## Related Components
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages Next.js 15 App Router Documentation
 * @see https://supabase.com/docs/guides/realtime Supabase Real-time Documentation
 * @see https://react.dev/reference/react/hooks React 18 Hooks Documentation
 * @see {@link FileUpload} - Integrated file upload component for direct file handling with S3 support
 * @see {@link EntitySelector} - Entity selection component for document association and user permission filtering
 * @see {@link /components/backoffice} - Backend communication utilities for API queue management
 * @see {@link /types#APIQueueType} - Type definitions for API queue structure and status management
 *
 * <AUTHOR>
 * @updated 2025-07-25
 * @description ESG document upload component with dual upload methods (URL/file), real-time processing tracking, and comprehensive error handling for enterprise ESG analysis workflows.
 * @example
 * ```tsx
 * // Basic usage in document management interface
 * function DocumentManagementPage() {
 *   const handleUploadComplete = (response: any) => {
 *     console.log('Document processed successfully:', response);
 *     // Refresh document list, update analytics dashboard, trigger notifications
 *   };
 *
 *   const handleStatusChange = (status: string, payload: any, error: any) => {
 *     if (status === 'processing') {
 *       console.log('Document processing in progress...');
 *       // Show loading indicators, update progress bars
 *     }
 *   };
 *
 *   return (
 *     <DocumentUpload 
 *       onComplete={handleUploadComplete}
 *       onChange={handleStatusChange}
 *     />
 *   );
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";
import React, { useEffect, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { createClient } from '@/app/supabase/client'
import { runAsync } from '@utils/react-utils'
import { useSearchParams } from 'next/navigation'
import { backOfficeRequestListener, callBackofficeAsync } from '@/components/backoffice'
import { useToast } from '@/hooks/use-toast'
import { Link } from 'lucide-react'
import { FileUpload } from '@/app/customer/files/file-upload'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { APIQueueType } from '@/types'
import { conciseDateTime } from '@utils/date-utils'
import { useAuth } from '@/components/context/auth/auth-context'
import { EntitySelector } from '@/components/entity-selector'

export function DocumentUpload({onComplete, onChange}: {
    onComplete: (response: any) => any,
    onChange?: ((status: string, payload: any, error: any) => void) | undefined
}) {
    const [files, setFiles] = useState<File[] | null>([]);
    const supabase = createClient();
    const [uploadStatus, setUploadStatus] = useState<string | null>("Upload Files");
    const urlRef = useRef<HTMLInputElement>(null);
    const entityRef = useRef<HTMLInputElement>(null);
    const queryParams = useSearchParams();
    const [listeners, setListeners] = useState<any[]>([]);
    const [company, setCompany] = useState<string | undefined>(queryParams.get("entity") || undefined);
    const {toast} = useToast();
    const [apiQueue, setApiQueue] = useState<APIQueueType[]>([]);
    const auth= useAuth();

    async function updateQueue(userId: string) {
        const {
            data: queue,
            error: queueError
        } = await supabase.from('api_queue').select().eq("requester", userId).eq("request_action", "single_url").order("created_at", {ascending: false}).limit(5)
        setApiQueue(queue as APIQueueType[]);
    }

    useEffect(() => {
        runAsync(async () => {
            const userId = auth.user?.id;
            if (!userId) return;
            await updateQueue(userId);

        });
        return () => {
            listeners.forEach((listener) => listener.unsubscribe());
        }
    }, []);


    async function uploadUrl(url: string) {
        const id = await callBackofficeAsync(supabase, "single_url", {
            "url": url,
            "entity": company
        });

        const listener = backOfficeRequestListener(supabase, auth.user?.id!, id, (status, payload, error, message) => {
            updateQueue(auth.user?.id!);
            if (onChange) {
                onChange(status, payload, error);
            }
            if (status === 'completed') {
                toast({description: `Document processed successfully`});
                listener.unsubscribe();
                setListeners(listeners.filter((l) => l !== listener));
                onComplete(payload);
            } else if (status === 'error') {
                toast({description: "Failed to upload file", variant: "destructive"});
                listener.unsubscribe();
                setListeners(listeners.filter((l) => l !== listener));
            } else if (status === 'processing') {
                toast({description: "Processing file now, this may take a few minutes"});
            }
        });
        await updateQueue(auth.user?.id!);

        setListeners([...listeners, listener]);
        toast({description: `URL submitted successfully`});
    }

    async function clickHandler() {

        try {
            if (urlRef?.current?.value && company) {
                const url= urlRef?.current?.value;

            } else {
                toast({description: "Please select a company and enter a URL", variant: "destructive"});
                return;
            }
        } catch (error) {
            toast({description: "Failed to upload file", variant: "destructive"});
            console.error(error);
        } finally {
            setUploadStatus("Upload Files");
        }


    }


    return (<div className=" ">
        <EntitySelector defaultEntity={queryParams.get("entity") || undefined} onChange={(value) => setCompany(value)}/>
        <div className="flex flex-row mt-4">
            <Input ref={urlRef} placeholder="Enter the URL of a report to upload"/><Button
            onClick={clickHandler}> <Link className="h-4 w-4 mr-2"/> Upload</Button>
        </div>

        <FileUpload onComplete={(urls: string[]) => urls.forEach(url => uploadUrl(url))}/>


        <Card>
            <CardHeader>
                <CardTitle>Analysis Queue</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="border-b">
                        <tr className="bg-zinc-50">
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">URL</th>
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">Entity</th>
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">Status</th>
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">Message</th>
                            <th className="px-4 py-2 text-left font-medium text-zinc-500">Date</th>
                        </tr>
                        </thead>
                        <tbody className="divide-y">
                        {apiQueue.map((analysis) => (
                                <tr key={analysis.id} className="hover:bg-zinc-50">
                                    <td className="px-4 py-2">{(analysis.request_data as any)?.url}</td>
                                    <td className="px-4 py-2">{(analysis.request_data as any)?.entity}</td>
                                    <td className="px-4 py-2">{analysis.status}</td>
                                    <td className="px-4 py-2">{!!(analysis.response_data as any)?.error ? (analysis.response_data as any)["error"] : ""}</td>
                                    <td className="px-4 py-2">{conciseDateTime(new Date(analysis.created_at!), Date.now())}</td>

                                </tr>
                            )
                        )}
                        </tbody>
                    </table>
                </div>
            </CardContent>
        </Card>
    </div>);
}
