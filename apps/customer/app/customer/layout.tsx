/**
 * # Customer Application Layout for EkoIntelligence ESG Analysis Platform
 *
 * This comprehensive layout component serves as the main authentication gateway and navigation shell for
 * the EkoIntelligence customer application. It provides secure user authentication, role-based access
 * control, and a sophisticated glass-morphism design system while integrating advanced ESG analysis
 * capabilities including AI-powered insights, collaborative document editing, and real-time data
 * synchronization across the platform's dual-database architecture.
 *
 * ## Core Functionality
 * - **Multi-Stage Authentication**: Server-side session validation with automatic login redirects
 * - **Admin Role Detection**: Dynamic admin privilege checking for enhanced navigation options
 * - **Context Providers**: Hierarchical authentication and entity context management
 * - **Glass-Morphism UI**: Sophisticated translucent design with gradient backgrounds and rounded elements
 * - **Responsive Navigation**: Collapsible sidebar with intelligent state persistence and entity filtering
 * - **AI Disclaimer Integration**: Comprehensive AI transparency footer with user-controlled dismissal
 * - **Real-Time Notifications**: Toast system integration for backend communication and user feedback
 *
 * ## Authentication & Authorization Architecture
 * **Secure Multi-Layer Access Control**: This layout implements comprehensive authentication and
 * authorization systems that protect customer data while enabling seamless user experience:
 *
 * **Server-Side Authentication Pipeline**:
 * 1. **Supabase Client Creation**: Initializes server-side Supabase client with cookie-based session handling
 * 2. **Session Validation**: Calls `supabase.auth.getUser()` to verify active authentication session
 * 3. **Authentication Redirect**: Redirects to `/login?next=/customer` if no valid user session exists
 * 4. **Admin Privilege Check**: Uses `checkAdmin()` utility to determine admin status via email domain validation
 * 5. **Context Establishment**: Provides authenticated user context to all child components via AuthProvider
 *
 * **Authorization Security Model**:
 * - Email-based admin detection (`@ekointelligence.com` domain validation)
 * - Row Level Security (RLS) integration through Supabase authentication context
 * - Real-time session management with automatic refresh capabilities
 * - Fail-safe design: defaults to restricted access when authorization checks fail
 *
 * ## Database Integration & Architecture
 * **Dual-Database ESG Analysis System**: The platform operates on a sophisticated two-database
 * architecture optimized for ESG data processing and customer application performance:
 *
 * **Analytics Database (Primary Processing)**:
 * - Advanced ESG statement extraction and analysis pipelines
 * - LLM-powered greenwashing detection and claims verification
 * - Entity relationship mapping and corporate hierarchy analysis
 * - Bayesian risk scoring with fail-fast error handling
 * - Complex analysis workflows including DEMISE model calculations
 *
 * **Customer Database (Application Layer)**:
 * - User authentication and profile management via Supabase Auth
 * - Document collaboration and version control systems
 * - Real-time presence and collaboration features
 * - Synchronized ESG data through `xfer_*` tables for optimized customer queries
 * - RLS policies ensuring secure multi-tenant data access
 *
 * **Data Synchronization Layer**:
 * - Automated sync between analytics and customer databases via `xfer_` tables
 * - Real-time updates for claims, promises, entities, and scores
 * - Optimized queries for customer-facing performance while maintaining data integrity
 * - Background processing isolation preventing customer application performance impact
 *
 * ## Glass-Morphism Design System
 * **Sophisticated Visual Identity**: The layout implements EkoIntelligence's signature glass-morphism
 * design language that combines modern aesthetics with functional user experience:
 *
 * **Visual Design Elements**:
 * - **Gradient Backgrounds**: Brand/brand-contrast gradient creating depth and visual hierarchy
 * - **Translucent Glass Effects**: Backdrop blur with reduced opacity for modern, professional appearance
 * - **Heavily Rounded Elements**: 1.5rem border radius standard creating approachable, modern aesthetic
 * - **Subtle Animation Systems**: Hover states and transitions enhancing user interaction feedback
 * - **Responsive Design Patterns**: Mobile-first approach with adaptive layouts and touch-friendly controls
 *
 * **Background Pattern Implementation**:
 * - **Light Mode**: Subtle radial gradient patterns with low opacity for texture without distraction
 * - **Dark Mode**: Animated dot patterns with gentle movement creating dynamic yet subtle visual interest
 * - **Pattern Layering**: Multiple gradient layers creating depth while maintaining readability
 * - **Performance Optimization**: CSS-only animations avoiding JavaScript overhead
 *
 * ## Navigation & User Experience
 * **Intelligent Sidebar System**: The `SidebarWithSelectors` component provides advanced navigation
 * capabilities tailored to ESG analysis workflows:
 *
 * **Navigation Features**:
 * - **Entity Context Filtering**: URL parameters automatically filter navigation based on selected entities
 * - **Feature Flag Integration**: Dynamic menu items based on user feature access and admin privileges
 * - **State Persistence**: localStorage-based sidebar collapse states for consistent user experience
 * - **Real-Time Notifications**: Integrated toast system for backend job completion and system messages
 * - **User Profile Management**: Dropdown with account, billing, usage, and notification access
 * - **Admin Detection**: Enhanced navigation options for users with administrative privileges
 *
 * **Responsive Design Patterns**:
 * - **Mobile-First Approach**: Collapsible navigation optimized for mobile and tablet usage
 * - **Touch-Friendly Interactions**: Appropriately sized touch targets and gesture-friendly interfaces
 * - **Accessibility Compliance**: ARIA labels, keyboard navigation, and screen reader compatibility
 * - **Performance Optimization**: Lazy loading and efficient rendering for complex navigation trees
 *
 * ## Context Management Architecture
 * **Hierarchical Provider System**: The layout establishes multiple context layers that provide
 * comprehensive application state management:
 *
 * **AuthProvider Context Layer**:
 * - User authentication state and profile information
 * - Feature flag access control and permission management
 * - Real-time session updates and automatic refresh handling
 * - Integration with backend API authentication for secure data access
 *
 * **EntityProvider Context Layer**:
 * - Selected entity context for ESG analysis workflows
 * - Query parameter management and URL state synchronization
 * - Entity filtering for navigation and data views
 * - Cross-component entity state sharing for consistent user experience
 *
 * ## AI Integration & Transparency
 * **Comprehensive AI Disclosure System**: The platform implements transparent AI usage disclosure
 * through the integrated `AIDisclaimerFooter` component:
 *
 * **AI Transparency Features**:
 * - **Prominent Disclosure**: Glass-morphism styled disclaimer explaining AI analysis limitations
 * - **User Control**: Dismissible interface with localStorage persistence for user preference management
 * - **Comprehensive Warnings**: Detailed explanation of AI-generated content limitations and verification recommendations
 * - **Smart Footer Integration**: Maintains access to disclaimer information through footer link system
 * - **Legal Compliance**: Professional disclaimer language meeting transparency and legal requirements
 *
 * ## Performance & Loading Optimization
 * **Advanced Loading States**: The layout implements sophisticated loading and suspense patterns
 * optimized for complex ESG data analysis workflows:
 *
 * **Loading State Management**:
 * - **Suspense Boundaries**: Strategic placement preventing render blocking during data fetching
 * - **Skeleton Loading**: Consistent placeholder patterns maintaining layout stability
 * - **Progressive Enhancement**: Core functionality available immediately with enhanced features loading progressively
 * - **Error Boundaries**: Graceful degradation when components fail to load or render
 *
 * ## Real-Time Communication Integration
 * **Backend Communication System**: Integrated toast notification system for real-time communication
 * with the Python analytics backend:
 *
 * **Notification Features**:
 * - **Job Completion Alerts**: Real-time notifications when ESG analysis jobs complete
 * - **Error Communication**: User-friendly error messages from backend processing failures
 * - **Progress Updates**: Status updates for long-running analysis operations
 * - **System Messages**: Administrative communications and platform updates
 *
 * ## System Architecture Integration
 * This layout serves as the central hub for the EkoIntelligence ecosystem, coordinating:
 * - **Frontend Application**: React/TypeScript customer interface with modern UI/UX patterns
 * - **Backend Analytics**: Python-based ESG analysis engine with LLM integration
 * - **Database Layer**: Dual PostgreSQL architecture with Supabase real-time capabilities
 * - **Authentication System**: Secure user management with role-based access control
 * - **Document Collaboration**: TipTap-based rich text editing with real-time collaboration
 * - **AI Transparency**: Comprehensive disclosure and user education about AI limitations
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/layouts-and-templates Next.js Layout Documentation
 * @see https://supabase.com/docs/guides/auth/server-side Supabase Server-Side Authentication
 * @see https://supabase.com/docs/guides/auth/row-level-security Row Level Security Documentation
 * @see https://tailwindcss.com/docs/backdrop-blur Tailwind Backdrop Blur for Glass-Morphism
 * @see /Users/<USER>/worktrees/279/apps/customer/app/customer/sidebar.tsx Sidebar Navigation Component
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/auth/auth-context.tsx Authentication Context Provider
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/entity-context.tsx Entity Context Provider
 * @see /Users/<USER>/worktrees/279/apps/customer/components/ai-disclaimer-footer.tsx AI Disclaimer Footer Component
 * @see /Users/<USER>/worktrees/279/apps/customer/app/auth-utils.ts Authentication Utilities
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Comprehensive customer application layout with authentication, glass-morphism design, and ESG analysis integration
 * @example
 * ```typescript
 * // Layout automatically applied to /customer/* routes
 * // URL: /customer/entities/AAPL/claims
 * // Layout: app/customer/layout.tsx (this file)
 * // Page: app/customer/entities/[id]/claims/page.tsx
 * 
 * // Server-side authentication and authorization
 * const supabase = await createClient();
 * const user = await supabase.auth.getUser();
 * if (!user) redirect('/login?next=/customer');
 * const admin = await checkAdmin();
 * 
 * // Hierarchical context providers with glass-morphism design
 * return (
 *   <AuthProvider>
 *     <EntityProvider>
 *       <div className="glass-morphism-background">
 *         <SidebarWithSelectors admin={admin}>
 *           <main className="responsive-layout">
 *             {children} // Customer page content
 *           </main>
 *           <AIDisclaimerFooter />
 *         </SidebarWithSelectors>
 *       </div>
 *     </EntityProvider>
 *   </AuthProvider>
 * );
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import '../globals.css'
import { Inter } from 'next/font/google'
import Link from 'next/link'
import { createClient } from '@/app/supabase/server'
import { SidebarWithSelectors } from '@/app/customer/sidebar'
import { Suspense } from 'react'
import { checkAdmin } from '@/app/auth-utils'
import { Skeleton } from '@/components/ui/skeleton'
import { Toaster } from '@/components/ui/toaster'
import { redirect } from 'next/navigation'
import { AuthProvider } from '@/components/context/auth/auth-context'
import { EntityProvider } from '@/components/context/entity/entity-context'
import { AIDisclaimerFooter } from '@/components/ai-disclaimer-footer'

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
})
const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000'


export default async function Layout({
                                       children,
                                     }: {
  children: React.ReactNode;
}) {
  const supabase = await createClient()

  const user = (await supabase.auth.getUser()).data.user
  if (!user) {
    return redirect('/login?next=/customer')
  }
  const admin = await checkAdmin()
  return (
    <AuthProvider>
      <EntityProvider>
        <Suspense>
          <div className="flex min-h-screen flex-col dark:bg-background relative bg-background text-foreground">
            {/* Subtle background pattern */}
            <div className="background-pattern fixed inset-0 pointer-events-none z-0">
              {/* Gradient background */}
              <div
                className="absolute inset-0 bg-gradient-to-br from-brand/5 via-transparent to-brand-contrast/5 dark:from-brand/10 dark:to-brand-contrast/10"></div>

              {/* Subtle dot pattern - light mode */}
              <div className="absolute inset-0 opacity-100 dark:hidden"
                   style={{
                     backgroundImage: 'radial-gradient(circle at 13% 47%, rgba(140, 140, 140,0.03) 0%, rgba(140, 140, 140,0.03) 25%,transparent 25%, transparent 100%),radial-gradient(circle at 28% 63%, rgba(143, 143, 143,0.03) 0%, rgba(143, 143, 143,0.03) 16%,transparent 16%, transparent 100%),radial-gradient(circle at 81% 56%, rgba(65, 65, 65,0.03) 0%, rgba(65, 65, 65,0.03) 12%,transparent 12%, transparent 100%),radial-gradient(circle at 26% 48%, rgba(60, 60, 60,0.03) 0%, rgba(60, 60, 60,0.03) 6%,transparent 6%, transparent 100%),radial-gradient(circle at 97% 17%, rgba(150, 150, 150,0.03) 0%, rgba(150, 150, 150,0.03) 56%,transparent 56%, transparent 100%),radial-gradient(circle at 50% 100%, rgba(25, 25, 25,0.03) 0%, rgba(25, 25, 25,0.03) 36%,transparent 36%, transparent 100%),radial-gradient(circle at 55% 52%, rgba(69, 69, 69,0.03) 0%, rgba(69, 69, 69,0.03) 6%,transparent 6%, transparent 100%),linear-gradient(90deg, rgb(255,255,255),rgb(255,255,255))',
                   }}>
              </div>

              {/* Subtle dot pattern - dark mode */}
              <div className="absolute inset-0 hidden dark:block animate-subtle-shift"
                   style={{
                     backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.03) 1px, transparent 1px), radial-gradient(circle, rgba(255,255,255,0.02) 1px, transparent 1px)',
                     backgroundSize: '40px 40px, 20px 20px',
                     backgroundPosition: '0 0, 20px 20px',
                     opacity: 0.4,
                   }}>
              </div>

            </div>

            <SidebarWithSelectors admin={admin}>
              <Toaster />
              <main
                className="min-h-screen flex flex-col items-center m-auto w-full text-foreground  relative z-10">
                <Suspense fallback={
                  <div>
                    <Skeleton className="h-4 w-[250px]" />
                    <div className="space-x-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  </div>
                }>{children}</Suspense>
              </main>
              <AIDisclaimerFooter />
            </SidebarWithSelectors>


          </div>
        </Suspense>
      </EntityProvider>
    </AuthProvider>
  )
}
