/**
 * # EkoIntelligence ESG Platform Navigation Sidebar System
 *
 * This TypeScript React component provides the primary navigation interface for the EkoIntelligence ESG 
 * (Environmental, Social, Governance) analysis platform's customer-facing application. It delivers a 
 * comprehensive sidebar navigation system that integrates hierarchical menu structures, real-time messaging,
 * user authentication workflows, feature flag-based access control, and glass-morphism UI design principles.
 *
 * ## Core Functionality Overview
 * - **Hierarchical Navigation System**: Multi-level collapsible navigation structure supporting nested menu items and sub-navigation
 * - **Real-Time Messaging Integration**: Live message notifications with persistent toast displays and database synchronization
 * - **Feature Flag Access Control**: Dynamic navigation visibility based on user permissions and organizational feature availability
 * - **Role-Based Navigation**: Administrative navigation items with proper access restrictions and admin-only sections
 * - **User Profile Management**: Comprehensive user dropdown with account access, billing, usage analytics, and authentication controls
 * - **Persistent State Management**: Local storage integration for sidebar collapse states and user preferences
 * - **Glass-Morphism Design**: Modern UI design system with translucent surfaces and visual depth effects
 *
 * ## ESG Platform Integration Context
 * **Primary ESG Analysis Navigation**:
 * - **Dashboard Analytics**: Core ESG performance metrics, flags analysis, and prediction modeling
 * - **Greenwashing Detection**: Claims analysis, promises tracking, and cherry-picking identification systems
 * - **Document Management**: ESG document creation, analysis, and collaborative editing workflows
 * - **Corporate Analysis**: Company ESG performance assessment and benchmarking tools
 * - **ESG Framework Models**: Doughnut Economics, UN SDG alignment, and Plant Based Treaty analysis
 * - **Usage Analytics**: Platform utilization monitoring and optimization insights
 *
 * **Administrative ESG Management**:
 * - **Organization Management**: Multi-tenant ESG data management and access control
 * - **User Administration**: ESG analyst and stakeholder account management
 * - **Feature Flag Control**: ESG module availability and access permission management
 * - **Virtual Entity Management**: ESG entity creation and relationship mapping
 * - **System Messaging**: ESG analysis completion notifications and system alerts
 *
 * ## Real-Time Messaging Architecture
 * **Message System Integration**: The sidebar implements a sophisticated real-time messaging system for ESG analysis workflows:
 * - **Persistent Toast Notifications**: 24-hour duration messages for ESG analysis completion, system alerts, and user notifications
 * - **Database Message Synchronization**: Integration with `acc_messages` table for message persistence and read state tracking
 * - **Real-Time Listeners**: Supabase real-time subscriptions for instant message delivery and backoffice communication
 * - **Message State Management**: Automatic read state updates when messages are dismissed, maintaining accurate notification counts
 * - **Error Handling**: Comprehensive error handling for message fetch failures and real-time connection issues
 *
 * **Message Types and ESG Context**:
 * - **Analysis Completion**: Notifications when ESG flag analysis, claims verification, or prediction modeling completes
 * - **System Alerts**: Critical system notifications, data processing updates, and platform maintenance alerts
 * - **Collaboration Messages**: Team communication related to ESG document editing and analysis sharing
 * - **Administrative Notifications**: Organization-level announcements, feature updates, and policy changes
 *
 * ## Navigation Structure and ESG Workflows
 * **Main Navigation Integration** (`navigationTree.navMain`):
 * - **Dashboard Section**: ESG performance dashboards with flags, claims, promises, cherry-picking, and prediction analytics
 * - **Documents Section**: ESG document management with creation, viewing, and collaborative editing capabilities
 * - **Analysis Section**: Company ESG analysis, document processing, and platform usage analytics
 * - **Models Section**: ESG framework implementations including Doughnut Economics, UN SDG, and Plant Based Treaty
 * - **Documentation Section**: User guidance system with ESG analysis tutorials and platform documentation
 * - **Admin Section**: Administrative functions for user management, organization control, and system configuration
 *
 * **Secondary Navigation** (`navigationTree.navSecondary`):
 * - **Support System**: Customer support channels for ESG analysis guidance and technical assistance
 * - **Feedback Collection**: User feedback mechanisms for ESG platform improvements and feature requests
 *
 * ## Feature Flag Integration and ESG Access Control
 * **Dynamic Navigation Visibility**: Navigation items are conditionally displayed based on feature flags and user permissions:
 * - **ESG Module Flags**: `dashboard.flags`, `dashboard.greenwashing`, `dashboard.prediction` control access to specific ESG analysis tools
 * - **Document Access Flags**: `document.create`, `document.view` manage ESG document management permissions
 * - **Documentation Flags**: `navigation.help.docs` controls access to ESG analysis documentation and tutorials
 * - **Administrative Flags**: Role-based access control for organization management and system administration
 *
 * **Access Control Implementation**:
 * - **Feature Flag Validation**: Real-time checking of user feature flags from authentication context
 * - **Administrative Access**: `adminOnly` property restrictions for administrative ESG management functions
 * - **Organization-Level Control**: Feature flag inheritance from organization-level settings to user-level permissions
 * - **Dynamic Menu Filtering**: Automatic hiding of navigation items when feature flags are disabled
 *
 * ## User Authentication and Profile Management
 * **Authentication Integration**: Deep integration with Supabase authentication system for ESG platform access:
 * - **User Profile Display**: Avatar, full name, and email display with organization context
 * - **Authentication State Management**: Real-time authentication state monitoring and session management
 * - **Profile Data Integration**: User profile information from `profiles` table with ESG-specific fields
 * - **Sign-Out Functionality**: Secure authentication termination with proper cleanup and redirection
 *
 * **User Dropdown Navigation**:
 * - **Account Management**: Direct access to user profile settings and ESG analyst preferences
 * - **Billing Integration**: Subscription management for ESG platform access and feature upgrades
 * - **Usage Analytics**: Platform utilization monitoring and ESG analysis usage tracking
 * - **Notification Settings**: Message preference management and ESG alert configuration
 *
 * ## Technical Implementation Architecture
 * **React Hooks Integration**:
 * - **useAuth Hook**: Authentication context for user state, profile data, and feature flag access
 * - **useEntity Hook**: ESG entity context for maintaining analysis scope and query parameters
 * - **useRouter Hook**: Next.js navigation with query parameter preservation for ESG analysis contexts
 * - **useToast Hook**: Toast notification system for real-time ESG analysis updates and system messages
 * - **useLocalStorage Hook**: Persistent sidebar state management with user preference retention
 *
 * **Supabase Integration**:
 * - **Real-Time Subscriptions**: Message listeners for instant ESG analysis notifications and system updates
 * - **Database Queries**: Message fetching from `acc_messages` table with read state management
 * - **Row Level Security**: Secure message access restricted to authenticated users and their authorized data
 * - **Authentication Management**: User session handling and profile data synchronization
 *
 * **State Management Patterns**:
 * - **Sidebar Collapse State**: Local storage persistence for navigation section expand/collapse preferences
 * - **Message State Tracking**: Real-time message read/unread state synchronization with database
 * - **Navigation Context**: Query parameter preservation for ESG analysis contexts across navigation
 * - **Error State Handling**: Comprehensive error handling for authentication, messaging, and navigation failures
 *
 * ## Glass-Morphism Design Implementation
 * **Visual Design System**: The sidebar implements a modern glass-morphism design with ESG-appropriate styling:
 * - **Translucent Surfaces**: Glass-effect classes for sidebar elements with backdrop blur and translucency
 * - **Brand Integration**: EkoIntelligence logo and brand elements with proper sizing and color schemes
 * - **Interactive States**: Hover effects, active states, and visual feedback for navigation interactions
 * - **Accessibility Support**: Screen reader labels, keyboard navigation support, and ARIA attributes
 * - **Responsive Design**: Mobile and desktop responsive design patterns for optimal ESG analyst workflows
 *
 * **Component Styling Patterns**:
 * - **Glass Effect Classes**: `glass-effect-brand`, `glass-effect-subtle`, and `glass-effect-brand-lit` for consistent visual hierarchy
 * - **Rounded Corners**: Generous border radii (rounded-xl, rounded-2xl) for modern, approachable interface design
 * - **Visual Depth**: Drop shadows and blur effects to create layered, three-dimensional interface elements
 * - **Color Integration**: ESG brand colors with dark/light mode support for optimal user experience
 *
 * ## Database Integration and ESG Data Context
 * **Message System Database Schema**:
 * - **acc_messages Table**: Message storage with recipient targeting, read state tracking, and deletion management
 * - **profiles Table**: User profile information with ESG-specific feature flags and administrative permissions
 * - **Row Level Security**: Database-level access control ensuring users only access their authorized ESG data
 * - **Real-Time Triggers**: Database triggers for instant message delivery and notification system integration
 *
 * **Query Parameter Management**: ESG analysis context preservation:
 * - **Entity Context**: Current ESG entity selection maintained across navigation for consistent analysis scope
 * - **Filter Preservation**: Analysis filters and search parameters preserved during navigation transitions
 * - **URL State Management**: Clean URL patterns with proper query parameter handling for ESG workflows
 * - **Deep Linking**: Direct access to specific ESG analysis views with preserved context and parameters
 *
 * ## Integration with Platform Architecture
 * **Component Dependencies and ESG System Integration**:
 * - **Navigation Tree**: Hierarchical navigation configuration from `@/app/customer/navigation` with ESG-specific routes
 * - **Authentication Context**: User authentication state and ESG analyst permissions from `@/components/context/auth/auth-context`
 * - **Entity Context**: ESG entity selection and analysis scope from `@/components/context/entity/entity-context`
 * - **Toast System**: Real-time notifications for ESG analysis completion and system alerts from `@/hooks/use-toast`
 * - **Backoffice Integration**: Communication with ESG analysis backend through `@/components/backoffice` listeners
 *
 * **Performance Optimizations**:
 * - **Conditional Rendering**: Feature flag-based rendering to minimize DOM elements for disabled ESG features
 * - **Message Deduplication**: Efficient message handling to prevent duplicate notifications for ESG analysis updates
 * - **State Management**: Optimized React hooks usage with proper dependency arrays for ESG context preservation
 * - **Database Efficiency**: Optimized Supabase queries with proper indexing for message and profile data retrieval
 *
 * ## Testing and Quality Assurance
 * **Test Integration Points**: The sidebar includes comprehensive test identifiers for ESG platform quality assurance:
 * - **Navigation Testing**: `data-testid` attributes for all navigation elements supporting automated ESG workflow testing
 * - **User Interaction Testing**: Test identifiers for dropdown menus, authentication flows, and ESG feature access
 * - **Message System Testing**: Test coverage for real-time messaging, notification display, and ESG analysis alerts
 * - **Accessibility Testing**: Screen reader support and keyboard navigation for inclusive ESG platform access
 *
 * @see https://supabase.com/docs/guides/realtime Supabase Real-time Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating Next.js Navigation and Linking
 * @see https://ui.shadcn.com/docs/components/sidebar Shadcn/ui Sidebar Component Documentation
 * @see https://lucide.dev/guide/packages/lucide-react Lucide React Icon System
 * @see {@link /components/context/auth/auth-context.tsx} Authentication Context Provider
 * @see {@link /components/context/entity/entity-context.tsx} ESG Entity Context Provider
 * @see {@link /app/customer/navigation.tsx} Navigation Tree Configuration
 * @see {@link /components/backoffice/index.ts} Backoffice Communication System
 * @see {@link /hooks/use-toast.ts} Toast Notification System
 * <AUTHOR>
 * @updated 2025-07-24
 * @description EkoIntelligence ESG Platform Navigation Sidebar System with Real-Time Messaging and Feature Flag Integration
 * @example
 * ```tsx
 * // Basic sidebar implementation with admin access control
 * import { SidebarWithSelectors } from '@/app/customer/sidebar';
 * 
 * function ESGDashboardLayout({ children }: { children: React.ReactNode }) {
 *   const { profile } = useAuth();
 *   const isAdmin = profile?.is_admin || false;
 * 
 *   return (
 *     <SidebarWithSelectors admin={isAdmin}>
 *       <main className="esg-dashboard-content">
 *         {children}
 *       </main>
 *     </SidebarWithSelectors>
 *   );
 * }
 * 
 * // ESG-specific navigation with feature flag integration
 * function ESGAnalysisWorkflow() {
 *   const { hasFeature } = useAuth();
 * 
 *   return (
 *     <div className="esg-analysis-container">
 *       {hasFeature('dashboard.flags') && (
 *         <Link href="/customer/dashboard/flags">
 *           ESG Flags Analysis →
 *         </Link>
 *       )}
 *       {hasFeature('dashboard.greenwashing') && (
 *         <Link href="/customer/dashboard/gw/claims">
 *           Greenwashing Claims Analysis →
 *         </Link>
 *       )}
 *     </div>
 *   );
 * }
 * 
 * // Real-time ESG message handling
 * function ESGMessageNotifications() {
 *   const { toast } = useToast();
 *   const supabase = createClient();
 * 
 *   useEffect(() => {
 *     const messageChannel = supabase
 *       .channel('esg-analysis-messages')
 *       .on('postgres_changes', {
 *         event: 'INSERT',
 *         schema: 'public',
 *         table: 'acc_messages'
 *       }, (payload) => {
 *         toast({
 *           title: "ESG Analysis Complete",
 *           description: payload.new.message,
 *           duration: 86400000 // 24 hours for ESG analysis notifications
 *         });
 *       })
 *       .subscribe();
 * 
 *     return () => messageChannel.unsubscribe();
 *   }, [supabase, toast]);
 * 
 *   return null; // Background message handler
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";
import { BadgeCheck, BarChart2Icon, Bell, ChevronRight, ChevronsUpDown, CreditCard, LogOut } from 'lucide-react'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarInset,
    SidebarMenu,
    SidebarMenuAction,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
    SidebarProvider,
} from '@/components/ui/sidebar'
import React, { useEffect } from 'react'
import { EkoLogoText, EkoSymbolBrand } from '@utils/images'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClient } from '@/app/supabase/client'
import Link from 'next/link'
import { backOfficeListener, messageListener } from '@/components/backoffice'
import { useToast } from '@/hooks/use-toast'
import { ToastMessage } from '@/components/toast-message'
import { useAuth } from '@/components/context/auth/auth-context'
import { useEntity } from '@/components/context/entity/entity-context'
import { useLocalStorage } from 'usehooks-ts'
import { navigationTree } from '@/app/customer/navigation'

type SidebarState = {
    [key: string]: boolean
}

export function SidebarWithSelectors({admin, children}: { admin: boolean, children: React.ReactNode }) {
    const router = useRouter();
    const supabase = createClient();
    const {toast, dismiss} = useToast()

    const auth = useAuth()
    const entityContext = useEntity()
    const searchParams = useSearchParams();
    
    // Use filtered entity context query string instead of all URL parameters
    const queryParams = entityContext.queryString;

    const [sidebarState, setSidebarState] = useLocalStorage<SidebarState>("sidebar-state", {});

    function toggleSidebar(item: string) {
        setSidebarState(prev => {
            // Get the current value (default to true if not set)
            const currentValue = prev && prev[item] !== undefined ? prev[item] : true;
            const newState = {...(prev || {}), [item]: !currentValue};
            return newState;
        });
    }

    function isSidebarOpen(item: string) {
        const isOpen = sidebarState && sidebarState[item] !== undefined ? sidebarState[item] : true;
        return isOpen;
    }

    async function signOut() {
        const {error} = await supabase.auth.signOut()
        if (error) {
            toast({description: "" + error});
        } else {
            window.location.href = ('/');
        }
    }


    useEffect(() => {
        if (!auth.user?.id) {
            return
        }

        const channels = backOfficeListener(supabase, (payload, error) => {
            if (error) {
                toast({description: error.message, variant: "destructive"});
            }
            if (payload) {
                toast({description: payload.message, title: "Request Completed", duration: -1});
            }
        });
        return () => {
            channels?.unsubscribe();
        };

    }, [auth.user?.id]); // Only depend on user ID, not the entire auth object

    useEffect(() => {
        if (!auth.user?.id) {
            return
        }

        // Fetch and show existing unread messages as persistent toasts
        const fetchUnreadMessages = async () => {
            try {
                if (!supabase || typeof supabase.from !== 'function') {
                    console.log('Supabase not available, skipping message fetch');
                    return;
                }

                const { data: unreadMessages, error } = await supabase
                    .from('acc_messages')
                    .select('*')
                    .eq('recipient', auth.user?.id!)
                    .is('deleted_at', null)
                    .is('read_at', null)
                    .order('created_at', { ascending: false });

                if (error) {
                    console.error('Error fetching unread messages:', error);
                    return;
                }

                if (unreadMessages && unreadMessages.length > 0) {
                console.log(`Showing ${unreadMessages.length} unread messages as persistent toasts`);
                unreadMessages.forEach((message) => {
                    const toastInstance = toast({
                        variant: "compact",
                        description: <ToastMessage notification={message} />,
                        duration: 86400000, // 24 hours in milliseconds - effectively persistent
                        onOpenChange: (open) => {
                            // When toast is dismissed/closed, mark as read
                            if (!open) {
                                const markAsRead = async () => {
                                    try {
                                        await supabase
                                            .from('acc_messages')
                                            .update({ read_at: new Date().toISOString() })
                                            .eq('id', message.id);
                                    } catch (error) {
                                        console.error('Error marking message as read:', error);
                                    }
                                };
                                void markAsRead();
                            }
                        }
                    });
                });
                }
            } catch (err) {
                console.error('Error in fetchUnreadMessages:', err);
            }
        };

        fetchUnreadMessages();

        // Listen for new unread messages
        console.log("Listening for new messages");
        const channels = messageListener(supabase, auth.user?.id!, (payload) => {
            console.log("New message payload", payload);
            if (payload) {
                const toastInstance = toast({
                    variant: "compact",
                    description: <ToastMessage notification={payload} />,
                    duration: 86400000, // 24 hours in milliseconds - effectively persistent
                    onOpenChange: (open) => {
                        // When toast is dismissed/closed, mark as read
                        if (!open) {
                            const markAsRead = async () => {
                                try {
                                    await supabase
                                        .from('acc_messages')
                                        .update({ read_at: new Date().toISOString() })
                                        .eq('id', payload.id);
                                } catch (error) {
                                    console.error('Error marking message as read:', error);
                                }
                            };
                            void markAsRead();
                        }
                    }
                });
            }
        });
        
        return () => {
            channels?.unsubscribe();
        };

    }, [auth.user?.id]); // Only depend on user ID, not the entire auth object





    return  (
        <SidebarProvider>
            <Sidebar variant="floating" className="sidebarz-20" data-testid="sidebar">
                <SidebarHeader>
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <SidebarMenuButton size="lg" asChild>
                                <a href="/">
                                    <div className="">
                                        <EkoSymbolBrand height={26} className="inline-block drop-shadow-lg"/>
                                        <EkoLogoText height={23}
                                                     className="dark:hidden ml-3 mt-0.5 inline-block drop-shadow-sm"
                                                     ekoColor="#666" intelligenceColor="#111"/>
                                        <EkoLogoText height={23}
                                                     className="hidden dark:inline-block ml-3 mt-0.5  drop-shadow-sm"
                                                     ekoColor="#bbb" intelligenceColor="#eee"/>
                                    </div>
                                </a>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarHeader>
                <SidebarContent>
                    <SidebarGroup>
                        <SidebarGroupLabel>Analysis</SidebarGroupLabel>
                        <SidebarMenu>
                            {navigationTree.navMain.map((item) => {
                                // Skip if feature flag required and not enabled
                                if (item.requires && !auth.hasFeature(item.requires)) return null;
                                
                                // Skip if admin-only item and user is not admin
                                if (item.adminOnly && !admin) return null;

                                return (
                                <Collapsible
                                    key={item.title}
                                    asChild
                                    defaultOpen={isSidebarOpen(item.title)}
                                    onOpenChange={(open) => toggleSidebar(item.title)}
                                    data-testid={`nav-section-${item.title.toLowerCase().replace(/\s+/g, '-')}`}
                                >
                                    <SidebarMenuItem>
                                        <SidebarMenuButton asChild tooltip={item.title} data-testid={`nav-${item.title.toLowerCase().replace(/\s+/g, '-')}`}>
                                            <div>
                                                {item.icon &&
                                                    <item.icon/>
                                                }
                                                <span>{item.title}</span>
                                            </div>
                                        </SidebarMenuButton>
                                        {item.items?.length ? (
                                            <>
                                                <CollapsibleTrigger asChild>
                                                    <SidebarMenuAction className="data-[state=open]:rotate-90" data-testid="section-collapse-button">
                                                        <ChevronRight/>
                                                        <span className="sr-only">Toggle</span>
                                                    </SidebarMenuAction>
                                                </CollapsibleTrigger>
                                                <CollapsibleContent data-testid="section-content">
                                                    <SidebarMenuSub>
                                                        {item.items?.map((subItem) => { 
                                                            // Skip if hidden
                                                            if (subItem.hidden) return null;
                                                            
                                                            // Skip if feature flag required and not enabled
                                                            if (subItem.requires && !auth.hasFeature(subItem.requires)) return null;
                                                            
                                                            return (
                                                                <SidebarMenuSubItem key={subItem.title}>
                                                                    <SidebarMenuSubButton asChild>
                                                                        <Link
                                                                          href={subItem.url + (queryParams && queryParams.trim() ? '?' + queryParams : '')}
                                                                            data-testid={`nav-${subItem.title.toLowerCase().replace(/\s+/g, '-')}`}>
                                                                            {subItem.icon &&
                                                                                <subItem.icon className="opacity-50"/>}
                                                                            <span>{subItem.title}</span>
                                                                        </Link>
                                                                    </SidebarMenuSubButton>
                                                                </SidebarMenuSubItem>
                                                            );
                                                        })}
                                                    </SidebarMenuSub>
                                                </CollapsibleContent>
                                            </>
                                        ) : null}
                                    </SidebarMenuItem>
                                </Collapsible>
                                );
                            })}
                        </SidebarMenu>
                    </SidebarGroup>
                    {/*<SidebarGroup className="group-data-[collapsible=icon]:hidden">*/}
                    {/*    <SidebarGroupLabel>Projects</SidebarGroupLabel>*/}
                    {/*    <SidebarMenu>*/}
                    {/*        {data.projects.map((item) => (*/}
                    {/*            <SidebarMenuItem key={item.name}>*/}
                    {/*                <SidebarMenuButton asChild>*/}
                    {/*                    <a href={item.url}>*/}
                    {/*                        <item.icon/>*/}
                    {/*                        <span>{item.name}</span>*/}
                    {/*                    </a>*/}
                    {/*                </SidebarMenuButton>*/}
                    {/*                <DropdownMenu>*/}
                    {/*                    <DropdownMenuTrigger asChild>*/}
                    {/*                        <SidebarMenuAction showOnHover>*/}
                    {/*                            <MoreHorizontal/>*/}
                    {/*                            <span className="sr-only">More</span>*/}
                    {/*                        </SidebarMenuAction>*/}
                    {/*                    </DropdownMenuTrigger>*/}
                    {/*                    <DropdownMenuContent*/}
                    {/*                        className="w-48"*/}
                    {/*                        side="bottom"*/}
                    {/*                        align="end"*/}
                    {/*                    >*/}
                    {/*                        <DropdownMenuItem>*/}
                    {/*                            <Folder className="text-muted-foreground"/>*/}
                    {/*                            <span>View Project</span>*/}
                    {/*                        </DropdownMenuItem>*/}
                    {/*                        <DropdownMenuItem>*/}
                    {/*                            <Share className="text-muted-foreground"/>*/}
                    {/*                            <span>Share Project</span>*/}
                    {/*                        </DropdownMenuItem>*/}
                    {/*                        <DropdownMenuSeparator/>*/}
                    {/*                        <DropdownMenuItem>*/}
                    {/*                            <Trash2 className="text-muted-foreground"/>*/}
                    {/*                            <span>Delete Project</span>*/}
                    {/*                        </DropdownMenuItem>*/}
                    {/*                    </DropdownMenuContent>*/}
                    {/*                </DropdownMenu>*/}
                    {/*            </SidebarMenuItem>*/}
                    {/*        ))}*/}
                    {/*        <SidebarMenuItem>*/}
                    {/*            <SidebarMenuButton>*/}
                    {/*                <MoreHorizontal/>*/}
                    {/*                <span>More</span>*/}
                    {/*            </SidebarMenuButton>*/}
                    {/*        </SidebarMenuItem>*/}
                    {/*    </SidebarMenu>*/}
                    {/*</SidebarGroup>*/}
                    <SidebarGroup className="mt-auto">
                        <SidebarGroupContent>
                            <SidebarMenu>
                                {navigationTree.navSecondary.map((item) => (
                                    <SidebarMenuItem key={item.title}>
                                        <SidebarMenuButton asChild size="sm">
                                            <a
                                              href={item.url + (queryParams && queryParams.trim() ? '?' + queryParams : '')}
                                               data-testid={`nav-${item.title.toLowerCase().replace(/\s+/g, '-')}`}>
                                                {item.icon &&
                                                    <item.icon/>
                                                }
                                                <span>{item.title}</span>
                                            </a>
                                        </SidebarMenuButton>
                                    </SidebarMenuItem>
                                ))}
                            </SidebarMenu>
                        </SidebarGroupContent>
                    </SidebarGroup>
                </SidebarContent>
                <SidebarFooter>
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <SidebarMenuButton
                                        size="lg"
                                        className="data-[state=open]:glass-effect-brand data-[state=open]:text-sidebar-accent-foreground"
                                        data-testid="user-dropdown-trigger"
                                    >
                                        {auth.profile &&
                                            <Avatar className="h-8 w-8 rounded-xl glass-effect-subtle">
                                                {auth.profile?.avatar_url &&
                                                    <AvatarImage
                                                        src={auth.profile?.avatar_url}
                                                        alt={auth.profile?.full_name || ''}
                                                    />
                                                }
                                                <AvatarFallback className="rounded-xl">
                                                    {auth.profile?.full_name?.split(" ").map((name) => name[0]).join("")}
                                                </AvatarFallback>
                                            </Avatar>
                                        }
                                        <div className="grid flex-1 text-left text-sm leading-tight">
                                            <span className="truncate font-semibold">{auth.profile?.full_name}</span>
                                            <span className="truncate text-xs">{auth.user?.email}</span>
                                        </div>
                                        <ChevronsUpDown className="ml-auto size-4"/>
                                    </SidebarMenuButton>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent
                                    className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-2xl glass-effect-brand-lit"
                                    side="bottom"
                                    align="end"
                                    sideOffset={4}
                                    data-testid="user-dropdown-menu"
                                >
                                    <DropdownMenuLabel className="p-0 font-normal" data-testid="user-info">
                                        <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                            {auth.profile &&
                                                <Avatar className="h-8 w-8 rounded-xl glass-effect-subtle">
                                                    {auth.profile?.avatar_url &&
                                                        <AvatarImage
                                                            src={auth.profile?.avatar_url}
                                                            alt={auth.profile?.full_name || ''}
                                                        />
                                                    }
                                                    <AvatarFallback className="rounded-xl">
                                                        {auth.profile?.full_name?.split(" ").map((name: string) => name[0]).join("")}
                                                    </AvatarFallback>
                                                </Avatar>
                                            }
                                            <div className="grid flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-semibold">
                          {auth.profile?.full_name}
                        </span>
                                                <span className="truncate text-xs">
                          {auth.user?.email}
                        </span>
                                            </div>
                                        </div>
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator/>

                                    <DropdownMenuGroup>
                                        <DropdownMenuItem
                                            className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-subtle rounded-xl"
                                            onClick={() => router.push("/customer/account" + (queryParams ? "?" + queryParams : ""))}
                                            data-testid="menu-account">
                                            <BadgeCheck/>
                                            Account
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-subtle rounded-xl"
                                            onClick={() => router.push("/customer/account/billing" + (queryParams ? "?" + queryParams : ""))}
                                            data-testid="menu-billing">
                                            <CreditCard/>
                                            Billing
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-subtle rounded-xl"
                                            onClick={() => router.push("/customer/analysis/usage" + (queryParams ? "?" + queryParams : ""))}
                                            data-testid="menu-usage">
                                            <BarChart2Icon/>
                                            Usage
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-subtle rounded-xl"
                                            onClick={() => router.push("/customer/account/notifications" + (queryParams ? "?" + queryParams : ""))}
                                            data-testid="menu-notifications">
                                            <Bell/>
                                            Notifications
                                        </DropdownMenuItem>
                                    </DropdownMenuGroup>
                                    <DropdownMenuSeparator/>
                                    <DropdownMenuItem
                                        className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-compliment rounded-xl"
                                        onClick={signOut}
                                        data-testid="menu-sign-out">
                                        <LogOut/>
                                        Sign out
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarFooter>
            </Sidebar>
            <SidebarInset>
                <div className="flex flex-col items-center bg-transparent justify-center w-full min-h-screen ">
                    {children}
                </div>
            </SidebarInset>
        </SidebarProvider>
    )
}

// <style jsx>{`
//     .select-trigger {
//         @apply h-8 text-sm;
//     }
// `}</style>
