# EkoIntelligence Customer Application - Main Platform Hub

The EkoIntelligence Customer Application is a comprehensive Next.js 15 App Router platform that serves as the central hub for Environmental, Social, and Governance (ESG) analysis within the EkoIntelligence ecosystem. This customer-facing application provides authenticated users with sophisticated ESG analytics, document management, collaborative editing, and real-time data visualization capabilities through a modern glass-morphism design system.

## Overview

This customer application module serves as the main entry point for authenticated users accessing the EkoIntelligence ESG analysis platform. Built with Next.js 15 App Router, Supabase authentication, and a sophisticated dual-database architecture, it provides comprehensive ESG analysis workflows including entity analysis, document processing, dashboard visualization, account management, and file upload capabilities.

The application integrates seamlessly with the backend Python analytics engine through a synchronized data layer, enabling real-time access to complex ESG insights including greenwashing detection, claims analysis, promise tracking, and predictive analytics. The platform implements a modern glass-morphism design system with heavily rounded elements, translucent surfaces, and responsive layouts optimized for both desktop and mobile usage.

**Key Platform Features:**
- **Comprehensive ESG Analytics**: Multi-modal analysis including flags, greenwashing detection, predictive modeling, and report generation
- **Real-time Collaboration**: TipTap-based document editing with multi-user presence and synchronized commenting
- **Advanced Authentication**: Supabase Auth with Row Level Security (RLS) and role-based access control
- **Dual-Database Architecture**: Analytics backend synchronized with customer-facing database for optimal performance
- **Glass-morphism Design**: Modern translucent interface with backdrop blur, rounded corners, and subtle animations
- **Progressive Loading**: Suspense boundaries, skeleton states, and optimized data fetching for large datasets
- **AI-Powered Content**: Integrated AI tools for content generation, analysis summaries, and report creation

## Specification

### System Architecture Requirements

The customer application implements a sophisticated multi-layered architecture supporting enterprise-grade ESG analysis workflows:

**Frontend Architecture (Next.js 15 App Router)**:
- Server Components for data fetching and initial rendering performance
- Client Components for interactivity and real-time features  
- Parallel routes for modal systems and intercepting routes
- Progressive enhancement with core functionality available without JavaScript
- Strategic use of Suspense boundaries for optimal loading experiences
- Type-safe database operations with comprehensive TypeScript interfaces

**Authentication & Authorization System**:
- Server-side session validation with automatic login redirects
- Email-based admin detection (`@ekointelligence.com` domain validation)  
- Row Level Security (RLS) integration through Supabase authentication context
- Real-time session management with automatic refresh capabilities
- Multi-tenant data isolation with fail-safe design defaults

**Database Integration Architecture**:
```mermaid
graph TB
    subgraph "Analytics Database (Python Backend)"
        PY[Python Analytics Engine]
        AD[(Analytics PostgreSQL)]
        PY --> AD
        AD --> DEMISE[DEMISE Model Processing]
        AD --> ESG[ESG Flag Generation]
        AD --> GW[Greenwashing Detection]
    end
    
    subgraph "Sync Layer"
        SYNC[Data Synchronization Jobs]
        AD --> SYNC
    end
    
    subgraph "Customer Database (Supabase)"
        CD[(Customer PostgreSQL)]
        SYNC --> CD
        CD --> VIEWS[Database Views]
        CD --> RLS[Row Level Security]
    end
    
    subgraph "Customer Application (Next.js 15)"
        UI[React Components]
        SC[Server Components]
        CC[Client Components]
        VIEWS --> SC
        SC --> UI
        CC --> UI
        UI --> AUTH[Supabase Auth]
        AUTH --> RLS
    end
```

**Data Flow Architecture**:
- **Analytics Processing**: Python backend performs complex ESG analysis, LLM processing, and entity relationship mapping
- **Data Synchronization**: Automated sync via `xfer_*` tables ensures customer database contains optimized, user-facing data  
- **Real-time Updates**: Supabase real-time subscriptions provide live data synchronization for analysis status and notifications
- **Performance Optimization**: Customer database views optimized for frontend queries with appropriate indexing and caching

## Key Components

### Core Application Structure

| Directory | Purpose | Key Features | Technologies |
|-----------|---------|--------------|-------------|
| **`layout.tsx`** | Root application layout | Authentication gateway, context providers, glass-morphism background | Next.js 15, Supabase Auth, React Context |
| **`page.tsx`** | Welcome dashboard | Quota monitoring, quick actions, entity context management | Server/Client Components, Real-time data |
| **`navigation.tsx`** | Client-side navigation | Breadcrumb management, URL parameter preservation | Client Component, URL state |
| **`sidebar.tsx`** | Collapsible navigation | Feature flag integration, entity-aware filtering | Context integration, localStorage |

### Feature Modules

| Module | Description | Core Capabilities | Integration Points |
|--------|-------------|-------------------|-------------------|
| **`account/`** | User account management | Profile editing, billing, notifications, contact forms | Supabase Auth, Slack integration, avatar management |
| **`analysis/`** | ESG analysis tools | Entity analysis, document processing, usage monitoring | Python backend, quota management, real-time tracking |
| **`dashboard/`** | Analytics visualization | ESG flags, greenwashing detection, predictive analysis, reports | Dual-database sync, interactive charts, modal system |
| **`documents/`** | Document collaboration | TipTap editing, templates, real-time collaboration, AI tools | Rich text editing, citation management, auto-save |
| **`files/`** | File upload system | URL processing, S3 uploads, document ingestion | AWS S3, presigned URLs, backend processing queue |

### Context Management Architecture

```mermaid
graph TD
    A[Root Layout] --> B[AuthProvider Context]
    A --> C[EntityProvider Context]
    A --> D[NavProvider Context]
    
    B --> E[User Authentication State]
    B --> F[Admin Permissions]
    B --> G[Organization Context]
    
    C --> H[Selected Entity State]
    C --> I[Model Configuration]
    C --> J[Run Parameter Management]
    
    D --> K[Breadcrumb Navigation]
    D --> L[URL Parameter Handling]
    
    E --> M[Feature Module Access]
    H --> M
    K --> M
```

## Dependencies

### Core Framework Stack

**Next.js 15 App Router Dependencies**:
- **Next.js 15**: Latest React framework with server components, parallel routes, and advanced caching
- **React 18+**: Concurrent rendering, Suspense boundaries, and server/client component architecture
- **TypeScript 5+**: Comprehensive type safety with strict configuration and advanced type features
- **Tailwind CSS**: Utility-first styling with custom glass-morphism classes and responsive design system

**Database & Authentication Stack**:
- **Supabase Client**: PostgreSQL database with Row Level Security policies and real-time subscriptions
- **Supabase Auth**: Integrated authentication with automatic token management and session handling
- **Supabase Storage**: File storage for avatars, documents, and media assets with signed URL access
- **Supabase Real-time**: WebSocket-based live data synchronization for analysis status and notifications

### UI Framework & Design System

**Component Library Dependencies**:
- **shadcn/ui**: Consistent component library built on Radix UI primitives with accessibility features
- **Radix UI**: Unstyled, accessible UI primitives for complex interactions and component composition
- **Lucide React**: Modern SVG icon library with comprehensive ESG-themed icons and accessibility support
- **TipTap Editor**: Collaborative rich text editing with real-time synchronization and custom extensions
- **React Dropzone**: Advanced drag-and-drop file selection with validation and progress feedback

### External Service Integration

**Third-Party Service Dependencies**:
- **Google Gemini LLM**: AI content generation for real-time summary creation and ESG analysis
- **AWS S3**: Secure document storage with presigned URL upload system for scalable file management
- **Slack Webhooks**: Contact form integration and administrative notification system
- **Vercel KV**: Response caching for improved performance and reduced database load
- **TipTap Cloud**: Collaborative document editing infrastructure with presence indicators

### Internal Module Dependencies

**Platform Integration Stack**:
- **Backend Analytics Engine**: Python-based ESG analysis pipeline with LLM integration (`/backoffice/src/eko/analysis_v2/`)
- **Database Synchronization**: Automated data sync between analytics and customer databases via `xfer_*` tables
- **Feature Flag System**: Permission-based feature access control with admin-level functionality
- **Toast Notification System**: Comprehensive user feedback system for operations, errors, and real-time updates
- **Entity Management**: Global entity selection and context management across application modules

## Usage Examples

### Basic Application Access and Navigation

```typescript
// Main application entry point - authentication handled automatically
// URL: /customer (redirects to /login if unauthenticated)

// Welcome dashboard with quota monitoring
const welcomePage = await fetch('/customer');
// Automatic entity context establishment if none selected
// URL becomes: /customer?entity=AAPL&run=latest&model=ekoIntelligence

// Navigation preserves entity context across all modules
<Link href="/customer/dashboard">View ESG Dashboard</Link>
// Results in: /customer/dashboard?entity=AAPL&run=latest&model=ekoIntelligence

<Link href="/customer/analysis/companies">Analyze Companies</Link>
// Results in: /customer/analysis/companies?entity=AAPL&run=latest&model=ekoIntelligence
```

### ESG Analysis Workflow Integration

```typescript
// Dashboard analysis with real-time data
import { useEntity } from '@/components/context/entity/entity-context';

function ESGAnalysisDashboard() {
  const { 
    entity,           // Selected entity (e.g., "AAPL")
    model,            // Analysis model (e.g., "ekoIntelligence")
    run,              // Analysis run (e.g., "latest")
    flagsData,        // ESG flags analysis data
    claimsData,       // Claims vs evidence analysis
    promisesData,     // Promise tracking data
    isLoadingFlags,   // Real-time loading states
    queryString       // URL parameters for navigation
  } = useEntity();

  // Navigate with preserved context
  const dashboardUrl = `/customer/dashboard?${queryString}`;
  const flagsUrl = `/customer/dashboard/flags?${queryString}`;
  
  return (
    <div className="glass-effect-lit rounded-3xl p-6">
      <h2>ESG Analysis for {entity}</h2>
      {flagsData && (
        <FlagsVisualization data={flagsData} loading={isLoadingFlags} />
      )}
      <Link href={flagsUrl}>View Detailed Flag Analysis</Link>
    </div>
  );
}
```

### Document Management and Collaboration

```typescript
// Document creation and editing workflow
import { createClient } from '@/app/supabase/client';

async function createESGDocument(templateId: string, entityId: string) {
  const supabase = createClient();
  
  // Create document with entity association
  const { data: document, error } = await supabase
    .from('doc_documents')
    .insert({
      title: `ESG Analysis Report - ${entityId}`,
      entity_id: entityId,
      run_id: 'latest',
      initial_content: templateContent,
      metadata: { template_id: templateId, citations: [] }
    })
    .select()
    .single();

  if (error) throw error;
  
  // Navigate to collaborative editor
  router.push(`/customer/documents/${document.id}`);
}

// Real-time collaboration features
function CollaborativeEditor({ documentId }: { documentId: string }) {
  const [document, setDocument] = useState<DocumentType>();
  const supabase = createClient();

  useEffect(() => {
    // Subscribe to real-time document changes
    const subscription = supabase
      .channel(`document:${documentId}`)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'doc_documents',
        filter: `id=eq.${documentId}`
      }, (payload) => {
        setDocument(payload.new as DocumentType);
      });

    return () => subscription.unsubscribe();
  }, [documentId]);

  return (
    <TipTapEditor
      document={document}
      onSave={handleAutoSave}
      collaborative={true}
      aiToolsEnabled={true}
    />
  );
}
```

### File Upload and Processing Integration

```typescript
// Document upload with ESG processing
async function uploadESGDocument(file: File, entityId: string) {
  // Get presigned S3 URL
  const uploadUrl = await presignedUrl(file.name, file.type, userId);
  
  // Direct browser-to-S3 upload
  await fetch(uploadUrl, {
    method: 'PUT',
    body: file,
    headers: { 'Content-Type': file.type }
  });
  
  // Generate S3 URL for backend processing
  const s3Url = `https://eko-analysis-cus-document-upload.s3.eu-west-2.amazonaws.com/${userId}/${file.name}`;
  
  // Trigger backend ESG analysis
  await callBackofficeAsync('single_url', {
    url: s3Url,
    entity_id: entityId,
    analysis_type: 'comprehensive'
  });
  
  // Real-time status monitoring
  const subscription = supabase
    .channel('upload_progress')
    .on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'api_queue'
    }, (payload) => {
      if (payload.new.status === 'completed') {
        toast({ title: 'Document analysis complete!' });
      }
    });
}
```

## Architecture Notes

### Next.js 15 App Router Implementation

The customer application leverages Next.js 15's advanced routing capabilities for optimal performance and user experience:

**Server Component Strategy**:
- Layout components are Server Components for optimal SEO and initial page load performance
- Data fetching occurs in Server Components close to the data source for reduced client-side JavaScript
- Server Components handle authentication checks, database queries, and initial state establishment
- Strategic use of async/await in Server Components for efficient data loading patterns

**Client Component Architecture**:
- Interactive components marked with `'use client'` for browser-specific functionality
- Real-time features (WebSocket subscriptions, live updates) implemented as Client Components
- Form handling, navigation state, and user interactions managed client-side for responsiveness
- Context providers implemented as Client Components for state management across component trees

**Parallel Routes and Intercepting Routes**:
```mermaid
graph TB
    A[/customer/dashboard/] --> B[layout.tsx - Server Component]
    A --> C[page.tsx - Dashboard Overview]
    A --> D[@modal/ - Parallel Route Slot]
    
    B --> E[EntityModelRunSelector]
    B --> F[Modal Infrastructure]
    
    D --> G[Flag Detail Modals]
    D --> H[Analysis Overlays]
    D --> I[Intercepting Routes]
    
    A --> J[Nested Module Routes]
    J --> K[flags/ - ESG Flags Analysis]
    J --> L[gw/ - Greenwashing Detection]
    J --> M[prediction-v2/ - Predictive Analytics]
    J --> N[reports/ - Report Management]
```

### Authentication and Security Architecture

**Comprehensive Security Model**:
```mermaid
sequenceDiagram
    participant U as User
    participant L as Layout (Server)
    participant S as Supabase Auth
    participant DB as Customer Database
    participant P as Page Component
    
    U->>L: Navigate to /customer/*
    L->>S: Validate session via getUser()
    S->>L: Return user session or null
    
    alt User authenticated
        L->>S: Check admin status
        S->>L: Return admin privileges
        L->>P: Render with AuthProvider context
        P->>DB: Query user data with RLS
        DB->>P: Return filtered data
        P->>U: Display authenticated content
    else No valid session
        L->>U: Redirect to /login?next=/customer
    end
```

**Row Level Security (RLS) Integration**:
- All database queries automatically filtered by user context through Supabase RLS policies
- Multi-tenant data isolation ensures users can only access their organization's data
- Admin-level access controlled through email domain validation and feature flag system
- Real-time subscriptions respect RLS policies for secure live data updates

### Glass-Morphism Design System Implementation

**Visual Design Architecture**:
- **Translucent Backgrounds**: `glass-effect-lit` utility class provides backdrop blur with optimized opacity
- **Rounded Corner Standard**: Consistent 1.5rem (`rounded-3xl`) border radius across all interactive elements
- **Color-Coded Interactions**: Brand colors for positive actions, complementary colors for warnings/errors
- **Responsive Breakpoints**: Mobile-first design with `lg:` and `xl:` breakpoints for desktop optimization
- **Animation System**: `hover:scale-105` transforms with smooth transitions for enhanced user feedback

**Performance-Optimized Background Patterns**:
```css
/* Light mode - Static radial gradients for texture */
.background-pattern-light {
  background-image: 
    radial-gradient(circle at 13% 47%, rgba(140, 140, 140,0.03) 0%, rgba(140, 140, 140,0.03) 25%,transparent 25%, transparent 100%),
    radial-gradient(circle at 28% 63%, rgba(143, 143, 143,0.03) 0%, rgba(143, 143, 143,0.03) 16%,transparent 16%, transparent 100%);
}

/* Dark mode - Animated dot patterns with subtle movement */
.background-pattern-dark {
  background-image: 
    radial-gradient(circle, rgba(255,255,255,0.03) 1px, transparent 1px);
  background-size: 40px 40px, 20px 20px;
  animation: subtle-shift 60s ease-in-out infinite;
}
```

## Known Issues

### Active Issues (From Linear Ticket Analysis)

#### High Priority - Platform Infrastructure

**EKO-238: Agentic Scraper (In Progress)**
- **Description**: Catch-all issue for working on the scraper system that feeds the ESG analysis pipeline
- **Impact**: Affects data ingestion quality and entity analysis completeness
- **Status**: In Progress - Assigned to Neil Ellis
- **Context**: Current branch `feature/eko-238` includes infrastructure improvements for document processing

#### Resolved Issues - Recent Platform Improvements

**EKO-291: Claims Page Navigation (Resolved)**
- **Issue**: Sidebar navigation failing specifically on claims page while top navigation worked correctly
- **Status**: Done - Fixed sidebar navigation across all analysis modules with comprehensive testing
- **Resolution**: Enhanced navigation context preservation and added robust test coverage to prevent regression

**EKO-232: JSON Parsing Error in Analytics Backend (Resolved)**
- **Issue**: `TypeError: the JSON object must be str, bytes or bytearray, not dict` in selective highlighting data processing
- **Status**: Done - Fixed JSON parsing in analytics backend affecting ESG analysis pipeline
- **Impact**: Resolved backend processing failures that could affect customer dashboard data loading

### Technical Debt and Maintenance Items

#### Component Architecture Improvements

1. **Large Component Refactoring**:
   - Welcome dashboard page component exceeds 450 lines - needs modular breakdown
   - Dashboard layout component could benefit from smaller, focused sub-components
   - Entity context state management could be split into more granular contexts

2. **Performance Optimization Opportunities**:
   - Large ESG flag datasets could benefit from virtualization and pagination
   - Real-time subscription management needs proper cleanup to prevent memory leaks
   - Complex dashboard queries could benefit from additional caching and optimization

3. **Error Handling Enhancement**:
   - Need more granular error boundaries for individual analysis modules
   - Enhanced error recovery mechanisms for failed real-time subscriptions
   - Improved user feedback for network connectivity issues and timeout scenarios

#### Browser Compatibility and Accessibility

- **Modern Browser Requirements**: Full support for Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Glass-morphism Dependencies**: Requires `backdrop-filter` CSS support for visual effects
- **Mobile Optimization**: Touch-friendly interactions with responsive breakpoint optimization
- **Accessibility Compliance**: WCAG 2.1 AA compliance with ongoing screen reader testing and keyboard navigation improvements

## Future Work

### Planned Enhancements (Based on Linear Projects & Requirements)

#### Enhanced Analytics Dashboard (Q2 2025)
- **Multi-Entity Comparison**: Side-by-side ESG analysis with peer benchmarking capabilities
- **Advanced Filtering**: Enhanced search and filtering across all analysis types with saved filter presets
- **Custom Dashboard Layouts**: User-configurable dashboard widgets with drag-and-drop layout management
- **Real-time Collaboration**: Multi-user dashboard sessions with shared analysis and annotation capabilities

#### Document Management Evolution (Q2-Q3 2025)
- **Advanced Template System**: Dynamic templates with entity-specific data population and custom field integration
- **Version Control Integration**: Git-like version history with branching, merging, and conflict resolution
- **Advanced Citation Management**: Automated citation extraction with academic formatting and validation
- **AI-Powered Content Enhancement**: Contextual AI suggestions, grammar checking, and ESG accuracy validation

#### Performance & Scalability Initiatives (Q3 2025)
- **Virtual Scrolling Implementation**: Efficient rendering for large datasets in flags, claims, and analysis history
- **Progressive Data Loading**: Intelligent data fetching with predictive caching based on user behavior patterns
- **Module Federation Architecture**: Micro-frontend approach for independent module development and deployment
- **Edge Caching Optimization**: CDN optimization for static analysis assets and frequently accessed data

#### Advanced AI Integration (Q3-Q4 2025)
- **Contextual AI Assistants**: Intelligent help system with analysis guidance and workflow recommendations
- **Automated Insight Generation**: AI-powered trend detection, anomaly identification, and predictive insights
- **Natural Language Query Interface**: Conversational interface for data exploration and report generation
- **Personalized Recommendations**: User-specific analysis suggestions based on behavior, industry, and role

### Requirements Tracking and Project Alignment

All future work initiatives are tied to specific Linear projects and customer feedback:
- **ESG Platform Enhancement Project** (Active - Q2 2025): Dashboard improvements, advanced analytics
- **Enterprise Collaboration Suite** (Planned - Q2 2025): Multi-user workflows, advanced permissions
- **Performance Optimization Initiative** (Ongoing): Database optimization, caching improvements, mobile performance
- **AI/ML Integration Expansion** (Q3-Q4 2025): Advanced AI tools, predictive analytics, automated insights

## Troubleshooting

### Common Issues and Resolution Steps

#### Application Loading and Authentication Issues

**Problem**: Application not loading or showing blank screen
```bash
# Debug sequence:
1. Check browser console for JavaScript errors and network failures
2. Verify Supabase connection status in Network tab - look for failed requests
3. Confirm user authentication session: localStorage contains 'sb-*' keys
4. Check entity context initialization in React DevTools
5. Verify RLS policies allow data access for current user

# Common causes and solutions:
- Authentication token expired → Clear localStorage, re-login
- Entity context not initialized → Check URL parameters, verify entity access
- Network connectivity issues → Check Supabase project status, internet connection
- JavaScript bundle errors → Clear browser cache, check for version conflicts
```

**Problem**: Entity selection not persisting across navigation
```bash
# Troubleshooting steps:
1. Verify URL parameters contain entity, run, and model values
2. Check EntityContext provider wraps all customer application routes
3. Inspect localStorage for corruption or conflicting values
4. Test entity selection dropdown functionality in browser DevTools
5. Verify navigation links include queryString parameter preservation

# Resolution approaches:
- Clear browser cache and localStorage completely
- Verify EntityModelRunSelector component is working correctly
- Check entity context state management in React DevTools
- Ensure all Link components include proper parameter preservation
```

#### Real-Time Data and Subscription Issues

**Problem**: Real-time updates not working (analysis status, notifications)
```bash
# Debug real-time connectivity:
1. Check Supabase WebSocket connection status in Network tab
2. Verify user authentication allows real-time subscription access
3. Review RLS policies on tables being subscribed to (api_queue, doc_documents)
4. Monitor browser console for subscription errors and connection drops
5. Test subscription cleanup on component unmount to prevent memory leaks

# Common fixes:
- Restart browser to reset WebSocket connections
- Verify Supabase project real-time is enabled
- Check for ad blockers interfering with WebSocket traffic
- Review subscription setup code for proper error handling
```

**Problem**: ESG analysis data not loading or showing outdated information
```bash
# Data loading diagnostics:
1. Verify selected entity has analysis data for chosen run
2. Check database connectivity and user permission levels
3. Confirm xfer_* table synchronization status between databases
4. Verify backend analysis completion status in admin interface
5. Test direct database queries to isolate frontend vs backend issues

# Resolution steps:
- Check Supabase client connection: SELECT 1 FROM profiles
- Verify user permissions: SELECT * FROM view_my_companies
- Test entity data query: SELECT * FROM xfer_flags WHERE entity_xid = ?
- Review database sync logs for synchronization failures
- Contact backend team if analysis pipeline is failing
```

#### Performance and UI Issues

**Problem**: Slow loading or poor performance on dashboard
```typescript
// Performance monitoring and optimization
useEffect(() => {
  const startTime = performance.now();
  
   // Component performance tracking
  return () => {
    const endTime = performance.now();
    console.log(`Dashboard render time: ${endTime - startTime}ms`);
    
    // Identify slow-loading data sources
    console.log('Loading states:', { 
      isLoadingFlags, 
      isLoadingClaims, 
      isLoadingPromises 
    });
  };
}, [entity, flagsData]);

// Database query optimization
const optimizeQuery = async () => {
  // Add indexes for common query patterns
  await supabase.sql`
    CREATE INDEX IF NOT EXISTS idx_xfer_flags_entity_run 
    ON xfer_flags(entity_xid, run_id);
    
    CREATE INDEX IF NOT EXISTS idx_xfer_claims_entity_run 
    ON xfer_claims(entity_xid, run_id);
  `;
};
```

**Problem**: Glass-morphism effects not displaying correctly
```bash
# CSS and browser compatibility issues:
1. Verify browser supports backdrop-filter CSS property
2. Check for CSS conflicts with backdrop-blur utility classes
3. Test in different browsers to isolate compatibility issues
4. Inspect computed styles for glass-effect-* utility classes
5. Verify Tailwind CSS configuration includes backdrop-blur variants

# Solutions:
- Update browser to latest version for backdrop-filter support
- Add CSS fallbacks for browsers without backdrop-filter support
- Check Tailwind CSS configuration for missing backdrop utilities
- Test with different backdrop-blur intensity values
```

### Development and Deployment Troubleshooting

**Problem**: Build failures or TypeScript errors
```bash
# Build troubleshooting:
1. Run `tsc --noEmit` to check for TypeScript errors
2. Verify all dependencies are properly installed with correct versions
3. Check for circular imports causing build failures
4. Review component imports for correct file paths and extensions
5. Validate environment variables are properly configured

# Common build fixes:
- Clear node_modules and reinstall dependencies
- Check package.json for version conflicts
- Verify Next.js configuration for custom webpack settings
- Update TypeScript definitions for third-party libraries
```

**Database Connection and Migration Issues**:
```sql
-- Check database connectivity and performance
SELECT 
  schemaname,
  tablename,
  n_tup_ins as inserts,
  n_tup_upd as updates,
  n_tup_del as deletes
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC;

-- Verify RLS policies are working correctly
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual 
FROM pg_policies 
WHERE schemaname = 'public';

-- Check xfer_* table synchronization status
SELECT 
  table_name,
  COUNT(*) as record_count,
  MAX(updated_at) as last_update
FROM information_schema.tables t
JOIN xfer_entities e ON t.table_name LIKE 'xfer_%'
GROUP BY table_name;
```

## FAQ

### User-Centric Questions

**Q: How do I navigate between different ESG analysis types while keeping my selected company context?**
A: The application automatically preserves your entity selection (company), analysis model, and run parameters across all navigation. When you select a company on the dashboard, all menu items and links will maintain this context. You'll see these parameters in the URL (`?entity=AAPL&run=latest&model=ekoIntelligence`) and they persist across browser sessions.

**Q: What's the difference between "Deep Analysis" and "Quick Analysis" for companies?**
A: Deep Analysis (12-48 hours) performs comprehensive web document discovery and complete ESG analysis from scratch, including new document collection and full pipeline processing. Quick Analysis (5-120 minutes) re-analyzes existing documents with updated models and algorithms, providing faster results for entities already in the system.

**Q: Why do some dashboard sections show "Loading" while others display data?**
A: The platform uses progressive loading for optimal performance. Different analysis types (flags, claims, promises, predictions) load independently from separate database views, allowing you to start exploring available data while other sections continue loading. This prevents any single slow query from blocking the entire interface.

**Q: How do I share my ESG analysis reports with team members?**
A: Document sharing is controlled through the collaborative document editor. Create or open a document in the Documents section, then use the sharing options within the TipTap editor. Team access is managed through your organization's Supabase authentication settings - contact your admin for user access management.

**Q: Can I export my ESG analysis data or reports?**
A: Currently, individual documents can be exported to PDF using your browser's print function (Ctrl/Cmd+P). Dashboard data export functionality for CSV/Excel is planned for Q3 2025. Generated reports are available for download in the Reports section of the dashboard.

**Q: What file types can I upload for ESG document analysis?**
A: The file upload system supports PDF files up to 100MB in size. You can also process documents via URL for web-based sustainability reports. Support for additional formats (DOC, DOCX, TXT) is being developed as part of the platform enhancement roadmap.

**Q: How do I know when my document analysis is complete?**
A: The platform provides real-time status updates through toast notifications and the analysis queue display. You'll see progress indicators in the Analysis section, and receive notifications when processing completes. The dashboard will update automatically with new analysis results.

**Q: Why do I need to select an entity before uploading documents?**
A: All ESG analysis requires entity context for proper categorization, comparison, and insight generation. Documents must be associated with a specific company or organization to enable accurate greenwashing detection, claims analysis, and benchmarking against industry standards.

### Developer and Administrative Questions  

**Q: How do I add a new analysis module to the customer dashboard?**
A: Create a new directory under `/customer/dashboard/` (e.g., `/customer/dashboard/my-module/`), implement `page.tsx` with your analysis interface, add navigation links in the sidebar configuration, update EntityContext if new data types are needed, and test modal integration if required for detail overlays.

**Q: How does the entity context system work across the application?**
A: EntityContext is a React Context provider that manages entity selection, model configuration, and analysis data globally. It automatically syncs with URL parameters (`?entity=AAPL&run=latest&model=ekoIntelligence`) and provides loading states for all analysis types. Access it via the `useEntity()` hook in any component within the customer application.

**Q: How do I customize the glass-morphism design system?**
A: Modify Tailwind CSS utility classes using the custom `glass-effect-*` classes defined in the global CSS. Key classes include `glass-effect-lit` (primary cards), `glass-effect-subtle` (secondary elements), and `glass-effect-brand-strong-lit` (active states). Ensure browser compatibility with `backdrop-filter` CSS property.

**Q: How do parallel routes and intercepting routes work in the dashboard?**
A: The `@modal` directory creates a parallel route slot rendered alongside the main content. Intercepting routes like `(.)flags/[id]` capture navigation to display modal overlays while preserving dashboard context. The layout component renders both `children` (main content) and `modal` (overlay) slots simultaneously.

**Q: How do I optimize performance for users with large datasets?**
A: Implement progressive loading with `useMemo` and `useCallback` for expensive computations, consider virtual scrolling for large lists, optimize database queries with proper indexing, implement client-side caching for frequently accessed data, and use Suspense boundaries strategically to prevent render blocking.

**Q: How does the dual-database architecture affect development?**
A: The analytics database (Python backend) handles complex ESG processing, while the customer database (Supabase) serves optimized data to the frontend. Data flows through `xfer_*` tables that sync processed results. Developers work primarily with customer database views and should understand that complex analysis happens asynchronously in the backend.

**Q: What's the authentication flow for different user types?**
A: All users authenticate through Supabase Auth with email/password. Admin status is determined by email domain (`@ekointelligence.com`). Regular users see standard ESG analysis features, while admins get additional navigation options and management capabilities. Row Level Security (RLS) policies ensure data isolation between organizations.

**Q: How do I add new real-time features to the application?**
A: Use Supabase real-time subscriptions with proper cleanup in `useEffect`. Subscribe to specific tables/rows, implement loading states, handle connection failures gracefully, and ensure RLS policies allow the subscription. Always clean up subscriptions on component unmount to prevent memory leaks.

## References

### Documentation Links

**Next.js Framework & React**:
- [Next.js 15 App Router Documentation](https://nextjs.org/docs/app) - Modern React framework with server components
- [React 18 Server Components](https://react.dev/reference/react/use-server) - Server/client component architecture  
- [Next.js Parallel Routes](https://nextjs.org/docs/app/building-your-application/routing/parallel-routes) - Modal systems and intercepting routes
- [TypeScript 5 Documentation](https://www.typescriptlang.org/docs/) - Type safety and advanced features

**Database & Authentication**:
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript) - Database operations and real-time features
- [Supabase Authentication Guide](https://supabase.com/docs/guides/auth/server-side) - Server-side auth implementation
- [Supabase Row Level Security](https://supabase.com/docs/guides/auth/row-level-security) - Multi-tenant security model
- [PostgreSQL Views Documentation](https://www.postgresql.org/docs/current/sql-createview.html) - Database view optimization

**UI & Design System**:
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - Utility-first CSS framework
- [shadcn/ui Components](https://ui.shadcn.com/docs/components) - Component library and accessibility
- [Radix UI Primitives](https://www.radix-ui.com/primitives) - Accessible component foundations
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react) - SVG icon system

### Related Code Files

**Core Application Structure**:
- [`/apps/customer/app/customer/layout.tsx`](./layout.tsx) - Root application layout with authentication and context
- [`/apps/customer/app/customer/page.tsx`](./page.tsx) - Welcome dashboard with quota monitoring
- [`/apps/customer/app/customer/sidebar.tsx`](./sidebar.tsx) - Navigation sidebar with entity context
- [`/apps/customer/components/context/auth/auth-context.tsx`](../../components/context/auth/auth-context.tsx) - Authentication context provider
- [`/apps/customer/components/context/entity/entity-context.tsx`](../../components/context/entity/entity-context.tsx) - Entity selection and analysis data context

**Feature Module Documentation**:
- [`/apps/customer/app/customer/account/README.md`](./account/README.md) - User account management system
- [`/apps/customer/app/customer/analysis/README.md`](./analysis/README.md) - ESG analysis tools and quota management
- [`/apps/customer/app/customer/dashboard/README.md`](./dashboard/README.md) - Analytics visualization and reporting
- [`/apps/customer/app/customer/documents/README.md`](./documents/README.md) - Document collaboration and editing
- [`/apps/customer/app/customer/files/README.md`](./files/README.md) - File upload and document processing

**Supporting Infrastructure**:
- [`/apps/customer/types/index.ts`](../../types/index.ts) - TypeScript interface definitions
- [`/apps/customer/app/supabase/client.ts`](../../app/supabase/client.ts) - Supabase client configuration
- [`/apps/customer/utils/react-utils.ts`](../../utils/react-utils.ts) - Utility functions and async helpers
- [`/apps/customer/hooks/use-navigation-with-params.ts`](../../hooks/use-navigation-with-params.ts) - Parameter-aware navigation

### Third-Party Dependencies and Resources

**Rich Text Editing & Collaboration**:
- [TipTap Editor Documentation](https://tiptap.dev/docs/editor/introduction) - Collaborative rich text editing
- [TipTap Collaboration Guide](https://tiptap.dev/docs/collaboration/introduction) - Real-time collaborative features
- [React Dropzone Documentation](https://react-dropzone.js.org/) - File drag-and-drop functionality

**External Service Documentation**:
- [AWS S3 Presigned URLs](https://docs.aws.amazon.com/AmazonS3/latest/userguide/PresignedUrlUploadObject.html) - Secure file upload system
- [Google Gemini AI Documentation](https://ai.google.dev/gemini-api/docs) - AI content generation integration
- [Slack Webhook Integration](https://api.slack.com/messaging/webhooks) - Contact form and notification system

**ESG & Sustainability Framework References**:
- [UN Sustainable Development Goals](https://sdgs.un.org/goals) - Official SDG framework implementation
- [SASB Standards](https://sasb.org/standards/) - Sustainability accounting standards
- [GRI Sustainability Reporting](https://www.globalreporting.org/standards/) - Global reporting framework
- [Task Force on Climate-related Financial Disclosures (TCFD)](https://www.fsb-tcfd.org/) - Climate disclosure framework

### Platform Architecture Resources

**Monorepo & Development Environment**:
- [`/CLAUDE.md`](../../../../CLAUDE.md) - Project-wide development guidelines and architecture
- [`/apps/customer/README.md`](../../README.md) - Customer application overview and setup
- [`/backoffice/README.md`](../../../../backoffice/README.md) - Python analytics backend documentation
- [`/packages/ui/README.md`](../../../../packages/ui/README.md) - Shared UI component library

**Linear Project Management**:
- [EKO-238: Agentic Scraper](https://linear.app/ekointelligence/issue/EKO-238/agentic-scraper) - Current development focus
- [EKO-291: Claims Page Navigation](https://linear.app/ekointelligence/issue/EKO-291/claims-page) - Resolved navigation issues
- [EKO-232: JSON Parsing Error](https://linear.app/ekointelligence/issue/EKO-232/json-object-must-be-str-bytes-or-bytearray-not-dict) - Resolved backend processing issue

**Testing & Quality Assurance**:
- [`/apps/customer/tests/`](../../tests/) - Playwright E2E test suite
- [Playwright Documentation](https://playwright.dev/) - End-to-end testing framework
- [Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/) - Component testing utilities

---

## Changelog

### 2025-07-31

- **CREATED**: Comprehensive README.md documentation for customer application main platform hub
- **ANALYZED**: Complete application architecture including authentication, routing, and context management
- **DOCUMENTED**: All five core feature modules (account, analysis, dashboard, documents, files) with cross-references
- **RESEARCHED**: Linear ticket integration for current issues and development context (EKO-238, EKO-291, EKO-232)
- **SPECIFIED**: Next.js 15 App Router implementation with server/client component architecture
- **DETAILED**: Dual-database architecture with analytics backend synchronization via xfer_ tables
- **MAPPED**: Glass-morphism design system implementation with performance-optimized CSS patterns
- **PROVIDED**: Comprehensive troubleshooting guide with database queries and performance monitoring
- **CREATED**: FAQ section addressing both user workflows and developer implementation questions
- **COMPILED**: Extensive reference documentation linking to framework docs, internal files, and external resources
- **INCLUDED**: Mermaid architecture diagrams for system integration, authentication flow, and context management

---

(c) All rights reserved ekoIntelligence 2025