# Document Management Module

This directory contains the Next.js 15 App Router document management system for the EkoIntelligence ESG analysis platform. It provides comprehensive document lifecycle management including creation, viewing, editing, collaboration, and integration with ESG entity analysis data.

## Overview

The document management module serves as the central hub for creating, managing, and collaborating on ESG analysis documents within the EkoIntelligence customer application. Built on Next.js 15 App Router with Supabase backend integration, it provides a modern, real-time collaborative document editing experience with TipTap rich text editor and seamless integration with the ESG analysis pipeline.

**Key Features:**
- **Rich Text Document Editor**: TipTap-based collaborative editing with AI tools and export capabilities
- **Template-Based Creation**: Dynamic and static templates for various ESG document types
- **Real-time Collaboration**: Multi-user editing with comments, sharing, and presence indicators
- **Entity Integration**: Documents linked to specific ESG entities and analysis runs
- **Auto-save & Sync**: Automatic document synchronization with conflict resolution
- **Citation Management**: Automated extraction and placeholder generation for document references
- **AI Content Generation**: Integrated AI tools for ESG content generation and editing assistance

## Specification

The document management system implements a three-tier architecture:

### Route Structure
```
/customer/documents/          # Main document listing and management
/customer/documents/new       # New document creation with template selection
/customer/documents/[id]      # Individual document editor with collaboration features
```

### Database Schema
**Primary Table**: `doc_documents` in customer database
```sql
CREATE TABLE doc_documents (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  title text NOT NULL,
  content text,                    -- Current document content (HTML/rich text)
  initial_content text,            -- Original template content for version tracking
  data jsonb,                      -- TipTap editor state and rich formatting data
  metadata jsonb DEFAULT '{}',     -- Citations, collaboration settings, template references
  entity_id text NOT NULL,        -- Associated ESG entity identifier
  run_id integer NOT NULL,        -- Analysis run identifier for data snapshots
  created_by uuid REFERENCES profiles(id),
  updated_by uuid REFERENCES profiles(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

### Authentication & Security
- **Row Level Security (RLS)**: Supabase policies ensure users access only authorized documents
- **User Authentication**: Valid Supabase user session required for all operations
- **Document Ownership**: Clear distinction between owned and shared documents
- **Permission-based Features**: Feature flags control access to collaboration and AI tools

## Key Components

### 1. Main Document Listing (`page.tsx`)
**File**: [`/apps/customer/app/customer/documents/page.tsx`]
- **Purpose**: Primary document management interface with listing, search, and filtering
- **Features**: Real-time document updates, template-based creation, search functionality
- **Key Functions**:
  - `loadDocuments()`: Fetches user documents with RLS filtering
  - `handleCreateDocument()`: Opens template selection dialog
  - `handleSelectTemplate()`: Creates new document from template with entity association
  - `handleDeleteDocument()`: Owner-only document removal

### 2. Document Creation (`new/page.tsx`)
**File**: [`/apps/customer/app/customer/documents/new/page.tsx`]
- **Purpose**: New document creation page with template selection interface
- **Features**: Template gallery, entity association, database integration
- **Key Functions**:
  - `handleSelectTemplate()`: Creates document with UUID generation and database insertion
  - `validateDocumentCreationParams()`: Ensures valid entity/run combinations

### 3. Document Editor (`[id]/page.tsx`)
**File**: [`/apps/customer/app/customer/documents/[id]/page.tsx`]
- **Purpose**: Individual document editing interface with comprehensive features
- **Features**: TipTap editor integration, real-time collaboration, auto-save, citation management
- **Key Functions**:
  - `handleSave()`: Debounced auto-save with conflict resolution
  - `extractCitationsFromContent()`: Automatic citation extraction from document content
  - `useNavigationAutoSave()`: Navigation-aware auto-save hook

## Dependencies

### Core Framework Dependencies
- **Next.js 15**: Modern React framework with App Router for client and server-side routing
- **React 18**: Client-side interactivity with hooks, state management, and concurrent features
- **TypeScript**: Full type safety for component interfaces and data structures

### Backend & Database
- **Supabase**: 
  - Database client for document storage and user authentication
  - Real-time subscriptions for live document updates
  - Row Level Security (RLS) for access control
  - Object storage for file attachments and exports

### Rich Text Editor
- **TipTap Editor**: 
  - Collaborative rich text editing with extensions
  - Real-time collaboration with comments and shared cursors
  - Custom extensions for ESG-specific content blocks
  - Export functionality (HTML, Markdown, Word)

### UI Framework
- **Shadcn/UI**: Consistent component library with accessibility support
- **Tailwind CSS**: Utility-first styling with responsive design system
- **Lucide Icons**: Comprehensive icon set for interface elements
- **Radix UI**: Accessible primitives for complex interactions

### Utility Libraries
- **Date Utilities**: Relative time formatting and timestamp handling
- **Document Utilities**: Validation, parameter processing, and content management
- **Toast Notifications**: User feedback system for operations and errors

## Usage Examples

### Creating a New Document
```typescript
// Navigate to document creation
router.push('/customer/documents/new')

// Template selection with entity context
router.push('/customer/documents/new?entity=AAPL&run=latest')
```

### Document Management Operations
```typescript
// Access main documents page
// Navigate to: /customer/documents

// Search documents
setSearchQuery("ESG Report") // Filters documents in real-time

// Toggle shared documents view
setShowShared(true) // Shows documents shared by other users

// Navigate to document editor
router.push(`/customer/documents/${documentId}`)
```

### Template-based Document Creation
```typescript
// Template selection with entity association
handleSelectTemplate(
  template,      // Template object with content/data
  "entity_123",  // Optional entity ID
  "run_456"      // Optional run ID
)
```

## Architecture Notes

### System Integration
```mermaid
graph TB
    A[Customer Application] --> B[Document Management Module]
    B --> C[Next.js App Router]
    B --> D[Supabase Database]
    B --> E[TipTap Editor]
    B --> F[Template System]
    
    C --> G[Document Listing]
    C --> H[Document Creation]
    C --> I[Document Editor]
    
    D --> J[doc_documents Table]
    D --> K[Real-time Subscriptions]
    D --> L[Row Level Security]
    
    E --> M[Rich Text Editing]
    E --> N[Real-time Collaboration]
    E --> O[AI Content Tools]
    
    F --> P[Static Templates]
    F --> Q[Dynamic Templates]
    F --> R[Entity-specific Templates]
```

### Data Flow Architecture
```mermaid
sequenceDiagram
    participant U as User
    participant C as Client Component
    participant S as Supabase
    participant D as Database
    participant E as ESG Analytics

    U->>C: Create New Document
    C->>S: Authenticate User
    S->>C: Return User Session
    C->>C: Select Template
    C->>E: Validate Entity/Run
    E->>C: Return Validation
    C->>D: Insert Document Record
    D->>S: Real-time Notification
    S->>C: Document Created
    C->>U: Navigate to Editor
```

### State Management Flow
```mermaid
stateDiagram-v2
    [*] --> Loading
    Loading --> DocumentListing: Documents Loaded
    DocumentListing --> TemplateSelection: Create Document
    TemplateSelection --> DocumentCreation: Template Selected
    DocumentCreation --> DocumentEditor: Document Created
    DocumentEditor --> DocumentEditor: Auto-save Loop
    DocumentEditor --> DocumentListing: Navigate Back
    DocumentListing --> DocumentEditor: Open Existing
```

## Known Issues

Based on Linear ticket analysis and code review:

### High Priority Issues
- **EKO-218**: Document templates need proper titles in `<h1>` tags including entity names
- **EKO-182**: Table formatting issues where LLM-generated tables get extra empty cells and blank rows
- **EKO-265**: Chart rendering failures need better error handling with `[chart missing]` fallbacks

### Medium Priority Issues  
- **EKO-239**: PDF export needs to preserve links and improve formatting
- **EKO-233**: Print dialog renders documents as editable artifacts instead of clean output
- **EKO-246**: PDF filenames should match document titles instead of default naming

### Feature Enhancement Opportunities
- **EKO-254**: Report extension menus behind feature flags for better UX control
- Citation system improvements for better academic reference handling
- Enhanced collaboration features for enterprise workflows

## Future Work

### Immediate Priorities (Next Sprint)
1. **Template Title Integration** (EKO-218): Implement dynamic `<h1>` title generation in all document templates
2. **Table Formatting Fix** (EKO-182): Resolve HTML table parsing issues in TipTap editor
3. **Chart Error Handling** (EKO-265): Add graceful chart failure handling with placeholder text

### Medium-term Enhancements
1. **Advanced PDF Export** (EKO-239): Implement jsPDF-based export with link preservation
2. **Print View Optimization** (EKO-233): Create dedicated print layouts without editor artifacts
3. **Enhanced Citation Management**: Automatic citation validation and academic formatting
4. **Collaboration Improvements**: Advanced commenting, suggestion modes, and version control

### Long-term Strategic Goals
1. **Multi-language Support**: Internationalization for global ESG reporting
2. **Advanced Analytics**: Document engagement metrics and collaboration insights
3. **Integration Enhancements**: Direct integration with external ESG data providers
4. **Mobile Optimization**: Responsive editing experience for mobile devices
5. **Offline Capabilities**: Progressive Web App features for offline document editing

## Troubleshooting

### Common Issues

**Document Not Loading**
- Verify user authentication and document permissions
- Check RLS policies in Supabase dashboard
- Ensure valid entity_id and run_id associations

**Auto-save Failures**
- Check network connectivity and Supabase connection
- Verify document ownership and write permissions
- Monitor browser console for JavaScript errors

**Template Selection Issues**
- Validate entity/run parameter combinations
- Check template configuration and content format
- Ensure proper template metadata structure

**Editor Performance Issues**
- Clear browser cache and local storage
- Check for TipTap extension conflicts
- Monitor memory usage for large documents

### Debug Steps

1. **Authentication Issues**:
   ```typescript
   // Check user session
   const { data: { user } } = await supabase.auth.getUser()
   console.log('Current user:', user)
   ```

2. **Database Connection**:
   ```typescript
   // Test Supabase connection
   const { data, error } = await supabase.from('doc_documents').select('count')
   console.log('Connection test:', { data, error })
   ```

3. **Document Loading**:
   ```typescript
   // Debug document fetch
   const { data, error } = await supabase
     .from('doc_documents')
     .select('*')
     .eq('id', documentId)
     .single()
   console.log('Document fetch:', { data, error })
   ```

## FAQ

### User-Centric Questions

**Q: How do I create a new document?**
A: Click the "New Document" button on the main documents page, select a template that matches your ESG reporting needs, choose the relevant entity and analysis run, then click "Create Document" to start editing.

**Q: Can multiple people edit the same document simultaneously?**
A: Yes, the document editor supports real-time collaboration. Multiple users can edit simultaneously with live cursors, comments, and automatic conflict resolution.

**Q: How do I export my document to PDF?**
A: Use the browser's print function (Ctrl/Cmd+P) and select "Save as PDF". The system automatically formats the document for clean PDF output with preserved links and formatting.

**Q: What happens if I lose internet connection while editing?**
A: The editor automatically saves your changes locally and will sync them when your connection is restored. You'll see an "Offline" indicator in the header during disconnection.

**Q: How do I associate a document with a specific company or analysis run?**
A: When creating a new document, select the appropriate entity (company) and analysis run from the template selection dialog. Existing documents can be updated through document settings.

**Q: Can I search through my documents?**
A: Yes, use the search bar on the main documents page to search through document titles and author names. You can also filter between "My Documents" and "Shared" documents.

**Q: How do I share a document with team members?**
A: Document sharing is controlled through Supabase Row Level Security policies. Contact your administrator to configure sharing permissions for specific users or organizations.

**Q: What document templates are available?**
A: The system provides both static templates for common ESG report types and dynamic templates that auto-populate with entity-specific data based on your selected company and analysis run.

## References

### Documentation Links
- [Next.js 15 App Router Documentation](https://nextjs.org/docs/app)
- [Supabase Database Documentation](https://supabase.com/docs/guides/database)
- [TipTap Editor Documentation](https://tiptap.dev/docs/editor/introduction)
- [Shadcn/UI Component Library](https://ui.shadcn.com/docs)

### Related Code Files
- [`/apps/customer/components/editor/EkoDocumentEditor.tsx`] - Main TipTap editor integration
- [`/apps/customer/components/editor/templates/DocumentTemplates.tsx`] - Template selection interface
- [`/apps/customer/components/context/auth/auth-context.tsx`] - Authentication context provider
- [`/apps/customer/utils/document-utils.ts`] - Document validation and processing utilities
- [`/apps/customer/utils/date-utils.ts`] - Date formatting and time utilities

### Third-party Dependencies
- [Next.js App Router](https://nextjs.org/docs/app/building-your-application/routing)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [TipTap Rich Text Editor](https://tiptap.dev/docs/editor/getting-started)
- [React 18 Documentation](https://react.dev/reference/react)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)

### External Resources
- [ESG Reporting Standards](https://www.globalreporting.org/standards/)
- [Collaborative Editing Best Practices](https://yjs.dev/)
- [Web Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

---

## Changelog

### 2025-07-31
- **Initial Documentation**: Created comprehensive README.md for document management module
- **Architecture Documentation**: Added detailed system architecture diagrams and component specifications
- **Issue Integration**: Documented known issues from Linear tickets with priorities and solutions
- **User Guide**: Added comprehensive FAQ and troubleshooting sections
- **Future Roadmap**: Outlined immediate, medium-term, and long-term development priorities

(c) All rights reserved ekoIntelligence 2025