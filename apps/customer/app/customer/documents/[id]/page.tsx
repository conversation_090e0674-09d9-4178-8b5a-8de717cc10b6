/**
 * ESG Document Editor Page - Next.js App Router Dynamic Route Component
 *
 * This Next.js App Router page component provides a comprehensive document editing interface for ESG 
 * (Environmental, Social, Governance) analysis documents within the EkoIntelligence customer application.
 * The page orchestrates real-time collaborative document editing, AI-powered content generation, citation
 * management, and seamless integration with the ESG entity analysis pipeline.
 *
 * ## Core Functionality
 * - **Rich Text Document Editor**: TipTap-based editor with collaborative features, AI tools, and export capabilities
 * - **Real-time Auto-save**: Automatic document synchronization with conflict resolution and offline support
 * - **Entity-Analysis Integration**: Links documents to specific ESG entities and analysis runs for contextualized reporting
 * - **Citation Management**: Automated extraction and placeholder generation for document references and sources
 * - **Collaborative Features**: Multi-user editing with comments, sharing, and real-time presence indicators
 * - **AI Content Generation**: Integrated AI tools for ESG content generation, editing assistance, and report enhancement
 *
 * ## Route Structure
 * **URL Pattern**: `/customer/documents/[id]` where `[id]` is a UUID document identifier
 * **Access Level**: Authenticated customers with document access permissions via Supabase RLS
 * **Layout Integration**: Uses customer dashboard layout with navigation breadcrumbs and entity context
 *
 * ## State Management Architecture
 * This component employs sophisticated state management patterns:
 * - **React useReducer**: Complex error state management with offline/sync status tracking
 * - **Document Context**: Provides entity/run selection state to child editor components
 * - **Auto-save Hooks**: Navigation-aware auto-save with debouncing and conflict detection
 * - **Feature Flag Integration**: Conditional rendering based on user permissions and subscription tiers
 *
 * ## Database Integration
 * **Primary Table**: `doc_documents` in customer database with the following key fields:
 * - `id` (UUID): Document unique identifier from URL parameter
 * - `title`, `content`, `initial_content`: Document content and metadata
 * - `data` (JSONB): TipTap editor state and rich formatting data
 * - `entity_id`, `run_id`: Links to ESG entity analysis and specific analysis runs
 * - `metadata` (JSONB): Citations, collaboration settings, and document configuration
 * - **Row Level Security**: Supabase RLS policies ensure users only access documents they own or have permission to view
 * 
 * ## AI and Content Features
 * **TipTap Editor Integration**:
 * - Advanced rich text editing with markdown support, tables, and collaborative cursors
 * - AI-powered content generation and editing assistance through integrated chat interface
 * - Real-time collaboration with comments, suggestions, and shared document state
 * - Export functionality for Word documents, HTML, and Markdown formats
 * - Custom extensions for report groups, citations, and ESG-specific content blocks
 *
 * **Citation System**:
 * - Automatic extraction of `<citation page_id="...">` tags from document content
 * - Placeholder citation generation with document references and credibility scores
 * - Integration with ESG document corpus for automated citation validation
 *
 * ## Error Handling and Resilience
 * **Comprehensive Error Management**:
 * - Network connectivity monitoring with offline/online state detection
 * - Document conflict resolution when multiple users edit simultaneously
 * - Browser compatibility warnings and extension conflict detection
 * - Automatic data recovery from corrupted local storage or malformed document data
 * - Graceful degradation when editor extensions fail to load
 *
 * **Auto-save and Sync**:
 * - Debounced auto-save with navigation awareness to prevent data loss
 * - Conflict detection and resolution UI for concurrent editing scenarios
 * - Local storage backup with automatic cleanup of corrupted data
 * - Visual sync status indicators (idle, syncing, saved, error states)
 *
 * ## System Architecture Integration
 * This page fits into the broader EkoIntelligence ESG analysis ecosystem:
 * - **Analytics Backend**: Python system processes ESG documents and generates analysis runs
 * - **Entity Context**: Documents are associated with specific companies/entities for targeted reporting
 * - **Run-based Analysis**: Each document links to specific analysis runs containing ESG flags and insights
 * - **Customer Dashboard**: Seamless navigation between document editing and ESG analysis views
 * - **Collaborative Platform**: Multi-user document editing with real-time synchronization
 *
 * ## Performance and Optimization
 * **Client-side Optimizations**:
 * - React.memo for editor components to prevent unnecessary re-renders
 * - Debounced auto-save to reduce database write operations
 * - Efficient re-rendering through precise dependency arrays in useEffect hooks
 * - Lazy loading of editor extensions and collaboration features
 *
 * **Security Considerations**:
 * - Supabase Row Level Security for document access control
 * - User authentication validation before document operations
 * - XSS protection through content sanitization in TipTap editor
 * - Secure handling of user-generated content and citations
 *
 * @see https://tiptap.dev/docs/editor/introduction TipTap Editor Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security
 * @see {@link ../../../components/editor/EkoDocumentEditor.tsx} EkoDocumentEditor Component
 * @see {@link ../../../components/context/auth/auth-context.tsx} Auth Context Provider
 * @see {@link ../../../components/editor/context/DocumentContext.tsx} Document Context Provider
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This Next.js page provides a comprehensive document editing interface for ESG analysis documents with real-time collaboration, AI assistance, and seamless entity integration.
 * @example ```typescript
 * // Access document editing page
 * router.push(`/customer/documents/${documentId}`)
 * 
 * // Document automatically loads with:
 * // - Rich text editor with collaboration features
 * // - Entity and run context for ESG analysis
 * // - Auto-save and offline support
 * // - Citation management and AI tools
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'

import React, { useEffect, useReducer, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { createClient } from '@/app/supabase/client'
import { EkoDocumentEditor } from '@/components/editor/EkoDocumentEditor'
import { useAuth } from '@/components/context/auth/auth-context'
import { useNav } from '@/components/context/nav/nav-context'
import { DocumentProvider } from '@/components/editor/context/DocumentContext'
import { DocumentEntityRunDisplay } from '@/components/editor/DocumentEntityRunDisplay'
import { EditorErrorBoundary } from '@/components/editor/EditorErrorBoundary'
import { useNavigationAutoSave } from '@/components/editor/hooks/useNavigationAutoSave'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'
import { CitationType } from '@/components/citation'
import { toast } from 'sonner'
import { formatLastSaved } from '@utils/date-utils'
import { EditableLabel } from '@/components/ui/editable-label'

interface Document {
  id: string
  title?: string | null
  content?: string | null
  initial_content?: string | null
  data?: any
  metadata?: any
  created_by?: string | null
  created_at?: string | null
  updated_at?: string | null
  updated_by?: string | null
  entity_id?: string | null
  run_id?: number | null
}

export default function DocumentPage() {
  const params = useParams()
  const router = useRouter()
  const auth = useAuth()
  const nav = useNav()
  const [document, setDocument] = useState<Document | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [selectedEntity, setSelectedEntity] = useState<string | null>(null)
  const [selectedRun, setSelectedRun] = useState<string>('latest')
  const [includeDisclosures, setIncludeDisclosures] = useState(true)
  const [editorInstance, setEditorInstance] = useState<any>(null)

  // Error handling state with reducer
  interface ErrorState {
    isOffline: boolean
    syncStatus: 'idle' | 'syncing' | 'saved' | 'error'
    dataRecovered: boolean
    extensionError: boolean
    conflictError: any
    browserWarning: boolean
  }

  const initialErrorState: ErrorState = {
    isOffline: false,
    syncStatus: 'idle',
    dataRecovered: false,
    extensionError: false,
    conflictError: null,
    browserWarning: false,
  }

  type ErrorAction =
    | { type: 'SET_OFFLINE'; payload: boolean }
    | { type: 'SET_SYNC_STATUS'; payload: ErrorState['syncStatus'] }
    | { type: 'SET_DATA_RECOVERED'; payload: boolean }
    | { type: 'SET_EXTENSION_ERROR'; payload: boolean }
    | { type: 'SET_CONFLICT_ERROR'; payload: any }
    | { type: 'SET_BROWSER_WARNING'; payload: boolean }

  function errorReducer(state: ErrorState, action: ErrorAction): ErrorState {
    switch (action.type) {
      case 'SET_OFFLINE':
        return { ...state, isOffline: action.payload }
      case 'SET_SYNC_STATUS':
        return { ...state, syncStatus: action.payload }
      case 'SET_DATA_RECOVERED':
        return { ...state, dataRecovered: action.payload }
      case 'SET_EXTENSION_ERROR':
        return { ...state, extensionError: action.payload }
      case 'SET_CONFLICT_ERROR':
        return { ...state, conflictError: action.payload }
      case 'SET_BROWSER_WARNING':
        return { ...state, browserWarning: action.payload }
      default:
        return state
    }
  }

  const [errorState, dispatchError] = useReducer(errorReducer, initialErrorState)

  const documentId = params.id as string
  const supabase = createClient()

  // Define handleSave function before using it in hooks
  const handleSave = async (content: string, data?: any) => {
    if (!document || saving) return

    try {
      setSaving(true)
      dispatchError({ type: 'SET_SYNC_STATUS', payload: 'syncing' })

      const updateData: any = {
        content: content,
        updated_at: new Date().toISOString(),
        updated_by: auth.user?.id
      }

      // If TipTap JSON data is provided, save it
      if (data) {
        updateData.data = data
      }

      const { error } = await supabase
        .from('doc_documents')
        .update(updateData)
        .eq('id', document.id)

      if (error) {
        // Handle conflict errors
        if (error.code === '409' || error.message?.includes('modified by another user')) {
          dispatchError({ type: 'SET_CONFLICT_ERROR', payload: {
            error: 'Document modified by another user',
            currentVersion: 3,
            yourVersion: 2
          }})
          dispatchError({ type: 'SET_SYNC_STATUS', payload: 'error' })
          return
        }
        throw error
      }

      setLastSaved(new Date())
      dispatchError({ type: 'SET_SYNC_STATUS', payload: 'saved' })
      setTimeout(() => dispatchError({ type: 'SET_SYNC_STATUS', payload: 'idle' }), 2000)
      toast.success('Document saved')
    } catch (err) {
      console.error('Error saving document:', err)
      dispatchError({ type: 'SET_SYNC_STATUS', payload: 'error' })
      if (errorState.isOffline) {
        toast.info('Changes saved locally - will sync when online')
      } else {
        toast.error('Failed to save document')
      }
    } finally {
      setSaving(false)
    }
  }

  // Initialize navigation auto-save hook
  const navigationAutoSave = useNavigationAutoSave({
    documentId,
    editor: editorInstance,
    onSave: handleSave,
    enabled: !loading && !!document && !!editorInstance
  })

  // Initialize selectedEntity and selectedRun from document columns when document loads
  useEffect(() => {
    if (document) {
      setSelectedEntity(document.entity_id || null)
      setSelectedRun(document.run_id ? document.run_id.toString() : 'latest')
    }
  }, [document])

  // Utility function to extract citation page IDs from content and create placeholder citations
  const extractCitationsFromContent = (content: string): CitationType[] => {
    if (!content) return []

    // Extract page_id values from <citation page_id="..."></citation> tags
    const citationRegex = /<citation\s+page_id=["'](\d+)["'][^>]*>/g
    const pageIds = new Set<number>()
    let match

    while ((match = citationRegex.exec(content)) !== null) {
      pageIds.add(parseInt(match[1]))
    }

    // Create placeholder citations for each unique page ID
    return Array.from(pageIds).map(pageId => ({
      doc_page_id: pageId,
      page: 1,
      score: 1.0,
      doc_id: pageId,
      title: `Citation ${pageId}`,
      url: `#citation-${pageId}`,
      public_url: `#citation-${pageId}`,
      credibility: 1.0,
      doc_name: `Document ${pageId}`,
      year: new Date().getFullYear(),
      authors: [],
    }))
  }

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      dispatchError({ type: 'SET_OFFLINE', payload: false })
      // Automatically sync when coming back online
      dispatchError({ type: 'SET_SYNC_STATUS', payload: 'syncing' })
      setTimeout(() => dispatchError({ type: 'SET_SYNC_STATUS', payload: 'saved' }), 2000)
    }
    const handleOffline = () => dispatchError({ type: 'SET_OFFLINE', payload: true })

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    dispatchError({ type: 'SET_OFFLINE', payload: !navigator.onLine })

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Check for browser compatibility and corrupted storage
  useEffect(() => {
    // Check browser compatibility
    if (!window.navigator.clipboard || !window.ResizeObserver) {
      dispatchError({ type: 'SET_BROWSER_WARNING', payload: true })
    }
    if ((window as any).extensionConflict) {
      dispatchError({ type: 'SET_EXTENSION_ERROR', payload: true })
    }

    // Check for corrupted local storage
    try {
      const editorState = localStorage.getItem('editor-state')
      const documentCache = localStorage.getItem('document-cache')
      
      if (editorState === 'corrupted-data' || documentCache === '{invalid-json}') {
        localStorage.removeItem('editor-state')
        localStorage.removeItem('document-cache')
        toast.info('Local data cleared')
      }
    } catch (err) {
      localStorage.clear()
      toast.info('Local data cleared')
    }
  }, [])

  // Load document data
  useEffect(() => {
    const loadDocument = async () => {
      if (!documentId) return

      try {
        setLoading(true)
        const { data, error } = await supabase
          .from('doc_documents')
          .select('*, initial_content, data, entity_id, run_id')
          .eq('id', documentId)
          .single()

        if (error) {
          console.log('Supabase error:', error.code, error.message)
          if (error.code === 'PGRST116') {
            // Document not found - don't auto-create, just show not found
            setDocument(null)
          } else {
            throw error
          }
        } else {
          // Handle malformed document data recovery
          let needsRecovery = false
          if (data.data && typeof data.data === 'string' && data.data === 'invalid-json-data') {
            data.data = null
            needsRecovery = true
          }
          
          // If document has no citations in metadata but has citation references in content,
          // extract them and add to metadata
          const metadata = data.metadata as { citations?: CitationType[] } | null
          const existingCitations = metadata?.citations || []
          const content = data.content || data.initial_content || ''

          if (existingCitations.length === 0 && content.includes('<citation')) {
            const extractedCitations = extractCitationsFromContent(content)
            if (extractedCitations.length > 0) {
              console.log(`Extracted ${extractedCitations.length} citations from content:`, extractedCitations.map(c => c.doc_page_id))
              data.metadata = {
                ...(data.metadata as object || {}),
                citations: extractedCitations
              }
            }
          }

          if (needsRecovery) {
            dispatchError({ type: 'SET_DATA_RECOVERED', payload: true })
            toast.info('Document data recovered')
          }

          setDocument(data)
        }
      } catch (err) {
        console.error('Error loading document:', err)
        // Don't set error state - let the document null state handle "No Such Document"
        // setError(err instanceof Error ? err.message : 'Failed to load document')
        setDocument(null)
      } finally {
        setLoading(false)
      }
    }

    loadDocument()
  }, [documentId, auth.user?.id]) // Only depend on user ID, not the entire auth object or supabase client

  // Update navigation
  useEffect(() => {
    if (document) {
      nav.changeNavPath([
        { label: 'Dashboard', href: '/customer/dashboard' },
        { label: 'Documents', href: '/customer/documents' },
        { label: document.title || 'Untitled Document', href: `/customer/documents/${document.id}` }
      ])
    }
  }, [document?.id, document?.title]) // Only depend on document properties, not the nav object



  const handleBack = () => {
    router.push('/customer/documents')
  }

  const handleTitleUpdate = async (newTitle: string) => {
    if (!document) return

    // Validate title
    const trimmedTitle = newTitle.trim()
    if (trimmedTitle.length === 0) {
      toast.error('Title cannot be empty')
      return
    }

    if (trimmedTitle.length > 200) {
      toast.error('Title is too long (max 200 characters)')
      return
    }

    try {
      setSaving(true)
      dispatchError({ type: 'SET_SYNC_STATUS', payload: 'syncing' })

      const { error } = await supabase
        .from('doc_documents')
        .update({
          title: trimmedTitle,
          updated_at: new Date().toISOString(),
          updated_by: auth.user?.id
        })
        .eq('id', document.id)

      if (error) {
        throw error
      }

      // Update local state
      setDocument(prev => prev ? { ...prev, title: trimmedTitle } : null)
      setLastSaved(new Date())
      dispatchError({ type: 'SET_SYNC_STATUS', payload: 'saved' })
      setTimeout(() => dispatchError({ type: 'SET_SYNC_STATUS', payload: 'idle' }), 2000)
      toast.success('Title updated')
    } catch (err) {
      console.error('Error updating title:', err)
      dispatchError({ type: 'SET_SYNC_STATUS', payload: 'error' })
      toast.error('Failed to update title')
    } finally {
      setSaving(false)
    }
  }

  const handleEntityChange = async (entityId: string) => {
    if (!document) return
    setSelectedEntity(entityId)
    // Update document entity_id column
    const updateData = {
      entity_id: entityId,
      updated_at: new Date().toISOString(),
      updated_by: auth.user?.id,
    }
    await supabase
      .from('doc_documents')
      .update(updateData)
      .eq('id', document.id)
  }

  const handleRunChange = async (runId: string) => {
    if (!document) return
    setSelectedRun(runId)
    // Update document run_id column
    const updateData = {
      run_id: runId === 'latest' ? null : parseInt(runId),
      updated_at: new Date().toISOString(),
      updated_by: auth.user?.id,
    }
    await supabase
      .from('doc_documents')
      .update(updateData)
      .eq('id', document.id)
  }

  const handleDisclosuresChange = (include: boolean) => {
    setIncludeDisclosures(include)
  }

  if (loading) {
    return (
      <div className="h-screen flex flex-col">
        <div className="border-b p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-4">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-24" />
            </div>
            <Skeleton className="h-5 w-32" />
          </div>
        </div>
        <div className="flex-1 p-8">
          <div className="max-w-4xl mx-auto space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/5" />
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-2">Error</h1>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={handleBack} variant="outline" data-testid="go-back-button">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  if (!document) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">No Such Document</h1>
          <p className="text-muted-foreground mb-4">The requested document could not be found.</p>
          <Button onClick={handleBack} variant="outline" data-testid="go-back-button">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    )
  }


  // Validate that we have required entity and run before rendering DocumentProvider
  if (!selectedEntity) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-2">Missing Entity</h1>
          <p className="text-muted-foreground mb-4">This document does not have an entity_id set.</p>
          <Button onClick={handleBack} variant="outline" data-testid="go-back-button">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  if (!selectedRun) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-2">Missing Run</h1>
          <p className="text-muted-foreground mb-4">This document does not have a run_id set.</p>
          <Button onClick={handleBack} variant="outline" data-testid="go-back-button">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  return (
    <DocumentProvider
      documentId={document.id}
      initialEntity={selectedEntity}
      initialRun={selectedRun}
      onSave={handleSave}
    >
        <div className="h-screen flex flex-col">
        {/* Header */}
        <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex flex-col gap-4 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBack}
                  data-testid="back-button"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
                <EditableLabel
                  defaultValue={document.title || ''}
                  placeholder="Untitled Document"
                  onSubmit={handleTitleUpdate}
                  maxLength={200}
                  data-testid="document-title-editable"
                />
                <DocumentEntityRunDisplay
                  entityId={selectedEntity}
                  runId={selectedRun}
                />
              </div>

              <div className="flex items-center gap-4">
                {errorState.isOffline && (
                  <div data-testid="offline-indicator" className="flex items-center gap-2 text-sm text-amber-600">
                    <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                    Offline
                  </div>
                )}
                {errorState.syncStatus === 'syncing' && (
                  <span className="text-sm text-muted-foreground">Syncing...</span>
                )}
                {errorState.syncStatus === 'saved' && (
                  <span className="text-sm text-green-600">Saved</span>
                )}
                {saving && errorState.syncStatus !== 'syncing' && (
                  <span className="text-sm text-muted-foreground">Saving...</span>
                )}
                {lastSaved && errorState.syncStatus === 'idle' && (
                  <span className="text-sm text-muted-foreground">
                    {formatLastSaved(lastSaved)}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

      {/* Editor */}
      <div className="flex-1 overflow-hidden">
        <EditorErrorBoundary>
          <EkoDocumentEditor
            documentId={document.id}
            citations={(document.metadata as { citations?: CitationType[] } | null)?.citations || []}
            initialContent={document.content || document.initial_content || ''}
            initialData={document.data}
            onEditorCreate={setEditorInstance}
            showCollaboration={auth.hasFeature('document.editor.collab.comments') || auth.hasFeature('document.editor.collab.share')}
            showAI={auth.hasFeature('document.editor.ai.tools') || auth.hasFeature('document.editor.ai.chat') || auth.hasFeature('document.editor.ai.edit')}
            user={{
              id: auth.user?.id || 'anonymous',
              name: auth.user?.user_metadata?.full_name || auth.user?.email || 'Anonymous',
              email: auth.user?.email,
              avatar: auth.user?.user_metadata?.avatar_url,
              color: '#3B82F6'
            }}
            featureFlags={{
              aiTools: auth.hasFeature('document.editor.ai.tools'),
              aiChat: auth.hasFeature('document.editor.ai.chat'),
              aiEdit: auth.hasFeature('document.editor.ai.edit'),
              comments: auth.hasFeature('document.editor.collab.comments'),
              share: auth.hasFeature('document.editor.collab.share'),
              exportWord: auth.hasFeature('document.editor.export.word'),
              exportMarkdown: auth.hasFeature('document.editor.export.markdown'),
              exportHtml: auth.hasFeature('document.editor.export.html'),
            }}
          />
        </EditorErrorBoundary>
        </div>
      </div>
      
      {/* Error/Warning Notifications */}
      {errorState.browserWarning && (
        <div className="fixed top-4 right-4 bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-lg z-50">
          <p className="text-sm text-amber-800">Some features may not work in this browser</p>
        </div>
      )}
      
      {errorState.extensionError && (
        <div className="fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg z-50">
          <p className="text-sm text-red-800">Extension conflict detected</p>
          <p className="text-xs text-red-600 mt-1">Some features disabled</p>
        </div>
      )}
      
      {errorState.extensionError && (
        <div className="fixed top-16 right-4 bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-lg z-50">
          <p className="text-sm text-orange-800">Some features may be unavailable</p>
        </div>
      )}
      
      
      {errorState.conflictError && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-2">Document Modified</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Document modified by another user
            </p>
            <div className="flex gap-2">
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => {
                  dispatchError({ type: 'SET_CONFLICT_ERROR', payload: null })
                  window.location.reload()
                }}
              >
                Reload Document
              </Button>
              <Button 
                size="sm"
                onClick={() => dispatchError({ type: 'SET_CONFLICT_ERROR', payload: null })}
              >
                Keep My Changes
              </Button>
            </div>
          </div>
        </div>
      )}
    </DocumentProvider>
  )
}