/**
 * Next.js App Router New Document Creation Page Component
 *
 * This React Client Component provides an intuitive document creation interface within the EkoIntelligence
 * ESG analysis platform. Users can select from predefined document templates (including entity-specific
 * templates) or create blank documents. The component handles template selection, document initialization,
 * database insertion, and seamless navigation to the newly created document in the TipTap editor.
 *
 * ## Core Functionality
 * - **Template Selection**: Interactive template gallery with various ESG document types and entity-specific templates
 * - **Document Creation**: Generates new documents with unique UUIDs and initializes content from selected templates
 * - **Database Integration**: Inserts document records into `doc_documents` table with proper metadata and relationships
 * - **User Authentication**: Validates user sessions and associates documents with authenticated users
 * - **Entity Association**: Links documents to specific ESG entities and analysis runs for contextual reporting
 * - **Navigation Flow**: Seamlessly redirects to document editor upon successful creation
 * - **Error Handling**: Comprehensive error management with user-friendly toast notifications
 *
 * ## Template System
 * **Template Structure**:
 * - `id`: Unique template identifier for tracking and metadata storage
 * - `name`: Human-readable template title displayed in selection interface
 * - `content`: Initial document content (HTML/rich text) that populates the TipTap editor
 * - `data`: Additional template configuration and entity-specific data for dynamic content generation
 * - **Entity-Specific Templates**: Dynamic templates generated based on selected entity and run data
 * - **Blank Document Option**: Default template for users who prefer to start with empty content
 *
 * ## Database Integration
 * **doc_documents Table Schema**:
 * - `id` (uuid, Primary Key): Auto-generated unique document identifier using crypto.randomUUID()
 * - `title` (text): Document title derived from template name or "Untitled Document" for blank templates
 * - `content` (text): Current document content state for the TipTap editor
 * - `initial_content` (text): Original template content preserved for version tracking and revert functionality
 * - `data` (jsonb): Template-specific configuration and entity data for dynamic content features
 * - `created_by` (uuid, FK to profiles): Document owner ID from authenticated user session
 * - `updated_by` (uuid, FK to profiles): Last editor ID (initially same as creator)
 * - `entity_id` (text, NOT NULL): Associated ESG entity identifier for contextual analysis
 * - `run_id` (integer, NOT NULL): Analysis run identifier linking document to specific data snapshots
 * - `metadata` (jsonb): Template metadata including original template_id and entity/run references
 * - `created_at/updated_at` (timestamp): Automatic timestamp tracking for document lifecycle
 *
 * ## Security & Access Control
 * - **Authentication Enforcement**: Requires valid Supabase user session before document creation
 * - **Row Level Security (RLS)**: Database policies ensure users can only create documents they own
 * - **Parameter Validation**: Validates entity_id and run_id parameters through `validateDocumentCreationParams` utility
 * - **Error Boundary**: Comprehensive error handling prevents unauthorized document creation
 * - **User Association**: All documents automatically linked to authenticated user for proper ownership
 *
 * ## User Experience Features
 * - **Dynamic Page Title**: Sets browser title to "Create New Document" using useEffect for proper SEO
 * - **Template Preview**: DocumentTemplates component provides visual template selection interface
 * - **Success Feedback**: Toast notifications confirm successful document creation
 * - **Error Communication**: Descriptive error messages guide users through resolution steps
 * - **Cancel Functionality**: Users can navigate back to document list without creating new documents
 * - **Responsive Design**: Template selection interface adapts to various screen sizes
 *
 * ## Entity & Run Association System
 * The component integrates with the ESG analysis system through entity and run parameters:
 * - **Entity Context**: Documents are associated with specific companies/organizations being analyzed
 * - **Run Versioning**: Links to specific analysis runs enabling temporal document snapshots
 * - **Data Validation**: `validateDocumentCreationParams` ensures valid entity/run combinations
 * - **Latest Run Support**: Automatically resolves "latest" run_id to current analysis version
 * - **Metadata Preservation**: Original template parameters stored in metadata for audit trails
 *
 * ## Navigation & Routing
 * - **Entry Point**: Accessed via `/customer/documents/new` route in Next.js App Router
 * - **Success Navigation**: Redirects to `/customer/documents/{document-id}` for immediate editing
 * - **Cancel Navigation**: Returns to `/customer/documents` main listing page
 * - **Deep Linking**: Supports URL parameters for entity/run pre-selection in template creation
 * - **Browser History**: Proper navigation maintains back/forward button functionality
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with client-side routing and app directory structure
 * - **React 18+**: Modern React with hooks for state management, lifecycle handling, and effect management
 * - **Supabase**: Database client for user authentication, document storage, and Row Level Security enforcement
 * - **TipTap Editor System**: Document editor infrastructure for rich text editing and collaborative features
 * - **DocumentTemplates Component**: Template selection interface with dynamic template generation
 * - **Toast Notifications**: User feedback system for success/error communication using shadcn/ui components
 *
 * ## Related Components & Systems
 * - **DocumentTemplates Component**: Template selection interface with entity-specific template generation
 * - **TipTap Document Editor**: Rich text editor for document content creation and collaborative editing
 * - **Document List Page**: Parent navigation context showing user's existing documents
 * - **ESG Analysis System**: Entity and run data that provides context for document creation
 * - **User Profile System**: Authentication and user management for document ownership
 * - **Template Generation System**: Dynamic template creation based on entity analysis data
 *
 * ## System Architecture Context
 * This component serves as the entry point for document creation within the broader EkoIntelligence system:
 * - **Customer Application Layer**: User-facing interface for document management and creation
 * - **Database Layer**: Supabase PostgreSQL with RLS policies for secure document storage
 * - **Analytics Integration**: Links to ESG analysis runs and entity data from analytics backend
 * - **Editor Integration**: Seamless handoff to TipTap-based collaborative document editor
 * - **Template System**: Configurable template engine for various ESG document types
 *
 * ## Error Handling & Edge Cases
 * - **Authentication Failures**: Graceful handling of unauthenticated users with clear error messages
 * - **Database Connection Issues**: Proper error logging and user notification for database failures
 * - **Invalid Parameters**: Validation of entity_id and run_id parameters with meaningful error feedback
 * - **Template Loading Errors**: Fallback handling for template system failures
 * - **Navigation Interruption**: Proper cleanup and state management during navigation cancellation
 * - **Concurrent Access**: Database constraints prevent duplicate document creation race conditions
 *
 * ## Performance Considerations
 * - **Client-Side Creation**: Document UUID generation using crypto.randomUUID() for immediate responsiveness
 * - **Single Database Insert**: Atomic document creation with all required fields in one operation
 * - **Optimistic Navigation**: Immediate redirect to editor upon successful database confirmation
 * - **Template Caching**: DocumentTemplates component handles template loading and caching efficiently
 * - **Minimal Re-renders**: Strategic use of useEffect and state management prevents unnecessary updates
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages Next.js App Router Pages
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase Row Level Security
 * @see https://tiptap.dev/docs/editor/getting-started TipTap Editor Documentation
 * @see https://react.dev/reference/react/useEffect React useEffect Hook
 * @see /components/editor/templates/DocumentTemplates.tsx DocumentTemplates Component
 * @see /utils/document-utils.ts Document Validation Utilities
 * <AUTHOR>
 * @updated 2025-07-23
 * @description New document creation page with template selection and database integration for the EkoIntelligence ESG analysis platform
 * @example 
 * ```typescript
// Navigate to new document page
router.push('/customer/documents/new')

// With entity context
router.push('/customer/documents/new?entity=AAPL&run=latest')
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/app/supabase/client'
import { useToast } from '@/hooks/use-toast'
import { DocumentTemplates } from '@/components/editor/templates/DocumentTemplates'
import { validateDocumentCreationParams } from '@/utils/document-utils'

export default function NewDocumentPage() {
  const router = useRouter()
  const { toast } = useToast()
  const supabase = createClient()

  // Set page title
  useEffect(() => {
    // Add a small delay to ensure the component is fully mounted
    const timer = setTimeout(() => {
      document.title = 'Create New Document'
    }, 100)
    
    return () => clearTimeout(timer)
  }, [])

  const handleSelectTemplate = async (template: any, entityId?: string, runId?: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User not authenticated')
      }

      const { entityId: validatedEntityId, runId: actualRunId } = await validateDocumentCreationParams(entityId, runId)

      // Generate a unique document ID
      const documentId = crypto.randomUUID()

      const { data: document, error } = await supabase
        .from('doc_documents')
        .insert({
          id: documentId,
          title: template.name === 'Blank Document' ? 'Untitled Document' : template.name,
          content: template.content || '',
          initial_content: template.content || '',
          data: template.data || null,
          created_by: user.id,
          updated_by: user.id,
          entity_id: validatedEntityId,
          run_id: actualRunId,
          metadata: {
            template_id: template.id,
            entity_id: entityId,
            run_id: runId
          }
        })
        .select()
        .single()

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Document created successfully'
      })

      // Navigate to the new document
      router.push(`/customer/documents/${document.id}`)
    } catch (error) {
      console.error('Error creating document:', error)
      toast({
        title: 'Error',
        description: 'Failed to create document',
        variant: 'destructive'
      })
    }
  }

  const handleCancel = () => {
    router.push('/customer/documents')
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <DocumentTemplates
          onSelectTemplate={handleSelectTemplate}
          onClose={handleCancel}
          className="w-full"
        />
      </div>
    </div>
  )
}
