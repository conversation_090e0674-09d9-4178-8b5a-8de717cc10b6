/**
 * # Customer Documents Management Page
 *
 * ## Overview
 * The main document management interface for the ekoIntelligence ESG analysis platform. This Next.js 15 App Router client component provides comprehensive document lifecycle management including creation, viewing, searching, filtering, and deletion. The page serves as the central hub for users to access both their own documents and shared documents within their organization.
 *
 * ## Core Functionality
 *
 * ### Document Management Operations
 * - **Document Creation**: Template-based document creation with entity and run association
 * - **Document Listing**: Real-time display of user documents with metadata
 * - **Document Search**: Full-text search across document titles and author names
 * - **Document Filtering**: Toggle between personal documents and shared documents
 * - **Document Deletion**: Owner-only document removal with confirmation
 * - **Document Navigation**: Direct navigation to individual document editors
 *
 * ### Real-time Features
 * - **Live Updates**: Supabase real-time subscriptions for document changes
 * - **Collaborative Visibility**: Shows shared documents from other users
 * - **Author Attribution**: Displays document creators with avatar support
 * - **Last Modified Tracking**: Shows relative timestamps for document updates
 *
 * ## Next.js 15 App Router Architecture
 *
 * ### Client Component Design
 * Uses `'use client'` directive for interactive features requiring:
 * - State management for documents, search, and UI states
 * - Real-time Supabase subscriptions and database interactions
 * - Router navigation and toast notifications
 * - Modal dialogs and template selection
 *
 * ### Routing Integration
 * - **Base Route**: `/customer/documents` - Document listing and management
 * - **Navigation**: `/customer/documents/[id]` - Individual document editing
 * - **Query State**: URL state management for entity/run selection
 * - **Page Title**: Dynamic document title setting for SEO
 *
 * ## Authentication & Authorization
 *
 * ### Supabase Authentication
 * - **User Context**: Retrieves authenticated user for document ownership
 * - **Authorization**: Row Level Security (RLS) for document access control
 * - **Permission Checks**: Owner-only operations for document deletion
 * - **Shared Access**: RLS-controlled visibility of shared documents
 *
 * ### Access Control Features
 * - **Document Ownership**: Clear distinction between owned and shared documents
 * - **Filter Controls**: Separate views for "My Documents" vs "Shared" documents
 * - **Operation Restrictions**: Delete operations limited to document owners
 * - **Visual Indicators**: Share icons for documents from other users
 *
 * ## Database Integration
 *
 * ### Supabase Database Operations
 * **Primary Table**: `doc_documents`
 * ```sql
 * -- Core document fields
 * id, title, created_at, updated_at, created_by, updated_by, 
 * content, initial_content, data, entity_id, run_id, metadata
 * ```
 *
 * ### Real-time Subscriptions
 * - **Change Detection**: Listens for all document table changes
 * - **Auto-refresh**: Automatically reloads document list on updates
 * - **Multi-user Support**: Reflects changes from other users instantly
 * - **Connection Management**: Proper cleanup of subscription channels
 *
 * ### Query Optimization
 * - **Selective Loading**: Filters documents based on ownership preferences
 * - **Ordered Results**: Documents sorted by last updated timestamp
 * - **Metadata Inclusion**: Loads template and entity association data
 * - **User Context**: Joins with authentication context for ownership
 *
 * ## Template System Integration
 *
 * ### Document Templates
 * - **Template Selection**: Modal dialog with categorized template library
 * - **Dynamic Templates**: Entity-specific templates with auto-population
 * - **Static Templates**: Pre-built templates for common document types
 * - **Content Processing**: Handles markdown and structured data templates
 *
 * ### Entity Association
 * - **Entity Selection**: Links documents to specific ESG entities
 * - **Run Association**: Associates documents with analysis runs
 * - **Validation**: Parameter validation for entity/run combinations
 * - **Metadata Storage**: Stores template and entity metadata
 *
 * ## User Interface Components
 *
 * ### Layout Structure
 * - **Header Section**: Page title, description, and new document button
 * - **Search Controls**: Search input with live filtering capability
 * - **Filter Buttons**: Toggle between personal and shared document views
 * - **Document Grid**: Responsive card layout for document display
 * - **Modal Dialogs**: Template selection and confirmation dialogs
 *
 * ### Interactive Elements
 * - **Document Cards**: Clickable cards with hover effects and metadata
 * - **Action Menus**: Dropdown menus with context-sensitive actions
 * - **Search Interface**: Real-time search with visual feedback
 * - **Loading States**: Progress indicators for async operations
 * - **Empty States**: Helpful prompts when no documents exist
 *
 * ## Performance & UX Optimizations
 *
 * ### Loading States
 * - **Document Loading**: Shows spinner during data fetching
 * - **Creation Loading**: Special state during document creation
 * - **Search Debouncing**: Efficient search with instant visual feedback
 * - **Real-time Updates**: Optimistic UI updates with error handling
 *
 * ### Error Handling
 * - **Authentication Errors**: Handles unauthenticated user scenarios
 * - **Database Errors**: Graceful error handling with user feedback
 * - **Creation Failures**: Rollback UI state on document creation errors
 * - **Network Issues**: Toast notifications for operation failures
 *
 * ## Security Considerations
 *
 * ### Data Protection
 * - **RLS Enforcement**: All database queries respect Row Level Security
 * - **User Isolation**: Users only see documents they own or have access to
 * - **Operation Validation**: Server-side validation of all document operations
 * - **Authentication Checks**: All operations require valid user authentication
 *
 * ### Input Validation
 * - **Search Sanitization**: Safe handling of search query inputs
 * - **Template Validation**: Validates template selection parameters
 * - **Entity Validation**: Ensures valid entity/run combinations
 * - **Content Security**: Secure handling of document content and metadata
 *
 * ## Dependencies & Integration
 *
 * ### Core Dependencies
 * - **React 18**: Modern React with hooks and concurrent features
 * - **Next.js 15**: App Router with client component architecture
 * - **Supabase**: Real-time database with authentication and RLS
 * - **TypeScript**: Full type safety for component and data structures
 *
 * ### UI Framework
 * - **Shadcn/UI**: Consistent component library with accessibility
 * - **Lucide Icons**: Comprehensive icon set for interface elements
 * - **Tailwind CSS**: Utility-first styling with responsive design
 * - **Radix UI**: Accessible primitives for complex interactions
 *
 * ### Utility Libraries
 * - **Date Utilities**: Relative time formatting for timestamps
 * - **Document Utilities**: Validation and processing helpers
 * - **Toast Notifications**: User feedback for operations
 * - **Router Navigation**: Type-safe routing and navigation
 *
 * ## Usage Examples
 *
 * ### Basic Document Management
 * ```typescript
 * // Access the documents page
 * // Navigate to: /customer/documents
 *
 * // Create a new document
 * handleCreateDocument() // Opens template selection dialog
 *
 * // Search documents
 * setSearchQuery("ESG Report") // Filters documents in real-time
 *
 * // Toggle shared documents
 * setShowShared(true) // Shows documents shared by other users
 * ```
 *
 * ### Template-based Creation
 * ```typescript
 * // Select a template with entity association
 * handleSelectTemplate(
 *   template,      // Template object with content/data
 *   "entity_123",  // Optional entity ID
 *   "run_456"      // Optional run ID
 * )
 * ```
 *
 * ### Document Operations
 * ```typescript
 * // Navigate to document editor
 * handleDocumentClick(documentId) // Routes to /customer/documents/[id]
 *
 * // Delete a document (owner only)
 * handleDeleteDocument(documentId) // Removes document with confirmation
 * ```
 *
 * ## Component State Management
 *
 * ### Primary State Variables
 * - `documents`: Complete list of user-accessible documents
 * - `filteredDocuments`: Search-filtered subset of documents
 * - `searchQuery`: Current search term for filtering
 * - `showShared`: Toggle between personal and shared document views
 * - `loading`: Loading state for document fetching operations
 * - `user`: Current authenticated user context
 * - `showTemplateDialog`: Modal visibility for template selection
 * - `isCreatingDocument`: Special loading state during document creation
 *
 * ### State Synchronization
 * - **Real-time Updates**: Supabase subscriptions keep state synchronized
 * - **Search Filtering**: Derived state updates automatically with search changes
 * - **User Context**: Authentication state drives document ownership display
 * - **Navigation State**: Router state manages page transitions
 *
 * @see {@link https://nextjs.org/docs/app/building-your-application/routing} Next.js App Router Documentation
 * @see {@link https://supabase.com/docs/guides/realtime} Supabase Realtime Documentation
 * @see {@link https://ui.shadcn.com/docs} Shadcn/UI Component Documentation
 * @see {@link /apps/customer/components/editor/templates/DocumentTemplates} Document Templates Component
 * @see {@link /apps/customer/app/customer/documents/[id]/page} Individual Document Editor
 * @see {@link /apps/customer/utils/document-utils} Document Validation Utilities
 *
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Next.js 15 App Router client component for comprehensive document management in the ekoIntelligence ESG analysis platform. Provides document creation, listing, search, filtering, and deletion with real-time collaboration features, template-based creation, entity association, and Row Level Security integration.
 * @example
 * ```tsx
 * // The DocumentsPage component is automatically rendered by Next.js App Router
 * // when users navigate to /customer/documents
 *
 * // Key user interactions:
 * // 1. Create new document -> Opens template selection dialog
 * // 2. Search documents -> Real-time filtering by title/author
 * // 3. Toggle shared view -> Shows documents from other users
 * // 4. Click document -> Navigate to document editor
 * // 5. Delete document -> Owner-only operation with confirmation
 *
 * // Example URL patterns:
 * // /customer/documents - Main document listing
 * // /customer/documents?entity=123&run=456 - With entity/run context
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import React, { useEffect, useState } from 'react'
import { createClient } from '@/app/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Clock,
  Edit3,
  FileText,
  FolderOpen,
  Loader2,
  MoreHorizontal,
  Plus,
  Search,
  Share2,
  Trash2,
  User,
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { DocumentTemplates } from '@/components/editor/templates/DocumentTemplates'
import { formatTimeAgo } from '@utils/date-utils'
import { validateDocumentCreationParams } from '@/utils/document-utils'

interface Document {
  id: string
  title: string
  createdAt: Date
  updatedAt: Date
  author: {
    id: string
    name: string
    email?: string
    avatar?: string
  }
  isOwner: boolean
  metadata?: any
}

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [showShared, setShowShared] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [showTemplateDialog, setShowTemplateDialog] = useState(false)
  const [isCreatingDocument, setIsCreatingDocument] = useState(false)

  const { toast } = useToast()
  const router = useRouter()
  const supabase = createClient()

  // Set page title
  useEffect(() => {
    document.title = 'Documents - ekoIntelligence'
  }, [])

  // Get current user
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    }
    getUser()
  }, [supabase])

  // Load documents from Supabase
  const loadDocuments = async () => {
    setLoading(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User not authenticated')
      }

      let query = supabase
        .from('doc_documents')
        .select(`
          id,
          title,
          created_at,
          updated_at,
          created_by,
          updated_by,
          metadata
        `)
        .order('updated_at', { ascending: false })

      // Filter based on showShared
      if (!showShared) {
        // Only show documents created by current user
        query = query.eq('created_by', user.id)
      }
      // If showShared is true, RLS will handle showing accessible documents

      const { data: documents, error } = await query

      if (error) {
        throw error
      }

      // Transform documents to match expected format
      const transformedDocuments = documents?.map(doc => ({
        id: doc.id,
        title: doc.title || 'Untitled Document',
        createdAt: new Date(doc.created_at || new Date()),
        updatedAt: new Date(doc.updated_at || new Date()),
        author: {
          id: doc.created_by || 'unknown',
          name: doc.created_by === user.id
            ? (user.user_metadata?.name || user.email || 'You')
            : 'Unknown User',
          email: doc.created_by === user.id ? user.email : '<EMAIL>',
          avatar: doc.created_by === user.id ? user.user_metadata?.avatar_url : undefined
        },
        isOwner: doc.created_by === user.id,
        metadata: doc.metadata || {}
      })) || []

      setDocuments(transformedDocuments)
    } catch (error) {
      console.error('Error loading documents:', error)
      toast({
        title: 'Error',
        description: 'Failed to load documents',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // Load documents on mount and set up real-time subscription
  useEffect(() => {
    loadDocuments()

    // Set up real-time subscription for documents
    const channel = supabase
      .channel('doc_documents')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'doc_documents',
        },
        () => {
          // Reload documents when changes occur
          loadDocuments()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [showShared, supabase])

  // Filter documents based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredDocuments(documents)
    } else {
      const filtered = documents.filter(doc =>
        doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.author.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredDocuments(filtered)
    }
  }, [documents, searchQuery])

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }


  const handleCreateDocument = () => {
    setIsCreatingDocument(true)
    setShowTemplateDialog(true)
  }

  const handleSelectTemplate = async (template: any, entityId?: string, runId?: string) => {
    let user: any = null
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      user = authUser

      if (!user) {
        throw new Error('User not authenticated')
      }

      const { entityId: validatedEntityId, runId: actualRunId } = await validateDocumentCreationParams(entityId, runId)

      // Generate a unique document ID
      const documentId = crypto.randomUUID()

      // Process template content to handle table of contents marker
      let processedContent = template.content || ''
      let processedData = template.data || null

      // For templates with markdown content, replace TABLE_OF_CONTENTS marker
      if (processedContent && processedContent.includes('<!-- TABLE_OF_CONTENTS -->')) {
        // Replace the marker with a placeholder that will be processed by the editor
        processedContent = processedContent.replace(
          '<!-- TABLE_OF_CONTENTS -->',
          '\n<div data-type="table-of-contents" id="table-of-contents"></div>\n'
        )
      }

      // Build metadata object, only including entity_id and run_id if they're defined
      const metadata: any = {
        template_id: template.id
      }
      if (entityId) {
        metadata.entity_id = entityId
      }
      if (runId && runId !== 'latest') {
        metadata.run_id = runId
      }

      const { data: document, error } = await supabase
        .from('doc_documents')
        .insert({
          id: documentId,
          title: template.name === 'Blank Document' ? 'Untitled Document' : template.name,
          content: processedContent,
          initial_content: processedContent,
          data: processedData,
          entity_id: validatedEntityId,
          run_id: actualRunId,
          created_by: user.id,
          updated_by: user.id,
          metadata
        })
        .select()
        .single()

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Document created successfully'
      })

      // Close template dialog first
      setShowTemplateDialog(false)
      setIsCreatingDocument(false)
      
      // Navigate to the new document
      await router.push(`/customer/documents/${documentId}`)
    } catch (error) {
      console.error('Error creating document:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        templateId: template.id, 
        templateName: template.name,
        entityId,
        runId,
        hasUser: !!user
      })
      
      const errorMessage = error instanceof Error ? error.message : 'Failed to create document'
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      })
      // Reset state on error
      setIsCreatingDocument(false)
      setShowTemplateDialog(false)
    }
  }

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const { error } = await supabase
        .from('doc_documents')
        .delete()
        .eq('id', documentId)

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Document deleted successfully'
      })

      // Reload documents
      loadDocuments()
    } catch (error) {
      console.error('Error deleting document:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete document',
        variant: 'destructive'
      })
    }
  }

  const handleDocumentClick = (documentId: string) => {
    router.push(`/customer/documents/${documentId}`)
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Documents</h1>
            <p className="text-muted-foreground mt-1">
              Create and manage your documents
            </p>
          </div>
          <Button onClick={handleCreateDocument} data-testid="new-document-button">
            <Plus className="w-4 h-4 mr-2" />
            New Document
          </Button>
        </div>

        {/* Search and Filter */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant={!showShared ? 'default' : 'outline'}
                  onClick={() => setShowShared(false)}
                >
                  My Documents
                </Button>
                <Button
                  size="sm"
                  variant={showShared ? 'default' : 'outline'}
                  onClick={() => setShowShared(true)}
                >
                  Shared
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Documents Grid */}
        {isCreatingDocument ? (
          <div className="text-center py-12" data-testid="document-creation-loading">
            <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin" />
            <p className="text-muted-foreground">Creating document...</p>
            <p className="text-sm text-muted-foreground mt-2">Please select a template to continue</p>
          </div>
        ) : loading ? (
          <div className="text-center py-12">
            <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin" />
            <p className="text-muted-foreground">Loading documents...</p>
          </div>
        ) : filteredDocuments.length === 0 ? (
          <div className="text-center py-12">
            <FolderOpen className="w-16 h-16 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="text-lg font-semibold mb-2">
              {searchQuery ? 'No documents found' : showShared ? 'No shared documents' : 'No documents yet'}
            </h3>
            <p className="text-muted-foreground mb-6">
              {!searchQuery && !showShared && 'Create your first document to get started'}
            </p>
            {!searchQuery && !showShared && (
              <Button onClick={handleCreateDocument}>
                <Plus className="w-4 h-4 mr-2" />
                Create Document
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" data-testid="documents-list">
            {filteredDocuments.map((document) => (
              <Card
                key={document.id}
                className="group cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]"
                onClick={() => handleDocumentClick(document.id)}
                data-testid="document-card"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      <FileText className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                      <CardTitle className="text-sm font-medium truncate">
                        {document.title}
                      </CardTitle>
                      {!document.isOwner && (
                        <Share2 className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="w-3 h-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleDocumentClick(document.id)}>
                          <Edit3 className="w-3 h-3 mr-2" />
                          Open
                        </DropdownMenuItem>
                        {document.isOwner && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDeleteDocument(document.id)
                              }}
                              className="text-destructive"
                            >
                              <Trash2 className="w-3 h-3 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <User className="w-3 h-3" />
                      <span className="truncate">{document.author.name}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      <span>Updated {formatTimeAgo(document.updatedAt)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Template Selection Dialog */}
      <Dialog 
        open={showTemplateDialog} 
        onOpenChange={(open) => {
          setShowTemplateDialog(open)
          if (!open) {
            // Reset creating state when dialog is closed
            setIsCreatingDocument(false)
          }
        }}
        modal={true}
      >
        <DialogContent 
          className="max-w-6xl max-h-[90vh] p-0 overflow-hidden"
          onPointerDownOutside={(e) => {
            // Prevent closing when clicking inside the dialog content area
            const target = e.target as HTMLElement;
            if (target.closest('[data-testid="template-dialog"]')) {
              e.preventDefault();
            }
          }}
          onInteractOutside={(e) => {
            // Prevent closing when interacting inside the dialog content area
            const target = e.target as HTMLElement;
            if (target.closest('[data-testid="template-dialog"]')) {
              e.preventDefault();
            }
          }}
        >
          <VisuallyHidden>
            <DialogTitle>Choose Document Template</DialogTitle>
          </VisuallyHidden>
          <DocumentTemplates
            onSelectTemplate={handleSelectTemplate}
            onClose={() => {
              setShowTemplateDialog(false)
              setIsCreatingDocument(false)
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
