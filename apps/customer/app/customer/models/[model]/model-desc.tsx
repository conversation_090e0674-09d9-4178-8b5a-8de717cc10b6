/**
 * ESG Model Descriptions Configuration Module for Dynamic Model Pages
 *
 * This module provides comprehensive definitions and visual content for various Environmental, Social,
 * and Governance (ESG) analytical frameworks used throughout the ekoIntelligence platform. It serves as
 * a centralized content repository for ESG model information displayed in dynamic model pages via the
 * Next.js App Router `[model]` route parameter system.
 *
 * ## Core Functionality
 * - **Dynamic Model Content**: Provides structured content for `/customer/models/[model]` route pages
 * - **Visual Asset Management**: Integrates optimized image assets for each model via Next.js Image component
 * - **Consistent Presentation**: Ensures uniform model information display across all ESG framework pages
 * - **Content Centralization**: Single source of truth for ESG model descriptions and visual assets
 * - **Type-Safe Configuration**: Strongly typed model definitions with JSX image components
 *
 * ## ESG Models Supported
 * ### UN Sustainable Development Goals ('sdg')
 * - **Scope**: 17 interconnected global objectives established in 2015 for sustainable development
 * - **Timeline**: 2030 Agenda for Sustainable Development with universal application
 * - **Coverage**: Poverty, hunger, health, education, gender equality, climate action, partnerships
 * - **Approach**: Comprehensive consultative process involving governments, businesses, civil society
 *
 * ### Doughnut Model ('doughnut')
 * - **Creator**: Kate <PERSON>orth's economic framework for sustainable development
 * - **Structure**: Social foundation (inner circle) + ecological ceiling (outer circle)
 * - **Safe Space**: Balance between meeting human needs and planetary boundaries
 * - **Innovation**: Challenges GDP-focused growth models in favor of regenerative practices
 *
 * ### Plant-Based Treaty ('plant_based_treaty')
 * - **Focus**: Addressing environmental impacts of animal agriculture systems
 * - **Approach**: Global campaign mirroring climate agreements like Paris Agreement
 * - **Impact Areas**: Greenhouse gas emissions, deforestation, water pollution, biodiversity loss
 * - **Support**: Environmental organizations, public figures, and municipalities worldwide
 *
 * ### ekoIntelligence Model ('eko')
 * - **Design**: Custom 24-section ESG framework addressing bias in existing models
 * - **Structure**: 12 ecological + 7 social + 5 governance sections for balanced coverage
 * - **Purpose**: Comprehensive and balanced categorization of ESG issues
 * - **Application**: Primary analytical framework for ekoIntelligence platform assessments
 *
 * ## Component Architecture
 * The exported `modelDesc` object follows a standardized structure:
 * - **Key-Value Mapping**: Model identifier strings ('sdg', 'doughnut', etc.) map to descriptive objects
 * - **Image Integration**: Each model includes optimized Next.js Image component with responsive sizing
 * - **Rich Text Content**: Detailed descriptions explaining framework purpose, scope, and implementation
 * - **Glass-Morphism Design**: Images styled with rounded corners consistent with design system
 *
 * ## Visual Structure
 * Each model definition includes:
 * - **title**: Human-readable framework name for page headers and metadata
 * - **image**: Optimized JSX.Element using Next.js Image component with specific dimensions
 * - **text**: Comprehensive markdown-style description covering framework details
 * - **Responsive Styling**: Consistent CSS classes (`float-left mr-4 mb-2 h-auto rounded`)
 *
 * ## Usage Context
 * This module is imported and utilized by:
 * - `/customer/models/[model]/page.tsx`: Dynamic route pages for individual model descriptions
 * - `entity-analysis-report.tsx`: Model context in ESG entity analysis dashboards
 * - Dashboard components requiring model metadata and visual assets
 * - Modal dialogs and tooltips displaying ESG framework information
 *
 * ## Route Integration
 * - **Dynamic Routes**: Works with Next.js App Router `[model]` parameter system
 * - **Static Generation**: Enables static generation of model pages at build time
 * - **SEO Optimization**: Provides structured content for search engine optimization
 * - **Type Safety**: TypeScript integration ensures compile-time validation of model keys
 *
 * ## Design System Integration
 * - **Glass-Morphism Compatibility**: Images use rounded corners and consistent spacing
 * - **Responsive Layout**: Float-left positioning integrates with glass-morphism card layouts
 * - **Typography System**: Text content styled with Tailwind CSS typography classes
 * - **Performance Optimization**: Next.js Image component provides automatic optimization
 *
 * ## Performance Optimizations
 * - **Next.js Image Optimization**: Automatic image optimization, lazy loading, and format conversion
 * - **Static Asset Management**: Images served from `/images/models/` directory for CDN caching
 * - **Compile-Time Validation**: TypeScript ensures model keys exist and content is well-formed
 * - **Bundle Optimization**: Tree-shaking eliminates unused model definitions in production
 *
 * ## System Architecture
 * This module fits into the broader ekoIntelligence architecture:
 * - **Content Layer**: Provides static content for dynamic pages in the customer application
 * - **Route Layer**: Integrates with Next.js App Router for SEO-friendly model description pages
 * - **Design System**: Follows glass-morphism design patterns with translucent, rounded elements
 * - **Frontend Stack**: Part of Next.js 15 customer application with TypeScript and Tailwind CSS
 * - **Asset Pipeline**: Leverages Next.js image optimization for performance and user experience
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://nextjs.org/docs/app/api-reference/components/image Next.js Image Component
 * @see https://sdgs.un.org/goals UN Sustainable Development Goals
 * @see https://doughnuteconomics.org Kate Raworth's Doughnut Economics
 * @see https://plantbasedtreaty.org Plant Based Treaty
 * @see /customer/models/[model]/page.tsx Dynamic Model Page Component
 * <AUTHOR>
 * @updated 2025-07-23
 * @description ESG model definitions and visual content for dynamic model description pages
 * @example ```tsx
 * // Usage in dynamic route page
 * import { modelDesc } from './model-desc'
 *
 * export default function ModelPage({ params }: { params: { model: string } }) {
 *   const model = modelDesc[params.model]
 *   if (!model) return <div>Model not found</div>
 *
 *   return (
 *     <div className="glass-card">
 *       <h1>{model.title}</h1>
 *       {model.image}
 *       <p>{model.text}</p>
 *     </div>
 *   )
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import Image from 'next/image'

import type { JSX } from 'react'

export const modelDesc: { [key: string]: { image: JSX.Element, text: string, title: string } } = {
  'sdg': {
    title: 'UN Sustainable Development Goals (SDGs)',
    image: <Image alt={'logo'} src="/images/models/sdg/logo.png" width={270} height={140}
                  className="float-left mr-4 mb-2 h-auto rounded" />, text: `
The United Nations Sustainable Development Goals (SDGs) are a set of 17 interconnected global objectives established in 2015 as part of the 2030 Agenda for Sustainable Development. These goals were designed to address the world’s most pressing challenges, aiming for a more equitable, prosperous, and sustainable future for all.
              
The SDGs cover a wide range of issues, including poverty, hunger, health, education, gender equality, clean water, and sanitation. They also emphasize the need for affordable and clean energy, decent work and economic growth, innovation and infrastructure, reduced inequalities, and sustainable cities and communities. Environmental priorities like climate action, life below water, and life on land are also central to the goals. The SDGs promote peace, justice, and strong institutions, while fostering global partnerships for their achievement.

What sets the SDGs apart is their universality, applying to both developing and developed countries alike. They are interconnected, recognizing that progress in one area often depends on progress in others. The goals were developed through a comprehensive consultative process involving governments, businesses, civil society, and millions of people worldwide.

The SDGs are more than a vision; they serve as a roadmap for action, emphasizing the importance of collaboration, inclusivity, and innovation. While progress varies across regions and goals, the SDGs remain a vital framework guiding global efforts towards sustainable development, urging countries and stakeholders to accelerate actions to achieve these objectives by 2030.
        `,
  },

  'doughnut': {
    title: 'Doughnut Model',
    image: <Image alt={'logo'} src="/images/models/doughnut/logo.png" width={200} height={200}
                  className="float-left mr-4 mb-2 h-auto rounded" />, text: `
The Doughnut Model, developed by British economist Kate Raworth, is a visual framework for sustainable development. It integrates social and environmental boundaries, aiming to create a safe and just space for humanity within the planet's limits. The model is shaped like a doughnut, with two concentric circles.

The inner circle represents the "social foundation," which outlines the minimum standards of living necessary for everyone to lead a dignified life. These include access to basic needs like food, water, education, healthcare, income, and political participation. Falling below this threshold means people are deprived of their fundamental rights and needs.

The outer circle signifies the "ecological ceiling," representing the environmental limits that must not be breached to avoid damaging Earth's life-supporting systems. This boundary is based on planetary boundaries, which include climate change, biodiversity loss, land use, and pollution. Exceeding these limits results in environmental degradation that threatens the stability of the planet.

The space between the two circles—the doughnut itself—is the "safe and just space" where humanity can thrive. It represents a balance between meeting everyone's needs while ensuring we do not over-exploit the planet’s resources.

The Doughnut Model challenges traditional economic growth models by prioritizing well-being over GDP growth. It encourages economies to focus on regenerative and distributive practices that respect both people and the planet. The model has gained traction in cities, organizations, and governments worldwide as a framework for creating more sustainable, equitable, and resilient communities.`,
  }
  ,
  'plant_based_treaty': {
    title: 'Plant-Based Treaty',
    image: <Image alt={'logo'} src="/images/models/plant_based_treaty/logo.png" width={200} height={200}
                  className="float-left mr-4 mb-2 h-auto rounded" />,
    text: `
The Plant Based Treaty is a global campaign and initiative aimed at addressing the environmental impacts of animal agriculture and promoting a shift toward plant-based food systems. It was launched to mirror the principles of climate agreements like the Paris Agreement but focuses specifically on food production and consumption.

Animal agriculture is a major contributor to greenhouse gas emissions, deforestation, water pollution, and biodiversity loss. The Plant Based Treaty highlights the need for systemic changes in food systems to meet global climate goals, reduce environmental damage, and improve public health. It also emphasizes the ethical concerns related to animal welfare and the inefficiencies of using animals for food.

The treaty is supported by various environmental and animal rights organizations, public figures, and cities worldwide that have endorsed its principles. It calls for world leaders, policymakers, and individuals to take action to mitigate the impact of animal agriculture on the planet.`,
  },
  'eko': {
    title: 'ekoIntelligence',
    image: <Image alt={'logo'} src="/images/models/eko/logo.svg" width={200} height={200}
                  className="float-left mr-4 mb-2 h-auto rounded" />,
    text: `The  ekoIntelligence model represents a comprehensive and balanced framework for categorizing ESG (Environmental, Social, and Governance) issues, specifically designed to address the uneven distribution found in existing ethical frameworks. Unlike traditional models that often exhibit bias toward specific areas, the 'eko' model features 24 carefully structured sections: 12 ecological sections covering critical environmental domains from climate and energy to biodiversity and marine ecosystems, 7 social sections encompassing labor rights, health, education, and human rights, and 5 governance sections addressing ethics, transparency, and stakeholder engagement`,
  },


}
