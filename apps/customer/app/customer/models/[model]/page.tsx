/**
 * Dynamic ESG Model Information Display Page Component for Customer Application
 *
 * This component serves as a Next.js 15 App Router dynamic route page that displays comprehensive
 * information about various Environmental, Social, and Governance (ESG) analytical frameworks
 * within the ekoIntelligence platform. It provides authenticated users with detailed descriptions,
 * visual assets, and contextual information about ESG models through a glass-morphism design interface.
 *
 * ## Core Functionality
 * - **Dynamic Route Handling**: Processes `[model]` route parameter to display specific ESG framework information
 * - **Authentication Integration**: Enforces user authentication before displaying model content
 * - **Content Presentation**: Shows comprehensive model descriptions with integrated visual assets
 * - **Responsive Design**: Implements glass-morphism design system with translucent card layouts
 * - **Server-Side Rendering**: Utilizes Next.js 15 server components for optimal performance and SEO
 *
 * ## Supported ESG Models
 * - **UN Sustainable Development Goals ('sdg')**: 17 interconnected global sustainability objectives
 * - **Doughnut Model ('doughnut')**: Kate Raworth's economic framework balancing social foundation and ecological ceiling
 * - **Plant-Based Treaty ('plant_based_treaty')**: Environmental campaign addressing animal agriculture impacts
 * - **ekoIntelligence Model ('eko')**: Custom 24-section balanced ESG framework (12 ecological + 7 social + 5 governance)
 *
 * ## Route Architecture
 * - **Dynamic Routing**: Uses Next.js App Router `[model]` parameter for SEO-friendly URLs
 * - **Route Pattern**: `/customer/models/[model]` where model is ESG framework identifier
 * - **Parameter Processing**: Handles Promise-based route parameters following Next.js 15 patterns
 * - **URL Examples**: `/customer/models/sdg`, `/customer/models/doughnut`, `/customer/models/eko`
 *
 * ## Authentication Flow
 * 1. **Server-Side Validation**: Calls `checkAuth()` to verify user authentication status
 * 2. **Redirect on Failure**: Unauthenticated users redirected to `/login?next=/customer`
 * 3. **Session Management**: Integrates with Supabase authentication and Row Level Security
 * 4. **Access Control**: Ensures only authenticated users can access ESG model information
 *
 * ## Component Integration
 * - **PageHeader**: Displays model title in consistent navigation header with glass-morphism styling
 * - **ExpandableText**: Renders model description with integrated image, markdown support, and expand/collapse functionality
 * - **Card Components**: Uses shadcn/ui Card and CardContent for consistent glass-morphism layout
 * - **Model Repository**: Retrieves content from `model-desc.tsx` centralized content repository
 *
 * ## Visual Design System
 * - **Glass-Morphism**: Translucent card backgrounds with backdrop blur effects
 * - **Generous Rounding**: 1.5rem border radius standard for modern, approachable appearance
 * - **Content Integration**: Images float-left with consistent spacing and rounded corners
 * - **Typography**: Tailwind CSS typography classes with proper heading hierarchy
 * - **Responsive Layout**: Full-width content with centered maximum width constraints
 *
 * ## Performance Optimizations
 * - **Server Components**: Leverages Next.js 15 server components for reduced client-side JavaScript
 * - **Static Generation**: Enables static generation of model pages at build time for faster loading
 * - **Image Optimization**: Next.js Image component provides automatic optimization and lazy loading
 * - **SEO Enhancement**: Server-side rendering improves search engine discoverability
 * - **Bundle Optimization**: Tree-shaking eliminates unused model definitions in production builds
 *
 * ## Data Flow Architecture
 * 1. **Route Resolution**: Next.js App Router extracts `model` parameter from URL segment
 * 2. **Authentication Check**: Server-side authentication validation using Supabase client
 * 3. **Content Lookup**: Retrieves model data from `modelDesc` configuration object
 * 4. **Component Rendering**: Renders PageHeader, Card, and ExpandableText with model-specific content
 * 5. **Client Hydration**: Minimal client-side JavaScript for interactive elements (expand/collapse)
 *
 * ## System Architecture Context
 * This component integrates within the broader ekoIntelligence architecture:
 * - **Frontend Layer**: Part of Next.js 15 customer application with TypeScript and Tailwind CSS
 * - **Authentication Layer**: Integrates with Supabase auth system and Row Level Security policies
 * - **Design System**: Follows glass-morphism patterns with translucent, rounded design elements
 * - **Content Management**: Uses centralized model description repository for consistency
 * - **Route System**: Implements App Router dynamic segments for SEO-friendly model pages
 *
 * ## Error Handling
 * - **Missing Models**: Graceful handling when model parameter doesn't match available models
 * - **Authentication Failures**: Automatic redirection to login page with return URL preservation
 * - **Content Errors**: Console logging for debugging model content access issues
 * - **Route Validation**: Ensures model parameter corresponds to valid ESG framework identifier
 *
 * ## Security Considerations
 * - **Server-Side Authentication**: Authentication validation occurs server-side before content rendering
 * - **Route Protection**: Prevents unauthorized access to ESG model information
 * - **XSS Prevention**: React's built-in XSS protection for user-generated content rendering
 * - **Content Sanitization**: Markdown content properly sanitized through react-markdown component
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes Documentation
 * @see https://nextjs.org/docs/app/building-your-application/rendering/server-components Next.js Server Components
 * @see https://supabase.com/docs/guides/auth/server-side-rendering Supabase Server-Side Authentication
 * @see /apps/customer/app/customer/models/[model]/model-desc.tsx ESG Model Content Repository
 * @see /apps/customer/components/text/expandable-text.tsx Expandable Text Component
 * @see /apps/customer/components/page-header.tsx Page Header Component
 * @see /apps/customer/app/auth-utils.ts Authentication Utilities
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Dynamic ESG model information display page with authentication, glass-morphism design, and comprehensive framework descriptions
 * @example
 * ```tsx
 * // Access via URL routing
 * // /customer/models/sdg - Displays UN Sustainable Development Goals
 * // /customer/models/doughnut - Shows Doughnut Economic Model
 * // /customer/models/eko - Presents ekoIntelligence Framework
 *
 * // Component usage (internal - handled by Next.js routing)
 * <Page params={Promise.resolve({ model: 'sdg' })} />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import {checkAuth} from "@/app/auth-utils";
import {Card, CardContent} from "@/components/ui/card";
import React from "react";
import ExpandableText from "@/components/text/expandable-text";
import {modelDesc} from "./model-desc";
import {PageHeader} from "@/components/page-header";

export default async function Page(props:{params: Promise<{model:string}>}) {
    const params = await props.params;

    const {
        model
    } = params;

    const redirect = await checkAuth(`/customer`);
    console.log(modelDesc[model]);
    return (<>
            <PageHeader  title={modelDesc[model].title}></PageHeader>
            <Card className="mt-8 col-span-4 row-span-2">
                <CardContent className="text-left mx-auto">
                    <ExpandableText image={modelDesc[model].image} title={modelDesc[model].title}
                                    text={modelDesc[model].text} expanded={true}/>
                </CardContent>
            </Card>
        </>

    );
}
