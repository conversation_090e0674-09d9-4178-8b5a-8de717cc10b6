/**
 * Predictive ESG Analysis Dashboard Page - Comprehensive Future Impact Forecasting Interface
 *
 * This Next.js 15 App Router page component serves as the primary dashboard interface for viewing
 * predictive ESG (Environmental, Social, Governance) analysis within the EkoIntelligence customer application.
 * The component provides intelligent forecasting of entity ESG performance across multiple future years
 * with detailed risk and opportunity assessments, confidence scoring, and structured data visualization.
 *
 * ## Core Functionality
 * - **Multi-Year Predictive Analysis**: Interactive year-based tabs displaying comprehensive ESG forecasts
 * - **Confidence-Scored Predictions**: Advanced ML confidence indicators for prediction reliability assessment
 * - **Risk & Opportunity Mapping**: Structured visualization of potential ESG risks and opportunities
 * - **Detailed Analysis Display**: Rich markdown rendering for comprehensive predictive insights
 * - **Entity-Specific Forecasting**: Contextual predictions based on selected ESG entity analysis
 * - **Glass-morphism UI**: Modern translucent design with rounded corners for enhanced user experience
 * - **Loading State Management**: Elegant skeleton loaders during data fetching operations
 *
 * ## Predictive Analysis Model
 * The component processes predictive analysis data with the following comprehensive structure:
 * - **Summary Analysis**: High-level predictive overview with key findings and trends
 * - **Detailed Analysis**: Comprehensive markdown-formatted analysis with citations and methodology
 * - **Risk Assessment**: Array of potential ESG risks with impact and likelihood considerations
 * - **Opportunity Identification**: Array of potential ESG opportunities with strategic value propositions
 * - **Confidence Scoring**: ML-derived confidence percentage (0-100%) with visual indicator classification
 * - **Temporal Organization**: Multi-year analysis structure enabling trend comparison and strategic planning
 *
 * ## Database Integration & Schema Considerations
 * **⚠️ Database Schema Discrepancy Detected**:
 * The component queries `xfer_predictive_analysis` table (line 45), but database schema shows `xfer_predict_entity_year` table.
 * This indicates either:
 * - Missing database view/table that maps the expected interface
 * - Code requires update to query correct table name
 * - Data migration pending to create expected table structure
 *
 * **Expected Data Structure** (based on interface definition):
 * ```typescript
 * interface PredictiveAnalysis {
 *   id: number                        // Unique analysis identifier
 *   virtual_entity_id: number         // Target entity for prediction
 *   year: number                      // Prediction target year
 *   summary: string                   // Executive summary of predictions
 *   detailed_analysis: string         // Comprehensive markdown analysis
 *   potential_risks: string[]         // Array of identified ESG risks
 *   potential_opportunities: string[] // Array of strategic opportunities
 *   confidence: number                // ML confidence score (0.0-1.0)
 *   prediction_id: number             // Reference to prediction methodology
 *   source_cluster_id: number         // Source data cluster reference
 * }
 * ```
 *
 * ## State Management Architecture
 * **Primary Data State**:
 * - `analyses`: Record<number, PredictiveAnalysis[]> - Year-indexed predictive analysis collections
 * - `years`: number[] - Available prediction years sorted chronologically
 * - `activeYear`: number | null - Currently selected year for analysis viewing
 * - `loading`: boolean - Data fetching state for UI feedback
 *
 * **Data Processing Pipeline**:
 * 1. **Entity Validation**: Ensures valid entity context before initiating data fetch
 * 2. **Database Query**: Retrieves predictive analyses filtered by entity with confidence-based ordering
 * 3. **Data Transformation**: Processes JSON fields and normalizes array structures for display
 * 4. **Year Organization**: Groups analyses by year and generates sorted year navigation
 * 5. **UI State Updates**: Updates component state with processed data and activates default year view
 *
 * ## User Interface & Interaction Design
 * **Year Navigation System**:
 * - Tabbed interface using shadcn/ui `Tabs` component for intuitive year selection
 * - Automatic activation of earliest available year on initial load
 * - Responsive tab layout supporting multiple prediction years
 *
 * **Analysis Card Layout**:
 * - Glass-morphism styled cards with rounded borders (`rounded-3xl`)
 * - Confidence badge with semantic color coding (green: ≥70%, yellow: 40-69%, red: <40%)
 * - Structured content sections for summary, detailed analysis, risks, and opportunities
 * - Responsive grid layout adapting to viewport size
 *
 * **Content Rendering**:
 * - `EkoMarkdown` component for rich detailed analysis rendering with citation support
 * - Color-coded risk (red) and opportunity (green) lists for quick visual categorization
 * - Responsive two-column layout for balanced risk/opportunity comparison
 *
 * ## Component Dependencies & Integration
 * **Context Integration**:
 * - `useEntity()`: Retrieves current entity selection and context for targeted predictions
 * - Entity context provides `entity` field used for database query filtering
 *
 * **UI Component Dependencies**:
 * - `Card`, `CardContent`, `CardHeader`, `CardTitle`: shadcn/ui card components for layout structure
 * - `Tabs`, `TabsContent`, `TabsList`, `TabsTrigger`: shadcn/ui tabs for year navigation
 * - `Badge`: shadcn/ui badge component for confidence score display
 * - `Skeleton`: Loading state components during data fetching
 * - `EkoMarkdown`: Custom markdown renderer with citation and admin feature support
 * - `Headline`: Reusable page header component for consistent navigation
 *
 * **Utility Dependencies**:
 * - `runAsync`: React utility for safe asynchronous operations with cleanup
 * - `cn`: Tailwind CSS class name utility for conditional styling
 * - `createClient`: Supabase client factory for database operations
 *
 * ## Error Handling & User Experience
 * **Graceful Degradation**:
 * - Empty state handling when no predictions available with clear user guidance
 * - Loading skeleton maintains layout during data fetching operations
 * - Error logging with fallback states for failed database operations
 * - JSON parsing safety for potentially malformed database fields
 *
 * **Accessibility & Performance**:
 * - Semantic HTML structure with proper heading hierarchy
 * - Keyboard navigation support through shadcn/ui components
 * - Efficient state updates with conditional rendering
 * - Optimized re-renders through proper dependency management
 *
 * ## Security & Data Validation
 * - Entity context validation before database operations
 * - Safe JSON field processing with fallback to empty arrays
 * - Type-safe data transformation with interface compliance
 * - Supabase RLS (Row Level Security) integration through authenticated client
 *
 * @see {@link /Users/<USER>/worktrees/279/apps/customer/components/context/entity/entity-context.tsx} Entity Context Provider
 * @see {@link /Users/<USER>/worktrees/279/apps/customer/components/markdown/eko-markdown.tsx} EkoMarkdown Component
 * @see {@link /Users/<USER>/worktrees/279/tmp/db/customer/schemas/public/tables/xfer_predict_entity_year.sql} Database Schema
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Predictive ESG analysis dashboard with multi-year forecasting, risk assessment, and confidence scoring
 * @example
 * ```tsx
 * // Component usage within Next.js App Router
 * // /app/customer/dashboard/predictive/page.tsx
 * export default function PredictivePage() {
 *   // Automatic entity context integration
 *   // Multi-year prediction display
 *   // Interactive risk/opportunity visualization
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright 2025 EkoIntelligence. All rights reserved.
 */

'use client'

import React, { useEffect, useState } from 'react'
import { createClient } from '@/app/supabase/client'
import { useEntity } from '@/components/context/entity/entity-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { Headline } from '@/components/front-page/headline'
import { runAsync } from '@utils/react-utils'
import { Skeleton } from '@/components/ui/skeleton'
import { cn } from '@utils/lib/utils'

// Define the interface for predictive analysis data
interface PredictiveAnalysis {
  id: number
  virtual_entity_id: number
  year: number
  summary: string
  detailed_analysis: string
  potential_risks: string[]
  potential_opportunities: string[]
  confidence: number
  prediction_id: number
  source_cluster_id: number
}

export default function PredictivePage() {
  const entityContext = useEntity()
  const supabase = createClient()
  const [analyses, setAnalyses] = useState<Record<number, PredictiveAnalysis[]>>({})
  const [years, setYears] = useState<number[]>([])
  const [loading, setLoading] = useState(true)
  const [activeYear, setActiveYear] = useState<number | null>(null)

  useEffect(() => {
    if (!entityContext.entity) return

    runAsync(async () => {
      setLoading(true)

      // Fetch predictive analyses for this entity
      const { data, error } = await supabase
        .from('xfer_predictive_analysis')
        .select('*')
        .eq('virtual_entity_id', entityContext.entity as string)
        .order('year', { ascending: true })
        .order('confidence', { ascending: false })

      if (error) {
        console.error('Error fetching predictive analyses:', error)
        setLoading(false)
        return
      }

      if (!data || data.length === 0) {
        console.log('No predictive analyses found for this entity')
        setLoading(false)
        return
      }

      // Process the data
      const analysesByYear: Record<number, PredictiveAnalysis[]> = {}
      const yearsList: number[] = []

      data.forEach((analysis: any) => {
        // Parse JSON fields
        const processedAnalysis: PredictiveAnalysis = {
          ...analysis,
          potential_risks: Array.isArray(analysis.potential_risks)
            ? analysis.potential_risks
            : (typeof analysis.potential_risks === 'object'
                ? Object.values(analysis.potential_risks)
                : []),
          potential_opportunities: Array.isArray(analysis.potential_opportunities)
            ? analysis.potential_opportunities
            : (typeof analysis.potential_opportunities === 'object'
                ? Object.values(analysis.potential_opportunities)
                : [])
        }

        if (!analysesByYear[analysis.year]) {
          analysesByYear[analysis.year] = []
          yearsList.push(analysis.year)
        }

        analysesByYear[analysis.year].push(processedAnalysis)
      })

      // Sort years
      yearsList.sort((a, b) => a - b)

      setAnalyses(analysesByYear)
      setYears(yearsList)
      setActiveYear(yearsList[0] || null)
      setLoading(false)
    })
  }, [entityContext.entity])

  // Helper function to get confidence class
  const getConfidenceClass = (confidence: number) => {
    if (confidence >= 0.7) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    if (confidence >= 0.4) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <Headline>Predictive Analysis</Headline>
        <div className="grid grid-cols-1 gap-6">
          <Card className="glass-effect-lit rounded-3xl overflow-hidden">
            <CardHeader>
              <Skeleton className="h-8 w-64" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (years.length === 0) {
    return (
      <div className="p-6 space-y-6">
        <Headline>Predictive Analysis</Headline>
        <Card className="glass-effect-lit rounded-3xl overflow-hidden">
          <CardContent className="p-6">
            <p className="text-center text-slate-600 dark:text-slate-300">
              No predictive analyses available for this entity. Run a predictive analysis first.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <Headline>Predictive Analysis</Headline>

      {/* Year tabs */}
      <Tabs
        value={activeYear?.toString() || ''}
        onValueChange={(value) => setActiveYear(parseInt(value))}
        className="w-full"
      >
        <TabsList className="mb-4">
          {years.map(year => (
            <TabsTrigger key={year} value={year.toString()}>
              {year}
            </TabsTrigger>
          ))}
        </TabsList>

        {years.map(year => (
          <TabsContent key={year} value={year.toString()} className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              {analyses[year]?.map((analysis, index) => (
                <Card key={index} className="glass-effect-lit rounded-3xl overflow-hidden">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="text-xl">
                      {analysis.year} Prediction {index + 1}
                    </CardTitle>
                    <Badge className={cn("ml-2", getConfidenceClass(analysis.confidence))}>
                      Confidence: {Math.round(analysis.confidence * 100)}%
                    </Badge>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Summary</h3>
                      <p className="text-slate-700 dark:text-slate-300">{analysis.summary}</p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-2">Detailed Analysis</h3>
                      <div className="prose dark:prose-invert max-w-none">
                        <EkoMarkdown citations={null} admin={false}>{analysis.detailed_analysis}</EkoMarkdown>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-lg font-semibold mb-2">Potential Risks</h3>
                        <ul className="list-disc pl-5 space-y-1">
                          {analysis.potential_risks.map((risk, i) => (
                            <li key={i} className="text-red-700 dark:text-red-400">{risk}</li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold mb-2">Potential Opportunities</h3>
                        <ul className="list-disc pl-5 space-y-1">
                          {analysis.potential_opportunities.map((opportunity, i) => (
                            <li key={i} className="text-green-700 dark:text-green-400">{opportunity}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
