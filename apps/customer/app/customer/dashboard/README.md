# EkoIntelligence ESG Analysis Dashboard

## Overview

The EkoIntelligence ESG Analysis Dashboard is a comprehensive Next.js 15 App Router application module that serves as the central hub for Environmental, Social, and Governance (ESG) analysis within the customer application. This dashboard provides a unified interface for ESG practitioners, sustainability professionals, and analysts to access multi-modal ESG analysis capabilities including flag analysis, greenwashing detection, predictive analytics, and report generation.

Built with modern React patterns, glass-morphism design principles, and real-time data synchronization, the dashboard integrates with EkoIntelligence's dual-database architecture to provide seamless access to comprehensive ESG insights derived from corporate documents, regulatory filings, and sustainability reports.

## Specification

### Functional Requirements

The ESG Analysis Dashboard must provide:

1. **Centralized ESG Hub**: Single entry point for all ESG analysis modalities with consistent navigation and state management
2. **Interactive Data Visualization**: Real-time charts, gauges, and metrics with glass-morphism styling and responsive design
3. **Multi-Modal Analysis Access**: Seamless navigation between flags, greenwashing, predictive analysis, and reports
4. **Entity Context Management**: Persistent entity selection across all dashboard sections with URL parameter preservation
5. **Progressive Loading Experience**: Skeleton states, suspense boundaries, and optimized data fetching for large datasets
6. **Real-time Collaboration**: Integration with TipTap collaborative editing for document creation and sharing
7. **Responsive Design System**: Mobile-first approach with adaptive layouts for desktop, tablet, and mobile devices
8. **Authentication Integration**: Secure access with Supabase authentication and role-based permissions
9. **Glass-morphism UI**: Translucent design with heavily rounded elements, backdrop blur, and subtle animations
10. **AI-Powered Summaries**: Dynamic content generation with caching for section-level analysis insights

### Technical Architecture Requirements

The dashboard implements a layered architecture supporting:

- **App Router Integration**: Next.js 15 App Router with parallel routes, intercepting routes, and server components
- **Entity Context System**: Centralized state management for entity selection, model configuration, and run parameters
- **Progressive Enhancement**: Core functionality available without JavaScript, enhanced with client-side features
- **Database Integration**: Dual-database architecture with analytics backend and customer-facing synchronized data
- **Real-time Updates**: Live data synchronization through Supabase real-time subscriptions
- **Performance optimization**: Strategic caching, memoization, and virtual scrolling for large datasets

## Key Components

### Core Dashboard Files

| File | Purpose | Responsibilities |
|------|---------|------------------|
| `layout.tsx` | Dashboard infrastructure layout | EntityModelRunSelector integration, parallel routes support, modal infrastructure |
| `page.tsx` | Main dashboard overview page | ESG metrics visualization, comprehensive analysis summary, interactive charts |
| `client.tsx` | Navigation controller | Breadcrumb management, URL parameter preservation, client-side routing |

### Analysis Modules

| Directory | Purpose | Key Features |
|-----------|---------|--------------|
| `flags/` | ESG effect flags analysis | Interactive donut charts, flag detail modals, model-based classification system |
| `gw/` | Greenwashing detection hub | Claims analysis, promises tracking, cherry picking detection, vague language analysis |
| `prediction-v2/` | Predictive ESG analytics | Hierarchical analysis (entity → cluster → component), temporal filtering |
| `reports/` | Report management interface | Generated report access, categorized viewing, download capabilities |

### Modal and Navigation System

| Directory | Purpose | Responsibilities |
|-----------|---------|------------------|
| `@modal/` | Parallel route modals | Intercepting routes for flag details, analysis overlays without losing context |
| `predictive/` | Legacy predictive analysis | Maintained for backward compatibility, redirects to prediction-v2 |

## Dependencies

### Core Framework Dependencies

- **Next.js 15 App Router**: Modern React framework with server components, parallel routes, and intercepting routes
- **React 18+**: Latest React features including Suspense, concurrent rendering, and server/client boundaries
- **TypeScript 5+**: Full compile-time type safety with strict configuration and advanced type features

### Database & Authentication

- **Supabase Client**: PostgreSQL database with Row Level Security (RLS) policies and real-time subscriptions
- **Supabase Auth**: Integrated authentication with automatic token management and admin role detection
- **Database Architecture**:
  - `xfer_flags`: ESG flag data synchronized from analytics backend
  - `xfer_claims`: Claims vs evidence analysis results  
  - `xfer_promises`: Promise tracking and fulfillment data
  - `xfer_selective`: Cherry picking and flooding detection results
  - `xfer_predict_*`: Predictive analysis data (entity, cluster, component levels)

### UI Framework & Design System

- **Tailwind CSS**: Utility-first CSS framework with custom glass-morphism classes and responsive design
- **shadcn/ui**: Component library built on Radix UI primitives with consistent design tokens
- **Lucide React**: Modern SVG icon library with ESG-themed icons and accessibility features
- **Framer Motion**: Animation library for smooth transitions and micro-interactions

### External Service Dependencies

- **Google Gemini LLM**: AI content generation for real-time summary creation and analysis
- **TipTap Cloud**: Collaborative document editing with real-time synchronization
- **Vercel KV**: Response caching for improved performance and reduced API calls

### Internal Module Dependencies

```mermaid
graph TD
    A[Dashboard Layout] --> B[EntityContext Provider]
    A --> C[AuthContext Provider] 
    A --> D[NavContext Provider]
    
    B --> E[Entity Selection State]
    B --> F[Model Configuration]
    B --> G[Run Parameter Management]
    
    C --> H[User Authentication]
    C --> I[Admin Permissions]
    C --> J[Organization Context]
    
    D --> K[Breadcrumb Navigation]
    D --> L[URL Parameter Handling]
    
    A --> M[Analysis Modules]
    M --> N[Flags Dashboard]
    M --> O[Greenwashing Hub]
    M --> P[Predictive Analytics]
    M --> Q[Reports Interface]
    
    N --> R[Flag Visualization]
    O --> S[Multi-Modal Analysis]
    P --> T[Hierarchical Analysis]
    Q --> U[Report Management]
```

## Usage Examples

### Basic Dashboard Navigation

```typescript
// Main dashboard access
// URL: /customer/dashboard?entity=AAPL&run=latest&model=ekoIntelligence

// Entity context automatically loads:
const { entity, model, run, flagsData, isLoadingFlags } = useEntity();

// Navigation preserves parameters:
<Link href="/customer/dashboard/flags">View Flags Analysis</Link>
// Results in: /customer/dashboard/flags?entity=AAPL&run=latest&model=ekoIntelligence
```

### Module-Specific Analysis

```typescript
// Flags analysis with modal overlay
<Link href="/customer/dashboard/flags/123">
  View Flag Details
</Link>
// Opens intercepting route modal while preserving dashboard context

// Greenwashing analysis navigation  
<Link href="/customer/dashboard/gw/claims">
  View Claims Analysis
</Link>

// Predictive analysis with year filtering
<Link href="/customer/dashboard/prediction-v2">
  View Predictive Analysis
</Link>
```

### Entity Context Integration

```typescript
// Dashboard components automatically integrate with entity context
const entityContext = useEntity();

// Access analysis data
const { 
  entity,           // Selected entity identifier
  model,            // Analysis model (ekoIntelligence, SDG, etc.)
  run,              // Analysis run (latest, specific ID)
  flagsData,        // ESG flags data
  claimsData,       // Claims analysis data
  promisesData,     // Promise tracking data
  isLoadingFlags,   // Loading states
  queryString       // URL parameters for navigation
} = entityContext;

// Generate navigation links with preserved context
const dashboardLink = `/customer/dashboard?${queryString}`;
```

## Architecture Notes

### Next.js App Router Implementation

The dashboard leverages Next.js 15 App Router's advanced routing features:

```mermaid
graph TB
    A[app/customer/dashboard/] --> B[layout.tsx - Server Component]
    A --> C[page.tsx - Server Component] 
    A --> D[client.tsx - Client Component]
    A --> E[@modal/ - Parallel Route]
    
    B --> F[EntityModelRunSelector]
    B --> G[Navigation Infrastructure]
    B --> H[Modal Support]
    
    E --> I[Flag Detail Modals]
    E --> J[Analysis Overlays]
    E --> K[Intercepting Routes]
    
    A --> L[Nested Routes]
    L --> M[flags/]
    L --> N[gw/]
    L --> O[prediction-v2/]
    L --> P[reports/]
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant D as Dashboard Layout
    participant EC as EntityContext
    participant S as Supabase Client
    participant DB as Customer Database
    participant AB as Analytics Backend
    
    U->>D: Navigate to /customer/dashboard
    D->>EC: Initialize entity context
    EC->>S: Fetch entity configuration
    S->>DB: Query xfer_* tables
    DB-->>S: Return synchronized data
    S-->>EC: Analysis data
    EC-->>D: Provide dashboard context
    D-->>U: Render dashboard with data
    
    U->>D: Select different entity
    D->>EC: Update entity selection
    EC->>S: Fetch new entity data
    S->>DB: Query updated xfer_* tables
    DB-->>S: Return new analysis data
    S-->>EC: Updated data
    EC-->>D: Re-render with new context
```

### Glass-morphism Design System

The dashboard implements consistent glass-morphism styling:

- **Translucent Backgrounds**: `glass-effect-lit` with backdrop blur and reduced opacity over text
- **Rounded Corners**: Standard 1.5rem border radius (`rounded-3xl`) for modern aesthetic
- **Color-coded Sections**: Brand colors for positive actions, compliment colors for negative actions
- **Hover Animations**: `hover:scale-105` with smooth transitions and shadow effects
- **Responsive Breakpoints**: Mobile-first design with `lg:` and `xl:` breakpoints

## Known Issues

### Active Issues (From Linear Tickets)

#### EKO-211: Report Generation Completeness (High Priority)
- **Issue**: Generated reports may not include all negative flags displayed in dashboard
- **Impact**: Potential underrepresentation of RED flags in customer reports  
- **Status**: Todo - Assigned to Neil Ellis
- **Mitigation**: Dashboard displays complete flag data while report generation is enhanced

#### EKO-267: Dashboard Summary Polling (Resolved)
- **Issue**: AI-generated summaries finishing on server but client continuing to poll
- **Status**: Done - Fixed polling mechanism for summary completion
- **Resolution**: Improved client-server synchronization for summary generation

#### EKO-291: Claims Page Navigation (Resolved) 
- **Issue**: Sidebar navigation failing specifically on claims page while top navigation worked
- **Status**: Done - Fixed sidebar navigation across all analysis modules
- **Testing**: Comprehensive navigation tests added to prevent regression

#### EKO-158: Hallucinated Features (Resolved)
- **Issue**: Non-functional filter components on Claims/Promises pages
- **Status**: Done - Removed placeholder filter UI elements that had no backend implementation
- **Resolution**: Cleaned up UI to match actual feature availability

### Technical Debt

1. **Component Size**: Large dashboard page component (>1000 lines) needs refactoring into smaller, focused components
2. **State Management**: Complex entity context state could benefit from more granular context splitting
3. **Performance**: Large flag datasets could benefit from virtualization and pagination
4. **Error Boundaries**: Need more granular error boundaries for individual analysis modules
5. **Testing Coverage**: Modal interactions and parallel route behavior need comprehensive test coverage

### Browser Compatibility

- **Modern Browsers**: Full support for Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Glass-morphism Support**: Requires backdrop-filter support for visual effects
- **Mobile Optimization**: Touch-friendly interactions with responsive breakpoints
- **Accessibility**: WCAG 2.1 AA compliance with screen reader testing

## Future Work

### Planned Enhancements (Based on Linear Projects & Requirements)

#### Enhanced Dashboard Analytics (Q2 2025)
- **Advanced Metrics**: Multi-entity comparison dashboards with peer benchmarking
- **Trend Analysis**: Time-series visualization of ESG performance evolution
- **Custom Dashboards**: User-configurable dashboard layouts with widget management
- **Real-time Updates**: WebSocket integration for live data updates across all modules

#### Collaborative Features Enhancement (Q2 2025)
- **Multi-user Sessions**: Real-time collaborative analysis with live cursors and comments
- **Shared Workspaces**: Team-based dashboard sharing with role-based access controls
- **Annotation System**: Collaborative markup and discussion threads on analysis results
- **Export Enhancement**: Team collaboration features in PDF/Excel exports

#### Performance & Scalability (Q3 2025)
- **Virtual Scrolling**: Efficient rendering for large flag and analysis datasets
- **Progressive Data Loading**: Intelligent data fetching with predictive caching
- **Module Federation**: Micro-frontend architecture for independent module development
- **Edge Caching**: CDN optimization for static analysis assets and generated reports

#### Advanced AI Integration (Q3-Q4 2025)
- **Contextual AI Assistants**: Intelligent help system with analysis guidance
- **Automated Insights**: AI-powered trend detection and anomaly identification
- **Natural Language Queries**: Conversational interface for data exploration
- **Personalized Recommendations**: User-specific analysis suggestions based on behavior

### Requirements Tracking

All future work is tied to Linear projects and customer feedback:

- **ESG Dashboard Enhancement Project** (Active - Q2 2025)
- **Enterprise Collaboration Features** (Planned - Q2 2025)  
- **Performance Optimization Initiative** (Ongoing)
- **AI/ML Integration Expansion** (Q3-Q4 2025)

## Troubleshooting

### Common Issues

#### Dashboard Not Loading / Blank Screen
```bash
# Debug steps:
1. Check browser console for JavaScript errors
2. Verify Supabase connection in Network tab
3. Confirm entity context initialization
4. Check authentication status in AuthContext
5. Verify RLS policies allow data access

# Common causes:
- Authentication token expired or invalid
- Entity context not properly initialized
- Network connectivity issues
- RLS policy blocking data access
- JavaScript errors preventing React rendering
```

#### Entity Selection Not Persisting
```bash
# Troubleshooting:
1. Check URL parameters for entity, run, model values
2. Verify EntityContext provider wraps dashboard components
3. Check localStorage for any corruption
4. Verify navigation preserves query parameters
5. Test entity selection dropdown functionality

# Solutions:
- Clear browser cache and localStorage
- Verify EntityModelRunSelector component is working
- Check entity context state in React DevTools
- Ensure navigation links include queryString parameter
```

#### Analysis Data Not Loading
```bash
# Common solutions:
1. Verify entity has analysis data for selected run
2. Check database connectivity and query permissions
3. Confirm xfer_* table synchronization status
4. Verify analysis run completion in backend
5. Check for expired authentication tokens

# Debug commands:
- Check Supabase client connection status
- Verify user permissions: SELECT * FROM profiles WHERE id = ?
- Test data query: SELECT * FROM xfer_flags WHERE entity_xid = ?
- Review RLS policies for data access
```

#### Modal/Navigation Issues
```bash
# Debug steps:
1. Verify intercepting routes are properly configured
2. Check parallel route @modal slot implementation
3. Test modal opening/closing functionality
4. Verify navigation context preservation
5. Check for z-index conflicts in CSS

# Common fixes:
- Ensure @modal directory structure is correct
- Verify modal components return null when inactive
- Check navigation context for proper path management
- Test both direct URL access and modal overlay
```

### Performance Debugging

```typescript
// Add performance monitoring to dashboard components
useEffect(() => {
  const startTime = performance.now();
  
  return () => {
    const endTime = performance.now();
    console.log(`Dashboard render time: ${endTime - startTime}ms`);
  };
}, [entity, flagsData]);

// Monitor entity context loading performance
const { isLoadingFlags, isLoadingClaims, isLoadingPromises } = useEntity();
console.log('Loading states:', { isLoadingFlags, isLoadingClaims, isLoadingPromises });
```

### Database Query Optimization

```sql
-- Optimize flag queries with proper indexing
CREATE INDEX IF NOT EXISTS idx_xfer_flags_entity_run 
ON xfer_flags(entity_xid, run_id);

CREATE INDEX IF NOT EXISTS idx_xfer_claims_entity_run 
ON xfer_claims(entity_xid, run_id);

-- Verify query performance
EXPLAIN ANALYZE SELECT * FROM xfer_flags 
WHERE entity_xid = 'AAPL' AND run_id = 123;
```

## FAQ

### User-Centric Questions

**Q: How do I switch between different ESG analysis models?**
A: Use the EntityModelRunSelector at the top of the dashboard. Select from ekoIntelligence, UN SDG, Doughnut Model, or Plant-Based Treaty models. Your selection persists across all dashboard sections.

**Q: What's the difference between green and red flags?**
A: Green flags represent positive ESG actions (environmental benefits, social improvements, governance excellence), while red flags indicate negative impacts or concerns (environmental harm, social issues, governance problems). Impact scores use Bayesian methodology for accurate assessment.

**Q: Why do some sections show "Loading" while others have data?**
A: The dashboard uses progressive loading for optimal performance. Different analysis types (flags, claims, promises) load independently, so you can start exploring available data while other sections continue loading.

**Q: How do I access detailed analysis for a specific flag or claim?**
A: Click on any flag badge or claim item to open a detailed modal overlay. This preserves your dashboard context while providing comprehensive analysis including citations, confidence scores, and impact assessments.

**Q: Can I export or share dashboard analysis?**
A: Currently, you can share dashboard views via URL (parameters are preserved). Full export functionality for PDF/Excel reports is planned for Q3 2025. Generated reports are available in the Reports section.

### Developer Questions

**Q: How do I add a new analysis module to the dashboard?**
A: 1) Create new directory under `/customer/dashboard/`, 2) Implement page.tsx with analysis interface, 3) Add navigation links in layout, 4) Update EntityContext if new data types needed, 5) Test modal integration if required.

**Q: How does the entity context system work?**
A: EntityContext provides centralized state for entity selection, model configuration, and analysis data. It automatically syncs with URL parameters and provides loading states. Access via `useEntity()` hook in any dashboard component.

**Q: How do I customize the glass-morphism styling?**
A: Modify Tailwind CSS classes using the `glass-effect-*` utility classes. Key classes: `glass-effect-lit` (primary), `glass-effect-subtle` (secondary), `glass-effect-brand-strong-lit` (active states). Ensure backdrop-blur support for visual effects.

**Q: How do parallel routes and intercepting routes work in the dashboard?**
A: The `@modal` directory creates a parallel route slot for modal overlays. Intercepting routes like `(.)flags/[id]` capture navigation to display modals while preserving dashboard context. The layout renders both `children` and `modal` slots simultaneously.

**Q: How do I optimize dashboard performance for large datasets?**
A: Use progressive loading patterns, implement proper memoization with `useMemo` and `useCallback`, consider virtual scrolling for large lists, optimize database queries with proper indexing, and implement client-side caching for frequently accessed data.

## References

### Documentation

- [Next.js App Router Documentation](https://nextjs.org/docs/app) - Modern React framework with advanced routing
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript) - Database and authentication
- [React 18 Documentation](https://react.dev/blog/2022/03/29/react-v18) - Concurrent features and Suspense
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - Utility-first CSS framework

### Related Code Files

- [`/apps/customer/app/customer/layout.tsx`](../layout.tsx) - Customer application root layout
- [`/apps/customer/components/context/entity/entity-context.tsx`](../../../components/context/entity/entity-context.tsx) - Entity context provider
- [`/apps/customer/components/context/auth/auth-context.tsx`](../../../components/context/auth/auth-context.tsx) - Authentication context
- [`/apps/customer/components/context/nav/nav-context.tsx`](../../../components/context/nav/nav-context.tsx) - Navigation context
- [`/apps/customer/hooks/use-navigation-with-params.ts`](../../../hooks/use-navigation-with-params.ts) - Parameter-aware navigation

### External Dependencies

- [Lucide React Icons](https://lucide.dev/icons) - Modern SVG icon library
- [shadcn/ui Components](https://ui.shadcn.com/) - Component library built on Radix UI
- [Framer Motion](https://www.framer.com/motion/) - Animation library for React
- [TipTap Editor](https://tiptap.dev/) - Collaborative rich text editor

### ESG Framework References

- [UN Sustainable Development Goals](https://sdgs.un.org/goals) - Official SDG framework
- [Doughnut Economics](https://doughnuteconomics.org/) - Kate Raworth's economic framework  
- [Plant Based Treaty](https://plantbasedtreaty.org/) - Environmental initiative
- [SASB Standards](https://sasb.org/) - Sustainability accounting standards

### Testing Resources

- [`/apps/customer/tests/`](../../tests/) - Playwright test suite for customer application
- [Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/) - Component testing
- [Playwright Documentation](https://playwright.dev/) - End-to-end testing framework

### Third-Party Information

- [Web Accessibility Guidelines (WCAG)](https://www.w3.org/WAI/WCAG21/quickref/) - Accessibility standards
- [Glass-morphism Design Principles](https://uxdesign.cc/glassmorphism-in-user-interfaces-1f39bb1308c9) - UI design trends
- [ESG Reporting Standards](https://www.ifrs.org/content/dam/ifrs/publications/pdf-files/english/2023/issued/part-a/issb-2023-a-s2-climate-related-disclosures.pdf) - Climate-related disclosures

### Linear Issue References

- [EKO-211: Report Generation Completeness](https://linear.app/ekointelligence/issue/EKO-211) - High Priority
- [EKO-267: Dashboard Summary Polling](https://linear.app/ekointelligence/issue/EKO-267) - Resolved
- [EKO-291: Claims Page Navigation](https://linear.app/ekointelligence/issue/EKO-291) - Resolved  
- [EKO-158: Hallucinated Features](https://linear.app/ekointelligence/issue/EKO-158) - Resolved
- [EKO-227: Entity Context Preservation](https://linear.app/ekointelligence/issue/EKO-227) - Resolved

---

## Changelog

### 2025-07-31

- **CREATED**: Comprehensive README.md documentation for ESG Analysis Dashboard module
- **ANALYZED**: Complete dashboard architecture including layout, page components, and navigation system
- **DOCUMENTED**: System integration with Next.js 15 App Router, parallel routes, and intercepting routes
- **RESEARCHED**: Dependencies including React 18+, Supabase, TipTap, and glass-morphism design system
- **SPECIFIED**: Usage examples covering entity context, navigation patterns, and modal integration
- **IDENTIFIED**: Known issues from Linear tickets including report generation and navigation fixes
- **PLANNED**: Future work roadmap based on Linear projects and enterprise requirements
- **PROVIDED**: Comprehensive troubleshooting guide with common issues and performance debugging
- **CREATED**: FAQ section addressing user workflows and developer implementation questions
- **COMPILED**: Extensive reference links to documentation, code files, and external resources

---

(c) All rights reserved ekoIntelligence 2025