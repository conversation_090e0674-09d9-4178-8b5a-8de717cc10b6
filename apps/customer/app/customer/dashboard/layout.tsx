/**
 * Next.js App Router Layout Component for ESG Dashboard Infrastructure
 *
 * This React layout component serves as the foundational structure for the EkoIntelligence ESG 
 * (Environmental, Social, Governance) dashboard section within the customer application. Built 
 * for Next.js 15 App Router architecture, this layout provides the core infrastructure for ESG 
 * analysis interfaces, entity reporting dashboards, and collaborative analysis tools. The component 
 * implements a glass-morphism design system with modern UI patterns optimized for enterprise-grade 
 * ESG data visualization and user interaction workflows.
 *
 * ## Core Functionality
 * - **Dashboard Container Structure**: Provides flexible layout foundation for ESG dashboard pages
 * - **Entity Context Integration**: Incorporates EntityModelRunSelector for entity, model, and run selection
 * - **Modal Support**: Built-in support for Next.js parallel routes modal patterns via dedicated modal slot
 * - **Navigation Context**: Establishes dashboard navigation breadcrumb structure for user orientation
 * - **Glass-morphism Layout**: Implements translucent dashboard container with proper responsive design
 * - **Client-Side Rendering**: Optimized for interactive dashboard components requiring client-side state
 *
 * ## Next.js App Router Architecture
 * **Layout File Convention**: This layout.tsx file follows Next.js 15 App Router conventions, where 
 * layout components wrap all pages within the `/customer/dashboard/` route segment. The layout:
 * - Wraps all nested dashboard pages (flags, claims, promises, etc.)
 * - Persists across page navigation within the dashboard section
 * - Maintains entity/model/run selection state through navigation
 * - Supports parallel routes through the `modal` prop for overlay interfaces
 *
 * **Parallel Routes Support**: The component accepts a `modal` prop that enables Next.js parallel 
 * routes pattern, allowing dashboard pages to display modal overlays without losing page context.
 * This supports features like detailed entity analysis overlays, report generation modals, and 
 * advanced filtering interfaces.
 *
 * ## Entity Selection Infrastructure
 * **EntityModelRunSelector Integration**: The layout incorporates the EntityModelRunSelector component 
 * that provides centralized controls for:
 * - **Entity Selection**: Dropdown for choosing which ESG entity (company/organization) to analyze
 * - **Model Selection**: Choice between analysis models (ekoIntelligence, SDG, Doughnut, Plant Based Treaty)
 * - **Run Selection**: Historical analysis run selection with date/time context
 * - **Disclosure Toggle**: Switch for including/excluding disclosure documents in analysis
 * - **Navigation Breadcrumbs**: Contextual navigation showing user's current location in dashboard
 *
 * ## Design System Integration
 * **Glass-morphism Aesthetic**: The layout implements the platform's signature glass-morphism design 
 * with:
 * - Translucent container backgrounds with backdrop-blur effects
 * - Heavily rounded corners (1.5rem border radius standard)
 * - Subtle depth and layering through shadow and opacity
 * - Modern glass-like surfaces that reduce opacity when displaying text content
 * - Responsive design patterns optimized for desktop and mobile ESG analysis workflows
 *
 * ## Component Architecture
 * **Layout Structure**:
 * ```jsx
 * <div className="dashboard-container"> // Main flex container
 *   <EntityModelRunSelector />          // Entity/model/run controls
 *   {children}                         // Nested dashboard pages
 *   {modal}                           // Parallel route modals
 *   <div id="simple-modal-root" />    // Modal portal root
 * </div>
 * ```
 *
 * **Modal Integration**: The layout provides infrastructure for multiple modal patterns:
 * - **Parallel Route Modals**: Via the `modal` prop for Next.js intercepted routes
 * - **Portal Modals**: Via the `simple-modal-root` element for programmatic modal rendering
 * - **Entity Detail Overlays**: For detailed entity analysis without losing dashboard context
 *
 * ## Database Integration Context
 * This layout operates within the broader EkoIntelligence data architecture:
 * - **Customer Database**: Supabase PostgreSQL with user authentication and entity access controls
 * - **Analytics Database**: Backend Python system for ESG analysis processing and scoring
 * - **Data Synchronization**: Real-time updates through `xfer_` table synchronization between systems
 * - **Entity Management**: Integration with entity relationship mapping and ESG scoring systems
 *
 * ## User Experience Features
 * **Persistent Navigation State**: The EntityModelRunSelector maintains user selections across 
 * page navigation within the dashboard, ensuring continuity of analysis context. Users can switch 
 * between different ESG analysis views (flags, claims, promises, predictive analysis) while 
 * maintaining their selected entity, model, and time period context.
 *
 * **Responsive Design**: The layout adapts to different screen sizes:
 * - Mobile: Condensed controls with priority information
 * - Tablet: Balanced layout with essential controls visible
 * - Desktop: Full control suite with detailed information panels
 *
 * ## Performance Considerations
 * **Client-Side Optimization**: The "use client" directive ensures this layout and its child 
 * components can leverage client-side state management, real-time updates, and interactive 
 * features essential for dashboard functionality.
 *
 * **State Management**: Integrates with the platform's entity context system for efficient 
 * state sharing across dashboard components without prop drilling or excessive re-renders.
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/layouts-and-templates Next.js Layouts Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing/parallel-routes Next.js Parallel Routes
 * @see https://nextjs.org/docs/app/building-your-application/routing/intercepting-routes Next.js Route Interception
 * @see {@link ../../components/context/entity/emr-selector.tsx} EntityModelRunSelector Component
 * @see {@link ../../components/context/entity/entity-context.tsx} Entity Context Provider
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Next.js App Router layout component providing dashboard infrastructure for ESG analysis interfaces with entity selection controls and modal support
 * @example
 * ```jsx
 * // This layout automatically wraps all pages in /customer/dashboard/
 * // Pages inherit entity selection context and modal support
 * export default function DashboardPage() {
 *   const { entity, model, run } = useEntity(); // Access selected context
 *   return <div>ESG Analysis for {entity}</div>;
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";
import React from 'react'
import { EntityModelRunSelector } from '@/components/context/entity/emr-selector'


export default function Layout({children, modal}: { children: any, modal: any }) {

    const navPath = [
        {
            href: "/customer/dashboard/",
            label: "Dashboard",
        }
    ]

    return (
        <>
            <div className="relative dashboard-container flex flex-col justify-center">
                <EntityModelRunSelector navPath={navPath}/>
                {children}
            </div>
            {modal}
            <div id="simple-modal-root"></div>
        </>);
}
