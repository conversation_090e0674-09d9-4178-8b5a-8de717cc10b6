/**
 * Next.js Intercepting Route Modal Page for ESG Promise Detail Display
 *
 * This file implements a Next.js 15 App Router intercepting route modal that displays
 * detailed ESG promise tracking and fulfillment analysis from the analytics pipeline.
 * It uses the parallel routes and intercepting routes pattern `@modal/(.)gw/promises/[id]`
 * to show promise details as a modal overlay while preserving the underlying dashboard
 * page state and navigation context.
 *
 * ## Core Functionality
 * - **Promise Detail Display**: Shows comprehensive ESG promise analysis including fulfillment status,
 *   greenwashing assessment, confidence scores, and AI-generated analysis with supporting evidence
 * - **Modal Interface**: Uses intercepting route pattern `(.)gw/promises/[id]` to overlay promise details
 *   without losing dashboard navigation context while maintaining URL state
 * - **Authentication Integration**: Provides admin-level features and controls based on user permissions
 * - **Real-time Data Fetching**: Retrieves promise data from Supabase `xfer_promises` table with type safety
 * - **Glass-morphism UI**: Implements modern glass-morphism design consistent with platform styling
 * - **Evidence Visualization**: Displays supporting evidence and citations for promise verification
 *
 * ## Route Pattern & Navigation Architecture
 * - **Route Path**: `/@modal/(.)gw/promises/[id]/page.tsx`
 * - **Intercepting Pattern**: `(.)` intercepts `/gw/promises/[id]` at the same directory level
 * - **Dynamic Segment**: `[id]` captures the promise ID parameter for database querying
 * - **Modal Behavior**: Opens as overlay modal while preserving underlying page URL and navigation state
 * - **Navigation Integration**: Closes modal and returns to previous page using `router.back()` from SimpleModal
 * - **Deep Linking**: Supports direct URL access which renders full page instead of modal
 *
 * ## Key Features & Capabilities
 * - **Supabase Integration**: Fetches promise data from `xfer_promises` table with Row Level Security
 * - **Type Safety**: Uses TypeScript with PromiseTypeV2 interface for compile-time type checking
 * - **Error Handling**: Graceful error handling with console logging for development debugging
 * - **Loading States**: Proper loading state management with conditional rendering (`data &&`)
 * - **Accessibility**: Keyboard navigation support and ARIA attributes via SimpleModal component
 * - **Responsive Design**: Mobile-first design with adaptive modal sizing and glass-morphism effects
 * - **Admin Controls**: Conditional admin features like delete buttons based on user permissions
 *
 * ## Data Flow & Processing Pipeline
 * 1. Component receives dynamic route parameter `id` from Next.js App Router using `use()` hook
 * 2. Creates authenticated Supabase client for secure database access with RLS policies
 * 3. Fetches promise data from `xfer_promises` table using promise ID with single record query
 * 4. Performs safe type casting from Supabase Json type to PromiseTypeV2 for application consistency
 * 5. Extracts authentication context for admin permission checks
 * 6. Passes promise data and admin status to PromiseCardV2 component for detailed rendering
 * 7. Renders promise details within SimpleModal component with glass-morphism styling and animations
 *
 * ## Database Integration & Schema
 * - **Primary Table**: `xfer_promises` - Contains processed ESG promise tracking and fulfillment results
 * - **Query Pattern**: Single record lookup by ID with all fields selected (`select('*')`)
 * - **Data Structure**: Hybrid model with direct columns (id, kept, summary, conclusion) and JSONB model field
 * - **Type Safety**: Safe casting from Supabase Json type to application-specific PromiseTypeV2 interface
 * - **Security**: Row Level Security policies ensure authenticated users can only access authorized data
 * - **Performance**: Optimized indexes on entity_xid, run_id, and JSONB model for fast lookups
 *
 * ## System Architecture Context
 * This modal fits into the broader ESG analysis and promise tracking system:
 * - **Analytics Backend**: Python system generates promise fulfillment analysis and greenwashing detection
 * - **Data Sync Layer**: `xfer_promises` tables synchronize data between analytics and customer databases
 * - **API Layer**: This modal provides direct database access for promise detail visualization
 * - **Frontend Dashboard**: Customer dashboard consumes this modal for promise detail overlays
 * - **Modal System**: Part of comprehensive parallel routes modal system for dashboard detail views
 * - **Authentication**: Integrated with Supabase Auth and custom authorization middleware
 *
 * ## Promise Analysis Components
 * - **Promise Status**: Visual indicators for kept/not kept/uncertain status with colored badges
 * - **Confidence Scoring**: AI confidence levels in promise fulfillment assessment
 * - **Evidence Display**: Supporting evidence from multiple sources with year-over-year tracking
 * - **Citation System**: Academic-style citations with public URLs and document references
 * - **Greenwashing Detection**: LLM-powered greenwashing analysis and flagging
 * - **Context Information**: Additional context and background for promise statements
 *
 * ## Related Components & Dependencies
 * - **PromiseCardV2**: Main promise detail display component with comprehensive analysis visualization
 * - **SimpleModal**: Reusable modal wrapper with glass-morphism styling, animations, and accessibility
 * - **AuthContext**: Provides user authentication state and admin permission checks
 * - **Supabase Client**: Database client for authenticated data access with automatic token management
 * - **Next.js 15 App Router**: Modern React framework with advanced routing and intercepting capabilities
 * - **TypeScript**: Full type safety with generated database types and interface definitions
 *
 * ## Performance & Security Considerations
 * - **Authentication Required**: Uses Supabase Row Level Security for granular data access control
 * - **Type Safety**: Full TypeScript coverage prevents runtime type errors and improves reliability
 * - **Optimized Queries**: Single record lookups with minimal data transfer overhead
 * - **Error Boundaries**: Console logging for debugging while maintaining user experience
 * - **Modal Optimization**: Conditional rendering and lazy loading for performance
 * - **Client-Side Security**: Secure client-side authentication with automatic token refresh
 *
 * ## Usage Examples & Patterns
 * ```typescript
 * // Accessed via intercepting route when navigating to:
 * // /customer/dashboard/gw/promises/123
 * // Opens as modal overlay while preserving dashboard context
 * 
 * // Direct URL access:
 * // /customer/dashboard/gw/promises/123
 * // Renders full page view instead of modal overlay
 * 
 * // Modal closes via:
 * // - ESC key press
 * // - Close button click
 * // - Background click
 * // - router.back() navigation
 * ```
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/intercepting-routes Next.js Intercepting Routes
 * @see https://nextjs.org/docs/app/building-your-application/routing/parallel-routes Next.js Parallel Routes
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security
 * @see https://supabase.com/docs/reference/javascript/select Supabase JavaScript Client
 * @see {@link ../../../gw/promises/promise.tsx} PromiseCardV2 Component
 * @see {@link ../../../../../components/simple-modal.tsx} SimpleModal Component
 * @see {@link ../../../../../components/context/auth/auth-context.tsx} AuthContext
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Intercepting route modal page that displays detailed ESG promise tracking analysis with
 * fulfillment status, greenwashing assessment, confidence scores, and comprehensive supporting evidence.
 * @example ```bash
 * # Navigate to promise modal from dashboard:
 * curl -X GET 'http://localhost:3000/customer/dashboard/gw/promises/123'
 * # Opens modal overlay while preserving dashboard state
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

"use client";

import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { PromiseTypeV2 } from '@/types'
import { useAuth } from '@/components/context/auth/auth-context'
import { SimpleModal } from '@/components/simple-modal'
import { PromiseCardV2 } from '@/app/customer/dashboard/gw/promises/promise'

export default function Page(props: { params: Promise<{ id: string }> }) {
    const params = use(props.params);
    const {id} = params;

    const [data, setData] = useState<PromiseTypeV2 | null>(null);
    const auth = useAuth();

    useEffect(() => {
        const fetchData = async () => {
            const supabase = createClient();

            const {
                data: promiseData,
                error
            } = await supabase.from('xfer_promises').select('*').eq('id', +id).single();

            if (error) {
                console.error("Error fetching promise:", error);
            } else {
                // Cast to unknown first to avoid TypeScript errors with Json type
                setData(promiseData as unknown as PromiseTypeV2);
            }
        };

        fetchData();
    }, [id]);

    return data && (
        <SimpleModal testId="promise-detail-modal">
            <PromiseCardV2 item={data!} admin={auth.admin}/>
        </SimpleModal>
    );
}
