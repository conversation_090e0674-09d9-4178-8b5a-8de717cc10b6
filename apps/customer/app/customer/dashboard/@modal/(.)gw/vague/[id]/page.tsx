/**
 * Next.js App Router Intercepting Route: ESG Vague Terms Modal Component
 *
 * This intercepting route component provides modal overlay functionality for displaying detailed 
 * vague term analysis within the ESG dashboard. It leverages Next.js App Router's intercepting 
 * routes convention to show vague term details as a modal when accessed via client-side navigation,
 * while preserving direct URL access for deep-linking and sharing capabilities.
 *
 * ## Core Functionality
 * - **Modal Intercept Pattern**: Uses `(.)gw/vague/[id]` pattern to intercept `/gw/vague/[id]` routes at same level
 * - **Vague Term Analysis Display**: Shows detailed ESG vagueness analysis including scores, explanations, and citations
 * - **Legacy Data Conversion**: Converts deprecated V2 vague term format to V1 for backward compatibility
 * - **Authentication Integration**: Respects user authentication state and admin privileges for data access
 * - **Database Integration**: Fetches vague term data from `_deprecated_xfer_gw_vague_v2` table via Supabase
 *
 * ## Route Pattern & Navigation
 * - **Intercepting Route**: `/@modal/(.)gw/vague/[id]/page.tsx` intercepts `/gw/vague/[id]` 
 * - **Modal Display**: Opens as overlay when navigated to from dashboard (client-side navigation)
 * - **Direct Access**: Falls back to regular page when accessed directly via URL
 * - **Dynamic Parameters**: Accepts `id` parameter for specific vague term lookup
 * - **Close Behavior**: Modal dismissal navigates back in browser history via `router.back()`
 *
 * ## Data Processing Pipeline
 * 1. **Route Parameter Extraction**: Uses React `use()` hook for async route params in App Router
 * 2. **Database Query**: Queries `_deprecated_xfer_gw_vague_v2` table by numeric ID via Supabase
 * 3. **Data Conversion**: Converts V2 format to legacy V1 format using `convertVagueV2ToVagueV1()`
 * 4. **State Management**: Manages loading state with `useState` and `useEffect` hooks
 * 5. **Modal Rendering**: Displays converted data in `VagueTerm` component within `SimpleModal`
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with intercepting routes and async params
 * - **Supabase Client**: Database client for ESG vague term data access with RLS security
 * - **React 18**: Component framework with concurrent features and hooks
 * - **Authentication Context**: User session management and admin privilege checking
 * - **Glass-morphism UI**: Modal component with translucent design system styling
 *
 * ## Database Schema Integration
 * - **Source Table**: `_deprecated_xfer_gw_vague_v2` with columns: `id`, `entity_xid`, `run_id`, `model`, `phrase`
 * - **JSONB Model Field**: Contains detailed analysis data including scores, explanations, and citations
 * - **Data Conversion**: Transforms JSONB model to legacy `VagueType` interface structure
 * - **Authentication**: Row Level Security (RLS) policies enforce data access permissions
 * - **Query Pattern**: Single record lookup by ID with type conversion for numeric parameter
 *
 * ## System Architecture
 * This intercepting route fits into the broader ESG analysis system:
 * - **Analytics Backend**: Python system analyzes corporate statements for vague language patterns
 * - **Data Sync Layer**: `xfer_` tables synchronize vague term analysis from analytics to customer database
 * - **Modal Layer**: This component provides user-friendly modal access to vague term details
 * - **Dashboard Integration**: Seamless modal overlay within dashboard without full page navigation
 * - **Legacy Support**: Maintains compatibility with existing vague term display components
 *
 * ## Security & Performance
 * - Database access through Supabase RLS (Row Level Security) policies for secure data access
 * - Authentication context integration ensures proper user session validation
 * - Client-side data fetching with loading states for responsive user experience
 * - Modal portal rendering prevents layout shift and provides clean overlay experience
 * - Error handling with fallback for missing or invalid vague term data
 *
 * ## Related Components
 * - **VagueTerm Component**: Core display component for vague term analysis details
 * - **SimpleModal Component**: Reusable modal wrapper with glass-morphism styling and accessibility
 * - **Dashboard Vague Terms List**: Parent component that triggers modal navigation
 * - **Vague Utils**: Data conversion utilities for V2 to V1 format compatibility
 * - **Authentication Context**: User session and permission management system
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/intercepting-routes Next.js Intercepting Routes
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation  
 * @see https://react.dev/reference/react/use React use() Hook Documentation
 * @see {@link /app/customer/dashboard/gw/vague/vague-term.tsx} VagueTerm Component
 * @see {@link /components/simple-modal.tsx} SimpleModal Component
 * @see {@link /utils/vague-utils.ts} Vague Term Conversion Utilities
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This intercepting route component provides modal overlay functionality for displaying detailed vague term analysis within the ESG dashboard using Next.js App Router conventions.
 * @example ```tsx
// Usage: Modal opens when navigating from dashboard to /gw/vague/123
<Link href="/gw/vague/123">View Vague Term</Link>
```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";

import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { VagueType } from '@/types'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/context/auth/auth-context'
import { VagueTerm } from '@/app/customer/dashboard/gw/vague/vague-term'
import { SimpleModal } from '@/components/simple-modal'
import { convertVagueV2ToVagueV1 } from '@/utils/vague-utils'

export default function Page(props: { params: Promise<{ id: string }> }) {
    const params = use(props.params);

    const {
        id
    } = params;

    const [vagueData, setVagueData] = useState<VagueType | null>(null);
    const router = useRouter();
    const auth = useAuth();
    useEffect(() => {
        const fetchData = async () => {
            const supabase = createClient();

            // Try to get vague term from _deprecated_xfer_gw_vague_v2 first
            const {
                data: vagueV2Data,
                error: vagueV2Error
            } = await supabase
              .from('_deprecated_xfer_gw_vague_v2')
                .select("*")
                .eq("id", +id)
                .single();

            if (vagueV2Data) {
                // Convert V2 vague term to V1 format
                const convertedVague = convertVagueV2ToVagueV1(vagueV2Data);
                setVagueData(convertedVague);
            } else {
                // No need to fallback anymore
                console.log("Vague term not found in V2 table");
            }
        };

        fetchData();
    }, [id]);

    if (!vagueData) return null;

    return (
      <SimpleModal>
                    <VagueTerm item={vagueData} admin={auth.admin}/>
      </SimpleModal>
    );
}
