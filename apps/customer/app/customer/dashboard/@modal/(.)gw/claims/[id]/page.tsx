/**
 * Next.js Intercepting Route Modal Page for ESG Claim Detail Display
 *
 * This file implements a Next.js 15 App Router intercepting route modal that displays
 * detailed ESG claim verification and greenwashing analysis from the analytics pipeline.
 * It uses the parallel routes and intercepting routes pattern `@modal/(.)gw/claims/[id]`
 * to show claim details as a modal overlay while preserving the underlying dashboard
 * page state and navigation context.
 *
 * ## Core Functionality
 * - **Claim Detail Display**: Shows comprehensive ESG claim analysis including verification status,
 *   greenwashing assessment, confidence scores, and AI-generated analysis with citations
 * - **Modal Interface**: Uses intercepting route pattern `(.)gw/claims/[id]` to overlay claim details
 *   without losing dashboard navigation context
 * - **Authentication Integration**: Provides admin-level features and controls based on user permissions
 * - **Real-time Data Fetching**: Retrieves claim data from Supabase `xfer_claims` table with type safety
 * - **Glass-morphism UI**: Implements modern glass-morphism design consistent with platform styling
 *
 * ## Route Pattern & Navigation
 * - **Route Path**: `/@modal/(.)gw/claims/[id]/page.tsx`
 * - **Intercepting Pattern**: `(.)` intercepts `/gw/claims/[id]` at the same directory level
 * - **Dynamic Segment**: `[id]` captures the claim ID parameter for data fetching
 * - **Modal Behavior**: Opens as overlay modal while preserving underlying page URL and state
 * - **Navigation Integration**: Closes modal and returns to previous page using `router.back()`
 *
 * ## Key Features
 * - **Supabase Integration**: Fetches claim data from `xfer_claims` table with Row Level Security
 * - **Type Safety**: Uses TypeScript with ClaimTypeV2 interface for compile-time type checking
 * - **Error Handling**: Graceful error handling with console logging for debugging
 * - **Loading States**: Proper loading state management during data fetching operations
 * - **Accessibility**: Keyboard navigation support and ARIA attributes via SimpleModal
 * - **Responsive Design**: Mobile-first design with adaptive modal sizing
 *
 * ## Data Flow & Processing
 * 1. Component receives dynamic route parameter `id` from Next.js router
 * 2. Creates Supabase client for authenticated database access
 * 3. Fetches claim data from `xfer_claims` table using claim ID
 * 4. Performs type casting from Supabase Json type to ClaimTypeV2 for type safety
 * 5. Passes claim data and admin status to ClaimDetailV2 component for rendering
 * 6. Renders claim details within SimpleModal component with glass-morphism styling
 *
 * ## Database Integration
 * - **Table**: `xfer_claims` - Contains processed ESG claim verification results
 * - **Query Pattern**: Single record lookup by ID with all fields selected
 * - **Type Casting**: Safely converts Supabase Json type to application types
 * - **Security**: Row Level Security policies ensure proper data access control
 *
 * ## System Architecture Context
 * This modal fits into the broader ESG analysis system:
 * - **Analytics Backend**: Python system generates claim verification and greenwashing analysis
 * - **Data Sync Layer**: `xfer_claims` tables synchronize data between analytics and customer databases
 * - **API Layer**: This modal provides direct database access for claim details
 * - **Frontend**: Customer dashboard consumes this modal for claim detail visualization
 * - **Modal System**: Part of comprehensive modal system for dashboard detail views
 *
 * ## Related Components & Dependencies
 * - **ClaimDetailV2**: Main claim detail display component with comprehensive analysis
 * - **SimpleModal**: Reusable modal wrapper with glass-morphism styling and animations
 * - **AuthContext**: Provides user authentication state and admin permission checks
 * - **Supabase Client**: Database client for authenticated data access
 * - **Next.js 15 App Router**: Modern React framework with advanced routing capabilities
 *
 * ## Performance & Security
 * - **Authentication Required**: Uses Supabase Row Level Security for data access control
 * - **Type Safety**: Full TypeScript coverage prevents runtime type errors
 * - **Optimized Queries**: Single record lookups with minimal data transfer
 * - **Error Boundaries**: Console logging for debugging and development visibility
 * - **Modal Optimization**: Lazy loading and efficient state management
 *
 * ## Usage Examples
 * ```typescript
 * // Accessed via intercepting route when navigating to:
 * // /customer/dashboard/gw/claims/123
 * // Opens as modal overlay while preserving dashboard context
 * ```
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/intercepting-routes Next.js Intercepting Routes
 * @see https://nextjs.org/docs/app/building-your-application/routing/parallel-routes Next.js Parallel Routes
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security
 * @see {@link ../../../gw/claims/claim-detail-v2.tsx} ClaimDetailV2 Component
 * @see {@link ../../../../../components/simple-modal.tsx} SimpleModal Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Intercepting route modal page that displays detailed ESG claim verification analysis
 * with greenwashing assessment, confidence scores, and comprehensive AI-generated analysis in modal overlay.
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

"use client";

import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { ClaimTypeV2 } from '@/types/claim'
import { useAuth } from '@/components/context/auth/auth-context'
import ClaimDetailV2 from '@/app/customer/dashboard/gw/claims/claim-detail-v2'
import { SimpleModal } from '@/components/simple-modal'

export default function Page(props: { params: Promise<{ id: string }> }) {
    const params = use(props.params);
    const { id } = params;

    const [claimData, setClaimData] = useState<ClaimTypeV2 | null>(null);
    const auth = useAuth();

    useEffect(() => {
        const fetchData = async () => {
            const supabase = createClient();

            const { data, error } = await supabase
              .from('xfer_claims')
                .select("*")
                .eq("id", +id)
                .single();

            if (error) {
                console.error("Error fetching claim:", error);
            } else {
                // Cast to unknown first to avoid TypeScript errors with Json type
                setClaimData(data as unknown as ClaimTypeV2);
            }
        };

        fetchData();
    }, [id]);

    if (!claimData) return null;

    return (
        <SimpleModal>
            <ClaimDetailV2 claim={claimData} admin={auth.admin} />
        </SimpleModal>
    );
}
