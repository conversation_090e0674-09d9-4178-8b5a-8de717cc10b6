/**
 * Next.js Intercepting Route Modal Page for ESG Effect Flag Detail Display
 *
 * This file implements a Next.js 15 App Router intercepting route modal that displays
 * detailed ESG effect flag information from the analytics pipeline. It uses the parallel
 * routes and intercepting routes pattern to show flag details as a modal overlay while
 * preserving the underlying dashboard page state and navigation context.
 *
 * ## Core Functionality
 * - **Flag Detail Display**: Shows comprehensive ESG effect flag analysis including impact scores,
 *   model classifications, and AI-generated analysis text with citations
 * - **Modal Interface**: Uses intercepting route pattern `(.)flags/[id]` to overlay flag details
 *   without losing dashboard navigation context
 * - **Authentication Integration**: Provides admin-level features and controls based on user permissions
 * - **Real-time Data Fetching**: Retrieves flag data from Supabase `xfer_flags` table with type safety
 * - **Issue Mapping**: Creates contextual issue maps for enhanced flag information display
 * - **Glass-morphism UI**: Implements modern glass-morphism design consistent with platform styling
 *
 * ## Route Pattern
 * - **Route Path**: `/@modal/(.)flags/[id]/page.tsx`
 * - **Intercepting Pattern**: `(.)` intercepts `/flags/[id]` at the same directory level
 * - **Dynamic Segment**: `[id]` captures the flag ID parameter for data fetching
 * - **Search Parameters**: Accepts `entity`, `run`, and `model` query parameters for context
 *
 * ## Key Features
 * - **Supabase Integration**: Fetches flag data from `xfer_flags` table with Row Level Security
 * - **Type Safety**: Uses TypeScript with FlagTypeV2 interface for compile-time type checking
 * - **Error Handling**: Graceful error handling with user-friendly toast notifications
 * - **Loading States**: Proper loading state management during data fetching operations
 * - **Accessibility**: Keyboard navigation support and ARIA attributes for screen readers
 * - **Responsive Design**: Mobile-first design with adaptive modal sizing
 *
 * ## Data Flow
 * 1. Component receives dynamic route parameters (id) and search parameters (entity, run, model)
 * 2. Creates Supabase client and fetches flag data from `xfer_flags` table
 * 3. Processes flag data through `ensureModelParsed` utility for model consistency
 * 4. Creates empty issue map for flag detail display component
 * 5. Renders flag details within SimpleModal component with glass-morphism styling
 *
 * ## Dependencies
 * - **Next.js 15 App Router**: Modern React framework with advanced routing capabilities
 * - **Supabase Client**: Database client for real-time data access and authentication
 * - **React 18+**: Latest React features including Suspense and concurrent rendering
 * - **TypeScript**: Compile-time type safety and enhanced developer experience
 * - **Custom Hooks**: useAuth, useToast, and useRouter for state management
 *
 * ## Related Components
 * - **FlagExpandedDetail**: Main flag detail display component with comprehensive analysis
 * - **SimpleModal**: Reusable modal wrapper with glass-morphism styling and animations
 * - **AuthContext**: Provides user authentication state and admin permission checks
 * - **ToastProvider**: User feedback system for success and error notifications
 *
 * ## Database Schema
 * - **Table**: `xfer_flags` - Contains processed ESG effect flags from analytics pipeline
 * - **Key Fields**: id, run_id, entity_xid, flag_type, flag_summary, flag_analysis, model
 * - **Security**: Row Level Security policies ensure proper data access permissions
 * - **Relationships**: Links to entities, runs, and model sections for comprehensive analysis
 *
 * ## Security & Performance
 * - **Authentication Required**: Uses Supabase Row Level Security for data access control
 * - **Type Safety**: Full TypeScript coverage prevents runtime type errors
 * - **Error Boundaries**: Graceful degradation with user-friendly error messages
 * - **Optimized Queries**: Selective field fetching to minimize data transfer
 * - **Navigation Handling**: Proper cleanup and navigation state management
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/intercepting-routes Next.js Intercepting Routes
 * @see https://nextjs.org/docs/app/building-your-application/routing/parallel-routes Next.js Parallel Routes
 * @see https://supabase.com/docs/guides/getting-started/tutorials/with-nextjs Supabase Next.js Integration
 * @see {@link ../../flags/flag-expanded-detail.tsx} FlagExpandedDetail Component
 * @see {@link ../../../../../components/simple-modal.tsx} SimpleModal Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Intercepting route modal page that displays detailed ESG effect flag analysis with
 * impact scores, model classifications, and comprehensive AI-generated analysis in a modal overlay.
 * @docgen doc-by-claude
 * 
 * (c) All rights reserved ekoIntelligence 2025
 */

"use client";
import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { createIssueMap } from '@/components/issues'
import { useAuth } from '@/components/context/auth/auth-context'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'
import { FlagExpandedDetail } from '@/app/customer/dashboard/flags/flag-expanded-detail'
import { SimpleModal } from '@/components/simple-modal'
import { FlagTypeV2 } from '@/types'
import { ensureModelParsed } from '@/utils/flag-converter'

export default function Page(
    props: {
        params: Promise<{ id: string }>,
        searchParams: Promise<{ entity: string, run: string, model: string }>
    }
) {
    const searchParams = use(props.searchParams);

    const {
        entity,
        run,
        model
    } = searchParams;

    const params = use(props.params);

    const {
        id
    } = params;

    const supabase = createClient();
    const [open, setOpen] = useState(true);
    const [issuesData, setIssuesData] = useState<any>(null);
    const [flagData, setFlagData] = useState<FlagTypeV2 | null>(null);
    const [issuesError, setIssuesError] = useState<any>(null);
    const [flagError, setFlagError] = useState<any>(null);
    const {toast} = useToast();
    const auth = useAuth();
    const router = useRouter();

    useEffect(() => {
        async function fetchData() {
            try {
                // Model sections are now directly attached to each flag
                // No need to query xfer_issues
                setIssuesData([]);
                setIssuesError(null);

                // Get flag from the xfer_flags table - optimized query
                let { data: flagV2, error: flagErrV2 } = await supabase
                  .from('xfer_flags')
                  .select(`
                      id, 
                      run_id, 
                      entity_xid, 
                      flag_type, 
                      flag_summary, 
                      flag_analysis,
                      flag_statements,
                      model
                    `)
                    .eq("id", +id)
                    .single();

                if (flagV2) {
                    // First convert to unknown, then to FlagTypeV2 to avoid type errors
                    // This is safe because ensureModelParsed will properly parse the model
                    const parsedFlag = ensureModelParsed(flagV2 as unknown as FlagTypeV2);
                    setFlagData(parsedFlag);
                    setFlagError(null);
                } else {
                    setFlagError(flagErrV2);
                    if(flagErrV2) {
                        toast({title: "Error", description: "Flag not found", variant: "destructive"})
                    }
                }
            } catch (error) {
                console.error(error);
                toast({title: "Error", description: "An error occurred while fetching data", variant: "destructive"})
            }
        }
        fetchData();
    }, [id, supabase]);

    useEffect(() => { if(open !== null && open === false) router.back()},[open]);

    const issueMap = issuesData && createIssueMap(issuesData);

    return issueMap && flagData && (
        <SimpleModal testId="flag-detail-modal">
                <FlagExpandedDetail flag={flagData} issueMap={issueMap}
                                    admin={auth.admin}/>
        </SimpleModal>
    );
}
