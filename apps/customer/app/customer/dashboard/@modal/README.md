# ESG Dashboard Modal System (@modal)

## Overview

The `@modal` directory implements a sophisticated modal overlay system for the EkoIntelligence ESG dashboard using
Next.js 15 App Router's parallel routes and intercepting routes patterns. This system provides seamless modal displays
for detailed ESG analysis views (flags, claims, promises, vague terms) while preserving the underlying dashboard
navigation context and state.

The modal system leverages the `@modal` parallel route slot to render detailed content overlays without triggering full
page navigation, creating a fluid user experience that maintains dashboard context while providing access to
comprehensive ESG analysis data.

## Specification

### Core Architecture Requirements

The modal system must:

1. **Preserve Navigation Context**: Modal overlays maintain the underlying dashboard page state and navigation
2. **Support Deep Linking**: Direct URL access renders full-page views instead of modals
3. **Type-Safe Data Integration**: Seamless integration with Supabase database and TypeScript interfaces
4. **Accessibility Compliance**: Full keyboard navigation, focus management, and screen reader support
5. **Glass-morphism Design**: Consistent with platform's modern glass-morphism visual design system
6. **Performance Optimization**: Efficient data fetching with minimal re-renders and proper loading states

### Route Interception Pattern

The system uses Next.js intercepting routes with the `(.)` pattern to capture same-level routes:

- `(.)flags/[id]` intercepts `/flags/[id]` routes
- `(.)gw/claims/[id]` intercepts `/gw/claims/[id]` routes
- `(.)gw/promises/[id]` intercepts `/gw/promises/[id]` routes
- `(.)gw/vague/[id]` intercepts `/gw/vague/[id]` routes

### Data Flow Specification

```mermaid
sequenceDiagram
    participant U as User
    participant D as Dashboard
    participant M as Modal System
    participant S as Supabase
    participant C as Content Component

    U->>D: Clicks ESG item link
    D->>M: Intercepts route via (.) pattern
    M->>S: Fetches item data by ID
    S-->>M: Returns typed data
    M->>C: Renders content in SimpleModal
    C-->>U: Displays modal overlay
    U->>M: Dismisses modal (ESC/click outside)
    M->>D: Router.back() to dashboard
```

## Key Components

### Core Files

| File                           | Purpose                    | Responsibilities                                                      |
|--------------------------------|----------------------------|-----------------------------------------------------------------------|
| `default.tsx`                  | Parallel route fallback    | Returns `null` for inactive modal slot, ensures clean modal closure   |
| `(.)flags/[id]/page.tsx`       | ESG flag detail modal      | Displays comprehensive flag analysis with impact scores and citations |
| `(.)gw/claims/[id]/page.tsx`   | Claims verification modal  | Shows claim verification status and greenwashing assessment           |
| `(.)gw/promises/[id]/page.tsx` | Promise tracking modal     | Displays promise fulfillment analysis and evidence                    |
| `(.)gw/vague/[id]/page.tsx`    | Vague terms analysis modal | Shows vague language analysis and scoring                             |

### Modal Content Components

- **FlagExpandedDetail**: Comprehensive ESG effect flag analysis with DEMISE model classifications
- **ClaimDetailV2**: Claims verification with confidence scores and supporting evidence
- **PromiseCardV2**: Promise fulfillment tracking with year-over-year analysis
- **VagueTerm**: Vague language analysis with scoring and explanations

### Infrastructure Components

- **SimpleModal**: Reusable modal wrapper with glass-morphism styling and accessibility features
- **AuthContext**: User authentication and admin permission management
- **ToastProvider**: User feedback system for success and error notifications

## Dependencies

### Framework Dependencies

- **Next.js 15 App Router**: Modern React framework with parallel routes and intercepting routes
- **React 18+**: Latest React features including Suspense, concurrent rendering, and `use()` hook
- **TypeScript**: Full compile-time type safety with generated database types

### Database & Authentication

- **Supabase Client**: Real-time database access with Row Level Security (RLS)
- **Authentication**: Supabase Auth integration with automatic token management
- **Database Tables**: `xfer_flags`, `xfer_claims`, `xfer_promises`, `_deprecated_xfer_gw_vague_v2`

### External Dependencies

- **Custom Hooks**: `useAuth`, `useToast`, `useRouter` for state management
- **Utility Functions**: Type converters, data transformers, and validation utilities
- **Glass-morphism UI**: Custom CSS classes and design system components

### Internal Module Dependencies

```mermaid
graph TD
    A[@modal Directory] --> B[SimpleModal Component]
    A --> C[Dashboard Layout]
    A --> D[ESG Content Components]
    A --> E[Supabase Client]
    A --> F[Authentication Context]
    A --> G[Type Definitions]
    A --> H[Utility Functions]
    
    B --> I[Glass-morphism Styles]
    B --> J[Portal Rendering]
    D --> K[FlagExpandedDetail]
    D --> L[ClaimDetailV2]  
    D --> M[PromiseCardV2]
    D --> N[VagueTerm]
    
    E --> O[Database Tables]
    E --> P[Row Level Security]
    G --> Q[TypeScript Interfaces]
```

## Usage Examples

### Opening Modals via Navigation

```typescript
// From dashboard components - opens as modal overlay
<Link href="/flags/123">View Flag Detail</Link>
<Link href="/gw/claims/456">View Claim Analysis</Link>
<Link href="/gw/promises/789">View Promise Status</Link>
```

### Direct URL Access

```bash
# Opens as full page (no modal overlay)
https://app.ekointel.app/customer/dashboard/flags/123
https://app.ekointel.app/customer/dashboard/gw/claims/456
```

### Programmatic Modal Closure

```typescript
// In modal components
const router = useRouter();

// Close modal and return to dashboard
router.back();
```

### Type-Safe Data Fetching

```typescript
// Flag modal example
const { data: flagV2, error } = await supabase
  .from('xfer_flags')
  .select(`
    id, run_id, entity_xid, flag_type, 
    flag_summary, flag_analysis, model
  `)
  .eq("id", +id)
  .single();

const parsedFlag = ensureModelParsed(flagV2 as unknown as FlagTypeV2);
```

## Architecture Notes

### Parallel Routes System

The dashboard layout implements Next.js parallel routes to simultaneously render:

```typescript
// Dashboard layout structure
export default function Layout({
  children,  // Main dashboard content
  modal,     // Modal overlay slot (@modal)
}: {
  children: React.ReactNode
  modal: React.ReactNode
}) {
  return (
    <>
      {children}
      {modal}
    </>
  )
}
```

### Route Resolution Flow

```mermaid
flowchart TD
    A[User Clicks ESG Item] --> B{Navigation Type}
    B -->|Client-side| C[Intercepting Route Triggered]
    B -->|Direct URL| D[Regular Page Route]
    
    C --> E[@modal Parallel Slot]
    E --> F[Modal Component Renders]
    F --> G[SimpleModal Wrapper]
    G --> H[Content Component]
    
    D --> I[Full Page Component]
    I --> J[Direct Content Render]
    
    H --> K[Modal Overlay Display]
    J --> K
```

### State Management Architecture

```mermaid
stateDiagram-v2
    [*] --> Inactive: default.tsx returns null
    Inactive --> Loading: Route intercepted
    Loading --> DataFetch: Component mounts
    DataFetch --> Rendering: Data received
    Rendering --> Active: Modal displayed
    Active --> Closing: User dismisses
    Closing --> Inactive: router.back()
    
    DataFetch --> Error: Fetch fails
    Error --> Inactive: Error handled
```

### Glass-morphism Design System

The modal system implements a consistent glass-morphism design:

- **Translucent backgrounds**: Frosted glass effect with backdrop blur
- **Rounded corners**: Generous border radius (1.5rem standard)
- **Subtle animations**: Hover effects and transition animations
- **Color variations**: Multiple glass color classes for visual hierarchy
- **Depth layers**: Z-index management for proper modal stacking

## Known Issues

### Active Issues

#### EKO-289: Modal Dismissal Bug (RESOLVED)

- **Status**: Fixed in PR #242
- **Issue**: Modal dismissed when clicking any content inside modal
- **Resolution**: Implemented proper backdrop click detection and blur effects
- **Tests**: Comprehensive unit tests in `/apps/customer/unit-tests/issues/issue-eko-289.spec.tsx`

#### EKO-291: Navigation Z-Index Conflicts (RESOLVED)

- **Status**: Fixed in PR #243
- **Issue**: Sidebar navigation blocked by modal overlays on claims page
- **Resolution**: Corrected z-index layering and modal portal rendering
- **Affected**: Claims page navigation specifically

### Technical Debt

1. **Legacy Data Conversion**: Vague terms modal still uses deprecated V2 to V1 conversion
2. **Type Casting**: Some components require `as unknown` casting for Supabase Json types
3. **Error Handling**: Inconsistent error handling patterns across different modal types
4. **Loading States**: Some modals lack proper skeleton loading states

### Browser Compatibility

- **Modern Browsers**: Full support for Chrome, Firefox, Safari, Edge
- **Accessibility**: Screen reader tested with NVDA and VoiceOver
- **Mobile**: Responsive design with touch-friendly modal dismissal

## Future Work

### Planned Enhancements

#### EKO-295: Enhanced Modal Animation System

- **Goal**: Implement smooth enter/exit animations using Framer Motion
- **Timeline**: Q1 2025
- **Dependencies**: Animation library integration

#### EKO-301: Modal Performance Optimization

- **Goal**: Implement lazy loading and data preloading for faster modal displays
- **Timeline**: Q2 2025
- **Benefits**: Reduced Time to Interactive (TTI) for modal content

#### EKO-310: Advanced Modal Features

- **Goal**: Multi-tab modals, modal chaining, and modal history management
- **Timeline**: Q2 2025
- **Use Cases**: Complex ESG analysis workflows

### Technical Improvements

1. **Unified Type System**: Standardize all modal data types to eliminate casting
2. **Error Boundary Integration**: Implement comprehensive error boundaries for modal crashes
3. **Progressive Enhancement**: Ensure modals degrade gracefully without JavaScript
4. **Performance Monitoring**: Add Core Web Vitals tracking for modal interactions

### Requirements Tracking

All future work is tied to Linear projects:

- **ESG Dashboard Enhancement Project** (ID: 2a668be0-6907-449a-8e3e-3b6808a5500c)
- **User Experience Improvements** (Planned)
- **Performance Optimization Initiative** (Planned)

## Troubleshooting

### Common Issues

#### Modal Not Opening

```bash
# Check route pattern matches
# Verify intercepting route file exists at correct path
# Confirm parallel route slot is rendered in layout

# Debug steps:
1. Check browser network tab for failed requests
2. Verify data exists in Supabase table
3. Check authentication status and RLS policies
```

#### Modal Dismissal Problems

```bash
# Common causes:
1. Event propagation not stopped in modal content
2. Portal rendering issues
3. Z-index conflicts with other components

# Solutions:
- Verify SimpleModal backdrop click handlers
- Check modal portal root element exists
- Review CSS z-index values
```

#### Data Loading Failures

```bash
# Debugging data issues:
1. Check Supabase connection status
2. Verify table and column names match
3. Confirm RLS policies allow data access
4. Check authentication token validity

# Common SQL errors:
- Column does not exist: Check select clause
- RLS policy violation: Verify user permissions
- Type casting errors: Review interface definitions
```

#### Routing Issues

```bash
# Route interception not working:
1. Verify file path follows (.) convention exactly
2. Check parent layout includes {modal} prop
3. Confirm no conflicting route handlers

# Direct URL access not working:
1. Verify corresponding page route exists
2. Check dynamic route parameter naming
3. Confirm no middleware blocking requests
```

### Performance Debugging

```typescript
// Add performance monitoring to modal components
useEffect(() => {
  const startTime = performance.now();
  
  return () => {
    const endTime = performance.now();
    console.log(`Modal render time: ${endTime - startTime}ms`);
  };
}, []);
```

### Authentication Debugging

```typescript
// Debug auth issues in modal components
const { user, admin } = useAuth();
console.log('Auth state:', { 
  user: !!user, 
  admin, 
  userId: user?.id 
});
```

## FAQ

### User-Centric Questions

**Q: Why do some modals take time to load?**
A: Modals fetch real-time data from the database. Loading time depends on network speed and data complexity. We're
implementing skeleton loaders to improve perceived performance.

**Q: Can I bookmark modal URLs?**
A: Yes! Direct URL access opens content as a full page instead of a modal. This enables sharing and bookmarking of
specific ESG analysis items.

**Q: How do I close a modal?**
A: Multiple ways: Press ESC key, click outside the modal area, click the close button (if present), or use browser back
button.

**Q: Why can't I interact with the background while modal is open?**
A: This is intentional UX design. The background is blurred and interaction is disabled to focus attention on the modal
content and improve accessibility.

**Q: Do modals work on mobile devices?**
A: Yes, modals are fully responsive and work on all device sizes. Touch gestures for dismissal are supported.

### Developer Questions

**Q: How do I add a new modal type?**
A: Create a new intercepting route file following the `(.)path/[id]/page.tsx` pattern, implement the data fetching
logic, and wrap content in `SimpleModal`.

**Q: Can I customize modal animations?**
A: Currently, animations are handled by the `SimpleModal` component. Custom animations can be added by extending the
component or creating new modal wrappers.

**Q: How do I handle modal-specific error states?**
A: Use the `useToast` hook for user notifications and implement try-catch blocks around data fetching. Consider error
boundaries for component-level error handling.

**Q: Why use parallel routes instead of a modal library?**
A: Parallel routes provide URL-based modal state, better SEO, improved accessibility, and seamless integration with
Next.js App Router features.

**Q: How do I test modal functionality?**
A: See existing tests in `/apps/customer/unit-tests/issues/issue-eko-289.spec.tsx` for modal interaction testing
patterns. Use Playwright for end-to-end modal testing.

## References

### Documentation

- [Next.js App Router Parallel Routes](https://nextjs.org/docs/app/building-your-application/routing/parallel-routes)
- [Next.js Intercepting Routes](https://nextjs.org/docs/app/building-your-application/routing/intercepting-routes)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [React 18 Concurrent Features](https://react.dev/blog/2022/03/29/react-v18)

### Code Files

- [`/apps/customer/app/customer/dashboard/layout.tsx`](../layout.tsx) - Dashboard layout with parallel routes
- [`/apps/customer/components/simple-modal.tsx`](../../../../components/simple-modal.tsx) - Base modal component
- [`/apps/customer/components/context/auth/auth-context.tsx`](../../../../components/context/auth/auth-context.tsx) -
  Authentication context
- [`/apps/customer/types/`](../../../../types/) - TypeScript interface definitions

### External Dependencies

- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [React Portal API](https://react.dev/reference/react-dom/createPortal)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

### Testing Resources

- [`/apps/customer/unit-tests/issues/issue-eko-289.spec.tsx`](../../unit-tests/issues/issue-eko-289.spec.tsx) - Modal
  dismissal tests
- [`/apps/customer/tests/`](../../tests/) - Playwright test suite
- [Testing Library React](https://testing-library.com/docs/react-testing-library/intro/)

### Linear Issues

- [EKO-289: Modal Dismissal Bug](https://linear.app/ekointelligence/issue/EKO-289)
- [EKO-291: Navigation Z-Index Issues](https://linear.app/ekointelligence/issue/EKO-291)
- [EKO-279: Admin Pages Development](https://linear.app/ekointelligence/issue/EKO-279)

### Third-Party Information

- [Web Accessibility Guidelines (WCAG)](https://www.w3.org/WAI/WCAG21/quickref/)
- [MDN Modal Dialog Patterns](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/dialog_role)
- [Glass-morphism Design Principles](https://uxdesign.cc/glassmorphism-in-user-interfaces-1f39bb1308c9)

---

## Changelog

### 2025-07-27

- Initial comprehensive documentation created
- Added system architecture diagrams
- Documented all existing modal implementations
- Included troubleshooting and FAQ sections
- Added references and development context

---

(c) All rights reserved ekoIntelligence 2025
