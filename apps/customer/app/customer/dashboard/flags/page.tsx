/**
 * ESG Entity Analysis Flags Page - Customer Dashboard Route Component
 *
 * This Next.js App Router page component serves as the main entry point for the ESG (Environmental, Social,
 * Governance) entity analysis flags dashboard within the EkoIntelligence customer application. It orchestrates
 * the display of comprehensive ESG flag analysis, impact visualization, and interactive donut charts for
 * entities selected within the customer dashboard navigation flow.
 *
 * ## Core Functionality
 * - **ESG Flags Dashboard**: Primary interface for viewing entity-specific ESG analysis flags and impact scores
 * - **Interactive Visualization**: Displays donut charts, flag summaries, and detailed analysis sections
 * - **Navigation Integration**: Integrates with entity context and dashboard navigation for seamless user experience
 * - **Responsive Layout**: Glass-morphism design system with mobile-responsive flag visualization components
 * - **Back-to-Top Utility**: Smooth scroll navigation for lengthy ESG flag analysis reports
 *
 * ## Route Structure
 * **URL Pattern**: `/customer/dashboard/flags`
 * **Access Level**: Authenticated customers with entity analysis permissions
 * **Layout Integration**: Inherits from customer dashboard layout with sidebar navigation and entity selection
 *
 * ## Component Architecture
 * This page acts as a composition root for two main components:
 * 1. **EntityAnalysisReport**: The primary ESG flags analysis dashboard with donut charts and flag details
 * 2. **BackToTop**: Utility component providing smooth scroll-to-top functionality for long reports
 *
 * ## Data Dependencies
 * The page relies on data flow through React Context providers:
 * - **EntityContext**: Provides selected entity, analysis model, run data, and filtering preferences
 * - **AuthContext**: Supplies user authentication state and admin privilege information
 * - **NavContext**: Manages breadcrumb navigation and dashboard state synchronization
 *
 * ## Database Integration
 * Indirectly integrates with customer database through EntityAnalysisReport component:
 * - **xfer_flags Table**: ESG flags data synchronized from analytics backend processing
 * - **xfer_model_sections Table**: ESG framework metadata for flag categorization and donut chart segments
 * - **Row Level Security**: Supabase RLS policies ensure users only access authorized entity data
 *
 * ## Key Features Integration
 * **ESG Analysis Pipeline**:
 * - Processes entity-specific ESG flags generated by Python analytics backend
 * - Displays positive (green) and negative (red) sustainability flags with impact scoring
 * - Organizes flags by ESG model sections (SDG, ESG frameworks) with visual donut chart representation
 * - Provides AI-powered summaries and detailed flag analysis with citation management
 *
 * **User Experience Enhancements**:
 * - Glass-morphism design consistent with platform aesthetic and heavily rounded UI elements
 * - Responsive design supporting mobile, tablet, and desktop viewing experiences
 * - Progressive loading states with skeleton components during data fetching operations
 * - Interactive elements with hover animations and smooth transitions for enhanced usability
 *
 * ## System Architecture
 * This page fits into the broader EkoIntelligence ESG analysis ecosystem:
 * - **Analytics Backend**: Python system processes corporate documents and generates ESG flags
 * - **Data Synchronization**: xfer_ tables synchronize processed analysis from analytics to customer database
 * - **Customer Dashboard**: This page provides user-facing visualization of backend ESG analysis results
 * - **Entity Navigation**: Integrated with platform navigation for entity browsing and flag exploration
 * - **Impact Assessment**: Connects to impact scoring system for environmental and social impact analysis
 *
 * ## Performance Characteristics
 * - **Client-Side Rendering**: Uses "use client" directive for interactive React components and state management
 * - **Context Optimization**: Leverages React Context for efficient data sharing without prop drilling
 * - **Component Memoization**: EntityAnalysisReport implements React.useMemo for optimized flag processing
 * - **Lazy Loading**: Dynamic imports and code splitting for optimal bundle size and loading performance
 *
 * ## Security & Access Control
 * - **Authentication Required**: Page accessible only to authenticated users via customer dashboard layout
 * - **Entity-Based Access**: Users can only view flags for entities they have authorization to analyze
 * - **Admin Features**: Conditional admin functionality for flag deletion and trace analysis (in EntityAnalysisReport)
 * - **RLS Enforcement**: Database-level security ensures data access compliance and user authorization
 *
 * ## Related Routes & Components
 * - `/customer/dashboard` - Parent dashboard with entity selection and navigation
 * - `/customer/dashboard/flags/[id]` - Individual flag detail pages for comprehensive flag analysis
 * - EntityContext Provider - Centralized entity data and filtering state management
 * - Glass-morphism UI Components - Consistent design system throughout customer application
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts Next.js Pages and Layouts
 * @see https://react.dev/reference/react/use-client React use client directive
 * @see https://tailwindcss.com/docs/backdrop-blur Tailwind CSS Glass-morphism styling
 * @see {@link ./entity-analysis-report.tsx} Primary ESG analysis dashboard component
 * @see {@link ../../../../components/back-to-top.tsx} Scroll-to-top utility component  
 * @see {@link ../../../components/context/entity/entity-context.tsx} Entity context provider
 * @see {@link ../../../../types/index.ts} TypeScript type definitions for ESG flags
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This Next.js App Router page component serves as the main entry point for the ESG entity analysis flags dashboard within the EkoIntelligence customer application.
 * @example ```tsx
  // Route usage in Next.js App Router
  // URL: /customer/dashboard/flags
  // Automatically integrates with:
  // - EntityContext for entity data and filtering
  // - Customer dashboard layout and navigation
  // - Authentication and authorization system
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";
import { EntityAnalysisReport } from "./entity-analysis-report";
import { BackToTop } from "@/components/back-to-top";

export default function Page() {
    return (
        <>
            <EntityAnalysisReport />
            <BackToTop />
        </>
    )
}
