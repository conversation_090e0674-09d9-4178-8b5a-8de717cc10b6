/**
 * React AutoComplete Component for Feature Flag Dashboard
 *
 * This reusable AutoComplete component provides an accessible, keyboard-navigable dropdown interface
 * for searching and selecting options within the EkoIntelligence ESG platform's feature flag dashboard.
 * Built on top of the cmdk library, it offers a modern command palette-style interface with real-time
 * search filtering, loading states, and full keyboard accessibility support.
 *
 * ## Core Functionality
 * - **Search Filtering**: Real-time search as users type, filtering options by label text matching
 * - **Keyboard Navigation**: Full keyboard support including Enter to select, Escape to close, arrow keys for navigation
 * - **Selection Management**: Controlled value state with callback support for parent component integration
 * - **Loading States**: Built-in skeleton loading UI for asynchronous data fetching scenarios
 * - **Visual Feedback**: Clear selected state indicators with checkmark icons for improved UX
 * - **Accessibility**: ARIA-compliant implementation with proper focus management and screen reader support
 * - **Empty States**: User-friendly messaging when no matching options are found
 *
 * ## Design System Integration
 * - **Glass-morphism Styling**: Consistent with EkoIntelligence's translucent, heavily rounded design aesthetic
 * - **Responsive Layout**: Adaptive interface that works seamlessly across desktop and mobile devices
 * - **Animation Support**: Smooth fade-in/zoom-in animations for dropdown appearance using Tailwind CSS
 * - **Dark Mode Compatible**: Full support for dark/light theme switching throughout the interface
 * - **Icon Integration**: Lucide React icons for visual consistency with the broader application
 *
 * ## Component Architecture
 * - **Option Type System**: Flexible option structure supporting value/label pairs plus additional metadata
 * - **Controlled Component**: Supports both controlled and uncontrolled usage patterns for maximum flexibility
 * - **Event Handling**: Comprehensive keyboard and mouse event handling with proper event propagation
 * - **State Management**: Internal state for dropdown visibility, input focus, and selection tracking
 * - **Performance Optimized**: useCallback hooks prevent unnecessary re-renders during user interactions
 *
 * ## Key Features
 * ### Search and Selection
 * - Real-time filtering as users type in the input field
 * - Enter key selection from filtered results
 * - Click-to-select functionality with mouse/touch support
 * - Automatic input clearing and focus management after selection
 *
 * ### Keyboard Accessibility
 * - **Enter**: Selects the current option from search results
 * - **Escape**: Closes the dropdown and clears focus
 * - **Arrow Keys**: Navigate through available options (handled by cmdk)
 * - **Tab**: Standard tab navigation for accessibility compliance
 *
 * ### Visual Feedback
 * - Selected options display checkmark icons for clear identification
 * - Hover states on interactive elements for improved usability
 * - Focus indicators that meet WCAG accessibility guidelines
 * - Skeleton loading placeholders during async operations
 *
 * ## Integration Context
 * This component is specifically designed for the feature flag dashboard within the customer portal,
 * where administrators select from lists of entities, users, organizations, or feature flag options.
 * It integrates with the broader EkoIntelligence system architecture including:
 * - **Feature Flag Management**: Selection of entities for flag configuration and targeting
 * - **Admin Dashboard**: User and organization selection for administrative operations
 * - **ESG Entity Browser**: Searching and selecting companies for analysis configuration
 * - **Permission Systems**: Integration with Supabase RLS for secure data access
 *
 * ## Technical Dependencies
 * - **cmdk**: Command palette library providing core search and navigation functionality
 * - **Lucide React**: SVG icon library for consistent visual elements (Check icons)
 * - **React 18+**: Modern React with hooks for state management and lifecycle handling
 * - **Tailwind CSS**: Utility-first CSS framework for responsive styling and animations
 * - **shadcn/ui**: Design system components (Command, Skeleton) for consistent UI patterns
 * - **Class Variance Authority**: Utility for conditional CSS class management
 *
 * ## Related Components
 * - **Command Components**: Uses CommandGroup, CommandInput, CommandItem, CommandList from shadcn/ui
 * - **Skeleton Component**: Loading placeholder component from the design system
 * - **Feature Flag Dashboard**: Parent context where this component is utilized for option selection
 * - **Admin Interface Components**: Related form components used throughout administrative interfaces
 *
 * ## Usage Patterns
 * The component supports multiple usage patterns depending on data loading requirements:
 * - **Static Options**: Pre-loaded option arrays for immediate display
 * - **Async Loading**: Dynamic option loading with loading state management
 * - **Search Integration**: Real-time search with server-side filtering capabilities
 * - **Form Integration**: Seamless integration with form libraries and validation systems
 *
 * @see https://cmdk.paco.me/ Command Menu for React (cmdk library documentation)
 * @see https://lucide.dev/icons Lucide React Icons
 * @see https://tailwindcss.com/docs/animation Tailwind CSS Animations
 * @see https://ui.shadcn.com/docs/components/command shadcn/ui Command Component
 * @see {@link ../../page.tsx} Feature Flag Dashboard Page
 * @see {@link ../../../../components/ui/command.tsx} Command UI Components
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Reusable autocomplete component for searching and selecting options with keyboard navigation, loading states, and accessibility support, specifically designed for the feature flag dashboard.
 * @example ```typescript
// Basic usage with static options
const entityOptions = [
  { value: "company-1", label: "Example Corp" },
  { value: "company-2", label: "Green Industries" }
];

<AutoComplete
  options={entityOptions}
  placeholder="Search entities..."
  emptyMessage="No entities found"
  onValueChange={(selected) => setSelectedEntity(selected)}
/>

// Usage with loading state
<AutoComplete
  options={isLoading ? [] : dynamicOptions}
  isLoading={isLoading}
  placeholder="Search companies..."
  emptyMessage="No companies found"
  value={selectedCompany}
  onValueChange={handleCompanySelection}
/>
```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Command as CommandPrimitive } from 'cmdk'
import { type KeyboardEvent, useCallback, useRef, useState } from 'react'

import { Skeleton } from '@/components/ui/skeleton'

import { Check } from 'lucide-react'
import { cn } from '@utils/lib/utils'

export type Option = Record<"value" | "label", string> & Record<string, string>

type AutoCompleteProps = {
    options: Option[]
    emptyMessage: string
    value?: Option
    onValueChange?: (value: Option) => void
    isLoading?: boolean
    disabled?: boolean
    placeholder?: string
}

export const AutoComplete = ({
                                 options,
                                 placeholder,
                                 emptyMessage,
                                 value,
                                 onValueChange,
                                 disabled,
                                 isLoading = false,
                             }: AutoCompleteProps) => {
    const inputRef = useRef<HTMLInputElement>(null)

    const [isOpen, setOpen] = useState(false)
    const [selected, setSelected] = useState<Option>(value as Option)
    const [inputValue, setInputValue] = useState<string>(value?.label || "")

    const handleKeyDown = useCallback(
        (event: KeyboardEvent<HTMLDivElement>) => {
            const input = inputRef.current
            if (!input) {
                return
            }

            // Keep the options displayed when the user is typing
            if (!isOpen) {
                setOpen(true)
            }

            // This is not a default behaviour of the <input /> field
            if (event.key === "Enter" && input.value !== "") {
                const optionToSelect = options.find(
                    (option) => option.label === input.value,
                )
                if (optionToSelect) {
                    setSelected(optionToSelect)
                    onValueChange?.(optionToSelect)
                }
            }

            if (event.key === "Escape") {
                input.blur()
            }
        },
        [isOpen, options, onValueChange],
    )

    const handleBlur = useCallback(() => {
        setOpen(false)
        setInputValue(selected?.label)
    }, [selected])

    const handleSelectOption = useCallback(
        (selectedOption: Option) => {
            setInputValue(selectedOption.label)

            setSelected(selectedOption)
            onValueChange?.(selectedOption)

            // This is a hack to prevent the input from being focused after the user selects an option
            // We can call this hack: "The next tick"
            setTimeout(() => {
                inputRef?.current?.blur()
            }, 0)
        },
        [onValueChange],
    )

    return (
        <CommandPrimitive onKeyDown={handleKeyDown}>
            <div>
                <CommandInput
                    ref={inputRef}
                    value={inputValue}
                    onValueChange={isLoading ? undefined : setInputValue}
                    onBlur={handleBlur}
                    onFocus={() => setOpen(true)}
                    placeholder={placeholder}
                    disabled={disabled}
                    className="text-base"
                />
            </div>
            <div className="relative mt-1">
                <div
                    className={cn(
                        "animate-in fade-in-0 zoom-in-95 absolute top-0 z-10 w-full standard-rounding bg-white dark:bg-zinc-900 outline-none",
                        isOpen ? "block" : "hidden",
                    )}
                >
                    <CommandList className="rounded-lg ring-1 ring-slate-200">
                        {isLoading ? (
                            <CommandPrimitive.Loading>
                                <div className="p-1">
                                    <Skeleton className="h-8 w-full"/>
                                </div>
                            </CommandPrimitive.Loading>
                        ) : null}
                        {options.length > 0 && !isLoading ? (
                            <CommandGroup>
                                {options.map((option) => {
                                    const isSelected = selected?.value === option.value
                                    return (
                                        <CommandItem
                                            key={option.value}
                                            value={option.label}
                                            onMouseDown={(event) => {
                                                event.preventDefault()
                                                event.stopPropagation()
                                            }}
                                            onSelect={() => handleSelectOption(option)}
                                            className={cn(
                                                "flex w-full items-center gap-2",
                                                !isSelected ? "pl-8" : null,
                                            )}
                                        >
                                            {isSelected ? <Check className="w-4"/> : null}
                                            {option.label}
                                        </CommandItem>
                                    )
                                })}
                            </CommandGroup>
                        ) : null}
                        {!isLoading ? (
                            <CommandPrimitive.Empty className="select-none rounded-sm px-2 py-3 text-center text-sm">
                                {emptyMessage}
                            </CommandPrimitive.Empty>
                        ) : null}
                    </CommandList>
                </div>
            </div>
        </CommandPrimitive>
    )
}
