/**
 * ESG Entity Analysis Report Dashboard Component
 *
 * This React component serves as the comprehensive ESG (Environmental, Social, Governance) analysis dashboard
 * for displaying entity-specific sustainability flags, impact assessments, and visual analytics within the
 * EkoIntelligence platform. It processes complex ESG data from the analytics backend and presents it through
 * an interactive, glass-morphism UI with donut charts, flag summaries, and detailed analysis sections.
 *
 * ## Core Functionality
 * - **ESG Flag Visualization**: Interactive display of positive (green) and negative (red) ESG flags with impact scores
 * - **Donut Chart Analytics**: Visual representation of ESG data distribution across ecological, social, and governance dimensions
 * - **Model-Based Segmentation**: Dynamic grouping of flags based on ethical model sections (SDG, ESG frameworks)
 * - **AI-Powered Summaries**: Real-time generation of section summaries using AI with automatic caching
 * - **Citation Management**: Comprehensive citation display and management for academic and regulatory compliance
 * - **Impact Assessment Modal**: Detailed impact analysis with measurement data and scoring methodology
 * - **Disclosure Filtering**: Toggle between disclosure-only and comprehensive flag views
 * - **Table of Contents**: Dynamic navigation for large reports with section linking
 *
 * ## Component Architecture
 * This file contains three main React components:
 * 1. **FlagColumn**: Renders grouped flag sections with donut charts and detailed flag cards
 * 2. **FlagDetail**: Tabbed interface organizing flags into positive/negative categories with summaries
 * 3. **EntityAnalysisReport**: Main orchestrating component managing data loading and navigation
 *
 * ## Key Dependencies
 * - **React 18+**: Modern React with hooks, context, and suspense for state management and UI optimization
 * - **Next.js 15 App Router**: Server-side routing, dynamic imports, and navigation management
 * - **Supabase**: Real-time database client for ESG flag data retrieval with RLS security
 * - **Lucide React**: Icon library providing ESG-themed icons (SproutIcon, SkullIcon, FlagIcon)
 * - **Tailwind CSS**: Utility-first CSS framework with glass-morphism design system implementation
 * - **nuqs**: URL state management for maintaining filter state across navigation
 * - **React Contexts**: Entity, Auth, and Navigation contexts for centralized state management
 *
 * ## Data Flow Architecture
 * 1. **Entity Context Integration**: Leverages EntityContext for centralized entity data, run information, and flag filtering
 * 2. **Flag Processing Pipeline**: Processes raw flag data through model parsing, disclosure filtering, and impact sorting
 * 3. **Model Section Mapping**: Maps flags to ethical model segments using xfer_model_sections data
 * 4. **Segment Generation**: Creates donut chart segments for ecological, social, and governance categories
 * 5. **Real-time Updates**: Responds to context changes and URL parameter updates automatically
 *
 * ## Database Integration
 * **xfer_flags Table Schema**:
 * - `id` (Integer, PK): Unique flag identifier for detailed flag navigation
 * - `entity_xid` (Text): Entity identifier linking flags to specific companies/organizations
 * - `run_id` (Integer): Analysis run identifier for temporal tracking and versioning
 * - `flag_type` (Text): Flag classification (red/green) for positive/negative impact categorization
 * - `model` (JSONB): Rich flag data including titles, analysis, impact scores, citations, and model sections
 * - `flag_summary` (Text): Optimized summary text field for performance and AI processing
 * - `flag_analysis` (Text): Detailed analytical text for comprehensive flag understanding
 * - `flag_statements` (JSONB): Source statements and evidence supporting flag conclusions
 *
 * **xfer_model_sections Table**: Defines the structure and metadata for ESG model frameworks
 *
 * ## System Architecture
 * This component integrates with the broader EkoIntelligence ESG analysis system:
 * - **Analytics Backend**: Python-based analysis engine processes corporate documents and generates flags
 * - **Data Synchronization**: xfer_ tables sync processed data from analytics to customer database
 * - **Flag Generation Pipeline**: AI-powered analysis identifies ESG issues and opportunities in corporate statements
 * - **Impact Scoring System**: Bayesian scoring methodology calculates environmental and social impact percentages
 * - **Customer Dashboard**: This component provides user-facing visualization of backend analysis results
 * - **Navigation System**: Integrated with platform navigation for entity browsing and flag exploration
 *
 * ## Performance Optimizations
 * - **React.useMemo**: Memoized flag filtering and processing to prevent unnecessary re-calculations
 * - **Suspense Integration**: Loading states with skeleton components for smooth user experience
 * - **Lazy Loading**: Dynamic imports and code splitting for optimal bundle size
 * - **Efficient Queries**: Supabase queries optimized with specific column selection and indexed lookups
 * - **State Management**: Centralized contexts prevent prop drilling and unnecessary re-renders
 * - **Image Optimization**: Next.js Image component with skeleton fallbacks for donut chart loading
 *
 * ## UI/UX Design System
 * **Glass-Morphism Implementation**:
 * - Translucent card backgrounds with backdrop blur effects for modern aesthetic
 * - Heavily rounded corners (1.5rem border radius) for approachable, friendly interface
 * - Gradient accents and color-coded sections for intuitive flag type recognition
 * - Hover animations and transitions providing interactive feedback and depth perception
 * - Responsive design supporting mobile, tablet, and desktop viewing experiences
 * - Accessibility features with proper ARIA labels and keyboard navigation support
 *
 * ## Advanced Features
 * **AI-Powered Content**:
 * - Real-time summary generation using ExpandableFlagsSummary component with AI models
 * - Contextual preambles for focused summary generation per ESG model section
 * - Caching mechanism prevents redundant AI API calls for performance optimization
 *
 * **Impact Assessment System**:
 * - Interactive impact badges with detailed modal overlays showing measurement methodology
 * - Color-coded impact indicators from red (negative) to green (positive) with percentage scores
 * - Integration with impact_value_analysis data for comprehensive impact evaluation
 *
 * **Citation and Evidence Management**:
 * - Compact citation components with expandable details for academic reference
 * - Citation reduction algorithms to prevent duplication and improve readability
 * - EkoMarkdown rendering with citation integration for rich text display
 *
 * ## Security & Access Control
 * - **Row Level Security**: Supabase RLS policies ensure users only access authorized entity data
 * - **Admin Features**: Conditional admin-only functionality for flag deletion and trace analysis
 * - **Input Validation**: Comprehensive parameter validation and sanitization for security
 * - **Error Boundaries**: Graceful error handling prevents application crashes from data issues
 *
 * ## Testing & Quality Assurance
 * - **Data-testid Attributes**: Comprehensive test identifiers for reliable Playwright testing
 * - **Error State Handling**: Robust error handling for network failures and data inconsistencies
 * - **Loading States**: Proper loading indicators and skeleton components for all async operations
 * - **Responsive Testing**: Cross-device compatibility testing for all viewport sizes
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing Next.js App Router Documentation
 * @see https://react.dev/reference/react/useEffect React useEffect Hook Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://tailwindcss.com/docs/backdrop-blur Tailwind CSS Backdrop Blur Documentation
 * @see {@link ../../components/graph/donut/donut.tsx} Donut Chart Component
 * @see {@link ../../components/summarize.tsx} AI Summarization Component
 * @see {@link ../../components/impact-modal.tsx} Impact Assessment Modal
 * @see {@link ../../utils/flag-converter.ts} Flag Data Processing Utilities
 * @see {@link ../../components/context/entity/entity-context.tsx} Entity Context Provider
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This React component serves as the comprehensive ESG analysis dashboard for displaying entity-specific sustainability flags, impact assessments, and visual analytics within the EkoIntelligence platform.
 * @example ```tsx
  // Usage in entity dashboard
  <EntityAnalysisReport />
  
  // Component automatically integrates with:
  // - EntityContext for data and filtering
  // - AuthContext for admin features
  // - NavContext for breadcrumb navigation
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'
import { FlagIcon, ListIcon, SkullIcon, SproutIcon } from 'lucide-react'
import { impactColor, impactFlagColorMap, impactText } from '@utils/lib/colors'
import { cn } from '@utils/lib/utils'
import { Segment, SegmentData } from '@/components/graph/donut/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AdminDeleteButton } from '@/components/admin'
import { AdminTraceButton } from '@/components/admin/AdminTraceButton'
import Donut from '@/components/graph/donut/donut'
import { Button } from '@/components/ui/button'
// Accordion removed as requested
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Summarize } from '@/components/summarize'
import { createClient } from '@/app/supabase/client'
import React, { Suspense, useEffect, useState } from 'react'
import { modelDesc } from './model-desc'
import ExpandableText from '@/components/text/expandable-text'
import { CitationType, CompactCitation, reduceCitations } from '@/components/citation'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { ScrollArea } from '@/components/ui/scroll-area'
import { createSegments, DonutSegmentData } from '@/components/issues'
import Image from 'next/image'
import { runAsync } from '@utils/react-utils'
import { FlagTypeV2 } from '@/types'
import { getFlagType, getModelSections, isDisclosureOnly, processFlags } from '@/utils/flag-converter'
import { useEntity } from '@/components/context/entity/entity-context'
import { useAuth } from '@/components/context/auth/auth-context'
import { useNav } from '@/components/context/nav/nav-context'
import { useNavigationWithParams } from '@/hooks/use-navigation-with-params'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { FlagsSkeleton } from '@/app/customer/dashboard/flags/flags-skeleton'
import { ExpandableFlagsSummary } from '@/components/expandable-flags-summary'
import { useQueryState } from 'nuqs'
import { ImpactModal } from '@/components/impact-modal'
import { truncate } from '@utils/text-utils'
import { FeatureFlag } from '@/components/feature-flag'


// Helper function to group flags by model section
function groupFlags(flags: FlagTypeV2[], model: string, ethicalModelSegments: Map<string, Segment>): Record<string, FlagTypeV2[]> {
  return flags.reduce((acc: any, flag) => {
    // Get the model section directly from the flag's model_sections
    // Use the current model to get the appropriate section
    const modelSections = getModelSections(flag)
    let section = modelSections[model]

    // If no section is found, use 'other' as a fallback
    if (!section) {
      section = 'other'
    }

    if (!acc[section]) {
      acc[section] = []
    }
    acc[section].push(flag)
    return acc
  }, {} as Record<string, FlagTypeV2[]>)
}

function FlagColumn({
                      type,
                      flags,
                      ethicalModelSegments,
                      ecoSegments,
                      socialSegments,
                      governanceSegments,
                      admin,
                      model,
                    }: {
  type: 'red' | 'green'
  flags: FlagTypeV2[],
  ethicalModelSegments: Map<string, Segment>,
  ecoSegments: SegmentData[],
  socialSegments: SegmentData[],
  governanceSegments: SegmentData[],
  admin: boolean,
  model: string
}) {
  const groupedFlags = groupFlags(flags, model, ethicalModelSegments)
  const router = useRouter()
  const [impactModalFlag, setImpactModalFlag] = useState<FlagTypeV2 | null>(null)
  const entityContext = useEntity()
  // console.log("Grouped Flags", groupedFlags)
  const flagsForSummary = flags && flags.length > 0 ? flags.map((item) => ({
    'id': item.id,
    'type': item.model.flag_type + '-flag',
    'issue': item.model.flag_title || item.issue || 'Untitled Flag',
    'reason': truncate(item.flag_summary || '', 1000),
  })) : []

  const { queryString } = useEntity()
  console.log('ECO SEGMENTS', ecoSegments)
  return (
    <div>
      <Card className="bg-transparent border-none">
        <div
          className={`absolute top-0 left-0 right-0 h-1 ${type === 'green' ? 'bg-brand-gradient' : 'bg-brand-gradient-compliment'}`}></div>
        {/*<CardHeader className="flex items-center justify-between">*/}
        {/*    <CardTitle className="heading-5 text-center w-full">{type === 'green' ? 'Positive' : 'Negative'} Actions Distribution</CardTitle>*/}
        {/*</CardHeader>*/}
        <CardContent className="text-center mx-auto mt-4 border-none">
          <Suspense
            fallback={<Image alt="donut skeleton" className="margin-auto width-full animate-pulse"
                             src={'/skeletons/donut.png'} width={328} height={328} />}>
            <Donut className="mx-auto" id={'doughnut-' + type}
                   breakpoints={{
                     0: 300,
                     400: 300,
                     600: 500,
                     768: 400,
                     1024: 500,
                     1280: 400,
                     1536: 500,
                   }}
                   ecoSegments={ecoSegments}
                   socialSegments={socialSegments}
                   governanceSegments={governanceSegments}
                   colorScale={type + '-flags' as any}
                   model={ethicalModelSegments}
            ></Donut>
          </Suspense>
        </CardContent>
      </Card>
      <div className="w-full mt-6 space-y-6">
        {Object.entries(groupedFlags).map(([segment, segmentFlags]: any[], index) => (
          <div key={index} className="space-y-4">
            <div id={`${type}-${segment}`} className="border-b border-border px-4 py-3 mb-2">
              <h2 className="text-lg font-semibold flex items-center">
                <FlagIcon className={cn(`w-5 h-5 mr-3 inline`)}
                          fill={'var(--' + impactFlagColorMap[segmentFlags[0].flag_type] + ')'}
                />
                <span>{ethicalModelSegments.get(segment)?.title || 'Other'} {segmentFlags.length > 0 ?
                  <span className="text-slate-400 text-sm ml-2">({segmentFlags.length})</span> : ''}</span>
              </h2>
            </div>

            {/* Section summary */}
            <div className="px-3 py-2 mb-4">
              <Summarize
                hashId={`${entityContext.hash()}-${type}-${segment}-summary-${segmentFlags.length}`}
                className="prose max-w-none"
                preamble={`Provide a concise summary of the ${type === 'green' ? 'positive' : 'negative'} actions related to ${ethicalModelSegments.get(segment)?.title || 'Other'}. Focus on the most significant points. Keep it to about 100 words.`}
                obj={segmentFlags.sort((a: FlagTypeV2, b: FlagTypeV2) => (b.model.impact || 0) - (a.model.impact || 0)).map((item: FlagTypeV2) => ({
                  'id': item.id,
                  'type': item.model.flag_type + '-flag',
                  'issue': item.model.flag_title || item.issue || 'Untitled Flag',
                  'reason': truncate(item.flag_summary || '', 1000),
                })).slice(0, 40)}
              />
            </div>

            <div className="px-3 py-2">
              {segmentFlags.map((flag: FlagTypeV2, flagIndex: number) => (
                <Card
                  key={flagIndex}
                  className={`mb-4 border border-${flag.flag_type === 'green' ? 'green-500/20' : 'red-500/20'} rounded-lg transition-standard hover:bg-${flag.flag_type === 'green' ? 'green-500/5' : 'red-500/5'} cursor-pointer relative group`}
                  data-testid="flag-item"
                  data-flag-type={getFlagType(flag)}
                  onClick={() => router.push(`/customer/dashboard/flags/${flag.id}?${queryString}`)}
                >
                  <AdminDeleteButton
                    tableName="xfer_gw_flags_v2"
                    recordId={flag.id}
                    recordType="flag"
                  />
                  <AdminTraceButton
                    flagId={flag.id}
                    flagTitle={flag.model.flag_title || 'Untitled Flag'}
                  />
                  <CardHeader>
                    <CardTitle
                      className="text-base flex items-center text-foreground justify-between dark:text-foreground dark:prose-a:text-muted-foreground">
                      <div className="dark:text-foreground prose">
                        <h3
                          className="dark:text-foreground prose">
                          <FlagIcon className={cn(`w-4 h-4 mr-2 inline`)}
                                    fill={'var(--' + impactFlagColorMap[segmentFlags[0].flag_type] + ')'}
                                    data-testid="flag-type-indicator"
                          />
                          <span
                            data-testid="flag-title">{flag.model.flag_short_title || flag.model.flag_title || 'Untitled Flag'}</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Badge
                                  variant="outline"
                                  className={cn(
                                    'absolute top-4 right-4 mx-1 px-2 py-1 float-right cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-lg border-2',
                                    'bg-gradient-to-r from-white/90 to-white/70 dark:from-slate-800/90 dark:to-slate-700/70 backdrop-blur-sm',
                                    'border-blue-400 text-blue-700 hover:border-blue-500 hover:bg-blue-50 dark:text-blue-300 dark:hover:bg-blue-950/20',
                                  )}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    setImpactModalFlag(flag)
                                  }}
                                  data-testid="impact-badge"
                                >
                                  {flag.model.flag_type == 'red' ?
                                    <SkullIcon
                                      className={cn('w-5 h-5 inline text-red-500 dark:text-red-500 mr-1', impactColor(flag.model.impact!))} />
                                    :
                                    <SproutIcon
                                      className={cn('w-5 h-5 inline text-green-500 dark:text-green-500 mr-1', impactColor(flag.model.impact!))} />
                                  }
                                  <span className="font-semibold text-sm">
                                    {impactText(flag.model.impact!)} Impact
                                  </span>
                                  <span className="text-xs opacity-80 ml-1">
                                    {flag.model.impact}%
                                  </span>
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="space-y-1">
                                  <p>Impact: {flag.model.impact}% - How great an impact does
                                    this action have on people, animals and the
                                    ecosystem?</p>
                                  <p className="text-xs opacity-80">Click to view detailed impact assessment</p>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {/*<TooltipProvider>*/}
                          {/*  <Tooltip>*/}
                          {/*    <TooltipTrigger asChild>*/}
                          {/*      <Badge variant="outline"*/}
                          {/*             className=" hidden sm:inline-block mx-1 px-0 float-right ">*/}
                        </h3>
                        <p>
                          {(() => {
                            const fullText = (flag.flag_summary || flag.flag_analysis || '')
                            const truncatedText = fullText && fullText.length > 200 ? fullText.substring(0, 200) + '...' : fullText
                            const needsMore = fullText && fullText.length > 200

                            return (
                              <>
                                {truncatedText}
                                {needsMore && (
                                  <Link
                                    className="ml-2 text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
                                    href={'/customer/dashboard/flags/' + flag.id + '?' + queryString}
                                  >
                                    more
                                  </Link>
                                )}
                              </>
                            )
                          })()}
                        </p>
                      </div>
                      {/*<Link href={"/admin/flag/" + flag.id}*/}
                      {/*      className="p-1 inline-block">*/}
                      {/*    <InfoCircledIcon/>*/}
                      {/*</Link>*/}
                      {/*<Button variant="ghost" size="icon" onClick={() => deleteFlag(flag.id!)}*/}
                      {/*        className="p-1 inline-block">*/}
                      {/*    <Trash2Icon size={16}/>*/}
                      {/*</Button>*/}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="bg-none">
                    {/* Show flag_analysis if available */}
                    {flag.flag_analysis && (
                      <div className="mb-4">
                        <EkoMarkdown
                          citations={flag.model.citations as CitationType[]}
                          admin={admin}>{flag.flag_analysis?.replaceAll(/\n/g, '\n\n')}</EkoMarkdown>
                      </div>
                    )}

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button className="hidden" variant="default">Detailed Analysis</Button>
                      </DialogTrigger>
                      <DialogContent className="sm:w-[800px] sm:max-w-[90vw]">
                        <ScrollArea className="max-h-[90dvh] ">
                          <div className="prose">
                            <EkoMarkdown
                              citations={flag.model.citations as CitationType[]}
                              admin={admin}>{flag.flag_analysis?.replaceAll(/\n/g, '\n\n')}</EkoMarkdown>
                          </div>
                        </ScrollArea>
                      </DialogContent>
                    </Dialog>
                    {reduceCitations(flag.model.citations as CitationType[])
                      .map((data, j) => (
                        <CompactCitation key={j} data={data} admin={admin} />
                      ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Impact Assessment Modal */}
      {impactModalFlag && (
        <FeatureFlag flag="dashboard.impact-assessment.modal">
          <ImpactModal
            isOpen={!!impactModalFlag}
            onClose={() => setImpactModalFlag(null)}
            measurement={impactModalFlag.model.impact_value_analysis?.impact_measurement}
            flagTitle={impactModalFlag.model.flag_title}
          />
        </FeatureFlag>
      )}
    </div>
  )
}

export function FlagDetail({
                             flags,
                             ethicalModelSegments,
                             socialSegmentsGreen,
                             socialSegmentsRed,
                             ecologicalSegmentsRed,
                             ecologicalSegmentsGreen,
                             governanceSegmentsGreen,
                             governanceSegmentsRed,
                             admin,
                             model,
                           }: {
  flags: FlagTypeV2[],
  ethicalModelSegments: Map<string, Segment>,
  socialSegmentsRed: SegmentData[],
  socialSegmentsGreen: SegmentData[],
  ecologicalSegmentsRed: SegmentData[],
  ecologicalSegmentsGreen: SegmentData[],
  governanceSegmentsRed: SegmentData[],
  governanceSegmentsGreen: SegmentData[],
  admin: boolean,
  model: string

}) {
  // Use disclosure filter toggle directly from entity context
  const entityContext = useEntity()

  // Filter flags based on type and disclosure setting
  const filterFlags = (flags: FlagTypeV2[], type: string) => {
    return flags
      .filter(flag => getFlagType(flag) === type)
      .filter(flag => entityContext.includeDisclosures || !isDisclosureOnly(flag))
  }

  const redFlags = filterFlags(flags, 'red').sort((a, b) => a.model.score - b.model.score)
  const greenFlags = filterFlags(flags, 'green').sort((a, b) => a.model.score - b.model.score)

  // Count disclosure-only flags
  const disclosureOnlyCount = flags.filter(flag => isDisclosureOnly(flag)).length
  const hasDisclosureFlags = disclosureOnlyCount > 0
  const [flagType, setFlagType] = useQueryState('flag-type', { defaultValue: 'red' }) as [string, (value: string) => void]

  // Ensure we have a valid tab value
  const tabValue = (flagType === 'all' ? 'red' : flagType) as string

  return (
    <div className="space-y-4">
      {/* Disclosure toggle is now in the EMR navigation */}

      <Card className="border rounded-lg overflow-hidden">
        <Tabs defaultValue={tabValue} className="w-full">
          <TabsList className="w-full mb-4 bg-slate-100/10 dark:bg-slate-800/20">
            <TabsTrigger value="green" className="w-1/2 data-[state=active]:bg-brand/10 data-[state=active]:text-brand"
                         onSelect={(value) => setFlagType('green')}>
              <SproutIcon className="w-4 h-4 mr-2" /> Positive Actions
            </TabsTrigger>
            <TabsTrigger value="red"
                         className="w-1/2 data-[state=active]:bg-red-500/10 data-[state=active]:text-red-500"
                         onSelect={(value) => setFlagType('red')}>
              <SkullIcon className="w-4 h-4 mr-2" /> Negative Actions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="green" className="mt-0">
            <div className="border-t border-green-500/20 overflow-hidden">
              <CardContent>
                {/* Overall summary for positive actions */}
                <div className="mb-6 p-4 border border-green-500/20 rounded-lg bg-green-500/5">
                  <h2 className="text-lg font-semibold mb-2 flex items-center">
                    <SproutIcon className="w-5 h-5 mr-2 text-green-500" /> Overall Summary
                  </h2>
                  <ExpandableFlagsSummary
                    flags={greenFlags}
                    type="green"
                    model={model}
                    className="prose max-w-none"
                    preamble="Provide a concise summary of all the positive actions (green flags) for this entity. Focus on the most significant actions and their impact. Keep it to about 150 words."
                    maxInitialFlags={40}
                  />
                </div>

                {/* Table of Contents */}
                <div className="mb-6 p-4 border rounded-lg">
                  <h2 className="text-lg font-semibold mb-2 flex items-center">
                    <ListIcon className="w-5 h-5 mr-2" /> Table of Contents
                  </h2>
                  <ul className="list-disc pl-5 space-y-1">
                    {Object.entries(groupFlags(greenFlags, model, ethicalModelSegments)).map(([segment, _], index) => (
                      <li key={index}>
                        <a href={`#green-${segment}`} className="text-brand hover:underline">
                          {ethicalModelSegments.get(segment)?.title || 'Other'}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>

                <FlagColumn socialSegments={socialSegmentsGreen} ecoSegments={ecologicalSegmentsGreen}
                            governanceSegments={governanceSegmentsGreen}
                            type="green" ethicalModelSegments={ethicalModelSegments}
                            admin={admin}
                            model={model}
                            flags={greenFlags} />
              </CardContent>
            </div>
          </TabsContent>

          <TabsContent value="red" className="mt-0">
            <div className="border-t border-red-500/20 overflow-hidden">
              <CardContent>
                {/* Overall summary for negative actions */}
                <div className="mb-6 p-4 border border-red-500/20 rounded-lg bg-red-500/5">
                  <h2 className="text-lg font-semibold mb-2 flex items-center">
                    <SkullIcon className="w-5 h-5 mr-2 text-red-500" /> Overall Summary
                  </h2>
                  <ExpandableFlagsSummary
                    flags={redFlags}
                    type="red"
                    model={model}
                    className="prose max-w-none"
                    preamble="Provide a concise summary of all the negative actions (red flags) for this entity. Focus on the most significant issues and their impact. Keep it to a maximum of 100 words NO MORE."
                    maxInitialFlags={40}
                  />
                </div>

                {/* Table of Contents */}
                <div className="mb-6 p-4 border rounded-lg">
                  <h2 className="text-lg font-semibold mb-2 flex items-center">
                    <ListIcon className="w-5 h-5 mr-2" /> Table of Contents
                  </h2>
                  <ul className="list-disc pl-5 space-y-1">
                    {Object.entries(groupFlags(redFlags, model, ethicalModelSegments)).map(([segment, _], index) => (
                      <li key={index}>
                        <a href={`#red-${segment}`} className="text-red-500 hover:underline">
                          {ethicalModelSegments.get(segment)?.title || 'Other'}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>

                <FlagColumn socialSegments={socialSegmentsRed} ecoSegments={ecologicalSegmentsRed}
                            governanceSegments={governanceSegmentsRed}
                            type="red" ethicalModelSegments={ethicalModelSegments}
                            admin={admin}
                            model={model}
                            flags={redFlags} />
              </CardContent>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  )
}


export function EntityAnalysisReport() {

  const supabase = createClient()
  const [segments, setSegments] = useState<DonutSegmentData | null>(null)
  const [flags, setFlags] = useState<FlagTypeV2[]>([])
  const entityContext = useEntity()
  const auth = useAuth()
  const nav = useNav()
  const navWithParams = useNavigationWithParams()
  const [flagType, setFlagType] = useQueryState('flag-type', { defaultValue: 'all' }) as [string, (value: string) => void]
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    setFlags([])
    setSegments(null)
    const { entity, model, runObject, flagsData, isLoadingFlags } = entityContext
    let abort = false

    if (entity) {
      runAsync(async () => {
        nav.changeNavPath(navWithParams.createNavItems([
          { label: 'Dashboard', href: '/customer/dashboard' },
          { label: 'Flags', href: '/customer/dashboard/flags' },
        ]))

        // Use flags from EntityContext instead of fetching them directly
        if (flagsData && !isLoadingFlags) {
          // Process flags to ensure models are properly parsed
          const processedFlags = processFlags(flagsData)
          setFlags(processedFlags)
          console.log('Using flags from EntityContext:', processedFlags.length)

          // Issues are no longer used, model sections are directly attached to each flag
          let { data: modelSections, error: errorForModelSections } = await supabase
            .from('xfer_model_sections')
            .select('*')
            .eq('model', model)

          if (abort) return

          // Create segments using the processed flags
          let segmentData = createSegments(model, modelSections!, processedFlags)
          if (abort) return
          setSegments(segmentData)
        } else {
          console.log('Waiting for flags to load from EntityContext...')
        }
      })
    }

    return () => {
      abort = true
    }
  }, [entityContext.entity, entityContext.model, entityContext.flagsData, entityContext.isLoadingFlags])

  // Filter flags based on flag type and search query
  const filteredFlags = React.useMemo(() => {
    if (!flags) return []

    let filtered = flags

    // Filter by flag type
    if (flagType !== 'all') {
      filtered = filtered.filter(flag => getFlagType(flag) === flagType)
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(flag =>
        (flag.model.flag_title || '').toLowerCase().includes(query) ||
        (flag.flag_summary || '').toLowerCase().includes(query) ||
        (flag.flag_analysis || '').toLowerCase().includes(query),
      )
    }

    return filtered
  }, [flags, flagType, searchQuery])

  return <div data-testid="flags-page-content">
    {entityContext.model &&
      (<div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4 dashboard-container">
        <Card className="glass-effect-lit rounded-3xl overflow-hidden hidden lg:block col-span-4 row-span-2">
          <div className="absolute top-0 left-0 right-0 h-1 bg-brand-gradient"></div>
          <CardContent className="text-left mx-auto">
            <ExpandableText image={modelDesc[entityContext.model!].image}
                            title={modelDesc[entityContext.model!].title}
                            text={modelDesc[entityContext.model!].text} expanded={false} />
          </CardContent>
        </Card>
      </div>)
    }


    {!flags && (
      <div
        className="glass-effect-lit rounded-3xl p-6 shadow-medium">
        <p className="text-slate-600 dark:text-slate-300">There is no relevant information on this entity.</p>
      </div>
    )}

    <div className="dashboard-container">
      {/*  /!* Filter Controls *!/*/}
      {/*  <div className="flex flex-col sm:flex-row gap-4 mb-6 p-4 bg-muted/30 rounded-lg">*/}
      {/*    <div className="flex-1">*/}
      {/*      <Label htmlFor="search" className="text-sm font-medium">Search Flags</Label>*/}
      {/*      <div className="relative mt-1">*/}
      {/*        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />*/}
      {/*        <Input*/}
      {/*          id="search"*/}
      {/*          placeholder="Search by title, summary, or analysis..."*/}
      {/*          value={searchQuery}*/}
      {/*          onChange={(e) => setSearchQuery(e.target.value)}*/}
      {/*          className="pl-10"*/}
      {/*          data-testid="flag-search-input"*/}
      {/*        />*/}
      {/*      </div>*/}
      {/*    </div>*/}

      {/*    <div>*/}
      {/*      <Label htmlFor="flag-type" className="text-sm font-medium">Flag Type</Label>*/}
      {/*      <Select value={flagType} onValueChange={setFlagType}>*/}
      {/*        <SelectTrigger className="w-[180px] mt-1" data-testid="flag-type-filter">*/}
      {/*          <SelectValue placeholder="Select flag type" />*/}
      {/*        </SelectTrigger>*/}
      {/*        <SelectContent>*/}
      {/*          <SelectItem value="all" data-testid="filter-option-all">All Flags</SelectItem>*/}
      {/*          <SelectItem value="red" data-testid="filter-option-red">Red Flags</SelectItem>*/}
      {/*          <SelectItem value="green" data-testid="filter-option-green">Green Flags</SelectItem>*/}
      {/*        </SelectContent>*/}
      {/*      </Select>*/}
      {/*    </div>*/}

      {/*    <div className="flex items-center space-x-2 mt-6">*/}
      {/*      <Switch*/}
      {/*        id="disclosure-toggle"*/}
      {/*        checked={entityContext.includeDisclosures}*/}
      {/*        onCheckedChange={entityContext.toggleDisclosures}*/}
      {/*        data-testid="disclosure-filter"*/}
      {/*      />*/}
      {/*      <Label htmlFor="disclosure-toggle" className="text-sm font-medium">*/}
      {/*        Include Disclosures*/}
      {/*      </Label>*/}
      {/*    </div>*/}
      {/*  </div>*/}

      <div className="mt-4">
        <div data-testid="flags-list">
          {/* Show loading state if flags are still loading from EntityContext */}
          {entityContext.isLoadingFlags ? (
            <FlagsSkeleton />
          ) : filteredFlags?.length > 0 && segments ? (
            <FlagDetail ethicalModelSegments={segments.ethicalModelSegments}
                        socialSegmentsRed={segments.socialSegmentsRed}
                        socialSegmentsGreen={segments.socialSegmentsGreen}
                        ecologicalSegmentsRed={segments.ecoSegmentsRed}
                        governanceSegmentsRed={segments.governanceSegmentsRed}
                        governanceSegmentsGreen={segments.governanceSegmentsGreen}
                        ecologicalSegmentsGreen={segments.ecoSegmentsGreen}
                        flags={filteredFlags}
                        model={entityContext.model!}
                        admin={auth.admin} />
          ) : (
            <div className="text-center py-8" data-testid="no-flags-message">
              <p className="text-muted-foreground">
                {filteredFlags?.length === 0 && flags?.length > 0
                  ? 'No flags match your current filters.'
                  : 'No flags found for this entity.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>


  </div>
}
