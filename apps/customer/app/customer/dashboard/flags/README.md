# ESG Entity Analysis Flags Dashboard

## Overview

The ESG Flags Dashboard is a comprehensive interactive visualization system within the EkoIntelligence customer application that provides detailed analysis of Environmental, Social, and Governance (ESG) impact flags for entities such as companies, funds, and organizations. This module serves as the primary interface for customers to explore AI-powered ESG analysis results, including positive and negative sustainability flags, impact assessments, and comprehensive analytical insights derived from corporate statements and regulatory filings.

The dashboard transforms complex ESG data from the analytics backend into intuitive visual representations including interactive donut charts, detailed flag cards, and comprehensive analytical reports. It integrates seamlessly with the broader EkoIntelligence platform to provide customers with actionable insights into corporate sustainability performance and ESG risk assessment.

## Specification

### Core Functional Requirements

The ESG Flags Dashboard must provide:

1. **Comprehensive Flag Visualization**: Display ESG effect flags with detailed impact scores, confidence levels, and analytical summaries
2. **Interactive Donut Chart Analytics**: Visual representation of ESG data distribution across ecological, social, and governance dimensions
3. **Model-Based Classification**: Dynamic grouping of flags based on multiple ESG frameworks (UN SDG, Doughnut Model, Plant-Based Treaty, ekoIntelligence)
4. **AI-Powered Content Generation**: Real-time summary generation with caching for section-level analysis
5. **Citation Management**: Comprehensive citation display and academic reference handling
6. **Impact Assessment System**: Detailed impact analysis with Bayesian scoring methodology
7. **Responsive Design**: Cross-device compatibility with glass-morphism design system
8. **Advanced Filtering**: Toggle between disclosure-only and comprehensive flag views
9. **Modal Detail Views**: Intercepting routes for seamless flag detail exploration
10. **Administrative Features**: Conditional admin controls for flag management and system debugging

### Technical Architecture Requirements

```mermaid
graph TB
    A[Customer Dashboard] --> B[Flags Page Route]
    B --> C[EntityAnalysisReport Component]
    C --> D[EntityContext Provider]
    C --> E[FlagDetail Component]
    E --> F[FlagColumn Components]
    F --> G[Donut Chart Visualization]
    F --> H[Flag Cards with Citations]
    
    I[Database Layer] --> J[xfer_flags Table]
    I --> K[xfer_model_sections Table]
    J --> L[Flag Processing Pipeline]
    K --> M[Model Section Mapping]
    
    N[Modal System] --> O[@modal Parallel Route]
    O --> P[Flag Detail Modal]
    P --> Q[FlagExpandedDetail Component]
    
    R[Analytics Backend] --> S[ESG Flag Generation]
    S --> T[DEMISE Model Processing]
    T --> U[Data Synchronization]
    U --> J
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant D as Dashboard
    participant EC as EntityContext
    participant S as Supabase
    participant AC as AnalyticsCore
    participant AI as AI Services

    U->>D: Navigate to /flags
    D->>EC: Request entity flags
    EC->>S: Query xfer_flags table
    S-->>EC: Return flag data
    EC->>D: Process flags with utilities
    D->>AI: Generate section summaries
    AI-->>D: Return cached/new summaries
    D-->>U: Render interactive dashboard
    
    U->>D: Click flag for details
    D->>D: Intercept route to modal
    D->>S: Fetch detailed flag data
    S-->>D: Return comprehensive flag info
    D-->>U: Display modal with full analysis
```

## Key Components

### Core Route Files

| File                                    | Purpose                           | Responsibilities                                                                |
|----------------------------------------|-----------------------------------|---------------------------------------------------------------------------------|
| `page.tsx`                             | Main flags dashboard route        | Orchestrates ESG flag visualization with EntityAnalysisReport and BackToTop    |
| `entity-analysis-report.tsx`          | Primary dashboard component       | Comprehensive ESG analysis with donut charts, flag details, and AI summaries   |
| `flag-expanded-detail.tsx`            | Detailed flag analysis component  | Full flag display with impact assessment, citations, and administrative controls |
| `flags-skeleton.tsx`                  | Loading state component           | Responsive skeleton matching dashboard layout for smooth UX transitions        |
| `autocomplete.tsx`                    | Search/selection component        | Keyboard-navigable dropdown for entity and filter selection                     |
| `model-desc.tsx`                      | ESG model definitions module      | Centralized content for UN SDG, Doughnut Model, Plant-Based Treaty, and eko   |

### Modal System Components

| File                                    | Purpose                           | Responsibilities                                                                |
|----------------------------------------|-----------------------------------|---------------------------------------------------------------------------------|
| `@modal/default.tsx`                  | Parallel route fallback           | Returns null for inactive modal state, ensures clean UI transitions            |
| `@modal/(.)flags/[id]/page.tsx`      | Intercepting route modal          | Modal overlay for flag details preserving dashboard context                     |

### Data Processing Components

- **Flag Converter Utilities**: Type-safe processing of complex JSONB flag model data
- **Model Section Loader**: Async loading of ESG framework metadata and classifications
- **Citation Processors**: Sorting, reduction, and display of academic citations
- **Impact Assessment Calculator**: Bayesian scoring methodology for environmental and social impact

### UI System Components

- **Glass-morphism Design System**: Translucent cards with backdrop blur and rounded corners
- **Donut Chart Visualization**: Interactive D3.js-based charts with responsive breakpoints
- **Expandable Text Components**: Collapsible content sections with smooth animations
- **Toast Notification System**: User feedback for success and error states

## Dependencies

### Framework Dependencies

- **Next.js 15 App Router**: Modern React framework with parallel routes, intercepting routes, and server components
- **React 18+**: Latest React features including Suspense, concurrent rendering, and modern hooks
- **TypeScript 5+**: Full compile-time type safety with strict configuration

### Database & Authentication

- **Supabase Client**: Real-time PostgreSQL database with Row Level Security (RLS) policies
- **Supabase Auth**: Integrated authentication with automatic token management and admin role detection
- **Database Tables**:
  - `xfer_flags`: ESG flag data synchronized from analytics backend
  - `xfer_model_sections`: ESG framework metadata and section definitions
  - `xfer_issues`: Legacy issue tracking integration (deprecated)

### External Service Dependencies

- **AI Content Generation**: Google Gemini LLM integration for real-time summary generation
- **Citation Management**: Academic reference processing and display system
- **Impact Assessment**: Bayesian scoring engine for environmental and social impact calculation

### Internal Module Dependencies

```mermaid
graph TD
    A[Flags Dashboard] --> B[EntityContext Provider]
    A --> C[AuthContext Provider] 
    A --> D[NavContext Provider]
    A --> E[Flag Converter Utils]
    A --> F[Model Section Loader]
    A --> G[Citation Components]
    A --> H[Glass-morphism UI]
    
    B --> I[Entity Data Management]
    B --> J[Filter State Management]
    C --> K[User Authentication]
    C --> L[Admin Permissions]
    D --> M[Breadcrumb Navigation]
    D --> N[URL Parameter Handling]
    
    E --> O[Type Safety Utilities]
    F --> P[ESG Framework Data]
    G --> Q[Academic References]
    H --> R[Design System Components]
```

### External Dependencies

- **Lucide React**: SVG icon library with ESG-themed icons (SproutIcon, SkullIcon, FlagIcon)
- **Tailwind CSS**: Utility-first CSS framework with custom glass-morphism classes
- **nuqs**: URL state management for filter persistence across navigation
- **cmdk**: Command palette library for autocomplete functionality

## Usage Examples

### Basic Dashboard Navigation

```typescript
// Navigate to flags dashboard
// URL: /customer/dashboard/flags
// Automatically loads entity context and displays interactive dashboard

// Filter flags by type
// URL: /customer/dashboard/flags?flag-type=red
// Shows only negative (red) flags

// Include disclosure flags
// URL: /customer/dashboard/flags?includeDisclosures=true
// Displays disclosure-only flags alongside regular analysis
```

### Modal Detail Views

```typescript
// Open flag detail modal (client-side navigation)
<Link href="/customer/dashboard/flags/123">
  View Flag Details
</Link>

// Direct URL access (full page view)
// URL: /customer/dashboard/flags/123?entity=AAPL&run=latest
```

### Integration with EntityContext

```typescript
// Dashboard automatically integrates with entity selection
const { entity, model, runObject, flagsData, isLoadingFlags } = useEntity();

// Flags are automatically filtered based on context
// - entity: Selected company/organization
// - model: ESG framework (sdg, doughnut, eko, plant_based_treaty)
// - run: Analysis run (latest, specific run ID)
// - includeDisclosures: Show/hide disclosure-only flags
```

### AI-Powered Summary Generation

```typescript
// Section summaries automatically generated and cached
<ExpandableFlagsSummary
  flags={flagsData}
  type="green"
  model={model}
  preamble="Provide concise summary of positive actions..."
  className="prose max-w-none"
/>

// Custom preambles for focused analysis
// - Ecological: "Focus on environmental impact and sustainability"
// - Social: "Emphasize social justice and human rights aspects"  
// - Governance: "Highlight corporate governance and transparency"
```

## Architecture Notes

### Parallel Routes System

The dashboard implements Next.js 15 parallel routes for simultaneous rendering:

```typescript
// Dashboard layout structure
export default function Layout({
  children,  // Main dashboard content
  modal,     // Modal overlay slot (@modal)
}: {
  children: React.ReactNode
  modal: React.ReactNode
}) {
  return (
    <>
      <div className="dashboard-container">
        {children}
      </div>
      {modal}
    </>
  )
}
```

### ESG Data Processing Pipeline

```mermaid
flowchart TD
    A[Corporate Documents] --> B[Analytics Backend]
    B --> C[AI Statement Analysis]
    C --> D[DEMISE Model Processing]
    D --> E[ESG Flag Generation]
    E --> F[Impact Score Calculation]
    F --> G[xfer_flags Table]
    
    H[ESG Frameworks] --> I[Model Section Mapping]
    I --> J[xfer_model_sections Table]
    
    G --> K[Flag Processing Utils]
    J --> K
    K --> L[Dashboard Visualization]
    L --> M[Interactive Donut Charts]
    L --> N[Detailed Flag Cards]
    L --> O[AI-Generated Summaries]
```

### Real-time Data Synchronization

```mermaid
stateDiagram-v2
    [*] --> Loading: Component mounts
    Loading --> DataFetch: EntityContext provides data
    DataFetch --> Processing: Flags received from context
    Processing --> Rendering: Flag processing complete
    Rendering --> Interactive: Dashboard displayed
    Interactive --> Filtering: User applies filters
    Filtering --> Processing: Re-process filtered data
    Interactive --> ModalView: User opens flag detail
    ModalView --> Interactive: Modal dismissed
    Interactive --> Refreshing: Context data updates
    Refreshing --> Processing: New data available
```

### Glass-morphism Design Implementation

The flags dashboard implements consistent glass-morphism styling:

- **Translucent Backgrounds**: `bg-white/90 dark:bg-slate-800/90` with backdrop blur
- **Rounded Corners**: Generous border radius (`rounded-3xl`, `rounded-lg`) for modern feel
- **Color-coded Sections**: Green for positive flags, red for negative flags
- **Hover Animations**: Smooth transitions with `hover:scale-105` and shadow effects
- **Responsive Breakpoints**: Mobile-first design with `lg:` and `xl:` breakpoints

## Known Issues

### Active Issues

#### Flag Loading Performance (MONITORING)

- **Status**: Under monitoring
- **Issue**: Large flag datasets (500+ flags) can cause slow initial render
- **Mitigation**: Implemented skeleton loading states and virtual scrolling consideration
- **Tracking**: Performance metrics added for render timing analysis

#### Modal Z-Index Conflicts (RESOLVED)

- **Status**: Fixed in recent commits
- **Issue**: Navigation sidebar blocked by modal overlays
- **Resolution**: Corrected z-index layering with proper portal rendering
- **Testing**: Comprehensive modal interaction tests added

### Technical Debt

1. **Type Casting Requirements**: Some Supabase JSONB fields require `as unknown` casting for complex nested data
2. **Legacy Issue Integration**: Deprecated issues system still referenced in some components
3. **Citation Deduplication**: Citation reduction algorithm could be optimized for better performance
4. **Loading State Inconsistency**: Some components lack proper skeleton states during async operations

### Browser Compatibility

- **Modern Browsers**: Full support for Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Glass-morphism Support**: Backdrop filter support required for visual effects
- **Mobile Optimization**: Touch-friendly interactions with responsive breakpoints
- **Accessibility**: WCAG 2.1 AA compliance with screen reader testing

## Future Work

### Planned Enhancements

#### EKO-320: Advanced Flag Analytics (PLANNED)

- **Goal**: Implement trend analysis and historical flag comparison
- **Timeline**: Q2 2025
- **Features**: Year-over-year flag evolution, correlation analysis, predictive insights
- **Dependencies**: Enhanced analytics backend processing

#### EKO-335: Multi-Entity Comparison (PLANNED)

- **Goal**: Side-by-side ESG flag comparison across multiple entities
- **Timeline**: Q2 2025
- **Benefits**: Peer benchmarking, industry analysis, portfolio assessment
- **UI Requirements**: Enhanced dashboard layout with comparison views

#### EKO-340: Enhanced Export Capabilities (PLANNED)

- **Goal**: PDF and Excel export of ESG flag analysis with customizable templates
- **Timeline**: Q3 2025
- **Features**: Executive summaries, detailed technical reports, regulatory compliance formats

### Technical Improvements

1. **Performance Optimization**: Virtual scrolling for large flag datasets
2. **Enhanced Caching**: Redis-based caching for AI-generated summaries
3. **Real-time Updates**: WebSocket integration for live flag updates
4. **Advanced Filtering**: Multi-dimensional filtering with saved filter presets
5. **Accessibility Enhancements**: Enhanced keyboard navigation and screen reader support

### Requirements Tracking

All future work is tied to Linear projects and customer feedback:

- **ESG Dashboard Enhancement Project** (Active)
- **Enterprise Feature Development** (Planned Q2 2025)
- **Performance Optimization Initiative** (Ongoing)
- **Accessibility Compliance Project** (Q1 2025)

## Troubleshooting

### Common Issues

#### Flags Not Loading

```bash
# Debug steps:
1. Check EntityContext state in React DevTools
2. Verify Supabase connection and authentication
3. Confirm entity selection in dashboard navigation
4. Check browser network tab for failed requests
5. Verify RLS policies allow flag data access

# Common causes:
- Entity not selected in dashboard context
- Authentication token expired
- RLS policy blocking data access
- Network connectivity issues
```

#### Donut Charts Not Rendering

```bash
# Troubleshooting:
1. Check browser console for JavaScript errors
2. Verify flag data contains model_sections mappings
3. Confirm D3.js dependencies loaded correctly
4. Check responsive breakpoint CSS classes

# Solutions:
- Clear browser cache and reload
- Verify flag processing utilities working correctly
- Check model section data availability in database
```

#### Modal Not Opening

```bash
# Common solutions:
1. Verify intercepting route file exists at correct path
2. Check parallel route slot rendered in dashboard layout
3. Confirm flag ID exists in database
4. Check authentication and admin permissions

# Debug commands:
- Check browser console for routing errors
- Verify flag data exists: SELECT * FROM xfer_flags WHERE id = ?
- Test direct URL access to confirm routing
```

#### AI Summary Generation Failing

```bash
# Debugging steps:
1. Check AI service API keys in environment
2. Verify network connectivity to AI services
3. Check rate limiting and quota usage
4. Review error logs for specific failure reasons

# Common fixes:
- Verify GOOGLE_GENERATIVE_AI_API_KEY environment variable
- Check AI service status and availability
- Review summary request payload for errors
```

### Performance Debugging

```typescript
// Add performance monitoring to flag components
useEffect(() => {
  const startTime = performance.now();
  
  return () => {
    const endTime = performance.now();
    console.log(`Flag render time: ${endTime - startTime}ms`);
  };
}, [flags]);

// Monitor flag processing performance
const processedFlags = useMemo(() => {
  const start = performance.now();
  const result = processFlags(flagsData);
  console.log(`Flag processing: ${performance.now() - start}ms`);
  return result;
}, [flagsData]);
```

### Database Query Optimization

```sql
-- Optimize flag queries with proper indexing
CREATE INDEX IF NOT EXISTS idx_xfer_flags_entity_run 
ON xfer_flags(entity_xid, run_id);

CREATE INDEX IF NOT EXISTS idx_xfer_flags_type 
ON xfer_flags(flag_type);

-- Verify query performance
EXPLAIN ANALYZE SELECT * FROM xfer_flags 
WHERE entity_xid = 'AAPL' AND run_id = 123;
```

## FAQ

### User-Centric Questions

**Q: What do the different flag colors mean?**
A: Green flags represent positive ESG actions (environmental benefits, social improvements, governance excellence), while red flags indicate negative impacts or concerns (environmental harm, social issues, governance problems).

**Q: How are impact scores calculated?**
A: Impact scores use Bayesian methodology combining multiple factors: statement importance, evidence quality, scope of effect, and expert validation. Scores range from 0-100% representing the magnitude of positive or negative impact.

**Q: Why do some flags show "Disclosure Only"?**
A: Disclosure flags represent statements that merely report information without demonstrating actual impact. Toggle "Include Disclosures" to view/hide these for focused analysis on actionable ESG performance.

**Q: Can I export flag analysis reports?**
A: Currently, flags can be viewed and shared via URLs. PDF/Excel export functionality is planned for Q3 2025 with customizable report templates.

**Q: How current is the ESG flag data?**
A: Flag data is synchronized from our analytics backend, typically updated within 24-48 hours of new corporate filings or statements being processed.

### Developer Questions  

**Q: How do I add support for a new ESG framework?**
A: 1) Add framework definition to `model-desc.tsx`, 2) Create model sections in `xfer_model_sections` table, 3) Update flag processing utilities to handle new model mappings, 4) Test with sample flag data.

**Q: Why use intercepting routes for modals?**
A: Intercepting routes provide URL-based modal state, better SEO, improved accessibility, and seamless integration with Next.js App Router features while preserving dashboard context.

**Q: How do I optimize flag loading performance?**
A: Use EntityContext for centralized data management, implement proper loading states, consider virtual scrolling for large datasets, and optimize database queries with appropriate indexing.

**Q: How do I customize the glass-morphism styling?**
A: Modify Tailwind CSS classes in components, update CSS custom properties for glass effects, ensure consistent backdrop-blur usage, and test across different browsers for compatibility.

**Q: How do I add new administrative features?**
A: Check admin permissions via `useAuth().admin`, implement conditional rendering, add proper authorization checks in API routes, and ensure admin actions are properly logged.

## References

### Documentation

- [Next.js App Router Documentation](https://nextjs.org/docs/app) - Modern React framework features
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript) - Database and authentication
- [React 18 Documentation](https://react.dev/blog/2022/03/29/react-v18) - Concurrent features and hooks
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - Utility-first CSS framework

### Code Files

- [`/apps/customer/app/customer/dashboard/layout.tsx`](../layout.tsx) - Dashboard layout with parallel routes
- [`/apps/customer/components/context/entity/entity-context.tsx`](../../../../components/context/entity/entity-context.tsx) - Entity context provider
- [`/apps/customer/utils/flag-converter.ts`](../../../../utils/flag-converter.ts) - Flag data processing utilities
- [`/apps/customer/components/graph/donut/donut.tsx`](../../../../components/graph/donut/donut.tsx) - Interactive donut chart component

### External Dependencies

- [Lucide React Icons](https://lucide.dev/icons) - SVG icon library
- [cmdk Command Palette](https://cmdk.paco.me/) - Search and autocomplete functionality
- [nuqs URL State](https://nuqs.47ng.com/) - URL parameter state management
- [D3.js Documentation](https://d3js.org/) - Data visualization library

### ESG Framework References

- [UN Sustainable Development Goals](https://sdgs.un.org/goals) - Official SDG framework
- [Doughnut Economics](https://doughnuteconomics.org/) - Kate Raworth's economic framework
- [Plant Based Treaty](https://plantbasedtreaty.org/) - Environmental initiative
- [SASB Standards](https://sasb.org/) - Sustainability accounting standards
- [GRI Standards](https://www.globalreporting.org/) - Global reporting initiative

### Testing Resources

- [`/apps/customer/tests/`](../../../tests/) - Playwright test suite
- [Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/) - Component testing
- [Playwright Documentation](https://playwright.dev/) - End-to-end testing

### Third-Party Information

- [Web Accessibility Guidelines (WCAG)](https://www.w3.org/WAI/WCAG21/quickref/) - Accessibility standards
- [Glass-morphism Design Principles](https://uxdesign.cc/glassmorphism-in-user-interfaces-1f39bb1308c9) - UI design trends
- [ESG Reporting Standards](https://www.ifrs.org/content/dam/ifrs/publications/pdf-files/english/2023/issued/part-a/issb-2023-a-s2-climate-related-disclosures.pdf) - Climate-related disclosures

---

## Changelog

### 2025-07-30

- Initial comprehensive documentation created for ESG Flags Dashboard
- Added system architecture diagrams and data flow specifications
- Documented all core components and their responsibilities
- Included troubleshooting guides and FAQ sections
- Added development context and integration patterns
- Provided usage examples and performance optimization guidance
- Created comprehensive reference section with external resources

---

(c) All rights reserved ekoIntelligence 2025