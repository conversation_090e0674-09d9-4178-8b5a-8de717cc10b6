/**
 * Next.js App Router Intercepting Route: ESG Flag Detail Modal Component
 *
 * This intercepting route component provides modal overlay functionality for displaying detailed ESG
 * flag analysis within the customer dashboard. It leverages Next.js 15 App Router's intercepting routes
 * convention to show flag details as a modal when accessed via client-side navigation, while preserving
 * direct URL access for deep-linking and sharing capabilities.
 *
 * ## Core Functionality
 * - **Modal Intercept Pattern**: Uses `(.)flags/[id]` pattern to intercept `/flags/[id]` routes at same level
 * - **ESG Flag Analysis Display**: Shows comprehensive flag data including impact scores, AI analysis, and citations
 * - **Model Section Integration**: Loads and displays model classifications (SASB, GRI, UN SDG) associated with flags
 * - **Authentication Integration**: Respects user authentication state and admin privileges for data access
 * - **Database Integration**: Fetches flag data from `xfer_flags` table with comprehensive model parsing
 *
 * ## Route Pattern & Navigation
 * - **Intercepting Route**: `/@modal/(.)flags/[id]/page.tsx` intercepts `/flags/[id]` when navigated from dashboard
 * - **Modal Display**: Opens as overlay preserving underlying dashboard context during client-side navigation
 * - **Direct Access**: Falls back to regular flag detail page when accessed directly via URL or page refresh
 * - **Dynamic Parameters**: 
 *   - `id` (required): Numeric flag identifier for database lookup
 *   - `entity` (optional): Entity identifier for context filtering
 *   - `run` (optional): Analysis run identifier for version control
 *   - `model` (optional): Model type for filtering display
 * - **Close Behavior**: Modal dismissal navigates back in browser history via `router.back()`
 *
 * ## Data Processing Pipeline
 * 1. **Route Parameter Extraction**: Uses React `use()` hook for async route params in Next.js 15 App Router
 * 2. **Database Query**: Queries `xfer_flags` table by numeric flag ID via Supabase client with RLS security
 * 3. **Model Data Parsing**: Applies `ensureModelParsed()` utility to convert JSONB model field to typed structure
 * 4. **Legacy Issue Compatibility**: Maintains backward compatibility with deprecated issues system
 * 5. **State Management**: Manages loading states and error handling with React hooks
 * 6. **Modal Rendering**: Displays processed flag data in `FlagExpandedDetail` component within glass-morphism modal
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with intercepting routes, dynamic parameters, and async params
 * - **Supabase Client**: Database client for ESG flag data access with Row Level Security (RLS) policies
 * - **React 18**: Component framework with concurrent features, hooks, and client-side state management
 * - **Authentication Context**: User session management and admin privilege checking for data access control
 * - **Glass-morphism UI**: `SimpleModal` component with translucent design system and backdrop blur effects
 *
 * ## Database Schema Integration
 * - **Source Table**: `xfer_flags` with columns: `id`, `entity_xid`, `run_id`, `flag_type`, `model` (JSONB), etc.
 * - **JSONB Model Field**: Contains comprehensive ESG analysis including:
 *   - Impact scores, confidence levels, credibility assessments
 *   - AI-generated flag analysis text and summaries
 *   - Citation arrays with source document references
 *   - Model section mappings for SASB, GRI, UN SDG classifications
 *   - DEMISE model embeddings for semantic analysis
 * - **Type Safety**: `FlagTypeV2` interface ensures compile-time type safety for complex nested data
 * - **Authentication**: RLS policies enforce multi-tenant data isolation and access control
 *
 * ## System Architecture
 * This intercepting route fits into the broader ESG analysis system:
 * - **Analytics Backend**: Python system processes corporate statements and generates ESG flags with DEMISE embeddings
 * - **Data Sync Layer**: `xfer_flags` table synchronizes processed flag data from analytics to customer database
 * - **Modal Layer**: This component provides seamless modal access to detailed flag analysis
 * - **Dashboard Integration**: Preserves dashboard context while displaying detailed flag information
 * - **Model Classification**: Integrates with multiple ESG frameworks (SASB, GRI, UN SDG) for comprehensive analysis
 *
 * ## Performance & Error Handling
 * - **Optimized Queries**: Single database query with comprehensive data loading to minimize round trips
 * - **Loading States**: Proper loading state management prevents UI flashing and provides user feedback
 * - **Error Recovery**: Comprehensive error handling with user-friendly toast notifications for database failures
 * - **Type Conversion**: Safe type conversion with fallback handling for malformed model data
 * - **Navigation Safety**: Proper cleanup and navigation handling prevents memory leaks and navigation issues
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/intercepting-routes Next.js Intercepting Routes
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://react.dev/reference/react/use React use Hook Documentation
 * @see {@link ../../../flags/flag-expanded-detail.tsx} FlagExpandedDetail Component
 * @see {@link /components/simple-modal.tsx} SimpleModal Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Intercepting route modal component for displaying detailed ESG flag analysis with comprehensive impact assessment, AI-generated content, and model classifications
 * @example
 * ```typescript
 * // Accessed via dashboard navigation (modal)
 * /customer/dashboard/flags/123
 * 
 * // Direct URL access (full page)
 * /flags/123?entity=AAPL&run=latest&model=ekoIntelligence
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";
import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { createIssueMap } from '@/components/issues'
import { useAuth } from '@/components/context/auth/auth-context'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'
import { FlagExpandedDetail } from '@/app/customer/dashboard/flags/flag-expanded-detail'
import { SimpleModal } from '@/components/simple-modal'
import { FlagTypeV2 } from '@/types'
import { ensureModelParsed } from '@/utils/flag-converter'

export default function Page(
    props: {
        params: Promise<{ id: string }>,
        searchParams: Promise<{ entity: string, run: string, model: string }>
    }
) {
    const searchParams = use(props.searchParams);

    const {
        entity,
        run,
        model
    } = searchParams;

    const params = use(props.params);

    const {
        id
    } = params;

    const supabase = createClient();
    const [open, setOpen] = useState(true);
    const [issuesData, setIssuesData] = useState<any>(null);
    const [flagData, setFlagData] = useState<FlagTypeV2 | null>(null);
    const [issuesError, setIssuesError] = useState<any>(null);
    const [flagError, setFlagError] = useState<any>(null);
    const {toast} = useToast();
    const auth = useAuth();
    const router = useRouter();

    useEffect(() => {
        async function fetchData() {
            try {
                // Issues are no longer used, but we'll keep this for backward compatibility
                let { data: issues, error: issuesErr } = await supabase.from('xfer_issues').select('*');
                setIssuesData(issues!);
                setIssuesError(issuesErr!);

                // Get flag from the xfer_flags table
                let { data: flagV2, error: flagErrV2 } = await supabase
                  .from('xfer_flags')
                    .select('*')
                    .eq("id", +id)
                    .single();

                if (flagV2) {
                    // First convert to unknown, then to FlagTypeV2 to avoid type errors
                    // This is safe because ensureModelParsed will properly parse the model
                    const parsedFlag = ensureModelParsed(flagV2 as unknown as FlagTypeV2);
                    setFlagData(parsedFlag);
                    setFlagError(null);
                } else {
                    setFlagError(flagErrV2);
                    if(flagErrV2) {
                        toast({title: "Error", description: "Flag not found", variant: "destructive"})
                    }
                }
            } catch (error) {
                console.error(error);
                toast({title: "Error", description: "An error occurred while fetching data", variant: "destructive"})
            }
        }
        fetchData();
    }, [id, supabase]);

    useEffect(() => { if(open !== null && open === false) router.back()},[open]);

    const issueMap = issuesData && createIssueMap(issuesData);

    return issueMap && flagData && (
        <SimpleModal testId="flag-detail-modal">
                <FlagExpandedDetail flag={flagData} issueMap={issueMap}
                                    admin={auth.admin}/>
        </SimpleModal>
    );
}
