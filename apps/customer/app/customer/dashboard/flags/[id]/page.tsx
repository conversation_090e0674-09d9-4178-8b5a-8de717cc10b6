/**
 * Next.js Client Component for ESG Effect Flag Detail View
 *
 * This client-side component provides a detailed view of individual ESG (Environmental, Social, 
 * Governance) effect flags within the EkoIntelligence platform. Effect flags represent analyzed
 * impact events or sustainability assessments for entities (companies, funds, organizations) that
 * have been processed through the analytics pipeline. The component fetches flag data from the
 * customer database and renders comprehensive analysis details including impact assessments,
 * model classifications, citations, and AI-generated analysis content.
 *
 * ## Core Functionality
 * - **Dynamic Flag Loading**: Fetches flag data from `xfer_flags` table using URL parameter ID
 * - **Model Data Parsing**: Processes complex JSONB model data using flag converter utilities
 * - **Navigation Integration**: Updates breadcrumb navigation with flag title and preserved URL parameters
 * - **Error Handling**: Displays toast notifications for missing or invalid flags
 * - **Responsive Display**: Renders flag details using glass-morphism design components
 *
 * ## Route Parameters
 * - **id**: Flag identifier from URL path (e.g., `/customer/dashboard/flags/123`)
 * - Preserves query parameters for entity and run context filtering
 *
 * ## Data Processing Pipeline
 * 1. **URL Parameter Extraction**: Gets flag ID from Next.js dynamic route
 * 2. **Database Query**: Fetches flag record from Supabase `xfer_flags` table
 * 3. **Model Parsing**: Processes JSONB model field using `ensureModelParsed()` utility
 * 4. **Navigation Update**: Sets page title and breadcrumb path with flag information
 * 5. **Component Rendering**: Passes processed data to `FlagExpandedDetail` component
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Client component with dynamic routing and navigation hooks
 * - **Supabase**: Database client for secure flag data access with RLS policies
 * - **React State Management**: `useState` and `useEffect` for component lifecycle management
 * - **Navigation Context**: Custom navigation hooks for breadcrumb and URL parameter handling
 * - **Flag Utilities**: Converter functions for model parsing and title extraction
 * - **Toast Notifications**: User feedback for error states and missing data
 *
 * ## System Architecture
 * This component fits into the broader ESG analysis system:
 * - **Analytics Backend**: Python system generates ESG flags and stores in analytics database
 * - **Data Sync Layer**: `xfer_flags` table synchronizes data between analytics and customer databases
 * - **Dashboard Navigation**: This component provides drill-down detail from flag list views
 * - **Flag Display System**: Renders comprehensive flag analysis using specialized components
 * - **User Context**: Maintains filtering and navigation state across dashboard views
 *
 * ## Related Components
 * - Flag list views and dashboard navigation
 * - Entity detail pages and run-based filtering
 * - Flag expanded detail component for comprehensive display
 * - Navigation context and URL parameter preservation
 *
 * ## Database Schema Integration
 * - Queries `xfer_flags` table with integer ID lookup
 * - Processes JSONB `model` field containing `FlagModelData` structure
 * - Handles flag metadata including entity references and run identifiers
 * - Integrates with Supabase RLS policies for secure data access
 *
 * ## Security & Performance
 * - Client-side rendering with Supabase RLS authentication
 * - Single database query with ID-based lookup for performance
 * - Error boundaries with user-friendly toast notifications
 * - Navigation state preservation for consistent user experience
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see {@link ../../flag-expanded-detail} FlagExpandedDetail Component
 * @see {@link ../../../../types} FlagTypeV2 Type Definition
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Client component for detailed ESG flag analysis display with dynamic routing and navigation integration
 * @example ```typescript
 * // URL: /customer/dashboard/flags/123?entity=AAPL&run=latest
 * // Component fetches flag data and displays comprehensive analysis
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

"use client";
import { createClient } from '@/app/supabase/client'
import React, { useEffect, useState } from 'react'
import { FlagExpandedDetail } from '@/app/customer/dashboard/flags/flag-expanded-detail'
import { runAsync } from '@utils/react-utils'
import { useNav } from '@/components/context/nav/nav-context'
import { useParams } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'
import { FlagTypeV2 } from '@/types'
import { ensureModelParsed, getFlagShortTitle, getFlagTitle } from '@/utils/flag-converter'
import { useNavigationWithParams } from '@/hooks/use-navigation-with-params'

export default function Page() {
    const supabase = createClient();
    const id= useParams().id;
    const [flagData, setFlagData] = useState<FlagTypeV2 | null>(null);
    const [issueMap, setIssueMap] = useState<any>(null);
    const nav = useNav();
    const navWithParams = useNavigationWithParams();
    const admin = false;
    const {toast} = useToast();

    useEffect(() => {
        runAsync(async () => {
            if (id) {
                // Model sections are now directly attached to each flag
                // No need to query xfer_issues table
                setIssueMap(new Map());

                // Get flag from the xfer_flags table
                const {data: flagDataV2, error: flagErrorV2} = await supabase
                  .from('xfer_flags')
                    .select('*')
                    .eq("id", +id)
                    .single();

                if (flagDataV2) {
                    // First convert to unknown, then to FlagTypeV2 to avoid type errors
                    // This is safe because ensureModelParsed will properly parse the model
                    const parsedFlag = ensureModelParsed(flagDataV2 as unknown as FlagTypeV2);
                    setFlagData(parsedFlag);

                    // Update navigation with preserved URL parameters
                    const flagTitle = getFlagTitle(parsedFlag);
                    const flagShortTitle = getFlagShortTitle(parsedFlag);

                    nav.changeTitle(flagTitle || "Flag Details");
                    nav.changeNavPath(navWithParams.createNavItems([
                        { label: "Dashboard", href: "/customer/dashboard" },
                        { label: "Flags", href: "/customer/dashboard/flags" },
                        { label: flagShortTitle || "Flag " + id, href: "/customer/dashboard/flags/" + id }
                    ]));
                } else {
                    toast({description: "Flag not found", variant: "destructive"});
                }
            }
        });
    },[]);
    if(!flagData) {
        return <div>Loading...</div>
    }
    return (
        <FlagExpandedDetail flag={flagData} issueMap={issueMap} admin={admin}/>
    )
}
