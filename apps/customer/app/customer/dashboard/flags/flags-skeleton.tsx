/**
 * ESG Flags Loading Skeleton Component for Entity Analysis Dashboard
 *
 * This React component provides a responsive loading skeleton specifically designed for the ESG (Environmental, 
 * Social, Governance) flags dashboard within the EkoIntelligence platform. It displays placeholder elements 
 * that mirror the actual flag analysis interface layout, including animated donut chart skeletons and text 
 * placeholders, ensuring consistent user experience during data loading states.
 *
 * ## Core Functionality
 * - **Responsive Skeleton Layout**: Mirrors the actual ESG flags dashboard structure with proper grid positioning
 * - **Animated Donut Chart Placeholders**: Shows spinning placeholder donuts matching the real chart dimensions
 * - **Text Content Skeletons**: Multiple skeleton bars simulating flag titles, summaries, and metadata
 * - **Glass-morphism Consistency**: Maintains visual design language through loading states
 * - **Accessibility Support**: Provides proper test IDs for automated testing and accessibility tools
 *
 * ## Visual Structure
 * The skeleton replicates the two-column ESG dashboard layout:
 * 1. **Left Column**: First donut chart skeleton with associated text placeholders
 * 2. **Right Column**: Second donut chart skeleton with matching text layout
 * 3. **Responsive Grid**: Adapts from single column on mobile to dual columns on larger screens
 * 4. **Animation Effects**: Pulse animations on skeleton elements and spinning donut charts
 *
 * ## Key Dependencies
 * - **Next.js Image**: Optimized image component for skeleton donut chart placeholders
 * - **Shadcn/ui Skeleton**: Reusable skeleton component with consistent animation and styling
 * - **Tailwind CSS**: Responsive grid layout, animation classes, and glass-morphism design system
 * - **React**: Modern component structure with TSX for type safety
 *
 * ## Design System Integration
 * This component follows the EkoIntelligence glass-morphism design principles:
 * - **Rounded Elements**: Generous border radii (1.5rem standard) for modern, approachable feel
 * - **Opacity Effects**: Reduced opacity (20%) on skeleton donuts for subtle visual hierarchy
 * - **Responsive Breakpoints**: Mobile-first design with lg: and xl: breakpoints for multi-column layouts
 * - **Consistent Spacing**: Follows established spacing patterns with gap-4, mt-12, mb-8 intervals
 * - **Animation Consistency**: Pulse animations maintain visual continuity during loading states
 *
 * ## System Architecture
 * This skeleton component integrates with the broader ESG analysis loading flow:
 * - **Entity Dashboard Loading**: Displayed while EntityAnalysisReport fetches flag data from xfer_flags table
 * - **Data Pipeline Visualization**: Provides visual feedback during flag processing and model section mapping
 * - **Performance Optimization**: Prevents layout shift by maintaining consistent dimensions during async operations
 * - **User Experience**: Ensures users understand content is loading rather than experiencing empty states
 *
 * ## Responsive Behavior
 * - **Mobile (default)**: Single column layout with stacked donut chart skeletons
 * - **Large (lg:)**: Single column maintained for medium-large screens
 * - **Extra Large (xl:)**: Two-column grid layout matching the production dashboard
 * - **Image Sizing**: Responsive donut images adapt across breakpoints (md:450px, lg:350px, 2xl:450px)
 *
 * ## Testing Integration
 * The component includes `data-testid="flags-skeleton"` for reliable automated testing, allowing
 * end-to-end tests to verify loading states and ensure proper skeleton display during flag data fetching.
 *
 * @see https://nextjs.org/docs/app/api-reference/components/image Next.js Image Component
 * @see https://ui.shadcn.com/docs/components/skeleton Shadcn/ui Skeleton Component  
 * @see https://tailwindcss.com/docs/responsive-design Tailwind CSS Responsive Design
 * @see {@link ../entity-analysis-report.tsx} Entity Analysis Report - Main dashboard component
 * @see {@link ../../../../components/ui/skeleton.tsx} Skeleton Component - Base skeleton implementation
 * <AUTHOR>
 * @updated 2025-07-23
 * @description ESG Flags Loading Skeleton Component for Entity Analysis Dashboard providing responsive loading placeholder interface
 * @example ```tsx
 * // Used in EntityAnalysisReport during data loading
 * {isLoading && <FlagsSkeleton />}
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import Image from "next/image";
import {Skeleton} from "@/components/ui/skeleton";

export function FlagsSkeleton() {
    return (<>
        <div className="grid gap-4   grid-cols-1  lg:grid-cols-1 xl:grid-cols-2" data-testid="flags-skeleton">

            <div className="flex flex-col items-center justify-center ">
                <div className="flex flex-col items-center justify-center opacity-20">
                    <Image alt="donut skeleton" className="block md:w-[450px] lg:w-[350px] 2xl:w-[450px] animate-pulse"
                           src={"/skeletons/donut.png"} width={328} height={328}/>
                </div>

                <div className="gap-4 items-center justify-center flex space-x-4 flex-row flex-wrap mt-12 mb-8">
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-24"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-20"/>
                    <Skeleton className="h-4 w-8"/>
                </div>
            </div>


            <div className="flex flex-col items-center justify-center ">
                <div className="flex flex-col items-center justify-center opacity-20">
                    <Image alt="donut skeleton" className="block md:w-[450px] lg:w-[350px] 2xl:w-[450px] animate-pulse"
                           src={"/skeletons/donut.png"} width={328} height={328}/>
                </div>

                <div className="gap-4 items-center justify-center flex space-x-4 flex-row flex-wrap mt-12 mb-8">
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-24"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-56"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-48"/>
                    <Skeleton className="h-4 w-36"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-18"/>
                    <Skeleton className="h-4 w-20"/>
                    <Skeleton className="h-4 w-8"/>
                </div>
            </div>


        </div>
    </>)
}
