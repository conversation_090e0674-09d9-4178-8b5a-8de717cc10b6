/**
 * ESG Flag Expanded Detail Display Component
 *
 * This React component provides a comprehensive, expanded view of individual ESG (Environmental, Social, Governance) 
 * flags within the EkoIntelligence platform. It serves as the detailed view component for displaying flag analysis,
 * impact assessments, model classifications, citations, and statements with full administrative capabilities.
 * The component processes complex ESG flag data from the analytics backend and presents it through an interactive,
 * glass-morphism UI with expandable sections and detailed metadata display.
 *
 * ## Core Functionality
 * - **Flag Detail Visualization**: Comprehensive display of flag titles, summaries, analysis text, and reason descriptions
 * - **Impact Badge Display**: Prominent impact assessment with severity indicators and tooltips for user guidance
 * - **Model Classification Mapping**: Dynamic display of model section classifications with descriptions and tooltips
 * - **Citation Management**: Integrated citation display with sorting and administrative access controls
 * - **Admin Controls**: Administrative delete and trace buttons for flag management and debugging
 * - **Collapsible Impact Assessment**: Expandable detailed impact analysis section with dimension breakdowns
 * - **Statement Processing**: Display of source statements and evidence supporting flag conclusions
 * - **Issue Integration**: Links to Linear issue tracking system for flag-related problem management
 *
 * ## Component Architecture
 * This component renders as a single card container with multiple sections:
 * 1. **Header Section**: Flag title, impact badge, model classifications, and administrative controls
 * 2. **Content Section**: Markdown-rendered flag analysis text with embedded citations
 * 3. **Citations Section**: Sorted and reduced citation display with administrative capabilities
 * 4. **Footer Section**: Collapsible impact assessment with detailed measurements and scoring
 *
 * ## Key Dependencies
 * - **React 18+**: Modern React with hooks (useState, useEffect) for state management and lifecycle
 * - **Next.js 15 App Router**: Component routing and dynamic imports for performance optimization
 * - **Lucide React**: Icon library providing ESG-themed icons (FlagIcon, Flame) and UI indicators
 * - **Tailwind CSS**: Utility-first CSS framework with glass-morphism design system implementation
 * - **Custom UI Components**: Card, Badge, Tooltip components from internal design system
 * - **EkoMarkdown**: Custom markdown renderer with citation support and admin-aware display
 * - **Flag Converter Utilities**: Helper functions for extracting data from complex flag model structures
 * - **Model Section Loader**: Async loader for fetching model section metadata and descriptions
 *
 * ## Data Flow Architecture
 * 1. **Flag Processing Pipeline**: Processes FlagTypeV2 objects through flag-converter utilities for data extraction
 * 2. **Model Section Loading**: Asynchronously loads model section metadata using getModelSection API
 * 3. **Citation Processing**: Reduces and sorts citations by score for optimal user experience
 * 4. **Issue Mapping**: Maps flag issues to Linear issue system for integrated project management
 * 5. **Admin Context Integration**: Responds to admin authentication status for conditional feature display
 *
 * ## Database Integration
 * **Primary Data Source**: `xfer_flags` table via FlagTypeV2 interface
 * - `id` (Integer, PK): Unique flag identifier for administrative operations and tracing
 * - `entity_xid` (Text): Entity identifier linking flags to specific companies/organizations
 * - `flag_type` (Text): Classification (red/green) determining visual styling and impact interpretation
 * - `model` (JSONB): Rich nested data structure containing:
 *   - `flag_title`: Primary display title extracted via getFlagTitle()
 *   - `impact`: Numerical impact score (0-100) for severity assessment and badge display
 *   - `citations`: Array of source citations with scoring and metadata
 *   - `model_sections`: Map of model names to section IDs for classification display
 *   - `flag_summary`: Summary text extracted via getFlagSummary() for quick overview
 *   - `flag_analysis`: Detailed analysis text rendered through EkoMarkdown component
 * - `flag_summary` (Text, Optimized): Extracted summary for performance-optimized access
 * - `flag_analysis` (Text, Optimized): Extracted analysis for performance-optimized rendering
 * - `flag_statements` (JSONB, Optimized): Source statements array for evidence display
 *
 * **Related Tables**:
 * - `xfer_model_sections`: Model section metadata accessed via getModelSection() API
 * - `issues` system: Linear issue tracking integration via issueMap parameter
 *
 * ## System Architecture Integration
 * This component fits into the broader ESG analysis ecosystem:
 * - **Analytics Backend**: Python system generates ESG flags and model sections via analysis pipeline
 * - **Data Sync Layer**: `xfer_` tables synchronize data between analytics and customer databases
 * - **Frontend Layer**: This component provides detailed flag inspection as part of entity dashboard
 * - **Admin Tools**: Integrated administrative controls for flag management and system debugging
 * - **Issue Tracking**: Connected to Linear project management for flag-related issue tracking
 *
 * ## Performance Optimizations
 * - **Lazy Model Loading**: Model sections loaded asynchronously to prevent blocking UI render
 * - **Citation Sorting**: Client-side citation sorting by score for optimal relevance display
 * - **Optimized Database Columns**: Uses extracted `flag_summary` and `flag_analysis` columns for performance
 * - **Conditional Rendering**: Model sections only rendered when data is available to prevent layout shift
 * - **Memoized Parsing**: Flag model parsing cached through ensureModelParsed() utility function
 *
 * ## Security & Access Control
 * - **Administrative Controls**: Delete and trace buttons only visible to admin users via `admin` prop
 * - **Citation Access**: Citation details respect admin authentication for sensitive source information
 * - **Database Security**: Relies on Supabase RLS policies for row-level security on flag access
 * - **Issue Integration**: Issue mapping respects Linear workspace permissions and authentication
 *
 * @see https://react.dev/reference/react/useState React useState Hook Documentation
 * @see https://react.dev/reference/react/useEffect React useEffect Hook Documentation  
 * @see https://lucide.dev/icons Lucide React Icon Library
 * @see https://tailwindcss.com/docs Tailwind CSS Documentation
 * @see {@link ../../utils/flag-converter.ts} Flag Converter Utilities
 * @see {@link ../../components/collapsible-impact-assessment.tsx} Collapsible Impact Assessment Component
 * @see {@link ../../components/citation/index.tsx} Citation Display Component
 * @see {@link ../../utils/model-section-loader.ts} Model Section Loader Utility
 * @see {@link ../../components/admin/index.tsx} Administrative Control Components
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This component provides comprehensive, expanded view of individual ESG flags with detailed analysis, impact assessments, model classifications, and administrative capabilities.
 * @example
 * ```tsx
 * // Basic usage in entity dashboard
 * <FlagExpandedDetail 
 *   flag={flagData} 
 *   issueMap={new Map()} 
 *   admin={false} 
 * />
 * 
 * // Admin usage with issue mapping
 * <FlagExpandedDetail 
 *   flag={flagData} 
 *   issueMap={issueDataMap} 
 *   admin={true} 
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { Issue } from '@/components/issues'
import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AdminDeleteButton, AdminTraceButton } from '@/components/admin'
import {
    AlertTriangle,
    FlagIcon,
    Flame,
    Heart,
    Leaf,
    Scale,
    Shield,
    TrendingDown,
    TrendingUp,
    Users,
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { impactFlagColorMap, impactText, severityColor } from '@utils/lib/colors'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Badge } from '@/components/ui/badge'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { Citation, CitationType, reduceCitations } from '@/components/citation'
import { FlagTypeV2, ModelSectionTypeV2 } from '@/types'
import { getModelSection } from '@/utils/model-section-loader'
import { CollapsibleImpactAssessment } from '@/components/collapsible-impact-assessment'
import {
    getCitations,
    getFlagSummary,
    getFlagText,
    getFlagTitle,
    getFlagType,
    getModelSections,
    getReason,
} from '@/utils/flag-converter'

export function FlagExpandedDetail(props: {
    flag: FlagTypeV2,
    issueMap: Map<string, Issue>,
    admin: boolean
}) {
    const compareFn = (a: any, b: any) => b.score - a.score;
    const [modelSections, setModelSections] = useState<Record<string, ModelSectionTypeV2 | null>>({});

    // Load model sections for this flag
    useEffect(() => {
        async function loadModelSections() {
            const sections: Record<string, ModelSectionTypeV2 | null> = {};
            const modelSectionsMap = getModelSections(props.flag);

            // Ensure modelSectionsMap is valid before processing
            if (modelSectionsMap && typeof modelSectionsMap === 'object') {
                // Load each model section
                for (const [modelName, sectionId] of Object.entries(modelSectionsMap)) {
                    const section = await getModelSection(modelName, sectionId);
                    sections[modelName] = section;
                }
            }

            setModelSections(sections);
        }

        loadModelSections();
    }, [props.flag]);

    const flagType = getFlagType(props.flag);
    const flagTitle = getFlagTitle(props.flag);
    const flagSummary = getFlagSummary(props.flag);
    const flagText = getFlagText(props.flag);
    const reason = getReason(props.flag);
    const citations = getCitations(props.flag);
    const modelData = props.flag.model;

    return <Card className="mb-8 relative group" data-testid="flag-detail-content">
        <AdminDeleteButton
            tableName="xfer_gw_flags_v2"
            recordId={props.flag.id}
            recordType="flag"
        />
        <AdminTraceButton
            flagId={props.flag.id}
            flagTitle={flagTitle || 'Untitled Flag'}
        />
        <CardHeader>
            <CardTitle className="text-base dark:text-foreground">
                <div className="space-y-3">
                    {/* Title row with icon and text */}
                    <div className="flex items-start gap-2">
                        <FlagIcon className={cn(`w-4 h-4 mt-1 flex-shrink-0`)}
                                  fill={"var(--" + impactFlagColorMap[flagType + "-" + modelData.impact] + ")"}
                        />
                        <h3 className="dark:text-foreground prose flex-1 min-w-0 break-words leading-tight" data-testid="modal-flag-title">
                            {props.issueMap.get(props.flag.issue!)?.title || flagTitle}
                        </h3>
                    </div>

                    {/* Badges row */}
                    <div className="flex flex-wrap gap-2">
                        {/* Enhanced Impact Badge - Made prominent and clickable */}
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Badge
                                        variant="outline"
                                        className={cn(
                                            "flex items-center gap-2 px-2 py-1 text-sm font-semibold border-2",
                                          "bg-gradient-to-r from-white/90 to-white/70 dark:from-slate-800/90 dark:to-slate-700/70 backdrop-blur-sm",
                                          "border-blue-400 text-blue-700 hover:border-blue-500 hover:bg-blue-50 dark:text-blue-300 dark:hover:bg-blue-950/20"
                                        )}
                                        data-testid="impact-badge">
                                        <Flame className="w-5 h-5"
                                               stroke={"var(--" + severityColor(flagType === "green" ? modelData.impact : (100 - modelData.impact)) + ")"}
                                        />
                                        <span className="font-bold">
                                            {impactText(modelData.impact)} Impact
                                        </span>
                                        <span className="text-sm opacity-80">
                                            {modelData.impact}%
                                        </span>
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>Impact: {modelData.impact}% - How great an impact does
                                        this action have on people, animals and the
                                        ecosystem?</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>


                    </div>

                    {/* Reason text */}
                    {reason && <p className="text-sm text-muted-foreground" data-testid="modal-flag-description">{reason}</p>}
                </div>
            </CardTitle>
        </CardHeader>
        <CardContent>
            {/* Display model sections if available */}
            {Object.keys(modelSections).length > 0 && (
                <div className="mb-4">
                    <h4 className="text-sm font-semibold mb-2">Model Classifications</h4>
                    <div className="flex flex-wrap gap-2">
                        {Object.entries(modelSections).map(([modelName, section]) => (
                            section && (
                                <Badge key={modelName} variant="outline" className="flex items-center gap-1">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <span className="flex items-center">
                                                    <span className="font-semibold">{modelName}:</span>
                                                    <span className="ml-1">{section.title || section.section}</span>
                                                </span>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p className="max-w-xs">{section.description || "No description available"}</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </Badge>
                            )
                        ))}
                    </div>
                </div>
            )}



            <div data-testid="modal-flag-analysis">
                <EkoMarkdown
                    citations={citations as CitationType[]}
                    admin={props.admin}>{flagText?.replaceAll(/\n/g, "\n\n")}</EkoMarkdown>
            </div>

            {reduceCitations(citations as CitationType[])
                .sort(compareFn)
                .map((data, j) => <Citation key={j} data={data} admin={props.admin}/>)}
        </CardContent>

        {/* Collapsible Impact Assessment at the bottom */}
        <CollapsibleImpactAssessment flag={props.flag} />

    </Card>;
}
