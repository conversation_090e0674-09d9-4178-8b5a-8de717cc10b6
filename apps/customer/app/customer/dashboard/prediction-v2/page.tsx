/**
 * Next.js App Router Predictive Analysis V2 Dashboard Page Component
 *
 * This React Client Component provides a comprehensive dashboard for exploring predictive ESG (Environmental,
 * Social, Governance) analysis data within the EkoIntelligence platform. The page enables users to analyze
 * multi-year predictive insights for ESG entities through hierarchical analysis layers: entity-year overviews,
 * cluster-based analysis, and granular component-level predictions with detailed risk and opportunity assessments.
 *
 * ## Core Functionality
 * - **Multi-Year Analysis**: Interactive year selection with automated loading of latest available analysis data
 * - **Hierarchical Data Display**: Three-tier analysis structure (Entity → Cluster → Component) for comprehensive insights
 * - **Entity-Year Analysis**: High-level predictive overview with confidence scoring and cluster summaries
 * - **Cluster Analysis**: Mid-level groupings of related ESG components with detailed analysis and interactive component access
 * - **Component Analysis**: Granular analysis of motivation, statement types, engagement, and impact factors
 * - **Interactive Modal System**: Detailed component analysis viewing with rich content display and navigation
 * - **Real-time Data Loading**: Dynamic data fetching based on entity selection and year filtering
 *
 * ## Data Architecture
 * The component retrieves predictive analysis data from three synchronized database tables:
 * - **xfer_predict_entity_year**: Entity-level yearly analysis summaries with cluster references
 * - **xfer_predict_cluster**: Cluster-level analysis groupings with component summaries  
 * - **xfer_predict_component**: Detailed component-level predictions for motivation, engagement, impact, and statement types
 *
 * ## Request Parameters
 * - **Entity Context**: Retrieved from EntityContext provider (entity XID for analysis target)
 * - **Year Selection**: User-controlled year filtering for temporal analysis comparison
 * - **Cluster Selection**: Interactive cluster navigation for drill-down analysis
 * - **Component Selection**: Modal-based component detail viewing with type-specific analysis
 *
 * ## State Management
 * **Primary Analysis Data State**:
 * - `entityYearAnalyses`: Array of yearly entity-level analysis summaries with confidence metrics
 * - `clusterAnalyses`: Array of cluster-level analysis data with component references
 * - `componentAnalyses`: Array of detailed component predictions and risk assessments
 *
 * **UI Control State**:
 * - `selectedYear`: Currently selected year for analysis filtering (defaults to latest available)
 * - `selectedClusterId`: Active cluster for component analysis drilling
 * - `selectedComponentType`: Target component type for modal display (motivation/engagement/impact/statement_type)
 * - `showComponentModal`: Modal visibility state for component detail viewing
 * - `loading`: Data loading state for user feedback during asynchronous operations
 * - `error`: Error state management with user-friendly error message display
 *
 * ## Component Interaction Flow
 * 1. **Initial Load**: Fetches entity-year analyses and auto-selects latest year
 * 2. **Year Selection**: Updates cluster and component data for selected year
 * 3. **Cluster Navigation**: Enables component drilling via cluster analysis cards
 * 4. **Component Analysis**: Opens detailed modal with comprehensive component insights
 * 5. **Modal Navigation**: Supports component type switching within modal interface
 *
 * ## Key Dependencies
 * - **React 18+**: Modern React with hooks, effects, and concurrent features for optimal user experience
 * - **Next.js 15 App Router**: Client-side routing with dynamic navigation and parameter management
 * - **Supabase Client**: Database connectivity for real-time ESG predictive analysis data retrieval
 * - **Entity Context**: Centralized entity state management with automatic entity selection and URL parameter preservation
 * - **Lucide React**: Modern icon library for intuitive UI elements and visual feedback indicators
 * - **ShadCN UI Components**: Consistent design system with glass-morphism styling and accessibility features
 *
 * ## Glass-morphism Design System
 * The component follows EkoIntelligence's signature glass-morphism design language:
 * - **Translucent Containers**: `glass-effect-lit` for primary content areas with backdrop blur
 * - **Interactive Elements**: `glass-effect-subtle` with hover transitions for user engagement
 * - **Brand Integration**: `glass-effect-brand-strong-lit` for active states and selections
 * - **Rounded Borders**: Consistent `rounded-2xl` for modern, approachable interface aesthetics
 * - **Smooth Animations**: `transition-all duration-300` for responsive and polished interactions
 *
 * ## Error Handling & User Experience
 * **Loading States**:
 * - Skeleton loading animations with glass-morphism styling during data retrieval
 * - Progressive data loading with immediate feedback for long-running operations
 * - Graceful handling of partial data availability scenarios
 *
 * **Error States**:
 * - User-friendly error messages with actionable guidance for resolution
 * - Fallback content for missing data scenarios (no entity selected, no analysis available)
 * - Robust error boundaries preventing application crashes from data inconsistencies
 *
 * **Empty States**:
 * - Informative empty state messaging when no predictive analysis data is available
 * - Clear guidance for users on next steps (run analysis, select different entity)
 *
 * ## Performance Optimizations
 * - **Efficient Re-renders**: Optimized useEffect dependencies to minimize unnecessary data fetching
 * - **Conditional Rendering**: Smart component mounting based on data availability and user interaction
 * - **Data Filtering**: Client-side data filtering for year and cluster selections to reduce API calls
 * - **Modal Lazy Loading**: Component analysis modal only renders when needed for memory efficiency
 * - **State Cleanup**: Proper state reset during navigation to prevent stale data display
 *
 * ## Related Components
 * - EntityYearAnalysisCard: Displays yearly entity-level analysis summaries with confidence metrics
 * - ClusterAnalysisCard: Renders cluster-level analysis with component interaction capabilities  
 * - ComponentAnalysisModal: Detailed component analysis viewer with risk/opportunity breakdowns
 * - PredictionClient: Navigation management for breadcrumb context and URL parameter preservation
 * - Entity Context Provider: Centralized entity state and parameter management across analysis pages
 *
 * ## Database Integration
 * **Prediction Analysis Tables**:
 * - Uses latest `run_id` for data consistency across all analysis levels
 * - Supports temporal filtering with year-based data organization
 * - Maintains referential integrity between entity, cluster, and component analysis levels
 * - Optimized indexes on entity_xid and year for efficient data retrieval
 *
 * ## System Architecture
 * This component integrates with the broader EkoIntelligence ESG analysis ecosystem:
 * - **Analytics Backend**: Python-based predictive analysis generation with ML/AI models
 * - **Data Sync Layer**: `xfer_` tables bridge analytics and customer databases for real-time access
 * - **Customer API Layer**: Supabase client provides secure, RLS-protected data access
 * - **Frontend Dashboard**: Integrated navigation with entity selection and analysis tools
 * - **Navigation System**: Breadcrumb context and parameter preservation for seamless user experience
 *
 * ## Security & Data Access
 * - **Row Level Security (RLS)**: Database-level security policies ensure users only access authorized entity data
 * - **Entity Context Validation**: Centralized entity validation prevents unauthorized data access
 * - **Parameter Validation**: URL parameter validation ensures data integrity and prevents injection attacks
 * - **Error Boundary Protection**: Graceful handling of unauthorized access attempts with user-friendly messaging
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts Next.js App Router Pages
 * @see https://react.dev/reference/react/useState React useState Hook Documentation
 * @see https://react.dev/reference/react/useEffect React useEffect Hook Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Integration
 * @see https://ui.shadcn.com/docs/components/dialog ShadCN Dialog Component
 * @see {@link ../../services/prediction.ts} Prediction Data Service Functions
 * @see {@link ../../types/prediction.ts} Prediction Analysis Type Definitions
 * @see {@link ../../components/prediction/EntityYearAnalysis.tsx} Entity Year Analysis Card Component
 * @see {@link ../../components/prediction/ClusterAnalysis.tsx} Cluster Analysis Card Component
 * @see {@link ../../components/prediction/ComponentAnalysis.tsx} Component Analysis Modal Component
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This React Client Component provides a comprehensive dashboard for exploring predictive ESG analysis data through hierarchical entity-year, cluster, and component analysis layers with interactive drilling and modal-based detail viewing.
 * @example ```tsx
  // Usage within customer dashboard routing
  // Route: /customer/dashboard/prediction-v2?entity=ENTITY_XID&run=latest
  
  // Component automatically:
  // 1. Retrieves entity from context
  // 2. Fetches latest predictive analysis data
  // 3. Renders hierarchical analysis interface
  // 4. Enables interactive drilling through analysis layers
  
  // User interaction flow:
  // Entity Selection → Year Selection → Cluster Analysis → Component Details
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  fetchEntityYearAnalyses,
  fetchClusterAnalyses,
  fetchComponentAnalyses
} from '@/services/prediction';
import {
  EntityYearAnalysisResponse,
  ClusterAnalysisResponse,
  PredictiveComponentResponse,
  ComponentType
} from '@/types/prediction';
import { EntityYearAnalysisCard } from '@/components/prediction/EntityYearAnalysis';
import { ClusterAnalysisCard } from '@/components/prediction/ClusterAnalysis';
import { ComponentAnalysisModal } from '@/components/prediction/ComponentAnalysis';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertTriangle } from 'lucide-react';
import { useEntity } from '@/components/context/entity/entity-context';
import { Headline } from '@/components/front-page/headline';
import PredictionClient from './client';

export default function PredictionV2Page() {
  const entityContext = useEntity();
  const entity = entityContext.entity;

  const [entityYearAnalyses, setEntityYearAnalyses] = useState<EntityYearAnalysisResponse[]>([]);
  const [clusterAnalyses, setClusterAnalyses] = useState<ClusterAnalysisResponse[]>([]);
  const [componentAnalyses, setComponentAnalyses] = useState<PredictiveComponentResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // For the modal
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [selectedClusterId, setSelectedClusterId] = useState<string | null>(null);
  const [selectedComponentType, setSelectedComponentType] = useState<ComponentType | null>(null);
  const [showComponentModal, setShowComponentModal] = useState(false);

  // Fetch data
  useEffect(() => {
    if (!entity) return;

    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        // Fetch entity-year analyses
        const entityYearData = await fetchEntityYearAnalyses(entity as string);
        setEntityYearAnalyses(entityYearData);

        // If we have entity-year analyses, set the selected year to the latest one
        if (entityYearData.length > 0) {
          const latestYear = Math.max(...entityYearData.map(a => a.year));
          setSelectedYear(latestYear);

          // Fetch cluster analyses for the latest year
          const clusterData = await fetchClusterAnalyses(entity as string, latestYear);
          setClusterAnalyses(clusterData);

          // Fetch component analyses for the latest year
          const componentData = await fetchComponentAnalyses(entity as string, latestYear);
          setComponentAnalyses(componentData);
        }
      } catch (err) {
        console.error('Error fetching prediction data:', err);
        setError('Failed to load prediction data. Please try again later.');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [entity]);

  // Handle year change
  const handleYearChange = async (year: number) => {
    if (!entity) return;

    try {
      setSelectedYear(year);
      setSelectedClusterId(null);
      setSelectedComponentType(null);

      // Fetch cluster analyses for the selected year
      const clusterData = await fetchClusterAnalyses(entity as string, year);
      setClusterAnalyses(clusterData);

      // Fetch component analyses for the selected year
      const componentData = await fetchComponentAnalyses(entity as string, year);
      setComponentAnalyses(componentData);
    } catch (err) {
      console.error('Error fetching data for year:', year, err);
      setError(`Failed to load data for year ${year}. Please try again later.`);
    }
  };

  // Handle cluster selection
  const handleClusterSelect = (clusterId: string) => {
    setSelectedClusterId(clusterId);
  };

  // Handle component selection
  const handleComponentSelect = (componentType: ComponentType) => {
    setSelectedComponentType(componentType);
    setShowComponentModal(true);
  };

  // Get the selected component analysis
  const getSelectedComponentAnalysis = () => {
    if (!selectedYear || !selectedClusterId || !selectedComponentType) return null;

    return componentAnalyses.find(
      a => a.year === selectedYear &&
           a.cluster_id.toString() === selectedClusterId &&
           a.component_type === selectedComponentType
    );
  };

  // Get years from entity-year analyses
  const yearsArray = entityYearAnalyses.map(a => a.year);
  const uniqueYears = Array.from(new Set(yearsArray));
  const years = uniqueYears.sort((a, b) => a - b);

  // Get clusters for the selected year
  const yearClusters = clusterAnalyses
    .filter(a => a.year === selectedYear)
    .sort((a, b) => a.cluster_id - b.cluster_id);

  // Get the selected entity-year analysis
  const selectedEntityYearAnalysis = entityYearAnalyses.find(a => a.year === selectedYear);

  if (!entity) {
    return (
      <div className="p-6 space-y-6">
        <Headline>Predictive Analysis V2</Headline>
        <div className="glass-effect-lit rounded-2xl p-6 text-center">
          <p className="text-foreground/70">Please select an entity to view prediction data.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Client component to set navigation path */}
      <PredictionClient />

      <Headline>Predictive Analysis V2</Headline>

      {loading ? (
        <div className="space-y-6">
          <Skeleton className="h-12 w-48 glass-effect-subtle rounded-2xl" />
          <Skeleton className="h-64 w-full glass-effect-subtle rounded-2xl" />
          <Skeleton className="h-64 w-full glass-effect-subtle rounded-2xl" />
        </div>
      ) : error ? (
        <div className="glass-effect-brand-compliment-lit rounded-2xl p-6 flex items-center border-l-4 border-red-500">
          <AlertTriangle className="h-6 w-6 text-red-400 mr-3" />
          <p className="text-foreground">{error}</p>
        </div>
      ) : entityYearAnalyses.length === 0 ? (
        <div className="glass-effect-lit rounded-2xl p-6 text-center">
          <p className="text-foreground/70">No predictive analysis data available for this entity. Run a predictive analysis first.</p>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Year selector */}
          {years.length > 1 && (
            <Tabs
              value={selectedYear?.toString()}
              onValueChange={(value) => handleYearChange(parseInt(value))}
              className="w-full"
            >
              <TabsList className="glass-effect-subtle rounded-xl p-1 space-x-1">
                {years.map(year => (
                  <TabsTrigger
                    key={year}
                    value={year.toString()}
                    className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                  >
                    {year}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          )}

          {/* Entity-year analysis */}
          {selectedEntityYearAnalysis && (
            <div className="mb-10">
              <h2 className="text-2xl font-bold text-white mb-4">Entity Overview</h2>
              <EntityYearAnalysisCard
                analysis={selectedEntityYearAnalysis.model}
                onViewCluster={handleClusterSelect}
              />
            </div>
          )}

          {/* Cluster analyses */}
          {yearClusters.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-white mb-4">Cluster Analysis</h2>
              <div className="space-y-6">
                {yearClusters.map(cluster => (
                  <ClusterAnalysisCard
                    key={`${cluster.year}-${cluster.cluster_id}`}
                    analysis={cluster.model}
                    onViewComponent={handleComponentSelect}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Component analysis modal */}
          <Dialog open={showComponentModal} onOpenChange={setShowComponentModal}>
            <DialogContent className="sm:max-w-[800px] glass-effect-strong-lit rounded-2xl border-border/20">
              <DialogHeader>
                <DialogTitle className="text-foreground">Component Analysis</DialogTitle>
              </DialogHeader>
              {getSelectedComponentAnalysis() && (
                <ComponentAnalysisModal analysis={getSelectedComponentAnalysis()!.model} />
              )}
            </DialogContent>
          </Dialog>
        </div>
      )}
    </div>
  );
}
