/**
 * ESG Claims List Component V2 - Interactive Claims Dashboard Display
 *
 * This React component renders a comprehensive list view of ESG (Environmental, Social, Governance) 
 * claims with advanced filtering, interactive animations, and detailed claim previews. The component
 * serves as the primary interface for browsing and accessing corporate sustainability claims analysis
 * within the customer dashboard.
 *
 * ## Core Functionality
 * - **Interactive Claims List**: Displays paginated claims with hover animations and visual indicators
 * - **Greenwashing Detection**: Visual badges and status indicators for greenwashing analysis results
 * - **Accuracy Assessment**: Color-coded badges showing claim verification status (accurate/inaccurate)
 * - **Confidence Visualization**: Progress bars and percentage badges displaying AI analysis confidence
 * - **Citation Integration**: First citation metadata display for document attribution
 * - **Admin Controls**: Conditional admin-only delete functionality with proper permissions
 * - **Responsive Design**: Glass-morphism cards with adaptive layouts for different screen sizes
 *
 * ## Greenwashing Analysis Integration
 * The component supports comprehensive greenwashing detection with multiple pattern types:
 * - **Vague**: Claims lacking specific metrics or commitments
 * - **Misleading**: Claims that misrepresent actual environmental impact  
 * - **Offsetting**: Claims relying heavily on carbon offsetting without direct action
 * - **Intentions**: Claims describing future intentions without concrete commitments
 * - **Distracting**: Claims diverting attention from significant environmental issues
 * - **Organizational/Fund/Product**: Category-specific greenwashing patterns
 *
 * ## Animation & User Experience
 * - **Framer Motion Integration**: Smooth entrance animations with viewport-based triggers
 * - **Staggered Loading**: Claims appear with elegant delays for visual appeal
 * - **Hover Effects**: Card elevation and shadow animations on interaction
 * - **Confidence Indicators**: Animated progress bars showing analysis confidence levels
 * - **Visual Hierarchy**: Importance and confidence badges for quick assessment
 *
 * ## Data Flow & System Architecture
 * - **Database Source**: Reads from `xfer_claims` table with optimized performance fields
 * - **Entity Context**: Integrates with entity selection context for proper routing
 * - **Citation System**: Displays first citation for immediate document attribution
 * - **Verification Override**: Shows manual verification status when available
 * - **Importance Scoring**: Filters and displays claims based on importance (0-100 scale)
 * - **ESG Classification**: Special badges for ESG-specific claims identification
 *
 * ## UI Components & Design System
 * - **Glass-morphism Cards**: Translucent surfaces with backdrop blur effects
 * - **Badge System**: Comprehensive status indicators (accuracy, confidence, importance, date)
 * - **Icon Integration**: Lucide React icons (CheckCircle2, AlertCircle) for status visualization
 * - **Markdown Rendering**: Rich text display for claim statements and verdicts
 * - **Responsive Typography**: Adaptive text sizing and truncation for different viewports
 * - **Color Coding**: Semantic color schemes (green=accurate, red=inaccurate, amber=greenwashing)
 *
 * ## Props Interface & Component Contract
 * ```typescript
 * interface ClaimsListV2Props {
 *   claimsData: ClaimTypeV2[] | undefined | null;  // Claims data array
 *   admin: boolean;                                 // Admin mode for enhanced controls
 *   compact?: boolean;                             // Optional compact layout mode
 * }
 * ```
 *
 * ## Database Schema Integration
 * Integrates with the `xfer_claims` table optimized structure:
 * - **Performance Fields**: summary, conclusion, statement_text extracted for fast rendering
 * - **Verification Status**: verified boolean field for manual override capability
 * - **Importance Filtering**: importance field (0-100) for claim prioritization
 * - **Model JSON**: Complete analysis results including greenwashing detection and citations
 * - **Entity Relationships**: entity_xid linking to entity analysis context
 * - **Run Tracking**: run_id for analysis version management
 *
 * ## System Architecture Context  
 * This component fits into the broader ESG analysis ecosystem:
 * - **Analytics Backend**: Python system generates claim analysis with LLM integration
 * - **Data Sync Layer**: `xfer_claims` table synchronizes between analytics and customer databases
 * - **API Layer**: Next.js API routes provide optimized claim data access
 * - **Frontend Layer**: This component provides the primary claims browsing interface
 * - **Admin Tools**: Enhanced controls for administrators managing claim verification
 * - **Citation System**: Integrated document attribution and referencing
 *
 * ## Security & Access Control
 * - **RLS Integration**: Respects Supabase Row Level Security policies on xfer_claims table
 * - **Admin Conditional**: Admin-only features (delete buttons) controlled via boolean prop
 * - **XSS Protection**: All user content properly escaped through React and Markdown components
 * - **Entity Access**: Claims filtered by user's entity access permissions
 *
 * ## Performance Optimizations
 * - **Viewport Animation**: Motion components trigger only when entering viewport (20% threshold)
 * - **Text Truncation**: Long summaries truncated at 120 characters for list performance
 * - **Optimized Fields**: Uses extracted database fields instead of parsing JSON for display
 * - **Memoized Rendering**: EkoMarkdown component uses memoization for expensive rendering
 * - **Citation Deduplication**: Efficient first citation extraction without full processing
 *
 * ## Interactive Features
 * - **Click Navigation**: Card clicks navigate to detailed claim analysis pages
 * - **Admin Delete**: Conditional delete buttons with proper confirmation for administrators
 * - **Hover States**: Visual feedback with shadows and transitions on interaction  
 * - **Empty State**: Friendly empty state display when no claims are available
 * - **Responsive Layout**: Adapts container padding and spacing based on compact mode
 *
 * @see https://lucide.dev/guide/packages/lucide-react Lucide React Icons Documentation
 * @see https://www.framer.com/motion/introduction/ Framer Motion Animation Library
 * @see https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating Next.js Navigation
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see {@link ../../../../types/claim.ts} ClaimTypeV2 Interface Definition
 * @see {@link ../../../../components/context/entity/entity-context.tsx} Entity Context Provider
 * @see {@link ../../../../components/markdown/eko-markdown.tsx} EkoMarkdown Renderer
 * @see {@link ../../../../components/admin.tsx} Admin Delete Button Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Interactive ESG claims list component with animations, greenwashing detection, accuracy badges, and comprehensive claim previews for customer dashboard browsing.
 * @example ```tsx
 * // Display claims list with admin controls
 * <ClaimsListV2 
 *   claimsData={claims} 
 *   admin={isAdmin} 
 *   compact={false}
 * />
 * 
 * // Compact mode for smaller spaces
 * <ClaimsListV2 
 *   claimsData={claims} 
 *   admin={false} 
 *   compact={true}
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { ClaimTypeV2 } from '@/types/claim'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle2 } from 'lucide-react'
import React from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useEntity } from '@/components/context/entity/entity-context'
import { cn } from '@utils/lib/utils'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import Markdown from 'react-markdown'
import { AdminDeleteButton } from '@/components/admin'

const variants = {
    hidden: {opacity: 0, y: 20},
    visible: {opacity: 1, y: 0},
};

const greenwashingTypes: {[key:string]:string} = {
    "vague": "Vague",
    "misleading": "Misleading",
    "offsetting": "Includes Offsetting",
    "intentions": "Describes Intentions",
    "distracting": "Distracting",
    "org": "Possible Greenwashing",
    "fund": "Possible Greenwashing",
    "product": "Possible Greenwashing"
}

function accuracy(valid: boolean, greenwashing: boolean, greenwashingType?: string) {
    if (greenwashing) {
        if (valid) {
            return "Accurate but " + (greenwashingTypes[greenwashingType || ""] || "Greenwashing");
        } else {
            return "Inaccurate and " + (greenwashingTypes[greenwashingType || ""] || "Greenwashing");
        }
    } else {
        return valid ? "Accurate Claim" : "Inaccurate";
    }
}

export function ClaimsListV2({claimsData, admin, compact = false}: {
    claimsData: ClaimTypeV2[] | undefined | null,
    admin: boolean,
    compact?: boolean
}) {
    const {queryString} = useEntity();
    const router = useRouter()

    if (!claimsData || claimsData.length === 0) {
        return (
            <div className="text-center py-8" data-testid="empty-state">
                <p className="text-sm text-muted-foreground" data-testid="no-claims-message">No claims found</p>
            </div>
        );
    }

    return (
        <div className={cn("mx-auto container", compact ? "p-0" : "p-4 min-h-screen")} data-testid="claims-list">
            {claimsData.map((claim, j) => {
                const model = claim.model;

                // Determine if the claim is valid and if it's greenwashing
                const isValid = claim.verified !== null ? claim.verified : model?.valid_claim;
                const isGreenwashing = model?.greenwashing || false;

                // Get greenwashing type from the model if available
                let greenwashingType = '';
                if (typeof model?.greenwashing === 'object' && model.greenwashing !== null) {
                    // Use type assertion to access the type property
                    const gwObj = model.greenwashing as any;
                    greenwashingType = gwObj.type || '';
                }

                // Determine the claim status icon, color, and text
                let statusIcon;
                let statusColor;
                let statusText;

                if (isValid) {
                    statusIcon = <CheckCircle2 className="w-5 h-5 inline-block"/>;
                    statusColor = "bg-green-50 border-green-200 text-green-700 dark:bg-green-950 dark:border-green-800 dark:text-green-400";
                    statusText = "Accurate";
                } else {
                    statusIcon = <AlertCircle className="w-5 h-5 inline-block"/>;
                    statusColor = "bg-red-50 border-red-200 text-red-700 dark:bg-red-950 dark:border-red-800 dark:text-red-400";
                    statusText = "Inaccurate";
                }

                // If it's greenwashing, add that to the status text
                if (isGreenwashing) {
                    statusText += greenwashingType ? ` (${greenwashingTypes[greenwashingType] || "Greenwashing"})` : " (Greenwashing)";
                }

                // Get document info from the model or first citation
                const firstCitation = model?.citations && model.citations.length > 0 ? model.citations[0] : null;
                const docAuthors = firstCitation?.authors || [];
                const docYear = model?.claim_doc_year || firstCitation?.year || new Date().getFullYear();
                const docTitle = model?.claim_doc || firstCitation?.title || '';

                // Truncate text if too long
                const summaryText = claim.summary || model?.summary || '';
                const truncatedText = summaryText.length > 120 ? summaryText.substring(0, 90) + '...' : summaryText

                // Handle card click navigation
                const handleCardClick = (e: React.MouseEvent) => {
                    // Prevent navigation if clicking on interactive elements
                    const target = e.target as HTMLElement
                    if (target.closest('button') || target.closest('a')) {
                        return
                    }

                    const url = '/customer/dashboard/gw/claims/' + claim.id + (queryString ? '?' + queryString : '')
                    router.push(url)
                }

                return (
                    <motion.div
                        initial="hidden"
                        key={j}
                        exit="hidden"
                        whileInView="visible"
                        variants={variants}
                        viewport={{once: true, amount: 0.2}} // Triggers once, 20% in view
                        transition={{duration: 0.3, ease: 'easeOut', delay: 0.2}}
                    >
                        <Card
                          className="mb-4 hover:shadow-md transition-shadow duration-200 overflow-hidden relative group cursor-pointer"
                          data-testid="claim-item"
                          onClick={handleCardClick}
                        >
                                {admin && <AdminDeleteButton
                                  tableName="xfer_claims"
                                    recordId={claim.id}
                                    recordType="claim"
                                />}
                                {/* Confidence indicator bar */}
                                <div className="w-full h-1.5 bg-slate-100 dark:bg-slate-800">
                                    <div
                                        className={`h-full ${isValid ? 'bg-green-500' : 'bg-red-500'}`}
                                        style={{width: `${model?.confidence || 0}%`}}
                                    />
                                </div>

                                <CardContent className="pt-4 pb-4">
                                    <div className="flex justify-between items-start mb-3">
                                        <Badge className={`${statusColor} flex items-center gap-1`}>
                                            {statusIcon}
                                            {statusText}
                                        </Badge>
                                        <div className="flex items-center gap-2">
                                            <Badge variant="outline" data-testid="claim-date">{docYear}</Badge>
                                            {model?.esg_claim && <Badge variant="secondary">ESG</Badge>}
                                        </div>
                                    </div>

                                    <div className="mb-3" data-testid="claim-title">
                                        <div className="font-medium text-lg">
                                            <Markdown
                                              children={'"' + (claim.statement_text || model?.text || truncatedText) + '"'} />
                                        </div>
                                    </div>

                                    <div className="flex flex-col gap-2">
                                        <div className="text-sm text-muted-foreground line-clamp-6" data-testid="claim-content">
                                            <EkoMarkdown
                                              citations={[]}
                                              admin={admin}
                                              skipCitations={true}
                                            >
                                                {model?.verdict || ''}
                                            </EkoMarkdown>
                                        </div>

                                        <div className="flex justify-between items-center mt-2" data-testid="claim-metadata">
                                            <div className="text-xs text-muted-foreground">
                                                ID: {claim.id}
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Badge variant="secondary" data-testid="claim-confidence">
                                                    {model?.confidence || 0}%
                                                </Badge>
                                                <Badge variant="outline" data-testid="claim-importance">
                                                    {model?.importance || 0}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                        </Card>
                    </motion.div>
                );
            })}
        </div>
    );
}
