/**
 * ESG Claims Dashboard Page - Interactive Claims Analysis Interface
 *
 * This Next.js 15 App Router page component serves as the primary dashboard interface for viewing 
 * and analyzing ESG (Environmental, Social, Governance) claims within the customer application.
 * The component provides a comprehensive filtering system, summary statistics, timeline visualization,
 * and detailed claims listing with advanced user interactions and data visualization.
 *
 * ## Core Functionality
 * - **Advanced Filtering**: Displays only ESG claims with confidence >50% and importance ≥30
 * - **Real-time Statistics**: Live summary counts for valid, invalid, and greenwashing claims
 * - **Timeline Visualization**: Chronological claims timeline showing temporal distribution patterns
 * - **Interactive Claims List**: Detailed claims list with hover animations, status indicators, and navigation
 * - **Navigation Integration**: Automatic breadcrumb navigation setup with dynamic nav path updates
 * - **Loading States**: Elegant loading indicators during data fetching operations
 * - **Empty State Handling**: Graceful fallback when no claims match the filtering criteria
 *
 * ## Filtering & Data Processing Logic
 * The component implements sophisticated claim filtering using `useMemo` for performance:
 * - **ESG Classification**: Filters for `model.esg_claim === true` to show only sustainability-related claims
 * - **Confidence Threshold**: Requires `model.confidence > 50` to ensure high-quality analysis results
 * - **Importance Scoring**: Filters for `model.importance >= 30` to highlight newsworthy and impactful claims
 * - **Smart Sorting**: Multi-level sorting by importance (descending) then confidence (descending)
 * - **Performance Optimization**: Limits display to top 100 claims to maintain viewport performance
 * - **Type Safety**: Robust type checking with safe property access patterns
 *
 * ## Summary Statistics & Analytics
 * The component calculates real-time summary statistics for user insights:
 * - **Valid Claims**: Count of claims where `verified === true` or `model.valid_claim === true`
 * - **Invalid Claims**: Count of claims where `verified === false` or `model.valid_claim === false`
 * - **Greenwashing Detection**: Count of claims flagged with `model.greenwashing === true`
 * - **Verification Priority**: Manual `verified` field overrides automated `model.valid_claim` analysis
 * - **Visual Indicators**: Color-coded summary cards with semantic styling for quick comprehension
 *
 * ## Navigation & User Experience
 * - **Breadcrumb Integration**: Automatic navigation path setup using `useNavigationWithParams`
 * - **Context Awareness**: Integrates with entity context for proper URL parameter handling
 * - **Loading States**: User-friendly loading indicators with test-friendly data attributes
 * - **Responsive Design**: Adaptive layout supporting mobile and desktop viewport sizes
 * - **Glass-morphism UI**: Consistent translucent design elements with rounded corners
 * - **Progressive Enhancement**: Works without JavaScript but enhanced with interactive features
 *
 * ## Component Architecture & Data Flow
 * - **Entity Context Integration**: Consumes claims data from `EntityContext` with loading state management
 * - **Authentication Context**: Accesses user admin status from `AuthContext` for enhanced controls
 * - **Navigation Context**: Updates navigation breadcrumbs using `NavContext` with dynamic path generation
 * - **Memoized Processing**: Expensive filtering and sorting operations cached with `useMemo` dependencies
 * - **Conditional Rendering**: Smart component rendering based on data availability and loading states
 *
 * ## Database Integration & Performance
 * Integrates with the `xfer_claims` table optimized for customer dashboard performance:
 * - **Performance Fields**: Uses pre-extracted `summary`, `conclusion`, `statement_text` for fast display
 * - **Importance Indexing**: Leverages `importance` index for efficient high-value claim filtering
 * - **Confidence Metrics**: AI-generated confidence scores (0-100%) for claim accuracy assessment
 * - **Entity Filtering**: Claims automatically filtered by user's entity access permissions via RLS
 * - **Run Tracking**: Supports multiple analysis runs with `run_id` for version management
 * - **Verification Override**: Manual `verified` field allows expert override of automated analysis
 *
 * ## System Architecture Context
 * This page fits into the broader ESG analysis and customer experience ecosystem:
 * - **Analytics Backend**: Python system processes corporate documents and generates claim analysis
 * - **Data Sync Layer**: `xfer_claims` table synchronizes processed data between analytics and customer databases
 * - **API Layer**: Next.js App Router provides server-side data access with Supabase integration
 * - **Frontend Layer**: This component provides the primary user interface for claim exploration
 * - **Navigation System**: Integrates with dashboard navigation and entity selection workflows
 * - **Authentication**: Respects user permissions and admin privileges for enhanced functionality
 *
 * ## UI Components & Design System
 * - **EkoPageTitle**: Semantic page title component with consistent branding and accessibility
 * - **ClaimsTimeline**: Chronological visualization component for temporal claim analysis
 * - **ClaimsListV2**: Advanced claims list with interactive features and detailed previews
 * - **NoData**: Consistent empty state component with descriptive messaging
 * - **Glass Cards**: Translucent filter information cards with backdrop blur effects
 * - **Summary Grid**: Responsive statistics grid with semantic color coding
 * - **Badge System**: Comprehensive status indicators for filters and metadata
 *
 * ## Accessibility & Testing
 * - **Test Attributes**: Comprehensive `data-testid` attributes for automated testing workflows
 * - **Semantic HTML**: Proper heading hierarchy and structural markup for screen readers
 * - **ARIA Labels**: Accessible form controls and interactive elements
 * - **Color Accessibility**: High contrast color schemes supporting various visual needs
 * - **Keyboard Navigation**: Full keyboard accessibility for interactive elements
 * - **Loading States**: Screen reader friendly loading announcements
 *
 * ## Performance Optimizations
 * - **Memoized Filtering**: `useMemo` prevents unnecessary recomputation on unrelated re-renders
 * - **Viewport Limiting**: Caps display at 100 claims to maintain smooth scrolling performance
 * - **Smart Dependencies**: Precise dependency arrays prevent cascade re-renders
 * - **Component Lazy Loading**: Child components load efficiently without blocking main thread
 * - **Database Indexing**: Leverages database indexes on importance, entity_xid, and confidence fields
 * - **Conditional Component Loading**: Timeline and list components only render when data is available
 *
 * ## Error Handling & Edge Cases
 * - **Null Safety**: Robust null checking throughout data processing pipeline
 * - **Type Guards**: Safe property access with proper TypeScript type checking
 * - **Loading States**: Graceful handling of async data loading scenarios
 * - **Empty Results**: User-friendly messaging when no claims match filtering criteria
 * - **Network Errors**: Integrates with parent error boundaries for connectivity issues
 * - **Data Validation**: Validates claim structure before processing and display
 *
 * ## Security & Access Control
 * - **RLS Integration**: Respects Supabase Row Level Security policies on xfer_claims access
 * - **Authentication Required**: Page requires valid user authentication via AuthContext
 * - **Admin Features**: Enhanced functionality conditionally available for admin users
 * - **Entity Scoping**: Claims automatically scoped to user's authorized entities
 * - **XSS Protection**: All user-generated content properly sanitized through React and markdown components
 * - **CSRF Protection**: Inherits Next.js built-in CSRF protection for form submissions
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts Next.js App Router Pages
 * @see https://react.dev/reference/react/useMemo React useMemo Hook Documentation
 * @see https://react.dev/reference/react/useEffect React useEffect Hook Documentation
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see https://lucide.dev/guide/packages/lucide-react Lucide React Icons
 * @see {@link ../../../../types/claim.ts} ClaimTypeV2 Interface Definition
 * @see {@link ../../../../components/context/entity/entity-context.tsx} Entity Context Provider
 * @see {@link ../../../../components/context/nav/nav-context.tsx} Navigation Context Provider  
 * @see {@link ../../../../components/context/auth/auth-context.tsx} Authentication Context Provider
 * @see {@link ./claims-list-v2.tsx} Interactive Claims List Component
 * @see {@link ./claims-timeline.tsx} Chronological Claims Timeline Component
 * @see {@link ../../../../components/no-data.tsx} Empty State Component
 * @see {@link ../../../../components/page-title.tsx} Page Title Component
 * @see {@link ../../../../hooks/use-navigation-with-params.tsx} Navigation Parameters Hook
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Primary ESG claims dashboard page with advanced filtering, real-time statistics, timeline visualization, and interactive claims listing for comprehensive corporate sustainability analysis.
 * @example ```tsx
 * // Standard claims dashboard access
 * // Navigate to: /customer/dashboard/gw/claims
 * // Automatically filters for:
 * // - ESG claims only (model.esg_claim === true)
 * // - High confidence (>50%)
 * // - Important claims (>=30)
 * // - Sorted by importance then confidence
 * // - Limited to top 100 results
 * 
 * // Admin users get additional features:
 * // - Delete claim functionality
 * // - Enhanced claim management tools
 * // - Advanced verification override controls
 * 
 * // Empty state handling:
 * // - Shows friendly message when no claims match filters
 * // - Maintains consistent UI layout
 * // - Provides context about active filtering criteria
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

"use client";

import React, { useEffect, useMemo } from 'react'
import NoData from '@/components/no-data'
import { useEntity } from '@/components/context/entity/entity-context'
import { useNav } from '@/components/context/nav/nav-context'
import { useAuth } from '@/components/context/auth/auth-context'
import { ClaimsListV2 } from './claims-list-v2'
import { EkoPageTitle } from '@/components/page-title'
import { ClaimsTimeline } from './claims-timeline'
import { useNavigationWithParams } from '@/hooks/use-navigation-with-params'

export default function Page() {
    const entityContext = useEntity();
    const auth = useAuth();
    const nav = useNav();
    const navWithParams = useNavigationWithParams();

    useEffect(() => {
        nav.changeNavPath(navWithParams.createNavItems([
            { label: "Dashboard", href: "/customer/dashboard" },
            { label: "Claims", href: "/customer/dashboard/gw/claims" }
        ]));
    }, [nav.changeNavPath, navWithParams.queryString]);

    // Filter and process claims data from EntityContext
    const filteredClaimsData = useMemo(() => {
        if (!entityContext.claimsData) return [];

        // Filter claims with ESG claims, confidence > 50, and importance >= 30
        const filteredClaims = entityContext.claimsData
            .filter(claim => {
                // Safely access esg_claim, confidence, and importance with type checking
                const model = claim.model as any;
                return model && typeof model === 'object' &&
                  'esg_claim' in model && model.esg_claim &&
                  'confidence' in model && model.confidence > 50 &&
                  'importance' in model && model.importance >= 30;
            });

        // Sort by importance first (descending), then by confidence (descending)
        filteredClaims.sort((a, b) => {
            // Safely access importance and confidence with type checking
            const modelA = a.model as any;
            const modelB = b.model as any;
            const importanceA = modelA && typeof modelA === 'object' && 'importance' in modelA ? modelA.importance : 0;
            const importanceB = modelB && typeof modelB === 'object' && 'importance' in modelB ? modelB.importance : 0;
            const confA = modelA && typeof modelA === 'object' && 'confidence' in modelA ? modelA.confidence : 0;
            const confB = modelB && typeof modelB === 'object' && 'confidence' in modelB ? modelB.confidence : 0;

            // Sort by importance first
            if (importanceB !== importanceA) {
                return importanceB - importanceA;
            }
            // If importance is equal, sort by confidence
            return confB - confA;
        });

        // Limit to 100 claims
        return filteredClaims.slice(0, 100);
    }, [entityContext.claimsData]);

    // Show loading state if claims are still loading
    if (entityContext.isLoadingClaims) {
        return (
            <div className="container mx-auto p-4" data-testid="loading-claims">
                <EkoPageTitle title="Claims" />
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg">Loading claims...</div>
                </div>
            </div>
        );
    }

    // Calculate counts for summary
    const validCount = filteredClaimsData.filter(c => {
        const isValid = c.verified !== null ? c.verified : c.model?.valid_claim;
        return isValid === true;
    }).length;
    const invalidCount = filteredClaimsData.filter(c => {
        const isValid = c.verified !== null ? c.verified : c.model?.valid_claim;
        return isValid === false;
    }).length;
    const greenwashingCount = filteredClaimsData.filter(c => c.model?.greenwashing).length;

    return (
        <div className="container mx-auto p-4" data-testid="claims-page-content">
            <EkoPageTitle title="Claims" />
            
            {filteredClaimsData && filteredClaimsData.length > 0 ? (
                <>

                    {/* Summary Section */}
                    <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="claims-summary">
                        <h3 className="text-lg font-semibold mb-3">Claims Summary</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="text-center p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                                <div className="text-3xl font-bold text-green-600 dark:text-green-400" data-testid="valid-claims-count">{validCount}</div>
                                <div className="text-sm text-muted-foreground">Valid Claims:</div>
                            </div>
                            <div className="text-center p-4 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
                                <div className="text-3xl font-bold text-red-600 dark:text-red-400" data-testid="invalid-claims-count">{invalidCount}</div>
                                <div className="text-sm text-muted-foreground">Invalid Claims:</div>
                            </div>
                            <div className="text-center p-4 bg-orange-50 dark:bg-orange-950/20 rounded-lg border border-orange-200 dark:border-orange-800">
                                <div className="text-3xl font-bold text-orange-600 dark:text-orange-400" data-testid="greenwashing-claims-count">{greenwashingCount}</div>
                                <div className="text-sm text-muted-foreground">Greenwashing Claims:</div>
                            </div>
                        </div>
                    </div>

                    {/* Claims Timeline */}
                    <ClaimsTimeline claimsData={filteredClaimsData} />

                    <ClaimsListV2 claimsData={filteredClaimsData} admin={auth.admin} />
                </>
            ) : (
                <NoData title="No Claims Found" description="No claims found for this entity" />
            )}
        </div>
    );
}
