# ESG Claims Analysis Dashboard

## Overview

The ESG Claims Analysis Dashboard is a sophisticated React-based interface that provides comprehensive analysis and visualization of corporate Environmental, Social, and Governance (ESG) claims. This module enables users to identify, analyze, and verify corporate sustainability statements against historical evidence, with advanced greenwashing detection capabilities.

The dashboard integrates with EkoIntelligence's dual-database architecture, where claims are processed in the analytics backend and synchronized to the customer database for real-time dashboard display and user interaction.

## Specification

### Core Requirements

The claims analysis system must provide:

1. **Advanced Filtering System**: Display only high-quality ESG claims with confidence >50% and importance ≥30
2. **Real-time Statistics**: Live summary counts for valid, invalid, and greenwashing claims
3. **Timeline Visualization**: Chronological display of claims distribution patterns
4. **Interactive Claims List**: Detailed claims browsing with hover animations and status indicators
5. **Individual Claim Analysis**: Comprehensive detailed view with evidence correlation and greenwashing detection
6. **Admin Controls**: Enhanced functionality for administrative users including claim management
7. **Citation Management**: Complete source attribution for regulatory compliance and transparency

### Data Quality Standards

- **Confidence Threshold**: Minimum 50% AI confidence score for claim accuracy
- **Importance Scoring**: Minimum 30/100 importance score for newsworthy claims
- **ESG Classification**: Only display claims verified as ESG-related
- **Performance Optimization**: Limit display to top 100 claims for optimal viewport performance

## Key Components

### 1. Main Dashboard Page (`page.tsx`)
- **Purpose**: Primary claims dashboard interface with filtering, statistics, and navigation
- **Key Features**: 
  - Advanced filtering logic with memoized performance optimization
  - Real-time summary statistics calculation
  - Integration with EntityContext for claims data
  - Breadcrumb navigation setup
- **Database Integration**: Consumes `xfer_claims` table via EntityContext
- **Performance**: Uses `useMemo` for expensive filtering operations

### 2. Interactive Claims List (`claims-list-v2.tsx`)
- **Purpose**: Comprehensive list view with interactive animations and detailed previews
- **Key Features**:
  - Framer Motion animations with staggered loading
  - Greenwashing detection badges (7 pattern types)
  - Color-coded accuracy indicators
  - Citation display with document attribution
  - Admin delete functionality
- **UI System**: Glass-morphism cards with hover effects and responsive typography

### 3. Claims Timeline (`claims-timeline.tsx`)
- **Purpose**: Chronological visualization of claims by year
- **Key Features**:
  - Vertical timeline with connecting lines
  - Year-based grouping with overflow handling
  - Status indicators for verification state
  - Responsive design for different screen sizes
- **Data Processing**: Groups claims by `claim_doc_year` with performance limits

### 4. Detailed Claim Analysis (`claim-detail-v2.tsx`)
- **Purpose**: In-depth claim analysis with evidence correlation
- **Key Features**:
  - Comprehensive greenwashing analysis (7 pattern types)
  - Evidence integration with citation management
  - AI-powered verdict and confidence scoring
  - Manual verification override capability
- **Patterns Detected**: Vague, Misleading, Offsetting, Intentions, Distracting, Organizational/Fund/Product

### 5. Dynamic Claim Detail Route (`[id]/page.tsx`)
- **Purpose**: Next.js 15 App Router page for individual claim access
- **Key Features**:
  - Client-side rendering with loading states
  - Type-safe database interactions
  - Navigation context updates
  - Admin permission integration

## Dependencies

### Core Framework Dependencies
- **Next.js 15 App Router**: Modern React framework with server-side rendering
- **React 19**: Latest React features including `use()` hook for promise handling
- **TypeScript**: Type safety and development experience

### UI Framework Dependencies
- **Tailwind CSS**: Utility-first CSS framework for styling
- **shadcn/ui**: Component library built on Radix UI primitives
- **Framer Motion**: Animation library for interactive effects
- **Lucide React**: Icon library for status indicators

### Data & State Management
- **Supabase Client**: Database access with Row Level Security
- **React Context API**: State management for entity, navigation, and authentication
- **SWR**: Data fetching with caching and revalidation

### Database Integration
- **Customer Database**: `xfer_claims` table for optimized dashboard performance
- **Analytics Database**: Backend processing in `ana_claims` table
- **Supabase RLS**: Row Level Security for user access control

## Usage Examples

### Basic Claims Dashboard Access
```typescript
// Navigate to claims dashboard
// URL: /customer/dashboard/gw/claims?entity=EntityId&run=latest

// Automatic filtering applied:
// - ESG claims only (model.esg_claim === true)
// - High confidence (>50%)
// - Important claims (>=30)
// - Sorted by importance then confidence
// - Limited to top 100 results
```

### Individual Claim Analysis
```typescript
// Access detailed claim analysis
// URL: /customer/dashboard/gw/claims/[claimId]

// Features available:
// - Comprehensive greenwashing detection
// - Evidence correlation and citations
// - AI-powered confidence scoring
// - Manual verification overrides (admin)
```

### Component Usage
```typescript
import { ClaimsListV2 } from './claims-list-v2'
import { ClaimsTimeline } from './claims-timeline'
import { ClaimDetailV2 } from './claim-detail-v2'

// Interactive claims list with admin controls
<ClaimsListV2 claimsData={filteredClaims} admin={isAdmin} />

// Timeline visualization
<ClaimsTimeline claimsData={claims} />

// Detailed claim analysis
<ClaimDetailV2 claim={claimData} admin={isAdmin} />
```

## Architecture Notes

### System Architecture Flow

```mermaid
graph TD
    A[Corporate Documents] --> B[Analytics Backend Python]
    B --> C[ana_claims Table]
    C --> D[Data Sync Layer]
    D --> E[xfer_claims Table]
    E --> F[Customer Database]
    F --> G[Next.js API Layer]
    G --> H[Claims Dashboard]
    H --> I[Interactive Components]
    
    subgraph "Analytics Database"
        C
    end
    
    subgraph "Customer Database"
        E
        F
    end
    
    subgraph "Frontend Layer"
        G
        H
        I
    end
```

### Component Architecture

```mermaid
graph TB
    A[page.tsx - Main Dashboard] --> B[EntityContext]
    A --> C[ClaimsTimeline]
    A --> D[ClaimsListV2]
    
    D --> E[ClaimDetailV2]
    F[id/page.tsx - Dynamic Route] --> E
    
    B --> G[Supabase Client]
    G --> H[xfer_claims Table]
    
    E --> I[Citation System]
    E --> J[Greenwashing Detection]
    E --> K[Evidence Correlation]
    
    subgraph "Context Providers"
        B
        L[AuthContext]
        M[NavContext]
    end
    
    A --> L
    A --> M
```

### Data Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant P as Page Component
    participant E as EntityContext
    participant S as Supabase Client
    participant D as Database
    
    U->>P: Navigate to Claims Dashboard
    P->>E: Request Claims Data
    E->>S: Query xfer_claims
    S->>D: Execute RLS Query
    D-->>S: Return Filtered Claims
    S-->>E: Claims Data
    E-->>P: Processed Claims
    P->>P: Apply Filters (ESG, Confidence, Importance)
    P->>U: Display Dashboard with Statistics
```

## Known Issues

Based on Linear ticket analysis, the following issues have been identified:

### Resolved Issues
- **EKO-301**: Claims page sidebar navigation blocking (Fixed)
- **EKO-291**: Claims navigation failures (Fixed) 
- **EKO-158**: Removed hallucinated filter features (Fixed)

### Active Issues
- **EKO-303**: Font size inconsistencies in claim display text
- **EKO-211**: Report generation may not include all negative flags from dashboard

### Technical Debt
- Large component files (>400 lines) should be refactored into smaller modules
- Memoization dependencies should be reviewed for cascade re-renders
- Type casting in filtering logic needs improvement for better type safety

## Future Work

### Planned Enhancements (Based on Requirements & Linear Tickets)

1. **Enhanced Filtering System** (EKO-158 follow-up)
   - Implement advanced filter UI with date ranges
   - Add claim type filters (promises vs statements)
   - Custom confidence threshold controls

2. **Performance Optimizations**
   - Implement virtual scrolling for large claim lists
   - Add client-side caching with SWR/React Query
   - Optimize database queries with better indexing

3. **Advanced Analytics Integration**
   - Real-time claim processing status indicators
   - Trend analysis charts and visualizations
   - Export functionality for compliance reporting

4. **Enhanced Greenwashing Detection**
   - Add confidence scores for greenwashing patterns
   - Implement pattern-specific explanations
   - Create educational content for pattern types

5. **Accessibility Improvements**
   - Enhanced keyboard navigation
   - Screen reader optimizations
   - High contrast mode support

## Troubleshooting

### Common Issues

**Claims not loading**
- Verify entity selection in URL parameters
- Check user permissions and RLS policies
- Ensure Supabase client is properly initialized

**Empty claims list**
- Check filtering criteria (confidence >50%, importance ≥30)
- Verify entity has processed claims data
- Check run_id parameter for active analysis run

**Navigation not working**
- Clear browser cache and localStorage
- Verify React Router setup and context providers
- Check for JavaScript errors in browser console

**Performance issues**
- Claims list limited to 100 items for performance
- Filtering operations are memoized for optimization
- Consider adjusting viewport limits if needed

### Debug Mode

Add `#debug` to URL hash to enable additional logging:
```
/customer/dashboard/gw/claims?entity=EntityId#debug
```

## FAQ

### User-Centric Questions

**Q: Why do I only see 100 claims on the dashboard?**
A: The dashboard limits display to the top 100 claims (sorted by importance and confidence) to maintain optimal performance. This ensures fast loading and smooth interactions while showing the most relevant claims.

**Q: What do the confidence scores mean?**
A: Confidence scores (0-100%) indicate how certain our AI analysis is about the claim's accuracy. Only claims with >50% confidence are displayed to ensure high-quality analysis.

**Q: How are greenwashing patterns detected?**
A: Our AI system analyzes claims for 7 specific greenwashing patterns: Vague statements, Misleading claims, Offsetting reliance, Future intentions, Distracting tactics, and category-specific patterns (Organizational/Fund/Product).

**Q: Can I export claims data?**
A: Currently, claims are displayed for analysis within the dashboard. Export functionality is planned for future releases to support compliance reporting.

**Q: Why might some claims show different verification status?**
A: Claims can have both automated AI analysis (`model.valid_claim`) and manual expert verification (`verified` field). Manual verification takes precedence when available.

**Q: How often is claims data updated?**
A: Claims data is synchronized from the analytics database based on processing runs. The frequency depends on entity-specific analysis schedules and document availability.

## References

### Documentation Links
- [Next.js App Router Documentation](https://nextjs.org/docs/app/building-your-application/routing)
- [Supabase React Integration](https://supabase.com/docs/guides/getting-started/tutorials/with-react)
- [Framer Motion Animation Guide](https://www.framer.com/motion/)
- [shadcn/ui Component Library](https://ui.shadcn.com/)

### Related Code Files
- [`/apps/customer/types/claim.ts`](../../types/claim.ts) - ClaimTypeV2 Interface Definition
- [`/apps/customer/components/context/entity/entity-context.tsx`](../../../components/context/entity/entity-context.tsx) - Entity Context Provider  
- [`/apps/customer/components/context/nav/nav-context.tsx`](../../../components/context/nav/nav-context.tsx) - Navigation Context Provider
- [`/apps/customer/components/context/auth/auth-context.tsx`](../../../components/context/auth/auth-context.tsx) - Authentication Context Provider
- [`/apps/customer/components/no-data.tsx`](../../../components/no-data.tsx) - Empty State Component
- [`/apps/customer/components/page-title.tsx`](../../../components/page-title.tsx) - Page Title Component

### Related README Files
- [`/apps/customer/app/customer/dashboard/gw/promises/README.md`](../promises/README.md) - Promises Analysis Module
- [`/apps/customer/app/customer/dashboard/gw/cherry/README.md`](../cherry/README.md) - Cherry Picking Analysis Module  
- [`/apps/customer/app/customer/dashboard/gw/vague/README.md`](../vague/README.md) - Vague Terms Analysis Module

### External Resources
- [ESG Analysis Best Practices](https://www.sustainability.com/thinking/esg-analysis-frameworks/)
- [Greenwashing Detection Methods](https://www.sciencedirect.com/topics/economics-econometrics-and-finance/greenwashing)
- [Corporate Sustainability Reporting Standards](https://www.globalreporting.org/standards/)
- [Glass-morphism Design System](https://uxdesign.cc/glassmorphism-in-user-interfaces-1f39bb1308c9)

### Third-Party Dependencies
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Radix UI Primitives](https://www.radix-ui.com/primitives)
- [React 19 Documentation](https://react.dev/reference/react)

---

## Changelog

### 2025-07-30
- Created comprehensive README.md documentation
- Documented all 5 key components with detailed specifications
- Added architecture diagrams and data flow documentation
- Included troubleshooting guide and FAQ section
- Documented known issues from Linear ticket analysis
- Added future work roadmap based on requirements
- Provided usage examples and component integration patterns

(c) All rights reserved ekoIntelligence 2025