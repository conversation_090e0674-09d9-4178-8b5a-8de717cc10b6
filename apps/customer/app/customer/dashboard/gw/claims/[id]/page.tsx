/**
 * Next.js Dynamic Route Page for ESG Claim Detail Display
 * 
 * This file implements a client-side React page component that displays detailed 
 * ESG (Environmental, Social, Governance) claim analysis information. It serves as 
 * the main claim detail view in the customer dashboard, fetching claim data from 
 * the Supabase database and rendering comprehensive claim analysis including validity 
 * assessment, greenwashing detection, confidence metrics, and supporting documentation.
 * 
 * ## Core Functionality
 * - **Claim Data Retrieval**: Fetches detailed claim information from `xfer_claims` table
 * - **Validity Analysis Display**: Shows claim accuracy assessment with confidence scores
 * - **Greenwashing Detection**: Displays results of AI-powered greenwashing analysis
 * - **Navigation Integration**: Updates breadcrumb navigation and page title dynamically
 * - **Authentication Awareness**: Provides admin-specific features based on user permissions
 * - **Type Safety**: Uses TypeScript with ClaimTypeV2 interface for robust data handling
 * 
 * ## Request Parameters
 * - **Route Parameter**: `id` - Numeric claim identifier from the database
 * 
 * ## Data Flow
 * 1. Extract claim ID from route parameters using Next.js 15 `use()` hook
 * 2. Query Supabase `xfer_claims` table for claim data by ID
 * 3. Update navigation context with claim-specific breadcrumb path
 * 4. Render ClaimDetailV2 component with fetched data and admin permissions
 * 
 * ## Dependencies
 * - **Next.js 15 App Router**: Modern React framework with async components
 * - **Supabase Client**: Database access with Row Level Security and real-time features
 * - **React 18+**: Latest React features including `use()` hook for promise handling
 * - **Navigation Context**: Custom navigation system for breadcrumb management
 * - **Authentication Context**: User authentication and role-based access control
 * 
 * ## Related Components
 * - ClaimDetailV2: Main claim display component with comprehensive analysis view
 * - Navigation Context: Breadcrumb and page title management system
 * - Authentication Context: User session and permission management
 * - Supabase Client: Database connection and query execution
 * 
 * ## Database Schema
 * - Connects to customer Supabase database via RLS-protected queries
 * - Uses `xfer_claims` table for claim data synchronized from analytics database
 * - Handles type casting from database JSON to TypeScript ClaimTypeV2 interface
 * - Supports error handling for non-existent or inaccessible claims
 * 
 * ## Security & Performance
 * - Database queries protected by Supabase Row Level Security policies
 * - Client-side rendering for dynamic content with loading states
 * - Type-safe database interactions prevent runtime errors
 * - Efficient single-record queries with specific ID lookup
 * 
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Guide
 * @see https://react.dev/reference/react/use React use() Hook Documentation
 * @see {@link ../claim-detail-v2.tsx} ClaimDetailV2 Component
 * @see {@link ../../../../types/claim.ts} ClaimTypeV2 Interface
 * <AUTHOR>
 * @updated 2025-07-22
 * @description ESG claim detail page that fetches and displays comprehensive claim analysis including validity assessment, greenwashing detection, and supporting documentation
 * @example ```bash
curl -X GET 'http://localhost:3000/customer/dashboard/gw/claims/12345'
```
 * @docgen doc-by-claude
 * 
 * (c) All rights reserved ekoIntelligence 2025
 */
"use client";
import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import ClaimDetailV2 from '../claim-detail-v2'
import { useNav } from '@/components/context/nav/nav-context'
import { runAsync } from '@utils/react-utils'
import { useAuth } from '@/components/context/auth/auth-context'
import { ClaimTypeV2 } from '@/types/claim'

export default function Page(props: { params: Promise<{ id: string }> }) {
    const params = use(props.params);
    const { id } = params;

    const supabase = createClient();
    const auth = useAuth();
    const nav = useNav();
    const [claim, setClaim] = useState<ClaimTypeV2 | null>(null);

    useEffect(() => {
        runAsync(async () => {
            if (id) {
                // Get claim from xfer_claims
                const {
                    data: claimV2Data,
                    error: claimV2Error
                } = await supabase
                  .from('xfer_claims')
                    .select("*")
                    .eq("id", +id)
                    .single();

                if (claimV2Error) {
                    console.error("Error fetching claim:", claimV2Error);
                    return;
                }

                if (claimV2Data) {
                    // Cast to unknown first to avoid TypeScript errors with Json type
                    setClaim(claimV2Data as unknown as ClaimTypeV2);
                    nav.changeTitle("Claim (" + claimV2Data.id + ")");

                    nav.changeNavPath([
                        { href: "/customer/dashboard", label: "Dashboard" },
                        { href: "/customer/dashboard/gw/claims", label: "Claims" },
                        { href: "/customer/dashboard/gw/claims/" + id, label: id }
                    ]);
                }
            }
        });
    }, [id]);

    if (!claim) {
        return null;
    }

    return (
        <div className="mt-4">
            <ClaimDetailV2 claim={claim} admin={auth.admin} />
        </div>
    );
}
