/**
 * ESG Claims Detailed Analysis Component - Advanced Claims Verification Display
 *
 * This React component renders comprehensive detailed analysis of ESG (Environmental, Social, Governance) 
 * claims with sophisticated greenwashing detection, verification status, and supporting evidence. The component
 * provides an in-depth view of individual corporate sustainability claims with AI-powered analysis results.
 *
 * ## Core Functionality
 * - **Accuracy Assessment**: Displays claim verification status with visual indicators for valid/invalid claims
 * - **Greenwashing Detection**: Advanced analysis of potential greenwashing with specific type categorization
 * - **Evidence Integration**: Displays citations, references, and supporting documentation with pagination
 * - **AI-Powered Analysis**: Shows LLM-generated verdict, summary, and confidence scoring
 * - **Citation Management**: Comprehensive citation display with document links and page references
 * - **Admin Controls**: Conditional admin-only features for enhanced claim management
 * - **Responsive Design**: Glass-morphism UI with badge-based status indicators and collapsible sections
 *
 * ## Greenwashing Analysis Types
 * The component supports detection and display of multiple greenwashing patterns:
 * - **Vague**: Claims lacking specific metrics or commitments
 * - **Misleading**: Claims that misrepresent actual environmental impact
 * - **Offsetting**: Claims that rely heavily on carbon offsetting without direct action
 * - **Intentions**: Claims describing future intentions without concrete commitments
 * - **Distracting**: Claims that divert attention from more significant environmental issues
 * - **Organizational/Fund/Product**: Category-specific greenwashing patterns
 *
 * ## Data Flow & System Integration
 * - **Database Source**: Reads from `xfer_claims` table with model JSON data containing analysis results
 * - **Citation System**: Integrates with citation reduction system for duplicate page consolidation
 * - **Verification Override**: Supports manual verification override via `verified` field
 * - **Document References**: Links to source documents with proper page targeting for PDF files
 * - **Analysis Pipeline**: Displays results from backend Python ESG analysis system
 *
 * ## UI Components & Design System
 * - **Badge System**: Status badges for validity, greenwashing, ESG classification, and confidence
 * - **Glass-morphism Cards**: Translucent sections with backdrop blur for modern aesthetic
 * - **Icon Integration**: Lucide React icons (CheckCircle2, AlertCircle, Info) for status visualization
 * - **Responsive Typography**: Adaptive text sizing from base to 2xl for different viewport sizes
 * - **Citation Display**: Formatted references with italic styling and academic citation format
 * - **Markdown Rendering**: EkoMarkdown component for rich text display with citation support
 *
 * ## Props Interface & Component Contract
 * ```typescript
 * interface ClaimDetailV2Props {
 *   claim: ClaimTypeV2;  // Complete claim data with model and metadata
 *   admin: boolean;      // Admin mode flag for enhanced functionality
 * }
 * ```
 *
 * ## Database Schema Integration
 * Integrates with the `xfer_claims` table structure:
 * - **Primary Data**: entity_xid, run_id, statement_id for entity linking
 * - **Verification Status**: verified boolean for manual override capability  
 * - **Performance Fields**: summary, conclusion, statement_text extracted for fast queries
 * - **Model JSON**: Complete analysis results including citations, confidence, greenwashing analysis
 * - **Importance Scoring**: Importance field (0-100) for claim prioritization and filtering
 *
 * ## System Architecture Context
 * This component fits into the broader ESG analysis system:
 * - **Analytics Backend**: Python system generates claim analysis with LLM integration
 * - **Data Sync Layer**: `xfer_claims` table synchronizes data between analytics and customer databases
 * - **Frontend Layer**: This component provides detailed claim analysis view for customer dashboard
 * - **Citation System**: Integrated citation management for academic-style referencing
 * - **Admin Tools**: Enhanced functionality for administrators managing claim verification
 *
 * ## Security & Access Control
 * - **RLS Integration**: Respects Supabase Row Level Security policies on xfer_claims table
 * - **Admin Conditional**: Admin-only features controlled via boolean prop
 * - **Citation Security**: Secure citation links with proper URL validation
 * - **XSS Protection**: All user content properly escaped through React and markdown rendering
 *
 * ## Performance Considerations
 * - **Optimized Data Access**: Uses extracted fields (summary, conclusion) to reduce JSON parsing
 * - **Citation Deduplication**: Implements efficient citation consolidation to reduce redundant displays
 * - **Memoized Components**: EkoMarkdown component uses memoization for expensive markdown processing
 * - **Lazy Loading**: Large analysis content rendered progressively for better user experience
 *
 * @see https://lucide.dev/guide/packages/lucide-react Lucide React Icons Documentation  
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts Next.js App Router
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see {@link ../../../types/claim.ts} ClaimTypeV2 Interface Definition
 * @see {@link ../../../../components/citation.tsx} Citation System Components
 * @see {@link ../../../../components/markdown/eko-markdown.tsx} EkoMarkdown Renderer
 * @see {@link ../../../../components/ui/badge.tsx} Badge Component System
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Advanced ESG claims analysis component with greenwashing detection, verification status, and comprehensive evidence display for corporate sustainability claim assessment.
 * @example ```tsx
 * // Display detailed claim analysis with admin controls
 * <ClaimDetailV2 
 *   claim={claimData} 
 *   admin={isAdmin} 
 * />
 * ```
 * @docgen doc-by-claude  
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { AlertCircle, CheckCircle2, Info } from 'lucide-react'
import { CitationType, CompactCitation, reduceCitations } from '@/components/citation'
import React from 'react'
import { ClaimTypeV2 } from '@/types/claim'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'

const greenwashingTypes: {[key:string]:string} = {
    "vague": "Vague",
    "misleading": "Misleading",
    "offsetting": "Includes Offsetting",
    "intentions": "Describes Intentions",
    "distracting": "Distracting",
    "org": "Possible Greenwashing",
    "fund": "Possible Greenwashing",
    "product": "Possible Greenwashing"
}

function accuracy(valid: boolean, greenwashing: boolean, greenwashingType?: string) {
    if (greenwashing) {
        if (valid) {
            return "Accurate but " + (greenwashingTypes[greenwashingType || ""] || "Greenwashing");
        } else {
            return "Inaccurate and " + (greenwashingTypes[greenwashingType || ""] || "Greenwashing");
        }
    } else {
        return valid ? "Accurate Claim" : "Inaccurate";
    }
}

export default function ClaimDetailV2({claim, admin}: {claim: ClaimTypeV2, admin: boolean}) {
    const model = claim.model;

    // Get all citations from the model
    const citations = model.citations || [];
    // Cast to unknown first to avoid TypeScript errors
    const allCitationsSet = reduceCitations(citations as unknown as CitationType[]);

    // Get the first citation if available for document info
    const firstCitation = citations && citations.length > 0 ? citations[0] : null;

    // Get document info from the model or first citation
    const docAuthors = firstCitation?.authors || [];
    const docTitle = model.claim_doc || firstCitation?.title || '';
    const docYear = model.claim_doc_year || firstCitation?.year || new Date().getFullYear();

    // Determine if the claim is valid and if it's greenwashing
    const isValid = claim.verified !== null ? claim.verified : model.valid_claim;
    const isGreenwashing = model.greenwashing || false;
    // Get greenwashing type from the model if available
    let greenwashingType = '';
    if (typeof model.greenwashing === 'object' && model.greenwashing !== null) {
        // Use type assertion to access the type property
        const gwObj = model.greenwashing as any;
        greenwashingType = gwObj.type || '';
    }

    return <>
        <section>
            <h2 className="text-xl font-semibold mb-3">
                <Badge
                    variant={isValid ? "outline" : "destructive"}
                    className="text-sm"
                >
                    {isValid ? <CheckCircle2 className="w-4 h-4 mr-1"/> : <AlertCircle className="w-4 h-4 mr-1"/>}
                    {accuracy(isValid, isGreenwashing, greenwashingType)}
                </Badge>
            </h2>
            <p className="mb-4 text-lg 2xl:text-xl">{model.statement_text || model.text}</p>
            <p>
                <span className="italic ml-4">
                    {docAuthors.map((author: any) => author.name || author.cn).join(", ")}.
                    ({docYear}) <strong>{docTitle}</strong>
                    {firstCitation?.doc_page_id !== undefined && <>, p.{firstCitation.doc_page_id + 1}</>}
                </span>
            </p>

            <h3 className="font-semibold text-lg mb-2">Verdict</h3>
            <p className="mb-4">{model.conclusion}</p>
        </section>

        <Separator/>

        <section className="text-left w-full mt-6">
            <h2 className="text-xl font-semibold mb-3">Analysis</h2>
            <div className="flex flex-wrap gap-2 mb-4">
                <Badge variant={isValid ? "default" : "destructive"} className="text-sm">
                    {isValid ? <CheckCircle2 className="w-4 h-4 mr-1"/> : <AlertCircle className="w-4 h-4 mr-1"/>}
                    {isValid ? "Valid Claim" : "Invalid Claim"}
                </Badge>
                <Badge variant={isGreenwashing ? "destructive" : "default"} className="text-sm">
                    {isGreenwashing ? <AlertCircle className="w-4 h-4 mr-1"/> : <CheckCircle2 className="w-4 h-4 mr-1"/>}
                    {(isGreenwashing  && isValid) ? "Greenwashing" : "No Greenwashing"}
                </Badge>
                {isGreenwashing && greenwashingType && (
                    <Badge variant="default" className="text-sm">
                        {greenwashingType}
                    </Badge>
                )}
                <Badge variant="default" className="text-sm">
                    <Info className="w-4 h-4 mr-1"/>
                    Confidence: {model.confidence}%
                </Badge>
                <Badge variant={model.esg_claim ? "default" : "outline"} className="text-sm">
                    {model.esg_claim ? "ESG Claim" : "Not ESG Claim"}
                </Badge>
            </div>

            <p className="mb-4"><EkoMarkdown citations={citations as unknown as CitationType[]}
                                             admin={admin}>{model.summary}</EkoMarkdown></p>
            <EkoMarkdown
                citations={citations as unknown as CitationType[]}
                admin={admin}
            >
                {model.verdict?.replaceAll(/\\n/g, "\n\n")}
            </EkoMarkdown>
        </section>

        <Separator/>

        <section className="text-left w-full mt-6">
            <h2 className="text-xl font-semibold mb-3">References</h2>
            {allCitationsSet
                .sort((a, b) => (b.score || 0) - (a.score || 0))
                .map((data, j) => (
                    <CompactCitation key={j} data={data} admin={admin}/>
                ))}
        </section>

        <Separator/>
    </>;
}
