/**
 * ESG Claims Timeline Component - Chronological Claims Visualization
 *
 * This React component renders ESG (Environmental, Social, Governance) claims in a vertical
 * timeline format, providing users with a chronological overview of corporate sustainability
 * statements and their verification status. The component serves as a compact visualization
 * tool for understanding the temporal distribution of claims within the customer dashboard.
 *
 * ## Core Functionality
 * - **Chronological Display**: Claims organized by year in reverse chronological order (most recent first)
 * - **Visual Timeline**: Vertical timeline with connecting lines and year markers
 * - **Status Indicators**: Color-coded icons showing claim verification status (accurate/inaccurate)
 * - **Greenwashing Detection**: Visual badges for claims identified as potential greenwashing
 * - **Claim Preview**: First 3 claims per year with truncated text for quick scanning
 * - **Count Summaries**: Badge indicators showing number of claims per year
 * - **Empty State**: Graceful handling when no claims data is available
 *
 * ## Timeline Visualization Design
 * - **Vertical Layout**: Time flows from top (most recent) to bottom (oldest)
 * - **Timeline Spine**: Continuous vertical line connecting all timeline entries
 * - **Year Nodes**: Circular markers for each year with primary color accent
 * - **Claim Previews**: Up to 3 claims displayed per year with status indicators
 * - **Overflow Handling**: "+N more claims" indicator when more than 3 claims exist per year
 * - **Responsive Design**: Adapts spacing and layout for different screen sizes
 *
 * ## Verification & Status System
 * The component supports comprehensive claim verification with visual indicators:
 * - **Manual Verification Override**: `verified` field takes precedence over automated analysis
 * - **Automated Analysis**: Falls back to `model.valid_claim` for AI-generated assessments
 * - **Accurate Claims**: Green CheckCircle2 icon for verified/valid claims
 * - **Inaccurate Claims**: Red CircleXIcon for disputed/invalid claims
 * - **Confidence Levels**: Percentage badges showing AI analysis confidence (0-100%)
 * - **ESG Classification**: Special badges for claims identified as ESG-specific
 * - **Greenwashing Flags**: Destructive-styled badges for potential greenwashing
 *
 * ## Data Processing & Sorting
 * - **Year Extraction**: Uses `claim_doc_year` from model, fallback to current year
 * - **Chronological Sorting**: Claims sorted by year in descending order (newest first)
 * - **Year Grouping**: Claims aggregated into year buckets for timeline display
 * - **Text Prioritization**: Uses `statement_text` → `model.text` → `summary` hierarchy
 * - **Performance Optimization**: Limits display to first 3 claims per year for viewport performance
 *
 * ## UI Components & Design System
 * - **Glass-morphism Container**: Rounded background container with subtle styling
 * - **Badge System**: Comprehensive status indicators using shadcn/ui Badge components
 * - **Icon Integration**: Lucide React icons (CheckCircle2, CircleXIcon, Calendar) for visual clarity
 * - **Typography**: Semantic heading hierarchy with responsive text sizing
 * - **Color Coding**: Consistent semantic colors (green=accurate, red=inaccurate, destructive=greenwashing)
 * - **Spacing System**: Consistent gap-based spacing for visual hierarchy
 *
 * ## Props Interface & Component Contract
 * ```typescript
 * interface ClaimsTimelineProps {
 *   claimsData: ClaimTypeV2[] | undefined | null;  // Claims data array from database
 * }
 * ```
 *
 * ## Database Integration
 * Integrates with the `xfer_claims` table structure:
 * - **Performance Fields**: Uses extracted `statement_text`, `summary` fields for fast rendering
 * - **Verification Status**: `verified` boolean field for manual verification override
 * - **Model Data**: Complete `XferClaimModel` JSON containing analysis results
 * - **Year Information**: `claim_doc_year` field for temporal organization
 * - **Confidence Metrics**: AI confidence scores for claim accuracy assessment
 * - **Entity Context**: Claims filtered by entity context for relevant display
 *
 * ## System Architecture Context
 * This component fits into the broader ESG analysis ecosystem:
 * - **Analytics Backend**: Python system generates temporal claim analysis
 * - **Data Sync Layer**: `xfer_claims` table provides optimized timeline data
 * - **Frontend Integration**: Embedded in dashboard layouts for compact claim overview
 * - **Navigation Context**: Complements detailed claim list views and individual claim pages
 * - **Performance Focus**: Optimized for quick visual scanning rather than detailed analysis
 *
 * ## Accessibility & User Experience
 * - **Test IDs**: Comprehensive `data-testid` attributes for automated testing
 * - **Semantic HTML**: Proper heading hierarchy and structural elements
 * - **Visual Hierarchy**: Clear distinction between years, claims, and metadata
 * - **Responsive Design**: Timeline adapts to different container sizes
 * - **Empty State**: Graceful null return when no data is available (parent handles display)
 * - **Screen Reader**: Semantic structure supports assistive technology
 *
 * ## Performance Considerations
 * - **Limited Display**: Shows maximum 3 claims per year to prevent viewport overflow
 * - **Efficient Sorting**: Single-pass sorting and grouping operations
 * - **Text Truncation**: Uses CSS `line-clamp-2` for consistent text preview lengths
 * - **Conditional Rendering**: Early return for empty states to prevent unnecessary processing
 * - **Badge Optimization**: Conditional badge rendering based on data availability
 *
 * ## Data Flow & State Management
 * - **Props-Based**: Receives claims data from parent components, no internal state
 * - **Computed Properties**: Year grouping and sorting performed on each render
 * - **Fallback Logic**: Multiple fallback strategies for missing data fields
 * - **Immutable Operations**: Creates new sorted arrays without mutating input data
 * - **Deterministic Display**: Consistent rendering based on input data structure
 *
 * ## Visual Design Elements
 * - **Timeline Spine**: Vertical gray line (0.5px width) connecting all timeline entries
 * - **Year Markers**: White/dark circular nodes with primary border (4x4 size)
 * - **Claim Previews**: Left-bordered claim containers with status icons
 * - **Badge Styling**: Consistent badge sizing and color schemes across all indicators
 * - **Spacing**: 6-unit spacing between timeline entries, 2-unit spacing for claim previews
 * - **Dark Mode Support**: Full support for dark theme color schemes
 *
 * @see https://lucide.dev/icons/check-circle-2 CheckCircle2 Icon Documentation
 * @see https://lucide.dev/icons/circle-x CircleXIcon Documentation  
 * @see https://lucide.dev/icons/calendar Calendar Icon Documentation
 * @see https://ui.shadcn.com/docs/components/badge Badge Component Documentation
 * @see {@link ../../../../types/claim.ts} ClaimTypeV2 Interface Definition
 * @see {@link claims-list-v2.tsx} Detailed Claims List Component
 * @see {@link ../../../../components/ui/badge.tsx} Badge Component Implementation
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Chronological timeline visualization for ESG claims with year-based grouping, verification status indicators, and compact claim previews for dashboard integration.
 * @example ```tsx
 * // Display claims timeline in dashboard
 * <ClaimsTimeline 
 *   claimsData={entityClaims} 
 * />
 * 
 * // Timeline with mixed verification statuses
 * <ClaimsTimeline 
 *   claimsData={[
 *     {
 *       id: 1, 
 *       verified: true, 
 *       model: { claim_doc_year: 2024, valid_claim: true, confidence: 95 },
 *       statement_text: "We will achieve net zero by 2030"
 *     },
 *     {
 *       id: 2, 
 *       verified: null, 
 *       model: { claim_doc_year: 2023, valid_claim: false, greenwashing: true },
 *       summary: "Sustainable packaging initiative"
 *     }
 *   ]} 
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client';

import React from 'react';
import { ClaimTypeV2 } from '@/types/claim';
import { Badge } from '@/components/ui/badge';
import { CheckCircle2, CircleXIcon, HelpCircle, Calendar } from 'lucide-react';

interface ClaimsTimelineProps {
    claimsData: ClaimTypeV2[] | undefined | null;
}

export function ClaimsTimeline({ claimsData }: ClaimsTimelineProps) {
    if (!claimsData || claimsData.length === 0) {
        return null;
    }

    // Sort claims by year (most recent first)
    const sortedClaims = [...claimsData].sort((a, b) => {
        const yearA = a.model.claim_doc_year || new Date().getFullYear();
        const yearB = b.model.claim_doc_year || new Date().getFullYear();
        return yearB - yearA;
    });

    // Group claims by year
    const claimsByYear = sortedClaims.reduce((acc, claim) => {
        const year = claim.model.claim_doc_year || new Date().getFullYear();
        if (!acc[year]) {
            acc[year] = [];
        }
        acc[year].push(claim);
        return acc;
    }, {} as Record<number, ClaimTypeV2[]>);

    return (
        <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="claims-timeline">
            <h3 className="text-lg font-semibold mb-4">Claims Timeline</h3>
            
            <div className="relative">
                {/* Vertical timeline line */}
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"></div>
                
                {/* Timeline entries */}
                <div className="space-y-6">
                    {Object.entries(claimsByYear)
                        .sort(([yearA], [yearB]) => Number(yearB) - Number(yearA))
                        .map(([year, claims]) => (
                            <div key={year} className="relative" data-testid="timeline-entry">
                                {/* Timeline dot */}
                                <div className="absolute left-2 w-4 h-4 bg-white dark:bg-gray-800 border-2 border-primary rounded-full"></div>
                                
                                {/* Year and summary */}
                                <div className="ml-12">
                                    <div className="flex items-center gap-3 mb-2">
                                        <h4 className="font-semibold text-lg">{year}</h4>
                                        <Badge variant="outline" className="text-xs">
                                            <Calendar className="w-3 h-3 mr-1" />
                                            {claims.length} claim{claims.length !== 1 ? 's' : ''}
                                        </Badge>
                                    </div>
                                    
                                    {/* Show first few claims for this year */}
                                    <div className="space-y-2">
                                        {claims.slice(0, 3).map((claim) => {
                                            // Determine claim status
                                            const isValid = claim.verified !== null ? claim.verified : claim.model.valid_claim;
                                            const isGreenwashing = claim.model.greenwashing || false;
                                            
                                            let statusIcon;
                                            let statusColor;
                                            
                                            if (isValid) {
                                                statusIcon = <CheckCircle2 className="w-4 h-4" />;
                                                statusColor = "text-green-600 dark:text-green-400";
                                            } else {
                                                statusIcon = <CircleXIcon className="w-4 h-4" />;
                                                statusColor = "text-red-600 dark:text-red-400";
                                            }
                                            
                                            return (
                                                <div key={claim.id} className="text-sm pl-2 border-l-2 border-gray-200 dark:border-gray-700">
                                                    <div className="flex items-start gap-2" data-testid="timeline-event">
                                                        <span className={statusColor}>{statusIcon}</span>
                                                        <div className="flex-1">
                                                            <span className="line-clamp-2">
                                                                {claim.statement_text || claim.model.text || claim.summary}
                                                            </span>
                                                            <div className="flex items-center gap-2 mt-1">
                                                                <Badge variant="secondary" className="text-xs">
                                                                    {claim.model.confidence}%
                                                                </Badge>
                                                                {claim.model.esg_claim && (
                                                                    <Badge variant="outline" className="text-xs">
                                                                        ESG
                                                                    </Badge>
                                                                )}
                                                                {isGreenwashing && (
                                                                    <Badge variant="destructive" className="text-xs">
                                                                        Greenwashing
                                                                    </Badge>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                        
                                        {claims.length > 3 && (
                                            <div className="text-sm text-muted-foreground pl-2">
                                                +{claims.length - 3} more claim{claims.length - 3 > 1 ? 's' : ''}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                </div>
            </div>
        </div>
    );
}
