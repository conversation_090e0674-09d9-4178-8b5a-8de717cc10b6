/**
 * Vague Term Card React Component - ESG Environmental Language Analysis Display
 *
 * This React component renders individual vague environmental terms identified in corporate ESG 
 * (Environmental, Social, Governance) communications as detailed cards showing vagueness scores,
 * analysis, explanations, and supporting documentation citations. Each card represents a potentially
 * misleading or unsubstantiated environmental term that has been analyzed for greenwashing indicators
 * with detailed AI-generated assessments to help users understand language imprecision in corporate communications.
 *
 * ## Core Functionality
 * - **Vagueness Score Visualization**: Clear numerical rating (0-100) converted to clarity percentage with color-coded badges
 * - **Term Analysis Display**: Comprehensive vague term evaluation including summary and detailed analysis with markdown support
 * - **Greenwashing Detection**: AI-powered analysis highlighting potentially misleading environmental language patterns
 * - **Citation Integration**: Source document references with clickable links and comprehensive citation management
 * - **Administrative Controls**: Delete functionality for administrators with proper permissions and confirmation dialogs
 * - **Interactive Design**: Glass-morphism cards with hover effects and responsive layout following design system
 *
 * ## Vague Term Assessment Data Structure
 * - **Phrase Text**: The identified vague environmental term or phrase from corporate documents
 * - **Vagueness Score**: Numerical score (0-100) where higher scores indicate more problematic vague language
 * - **Analysis Content**: AI-generated detailed assessment of why the term is considered vague or misleading
 * - **Summary Text**: Concise overview of the vagueness concerns and implications for stakeholders
 * - **Citation References**: Document sources with authors, publication details, and page-specific verification
 * - **Model Metadata**: Additional scoring metrics and confidence indicators from AI analysis
 *
 * ## Visual Design & Score Interpretation
 * - **Rating Badge System**: Color-coded badges showing clarity ratings as inverted percentages
 *   - Red (destructive): High vagueness scores (>=75) indicating serious greenwashing concerns
 *   - Gray (default): Moderate vagueness scores (<75) for terms requiring attention but less critical
 * - **Card Layout**: Glass-morphism design with rounded corners, backdrop blur, and translucent surfaces
 * - **Typography Hierarchy**: Clear phrase titles, descriptive summaries, and structured analysis content
 * - **Admin Integration**: Hover-revealed delete controls positioned absolutely in top-right corner
 *
 * ## Analysis Processing & Display
 * - **Markdown Support**: Uses EkoMarkdown component for rich formatting of detailed analysis content
 * - **Citation Management**: Handles CitationType objects with proper URL linking and document references
 * - **Score Conversion**: Transforms vagueness scores (0-100) to clarity ratings (100-0) for user-friendly display
 * - **Admin Permissions**: Conditional rendering of administrative controls based on user authentication
 * - **Content Structure**: Organized display of phrase, summary, and analysis in hierarchical card sections
 *
 * ## Database Integration
 * The component connects to the customer Supabase database via:
 * - **_deprecated_xfer_gw_vague_v2 Table**: Core vague term data with id, entity_xid, run_id, phrase
 * - **Model JSONB Field**: Contains XferVagueModel with score, explanation, analysis, summary, citations
 * - **Row Level Security**: Protected by Supabase RLS policies ensuring proper access controls
 * - **Citation Embedding**: CitationType objects stored within model for document traceability
 * - **Admin Deletion**: Supports administrative deletion through secure database operations
 *
 * ## Component Architecture
 * - **Props Interface**: Accepts VagueType item and admin boolean for comprehensive permission control
 * - **Model Access**: Direct access to nested model properties for score, summary, analysis, and citations
 * - **Conditional Controls**: Admin delete button rendered only for authorized users with proper permissions
 * - **Testing Support**: Includes structured markup and class names for comprehensive Playwright testing
 * - **Citation Integration**: Passes citation data to EkoMarkdown for proper document reference handling
 *
 * ## System Integration Context
 * This component fits into the broader ESG vague language detection system:
 * - **Analytics Backend**: Python backend analyzes corporate documents and identifies vague environmental terminology
 * - **Greenwashing Analysis**: Part of comprehensive greenwashing detection pipeline analyzing language patterns
 * - **Data Synchronization**: _deprecated_xfer_gw_vague_v2 table syncs processed data from analytics database
 * - **Dashboard Integration**: Component renders within vague terms management and analysis interface
 * - **Modal Displays**: Used in both list views and detailed modal overlays for comprehensive term analysis
 * - **Citation Pipeline**: Connects to document analysis and source verification systems for traceability
 *
 * ## Performance Considerations
 * - **Direct Model Access**: Efficient access to nested model properties without excessive data processing
 * - **Conditional Rendering**: Avoids unnecessary DOM operations for admin controls when user lacks permissions
 * - **Markdown Optimization**: EkoMarkdown component handles complex analysis formatting with proper caching
 * - **Citation Processing**: Efficient handling of embedded citation objects without redundant API calls
 * - **Score Calculation**: Simple arithmetic operations for score conversion with minimal computational overhead
 *
 * ## Security & Administrative Features
 * - **Permission-Based Controls**: Admin delete functionality restricted through authentication context validation
 * - **Database Security**: Operations protected by Supabase Row Level Security policies and admin-only deletion policies
 * - **Confirmation Workflows**: Administrative actions require explicit confirmation through AdminDeleteButton component
 * - **Error Handling**: Proper error handling and user feedback for failed administrative operations
 * - **Audit Trail**: Administrative actions logged through database triggers and application monitoring
 *
 * @see https://react.dev/reference/react React Documentation
 * @see https://ui.shadcn.com/docs/components/card shadcn/ui Card Component Documentation  
 * @see https://ui.shadcn.com/docs/components/badge shadcn/ui Badge Component Documentation
 * @see {@link ../../../../../components/markdown/eko-markdown.tsx} EkoMarkdown Component
 * @see {@link ../../../../../components/admin/AdminDeleteButton.tsx} AdminDeleteButton Component
 * @see {@link ../../../../../types/vague.ts} VagueType and XferVagueModel Interfaces
 * @see {@link ./page.tsx} Vague Terms Dashboard Page
 * <AUTHOR>
 * @updated 2025-07-22
 * @description React component for displaying individual vague environmental terms with greenwashing analysis, scores, and administrative controls
 * @example
 * ```tsx
 * // Component is typically rendered within VagueTermsList
 * <VagueTerm 
 *   item={{
 *     id: 123,
 *     phrase: "sustainable practices",
 *     model: {
 *       score: 85,
 *       summary: "Vague term lacking specific metrics",
 *       analysis: "This phrase provides no measurable criteria...",
 *       citations: [...]
 *     }
 *   }}
 *   admin={true}
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'
import { VagueType } from '@/types'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CitationType } from '@/components/citation'
import React from 'react'
import { AdminDeleteButton } from '@/components/admin'

export function VagueTerm(props: { item: VagueType, admin: boolean }) {
    return <Card className="w-full mt-4 relative group">
        <AdminDeleteButton
          tableName="_deprecated_xfer_gw_vague_v2"
            recordId={props.item.id}
            recordType="vague term"
        />
        <CardHeader>
            <div className="flex justify-between items-center">
                <CardTitle className="text-xl">{props.item.phrase}</CardTitle>
                <Badge
                    variant={props.item.model.score! >= 75 ? "destructive" : "default"}>
                    Rating {100 - props.item.model.score!}%
                </Badge>
            </div>
            <CardDescription>{props.item.model.summary}</CardDescription>
        </CardHeader>
        <CardContent>
            <EkoMarkdown
                citations={props.item.model.citations as CitationType[]}
                admin={props.admin}>{props.item.model.analysis}</EkoMarkdown>
        </CardContent>
    </Card>;
}
