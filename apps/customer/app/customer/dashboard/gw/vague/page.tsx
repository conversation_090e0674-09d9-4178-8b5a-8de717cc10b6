/**
 * Next.js Client-Side ESG Vague Terms Analysis Dashboard - Greenwashing Detection Interface
 *
 * This page provides a comprehensive dashboard for viewing and analyzing vague environmental terminology
 * used in corporate ESG communications. The interface displays identified vague terms with AI-powered
 * analysis to detect potential greenwashing through imprecise language, inadequate substantiation,
 * and missing verification mechanisms in corporate sustainability claims.
 *
 * ## Core Functionality
 * - **Vague Terms Detection**: Displays vague environmental terminology identified from corporate communications
 * - **Greenwashing Analysis**: AI-powered analysis of potentially misleading or unsubstantiated environmental claims
 * - **Interactive Term List**: Clickable vague terms with detailed analysis cards showing scores and explanations
 * - **Summary Generation**: AI-generated comprehensive analysis report following standardized format with ~2000 word summaries
 * - **Citation Integration**: Preserves document citations and source references for verification and traceability
 * - **Admin Controls**: Administrative functions for term deletion and management (admin users only)
 *
 * ## Data Processing & State Management
 * - **Vague Terms Data**: Fetched from EntityContext which queries `_deprecated_xfer_gw_vague_v2` table via Supabase RLS policies
 * - **Dual Data Sources**: Summary data (`vagueData`) and detailed term list (`vagueDetailData`) for comprehensive analysis
 * - **Loading States**: Graceful loading indicators while vague terms data is being fetched from analytics backend
 * - **Navigation Integration**: Breadcrumb updates through navigation context and URL parameter preservation
 * - **Entity Context Integration**: Uses entity hash for consistent summary caching and parameter management
 *
 * ## User Interface Components
 * - **Glass-Morphism Design**: Translucent, rounded UI elements consistent with application design system
 * - **Summary Section**: AI-generated comprehensive analysis using Summarize component with cached results
 * - **Details Section**: Interactive list of individual vague terms with scores, explanations, and citations
 * - **VagueTermsList Component**: Renders clickable vague term cards with hover effects and admin controls
 * - **NoData Fallback**: User-friendly empty state when no vague terms are found for selected entity
 *
 * ## AI Analysis Integration
 * - **Summarize Component**: Uses `/api/summarize` endpoint for AI-powered content generation with caching
 * - **Analysis Format**: Follows standardized format based on example template for consistency across entities
 * - **Content Guidelines**: ~2000 word summaries without recommendations, focusing on analysis and findings
 * - **Citation Preservation**: Maintains document citations from source materials throughout analysis
 * - **Version Control**: Uses versioned hash IDs for summary caching and consistency management
 *
 * ## System Architecture
 * This page fits into the broader ESG greenwashing detection system:
 * - **Analytics Backend**: Python system analyzes corporate communications and generates vague term flags
 * - **Data Sync Layer**: `_deprecated_xfer_gw_vague_v2` table synchronizes data from analytics database
 * - **API Layer**: Summary generation through `/api/summarize` with AI-powered analysis
 * - **Frontend**: This dashboard provides customer-facing access to vague terms analysis and reporting
 * - **Navigation System**: Integrated with entity context and navigation breadcrumbs for seamless UX
 *
 * ## Security & Performance
 * - **Row Level Security**: Database access through Supabase RLS policies ensuring proper data access controls
 * - **Admin Controls**: Administrative deletion functionality restricted to admin users via authentication context
 * - **Caching Strategy**: Summary results cached using localStorage with hash-based invalidation
 * - **Loading Optimization**: Conditional rendering and loading states for improved user experience
 * - **Error Handling**: Graceful fallbacks for missing data and failed API requests
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages Next.js App Router Pages
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://react.dev/reference/react/useEffect React useEffect Hook Documentation
 * @see {@link ./vague-terms-list.tsx} VagueTermsList Component
 * @see {@link ../../../../components/summarize.tsx} Summarize Component
 * @see {@link ../../../../components/context/entity/entity-context.tsx} Entity Context Provider
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Client-side dashboard for ESG vague terms analysis and greenwashing detection with AI-powered summaries
 * @example
 * ```tsx
 * // This page is automatically rendered at /customer/dashboard/gw/vague
 * // It requires entity context to be available and authentication
 * // Example usage: /customer/dashboard/gw/vague?entity=ABC123&run=latest
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";
import React, { useEffect } from 'react'
import { VagueTermsList } from './vague-terms-list'
import NoData from '@/components/no-data'
import { useAuth } from '@/components/context/auth/auth-context'
import { useEntity } from '@/components/context/entity/entity-context'
import { useNav } from '@/components/context/nav/nav-context'
import { Summarize } from '@/components/summarize'
import { useNavigationWithParams } from '@/hooks/use-navigation-with-params'

const example = `
# Analysis of Environmental Terms Usage in [Example Company] Corporate Communications
## Overview of Key Findings
A detailed examination of [Example Company]'s corporate communications reveals consistent patterns in the use of vague environmental terminology across multiple reports and statements. The analysis identified significant concerns with the company's usage of 14 key environmental terms, highlighting potential greenwashing through imprecise language and inadequate substantiation.

## Primary Areas of Concern
### Clean Energy and Environmental Claims
In "How we're working towards 100% renewable energy by 2030", [Example Company] uses terms like 'clean energy' without providing specific definitions or metrics. The company discusses various energy initiatives but fails to establish clear criteria for what constitutes "clean" energy or how it's verified.

### Manufacturing and Innovation Claims
The term "sustainable manufacturing" appears in [Example Company]'s Modern Slavery Statement 2023, primarily focusing on social governance rather than comprehensive sustainability measures. Similarly, their use of "responsible innovation" in the "[Example Company] Sustainability Report" lacks clear definition and verification mechanisms.

### Product and Material Claims
In their product communications, terms like 'biodegradable' and 'naturally derived' are used without specific certifications or standards. For instance, in the "Purpose-led, future-fit [Example Company] Integrated Annual Report 2021-22", claims about "energy efficient" equipment lack quantifiable metrics or industry standard references.

### Climate and Environmental Impact
The company's "Climate Transition Action Plan" acknowledges previous use of terms like 'climate neutral' in consumer-facing claims, noting a shift away from such terminology. This suggests growing awareness of the problematic nature of vague environmental claims.

## Systematic Issues Identified
### Lack of Verification

 * Consistent absence of third-party certification
 * Limited independent verification of claims
 * Insufficient documentation of environmental benefits

### Metric Deficiency

 * Few quantifiable measurements
 * Absence of specific performance indicators
 * Limited comparative data against industry standards

### Definition Gaps

 * Terms used without clear definitions
 * Ambiguous application of environmental terminology
 * Inconsistent usage across different communications


## Notable Improvements

In their recent communications, [Example Company] shows signs of recognizing these issues. The "[Example Company] Annual Report on Form 20-F 2021" indicates a shift toward more specific environmental claims and metrics, though significant room for improvement remains.

##Conclusion
The analysis suggests that while [Example Company] is making efforts to improve its environmental communications, significant work remains to avoid potential greenwashing through vague terminology. The company's recent acknowledgment of these issues and shifts in policy indicate awareness of the need for more precise and verifiable environmental claims.
`;

export default function Page() {
    const auth = useAuth();
    const entityContext = useEntity();
    const nav = useNav();
    const navWithParams = useNavigationWithParams();

    useEffect(() => {
        nav.changeNavPath(navWithParams.createNavItems([
            { label: "Dashboard", href: "/customer/dashboard" },
            { label: "Vague Terms", href: "/customer/dashboard/gw/vague" }
        ]));
    }, [navWithParams.queryString]);

    // Show loading state if vague data is still loading
    if (entityContext.isLoadingVague) {
        return (
            <div className="prose-container">
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg">Loading vague terms...</div>
                </div>
            </div>
        );
    }

    // Check if we have vague data from EntityContext
    const vague = entityContext.vagueData;
    const detail = entityContext.vagueDetailData || [];

    if (vague === null || detail.length === 0) {
        return <NoData title="No Vague Terms" description="No vague terms found for this entity" />;
    }


    return (<div className="prose-container">
        <div>
            <div className="text-2xl font-bold my-4 ">Summary</div>
            <Summarize hashId={"v1.1:gw-vague-summary-long-" + entityContext.hash()}
                       preamble={"Please keep the summary to about 2000 words. Do not include recommendations. An example for [Example Company] is shown here, use this example as a guide to the format and style required: <example>"+example+"</example>\n\n"}
                       obj={{
                           claims: detail,
                           summary: vague ? vague.model.summary : "",
                           version:"1.0"
                       }}/>
        </div>
        <div>
            <div className="text-2xl font-bold my-4 ">Details</div>
            <VagueTermsList vagueDetailData={detail!} admin={auth.admin}/>
        </div>

    </div>)
        ;
}
