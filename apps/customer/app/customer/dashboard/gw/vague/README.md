# Vague Language Analysis Module

## Overview

The Vague Language Analysis module is a comprehensive ESG (Environmental, Social, Governance) greenwashing detection
system that identifies and analyzes ambiguous environmental terminology in corporate sustainability communications. This
module leverages advanced NLP and AI models to detect potentially misleading or unsubstantiated environmental language
that may indicate greenwashing attempts or inadequate transparency in ESG reporting.

The system provides a customer-facing dashboard interface for viewing and analyzing vague environmental terms with
AI-powered analysis, interactive term lists, and comprehensive summary generation capabilities.

## Specification

### Core Requirements

1. **Vague Language Detection**: Identify and score vague environmental terminology from corporate documents using
   AI-powered analysis
2. **Greenwashing Assessment**: Evaluate potentially misleading environmental claims through language precision analysis
3. **Interactive Dashboard**: Provide user-friendly interface for browsing, analyzing, and managing vague terms
4. **Citation Integration**: Maintain full traceability to source documents with page-level precision
5. **Administrative Controls**: Enable authorized users to manage and curate vague term data
6. **Summary Generation**: Provide AI-generated comprehensive analysis reports with standardized formatting

### Functional Specifications

- **Scoring Algorithm**: 0-100 scale where higher scores indicate more problematic vague language
- **Analysis Coverage**: Support for multiple vague language categories (quantitative ambiguity, temporal vagueness,
  comparative ambiguity, etc.)
- **Real-time Navigation**: Seamless navigation between list views and detailed term analysis
- **Citation Management**: Integration with document citation system for verification and audit trails
- **Responsive Design**: Glass-morphism UI design system with mobile-first responsive layout

## Key Components

### Page Components

#### [`page.tsx`](./page.tsx)

- **Role**: Main dashboard page for vague terms analysis
- **Features**:
    - Summary section with AI-generated ~2000 word analysis
    - Details section showing interactive list of vague terms
    - Loading states and error handling
    - Breadcrumb navigation integration
- **Data Sources**: EntityContext for vague terms data and summaries

#### [`[id]/page.tsx`](./%5Bid%5D/page.tsx)

- **Role**: Dynamic route for individual vague term detail views
- **Features**:
    - Single vague term detailed analysis
    - Database queries for specific term data
    - Navigation breadcrumb updates
    - Admin controls integration

### UI Components

#### [`vague-term.tsx`](./vague-term.tsx)

- **Role**: Individual vague term card component
- **Features**:
    - Vagueness score visualization with color-coded badges
    - Comprehensive term analysis display with markdown support
    - Citation integration for source document references
    - Administrative delete functionality
    - Glass-morphism card design with hover effects

#### [`vague-terms-list.tsx`](./vague-terms-list.tsx)

- **Role**: Interactive list view of all vague terms
- **Features**:
    - Clickable term cards with navigation
    - Score badges and context display
    - Admin controls for term management
    - Empty state handling
    - Query parameter preservation for navigation

## Dependencies

### External Dependencies

- **Next.js 15**: App Router framework for page routing and client-side navigation
- **React 18+**: Core UI framework with hooks and functional components
- **shadcn/ui**: UI component library for consistent design system (Cards, Badges)
- **Lucide React**: Icon library for UI indicators and visual elements
- **Supabase**: Database client for real-time data access and Row Level Security

### Internal Dependencies

- **Entity Context**: Provides vague terms data and entity-specific parameters
- **Auth Context**: User authentication and admin permission management
- **Navigation Context**: Breadcrumb management and page title updates
- **EkoMarkdown**: Custom markdown renderer with citation support
- **AdminDeleteButton**: Specialized administrative controls with security integration
- **Citation System**: Document reference and traceability infrastructure

### Data Dependencies

- **Customer Database**: `_deprecated_xfer_gw_vague_v2` table with vague terms data
- **Analytics Pipeline**: Python backend system for vague language processing
- **Citation System**: Document citation management for source attribution

## Usage Examples

### Basic Vague Terms Dashboard

```typescript
// Accessing the main vague terms dashboard
// URL: /customer/dashboard/gw/vague?entity=MSFT_001&run=latest

// The page automatically displays:
// 1. AI-generated summary of vague language usage
// 2. Interactive list of individual vague terms
// 3. Navigation breadcrumbs and page title updates
```

### Individual Term Analysis

```typescript
// Navigating to specific vague term details
// URL: /customer/dashboard/gw/vague/12345?entity=MSFT_001&run=latest

// Displays detailed analysis including:
// - Vagueness score and rating badge
// - Comprehensive explanation and analysis
// - Source document citations
// - Admin controls (if authorized)
```

### Administrative Operations

```typescript
// Admin users can delete vague terms
<AdminDeleteButton
  tableName="_deprecated_xfer_gw_vague_v2"
  recordId={item.id}
  recordType="vague term"
/>
```

## Architecture Notes

### Data Flow Architecture

```mermaid
flowchart TD
    A[Corporate Documents] --> B[Python NLP Pipeline]
    B --> C[Analytics Database]
    C --> D[Data Sync Process]
    D --> E[Customer Database]
    E --> F[EntityContext Provider]
    F --> G[Vague Terms Dashboard]
    G --> H[Individual Term Analysis]
    
    subgraph "Frontend Components"
        G --> I[VagueTermsList]
        H --> J[VagueTerm]
        I --> K[EkoMarkdown]
        J --> K
    end
    
    subgraph "Data Sources"
        E --> L[_deprecated_xfer_gw_vague_v2]
        L --> M[JSONB Model Field]
    end
```

### Component Hierarchy

```mermaid
classDiagram
    class VaguePage {
        +summary: Summarize
        +details: VagueTermsList
        +navigation: NavContext
    }
    
    class VagueTermsList {
        +items: VagueType[]
        +admin: boolean
        +navigation: router.push()
    }
    
    class VagueTerm {
        +item: VagueType
        +admin: boolean
        +markdown: EkoMarkdown
        +citations: CitationType[]
    }
    
    class AdminDeleteButton {
        +tableName: string
        +recordId: number
        +recordType: string
    }
    
    VaguePage --> VagueTermsList
    VagueTermsList --> VagueTerm
    VagueTerm --> AdminDeleteButton
    VagueTerm --> EkoMarkdown
```

### State Management Flow

```mermaid
sequenceDiagram
    participant U as User
    participant P as Page Component
    participant E as EntityContext
    participant S as Supabase
    participant N as NavContext
    
    U->>P: Navigate to vague terms
    P->>E: Request vague data
    E->>S: Query _deprecated_xfer_gw_vague_v2
    S-->>E: Return vague terms data
    E-->>P: Provide vagueData & vagueDetailData
    P->>N: Update breadcrumbs
    P->>U: Render dashboard with data
    
    U->>P: Click vague term
    P->>N: Navigate with query params
    N->>P: Load term detail page
    P->>S: Query specific term by ID
    S-->>P: Return term details
    P->>U: Display detailed analysis
```

## Known Issues

### Current Issues from Linear

- **EKO-67** ✅ **RESOLVED**: "Vague Terms" icon removed from dashboard - Analysis now properly associated with specific
  analyzed documents
- **Deprecated Table Usage**: Module uses `_deprecated_xfer_gw_vague_v2` table which may need migration to newer schema
  in future releases

### Technical Debt

1. **Table Schema**: The `_deprecated_xfer_gw_vague_v2` table naming suggests future migration needed
2. **Loading States**: Limited error handling for network failures during data fetching
3. **Performance**: Large datasets may impact rendering performance without pagination
4. **Caching**: Summary generation could benefit from improved caching strategies

### Code Quality Notes

- **Type Safety**: Comprehensive TypeScript coverage with proper interface definitions
- **Security**: Row Level Security policies protect data access
- **Testing**: Components need comprehensive Playwright test coverage
- **Documentation**: All components have detailed JSDoc documentation

## Future Work

### Planned Enhancements

1. **Schema Migration**: Migrate from deprecated table to new optimized schema
2. **Performance Optimization**: Implement pagination and virtual scrolling for large datasets
3. **Enhanced Analytics**: Add trending analysis and comparative scoring features
4. **Export Capabilities**: PDF and CSV export functionality for vague terms analysis
5. **Real-time Updates**: WebSocket integration for live data updates

### Integration Roadmap

1. **Enhanced Citation System**: Deeper integration with document analysis pipeline
2. **Advanced Filtering**: Filters by score ranges, document types, and date ranges
3. **Collaborative Features**: User annotations and collaborative analysis tools
4. **API Expansion**: RESTful API endpoints for external integrations

### Compliance & Regulatory

- **GDPR Compliance**: Enhanced data privacy controls for vague language analysis
- **Audit Trail**: Comprehensive logging for regulatory compliance requirements
- **Data Retention**: Automated data lifecycle management policies

## Troubleshooting

### Common Issues

**Q: Vague terms not loading?**
A: Check EntityContext initialization and verify entity/run parameters in URL

**Q: Summary generation failing?**
A: Verify `/api/summarize` endpoint availability and check localStorage cache limits

**Q: Admin controls not appearing?**
A: Confirm user has admin permissions in AuthContext and proper authentication

**Q: Navigation breadcrumbs not updating?**
A: Ensure NavContext is properly initialized and navigation parameters are preserved

### Performance Issues

**Slow loading with large datasets:**

- Implement pagination in VagueTermsList component
- Add virtual scrolling for better performance
- Consider server-side filtering

**Memory usage concerns:**

- Monitor EntityContext data caching
- Implement proper component cleanup
- Review citation data size and optimization

### Development Issues

**TypeScript errors:**

- Ensure proper VagueType interface usage
- Verify CitationType integration
- Check database type casting in [id]/page.tsx

**Styling inconsistencies:**

- Review glass-morphism design system usage
- Ensure responsive breakpoints are properly implemented
- Verify badge color variants match score thresholds

## FAQ

**Q: How is the vagueness score calculated?**
A: Scores are calculated using AI analysis on a 0-100 scale where higher scores indicate more problematic vague
language. The scoring considers context, measurability, and potential for misinterpretation.

**Q: What types of vague language are detected?**
A: The system identifies quantitative ambiguity ("significant"), temporal vagueness ("soon"), comparative ambiguity ("
better"), commitment uncertainty ("aim to"), and scope limitations ("where possible").

**Q: Can users add their own vague terms?**
A: Currently, vague terms are generated through the AI analysis pipeline. Manual additions require backend processing
and are not supported through the frontend interface.

**Q: How often is vague language analysis updated?**
A: Analysis is tied to specific analysis runs and updated when new document processing occurs. Real-time updates are not
currently supported.

**Q: What permissions are required for admin features?**
A: Admin features require authenticated users with `admin: true` in their authentication context and proper Row Level
Security permissions.

**Q: How can I export vague terms analysis?**
A: Export functionality is planned for future releases. Currently, users can copy analysis content or use browser print
functionality for the summary reports.

## References

### Technical Documentation

- [Next.js App Router Documentation](https://nextjs.org/docs/app)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [shadcn/ui Component Library](https://ui.shadcn.com/)
- [React Hooks Documentation](https://react.dev/reference/react)

### Related Code Files

- [`types/vague.ts`](../../../types/vague.ts) - TypeScript interface definitions
- [`utils/vague-utils.ts`](../../../utils/vague-utils.ts) - Utility functions for vague data processing
- [`utils/vague-converter.ts`](../../../utils/vague-converter.ts) - Data conversion utilities
- [
  `components/context/entity/data/vague-data-fetcher.ts`](../../../components/context/entity/data/vague-data-fetcher.ts) -
  Data fetching implementation

### External Resources

- [PostgreSQL JSONB Documentation](https://www.postgresql.org/docs/current/datatype-json.html)
- [Supabase Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)
- [ESG Greenwashing Detection Research](https://www.example-esg-research.com)

### Related README Files

- [Claims Analysis Module](../claims/README.md)
- [Promises Tracking Module](../promises/README.md)
- [Customer App Documentation](../../../README.md)

---

## Changelog

### 2025-07-27

- **CREATED**: Initial comprehensive README.md documentation
- **ANALYZED**: Complete codebase structure and component relationships
- **DOCUMENTED**: Architecture patterns, data flow, and integration points
- **RESEARCHED**: Dependencies, Linear issues, and technical requirements
- **SPECIFIED**: Usage examples, troubleshooting guides, and future roadmap

---

(c) All rights reserved ekoIntelligence 2025
