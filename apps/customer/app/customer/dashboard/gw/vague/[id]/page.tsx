/**
 * Next.js Dynamic Route Page for ESG Vague Terms Detail Display
 * 
 * This file implements a client-side React page component that displays detailed 
 * ESG vague language analysis information. It serves as the main vague term detail 
 * view in the customer dashboard, fetching vague term data from the Supabase database 
 * and rendering comprehensive vagueness analysis including scores, explanations, and 
 * supporting documentation.
 * 
 * ## Core Functionality
 * - **Vague Term Data Retrieval**: Fetches detailed vague term information from `_deprecated_xfer_gw_vague_v2` table
 * - **Vagueness Analysis Display**: Shows vague language assessment with scores and explanations
 * - **Navigation Integration**: Updates breadcrumb navigation and page title dynamically
 * - **Authentication Awareness**: Provides admin-specific features based on user permissions
 * - **Type Safety**: Uses TypeScript with VagueType interface for robust data handling
 * 
 * ## Request Parameters
 * - **Route Parameter**: `id` - Numeric vague term identifier from the database
 * 
 * ## Data Flow
 * 1. Extract vague term ID from route parameters using Next.js 15 `use()` hook
 * 2. Query Supabase `_deprecated_xfer_gw_vague_v2` table for vague term data by ID
 * 3. Update navigation context with vague term-specific breadcrumb path
 * 4. Render VagueTerm component with fetched data and admin permissions
 * 
 * ## Dependencies
 * - **Next.js 15 App Router**: Modern React framework with async components
 * - **Supabase Client**: Database access with Row Level Security and real-time features
 * - **React 18+**: Latest React features including `use()` hook for promise handling
 * - **Navigation Context**: Custom navigation system for breadcrumb management
 * - **Authentication Context**: User authentication and role-based access control
 * 
 * ## Related Components
 * - VagueTerm: Main vague term display component with comprehensive analysis view
 * - Navigation Context: Breadcrumb and page title management system
 * - Authentication Context: User session and permission management
 * - Supabase Client: Database connection and query execution
 * 
 * ## Database Schema
 * - Connects to customer Supabase database via RLS-protected queries
 * - Uses `_deprecated_xfer_gw_vague_v2` table for vague term data synchronized from analytics database
 * - Handles type casting from database JSON to TypeScript VagueType interface
 * - Supports error handling for non-existent or inaccessible vague terms
 * 
 * ## Security & Performance
 * - Database queries protected by Supabase Row Level Security policies
 * - Client-side rendering for dynamic content with loading states
 * - Type-safe database interactions prevent runtime errors
 * - Efficient single-record queries with specific ID lookup
 * 
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Guide
 * @see https://react.dev/reference/react/use React use() Hook Documentation
 * @see {@link ../vague-term.tsx} VagueTerm Component
 * @see {@link ../../../../types/vague.ts} VagueType Interface
 * <AUTHOR>
 * @updated 2025-07-22
 * @description ESG vague term detail page that fetches and displays comprehensive vague language analysis including scores, explanations, and supporting documentation
 * @example ```bash
curl -X GET 'http://localhost:3000/customer/dashboard/gw/vague/12345'
```
 * @docgen doc-by-claude
 * 
 * (c) All rights reserved ekoIntelligence 2025
 */
"use client";
import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { VagueType } from '@/types'
import { useNav } from '@/components/context/nav/nav-context'
import { runAsync } from '@utils/react-utils'
import { useAuth } from '@/components/context/auth/auth-context'
import { VagueTerm } from '../vague-term'

export default function Page(props: { params: Promise<{ id: string }> }) {
    const params = use(props.params);
    const { id } = params;

    const supabase = createClient();
    const auth = useAuth();
    const nav = useNav();
    const [vagueData, setVagueData] = useState<VagueType | null>(null);

    useEffect(() => {
        runAsync(async () => {
            if (id) {
                // Get vague term from _deprecated_xfer_gw_vague_v2
                const {
                    data: vagueV2Data,
                    error: vagueV2Error
                } = await supabase
                  .from('_deprecated_xfer_gw_vague_v2')
                    .select("*")
                    .eq("id", +id)
                    .single();

                if (vagueV2Error) {
                    console.error("Error fetching vague term:", vagueV2Error);
                    return;
                }

                if (vagueV2Data) {
                    // Cast to unknown first to avoid TypeScript errors with Json type
                    setVagueData(vagueV2Data as unknown as VagueType);
                    nav.changeTitle("Vague Term (" + vagueV2Data.id + ")");

                    nav.changeNavPath([
                        { href: "/customer/dashboard", label: "Dashboard" },
                        { href: "/customer/dashboard/gw/vague", label: "Vague Terms" },
                        { href: "/customer/dashboard/gw/vague/" + id, label: id }
                    ]);
                }
            }
        });
    }, [id]);

    if (!vagueData) {
        return null;
    }

    return (
        <div className="mt-4">
            <VagueTerm item={vagueData} admin={auth.admin} />
        </div>
    );
}
