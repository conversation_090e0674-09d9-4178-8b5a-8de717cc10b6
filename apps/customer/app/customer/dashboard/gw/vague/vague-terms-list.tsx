/**
 * ESG Vague Terms List Component - Interactive Vagueness Analysis Display
 *
 * This React component provides a comprehensive list view of ESG (Environmental, Social, Governance) vague terms 
 * identified by AI analysis in corporate sustainability documents. Each term is displayed with its vagueness score,
 * context phrase, detailed analysis summary, and provides navigation to detailed vague term analysis views with
 * full context preservation and citation support.
 *
 * ## Core Functionality
 * - **Vague Terms Display**: Renders analyzed vague terms as interactive clickable cards with glass-morphism styling
 * - **Vagueness Scoring**: Visual display of AI-computed vagueness scores (0-100%) with Info icon indicators
 * - **Contextual Navigation**: Click-through to detailed vague term analysis while preserving entity query parameters
 * - **Administrative Controls**: Delete functionality for admins with proper table-specific permissions and security
 * - **Empty State Handling**: Graceful display of no vague terms message when dataset is empty or undefined
 * - **Citation Integration**: Full support for document citations and source references through EkoMarkdown
 *
 * ## Vague Terms Data Structure & Analysis
 * - **Vagueness Score**: Numerical score (0-100%) indicating degree of vagueness identified by AI analysis
 * - **Context Phrase**: The specific vague phrase or term extracted from corporate documents (e.g., "substantial", "significant")
 * - **Analysis Summary**: AI-generated detailed explanation of why the term is considered vague and its implications
 * - **Document Context**: Citation information including source documents, page references, and contextual metadata
 * - **Entity Association**: Terms linked to specific corporate entities and analysis runs for proper filtering
 * - **Model Data**: Complex vagueness analysis stored in JSONB model field with scoring, explanations, and evidence
 *
 * ## Visual Design & Interaction Pattern
 * - **Minimalist Cards**: Clean, bordered cards with hover effects for optimal readability and interaction
 * - **Score Badge Display**: Prominent Info icon with percentage score using secondary variant styling
 * - **Phrase Highlighting**: Bold display of the actual vague phrase in single quotes for immediate recognition
 * - **Content Summary**: EkoMarkdown-rendered analysis summary with full citation support and admin controls
 * - **Responsive Layout**: Mobile-first design with proper spacing, padding, and touch-friendly interactions
 * - **Border Styling**: Conditional borders (desktop only) with consistent margin and padding patterns
 *
 * ## Navigation & Query Parameter Management
 * - **Entity Context Integration**: Utilizes useEntity hook to access current entity, run, and model parameters
 * - **Query String Preservation**: Maintains all entity-specific URL parameters when navigating to term details
 * - **Next.js Router**: Uses Next.js router.push for client-side navigation with preserved query strings
 * - **URL Structure**: Navigates to `/customer/dashboard/gw/vague/{id}?{preserved_query_params}`
 * - **Context Preservation**: Ensures analysis context is maintained across navigation for seamless user experience
 *
 * ## Database Integration & Data Flow
 * The component integrates with the customer Supabase database through:
 * - **_deprecated_xfer_gw_vague_v2 Table**: Primary vague terms data storage with Row Level Security policies
 * - **JSONB Model Field**: Complex vagueness analysis including score, summary, citations, and AI reasoning
 * - **Entity Filtering**: Automatic filtering by entity_xid and run_id through EntityContext provider
 * - **Citation Management**: Embedded CitationType objects with document references and metadata
 * - **Admin Permissions**: Delete operations respect admin permissions and target correct deprecated table
 *
 * ## Administrative Features & Security
 * - **AdminDeleteButton Integration**: Contextual delete functionality for users with administrative permissions
 * - **Table-Specific Operations**: Delete operations target the correct deprecated table (_deprecated_xfer_gw_vague_v2)
 * - **Permission-Based Access**: Admin prop controls visibility and functionality of administrative features
 * - **Record Type Specification**: Proper record type labeling for audit trails and user feedback
 * - **Security Context**: Integrates with Supabase authentication and Row Level Security policies
 *
 * ## System Integration Context
 * This component fits into the broader ESG vagueness detection system:
 * - **Analytics Backend**: Python backend analyzes documents for vague language using NLP and LLM techniques
 * - **Data Synchronization**: xfer tables synchronize vagueness analysis from analytics to customer database
 * - **Vagueness Dashboard**: Component renders within vague terms management interface with entity filtering
 * - **Detail Navigation**: Links to individual vague term analysis pages with preserved entity context
 * - **Citation System**: Full integration with document citation system for source verification and traceability
 * - **Authentication Integration**: Respects user permissions for administrative functionality and data access
 *
 * ## Component Architecture & Props
 * - **VagueDetailData Array**: Array of VagueType objects containing comprehensive vague term analysis
 * - **Admin Boolean**: Controls visibility and functionality of administrative delete features
 * - **Conditional Rendering**: Handles undefined/null vague term data gracefully with appropriate user messages
 * - **Type Safety**: Full TypeScript integration with strict type checking for props and data structures
 * - **Testing Support**: Comprehensive structure supports reliable end-to-end testing and debugging
 *
 * ## Key Dependencies & Integration
 * - **React 18+**: Modern React with functional components, hooks, and client-side rendering
 * - **Next.js 15 App Router**: Latest Next.js routing with useRouter hook for client-side navigation
 * - **ShadCN/UI Components**: Badge component for consistent score display and visual hierarchy
 * - **Lucide React Icons**: Info icon for visual score indicators and user interface consistency
 * - **Entity Context**: Custom React context providing vague term data and entity-specific parameters
 * - **EkoMarkdown Component**: Custom markdown renderer with citation support for analysis summaries
 * - **AdminDeleteButton**: Specialized administrative control with proper permissions and security integration
 *
 * ## Performance & Optimization
 * - **Conditional Rendering**: Avoids unnecessary DOM operations when data is missing or invalid
 * - **Efficient Mapping**: Direct array mapping without complex transformations for optimal performance  
 * - **Citation Integration**: Uses EkoMarkdown for efficient rendering of analysis summaries with citations
 * - **Memory Management**: Proper component lifecycle and efficient re-rendering through optimized dependencies
 * - **Text Display**: Direct display of phrase and summary content without complex processing or truncation
 *
 * @see https://react.dev/reference/react React Documentation
 * @see https://nextjs.org/docs/app/api-reference/functions/use-router Next.js useRouter Hook
 * @see https://ui.shadcn.com/docs/components/badge ShadCN/UI Badge Component
 * @see https://lucide.dev/icons/info Lucide Info Icon
 * @see {@link ../../../../types/vague.ts} VagueType and VagueTypeV2 Interface Definitions
 * @see {@link ../../../../components/context/entity/entity-context.tsx} EntityContext Provider
 * @see {@link ../../../../components/markdown/eko-markdown.tsx} EkoMarkdown Component
 * @see {@link ../../../../components/admin.tsx} AdminDeleteButton Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Interactive ESG vague terms list component displaying AI-analyzed vague language with scores, context, and navigation to detailed analysis views.
 * @example ```tsx
  // Display filtered vague terms list with admin controls
  <VagueTermsList vagueDetailData={filteredVagueTerms} admin={user?.is_admin} />
  
  // Empty state handling
  <VagueTermsList vagueDetailData={[]} admin={false} />
  
  // Full vague terms list in dashboard context
  {entity.vagueDetailData && (
    <VagueTermsList 
      vagueDetailData={entity.vagueDetailData} 
      admin={auth.admin}
    />
  )}
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

"use client";
import { VagueType } from '@/types'
import { Badge } from '@/components/ui/badge'
import { Info } from 'lucide-react'
import React from 'react'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CitationType } from '@/components/citation'
import { useRouter } from 'next/navigation'
import { useEntity } from '@/components/context/entity/entity-context'
import { AdminDeleteButton } from '@/components/admin'

export function VagueTermsList({vagueDetailData, admin}: { vagueDetailData: VagueType[] | undefined | null, admin: boolean }) {
    const router = useRouter();
    const {queryString} = useEntity();

    if (!vagueDetailData || vagueDetailData.length === 0) {
        return <p className="text-sm text-muted-foreground text-center py-4">No vague terms found</p>;
    }

    return <>
        {vagueDetailData.map((item) => (
            <div key={item.id} className="lg:p-2 lg:m-4 border-0 lg:border my-2 hover:cursor-pointer relative group"
                 onClick={() => router.push("/customer/dashboard/gw/vague/" + item.id + "?" + queryString)}>
                <AdminDeleteButton
                  tableName="_deprecated_xfer_gw_vague_v2"
                    recordId={item.id}
                    recordType="vague term"
                />
                <div className="flex items-center gap-2 mt-2 mb-2 text-sm">
                    <Badge variant="secondary">
                        <Info className="w-3 h-3 mr-1"/>
                        Score: {item.model.score}%
                    </Badge>
                    <span className="font-bold text-md">'{item.phrase}'</span>
                </div>
                <EkoMarkdown className="text-sm font-light" citations={item.model.citations as CitationType[]}
                             admin={admin}>{item.model.summary}</EkoMarkdown>
            </div>

        ))}
    </>;
}
