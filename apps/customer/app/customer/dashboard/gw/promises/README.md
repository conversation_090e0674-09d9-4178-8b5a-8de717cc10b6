# ESG Corporate Promise Tracking Dashboard

## Overview

The ESG Corporate Promise Tracking Dashboard is a comprehensive React-based interface for monitoring, analyzing, and verifying corporate Environmental, Social, and Governance (ESG) promises and commitments. This module enables stakeholders to track promise fulfillment over time, identify broken promises, assess corporate reliability, and detect greenwashing patterns in sustainability commitments.

The dashboard integrates with EkoIntelligence's dual-database architecture, where promises are processed and analyzed in the analytics backend using AI-powered assessment algorithms, then synchronized to the customer database for real-time dashboard display, interactive visualizations, and user engagement.

## Specification

### Core Requirements

The promise tracking system must provide:

1. **Promise Fulfillment Analysis**: Comprehensive evaluation of corporate promises against historical evidence and delivery outcomes
2. **Timeline Visualization**: Chronological display of promises with status indicators and trend analysis over 5-year periods
3. **Status Classification**: Clear categorization of promises as kept (fulfilled), broken (unfulfilled), or uncertain (insufficient evidence)
4. **Interactive Dashboard**: User-friendly interface with filtering, sorting, and detailed promise exploration capabilities
5. **Assessment History**: Historical tracking of promise evaluations and analysis changes over time
6. **Evidence Integration**: Supporting evidence display with citation management and source attribution
7. **Administrative Controls**: Enhanced functionality for admin users including promise management and verification overrides
8. **Confidence Scoring**: AI-powered confidence metrics indicating reliability of promise assessments

### Data Quality Standards

- **AI Confidence**: Promise assessments include confidence percentages for reliability indication
- **Evidence Requirements**: Supporting evidence from reputable sources with proper citation
- **Temporal Accuracy**: Promise document years and target dates tracked for timeline analysis
- **Status Integrity**: Boolean kept/broken status with null handling for uncertain outcomes
- **Performance Limits**: Display optimization with reasonable limits on promise quantities

## Key Components

### 1. Main Promise Dashboard (`page.tsx`)
- **Purpose**: Primary promise tracking interface with comprehensive filtering, statistics, and visualization
- **Key Features**:
  - Advanced filtering by promise status (kept/broken/uncertain) with real-time updates
  - Multi-criteria sorting (relevance, date, status) with memoized performance optimization
  - Promise summary statistics with color-coded indicators
  - Integration with EntityContext for promise data management
  - Breadcrumb navigation and page title management
- **Database Integration**: Consumes `xfer_promises` table via EntityContext with Row Level Security
- **Performance**: Uses `useMemo` for expensive filtering and sorting operations

### 2. Individual Promise Detail Page (`[id]/page.tsx`)
- **Purpose**: Next.js 15 App Router dynamic route for detailed promise analysis display
- **Key Features**:
  - Client-side rendering with loading states and error handling
  - Type-safe database interactions with promise ID validation
  - Navigation context updates with promise-specific breadcrumbs
  - Admin permission integration for enhanced functionality
- **Data Flow**: Direct Supabase query to `xfer_promises` table with RLS security
- **User Experience**: Seamless navigation with loading indicators and error boundaries

### 3. Interactive Promise List (`promises-list.tsx`)
- **Purpose**: Comprehensive list view with interactive cards, animations, and detailed previews
- **Key Features**:
  - Framer Motion animations with staggered loading and viewport triggers
  - Status visualization with color-coded progress bars and badges
  - Promise content display with truncation and responsive typography
  - Navigation links with query parameter preservation
  - Admin delete functionality with proper permissions
- **UI System**: Glass-morphism cards with hover effects and motion animations
- **Testing**: Comprehensive data-testid attributes for Playwright integration

### 4. Promise Timeline Visualization (`promises-timeline.tsx`)
- **Purpose**: Chronological timeline showing promise distribution and status over time
- **Key Features**:
  - Vertical timeline with connecting lines and year-based grouping
  - Status indicators with color-coded icons for visual clarity
  - Promise overflow handling (shows top 3 per year with "+N more" indicators)
  - Year-based sorting with most recent first presentation
- **Data Processing**: Groups promises by `promise_doc_year` with performance optimization
- **Responsive Design**: Mobile-first layout with accessible touch targets

### 5. Five-Year Trend Chart (`promises-five-year-chart.tsx`)
- **Purpose**: Horizontal stacked bar chart showing promise fulfillment trends over 5 years
- **Key Features**:
  - Dynamic 5-year window calculation relative to current date
  - Stacked bar visualization with proportional segments for each status
  - Aggregate statistics summary with total counts across all years
  - Interactive data display with year-over-year comparison
- **Chart Components**: Color-coded segments (green=kept, red=broken, yellow=uncertain)
- **Performance**: Efficient data filtering and percentage calculations for visualization

### 6. Promise Assessment History (`promise-assessment-history.tsx`)
- **Purpose**: Historical tracking of promise evaluations and analysis changes over time
- **Key Features**:
  - Chronological display of assessments sorted by most recent first
  - Status badges with confidence metrics for reliability indication
  - Assessment limitation (top 5) with overflow indicators
  - Text truncation with CSS line clamping for consistent presentation
- **Data Structure**: Transforms PromiseTypeV2 data into simplified assessment objects
- **UI Integration**: Seamless integration with overall dashboard design system

### 7. Detailed Promise Card (`promise.tsx`)
- **Purpose**: Comprehensive promise analysis display with evidence, citations, and AI assessment
- **Key Features**:
  - Promise status banner with color-coded indicators and confidence metrics
  - Detailed analysis sections (summary, conclusion, AI verdict) with markdown rendering
  - Evidence integration with supporting/contradicting indicators
  - Citation management with document links and source attribution
  - Administrative controls for promise management and verification
- **Content Structure**: Organized sections for promise text, analysis, evidence, and context
- **Testing Support**: Comprehensive data-testid attributes for component testing

## Dependencies

### Core Framework Dependencies
- **Next.js 15 App Router**: Modern React framework with server-side rendering and dynamic routing
- **React 18+**: Latest React features including hooks, strict mode, and concurrent features
- **TypeScript**: Complete type safety with PromiseTypeV2 interface and strict compilation

### UI Framework Dependencies  
- **Tailwind CSS**: Utility-first CSS framework with glass-morphism design system support
- **shadcn/ui**: Component library built on Radix UI primitives (Card, Badge, Select components)
- **Framer Motion**: Animation library for smooth card reveals, staggered loading, and interactive effects
- **Lucide React**: Modern icon library for status indicators (CheckCircle2, CircleXIcon, HelpCircle)

### Data & State Management
- **Supabase Client**: Database access with Row Level Security policies and real-time capabilities
- **React Context API**: State management via EntityContext, NavigationContext, and AuthenticationContext
- **Entity Context Provider**: Centralized promise data management with loading states and error handling

### Database Integration
- **Customer Database**: `xfer_promises` table for optimized dashboard performance and user access
- **Analytics Database**: Backend processing in `ana_promises` table with comprehensive promise analysis
- **Supabase RLS**: Row Level Security for tenant isolation and user access control
- **JSONB Model Field**: Complex promise analysis data storage with evidence, citations, and metadata

## Usage Examples

### Basic Promise Dashboard Access
```typescript
// Navigate to promise dashboard
// URL: /customer/dashboard/gw/promises?entity=EntityId&run=latest&model=ekoIntelligence

// Features available:
// - Promise status filtering (all/kept/broken)
// - Multi-criteria sorting (relevance/date/status)
// - Summary statistics with color-coded counts
// - Five-year trend visualization
// - Timeline view with year-based grouping
```

### Individual Promise Analysis
```typescript
// Access detailed promise analysis
// URL: /customer/dashboard/gw/promises/[promiseId]?entity=EntityId&run=latest

// Features available:
// - Comprehensive promise assessment with AI verdict
// - Evidence correlation with supporting/contradicting indicators
// - Citation management with document links
// - Confidence scoring and reliability metrics
// - Admin controls for verification overrides
```

### Component Integration
```typescript
import { PromisesListV2 } from './promises-list'
import { PromisesTimeline } from './promises-timeline'
import { PromisesFiveYearChart } from './promises-five-year-chart'
import { PromiseAssessmentHistory } from './promise-assessment-history'
import { PromiseCardV2 } from './promise'

// Interactive promise list with admin controls
<PromisesListV2 promisesData={filteredPromises} admin={isAdmin} />

// Timeline visualization
<PromisesTimeline promisesData={promises} />

// Five-year trend chart
<PromisesFiveYearChart promisesData={promises} />

// Assessment history tracking
<PromiseAssessmentHistory promisesData={promises} />

// Detailed promise card
<PromiseCardV2 item={promiseData} admin={isAdmin} />
```

## Architecture Notes

### System Architecture Flow

```mermaid
graph TD
    A[Corporate Documents] --> B[Analytics Backend Python]
    B --> C[Promise Analysis Pipeline]
    C --> D[ana_promises Table]
    D --> E[Data Sync Layer]
    E --> F[xfer_promises Table]
    F --> G[Customer Database]
    G --> H[Supabase Client]
    H --> I[Promise Dashboard]
    I --> J[Interactive Components]
    
    subgraph "Analytics Database"
        C
        D
    end
    
    subgraph "Customer Database"
        F
        G
    end
    
    subgraph "Frontend Layer"
        H
        I
        J
    end
    
    subgraph "Promise Analysis Components"
        K[AI Assessment]
        L[Evidence Correlation]
        M[Confidence Scoring]
    end
    
    B --> K
    B --> L
    B --> M
```

### Component Architecture

```mermaid
graph TB
    A[page.tsx - Main Dashboard] --> B[EntityContext]
    A --> C[PromisesTimeline]
    A --> D[PromisesFiveYearChart]
    A --> E[PromiseAssessmentHistory]
    A --> F[PromisesListV2]
    
    F --> G[PromiseCardV2]
    H[id/page.tsx - Dynamic Route] --> G
    
    B --> I[Supabase Client]
    I --> J[xfer_promises Table]
    
    G --> K[Citation System]
    G --> L[Evidence Display]
    G --> M[AI Verdict Rendering]
    
    subgraph "Context Providers"
        B
        N[AuthContext]
        O[NavContext]
    end
    
    A --> N
    A --> O
    
    subgraph "Database Schema"
        J
        P[model JSONB Field]
        Q[kept Boolean Field]
        R[statement_text Field]
    end
    
    J --> P
    J --> Q
    J --> R
```

### Data Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant P as Page Component
    participant E as EntityContext
    participant S as Supabase Client
    participant D as Database
    participant A as Analytics Backend
    
    U->>P: Navigate to Promise Dashboard
    P->>E: Request Promise Data
    E->>S: Query xfer_promises
    S->>D: Execute RLS Query
    D-->>S: Return Filtered Promises
    S-->>E: Promise Data
    E-->>P: Processed Promises
    P->>P: Apply Filters & Sorting
    P->>U: Display Dashboard with Visualizations
    
    Note over A,D: Background Process
    A->>D: Sync Promise Analysis
    D->>E: Real-time Updates (if applicable)
```

## Known Issues

Based on Linear ticket analysis and code review:

### Active Issues
- **EKO-298**: Broken promises evidence expansion - "+8 more pieces of evidence" displayed without expansion option (High Priority)
- **EKO-234**: Supporting evidence for broken promises may be insufficient or weak in some cases (High Priority)
- **EKO-158**: Legacy hallucinated filter features removed but may need enhanced replacement (Resolved - Follow-up needed)

### Technical Debt
- Large component files (>300 lines) should be refactored into smaller, focused modules
- Promise evidence display limited to top 3 items - expansion mechanism needed
- Type casting in database queries needs improvement for better type safety
- Animation performance could be optimized for large promise datasets

### User Experience Issues
- Evidence expansion functionality missing for promises with extensive supporting documentation
- Font size inconsistencies in promise display text across different components
- Mobile responsiveness could be enhanced for timeline and chart components

## Future Work

### Planned Enhancements (Based on Requirements & Linear Tickets)

1. **Evidence Expansion System** (EKO-298 Priority)
   - Implement expandable evidence sections for promises with extensive documentation
   - Add pagination or modal views for detailed evidence browsing
   - Enhanced evidence filtering and categorization

2. **Enhanced Promise Analytics** (EKO-257 Integration)
   - Integration with behavioral analysis patterns from other GW modules
   - Advanced greenwashing detection specific to promise content
   - Cross-reference with claims analysis for comprehensive corporate assessment

3. **Performance Optimizations**
   - Implement virtual scrolling for large promise datasets
   - Add client-side caching with optimistic updates
   - Optimize database queries with better indexing strategies

4. **Advanced Visualization Features**
   - Interactive timeline with zoom capabilities and milestone tracking
   - Promise fulfillment rate trends with industry benchmarking
   - Geographic and sector-based promise analysis comparisons

5. **Enhanced User Experience**
   - Advanced filtering UI with date ranges and promise type categorization
   - Export functionality for compliance reporting and stakeholder communication
   - Real-time notifications for promise status changes and updates

6. **Mobile and Accessibility Improvements**
   - Enhanced mobile responsiveness for all visualization components
   - Improved keyboard navigation and screen reader support
   - High contrast mode and accessibility compliance (WCAG 2.1)

## Troubleshooting

### Common Issues

**Promises not loading**
- Verify entity selection in URL parameters (?entity=EntityId)
- Check user permissions and Supabase RLS policies
- Ensure Supabase client is properly initialized with correct project configuration

**Empty promise list**
- Verify entity has processed promise data in analytics backend
- Check run_id parameter for active analysis run
- Confirm promise analysis pipeline has completed for selected entity

**Timeline visualization not displaying**
- Check if promises have valid `promise_doc_year` values in model data
- Verify promise data contains temporal information for chronological display
- Ensure promises span multiple years for meaningful timeline visualization

**Chart visualization issues**
- Confirm promise status values (kept/broken/uncertain) are properly set
- Check that promise data covers the 5-year window being analyzed
- Verify chart calculations are not encountering division by zero

**Navigation or context issues**
- Clear browser cache and localStorage for fresh session state
- Verify React context providers are properly configured in component tree
- Check browser console for JavaScript errors or network failures

### Debug Mode

Enable additional logging by adding debug query parameter:
```
/customer/dashboard/gw/promises?entity=EntityId&debug=true
```

## FAQ

### User-Centric Questions

**Q: How are promises different from claims in the EkoIntelligence system?**
A: Promises are forward-looking commitments about future actions or goals, while claims are statements about current or past achievements. Promises are evaluated against future evidence to determine fulfillment, whereas claims are verified against historical evidence.

**Q: What do the confidence scores mean for promise assessments?**
A: Confidence scores (0-100%) indicate how certain our AI analysis is about whether a promise was kept, broken, or remains uncertain. Higher confidence scores suggest more definitive evidence and reliable assessment.

**Q: Why do some promises show as "uncertain" rather than kept or broken?**
A: Promises are marked as uncertain when there's insufficient evidence to make a definitive determination, when the target timeline hasn't been reached, or when evidence is contradictory or ambiguous.

**Q: How often is promise data updated?**
A: Promise data is synchronized from the analytics database based on processing runs. Update frequency depends on entity-specific analysis schedules, document availability, and evidence collection cycles.

**Q: Can I see more than 3 pieces of evidence for a promise?**
A: Currently, promise cards display the top 3 pieces of evidence. Full evidence expansion is planned for future releases (see EKO-298). Admin users may have access to additional evidence details.

**Q: What does the five-year chart show exactly?**
A: The five-year chart displays promise fulfillment trends over the most recent five years, showing proportional breakdowns of kept, broken, and uncertain promises by year with aggregate totals.

**Q: How are promises discovered and extracted from corporate documents?**
A: Our AI system analyzes corporate documents, sustainability reports, and public statements to identify forward-looking commitments and sustainability promises using natural language processing and machine learning algorithms.

**Q: What makes a promise "ESG-related"?**
A: Promises are classified as ESG-related when they pertain to Environmental (climate, pollution, resource use), Social (community, workforce, human rights), or Governance (ethics, transparency, accountability) topics based on AI analysis.

## References

### Documentation Links
- [Next.js App Router Documentation](https://nextjs.org/docs/app/building-your-application/routing)
- [Supabase React Integration](https://supabase.com/docs/guides/getting-started/tutorials/with-react)
- [Framer Motion Animation Guide](https://www.framer.com/motion/)
- [shadcn/ui Component Library](https://ui.shadcn.com/)
- [Tailwind CSS Glass-morphism](https://tailwindcss.com/docs/backdrop-blur)

### Related Code Files
- [`/apps/customer/types/promise.ts`](../../types/promise.ts) - PromiseTypeV2 and XferPromiseModel Type Definitions
- [`/apps/customer/components/context/entity/entity-context.tsx`](../../../components/context/entity/entity-context.tsx) - Entity Context Provider
- [`/apps/customer/components/context/nav/nav-context.tsx`](../../../components/context/nav/nav-context.tsx) - Navigation Context Provider
- [`/apps/customer/components/context/auth/auth-context.tsx`](../../../components/context/auth/auth-context.tsx) - Authentication Context Provider
- [`/apps/customer/components/markdown/eko-markdown.tsx`](../../../components/markdown/eko-markdown.tsx) - Markdown Renderer with Citation Support
- [`/apps/customer/components/admin.tsx`](../../../components/admin.tsx) - Administrative Controls Component

### Related README Files
- [`/apps/customer/app/customer/dashboard/gw/claims/README.md`](../claims/README.md) - Claims Analysis Module
- [`/apps/customer/app/customer/dashboard/gw/cherry/README.md`](../cherry/README.md) - Cherry Picking Analysis Module
- [`/apps/customer/app/customer/dashboard/gw/vague/README.md`](../vague/README.md) - Vague Terms Analysis Module
- [`/apps/customer/app/customer/dashboard/@modal/README.md`](../@modal/README.md) - Modal System Documentation

### External Resources
- [ESG Promise Tracking Best Practices](https://www.sustainability.com/thinking/esg-promise-monitoring/)
- [Corporate Sustainability Commitment Analysis](https://www.sciencedirect.com/topics/economics-econometrics-and-finance/sustainability-commitments)
- [Greenwashing Detection in Corporate Promises](https://www.nature.com/articles/s41558-021-01097-4)
- [Glass-morphism Design System](https://uxdesign.cc/glassmorphism-in-user-interfaces-1f39bb1308c9)

### Third-Party Dependencies
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Radix UI Primitives](https://www.radix-ui.com/primitives)
- [React 18+ Documentation](https://react.dev/reference/react)
- [PostgreSQL JSONB Documentation](https://www.postgresql.org/docs/current/datatype-json.html)

### Linear Issue References
- [EKO-298: Broken promises evidence expansion](https://linear.app/ekointelligence/issue/EKO-298/broken-promises) - High Priority
- [EKO-234: Supporting Evidence Quality](https://linear.app/ekointelligence/issue/EKO-234/supporting-evidence-for-broken-promises-flimsy) - High Priority
- [EKO-257: EKO Behavioral Analysis Integration](https://linear.app/ekointelligence/issue/EKO-257/eko-behavioural-analysis-needs-to-include-in-report) - Medium Priority
- [EKO-158: Hallucinated Features](https://linear.app/ekointelligence/issue/EKO-158/hallucinated-features) - Resolved

---

## Changelog

### 2025-07-30
- Created comprehensive README.md documentation for ESG Corporate Promise Tracking Dashboard
- Documented all 7 key components with detailed specifications and functionality descriptions
- Added system architecture diagrams including data flow and component relationships
- Included troubleshooting guide with common issues and debug mode instructions
- Created comprehensive FAQ section addressing user-centric questions about promise tracking
- Documented known issues from Linear ticket analysis including EKO-298 and EKO-234
- Added future work roadmap based on Linear requirements and technical debt analysis
- Provided detailed usage examples and component integration patterns
- Added complete dependency documentation covering framework, UI, and database integrations
- Included extensive reference links to documentation, related files, and external resources

(c) All rights reserved ekoIntelligence 2025