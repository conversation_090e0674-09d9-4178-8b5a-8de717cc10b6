/**
 * Promise Assessment History React Component - ESG Corporate Promise Evaluation Timeline
 *
 * This React component displays a chronological history of ESG (Environmental, Social, Governance) 
 * promise assessments for corporate entities, showing how promises have been evaluated and tracked 
 * over time. It provides visual indicators for promise fulfillment status with confidence metrics,
 * focusing on the most recent assessments to give users immediate visibility into promise tracking outcomes.
 *
 * ## Core Functionality
 * - **Assessment Timeline Display**: Shows chronological list of promise evaluations sorted by year (most recent first)
 * - **Status Visualization**: Visual icons and color-coded badges indicating kept (✓), broken (✗), or uncertain (?) promise status
 * - **Confidence Metrics**: Displays confidence percentages for each assessment to indicate reliability of evaluation
 * - **Summary Text Display**: Truncated promise text with line clamping for clean presentation
 * - **Responsive Layout**: Adapts to different screen sizes with proper spacing and typography
 * - **Empty State Handling**: Gracefully handles cases where no promise data is available
 * - **Assessment Limiting**: Shows top 5 most recent assessments with overflow indicator
 *
 * ## Data Processing & Display Logic
 * - **Promise Mapping**: Transforms PromiseTypeV2 data into simplified assessment objects for display
 * - **Year Extraction**: Uses promise_doc_year from model or falls back to current year
 * - **Status Interpretation**: Converts boolean/null kept status to user-friendly text and visual indicators
 * - **Sorting Algorithm**: Chronological sorting by year (descending) to show most recent first
 * - **Text Fallback**: Prioritizes statement_text, then model.text, then summary for display content
 *
 * ## Visual Design & UI Components
 * - **Glass-Morphism Cards**: Individual assessment entries with consistent background and border styling
 * - **Status Icons**: Lucide React icons (CheckCircle2, CircleXIcon, HelpCircle) for clear visual status
 * - **Color-Coded Elements**: Green for kept promises, red for broken, yellow/amber for uncertain
 * - **Badge Components**: ShadCN/UI badges for status and confidence display with appropriate variants
 * - **Typography Hierarchy**: Clear year display, status badges, confidence indicators, and truncated text
 * - **Spacing System**: Consistent padding, margins, and gaps following Tailwind design system
 *
 * ## Component Integration
 * - **Parent Component**: Used within promise dashboard pages to show historical assessment context
 * - **Data Source**: Receives PromiseTypeV2[] from EntityContext via xfer_promises table data
 * - **Testing Infrastructure**: Includes data-testid attributes for comprehensive Playwright testing
 * - **Accessibility**: Proper semantic HTML structure with meaningful text content and ARIA considerations
 *
 * ## Props Interface
 * - **promisesData**: Array of PromiseTypeV2 objects containing assessment data from xfer_promises table
 * - **Nullable Handling**: Safely handles undefined or null promise data arrays
 *
 * ## Key Dependencies
 * - **React 18+**: Modern React with hooks (no state management needed as component is purely presentational)
 * - **Next.js 15**: Leverages Next.js client-side rendering with 'use client' directive
 * - **Tailwind CSS**: Utility-first styling for responsive design and consistent visual appearance
 * - **ShadCN/UI Badge**: Pre-styled badge component for status and confidence display
 * - **Lucide React Icons**: Modern icon library for status visualization (CheckCircle2, CircleXIcon, HelpCircle)
 * - **TypeScript**: Full type safety with PromiseTypeV2 interface from types system
 *
 * ## Database Schema Integration
 * The component connects to customer Supabase database via the following structure:
 * - **xfer_promises Table**: Main promises data with id, entity_xid, run_id, kept status, summary, conclusion, statement_text
 * - **Model JSONB Field**: Contains XferPromiseModel with text, confidence, promise_doc_year, evidence, citations
 * - **Promise Assessment Data**: Historical tracking of promise evaluations with temporal analysis
 *
 * ## System Architecture Context
 * This component fits into the broader ESG analysis ecosystem:
 * - **Analytics Backend**: Python backend generates promise assessments and stores in analytics database
 * - **Data Synchronization**: xfer_promises table syncs processed data from analytics to customer database
 * - **Frontend Integration**: Component renders within promise dashboard providing historical context
 * - **Assessment Pipeline**: Part of comprehensive promise tracking and evaluation system
 * - **User Experience**: Provides immediate visual feedback on promise fulfillment patterns over time
 *
 * ## Performance Considerations
 * - **Limited Display**: Shows only top 5 assessments to prevent UI overflow and maintain performance
 * - **Efficient Sorting**: Simple year-based sorting with minimal computational overhead
 * - **Text Truncation**: CSS-based line clamping prevents layout issues with long promise text
 * - **Conditional Rendering**: Early return for empty data sets to avoid unnecessary processing
 * - **Memoization Ready**: Pure functional component suitable for React.memo optimization if needed
 *
 * @see https://react.dev/learn/passing-props-to-a-component React Props Documentation
 * @see https://nextjs.org/docs/app/building-your-application/rendering/client-components Next.js Client Components
 * @see https://tailwindcss.com/docs/responsive-design Tailwind CSS Responsive Design
 * @see https://lucide.dev/icons/ Lucide React Icons
 * @see https://ui.shadcn.com/docs/components/badge ShadCN/UI Badge Component
 * @see {@link ../../../types/promise.ts} PromiseTypeV2 Type Definition
 * @see {@link ../page.tsx} Promise Dashboard Parent Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description React component that displays chronological history of ESG promise assessments with status indicators and confidence metrics.
 * @example ```tsx
  <PromiseAssessmentHistory promisesData={promisesData} />
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client';

import React from 'react';
import { PromiseTypeV2 } from '@/types';
import { Badge } from '@/components/ui/badge';
import { CheckCircle2, CircleXIcon, HelpCircle } from 'lucide-react';

interface PromiseAssessmentHistoryProps {
    promisesData: PromiseTypeV2[] | undefined | null;
}

export function PromiseAssessmentHistory({ promisesData }: PromiseAssessmentHistoryProps) {
    if (!promisesData || promisesData.length === 0) {
        return null;
    }

    // Group promises by status and track changes over time
    const assessmentData = promisesData.map(promise => ({
        id: promise.id,
        year: promise.model.promise_doc_year || new Date().getFullYear(),
        status: promise.kept,
        confidence: promise.model.confidence,
        text: promise.statement_text || promise.model.text || promise.summary
    }));

    // Sort by year
    assessmentData.sort((a, b) => b.year - a.year);

    return (
        <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promise-assessment-history">
            <h3 className="text-lg font-semibold mb-4">Assessment History</h3>

            {/* Recent Assessments */}
            <div className="space-y-3">
                <h4 className="font-medium mb-2">Recent Assessments</h4>
                {assessmentData.slice(0, 5).map((assessment, idx) => {
                    let statusIcon;
                    let statusColor;
                    let statusText;
                    
                    if (assessment.status === true) {
                        statusIcon = <CheckCircle2 className="w-4 h-4" />;
                        statusColor = "text-green-600 dark:text-green-400";
                        statusText = "Kept";
                    } else if (assessment.status === false) {
                        statusIcon = <CircleXIcon className="w-4 h-4" />;
                        statusColor = "text-red-600 dark:text-red-400";
                        statusText = "Not Kept";
                    } else {
                        statusIcon = <HelpCircle className="w-4 h-4" />;
                        statusColor = "text-yellow-600 dark:text-yellow-400";
                        statusText = "Uncertain";
                    }
                    
                    return (
                        <div key={assessment.id} className="p-3 border rounded-md" data-testid="assessment-entry">
                            <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center gap-2">
                                    <span className={statusColor}>{statusIcon}</span>
                                    <span className="font-medium" data-testid="assessment-date">{assessment.year}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Badge 
                                        variant={assessment.status === true ? "default" : assessment.status === false ? "destructive" : "secondary"}
                                        data-testid="assessment-status"
                                    >
                                        {statusText}
                                    </Badge>
                                    <Badge variant="outline">
                                        {assessment.confidence}% confidence
                                    </Badge>
                                </div>
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                                {assessment.text}
                            </p>
                        </div>
                    );
                })}
                
                {assessmentData.length > 5 && (
                    <div className="text-sm text-muted-foreground text-center">
                        +{assessmentData.length - 5} more assessments
                    </div>
                )}
            </div>
        </div>
    );
}