/**
 * Promise Five-Year Trend Chart React Component - ESG Corporate Promise Tracking Visualization
 *
 * This React component renders a comprehensive 5-year trend visualization for ESG (Environmental, Social,
 * Governance) corporate promise tracking, displaying promise fulfillment statistics across the most recent
 * five years using horizontal stacked bar charts. The component provides stakeholders with clear visual
 * insights into corporate promise-keeping patterns over time, showing kept, broken, and uncertain promises
 * with detailed year-over-year breakdowns and aggregate statistics.
 *
 * ## Core Functionality
 * - **Five-Year Data Processing**: Automatically calculates and displays the last 5 years relative to current date
 * - **Promise Categorization**: Groups promises into three categories (kept, broken, uncertain) based on fulfillment status
 * - **Stacked Bar Visualization**: Uses horizontal stacked bars with proportional segments for each promise type
 * - **Year-over-Year Analysis**: Shows trends and patterns in corporate promise fulfillment over time
 * - **Aggregate Statistics**: Displays total counts for all promise categories across the entire 5-year period
 * - **Interactive Data Display**: Hover effects and detailed breakdowns for enhanced user engagement
 *
 * ## Data Processing Logic
 * - **Year Calculation**: Dynamically calculates current year and generates 5-year range (e.g., 2020-2024)
 * - **Promise Filtering**: Filters promises by `promise_doc_year` from the model data structure
 * - **Status Mapping**: Maps boolean and null values to kept (true), broken (false), uncertain (null)
 * - **Performance Optimization**: Calculates maximum values for proper chart scaling and visual proportion
 * - **Empty State Handling**: Gracefully handles scenarios with no promise data or zero totals
 *
 * ## Visual Design & Chart Components
 * - **Stacked Horizontal Bars**: Color-coded segments representing different promise statuses
 *   - Green (#10b981): Kept promises with success styling
 *   - Red (#ef4444): Broken promises with destructive styling
 *   - Yellow (#eab308): Uncertain promises with warning styling
 * - **Year Labels**: Clear year identifiers with total promise counts for context
 * - **Legend System**: Inline legends for each year showing exact counts per category
 * - **Summary Statistics**: Prominent bottom section with aggregate totals across all years
 * - **Glass-morphism Styling**: Follows project design system with rounded corners and consistent spacing
 *
 * ## Chart Data Structure & Processing
 * - **YearData Interface**: Structured data containing year, kept, broken, uncertain, and total counts
 * - **Dynamic Scaling**: Calculates maximum values for proportional bar width rendering
 * - **Percentage Calculations**: Converts counts to percentages for accurate stacked bar visualization
 * - **Performance Metrics**: Optimized calculations for chart rendering and data processing efficiency
 * - **Responsive Design**: Adapts to different screen sizes while maintaining readability
 *
 * ## Database Integration & Data Flow
 * The component integrates with the customer database through:
 * - **xfer_promises Table**: Core promise data synchronized from analytics backend
 * - **Model JSONB Field**: Contains XferPromiseModel with promise_doc_year for temporal filtering
 * - **Promise Status Fields**: Uses `kept` boolean field for status categorization
 * - **Performance Columns**: Leverages extracted fields for efficient querying and filtering
 * - **Data Synchronization**: Receives processed promise data via data sync layer from analytics database
 *
 * ## Props Interface & Component Architecture
 * - **PromisesFiveYearChartProps**: Accepts promisesData array of PromiseTypeV2 objects or null/undefined
 * - **Type Safety**: Full TypeScript coverage with proper null/undefined handling
 * - **Error Boundaries**: Graceful degradation for missing or malformed data
 * - **Testing Support**: Comprehensive data-testid attributes for Playwright test automation
 * - **Performance Optimization**: Minimal re-renders and efficient data processing patterns
 *
 * ## System Integration Context
 * This component fits into the broader ESG promise tracking ecosystem:
 * - **Promise Dashboard**: Primary visualization component within promises overview page
 * - **Analytics Backend**: Receives processed promise data from Python analytics pipeline
 * - **Data Pipeline**: Part of promise analysis and assessment workflow processing
 * - **Dashboard Navigation**: Integrated with promise management and assessment interfaces
 * - **Reporting System**: Contributes to comprehensive ESG reporting and stakeholder communication
 * - **Trend Analysis**: Supports long-term pattern recognition and corporate accountability tracking
 *
 * ## Performance Considerations & Optimization
 * - **Data Filtering Efficiency**: Uses filter operations on pre-processed arrays for optimal performance
 * - **Chart Scaling Logic**: Calculates maximum values once for consistent visual proportions
 * - **Conditional Rendering**: Avoids unnecessary DOM operations for empty states or missing data
 * - **Memory Management**: Efficient array operations and minimal object creation during processing
 * - **Responsive Calculations**: Dynamic percentage calculations for accurate stacked bar visualization
 *
 * ## Key Dependencies & Technical Stack
 * - **React 18+**: Modern functional component with hooks for state and effect management
 * - **Next.js 15 App Router**: Client-side rendering with optimized performance characteristics
 * - **TypeScript**: Full type safety with PromiseTypeV2 interface and proper error handling
 * - **Tailwind CSS**: Utility-first styling with responsive design and glass-morphism effects
 * - **Glass-morphism Design**: Consistent with project-wide design system and visual hierarchy
 *
 * @see https://react.dev/learn/rendering-lists React List Rendering Documentation
 * @see https://nextjs.org/docs/app/building-your-application/rendering/client-components Next.js Client Components
 * @see https://tailwindcss.com/docs/responsive-design Tailwind CSS Responsive Design
 * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/filter Array.filter() Documentation
 * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from Array.from() Documentation
 * @see {@link ../../../../types/promise.ts} PromiseTypeV2 and XferPromiseModel Types
 * @see {@link ../page.tsx} Promises Dashboard Page Integration
 * <AUTHOR>
 * @updated 2025-07-22
 * @description React component that renders a comprehensive 5-year trend visualization for ESG corporate promise tracking with horizontal stacked bar charts and aggregate statistics.
 * @example ```tsx
  // Display 5-year promise trends
  <PromisesFiveYearChart promisesData={promiseAnalysisData} />
  
  // Handle empty state gracefully
  <PromisesFiveYearChart promisesData={null} />
  
  // Integration in promises dashboard
  {promiseData && (
    <PromisesFiveYearChart promisesData={promiseData} />
  )}
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client';

import React from 'react';
import { PromiseTypeV2 } from '@/types';

interface PromisesFiveYearChartProps {
    promisesData: PromiseTypeV2[] | undefined | null;
}

interface YearData {
    year: number;
    kept: number;
    broken: number;
    uncertain: number;
    total: number;
}

export function PromisesFiveYearChart({ promisesData }: PromisesFiveYearChartProps) {
    if (!promisesData || promisesData.length === 0) {
        return (
            <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promises-five-year-chart">
                <h3 className="text-lg font-semibold mb-4">Promise Trends (Last 5 Years)</h3>
                <p className="text-sm text-muted-foreground text-center py-8">
                    No promise data available
                </p>
            </div>
        );
    }

    // Get the last 5 years
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 5 }, (_, i) => currentYear - i).reverse();

    // Group promises by year and calculate statistics
    const yearlyData: YearData[] = years.map(year => {
        const yearPromises = promisesData.filter(promise => 
            promise.model.promise_doc_year === year
        );

        const kept = yearPromises.filter(p => p.kept === true).length;
        const broken = yearPromises.filter(p => p.kept === false).length;
        const uncertain = yearPromises.filter(p => p.kept === null).length;

        return {
            year,
            kept,
            broken,
            uncertain,
            total: kept + broken + uncertain
        };
    });

    // Calculate maximum value for scaling
    const maxValue = Math.max(...yearlyData.map(d => d.total));
    const maxBarValue = Math.max(...yearlyData.map(d => Math.max(d.kept, d.broken, d.uncertain)));

    if (maxValue === 0) {
        return (
            <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promises-five-year-chart">
                <h3 className="text-lg font-semibold mb-4">Promise Trends (Last 5 Years)</h3>
                <p className="text-sm text-muted-foreground text-center py-8">
                    No promise data available for the last 5 years
                </p>
            </div>
        );
    }

    return (
        <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promises-five-year-chart">
            <h3 className="text-lg font-semibold mb-4">Promise Trends (Last 5 Years)</h3>
            
            {/* Bar Chart */}
            <div className="space-y-6">
                {yearlyData.map((data) => (
                    <div key={data.year} className="space-y-2">
                        <div className="flex justify-between items-center">
                            <span className="font-medium text-sm">{data.year}</span>
                            <span className="text-xs text-muted-foreground">
                                Total: {data.total}
                            </span>
                        </div>
                        
                        {/* Stacked horizontal bar */}
                        <div className="relative w-full h-8 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden chart-bar" data-testid={`chart-bar-${data.year}`}>
                            {data.total > 0 && (
                                <>
                                    {/* Kept promises (green) */}
                                    <div
                                        className="absolute left-0 top-0 h-full bg-green-500 transition-all duration-300"
                                        style={{ width: `${(data.kept / data.total) * 100}%` }}
                                        data-testid={`chart-bar-kept-${data.year}`}
                                    />
                                    {/* Broken promises (red) */}
                                    <div
                                        className="absolute top-0 h-full bg-red-500 transition-all duration-300"
                                        style={{
                                            left: `${(data.kept / data.total) * 100}%`,
                                            width: `${(data.broken / data.total) * 100}%`
                                        }}
                                        data-testid={`chart-bar-broken-${data.year}`}
                                    />
                                    {/* Uncertain promises (yellow) */}
                                    <div
                                        className="absolute top-0 h-full bg-yellow-500 transition-all duration-300"
                                        style={{
                                            left: `${((data.kept + data.broken) / data.total) * 100}%`,
                                            width: `${(data.uncertain / data.total) * 100}%`
                                        }}
                                        data-testid={`chart-bar-uncertain-${data.year}`}
                                    />
                                </>
                            )}
                        </div>
                        
                        {/* Legend for this year */}
                        <div className="flex justify-between text-xs text-muted-foreground">
                            <div className="flex items-center gap-4">
                                <div className="flex items-center gap-1">
                                    <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
                                    <span>Kept: {data.kept}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <div className="w-3 h-3 bg-red-500 rounded-sm"></div>
                                    <span>Broken: {data.broken}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <div className="w-3 h-3 bg-yellow-500 rounded-sm"></div>
                                    <span>Uncertain: {data.uncertain}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Summary statistics */}
            <div className="mt-6 pt-4 border-t border-border">
                <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="space-y-1" data-testid="total-kept">
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                            {yearlyData.reduce((sum, d) => sum + d.kept, 0)}
                        </div>
                        <div className="text-xs text-muted-foreground">Total Kept</div>
                    </div>
                    <div className="space-y-1" data-testid="total-broken">
                        <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                            {yearlyData.reduce((sum, d) => sum + d.broken, 0)}
                        </div>
                        <div className="text-xs text-muted-foreground">Total Broken</div>
                    </div>
                    <div className="space-y-1" data-testid="total-uncertain">
                        <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                            {yearlyData.reduce((sum, d) => sum + d.uncertain, 0)}
                        </div>
                        <div className="text-xs text-muted-foreground">Total Uncertain</div>
                    </div>
                </div>
            </div>
        </div>
    );
}
