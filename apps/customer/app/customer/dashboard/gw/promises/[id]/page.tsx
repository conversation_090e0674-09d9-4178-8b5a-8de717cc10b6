/**
 * Next.js Dynamic Route Page for ESG Promise Detail Display
 *
 * This file implements a client-side React page component that displays detailed
 * ESG (Environmental, Social, Governance) promise tracking and fulfillment analysis.
 * It serves as the main promise detail view in the customer dashboard, fetching promise
 * data from the Supabase database and rendering comprehensive promise analysis including
 * fulfillment status, greenwashing assessment, confidence metrics, supporting evidence,
 * and AI-powered verdict analysis.
 *
 * ## Core Functionality
 * - **Promise Data Retrieval**: Fetches detailed promise information from `xfer_promises` table
 * - **Fulfillment Analysis Display**: Shows promise kept/broken/uncertain status with confidence scores
 * - **Evidence Visualization**: Displays supporting evidence and documentation for promise verification
 * - **Navigation Integration**: Updates breadcrumb navigation and page title dynamically
 * - **Authentication Awareness**: Provides admin-specific features based on user permissions
 * - **Type Safety**: Uses TypeScript with PromiseTypeV2 interface for robust data handling
 * - **Loading State Management**: Graceful loading indicators while data is being fetched
 *
 * ## Request Parameters
 * - **Route Parameter**: `id` - Numeric promise identifier from the database
 *
 * ## Data Flow
 * 1. Extract promise ID from route parameters using Next.js `useParams()` hook
 * 2. Query Supabase `xfer_promises` table for promise data by ID with single record lookup
 * 3. Update navigation context with promise-specific breadcrumb path and page title
 * 4. Render PromiseCardV2 component with fetched data and admin permissions
 * 5. Display loading state until data is available or error occurs
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with client-side routing and dynamic routes
 * - **Supabase Client**: Database access with Row Level Security policies and real-time features
 * - **React 18+**: Latest React features including hooks (`useState`, `useEffect`) and strict mode
 * - **Navigation Context**: Custom navigation system for breadcrumb management and page titles
 * - **Authentication Context**: User authentication and role-based access control (admin features)
 *
 * ## Related Components
 * - PromiseCardV2: Main promise display component with comprehensive analysis view and evidence display
 * - Navigation Context: Breadcrumb and page title management system with route-aware updates
 * - Authentication Context: User session and permission management with admin role detection
 * - Supabase Client: Database connection and query execution with RLS security
 *
 * ## Database Schema Integration
 * - Connects to customer Supabase database via Row Level Security protected queries
 * - Uses `xfer_promises` table for promise data synchronized from analytics database
 * - Handles JSON model data casting from database to TypeScript PromiseTypeV2 interface
 * - Supports error handling for non-existent or inaccessible promises with console logging
 * - Accesses composite JSONB `model` field containing: text, confidence, summary, conclusion,
 *   verdict, evidence, citations, context, and analysis metadata
 *
 * ## Promise Analysis System
 * This page displays results from the comprehensive ESG promise tracking system:
 * - **Backend Analytics**: Python system generates promise analysis using Claims vs Evidence pipeline
 * - **Data Sync Layer**: `xfer_promises` table synchronizes processed data to customer database
 * - **API Layer**: This page provides customer-facing access to promise detail analysis
 * - **Frontend**: Customer dashboard consumes this data for promise tracking and verification display
 * - **AI Analysis**: Displays AI-powered verdicts on promise fulfillment with confidence scoring
 *
 * ## Security & Performance
 * - Database access through Supabase RLS (Row Level Security) policies for tenant isolation
 * - Client-side rendering for dynamic content with loading states and error boundaries
 * - Type-safe database interactions prevent runtime errors with TypeScript compilation
 * - Efficient single-record queries with specific ID lookup and database indexing
 * - Error handling with structured console error logging for development debugging
 *
 * ## Navigation Architecture
 * - **Route**: `/customer/dashboard/gw/promises/[id]`
 * - **Dynamic Segment**: `[id]` captures numeric promise ID parameter
 * - **Breadcrumb Path**: Dashboard → Promises → [Promise ID]
 * - **Page Title**: "Promise ([ID])" format for browser tab and navigation context
 * - **Parent Routes**: Integrates with promises list page and dashboard navigation system
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Guide
 * @see https://react.dev/reference/react/hooks React Hooks Documentation
 * @see {@link ../promise.tsx} PromiseCardV2 Component
 * @see {@link ../../../../../types.ts} PromiseTypeV2 Interface
 * @see {@link ../../../../../app/supabase/client.ts} Supabase Client Configuration
 * <AUTHOR>
 * @updated 2025-07-22
 * @description ESG promise detail page that fetches and displays comprehensive promise tracking including fulfillment analysis, evidence visualization, and AI-powered verdict assessment
 * @example ```bash
 * curl -X GET 'http://localhost:3000/customer/dashboard/gw/promises/12345'
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";

import { createClient } from '@/app/supabase/client'
import { PromiseCardV2 } from '../promise'
import { useEffect, useState } from 'react'
import { useAuth } from '@/components/context/auth/auth-context'
import { PromiseTypeV2 } from '@/types'
import { useParams } from 'next/navigation'
import { useNav } from '@/components/context/nav/nav-context'

export default function Page() {
    const [data, setData] = useState<PromiseTypeV2 | null>(null);
    const auth = useAuth();
    const id = useParams().id!;
    const nav = useNav();

    useEffect(() => {
        const fetchData = async () => {
            const supabase = createClient();

            // Get promise from xfer_promises
            const {
                data: promiseV2Data,
                error: promiseV2Error
            } = await supabase
              .from('xfer_promises')
                .select("*")
                .eq("id", +id)
                .single();

            if (promiseV2Error) {
                console.error("Error fetching promise:", promiseV2Error);
                return;
            }

            if (promiseV2Data) {
                // Cast to unknown first to avoid TypeScript errors with Json type
                setData(promiseV2Data as unknown as PromiseTypeV2);
                nav.changeTitle("Promise (" + promiseV2Data.id + ")");
                nav.changeNavPath([
                    {label: "Dashboard", href: "/customer/dashboard"},
                    {label: "Promises", href: "/customer/dashboard/gw/promises"},
                    {label: ""+promiseV2Data.id}
                ]);
            }
        };

        fetchData();
    }, [id]);

    return data ? <PromiseCardV2 item={data} admin={auth.admin}/> : <p>Loading...</p>;
}
