/**
 * Promise Card React Component - ESG Corporate Promise Analysis Display
 *
 * This React component renders individual ESG (Environmental, Social, Governance) promise assessments 
 * as detailed cards showing promise fulfillment status, analysis, evidence, and supporting documentation.
 * Each card represents a corporate promise that has been evaluated against historical evidence to determine
 * whether the commitment was kept, broken, or remains uncertain with detailed AI-generated analysis.
 *
 * ## Core Functionality
 * - **Promise Status Visualization**: Clear visual indicators (✓, ✗, ?) showing whether promises were kept, broken, or uncertain
 * - **Detailed Analysis Display**: Comprehensive promise evaluation including summary, conclusion, and detailed assessment
 * - **Evidence Integration**: Supporting evidence with fulfillment indicators and temporal context
 * - **Citation Management**: Source document references with clickable links and page numbers
 * - **Confidence Metrics**: Numerical confidence scores indicating assessment reliability
 * - **Administrative Controls**: Delete functionality for administrators with proper permissions
 *
 * ## Promise Assessment Data Structure
 * - **Promise Text**: The original corporate commitment or statement being evaluated
 * - **Verdict Analysis**: AI-generated detailed assessment of promise fulfillment with markdown formatting
 * - **Summary & Conclusion**: Concise overview and final determination of promise status
 * - **Evidence Objects**: Supporting evidence entries with fulfillment indicators and analysis
 * - **Citation References**: Document sources with authors, years, and page-specific links
 * - **ESG Classification**: Flag indicating if promise relates to Environmental, Social, or Governance commitments
 *
 * ## Visual Design & Status Indicators
 * - **Status Banner**: Color-coded header with appropriate icons and confidence percentage display
 *   - Green (kept): CheckCircle2 icon with success styling for fulfilled promises
 *   - Red (broken): CircleXIcon with destructive styling for unfulfilled promises  
 *   - Yellow (uncertain): HelpCircle with warning styling for ambiguous outcomes
 * - **Card Layout**: Glass-morphism design following project design system with hover effects
 * - **Typography Hierarchy**: Clear section headers, emphasized promise text, and structured content
 * - **Badge System**: ESG promise indicators and confidence metrics with appropriate variants
 *
 * ## Evidence Processing & Display
 * - **Evidence Parsing**: Handles both object and string evidence formats from model data
 * - **Evidence Limitation**: Shows top 3 evidence pieces with overflow indicator for performance
 * - **Fulfillment Indicators**: Clear badges showing whether evidence supports or contradicts the promise
 * - **Temporal Context**: Year information for evidence timing and document context
 * - **Analysis Integration**: Evidence-specific analysis and summary text from AI evaluation
 *
 * ## Database Integration
 * The component connects to the customer Supabase database via:
 * - **xfer_promises Table**: Core promise data with id, entity_xid, run_id, kept status
 * - **Model JSONB Field**: Contains XferPromiseModel with detailed promise analysis
 * - **Performance Columns**: Extracted summary, conclusion, statement_text for query optimization
 * - **Citation Data**: Embedded CitationType objects with document references and URLs
 * - **Evidence Structure**: Complex evidence objects with fulfillment indicators and analysis
 *
 * ## Component Architecture
 * - **Props Interface**: Accepts PromiseTypeV2 item and admin boolean for permission control
 * - **Destructuring Pattern**: Extracts model data and first citation for display optimization
 * - **Conditional Rendering**: Handles missing data gracefully with appropriate fallbacks
 * - **Testing Support**: Includes data-testid attributes for comprehensive Playwright testing
 * - **Admin Features**: Delete button with proper permissions and table-specific operations
 *
 * ## System Integration Context
 * This component fits into the broader ESG promise tracking system:
 * - **Analytics Backend**: Python backend analyzes promises and generates detailed assessments
 * - **Data Synchronization**: xfer_promises table syncs processed data from analytics database
 * - **Promise Dashboard**: Component renders within promise management interface
 * - **Modal Displays**: Used in both list views and detailed modal overlays
 * - **Evidence Pipeline**: Connects to evidence analysis and claim verification systems
 *
 * ## Performance Considerations
 * - **Evidence Limiting**: Shows only top 3 evidence pieces to prevent UI overflow
 * - **Citation Optimization**: Uses first citation for quick author/document display
 * - **Conditional Rendering**: Avoids unnecessary DOM operations for missing data
 * - **Model Data Access**: Direct access to nested model properties without excessive processing
 * - **Markdown Processing**: EkoMarkdown component handles complex verdict formatting efficiently
 *
 * ## Key Dependencies
 * - **React 18+**: Modern React with functional components and hooks
 * - **Next.js 15**: Client-side rendering with project routing integration
 * - **ShadCN/UI Components**: Card, Badge, and other UI primitives for consistent styling
 * - **Lucide React Icons**: Status icons (CheckCircle2, CircleXIcon, HelpCircle)
 * - **EkoMarkdown**: Custom markdown renderer with citation support and admin controls
 * - **AdminDeleteButton**: Specialized delete control with proper permissions and RLS
 *
 * @see https://react.dev/learn/passing-props-to-a-component React Props Documentation
 * @see https://nextjs.org/docs/app/building-your-application/rendering/client-components Next.js Client Components
 * @see https://ui.shadcn.com/docs/components/card ShadCN/UI Card Component
 * @see https://ui.shadcn.com/docs/components/badge ShadCN/UI Badge Component
 * @see https://lucide.dev/icons/ Lucide React Icons
 * @see {@link ../../../types/promise.ts} PromiseTypeV2 and XferPromiseModel Types
 * @see {@link ../../../components/citation.tsx} CitationType Definition
 * @see {@link ../../../components/markdown/eko-markdown.tsx} EkoMarkdown Component
 * @see {@link ../../../components/admin.tsx} AdminDeleteButton Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description React component that renders detailed ESG corporate promise assessments with status indicators, evidence, and supporting documentation.
 * @example ```tsx
  // Display a promise assessment card
  <PromiseCardV2 item={promiseData} admin={isAdmin} />
  
  // In a promise list context
  {promises.map(promise => (
    <PromiseCardV2 key={promise.id} item={promise} admin={user?.is_admin} />
  ))}
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CitationType } from '@/components/citation'
import React from 'react'
import { PromiseTypeV2 } from '@/types'
import { CheckCircle2, CircleXIcon, HelpCircle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { AdminDeleteButton } from '@/components/admin'

// New component that works directly with PromiseTypeV2
export function PromiseCardV2({item, admin}: { item: PromiseTypeV2, admin: boolean }) {
    const model = item.model;

    // Get the first citation if available
    const firstCitation = model.citations && model.citations.length > 0 ? model.citations[0] : null;
    const authors = firstCitation?.authors?.join(", ") || '';
    const docTitle = firstCitation?.title || model.promise_doc || '';
    const docYear = firstCitation?.year || model.promise_doc_year || new Date().getFullYear();
    const docUrl = firstCitation?.public_url || '';

    // Determine the promise status icon and color
    let statusIcon;
    let statusColor;
    let statusText;

    if (item.kept === true) {
        statusIcon = <CheckCircle2 className="w-8 h-8 inline-block"/>;
        statusColor = "bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800";
        statusText = "Promise Kept";
    } else if (item.kept === false) {
        statusIcon = <CircleXIcon className="w-8 h-8 inline-block"/>;
        statusColor = "bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800";
        statusText = "Promise Not Kept";
    } else {
        statusIcon = <HelpCircle className="w-8 h-8 inline-block"/>;
        statusColor = "bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800";
        statusText = "Status Uncertain";
    }

    // Parse evidence if available
    const evidence = typeof model.evidence === 'object' ? model.evidence : {};
    const evidenceEntries = Object.entries(evidence);

    return (
        <Card key={item.id} className="mb-6 overflow-hidden relative group">
            <AdminDeleteButton
              tableName="xfer_promises"
                recordId={item.id}
                recordType="promise"
            />
            {/* Status Banner */}
            <div className={`w-full p-2 flex items-center justify-between ${statusColor} border-b`}>
                <div className="flex items-center gap-2" data-testid="modal-promise-status">
                    <span className={`${item.kept === true ? 'text-green-500' : item.kept === false ? 'text-red-500' : 'text-yellow-500'}`}>
                        {statusIcon}
                    </span>
                    <span className="font-semibold">{statusText}</span>
                </div>
                <Badge variant="secondary">
                    Confidence: {model.confidence}%
                </Badge>
            </div>

            <CardHeader className="pb-2">
                <CardTitle className="text-xl flex items-start gap-2" data-testid="modal-promise-title">
                    <div className="flex-1">
                        <span className="text-sm text-muted-foreground block mb-1">The Promise:</span>
                        "<em>{model.text}"</em>
                    </div>
                </CardTitle>
                <CardDescription>
                    <div className="flex justify-between items-center mt-2">
                        <div>
                            <span className="text-sm font-medium">Made in {docYear}</span>
                            {model.esg_promise &&
                                <Badge variant="outline" className="ml-2">ESG Promise</Badge>
                            }
                        </div>
                        <div>
                            {docUrl ? (
                                <a href={docUrl} target="_blank" rel="noopener noreferrer" className="text-sm hover:underline">
                                    {authors && <span>{authors}, </span>}
                                    {docTitle} ({docYear})
                                </a>
                            ) : (
                                <span className="text-sm">
                                    {authors && <span>{authors}, </span>}
                                    {docTitle} ({docYear})
                                </span>
                            )}
                        </div>
                    </div>
                </CardDescription>
            </CardHeader>

            <CardContent>
                {/* Summary Section */}
                <div className="mb-6 p-4 border rounded-md bg-slate-50 dark:bg-slate-900" data-testid="modal-promise-content">
                    <h3 className="text-lg font-semibold mb-2">Summary</h3>
                    <p>{model.summary}</p>
                </div>

                {/* Conclusion Section */}
                <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-2">Conclusion</h3>
                    <div className="p-4 border rounded-md">
                        <p className="font-medium">{model.conclusion}</p>
                    </div>
                </div>

                {/* Detailed Analysis */}
                <div className="mb-6" data-testid="modal-promise-assessment">
                    <h3 className="text-lg font-semibold mb-2">Detailed Analysis</h3>
                    <div className="p-4 border rounded-md">
                        <EkoMarkdown
                            citations={model.citations as unknown as CitationType[]}
                            admin={admin}
                        >
                            {model.verdict}
                        </EkoMarkdown>
                    </div>
                </div>

                {/* Evidence Section */}
                {evidenceEntries.length > 0 && (
                    <div className="mb-4">
                        <h3 className="text-lg font-semibold mb-2">Supporting Evidence</h3>
                        <div className="space-y-3">
                            {evidenceEntries.slice(0, 3).map(([key, value]: [string, any], index) => (
                                <div key={key} className="p-3 border rounded-md">
                                    <div className="flex justify-between mb-2">
                                        <span className="font-medium">Evidence {index + 1}</span>
                                        {value.doc_year && (
                                            <Badge variant="outline">{value.doc_year}</Badge>
                                        )}
                                    </div>
                                    <p className="text-sm mb-2">{value.text}</p>
                                    {value.summary && (
                                        <div className="text-sm text-muted-foreground">
                                            <span className="font-medium">Analysis: </span>
                                            {value.summary}
                                        </div>
                                    )}
                                    {value.fulfills !== undefined && (
                                        <Badge
                                            variant={value.fulfills ? "default" : "destructive"}
                                            className="mt-2"
                                        >
                                            {value.fulfills ? "Supports Promise" : "Does Not Support Promise"}
                                        </Badge>
                                    )}
                                </div>
                            ))}

                            {evidenceEntries.length > 3 && (
                                <p className="text-sm text-muted-foreground text-center">
                                    + {evidenceEntries.length - 3} more pieces of evidence
                                </p>
                            )}
                        </div>
                    </div>
                )}

                {/* Context Section if available */}
                {model.context && (
                    <div className="mt-4 p-4 border rounded-md bg-slate-50 dark:bg-slate-900">
                        <h3 className="text-lg font-semibold mb-2">Additional Context</h3>
                        <p className="text-sm">{model.context}</p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
