/**
 * Next.js Client-Side ESG Promise Dashboard - Corporate Commitment Tracking Interface
 *
 * This page provides a comprehensive dashboard for viewing, analyzing, and tracking ESG (Environmental, Social,
 * Governance) promises made by corporate entities. It displays promise fulfillment analytics with advanced
 * filtering, sorting, and visualization capabilities including five-year trend analysis, timeline views,
 * and promise assessment history tracking.
 *
 * ## Core Functionality
 * - **Promise Tracking Dashboard**: Comprehensive view of corporate ESG promises with kept/broken/uncertain status
 * - **Advanced Filtering**: Filter promises by fulfillment status (all, kept, broken) with real-time updates
 * - **Multi-Criteria Sorting**: Sort by relevance (confidence), date (promise_doc_year), or fulfillment status
 * - **Promise Summary Analytics**: Display total counts of kept, broken, and uncertain promises with color-coded indicators
 * - **Five-Year Trend Visualization**: Horizontal stacked bar chart showing promise performance over last 5 years
 * - **Timeline View**: Chronological promise visualization grouped by year with status indicators
 * - **Assessment History**: Historical analysis of promise evaluation and tracking over time
 * - **Individual Promise Details**: Detailed cards with confidence scores, evidence, and citation information
 *
 * ## Data Processing & State Management
 * - **Promise Data**: Fetched from EntityContext which queries `xfer_promises` table via Supabase RLS policies
 * - **Filtering Logic**: Real-time filtering based on `kept` boolean field (true/false/null for uncertain)
 * - **Sorting Algorithm**: Multi-criteria sorting with date fallback (most recent first), status prioritization, and confidence-based relevance
 * - **Loading States**: Graceful loading indicators while promise data is being fetched from backend
 * - **Navigation Integration**: Breadcrumb updates and page title management through navigation context
 *
 * ## User Interface Components
 * - **Glass-Morphism Design**: Translucent, rounded UI elements consistent with application design system
 * - **Interactive Filters**: Dropdown selectors for status filtering and sorting with immediate visual feedback
 * - **Summary Cards**: Color-coded statistics showing kept (green), broken (red), and uncertain (yellow) promises
 * - **PromisesFiveYearChart**: Horizontal stacked bar visualization showing promise trends across recent years
 * - **PromisesTimeline**: Chronological view with timeline dots and promise grouping by document year
 * - **PromiseAssessmentHistory**: Historical tracking of promise evaluation and analysis over time
 * - **PromisesListV2**: Card-based list view with individual promise details, confidence indicators, and navigation links
 *
 * ## Request Parameters & Routing
 * - **Entity Context**: Reads entity ID, run ID, model selection from URL query parameters via EntityProvider
 * - **Navigation Context**: Updates breadcrumb path to show "Dashboard → Promises" structure
 * - **Query String Preservation**: Maintains entity-specific parameters when navigating to individual promise details
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with client-side rendering and component-based architecture
 * - **React 18+**: Latest React features including hooks (useState, useEffect, useMemo), strict mode, and concurrent features
 * - **EntityContext**: Custom React context providing promise data, loading states, and entity-specific information
 * - **Navigation Context**: Breadcrumb management and page title system with route-aware updates
 * - **Authentication Context**: User session and admin permission management for admin-specific features
 * - **Tailwind CSS & ShadCN/UI**: Utility-first styling with pre-built UI components (Select, Badge, etc.)
 * - **Lucide React Icons**: Modern icon set for status indicators and UI enhancement
 *
 * ## Database Schema Integration
 * The page connects to the customer Supabase database accessing:
 * - **xfer_promises table**: Synchronized promise data from analytics database containing:
 *   - `id`: Promise identifier for detail page navigation
 *   - `entity_xid`: Entity identifier for filtering and association  
 *   - `run_id`: Analysis run identifier for versioning
 *   - `kept`: Boolean promise fulfillment status (true/false/null)
 *   - `model`: JSONB field containing promise analysis, confidence, evidence, citations
 *   - `summary`: Extracted promise summary for performance optimization
 *   - `statement_text`: Original promise statement text
 *
 * ## Related Components & System Architecture
 * This page integrates with the broader ESG analysis system:
 * - **Analytics Backend**: Python system generates promise analysis and stores in analytics database
 * - **Data Sync Layer**: `xfer_promises` table synchronizes data between analytics and customer databases  
 * - **Promise Detail Pages**: Individual promise analysis views accessible via card navigation
 * - **Entity Dashboard**: Parent dashboard providing entity selection and overview context
 * - **Comparative Analysis**: Links to claims analysis and other ESG assessment tools
 *
 * ## Performance Optimizations
 * - **useMemo Hook**: Optimized filtering and sorting with dependency-based recalculation
 * - **Entity Context Caching**: Shared promise data across components to minimize database queries
 * - **Supabase RLS**: Row-level security policies ensure efficient, secure data access
 * - **Loading State Management**: Progressive loading with skeleton states for improved perceived performance
 * - **Component Lazy Loading**: Efficient rendering of large promise datasets with virtualization considerations
 *
 * ## Security & Access Control
 * - **Row Level Security**: Supabase RLS policies ensure users only access authorized entity data
 * - **Authentication Integration**: Admin-specific features (delete buttons, advanced controls) based on user role
 * - **Query Parameter Validation**: Entity and run ID validation through EntityContext
 * - **XSS Prevention**: React's built-in XSS protection with secure rendering of user-generated content
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages Next.js App Router Pages
 * @see https://react.dev/reference/react/hooks React Hooks Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Database
 * @see https://tailwindcss.com/docs/utility-first Tailwind CSS Utility Classes
 * @see {@link ./promises-list.tsx} PromisesListV2 Component
 * @see {@link ./promises-timeline.tsx} PromisesTimeline Component  
 * @see {@link ./promises-five-year-chart.tsx} PromisesFiveYearChart Component
 * @see {@link ./promise-assessment-history.tsx} PromiseAssessmentHistory Component
 * @see {@link ../../../../components/context/entity/entity-context.tsx} EntityContext Provider
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Comprehensive ESG promise dashboard for corporate commitment tracking with advanced filtering, sorting, and visualization capabilities including five-year trends and timeline views.
 * @example
 * ```typescript
 * // Page accessible via:
 * // /customer/dashboard/gw/promises?entity=ENTITY_ID&run=latest&model=ekoIntelligence
 * // Displays promise dashboard with filtering and visualization components
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'
import React, { useEffect, useState, useMemo } from 'react'
import { PromisesListV2 } from './promises-list'
import { EkoPageTitle } from '@/components/page-title'
import NoData from '@/components/no-data'
import { useEntity } from '@/components/context/entity/entity-context'
import { useAuth } from '@/components/context/auth/auth-context'
import { useNav } from '@/components/context/nav/nav-context'
import { useNavigationWithParams } from '@/hooks/use-navigation-with-params'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'

import { PromisesTimeline } from './promises-timeline'
import { PromiseAssessmentHistory } from './promise-assessment-history'
import { PromisesFiveYearChart } from './promises-five-year-chart'

export default function Page() {
    const entityContext = useEntity();
    const auth = useAuth();
    const nav = useNav();
    const navWithParams = useNavigationWithParams();
    const [statusFilter, setStatusFilter] = useState<'all' | 'kept' | 'broken'>('all');
    const [sortBy, setSortBy] = useState<'date' | 'relevance' | 'status'>('relevance');

    useEffect(() => {
        nav.changeNavPath(navWithParams.createNavItems([
            { label: "Dashboard", href: "/customer/dashboard" },
            { label: "Promises", href: "/customer/dashboard/gw/promises" }
        ]));
    }, [navWithParams.queryString]);

    // Filter and sort promises data
    const filteredAndSortedPromises = useMemo(() => {
        if (!entityContext.promisesData) return [];

        let filtered = [...entityContext.promisesData];

        // Apply status filter
        if (statusFilter === 'kept') {
            filtered = filtered.filter(promise => promise.kept === true);
        } else if (statusFilter === 'broken') {
            filtered = filtered.filter(promise => promise.kept === false);
        }

        // Apply sorting
        filtered.sort((a, b) => {
            if (sortBy === 'date') {
                const yearA = a.model.promise_doc_year || new Date().getFullYear();
                const yearB = b.model.promise_doc_year || new Date().getFullYear();
                return yearB - yearA; // Most recent first
            } else if (sortBy === 'status') {
                // Sort by kept status (kept first, then uncertain, then broken)
                if (a.kept === b.kept) return 0;
                if (a.kept === true) return -1;
                if (b.kept === true) return 1;
                if (a.kept === false) return 1;
                if (b.kept === false) return -1;
                return 0;
            } else {
                // Sort by relevance (confidence)
                return b.model.confidence - a.model.confidence;
            }
        });

        return filtered;
    }, [entityContext.promisesData, statusFilter, sortBy]);

    // Calculate counts for summary
    const keptCount = entityContext.promisesData?.filter(p => p.kept === true).length || 0;
    const brokenCount = entityContext.promisesData?.filter(p => p.kept === false).length || 0;
    const uncertainCount = entityContext.promisesData?.filter(p => p.kept === null).length || 0;

    // Show loading state while promises are being fetched
    if (entityContext.isLoadingPromises) {
        return <div className="container mx-auto p-4" data-testid="loading-promises">Loading promises...</div>;
    }

    return entityContext.promisesData && entityContext.promisesData.length > 0 ? (
        <div className="container mx-auto p-4" data-testid="promises-page-content">
            <EkoPageTitle title="Promises"/>
            
            {/* Filters Section */}
            <div className="mb-6 p-4 bg-muted/50 rounded-lg" data-testid="promises-filters">
                <div className="flex flex-wrap gap-4 items-center">
                    <div className="flex items-center gap-2">
                        <label className="text-sm font-medium">Status:</label>
                        <Select value={statusFilter} onValueChange={(value: 'all' | 'kept' | 'broken') => setStatusFilter(value)}>
                            <SelectTrigger className="w-[180px]" data-testid="promise-status-filter">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all" data-testid="filter-option-all-promises">All Promises</SelectItem>
                                <SelectItem value="kept" data-testid="filter-option-kept-promises">Kept Promises</SelectItem>
                                <SelectItem value="broken" data-testid="filter-option-broken-promises">Broken Promises</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    
                    <div className="flex items-center gap-2">
                        <label className="text-sm font-medium">Sort by:</label>
                        <Select value={sortBy} onValueChange={(value: 'date' | 'relevance' | 'status') => setSortBy(value)}>
                            <SelectTrigger className="w-[180px]" data-testid="promises-sort">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="relevance" data-testid="sort-by-relevance">Relevance</SelectItem>
                                <SelectItem value="date" data-testid="sort-by-date">Date</SelectItem>
                                <SelectItem value="status" data-testid="sort-by-status">Status</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>

            {/* Summary Section */}
            <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promises-summary">
                <h3 className="text-lg font-semibold mb-3">Promise Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                        <div className="text-3xl font-bold text-green-600 dark:text-green-400" data-testid="kept-promises-count">{keptCount}</div>
                        <div className="text-sm text-muted-foreground">Kept Promises</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
                        <div className="text-3xl font-bold text-red-600 dark:text-red-400" data-testid="broken-promises-count">{brokenCount}</div>
                        <div className="text-sm text-muted-foreground">Broken Promises</div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                        <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400" data-testid="uncertain-promises-count">{uncertainCount}</div>
                        <div className="text-sm text-muted-foreground">Uncertain</div>
                    </div>
                </div>
            </div>



            {/* Five Year Trends Chart */}
            <PromisesFiveYearChart promisesData={entityContext.promisesData} />

            {/* Promises Timeline */}
            <PromisesTimeline promisesData={entityContext.promisesData} />
            
            {/* Assessment History */}
            <PromiseAssessmentHistory promisesData={entityContext.promisesData} />
            
            <PromisesListV2 promisesData={filteredAndSortedPromises} admin={auth.admin}/>
        </div>
    ) : (
        <div data-testid="promises-page-content">
            <NoData title="No Promises" description="No promises found for this entity" dataTestId="no-promises-message" />
        </div>
    )
}
