/**
 * ESG Corporate Promise Timeline Visualization Component
 *
 * This React component renders a chronological timeline visualization of ESG (Environmental, Social, Governance)
 * corporate promises, organizing them by document year with clear visual status indicators and hierarchical
 * information display. The timeline provides users with a temporal view of promise evolution, showing fulfillment
 * status, promise clustering by year, and quick access to promise details within a cohesive timeline interface.
 *
 * ## Core Functionality
 * - **Chronological Organization**: Groups promises by document year in descending order (most recent first)
 * - **Visual Timeline**: Vertical timeline with connecting lines, status dots, and year-based clustering
 * - **Status Indicators**: Color-coded icons (green=kept, red=broken, yellow=uncertain) with intuitive visual design
 * - **Promise Aggregation**: Shows promise counts per year with badge indicators for quick overview
 * - **Content Preview**: Displays truncated promise text with "line-clamp-2" for consistent card heights
 * - **Smart Truncation**: Shows first 3 promises per year with expandable "+N more" indicators
 *
 * ## Timeline Design & Visual Structure
 * - **Vertical Timeline**: Central timeline spine with branching year nodes and promise entries
 * - **Year Markers**: Prominent year headings with calendar icons and promise count badges
 * - **Status Dots**: Circular timeline markers with primary border colors for visual continuity
 * - **Promise Cards**: Structured promise entries with left border styling and status icon alignment
 * - **Content Hierarchy**: Clear separation between year markers, promise counts, and individual promise details
 * - **Responsive Layout**: Mobile-first design with proper spacing and accessible touch targets
 *
 * ## Promise Data Processing
 * - **Year Extraction**: Uses `promise_doc_year` field from PromiseTypeV2.model with current year fallback
 * - **Chronological Sorting**: Promises sorted by document year (descending) for temporal relevance
 * - **Status Resolution**: Maps boolean `kept` field to appropriate icons and color schemes
 * - **Text Content**: Prioritizes `statement_text`, falls back to model.text, then summary for display
 * - **Grouping Logic**: Creates year-based buckets with promise aggregation for timeline organization
 * - **Empty State**: Graceful handling of null/undefined/empty promise datasets with early return
 *
 * ## Visual Status System
 * - **Promise Kept (kept=true)**: CheckCircle2 icon with green color scheme (text-green-600/dark:text-green-400)
 * - **Promise Broken (kept=false)**: CircleXIcon with red color scheme (text-red-600/dark:text-red-400)
 * - **Promise Uncertain (kept=null)**: HelpCircle icon with yellow scheme (text-yellow-600/dark:text-yellow-400)
 * - **Timeline Dots**: Consistent white/dark background with primary border for visual timeline continuity
 * - **Badge Variants**: Secondary badge variants for promise counts with proper contrast ratios
 * - **Dark Mode**: Full dark mode support with appropriate color adjustments for accessibility
 *
 * ## Component Architecture & Props
 * - **PromisesTimelineProps**: Interface defining component props with nullable PromiseTypeV2 array
 * - **Promise Type Safety**: Full TypeScript integration with PromiseTypeV2 interface from @/types
 * - **Conditional Rendering**: Early return pattern for empty/null datasets to optimize performance
 * - **Data Testability**: Comprehensive data-testid attributes for reliable Playwright end-to-end testing
 * - **Accessibility**: Semantic HTML structure with proper ARIA patterns and keyboard navigation support
 *
 * ## System Integration Context
 * This component integrates with the broader ESG promise tracking ecosystem:
 * - **Analytics Backend**: Python backend processes corporate documents and generates promise analysis
 * - **Data Synchronization**: Promise data flows from analytics database (ana_promises) to customer database (xfer_promises)
 * - **Promise Dashboard**: Timeline component renders within promise management interface with filtering capabilities
 * - **Entity Context**: Operates within entity-specific contexts preserving company, run, and temporal parameters
 * - **Database Schema**: Connects to xfer_promises table with JSONB model field containing detailed analysis
 * - **Promise Analysis Pipeline**: Displays results from Claims vs. Evidence analysis and Promise Assessment systems
 *
 * ## Performance Optimizations
 * - **Early Return**: Immediate return for empty datasets prevents unnecessary processing and rendering
 * - **Limited Display**: Shows maximum 3 promises per year to maintain interface responsiveness
 * - **Text Truncation**: CSS-based line clamping for consistent visual presentation without layout shifts
 * - **Efficient Grouping**: Single-pass promise grouping by year minimizes computational overhead
 * - **Conditional Icons**: Dynamic icon resolution based on promise status for efficient rendering
 * - **Memory Management**: Uses spread operator for array copying without mutating original data
 *
 * ## Key Dependencies
 * - **React 18+**: Modern React with functional components, hooks, and TypeScript integration
 * - **PromiseTypeV2**: TypeScript interface for promise data structure from customer database
 * - **ShadCN/UI Badge**: Consistent badge styling for promise counts and status indicators
 * - **Lucide React Icons**: Calendar, CheckCircle2, CircleXIcon, HelpCircle for timeline visualization
 * - **Tailwind CSS**: Utility-first styling for responsive design and dark mode support
 * - **CSS Line Clamp**: Text truncation utilities for consistent content presentation
 *
 * ## Testing & Quality Assurance
 * - **Data TestIDs**: Comprehensive test identifiers including promises-timeline, timeline-entry, timeline-date, timeline-event
 * - **Playwright Integration**: Component designed for reliable end-to-end testing with stable selectors
 * - **Empty State Testing**: Proper handling of null, undefined, and empty array scenarios
 * - **Visual Regression**: Timeline layout remains consistent across different promise counts and content lengths
 * - **Accessibility Testing**: Component structure supports screen readers and keyboard navigation
 *
 * @see https://react.dev/reference/react/useState React Functional Components
 * @see https://ui.shadcn.com/docs/components/badge ShadCN/UI Badge Component
 * @see https://lucide.dev/icons/ Lucide React Icon Library
 * @see https://tailwindcss.com/docs/line-clamp Tailwind CSS Line Clamp Utilities
 * @see https://www.postgresql.org/docs/current/datatype-json.html PostgreSQL JSONB Data Type
 * @see {@link ../../../types/promise.ts} PromiseTypeV2 Interface Definition
 * @see {@link ../../../components/ui/badge.tsx} Badge Component Implementation
 * @see {@link ../../../../tmp/db/customer/schemas/public/tables/xfer_promises.sql} Database Schema
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Chronological timeline visualization of ESG corporate promises with status indicators, year-based grouping, and temporal organization for promise fulfillment tracking.
 * @example ```tsx
  // Display promises in timeline format
  <PromisesTimeline promisesData={entityPromises} />
  
  // Handle empty state gracefully
  <PromisesTimeline promisesData={[]} />
  
  // Integration with entity context
  {entity.promisesData && (
    <PromisesTimeline promisesData={entity.promisesData} />
  )}
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client';

import React from 'react';
import { PromiseTypeV2 } from '@/types';
import { Badge } from '@/components/ui/badge';
import { CheckCircle2, CircleXIcon, HelpCircle, Calendar } from 'lucide-react';

interface PromisesTimelineProps {
    promisesData: PromiseTypeV2[] | undefined | null;
}

export function PromisesTimeline({ promisesData }: PromisesTimelineProps) {
    if (!promisesData || promisesData.length === 0) {
        return null;
    }

    // Sort promises by year (most recent first)
    const sortedPromises = [...promisesData].sort((a, b) => {
        const yearA = a.model.promise_doc_year || new Date().getFullYear();
        const yearB = b.model.promise_doc_year || new Date().getFullYear();
        return yearB - yearA;
    });

    // Group promises by year
    const promisesByYear = sortedPromises.reduce((acc, promise) => {
        const year = promise.model.promise_doc_year || new Date().getFullYear();
        if (!acc[year]) {
            acc[year] = [];
        }
        acc[year].push(promise);
        return acc;
    }, {} as Record<number, PromiseTypeV2[]>);

    return (
        <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promises-timeline">
            <h3 className="text-lg font-semibold mb-4">Promise Timeline</h3>
            
            <div className="relative">
                {/* Vertical timeline line */}
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"></div>
                
                {/* Timeline entries */}
                <div className="space-y-6">
                    {Object.entries(promisesByYear)
                        .sort(([yearA], [yearB]) => Number(yearB) - Number(yearA))
                        .map(([year, promises]) => (
                            <div key={year} className="relative" data-testid="timeline-entry">
                                {/* Timeline dot */}
                                <div className="absolute left-2 w-4 h-4 bg-white dark:bg-gray-800 border-2 border-primary rounded-full"></div>
                                
                                {/* Content */}
                                <div className="ml-10">
                                    <div className="flex items-center gap-2 mb-2">
                                        <Calendar className="w-4 h-4 text-muted-foreground" />
                                        <span className="font-semibold text-lg" data-testid="timeline-date">{year}</span>
                                        <Badge variant="secondary">{promises.length} promise{promises.length > 1 ? 's' : ''}</Badge>
                                    </div>
                                    
                                    <div className="space-y-2">
                                        {promises.slice(0, 3).map((promise, idx) => {
                                            let statusIcon;
                                            let statusColor;
                                            
                                            if (promise.kept === true) {
                                                statusIcon = <CheckCircle2 className="w-4 h-4" />;
                                                statusColor = "text-green-600 dark:text-green-400";
                                            } else if (promise.kept === false) {
                                                statusIcon = <CircleXIcon className="w-4 h-4" />;
                                                statusColor = "text-red-600 dark:text-red-400";
                                            } else {
                                                statusIcon = <HelpCircle className="w-4 h-4" />;
                                                statusColor = "text-yellow-600 dark:text-yellow-400";
                                            }
                                            
                                            return (
                                                <div key={promise.id} className="text-sm pl-2 border-l-2 border-gray-200 dark:border-gray-700">
                                                    <div className="flex items-start gap-2" data-testid="timeline-event">
                                                        <span className={statusColor}>{statusIcon}</span>
                                                        <span className="line-clamp-2 flex-1">
                                                            {promise.statement_text || promise.model.text || promise.summary}
                                                        </span>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                        
                                        {promises.length > 3 && (
                                            <div className="text-sm text-muted-foreground pl-2">
                                                +{promises.length - 3} more promise{promises.length - 3 > 1 ? 's' : ''}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                </div>
            </div>
        </div>
    );
}