/**
 * ESG Corporate Promise List Component - Interactive Promise Cards Display
 *
 * This React component provides a comprehensive list view of ESG (Environmental, Social, Governance) corporate
 * promises with interactive cards, status indicators, motion animations, and detailed promise information display.
 * Each promise card shows fulfillment status, confidence metrics, metadata, and provides navigation to detailed
 * promise analysis views with full context preservation.
 *
 * ## Core Functionality
 * - **Promise List Display**: Renders filtered and sorted corporate promises as interactive cards with glass-morphism styling
 * - **Status Visualization**: Color-coded status indicators (green=kept, red=broken, yellow=uncertain) with confidence bars
 * - **Interactive Navigation**: Click-through to detailed promise analysis while preserving entity query parameters
 * - **Motion Animations**: Smooth reveal animations using Framer Motion with viewport-based triggers
 * - **Administrative Controls**: Delete functionality for admins with proper table-specific permissions
 * - **Empty State Handling**: Graceful display of no promises message when dataset is empty
 *
 * ## Promise Data Structure & Display
 * - **Promise Status**: Boolean `kept` field (true/false/null) determines visual status and icon display
 * - **Confidence Metrics**: Numerical confidence scores displayed as badges and progress bars
 * - **Promise Content**: Statement text, summary, and metadata extracted from JSONB model field
 * - **Document Context**: Citation information including source documents, years, and page references
 * - **ESG Classification**: Special indicators for Environmental, Social, Governance promise categorization
 * - **Evidence Count**: Display of supporting evidence quantity when available
 *
 * ## Visual Design & Interaction
 * - **Glass-Morphism Cards**: Translucent, rounded cards with hover effects and shadow animations
 * - **Status Progress Bar**: Horizontal confidence indicator with color-coded status representation
 * - **Interactive Badges**: Status badges, date indicators, and promise categorization with appropriate variants
 * - **Typography Hierarchy**: Clear separation of promise title, summary content, and metadata
 * - **Responsive Layout**: Mobile-first design with proper spacing and touch-friendly interactions
 * - **Motion Integration**: Staggered card animations with viewport detection for performance
 *
 * ## Navigation & Query Preservation
 * - **Entity Context**: Utilizes useEntity hook to access current entity, run, and model parameters
 * - **Query String Preservation**: Maintains entity-specific URL parameters when navigating to promise details
 * - **Link Generation**: Creates proper Next.js Link components with preserved query parameters
 * - **Test Attributes**: Comprehensive data-testid attributes for reliable Playwright testing
 *
 * ## Database Integration
 * The component connects to the customer Supabase database via:
 * - **xfer_promises Table**: Main promise data storage with Row Level Security (RLS) policies
 * - **Performance Columns**: Direct access to extracted summary, statement_text, and conclusion fields
 * - **JSONB Model Field**: Complex promise analysis data including evidence, citations, and confidence metrics
 * - **Entity Filtering**: Automatic filtering by entity_xid and run_id through EntityContext
 * - **Citation References**: Embedded CitationType objects with document links and metadata
 *
 * ## Animation & Performance
 * - **Framer Motion Integration**: Smooth reveal animations with configurable timing and easing
 * - **Viewport Triggers**: Animations trigger when 20% of card enters viewport for better performance
 * - **Animation Variants**: Consistent hidden/visible states with opacity and transform transitions
 * - **Staggered Delays**: Sequential card reveals with 200ms delay for visual flow
 * - **Memory Optimization**: Efficient rendering with minimal re-renders through proper dependency management
 *
 * ## Administrative Features
 * - **AdminDeleteButton**: Contextual delete functionality for users with admin permissions
 * - **Permission Integration**: Admin prop controls visibility of administrative features
 * - **Table Awareness**: Delete operations target correct xfer_promises table with proper record typing
 * - **Security Context**: Integrates with authentication system for proper access control
 *
 * ## Component Architecture & Props
 * - **PromisesData Array**: Array of PromiseTypeV2 objects with comprehensive promise information
 * - **Admin Boolean**: Controls visibility and functionality of administrative features
 * - **Conditional Rendering**: Handles undefined/null promise data gracefully with appropriate messages
 * - **Type Safety**: Full TypeScript integration with strict type checking for props and data structures
 * - **Testing Support**: Comprehensive data-testid attributes for reliable end-to-end testing
 *
 * ## System Integration Context
 * This component integrates with the broader ESG promise tracking system:
 * - **Analytics Backend**: Python backend generates promise analysis stored in analytics database
 * - **Data Synchronization**: xfer_promises table syncs data from analytics to customer database
 * - **Promise Dashboard**: Component renders within promise management interface with filtering/sorting
 * - **Detail Navigation**: Links to individual promise analysis pages with full context preservation
 * - **Entity Context**: Shares entity-specific data and parameters across promise-related components
 * - **Authentication Integration**: Respects user permissions for admin-specific functionality
 *
 * ## Key Dependencies
 * - **React 18+**: Modern React with functional components, hooks, and strict mode support
 * - **Next.js 15 App Router**: Latest Next.js routing with Link component and query parameter handling
 * - **Framer Motion**: Animation library for smooth card reveal transitions and motion effects
 * - **ShadCN/UI Components**: Card, CardContent, Badge, and other UI primitives for consistent styling
 * - **Lucide React Icons**: Status icons (CheckCircle2, CircleXIcon, HelpCircle) for promise status
 * - **Entity Context**: Custom React context providing promise data and entity-specific parameters
 * - **EkoMarkdown Component**: Custom markdown renderer with citation support for promise content
 * - **AdminDeleteButton**: Specialized administrative control with proper permissions and security
 *
 * ## Performance Considerations
 * - **Text Truncation**: Long promise summaries truncated at 120 characters for UI consistency
 * - **Citation Optimization**: Uses first citation for quick document year and metadata display
 * - **Conditional Rendering**: Avoids unnecessary DOM operations when data is missing or invalid
 * - **Animation Throttling**: Viewport-based triggers prevent excessive animation calculations
 * - **Memory Management**: Proper cleanup and efficient re-rendering through optimized dependencies
 *
 * @see https://react.dev/reference/react/useState React useState Hook
 * @see https://www.framer.com/motion/introduction/ Framer Motion Animation Library
 * @see https://nextjs.org/docs/app/api-reference/components/link Next.js Link Component
 * @see https://ui.shadcn.com/docs/components/card ShadCN/UI Card Component
 * @see https://ui.shadcn.com/docs/components/badge ShadCN/UI Badge Component
 * @see https://lucide.dev/icons/ Lucide React Icons
 * @see {@link ../../../types/promise.ts} PromiseTypeV2 and XferPromiseModel Types
 * @see {@link ../../../components/context/entity/entity-context.tsx} EntityContext Provider
 * @see {@link ../../../components/markdown/eko-markdown.tsx} EkoMarkdown Component
 * @see {@link ../../../components/admin.tsx} AdminDeleteButton Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Interactive ESG corporate promise list component with status indicators, animations, and navigation to detailed promise analysis views.
 * @example ```tsx
  // Display filtered promises list with admin controls
  <PromisesListV2 promisesData={filteredPromises} admin={user?.is_admin} />
  
  // Empty state handling
  <PromisesListV2 promisesData={[]} admin={false} />
  
  // Full promise list in dashboard context
  {entity.promisesData && (
    <PromisesListV2 
      promisesData={entity.promisesData} 
      admin={auth.admin}
    />
  )}
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { PromiseTypeV2 } from '@/types'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle2, CircleXIcon, HelpCircle } from 'lucide-react'
import React from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { useEntity } from '@/components/context/entity/entity-context'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import Markdown from 'react-markdown'
import { AdminDeleteButton } from '@/components/admin'

const variants = {
    hidden: {opacity: 0, y: 20},
    visible: {opacity: 1, y: 0},
};


// New component that works directly with PromiseTypeV2
export function PromisesListV2({promisesData, admin}: { promisesData: PromiseTypeV2[] | undefined | null, admin: boolean }) {
    const {queryString} = useEntity();

    if (!promisesData || promisesData.length === 0) {
        return <p className="text-sm text-muted-foreground text-center py-4" data-testid="no-promises-message">No promises found</p>;
    }

    return <div data-testid="promises-list">
        {promisesData.map((item, j) => {
            const model = item.model;

            // Determine the promise status icon and color
            let statusIcon;
            let statusColor;
            let statusText;

            if (item.kept === true) {
                statusIcon = <CheckCircle2 className="w-5 h-5 inline-block"/>;
                statusColor = "bg-green-50 border-green-200 text-green-700 dark:bg-green-950 dark:border-green-800 dark:text-green-400";
                statusText = "Kept";
            } else if (item.kept === false) {
                statusIcon = <CircleXIcon className="w-5 h-5 inline-block"/>;
                statusColor = "bg-red-50 border-red-200 text-red-700 dark:bg-red-950 dark:border-red-800 dark:text-red-400";
                statusText = "Broken";
            } else {
                statusIcon = <HelpCircle className="w-5 h-5 inline-block"/>;
                statusColor = "bg-yellow-50 border-yellow-200 text-yellow-700 dark:bg-yellow-950 dark:border-yellow-800 dark:text-yellow-400";
                statusText = "Uncertain";
            }

            // Get the document year from the first citation or the model
            const firstCitation = model.citations && model.citations.length > 0 ? model.citations[0] : null;
            const docYear = firstCitation?.year || model.promise_doc_year || new Date().getFullYear();

            // Truncate text if too long
            const truncatedText = (item.summary && item.summary.length > 120) ? item.summary.substring(0, 90) + '...' : (item.summary || model.summary)

            return (
                <motion.div
                    initial="hidden"
                    key={j}
                    exit="hidden"
                    whileInView="visible"
                    variants={variants}
                    viewport={{once: true, amount: 0.2}} // Triggers once, 20% in view
                    transition={{duration: 0.3, ease: 'easeOut', delay: 0.2}}
                    className="px-4"
                >
                    <Link href={"/customer/dashboard/gw/promises/" + item.id+"?"+queryString} passHref>
                        <Card className="mb-4 hover:shadow-md transition-shadow duration-200 overflow-hidden relative group" data-testid="promise-item">
                            <AdminDeleteButton
                              tableName="xfer_promises"
                                recordId={item.id}
                                recordType="promise"
                            />
                            {/* Status indicator */}
                            <div className="w-full h-1.5 bg-slate-100 dark:bg-slate-800">
                                <div
                                    className={`h-full ${item.kept === true ? 'bg-green-500' : item.kept === false ? 'bg-red-500' : 'bg-yellow-500'}`}
                                    style={{width: `${model.confidence}%`}}
                                />
                            </div>

                            <CardContent className="pt-4 pb-4">
                                <div className="flex justify-between items-start mb-3">
                                    <Badge className={`${statusColor} flex items-center gap-1`} data-testid="promise-status">
                                        {statusIcon}
                                        {statusText}
                                    </Badge>
                                    <div className="flex items-center gap-2">
                                        <Badge variant="outline" data-testid="promise-date">{docYear}</Badge>
                                        {model.esg_promise && <Badge variant="secondary" data-testid="promise-theme">ESG</Badge>}
                                        <Badge variant="secondary" data-testid="promise-category">
                                            {model.esg_promise ? 'Environmental' : 'General'}
                                        </Badge>
                                    </div>
                                </div>

                                <div className="mb-3">
                                    <div className="font-medium text-lg" data-testid="promise-title">
                                        <Markdown children={model.statement_text || model.text || truncatedText} />
                                    </div>
                                </div>

                                <div className="flex flex-col gap-2">
                                    <div className="text-sm text-muted-foreground line-clamp-6" data-testid="promise-content">
                                        <EkoMarkdown
                                          citations={[]}
                                          admin={admin}
                                          skipCitations={true}
                                        >
                                        {model.summary}
                                        </EkoMarkdown>
                                    </div>

                                    <div className="flex justify-between items-center mt-2">
                                        <div className="text-xs text-muted-foreground">
                                            ID: {item.id}
                                        </div>
                                        <Badge variant="secondary" className="ml-auto" data-testid="promise-confidence">
                                            Confidence: {model.confidence}%
                                        </Badge>
                                    </div>
                                    
                                    {/* Additional metadata */}
                                    <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-800">
                                        <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                                            {model.promise_doc && (
                                                <span data-testid="promise-source">Source: {model.promise_doc}</span>
                                            )}
                                            {model.promise_doc_year && (
                                                <span data-testid="promise-target-date">Target: {model.promise_doc_year + 5}</span>
                                            )}
                                            {model.citations && model.citations.length > 0 && (
                                                <span data-testid="promise-citations">{model.citations.length} citation{model.citations.length > 1 ? 's' : ''}</span>
                                            )}
                                            <span data-testid="promise-references">View details</span>
                                        </div>
                                        {model.evidence && (
                                            <div className="mt-2" data-testid="promise-evidence">
                                                <span className="text-xs font-medium">Evidence: </span>
                                                <span className="text-xs">{typeof model.evidence === 'object' ? Object.keys(model.evidence).length : 0} items</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </Link>
                </motion.div>
            );
        })}
    </div>;
}
