/**
 * Next.js Client Component for Single ESG Document Analysis Display
 *
 * This client component serves as the main page for displaying comprehensive single document 
 * analysis results within the ESG (Environmental, Social, Governance) analysis system. It 
 * provides users with detailed claim verdicts, vague term analysis, and AI-powered assessment
 * of document credibility and transparency. The page is part of the customer dashboard's 
 * greenwashing (gw) detection module, offering evidence-based evaluation of corporate 
 * sustainability communications.
 *
 * ## Core Functionality
 * - **Document Analysis Retrieval**: Fetches complete analysis results from `xfer_gw_single_doc` table
 * - **Claim Verification Display**: Shows individual claim verdicts with confidence scores (>50% filter)
 * - **Vague Terms Analysis**: Identifies and analyzes ambiguous language patterns (>50% score filter)
 * - **AI-Powered Assessment**: Displays AI-generated summaries and analysis conclusions
 * - **Citation System**: Renders analysis with proper citation management via EkoMarkdown
 * - **Administrative Features**: Provides enhanced functionality for admin users
 * - **Loading States**: Implements proper loading indicators during data fetching
 *
 * ## Request Parameters
 * - **Dynamic Route**: `[id]` - Numeric document analysis identifier from `xfer_gw_single_doc` table
 *
 * ## Database Integration
 * The component connects to the customer Supabase database and queries the `xfer_gw_single_doc` table:
 * - **Primary Query**: Single document analysis by ID with complete analysis_json payload
 * - **Data Structure**: JSON analysis containing summary, claim_verdicts, and vague_analyses arrays
 * - **Security**: Protected by Supabase Row Level Security for authenticated users only
 * - **Performance**: Uses single query with JSONB field access for efficient data retrieval
 *
 * ## Analysis Data Structure
 * The `analysis_json` field contains:
 * ```typescript
 * {
 *   summary: {
 *     summary: string;        // Analysis overview text
 *     citations: Citation[];  // Supporting document references
 *   };
 *   claim_verdicts: Array<{
 *     id: number;
 *     confidence: number;     // Confidence score (0-100)
 *     claim: { text: string };
 *     verdict: string;        // AI analysis verdict
 *     citations: Citation[];
 *   }>;
 *   vague_analyses: Array<{
 *     id: number;
 *     score: number;         // Vagueness score (0-100)
 *     phrase: string;        // Identified vague term
 *     summary: string;       // Analysis explanation
 *     citations: Citation[];
 *   }>;
 * }
 * ```
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with client components and dynamic routing
 * - **Supabase Client**: Database access with authentication and real-time capabilities
 * - **React Hooks**: useState and useEffect for state management and side effects
 * - **EkoMarkdown**: Custom markdown renderer with citation support and administrative features
 * - **Authentication Context**: User session management and role-based access control
 *
 * ## Related Components
 * - EkoMarkdown: Advanced markdown renderer with citation management and admin controls
 * - Authentication Context: User authentication state and admin permission management
 * - Supabase Client: Secure database connection with Row Level Security
 * - Customer Dashboard Layout: Parent dashboard providing navigation and context
 *
 * ## System Architecture
 * This component fits into the broader ESG analysis workflow:
 * - **Analytics Backend**: Python system processes documents and generates analysis results
 * - **Data Sync Layer**: `xfer_gw_single_doc` table synchronizes analysis data to customer database
 * - **Frontend Display**: This component provides user-facing access to analysis results
 * - **Citation Pipeline**: EkoMarkdown component handles document citations and reference linking
 * - **Admin Interface**: Enhanced features for administrative users including raw data debugging
 *
 * ## Security & Performance
 * - Database access through Supabase Row Level Security policies for authenticated users
 * - Client-side filtering of low-confidence claims and vague terms (>50% threshold)
 * - Efficient single-query data loading with loading state management
 * - Type-safe database interactions using generated TypeScript definitions
 * - Debug information available for development and administrative troubleshooting
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://react.dev/reference/react/useState React useState Hook
 * @see {@link ../../../components/markdown/eko-markdown.tsx} EkoMarkdown Component
 * @see {@link ../../../components/context/auth/auth-context.tsx} Authentication Context
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This client component displays comprehensive single document analysis results within the ESG analysis system, showing claim verdicts, vague term analysis, and AI-powered assessment of document credibility.
 * @example ```bash
 * # Access single document analysis
 * curl -X GET 'http://localhost:3000/customer/dashboard/gw/doc/123'
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client"
import React, {useEffect, useState} from 'react';
import {createClient} from "@/app/supabase/client";
import {useParams} from "next/navigation";
import {Database} from "@/database.types";
import {EkoMarkdown} from "@/components/markdown/eko-markdown";
import {useAuth} from "@/components/context/auth/auth-context";

type SingleDocType = Database["public"]["Tables"]["xfer_gw_single_doc"]["Row"];
export default function Page() {
    const supabase = createClient();
    const params= useParams();
    const id = params.id!;
    const {user, admin} = useAuth();
    const [singleDocAnalysis, setSingleDocAnalysis] = useState<any>();
    useEffect(() => {
        supabase.from("xfer_gw_single_doc").select("*").eq("id", +id).single().then(({data, error}) => {
            console.log(data, error)
            setSingleDocAnalysis(data?.analysis_json)
        });
    }, []);

    if (!singleDocAnalysis) return <div>Loading...</div>
    return (
        <div
            className="container mx-auto p-4 prose dark:prose-headings:text-foreground dark:prose-a:text-muted-foreground dark:text-foreground ">
            <h1>Single Doc Analysis</h1>
            <h2>Vague Terms</h2>
            {singleDocAnalysis.summary.summary}
            {/*<EkoMarkdown admin={admin} citations={singleDocAnalysis.summary.citations}>{singleDocAnalysis.summary.analysis}</EkoMarkdown>*/}
            <h2>Claim Verdicts</h2>
            {}
            {singleDocAnalysis.claim_verdicts.filter((claim: any) => claim.confidence > 50).map((claim: any) => (
                <div key={claim.id}>
                    <h3>{claim.claim.text}</h3>
                    <EkoMarkdown admin={admin} citations={claim.citations}>{claim.verdict}</EkoMarkdown>
                </div>
            ))}

            {singleDocAnalysis.vague_analyses.filter((analysis: any) => analysis.score > 50).map((analysis: any) => (
                <div key={analysis.id}>
                    <h3>{analysis.phrase}</h3>
                    <EkoMarkdown admin={admin} citations={analysis.citations}>{analysis.summary}</EkoMarkdown>
                </div>
            ))}

            <code>
                <pre>
                    {singleDocAnalysis && JSON.stringify(singleDocAnalysis, null, 2)}
                </pre>
            </code>
        </div>
    );
}
