/**
 * React Component for Cherry Picking (Selective Highlighting) Analysis Display
 *
 * This component renders comprehensive cherry picking analysis for ESG entities, displaying AI-powered
 * greenwashing and selective highlighting detection results. Cherry picking analysis identifies when
 * companies present predominantly positive statements while omitting or downplaying negative information,
 * a common form of greenwashing in ESG communications.
 *
 * ## Core Functionality
 * - **Selective Highlighting Detection**: Displays analysis of positive vs negative statement imbalances
 * - **Multi-Section Display**: Organizes content into reason, explanation, and detailed analysis sections
 * - **Citation Management**: Aggregates and deduplicates citations from positive and negative statements
 * - **Interactive References**: Renders clickable citation references with document links and metadata
 * - **Admin Support**: Provides administrative features when admin mode is enabled
 * - **Glass-Morphism UI**: Implements modern glass-morphism design system with rounded elements
 *
 * ## Data Structure
 * The component processes `CherryTypeV2` data containing:
 * - **Positive Statements**: Green flags representing favorable ESG claims
 * - **Negative Statements**: Red flags highlighting concerning or contradictory information
 * - **Analysis Text**: LLM-generated detailed analysis of the selective highlighting patterns
 * - **Citations**: Document references supporting the analysis findings
 * - **Metadata**: Run ID, entity ID, model information, and analysis labels
 *
 * ## UI Components
 * - **Reason Section**: Brief explanation of why cherry picking was detected
 * - **Explanation Section**: Detailed explanation of the selective highlighting behavior
 * - **Analysis Section**: Comprehensive AI-powered analysis with markdown rendering and citations
 * - **References Card**: Compact citation display with document links and metadata
 * - **Metadata Footer**: Analysis run information and entity details
 *
 * ## Key Dependencies
 * - **React 18+**: Modern functional component with TypeScript support
 * - **Shadcn/UI Card Components**: Glass-morphism card system for consistent layout
 * - **Lucide React Icons**: Information icon for metadata display
 * - **EkoMarkdown**: Custom markdown renderer with citation support and chart rendering
 * - **Citation Components**: Specialized components for handling document references
 * - **CherryTypeV2**: TypeScript interface defining the cherry picking data structure
 *
 * ## System Architecture
 * This component fits within the broader ESG analysis system:
 * - **Analytics Backend**: Python system generates cherry picking flags and analysis
 * - **Data Sync Layer**: `xfer_selective` table synchronizes data between analytics and customer databases
 * - **Entity Context**: Component receives data through React Context from `CherryDataFetcher`
 * - **Dashboard Integration**: Displayed within entity dashboard for ESG analysis workflows
 * - **Citation System**: Integrates with document management for source verification
 *
 * ## Greenwashing Detection
 * Cherry picking analysis specifically targets:
 * - **Positive Bias**: Overrepresentation of positive environmental or social claims
 * - **Negative Omission**: Systematic exclusion of concerning information or contradictory evidence
 * - **Context Manipulation**: Presenting information without proper context or comparison
 * - **Temporal Bias**: Focusing on positive trends while ignoring negative historical patterns
 * - **Scope Limitation**: Highlighting narrow successes while ignoring broader systemic issues
 *
 * ## Performance Optimizations
 * - Citation aggregation performed once during render to avoid duplicate processing
 * - Conditional rendering of references card only when citations exist
 * - Efficient citation deduplication using `reduceCitations` utility function
 * - Lazy loading of markdown content through `EkoMarkdown` component
 *
 * @see https://react.dev/learn React Documentation
 * @see https://ui.shadcn.com/docs/components/card Shadcn UI Card Documentation
 * @see https://lucide.dev/ Lucide React Icons
 * @see {@link ../../../../../../components/citation} Citation Components
 * @see {@link ../../../../../../components/markdown/eko-markdown} EkoMarkdown Component
 * @see {@link ../../../../../../types/cherry} CherryTypeV2 Interface
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This component renders comprehensive cherry picking analysis for ESG entities, displaying AI-powered greenwashing and selective highlighting detection results.
 * @example
 * ```tsx
 * <CherryPicking 
 *   data={cherryPickingData} 
 *   admin={true} 
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Info } from 'lucide-react'
import { CitationType, CompactCitation, reduceCitations } from '@/components/citation'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CherryTypeV2 } from '@/types'

export default function CherryPicking ({data, admin}: { data: CherryTypeV2, admin:boolean })  {

    const citations:any[]= [];

    // Get statements from the model field in v2 format
    const greenFlags = data.model.positive_statements || [];
    const redFlags = data.model.negative_statements || [];

    greenFlags.forEach((flag:any) => {
        if (flag.citations) {
            flag.citations.forEach((citation:any) => citations.push(citation));
        }
    });

    redFlags.forEach((flag:any) => {
        if (flag.citations) {
            flag.citations.forEach((citation:any) => citations.push(citation));
        }
    });

    for (const citation of citations) {
        if(!citation) {
            console.log("Citation is null");
        }
    }
    return (
        <div className="p-6 space-y-6">

            <div>
                <h2 >Cherry Picking</h2>
                <div>{data.reason}</div>
            </div>

            <div>
                <h2>Explanation</h2>
                <div>{data.explanation}</div>
            </div>

            <div>
                <h2>Detailed Analysis</h2>
                <div><EkoMarkdown citations={data.model.citations as unknown as CitationType[] || []}
                                  admin={admin}>{data.analysis}</EkoMarkdown></div>
            </div>


            {data.model.citations && (<Card className="mb-6">
                <CardHeader>
                    <CardTitle>References</CardTitle>
                </CardHeader>
                <CardContent>
                    {reduceCitations(data.model.citations as unknown as CitationType[] || []).map((citation) => (
                        <CompactCitation key={citation.doc_id + ":" + citation.pages.join(",")} data={citation}
                                         admin={admin}/>))}
                </CardContent>
            </Card>)}

            <div className="mt-6 text-sm text-zinc-500 flex items-center">
                <Info className="mr-2 h-4 w-4"/>
                <span>Run ID: {data.run_id} | Entity ID: {data.entity_xid} | Model: {data.model.model} | Label: {data.label}</span>
            </div>
        </div>
    );
};
