/**
 * ESG Selective Highlighting Analysis Card Component - Cherry Picking & Flooding Detection Display
 *
 * This React functional component renders individual cherry picking and flooding analysis results as
 * comprehensive, interactive cards within the EkoIntelligence ESG analysis platform. Cherry picking
 * analysis identifies selective positive highlighting in corporate communications, while flooding analysis
 * detects overwhelming negative information patterns, both representing potential forms of greenwashing.
 *
 * ## Core Functionality
 * - **Dual Analysis Types**: Renders both cherry picking (selective positive) and flooding (negative overwhelming) analyses
 * - **Interactive Display**: Expandable accordion sections for detailed statement analysis with positive/negative categorization
 * - **Scoring Visualization**: Displays AI-computed severity scores (0-100) with dynamic badge coloring based on risk levels
 * - **Citation Integration**: Full document reference support with clickable citations through CompactCitation components
 * - **Administrative Controls**: AdminDeleteButton integration for record management with proper xfer_selective table targeting
 * - **Glass-Morphism Design**: Consistent UI styling with dynamic color schemes based on analysis type and severity
 *
 * ## Analysis Types & Visual Differentiation
 * - **Cherry Picking** (`model: 'cherry_picking'`): Rose color scheme with Cherry icon, detects selective positive highlighting
 * - **Flooding** (`model: 'flooding'`): Amber color scheme with Waves icon, identifies overwhelming negative information patterns
 * - **Unified Structure**: Both types share identical data structure and UI layout with contextually appropriate styling
 * - **Severity Mapping**: Visual indicators adapt based on severity score thresholds (≥70 destructive, ≥40 secondary, <40 default)
 *
 * ## Data Structure & Model Integration
 * The component processes `CherryTypeV2` objects containing:
 * - **Model Field**: JSONB data with analysis metadata, scoring, and categorized statements
 * - **Analysis Text**: AI-generated detailed analysis rendered through EkoMarkdown with citation support
 * - **Statement Arrays**: Positive and negative statements with impact values and metadata
 * - **Citations Array**: Document references with page numbers and source identification
 * - **Confidence Metrics**: AI confidence scores, severity ratings, and analysis timestamps
 *
 * ## UI Components & Layout Architecture
 * - **GlassCard Container**: Main card with dynamic border coloring and hover transitions
 * - **Header Section**: Analysis type display with icon, title, severity badge, and score metrics
 * - **Content Sections**: Summary, explanation, detailed analysis with markdown rendering
 * - **Accordion Integration**: Expandable statements section with positive/negative categorization
 * - **Footer Metadata**: Record identifiers, timestamps, and entity information
 * - **Citation Display**: Compact citations with administrative controls and reduced duplicate handling
 *
 * ## Database Integration & Data Flow
 * The component integrates with the customer Supabase database through:
 * - **xfer_selective Table**: Primary data source for cherry picking and flooding analysis records
 * - **JSONB Model Field**: Complex analysis data including statements, scores, and AI reasoning
 * - **Citation Management**: Document references with page numbers and source identification
 * - **Administrative Security**: Delete operations respect admin permissions with proper table targeting
 * - **Entity Association**: Records linked to specific entities and analysis runs for proper filtering
 *
 * ## Props Interface & Component Contract
 * - **data: CherryTypeV2**: Complete selective highlighting analysis record with model data
 * - **admin: boolean**: Administrative permissions flag for enabling delete controls and citation admin features
 * - **Component Safety**: Defensive programming with fallback values for missing model properties
 * - **Type Safety**: Full TypeScript integration with proper type checking and inference
 *
 * ## Greenwashing Detection & ESG Context
 * This component serves the broader ESG greenwashing detection pipeline:
 * 1. **Statement Analysis**: Processes positive vs negative statement distributions in corporate communications
 * 2. **Selective Highlighting Detection**: Identifies overrepresentation patterns in ESG disclosures
 * 3. **AI Analysis Integration**: Renders LLM-powered detailed explanations and severity assessments
 * 4. **Visual Pattern Recognition**: Provides immediate visual feedback for analysis patterns through color coding
 * 5. **Citation Verification**: Enables source document verification through integrated citation system
 *
 * ## Accessibility & User Experience
 * - **Semantic HTML**: Proper heading hierarchy and landmark elements for screen readers
 * - **Keyboard Navigation**: Full keyboard accessibility through accordion and button interactions
 * - **Visual Hierarchy**: Clear information hierarchy with appropriate font sizing and spacing
 * - **Color Accessibility**: High contrast ratios maintained across both cherry picking and flooding color schemes
 * - **Loading States**: Graceful handling of missing data with appropriate fallback displays
 *
 * ## Performance Considerations
 * - **Conditional Rendering**: Efficient rendering based on data availability and user permissions
 * - **Citation Deduplication**: Uses reduceCitations utility to minimize duplicate citation displays
 * - **Dynamic Imports**: Lucide React icons loaded efficiently with tree shaking support
 * - **Glass Effects Optimization**: Hardware-accelerated backdrop blur effects with proper CSS containment
 * - **Statement Processing**: Efficient mapping and rendering of potentially large statement arrays
 *
 * ## Key Dependencies
 * - **React 18+**: Functional component using modern React patterns and hooks
 * - **Lucide React**: Icon library providing Cherry, Waves, AlertTriangle, CheckCircle, Info, TrendingUp icons
 * - **Shadcn/UI**: Design system components including Badge, Accordion, GlassCard with glass-morphism styling
 * - **EkoMarkdown**: Custom markdown renderer with citation support for AI-generated analysis content
 * - **Citation System**: CompactCitation component with reduceCitations utility for source management
 * - **Admin Components**: AdminDeleteButton for secure record management with permission controls
 *
 * ## System Architecture Integration
 * This component fits within the broader ESG analysis ecosystem:
 * - **Analytics Backend**: Python system processes documents and generates selective highlighting flags
 * - **Data Sync Layer**: xfer_selective table synchronizes analysis results between analytics and customer databases
 * - **Dashboard Integration**: Component used within cherry picking dashboard for detailed analysis display
 * - **Citation System**: Integrates with document management system for source verification and references
 * - **Admin Interface**: Provides administrative controls while maintaining data security and integrity
 *
 * @see https://ui.shadcn.com/docs/components/badge Badge Component Documentation
 * @see https://lucide.dev/icons Lucide React Icons Library
 * @see https://ui.shadcn.com/docs/components/accordion Accordion Component Documentation
 * @see https://react.dev/reference/react React Functional Components
 * @see {@link @/components/citation} Citation System Components
 * @see {@link @/components/markdown/eko-markdown} EkoMarkdown Component
 * @see {@link @/components/ui/glass-card} GlassCard Component
 * @see {@link @/components/admin} AdminDeleteButton Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description ESG Selective Highlighting Analysis Card Component that renders cherry picking and flooding detection results with interactive displays, citations, and administrative controls.
 * @example
 * ```tsx
 * // Render cherry picking analysis card
 * <SelectiveHighlightingCard 
 *   data={cherryPickingData} 
 *   admin={true} 
 * />
 * 
 * // Render flooding analysis card
 * <SelectiveHighlightingCard 
 *   data={floodingAnalysisData} 
 *   admin={false} 
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import React from 'react'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle, CheckCircle, Cherry, Info, TrendingUp, Waves } from 'lucide-react'
import { CitationType, CompactCitation, reduceCitations } from '@/components/citation'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CherryTypeV2 } from '@/types'
import { StatementAndMetadata } from '@/types/types'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { GlassCard } from '@/components/ui/glass-card'
import { AdminDeleteButton } from '@/components/admin'

interface SelectiveHighlightingCardProps {
  data: CherryTypeV2;
  admin: boolean;
}

export default function SelectiveHighlightingCard({ data, admin }: SelectiveHighlightingCardProps) {
  console.log("SelectiveHighlightingCard received data:", data);

  // Safely access model properties with fallbacks
  const model = data.model || {};
  const modelType = model.model || 'unknown';
  const isFlooding = modelType === 'flooding';
  const isCherryPicking = modelType === 'cherry_picking';
  const severity = model.severity || 0;
  const score = model.score || 0;
  const confidence = model.confidence || 0;

  // Determine the badge color based on the model type and severity
  const getBadgeVariant = () => {
    if (severity >= 70) return "destructive";
    if (severity >= 40) return "secondary"; // Use secondary instead of warning as warning is not a valid variant
    return "secondary";
  };

  // Format the score as a percentage
  const formatScore = (score: number) => {
    return `${score}%`;
  };

  return (
    <GlassCard
      variant="default"
      className={`overflow-hidden border-l-4 relative group ${
        isFlooding
          ? 'border-l-amber-500 hover:border-l-amber-400'
          : 'border-l-rose-500 hover:border-l-rose-400'
      } transition-all duration-300`}
    >
      <AdminDeleteButton
        tableName="xfer_selective"
        recordId={data.id}
        recordType={isFlooding ? "flooding" : "cherry picking"}
      />
      {/* Header */}
      <div className={`p-6 pb-4 rounded-t-2xl ${
        isFlooding
          ? 'bg-gradient-to-r from-amber-50/50 to-transparent dark:from-amber-950/20 dark:to-transparent'
          : 'bg-gradient-to-r from-rose-50/50 to-transparent dark:from-rose-950/20 dark:to-transparent'
      }`}>
        <div className="flex justify-between items-start">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              {isFlooding ? (
                <>
                  <div className="p-2 rounded-xl bg-amber-100 dark:bg-amber-900/30">
                    <Waves className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Flooding</h3>
                    <p className="text-sm text-muted-foreground">Information overwhelming</p>
                  </div>
                </>
              ) : (
                <>
                  <div className="p-2 rounded-xl bg-rose-100 dark:bg-rose-900/30">
                    <Cherry className="h-5 w-5 text-rose-600 dark:text-rose-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Cherry Picking</h3>
                    <p className="text-sm text-muted-foreground">Selective data presentation</p>
                  </div>
                </>
              )}
              <Badge variant={getBadgeVariant()} className="ml-2">
                Severity: {severity}
              </Badge>
            </div>
            <h4 className="text-base font-medium text-foreground/90">
              {model.label || 'No label available'}
            </h4>
          </div>
          <div className="text-right space-y-1">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <span className="text-2xl font-bold">{formatScore(score)}</span>
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              <div>Confidence: {confidence}%</div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 space-y-6">
        <div className="space-y-3">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Info className="h-4 w-4 text-brand" />
            Summary
          </h3>
          <div className="p-4 rounded-xl bg-muted/30 border border-border/50">
            <p className="text-sm leading-relaxed">{data.reason || 'No summary available'}</p>
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-amber-500" />
            Explanation
          </h3>
          <div className="p-4 rounded-xl bg-muted/30 border border-border/50">
            <p className="text-sm leading-relaxed">{data.explanation || 'No explanation available'}</p>
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-brand" />
            Detailed Analysis
          </h3>
          <div className="p-4 rounded-xl bg-muted/30 border border-border/50 prose dark:prose-invert max-w-none prose-sm">
            <EkoMarkdown
              citations={model.citations as unknown as CitationType[] || []}
              admin={admin}
            >
              {data.analysis || 'No detailed analysis available'}
            </EkoMarkdown>
          </div>
        </div>

        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="statements" className="border border-border/50 rounded-xl px-4">
            <AccordionTrigger className="hover:no-underline">
              <div className="flex items-center gap-3">
                <div className="p-1.5 rounded-lg bg-brand/10">
                  <CheckCircle className="h-4 w-4 text-brand" />
                </div>
                <span className="font-medium">Statements Analysis</span>
                <Badge variant="outline" className="ml-auto">
                  {model.positive_statements?.length || 0} positive, {model.negative_statements?.length || 0} negative
                </Badge>
              </div>
            </AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="space-y-6">
                {model.positive_statements && model.positive_statements.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold flex items-center gap-2">
                      <div className="p-1 rounded bg-green-100 dark:bg-green-900/30">
                        <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400" />
                      </div>
                      Positive Statements
                    </h4>
                    <div className="space-y-2">
                      {model.positive_statements.map((statement: StatementAndMetadata, index: number) => (
                        <div key={`positive-${index}`} className="p-3 rounded-lg bg-green-50/50 dark:bg-green-950/20 border border-green-200/50 dark:border-green-800/50">
                          <p className="text-sm leading-relaxed">{statement.statement_text}</p>
                          {statement.metadata?.impact_value && (
                            <Badge variant="outline" className="mt-2 text-xs">
                              Impact: {statement.metadata.impact_value}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {model.negative_statements && model.negative_statements.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold flex items-center gap-2">
                      <div className="p-1 rounded bg-red-100 dark:bg-red-900/30">
                        <AlertTriangle className="h-3 w-3 text-red-600 dark:text-red-400" />
                      </div>
                      Negative Statements
                    </h4>
                    <div className="space-y-2">
                      {model.negative_statements.map((statement: StatementAndMetadata, index: number) => (
                        <div key={`negative-${index}`} className="p-3 rounded-lg bg-red-50/50 dark:bg-red-950/20 border border-red-200/50 dark:border-red-800/50">
                          <p className="text-sm leading-relaxed">{statement.statement_text}</p>
                          {statement.metadata?.impact_value && (
                            <Badge variant="outline" className="mt-2 text-xs">
                              Impact: {statement.metadata.impact_value}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {model.citations && model.citations.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Info className="h-4 w-4 text-brand" />
              References
            </h3>
            <div className="p-4 rounded-xl bg-muted/30 border border-border/50 space-y-3">
              {reduceCitations(model.citations as unknown as CitationType[]).map((citation) => (
                <CompactCitation
                  key={citation.doc_id + ":" + citation.pages.join(",")}
                  data={citation}
                  admin={admin}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 mt-6 border-t border-border/20 bg-muted/10 rounded-b-2xl">
        <div className="text-xs text-muted-foreground flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Info className="h-3 w-3" />
            <span>ID: {data.id} | Run: {data.run_id} | Entity: {data.entity_xid}</span>
          </div>
          {model.created_at && (
            <div className="flex items-center gap-1">
              <span>Created: {new Date(model.created_at).toLocaleDateString()}</span>
            </div>
          )}
        </div>
      </div>
    </GlassCard>
  );
}
