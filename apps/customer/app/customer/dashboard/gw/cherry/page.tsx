/**
 * Next.js Client Component for ESG Cherry Picking and Flooding Detection Dashboard
 *
 * This client-side component provides the main dashboard view for cherry picking (selective highlighting)
 * and flooding analysis within the EkoIntelligence ESG platform. Cherry picking analysis identifies when
 * entities (companies, funds, organizations) present predominantly positive statements while omitting or
 * downplaying negative information, a common form of greenwashing in ESG communications. Flooding analysis
 * detects the opposite pattern - overwhelming presentation of negative information while omitting positive developments.
 *
 * ## Core Functionality
 * - **Cherry Picking Detection**: Displays analysis of entities that selectively highlight positive ESG information
 * - **Flooding Detection**: Shows analysis of entities that overwhelmingly present negative information patterns
 * - **Loading State Management**: Provides skeleton loading UI while data fetches from the `xfer_selective` database table
 * - **Navigation Integration**: Updates breadcrumb navigation with preserved URL parameters for entity and run context
 * - **Empty State Handling**: Displays appropriate messaging when no cherry picking or flooding instances are found
 * - **Admin Mode Support**: Passes admin flag to child components for administrative features and debugging
 *
 * ## Data Sources
 * The component consumes cherry picking data through the Entity Context system:
 * - **Database Table**: `xfer_selective` containing both cherry picking and flooding analyses
 * - **Entity Context**: React Context providing `cherryData` array of `CherryTypeV2` objects
 * - **Loading States**: `isLoadingCherry` boolean for skeleton display management
 * - **Data Filtering**: Automatically filtered by entity ID and run ID from URL parameters
 *
 * ## Analysis Types
 * Both analysis types share the same data structure but are differentiated by the `model` field:
 * - **Cherry Picking** (`model: 'cherry_picking'`): Selective positive highlighting detection
 * - **Flooding** (`model: 'flooding'`): Overwhelming negative information detection
 * - **Unified Display**: Single component renders both types in categorized tabs
 *
 * ## UI Components
 * - **Loading Skeleton**: Glass-morphism cards with shimmer effects during data fetch
 * - **SelectiveHighlightingList**: Main component for displaying categorized analysis results
 * - **NoData Component**: Empty state with descriptive messaging when no data exists
 * - **EkoPageTitle**: Consistent page header with glass-morphism styling
 * - **Navigation Breadcrumbs**: Dynamic navigation path with preserved query parameters
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with client-side routing and navigation
 * - **React 18+**: Functional component using hooks for state management and lifecycle
 * - **Entity Context**: Custom React Context for ESG entity data and loading states
 * - **Navigation Context**: Custom navigation system with breadcrumb management
 * - **Authentication Context**: User authentication and admin role checking
 * - **Shadcn/UI Components**: Glass-morphism design system (GlassCard, Skeleton)
 * - **Custom Navigation Hook**: `useNavigationWithParams` for query parameter preservation
 *
 * ## System Architecture
 * This component fits within the broader ESG analysis system:
 * - **Analytics Backend**: Python system processes documents and generates selective highlighting flags
 * - **Data Sync Layer**: `xfer_selective` table synchronizes analysis results from analytics to customer database
 * - **Entity Dashboard**: This component provides specialized view for selective highlighting analysis
 * - **Context System**: Integrates with entity context for data fetching and state management
 * - **Navigation System**: Maintains consistent breadcrumb navigation across dashboard views
 *
 * ## Greenwashing Detection Pipeline
 * The cherry picking analysis specifically targets ESG greenwashing patterns:
 * 1. **Statement Analysis**: Analyzes positive vs negative statement distributions in entity communications
 * 2. **Selective Highlighting**: Detects overrepresentation of positive claims while omitting negative information
 * 3. **Citation Tracking**: Provides document references and source verification for all claims
 * 4. **AI Analysis**: Uses LLM-powered analysis for detailed explanations and severity scoring
 * 5. **Visual Display**: Presents findings through categorized tabs and detailed analysis cards
 *
 * ## Route Structure
 * - **Path**: `/customer/dashboard/gw/cherry` - Main cherry picking dashboard
 * - **Query Parameters**: Preserved from entity context (entity, run, model, disclosures)
 * - **Breadcrumb Navigation**: Dashboard → Cherry Picking
 *
 * ## Performance Considerations
 * - **Skeleton Loading**: Provides immediate visual feedback during data fetching
 * - **Conditional Rendering**: Efficient rendering based on data availability and loading states
 * - **Context Integration**: Leverages shared entity context to avoid duplicate data fetching
 * - **Glass-Morphism UI**: Optimized glass effects with proper backdrop blur and transparency
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages Next.js App Router Pages
 * @see https://react.dev/reference/react/useEffect React useEffect Hook
 * @see https://ui.shadcn.com/docs/components/skeleton Skeleton Component Documentation
 * @see {@link ../../../components/context/entity/entity-context.tsx} Entity Context Provider
 * @see {@link ./selective-highlighting-list.tsx} Selective Highlighting List Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This client-side component provides the main dashboard view for cherry picking and flooding analysis within the EkoIntelligence ESG platform.
 * @example
 * ```tsx
 * // Rendered at /customer/dashboard/gw/cherry?entity=AAPL&run=latest
 * <Page />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";

import React, { useEffect } from 'react'
import { CherryTypeV2 } from '@/types'
import NoData from '@/components/no-data'
import { useEntity } from '@/components/context/entity/entity-context'
import { useNav } from '@/components/context/nav/nav-context'
import { useAuth } from '@/components/context/auth/auth-context'
import SelectiveHighlightingList from './selective-highlighting-list'
import { EkoPageTitle } from '@/components/page-title'
import { GlassCard } from '@/components/ui/glass-card'
import { Skeleton } from '@/components/ui/skeleton'
import { useNavigationWithParams } from '@/hooks/use-navigation-with-params'

export default function Page() {
    const entityContext = useEntity();
    const auth = useAuth();
    const nav = useNav();
    const navWithParams = useNavigationWithParams();

    useEffect(() => {
        nav.changeNavPath(navWithParams.createNavItems([
            { label: "Dashboard", href: "/customer/dashboard" },
            { label: "Cherry Picking", href: "/customer/dashboard/gw/cherry" }
        ]));
    }, [navWithParams.queryString]);

    // Show loading state if cherry data is still loading
    if (entityContext.isLoadingCherry) {
        return (
            <div className="dashboard-container" data-testid="cherry-picking-content">
                <EkoPageTitle title="Cherry Picking" />
                <div className="space-y-6">
                    {/* Loading skeleton */}
                    <GlassCard variant="subtle" hover={false}>
                        <div className="space-y-4">
                            <Skeleton className="h-8 w-64" />
                            <Skeleton className="h-4 w-32" />
                        </div>
                    </GlassCard>
                    <GlassCard variant="subtle" hover={false}>
                        <div className="space-y-4">
                            <Skeleton className="h-6 w-48" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-3/4" />
                        </div>
                    </GlassCard>
                    <GlassCard variant="subtle" hover={false}>
                        <div className="space-y-4">
                            <Skeleton className="h-6 w-56" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-2/3" />
                        </div>
                    </GlassCard>
                </div>
            </div>
        );
    }

    return entityContext.cherryData && entityContext.cherryData.length > 0 ? (
        <div className="dashboard-container" data-testid="cherry-picking-content">
            <EkoPageTitle title="Cherry Picking" />
            <SelectiveHighlightingList data={entityContext.cherryData} admin={auth.admin} />
        </div>
    ) : (
        <div data-testid="cherry-picking-content">
            <NoData title="No Cherry Picking Data Found" description="No cherry picking or flooding instances found for this entity" />
        </div>
    );
}
