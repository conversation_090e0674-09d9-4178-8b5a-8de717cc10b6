/**
 * ESG Selective Highlighting Analysis List Component - Tabbed Display for Cherry Picking & Flooding Detection
 *
 * This React component provides a comprehensive tabbed interface for displaying selective highlighting
 * analysis results within the EkoIntelligence ESG analysis platform. It renders cherry picking and 
 * flooding analysis instances in organized tabs with statistics, filtering, and sorting capabilities.
 * The component serves as the main container for displaying greenwashing detection results from the
 * backend analytics system.
 *
 * ## Core Functionality
 * - **Tabbed Organization**: Three-tab interface (All, Cherry Picking, Flooding) for organized data presentation
 * - **Data Validation**: Defensive programming with comprehensive validation of `CherryTypeV2` data structures
 * - **Dynamic Sorting**: Severity-based sorting (highest first) for prioritizing critical analysis results
 * - **Statistics Display**: Real-time counts and visual indicators for each analysis type with appropriate icons
 * - **Empty State Handling**: Graceful fallback UI for scenarios with no analysis data available
 * - **Glass-Morphism Design**: Consistent visual styling with translucent cards and glass-effect elements
 *
 * ## Analysis Types & Visual Differentiation
 * - **Cherry Picking Analysis**: Rose-colored theme with Cherry icon - detects selective positive highlighting
 * - **Flooding Analysis**: Amber-colored theme with Waves icon - identifies overwhelming negative information patterns
 * - **Combined Statistics**: Header card displays total counts with breakdown by analysis type
 * - **Visual Indicators**: Color-coded tabs and icons for immediate analysis type recognition
 *
 * ## Data Flow & System Integration
 * The component integrates with the EkoIntelligence platform architecture:
 * - **Data Source**: Receives `CherryTypeV2[]` from parent components via props
 * - **Backend Analytics**: Data originates from Python analytics engine in `/backoffice/src/eko/analysis_v2/`
 * - **Database Layer**: Analytics results synchronized to `xfer_selective` table in customer database
 * - **Component Hierarchy**: Renders individual results using `SelectiveHighlightingCard` child components
 * - **Administrative Security**: Passes admin permissions to child components for record management
 *
 * ## UI Components & Design System
 * - **Tabs Component**: shadcn/ui tabs with glass-effect styling for consistent visual hierarchy
 * - **GlassCard Containers**: Main layout containers with dynamic border colors and glass-morphism effects
 * - **Badge Elements**: Count displays with secondary variant styling for non-intrusive information presentation
 * - **Lucide Icons**: Cherry, Waves, and AlertTriangle icons for visual analysis type identification
 * - **Empty State Cards**: Subtle glass cards with appropriate messaging for scenarios with no data
 *
 * ## Props Interface & Component Contract
 * - **data: CherryTypeV2[]**: Array of selective highlighting analysis records from `xfer_selective` table
 * - **admin: boolean**: Administrative permissions flag for enabling management controls in child components
 * - **Validation Logic**: Comprehensive data structure validation to ensure robust rendering
 * - **Error Boundaries**: Console warning system for invalid data structures with graceful degradation
 *
 * ## Database Schema Integration
 * Works with the `xfer_selective` PostgreSQL table structure:
 * - **Primary Key**: `id` integer for unique record identification
 * - **Entity Association**: `entity_xid` and `run_id` for linking to specific analysis runs
 * - **Analysis Data**: `model` JSONB field containing complex analysis metadata and statements
 * - **Performance Columns**: `analysis`, `explanation`, `reason` extracted for improved query performance
 * - **Row Level Security**: Integrated with Supabase RLS policies for authenticated user access
 *
 * ## System Architecture Context
 * This component operates within the broader EkoIntelligence platform:
 * - **Frontend Layer**: Next.js 15 App Router with React Server Components and client-side interactivity
 * - **Analytics Pipeline**: Python backend processes corporate documents and generates analysis flags
 * - **Data Synchronization**: Results transferred from analytics database to customer database via `xfer_` tables
 * - **User Interface**: Glass-morphism design system with consistent visual patterns across platform
 * - **Security Model**: Admin-controlled deletion capabilities with proper permission checking
 *
 * @see https://react.dev/learn React Documentation
 * @see https://lucide.dev/guide/packages/lucide-react Lucide React Icons
 * @see https://ui.shadcn.com/docs/components/tabs shadcn/ui Tabs Component
 * @see {@link ./selective-highlighting-card.tsx} Individual Analysis Card Component
 * @see {@link /Users/<USER>/worktrees/279/apps/customer/types/cherry.ts} CherryTypeV2 Interface
 * <AUTHOR>
 * @updated 2025-07-22
 * @description ESG selective highlighting analysis display component with tabbed interface for cherry picking and flooding detection results
 * @example ```tsx
 * <SelectiveHighlightingList 
 *   data={selectiveHighlightingData} 
 *   admin={isUserAdmin}
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'
import React from 'react'
import { CherryTypeV2 } from '@/types'
import SelectiveHighlightingCard from './selective-highlighting-card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { AlertTriangle, Cherry, Waves } from 'lucide-react'
import { GlassCard } from '@/components/ui/glass-card'
import { Badge } from '@/components/ui/badge'

interface SelectiveHighlightingListProps {
  data: CherryTypeV2[];
  admin: boolean;
}

export default function SelectiveHighlightingList({ data, admin }: SelectiveHighlightingListProps) {
  // Add debugging to check data structure
  console.log("SelectiveHighlightingList received data:", data);

  // Validate data structure before processing
  const validData = data.filter(item => {
    // Check if item has the expected structure
    const isValid = item &&
                   typeof item === 'object' &&
                   item.model &&
                   typeof item.model === 'object' &&
                   item.model.model;

    if (!isValid) {
      console.warn("Invalid item structure:", item);
    }

    return isValid;
  });

  console.log("Valid data items:", validData.length);

  // Separate cherry picking and flooding instances
  const cherryPickingInstances = validData.filter(item => item.model.model === 'cherry_picking');
  const floodingInstances = validData.filter(item => item.model.model === 'flooding');

  // Sort instances by severity (highest first)
  const sortedCherryPicking = [...cherryPickingInstances].sort((a, b) =>
    (b.model.severity || 0) - (a.model.severity || 0)
  );
  const sortedFlooding = [...floodingInstances].sort((a, b) =>
    (b.model.severity || 0) - (a.model.severity || 0)
  );

  // Count instances
  const cherryPickingCount = cherryPickingInstances.length;
  const floodingCount = floodingInstances.length;
  const totalCount = validData.length;

  if (totalCount === 0) {
    return (
      <GlassCard variant="subtle" className="text-center">
        <div className="flex flex-col items-center space-y-4">
          <AlertTriangle className="h-12 w-12 text-muted-foreground/50" />
          <p className="text-muted-foreground">No selective highlighting instances found for this entity.</p>
        </div>
      </GlassCard>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with stats */}
      <GlassCard variant="brand" className="border-l-4 border-l-brand">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-6 w-6 text-brand" />
              <h2 className="text-xl font-semibold">Selective Highlighting Analysis</h2>
            </div>
            <Badge variant="secondary" className="text-sm">
              {totalCount} instance{totalCount !== 1 ? 's' : ''} found
            </Badge>
          </div>
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Cherry className="h-4 w-4 text-rose-500" />
              <span>{cherryPickingCount} cherry picking</span>
            </div>
            <div className="flex items-center space-x-1">
              <Waves className="h-4 w-4 text-amber-500" />
              <span>{floodingCount} flooding</span>
            </div>
          </div>
        </div>
      </GlassCard>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3 glass-effect-subtle rounded-2xl">
          <TabsTrigger value="all" className="rounded-xl">
            All ({totalCount})
          </TabsTrigger>
          <TabsTrigger value="cherry-picking" className="rounded-xl">
            <Cherry className="h-4 w-4 mr-2" />
            Cherry Picking ({cherryPickingCount})
          </TabsTrigger>
          <TabsTrigger value="flooding" className="rounded-xl">
            <Waves className="h-4 w-4 mr-2" />
            Flooding ({floodingCount})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6 space-y-6">
          {validData.length === 0 ? (
            <GlassCard variant="subtle" className="text-center">
              <p className="text-muted-foreground">No instances found</p>
            </GlassCard>
          ) : (
            validData.map(instance => (
              <SelectiveHighlightingCard
                key={instance.id}
                data={instance}
                admin={admin}
              />
            ))
          )}
        </TabsContent>

        <TabsContent value="cherry-picking" className="mt-6 space-y-6">
          {sortedCherryPicking.length === 0 ? (
            <GlassCard variant="subtle" className="text-center">
              <div className="flex flex-col items-center space-y-3">
                <Cherry className="h-8 w-8 text-muted-foreground/50" />
                <p className="text-muted-foreground">No cherry picking instances found</p>
              </div>
            </GlassCard>
          ) : (
            sortedCherryPicking.map(instance => (
              <SelectiveHighlightingCard
                key={instance.id}
                data={instance}
                admin={admin}
              />
            ))
          )}
        </TabsContent>

        <TabsContent value="flooding" className="mt-6 space-y-6">
          {sortedFlooding.length === 0 ? (
            <GlassCard variant="subtle" className="text-center">
              <div className="flex flex-col items-center space-y-3">
                <Waves className="h-8 w-8 text-muted-foreground/50" />
                <p className="text-muted-foreground">No flooding instances found</p>
              </div>
            </GlassCard>
          ) : (
            sortedFlooding.map(instance => (
              <SelectiveHighlightingCard
                key={instance.id}
                data={instance}
                admin={admin}
              />
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* Information card */}
      <GlassCard variant="subtle" className="border-l-4 border-l-amber-500">
        <div className="flex items-start gap-4">
          <AlertTriangle className="h-6 w-6 text-amber-500 mt-1 flex-shrink-0" />
          <div className="space-y-2">
            <h3 className="font-semibold text-amber-700 dark:text-amber-300">About Selective Highlighting</h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              Selective highlighting refers to practices where an entity emphasizes positive aspects while downplaying negative ones.
              <span className="font-medium text-rose-600 dark:text-rose-400"> Cherry picking</span> involves selectively presenting favorable data while ignoring unfavorable information.
              <span className="font-medium text-amber-600 dark:text-amber-400"> Flooding</span> involves overwhelming audiences with minor positive claims to distract from more significant negative issues.
            </p>
          </div>
        </div>
      </GlassCard>
    </div>
  );
}
