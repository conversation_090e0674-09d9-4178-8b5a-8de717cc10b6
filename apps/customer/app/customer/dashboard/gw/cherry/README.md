# Cherry Picking Dashboard Module

## Overview

The Cherry Picking Dashboard module is a comprehensive React-based interface for displaying and analyzing selective
highlighting patterns in ESG (Environmental, Social, and Governance) communications. This module serves as part of
EkoIntelligence's greenwashing detection system, specifically targeting two key deceptive communication patterns:

- **Cherry Picking**: Selective presentation of favorable data while ignoring unfavorable information
- **Flooding**: Overwhelming audiences with minor positive claims to distract from significant negative issues

The dashboard provides tabbed organization, detailed analysis cards, citation tracking, and administrative controls for
managing selective highlighting analysis results.

## Specification

### Core Functionality Requirements

1. **Dual Analysis Display**: Support for both cherry picking and flooding analysis types with visual differentiation
2. **Tabbed Interface**: Three-tab organization (All, Cherry Picking, Flooding) with real-time counts
3. **Interactive Analysis Cards**: Expandable cards showing detailed analysis with severity scoring
4. **Citation Integration**: Full document reference support with clickable citations
5. **Administrative Controls**: Admin-only delete functionality with proper permission checking
6. **Glass-Morphism Design**: Consistent UI styling with dynamic color schemes based on analysis type
7. **Loading States**: Skeleton loading UI with glass-effect cards during data fetching
8. **Empty State Handling**: Graceful fallback displays when no analysis data is available

### Data Model Specification

The module processes `CherryTypeV2` objects with the following structure:

```typescript
interface CherryTypeV2 {
  id: number;
  entity_xid: string;
  run_id: string;
  reason: string;
  explanation: string;
  analysis: string;
  label: string;
  model: {
    model: 'cherry_picking' | 'flooding';
    severity: number;
    score: number;
    confidence: number;
    positive_statements: StatementAndMetadata[];
    negative_statements: StatementAndMetadata[];
    citations: CitationType[];
    created_at?: string;
  };
}
```

### Scoring and Severity Thresholds

- **Severity Levels**: 0-100 scale with visual indicators
    - `≥70`: Destructive (high risk)
    - `≥40`: Secondary (medium risk)
    - `<40`: Default (low risk)
- **Confidence Scoring**: AI confidence percentage for analysis accuracy
- **Impact Values**: Statement-level impact assessments for positive/negative categorization

## Key Components

### 1. **page.tsx** - Main Dashboard Entry Point

- **Role**: Primary route component for `/customer/dashboard/gw/cherry`
- **Responsibilities**:
    - Entity context integration and data fetching coordination
    - Loading state management with skeleton UI
    - Navigation breadcrumb updates
    - Empty state handling and error boundaries
- **Key Features**: Glass-morphism loading cards, admin permission passing

### 2. **selective-highlighting-list.tsx** - Tabbed Analysis Display

- **Role**: Main container component for organizing and displaying analysis results
- **Responsibilities**:
    - Three-tab interface implementation (All, Cherry Picking, Flooding)
    - Data validation and filtering by analysis type
    - Severity-based sorting (highest first)
    - Statistics display with count badges
- **Key Features**: Defensive programming with data structure validation, visual differentiation by analysis type

### 3. **selective-highlighting-card.tsx** - Individual Analysis Display

- **Role**: Detailed card component for rendering individual cherry picking/flooding analyses
- **Responsibilities**:
    - Dynamic color theming based on analysis type (rose for cherry picking, amber for flooding)
    - Severity badge display with dynamic coloring
    - Expandable accordion for statement analysis
    - Citation display with administrative controls
- **Key Features**: AdminDeleteButton integration, comprehensive metadata display, interactive statement categorization

### 4. **cherry-analysis.tsx** - Legacy Analysis Component

- **Role**: Individual cherry picking analysis display (appears to be legacy/deprecated)
- **Responsibilities**:
    - Basic cherry picking analysis rendering
    - Citation aggregation and display
    - Simple layout without advanced UI features
- **Note**: This component appears to be superseded by `selective-highlighting-card.tsx`

## Dependencies

### Core Framework Dependencies

- **Next.js 15 App Router**: Modern React framework with server-side rendering and client-side routing
- **React 18+**: Functional components with hooks for state management and lifecycle
- **TypeScript**: Full type safety with strict typing enabled

### UI Component Dependencies

- **Shadcn/UI Components**:
    - `GlassCard`: Glass-morphism design system components
    - `Badge`: Severity and count indicators
    - `Tabs`: Tabbed interface implementation
    - `Accordion`: Expandable statement sections
    - `Skeleton`: Loading state components
- **Lucide React Icons**: Cherry, Waves, AlertTriangle, Info, TrendingUp icons for visual identification

### Internal System Dependencies

- **Entity Context System**:
    - `useEntity()`: Provides `cherryData` and `isLoadingCherry` state
    - Data fetching coordination for selective highlighting analysis
- **Navigation Context**:
    - `useNav()`: Breadcrumb management and navigation state
    - `useNavigationWithParams()`: Query parameter preservation across routes
- **Authentication Context**:
    - `useAuth()`: Admin permission checking for management controls
- **Citation System**:
    - `CompactCitation`: Document reference display components
    - `reduceCitations()`: Citation deduplication utility
- **Markdown Rendering**:
    - `EkoMarkdown`: Custom markdown renderer with citation support for AI-generated analysis

### Database and Backend Dependencies

- **Customer Database**:
    - `xfer_selective` table: Primary data source for analysis records
    - Supabase Row Level Security for authenticated access
- **Analytics Backend**:
    - Python analytics engine in `/backoffice/src/eko/analysis_v2/selective_highlighting/`
    - Data synchronization pipeline between analytics and customer databases
- **Administrative System**:
    - AdminDeleteButton component with `xfer_selective` table targeting
    - Permission-based record management functionality

## Usage Examples

### Basic Dashboard Access

```typescript
// Navigate to cherry picking dashboard
// URL: /customer/dashboard/gw/cherry?entity=AAPL&run=latest

// The dashboard automatically:
// 1. Loads cherry picking and flooding data for the specified entity
// 2. Updates navigation breadcrumbs
// 3. Displays tabbed interface with analysis results
// 4. Handles loading states and empty data scenarios
```

### Administrative Usage

```typescript
// Admin users can:
// 1. Delete analysis records using AdminDeleteButton
// 2. View detailed citation information with admin controls
// 3. Access additional metadata and debugging information

// Admin permissions are checked via useAuth() context:
const { admin } = useAuth();
// Passed to child components for conditional feature access
```

### Data Flow Example

```typescript
// 1. Entity context fetches data from xfer_selective table
const { cherryData, isLoadingCherry } = useEntity();

// 2. Data is filtered and validated by analysis type
const cherryPickingInstances = data.filter(item => item.model.model === 'cherry_picking');
const floodingInstances = data.filter(item => item.model.model === 'flooding');

// 3. Results are displayed in tabbed interface with severity sorting
const sortedResults = instances.sort((a, b) => (b.model.severity || 0) - (a.model.severity || 0));
```

## Architecture Notes

### System Integration Architecture

```mermaid
graph TB
    A[Analytics Backend<br/>Python] --> B[xfer_selective Table<br/>PostgreSQL]
    B --> C[Entity Context<br/>React Context]
    C --> D[Cherry Dashboard<br/>page.tsx]
    D --> E[Selective Highlighting List<br/>Tabbed Interface]
    E --> F[Analysis Cards<br/>Individual Results]
    F --> G[Citation System<br/>Document References]
    F --> H[Admin Controls<br/>Record Management]
    
    I[User Authentication] --> J[Admin Permissions]
    J --> H
    
    K[Navigation Context] --> D
    L[Glass-Morphism UI] --> E
    L --> F
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant P as Page Component
    participant EC as Entity Context
    participant DB as xfer_selective Table
    participant SHL as Selective Highlighting List
    participant AC as Analysis Cards

    U->>P: Navigate to /cherry?entity=X&run=Y
    P->>EC: Request cherry data for entity
    EC->>DB: Query selective highlighting records
    DB-->>EC: Return CherryTypeV2[] data
    EC-->>P: Provide cherryData and loading state
    P->>SHL: Pass validated data array
    SHL->>SHL: Filter by analysis type (cherry_picking/flooding)
    SHL->>SHL: Sort by severity (highest first)
    SHL->>AC: Render individual analysis cards
    AC->>AC: Display detailed analysis with citations
```

### Component State Management

```mermaid
stateDiagram-v2
    [*] --> Loading
    Loading --> HasData : Data loaded successfully
    Loading --> NoData : No analysis data found
    HasData --> TabView : Display tabbed interface
    TabView --> AllTab : Show all analyses
    TabView --> CherryTab : Show cherry picking only
    TabView --> FloodingTab : Show flooding only
    AllTab --> CardDisplay : Render analysis cards
    CherryTab --> CardDisplay
    FloodingTab --> CardDisplay
    CardDisplay --> ExpandedCard : User interaction
    ExpandedCard --> CardDisplay : Collapse
    NoData --> EmptyState : Display no data message
```

## Known Issues

### Current Limitations

1. **Legacy Component Overlap** (Priority: Medium)
    - `cherry-analysis.tsx` appears to be legacy/deprecated
    - Should be removed or consolidated with `selective-highlighting-card.tsx`
    - May cause confusion for developers

2. **Data Structure Validation** (Priority: Low)
    - Component includes defensive programming for invalid data structures
    - Console warnings for malformed `CherryTypeV2` objects
    - May indicate upstream data quality issues

3. **Citation Deduplication** (Priority: Low)
    - Multiple citation aggregation approaches across components
    - Some code duplication in citation handling logic
    - Could be consolidated into shared utilities

### Related Linear Issues

- **EKO-261**: Cherry Picking / Flooding Analysis - broken *(Fixed)*
    - Issue with analysis fields being read from wrong object location
    - Required fallback messages for null analysis fields
    - Status: Done (July 2025)

- **EKO-232**: JSON object parsing error in selective highlighting data *(Active)*
    - Backend parsing error in `analysis_v2/selective_highlighting/data.py`
    - Affects data loading pipeline for cherry picking analysis
    - Status: In Progress

- **EKO-257**: EKO Behavioural Analysis integration *(Planned)*
    - Request to include cherry picking/flooding analysis in reports
    - Enhancement to connect analysis results with report generation
    - Status: Backlog

### Technical Debt

1. **Component Documentation**: Some inline comments could be expanded for better developer understanding
2. **Error Boundaries**: Could benefit from more granular error handling for API failures
3. **Performance Optimization**: Large datasets might benefit from virtualization or pagination
4. **Accessibility**: Enhanced keyboard navigation and screen reader support needed

## Future Work

### Planned Enhancements

1. **Report Integration** (EKO-257)
    - Integrate cherry picking/flooding analysis results into report generation
    - Provide analysis annotations for identified issues
    - Connect with broader EKO behavioral analysis framework

2. **Enhanced Filtering and Search**
    - Advanced filtering by severity, confidence, date ranges
    - Search functionality across analysis text and citations
    - Sorting options beyond severity (date, confidence, entity)

3. **Data Visualization Improvements**
    - Trend analysis charts for cherry picking patterns over time
    - Comparative analysis between entities
    - Interactive severity distribution visualizations

4. **Performance Optimizations**
    - Virtual scrolling for large datasets
    - Lazy loading of analysis details
    - Cached analysis results with intelligent invalidation

5. **Advanced Administrative Features**
    - Bulk operations for analysis management
    - Analysis approval workflows
    - Audit trails for administrative actions

### API and Backend Improvements

1. **Real-time Updates**
    - WebSocket integration for live analysis updates
    - Real-time notification system for new analyses

2. **Enhanced Analytics**
    - Aggregated statistics and trend analysis
    - Cross-entity comparative metrics
    - Historical pattern recognition

3. **Data Quality Improvements**
    - Enhanced validation in analytics pipeline
    - Automated data quality checks
    - Improved error handling and recovery

## Troubleshooting

### Common Issues

**Problem**: "No Summary Available" messages displayed

- **Cause**: Analysis fields (reason, explanation, analysis) are null in database
- **Solution**: Check data pipeline from analytics backend, verify EKO-261 fixes are applied
- **Debug**: Inspect `model` vs `data` object structure in component props

**Problem**: Loading state never resolves

- **Cause**: Entity context not receiving data or infinite loading state
- **Solution**: Check entity and run URL parameters, verify database connectivity
- **Debug**: Monitor network requests and Entity Context state in React DevTools

**Problem**: Admin controls not visible

- **Cause**: User lacks admin permissions or authentication context not loading
- **Solution**: Verify `is_admin` flag in profiles table, check authentication state
- **Debug**: Inspect `useAuth()` context values in browser DevTools

**Problem**: Citations not displaying

- **Cause**: Citation data malformed or `reduceCitations()` utility failing
- **Solution**: Validate citation data structure, check citation component integration
- **Debug**: Log citation arrays in console, verify `CitationType` interface compliance

### Performance Issues

**Problem**: Slow rendering with large datasets

- **Solution**: Consider implementing virtual scrolling or pagination
- **Optimization**: Memoize expensive calculations, optimize re-renders

**Problem**: Memory usage growth over time

- **Solution**: Check for memory leaks in Entity Context, implement proper cleanup
- **Monitoring**: Use React DevTools Profiler to identify performance bottlenecks

### Development Debugging

```bash
# Enable detailed logging for Entity Context
localStorage.setItem('debug', 'entity-context,cherry-data');

# Monitor API requests
# Open Network tab in DevTools, filter for 'selective' or 'cherry'

# Check component state
# React DevTools → Components → Search for 'SelectiveHighlightingList'
```

## FAQ

### User-Centric Questions

**Q: What's the difference between cherry picking and flooding analysis?**
A: Cherry picking detects when entities selectively highlight positive information while omitting negative details.
Flooding identifies overwhelming presentation of minor positive claims to distract from significant negative issues.
Both are forms of greenwashing in ESG communications.

**Q: How is the severity score calculated?**
A: Severity scores (0-100) are generated by AI analysis of the selective highlighting patterns. Scores ≥70 indicate
high-risk deceptive practices, ≥40 indicate medium risk, and <40 indicate lower risk patterns.

**Q: Can I export or share analysis results?**
A: Currently, sharing is available through URL parameters that preserve entity and run context. Export functionality is
not yet implemented but may be added in future versions.

**Q: Why do some analyses show "No data available"?**
A: This typically occurs when: (1) No selective highlighting patterns were detected for the entity, (2) Analysis
pipeline encountered errors (see EKO-232), or (3) Data hasn't been processed yet for the selected entity/run
combination.

**Q: What do the confidence percentages mean?**
A: Confidence scores represent the AI model's certainty in its analysis. Higher percentages indicate greater confidence
in the selective highlighting detection and severity assessment.

### Developer Questions

**Q: How do I add new analysis types beyond cherry picking and flooding?**
A: Extend the `CherryTypeV2` interface to support additional `model.model` values, update filtering logic in
`selective-highlighting-list.tsx`, and add corresponding UI themes in `selective-highlighting-card.tsx`.

**Q: How can I customize the glass-morphism styling?**
A: Modify the `GlassCard` component variants or override CSS classes. The design system uses Tailwind CSS with custom
glass-effect utilities defined in the project's CSS configuration.

**Q: What's the relationship between this module and the report generation system?**
A: Currently minimal - this module displays analysis results while reports are generated separately. EKO-257 plans to
integrate cherry picking findings into automated report generation.

**Q: How do I test this module effectively?**
A: Use the existing test patterns in `/apps/customer/tests/` with Playwright. Mock the Entity Context data, test tabbed
navigation, verify admin controls, and check empty state handling.

## References

### Links to Documentation

- [Next.js App Router Documentation](https://nextjs.org/docs/app)
- [Shadcn/UI Component Library](https://ui.shadcn.com/)
- [React Context Documentation](https://react.dev/reference/react/useContext)
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react)

### Links to Code Files

- [`page.tsx`](./page.tsx) - Main dashboard entry point
- [`selective-highlighting-list.tsx`](./selective-highlighting-list.tsx) - Tabbed analysis display
- [`selective-highlighting-card.tsx`](./selective-highlighting-card.tsx) - Individual analysis cards
- [`cherry-analysis.tsx`](./cherry-analysis.tsx) - Legacy analysis component
- [`/apps/customer/types/cherry.ts`](../../../../../../types/cherry.ts) - TypeScript interfaces
- [
  `/apps/customer/components/context/entity/entity-context.tsx`](../../../../components/context/entity/entity-context.tsx) -
  Entity Context Provider

### Links to Related Systems

- [Analytics Backend](/backoffice/src/eko/analysis_v2/selective_highlighting/) - Python analysis pipeline
- [Database Schema Documentation](/docs/database-schema.md) - xfer_selective table structure
- [Citation System Components](/apps/customer/components/citation/) - Citation handling utilities
- [Glass-Morphism Design System](/apps/customer/components/ui/) - UI component library

### Links to Third-Party Dependencies

- [React 18 Documentation](https://react.dev/) - Core framework
- [TypeScript Handbook](https://www.typescriptlang.org/docs/) - Type system
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - Styling framework
- [Supabase Documentation](https://supabase.com/docs) - Database and authentication

### Links to Related Linear Issues

- [EKO-261: Cherry Picking / Flooding Analysis - broken](https://linear.app/ekointelligence/issue/EKO-261)
- [EKO-232: JSON object parsing error](https://linear.app/ekointelligence/issue/EKO-232)
- [EKO-257: EKO Behavioural Analysis integration](https://linear.app/ekointelligence/issue/EKO-257)
- [EKO-279: Admin Pages](https://linear.app/ekointelligence/issue/EKO-279) - Current branch development

## Changelog

---

### 2025-07-27

- **Added**: Comprehensive README.md documentation
- **Documented**: All four components with detailed specifications
- **Added**: Architecture diagrams with Mermaid syntax
- **Documented**: Dependencies, usage examples, and troubleshooting guides
- **Added**: FAQ section with user-centric and developer questions
- **Documented**: Known issues from Linear tickets (EKO-261, EKO-232, EKO-257)
- **Added**: Future work planning based on Linear backlog items

### 2025-07-22 (Estimated from code comments)

- **Updated**: All component documentation with detailed JSDoc comments
- **Enhanced**: Type safety and error handling across all components
- **Improved**: Glass-morphism UI consistency and visual design
- **Added**: Comprehensive accessibility and performance considerations

---

(c) All rights reserved ekoIntelligence 2025
