# Greenwashing Analysis Dashboard Module

## Overview

The Greenwashing Analysis Dashboard module is a comprehensive React-based system for detecting, analyzing, and visualizing Environmental, Social, and Governance (ESG) greenwashing patterns in corporate communications. This module serves as the central hub for EkoIntelligence's greenwashing detection capabilities, providing unified access to multiple analysis types that identify deceptive sustainability practices across corporate documents and communications.

The module integrates with EkoIntelligence's dual-database architecture, where advanced AI-powered analytics are processed in the backend Python system and synchronized to the customer database for real-time dashboard display, interactive visualizations, and stakeholder engagement.

## Specification

### Core Requirements

The greenwashing analysis system must provide:

1. **Multi-Modal Analysis**: Unified dashboard for four distinct greenwashing detection patterns:
   - **Claims Analysis**: Corporate sustainability claims vs historical evidence verification
   - **Promises Tracking**: Future commitment monitoring and fulfillment assessment
   - **Vague Language Detection**: Ambiguous environmental terminology identification
   - **Selective Highlighting**: Cherry picking and flooding pattern detection

2. **Centralized Navigation**: Single entry point with intelligent routing to specialized analysis modules
3. **Contextual Data Management**: Shared entity and analysis run context across all analysis types
4. **Performance Optimization**: Efficient data loading with shared resources and lazy loading patterns
5. **Administrative Controls**: Unified permission management across all greenwashing analysis types
6. **Citation Integration**: Comprehensive document traceability and source attribution system
7. **Glass-Morphism UI**: Consistent design system with translucent, heavily rounded design elements

### Data Architecture Standards

- **Unified Entity Context**: Shared entity parameters (`entity`, `run`, `model`) across all analysis modules
- **Database Integration**: Synchronized data from analytics backend via `xfer_*` tables in customer database
- **Row Level Security**: Supabase RLS policies ensuring tenant isolation and access control
- **Real-time State Management**: React Context API for centralized state management
- **Performance Limits**: Optimized display limits and memoization for large dataset handling

## Key Components

### 1. Central Data Utilities (`data.ts`)
- **Purpose**: Legacy placeholder for centralized data loading functionality
- **Current State**: Migrated to EntityContext system for improved state management
- **Historical Context**: Previously contained `loadData` function and `DataType` interface
- **Architecture Role**: Documents migration to distributed data fetching pattern
- **Future Potential**: Reserved for greenwashing-specific utility functions

### 2. Claims Analysis Module (`claims/`)
- **Purpose**: ESG claims vs evidence verification system
- **Key Features**: 
  - High-confidence claim filtering (>50% confidence, ≥30 importance)
  - 7-pattern greenwashing detection (Vague, Misleading, Offsetting, etc.)
  - Interactive timeline visualization and detailed analysis cards
  - Citation management with document attribution
- **Data Source**: `xfer_claims` table with AI-powered analysis results
- **Performance**: Memoized filtering and top 100 display optimization

### 3. Promises Tracking Module (`promises/`)
- **Purpose**: Corporate commitment monitoring and fulfillment analysis
- **Key Features**:
  - Promise status classification (kept/broken/uncertain)
  - Five-year trend visualization with stacked bar charts
  - Assessment history tracking with confidence metrics
  - Evidence integration with supporting/contradicting indicators
- **Data Source**: `xfer_promises` table with temporal analysis capabilities
- **Visualization**: Interactive charts and timeline components

### 4. Cherry Picking Detection Module (`cherry/`)
- **Purpose**: Selective highlighting and flooding pattern detection
- **Key Features**:
  - Dual analysis types (cherry picking vs flooding)
  - Tabbed interface with severity-based sorting
  - Dynamic color theming and expandable analysis cards
  - Administrative controls with permission checking
- **Data Source**: `xfer_selective` table with pattern analysis results
- **UI System**: Glass-morphism design with interactive animations

### 5. Vague Language Analysis Module (`vague/`)
- **Purpose**: Ambiguous environmental terminology identification
- **Key Features**:
  - AI-generated comprehensive analysis summaries (~2000 words)
  - Interactive term lists with vagueness scoring (0-100 scale)
  - Citation integration for source document traceability
  - Administrative management controls
- **Data Source**: `_deprecated_xfer_gw_vague_v2` table (pending migration)
- **Analysis Types**: Quantitative ambiguity, temporal vagueness, comparative ambiguity

### 6. Document Analysis Route (`doc/[id]/`)
- **Purpose**: Individual document-level greenwashing assessment
- **Features**: Dynamic routing for specific document analysis
- **Integration**: Links with all analysis modules for document-centric views

## Dependencies

### Core Framework Dependencies
- **Next.js 15 App Router**: Modern React framework with server-side rendering and dynamic routing
- **React 18+**: Latest React features including hooks, Server Components, and concurrent features
- **TypeScript**: Complete type safety with strict compilation and interface definitions

### UI Framework Dependencies
- **Tailwind CSS**: Utility-first CSS framework with glass-morphism design system
- **shadcn/ui**: Component library built on Radix UI primitives (Cards, Badges, Tabs, Accordion)
- **Framer Motion**: Animation library for smooth transitions and interactive effects
- **Lucide React**: Modern icon library for status indicators and visual elements

### Data & State Management
- **Supabase Client**: Database access with Row Level Security and real-time capabilities
- **React Context API**: State management via EntityContext, NavigationContext, and AuthenticationContext
- **Entity Context Provider**: Centralized data management for all greenwashing analysis types

### Internal System Dependencies
- **Citation System**: Document reference and traceability infrastructure
- **EkoMarkdown**: Custom markdown renderer with citation support for AI-generated analysis
- **AdminDeleteButton**: Specialized administrative controls with security integration
- **Navigation Context**: Breadcrumb management and query parameter preservation

### Database Integration
- **Customer Database**: Multiple `xfer_*` tables for optimized dashboard performance
  - `xfer_claims`: Claims analysis results
  - `xfer_promises`: Promise tracking data
  - `xfer_selective`: Cherry picking/flooding analysis
  - `_deprecated_xfer_gw_vague_v2`: Vague language data (pending migration)
- **Analytics Database**: Backend processing in corresponding `ana_*` tables
- **Supabase RLS**: Row Level Security for tenant isolation and access control

## Usage Examples

### Basic Greenwashing Dashboard Access
```typescript
// Navigate to main greenwashing dashboard hub
// URL: /customer/dashboard/gw?entity=AAPL&run=latest&model=ekoIntelligence

// Available analysis modules:
// - /gw/claims - ESG claims vs evidence analysis
// - /gw/promises - Corporate promise tracking  
// - /gw/cherry - Selective highlighting detection
// - /gw/vague - Vague language analysis
```

### Module-Specific Navigation
```typescript
// Claims analysis with filtering
// URL: /gw/claims?entity=AAPL&run=latest
// Features: High-confidence claims, greenwashing pattern detection

// Promise tracking with timeline
// URL: /gw/promises?entity=AAPL&run=latest  
// Features: Status classification, five-year trends, evidence correlation

// Cherry picking detection with tabbed interface
// URL: /gw/cherry?entity=AAPL&run=latest
// Features: Pattern type filtering, severity sorting, administrative controls

// Vague language analysis with AI summaries
// URL: /gw/vague?entity=AAPL&run=latest
// Features: Comprehensive summaries, interactive term lists, scoring system
```

### Administrative Operations
```typescript
// Admin users have enhanced controls across all modules:
// - Delete analysis records with proper permissions
// - Access detailed citation information and metadata
// - View debugging information and processing details
// - Manage and curate analysis results

const { admin } = useAuth();
// Admin permissions checked via AuthContext across all modules
```

## Architecture Notes

### System Integration Architecture

```mermaid
graph TB
    A[Corporate Documents] --> B[Analytics Backend Python]
    B --> C[AI Analysis Pipeline]
    C --> D[Analytics Database ana_* tables]
    D --> E[Data Sync Layer]
    E --> F[Customer Database xfer_* tables]
    F --> G[Supabase Client]
    G --> H[EntityContext Provider]
    H --> I[Greenwashing Dashboard Hub]
    
    I --> J[Claims Analysis]
    I --> K[Promises Tracking] 
    I --> L[Cherry Picking Detection]
    I --> M[Vague Language Analysis]
    
    subgraph "Analytics Processing"
        C --> N[Claims vs Evidence AI]
        C --> O[Promise Fulfillment AI]
        C --> P[Selective Highlighting AI]
        C --> Q[Vague Language NLP]
    end
    
    subgraph "Frontend Analysis Modules"
        J --> R[Interactive Claims List]
        K --> S[Promise Timeline Charts]
        L --> T[Pattern Detection Cards]
        M --> U[Term Analysis Interface]
    end
```

### Component Architecture Hierarchy

```mermaid
graph TB
    A[/gw/ Dashboard Hub] --> B[EntityContext Provider]
    A --> C[NavigationContext]
    A --> D[AuthContext]
    
    B --> E[Claims Module /claims/]
    B --> F[Promises Module /promises/]
    B --> G[Cherry Module /cherry/]
    B --> H[Vague Module /vague/]
    
    E --> I[ClaimsListV2]
    E --> J[ClaimsTimeline]
    E --> K[ClaimDetailV2]
    
    F --> L[PromisesList]
    F --> M[PromisesTimeline]
    F --> N[PromisesFiveYearChart]
    F --> O[PromiseAssessmentHistory]
    
    G --> P[SelectiveHighlightingList]
    G --> Q[SelectiveHighlightingCard]
    
    H --> R[VagueTermsList]
    H --> S[VagueTerm]
    
    subgraph "Shared Components"
        T[EkoMarkdown]
        U[CitationSystem]
        V[AdminDeleteButton]
        W[GlassCard UI]
    end
    
    I --> T
    I --> U
    I --> V
    I --> W
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant GW as GW Dashboard Hub
    participant EC as EntityContext
    participant SC as Supabase Client
    participant DB as Customer Database
    participant M as Analysis Module
    
    U->>GW: Navigate to /gw?entity=X&run=Y
    GW->>EC: Initialize entity context
    EC->>SC: Fetch all analysis data types
    SC->>DB: Query xfer_claims, xfer_promises, xfer_selective, xfer_vague
    DB-->>SC: Return analysis datasets
    SC-->>EC: Consolidated analysis data
    EC-->>GW: Provide data context
    
    U->>M: Navigate to specific module (/claims, /promises, etc.)
    M->>EC: Request module-specific data
    EC-->>M: Filtered data for analysis type
    M->>M: Apply module-specific filters & processing
    M->>U: Render interactive analysis interface
    
    Note over M,U: Module-specific interactions (filtering, sorting, detailed views)
```

## Known Issues

### Resolved Issues
- **EKO-67** ✅ **RESOLVED**: Vague Terms icon removed from dashboard - Analysis properly associated with documents
- **EKO-261** ✅ **RESOLVED**: Cherry Picking/Flooding analysis fixed - Field reading corrected
- **EKO-301** ✅ **RESOLVED**: Claims page sidebar navigation blocking fixed
- **EKO-291** ✅ **RESOLVED**: Claims navigation failures fixed
- **EKO-158** ✅ **RESOLVED**: Removed hallucinated filter features across modules

### Active Issues
- **EKO-298**: Broken promises evidence expansion - "+8 more pieces of evidence" displayed without expansion option (High Priority)
- **EKO-234**: Supporting evidence for broken promises may be insufficient or weak in some cases (High Priority)
- **EKO-303**: Font size inconsistencies in claim display text across components
- **EKO-211**: Report generation may not include all negative flags from dashboard
- **EKO-232**: JSON object parsing error in selective highlighting data pipeline (Active)

### Technical Debt
1. **Schema Migration**: Vague language module uses deprecated table naming (`_deprecated_xfer_gw_vague_v2`)
2. **Component Size**: Large component files (>400 lines) across modules need refactoring
3. **Performance**: Large datasets could benefit from virtualization and pagination
4. **Type Safety**: Some database query type casting needs improvement
5. **Legacy Code**: `cherry-analysis.tsx` appears deprecated and should be consolidated

## Future Work

### Planned Enhancements (Based on Requirements & Linear Tickets)

1. **Evidence Expansion System** (EKO-298 Priority)
   - Implement expandable evidence sections across all modules
   - Add modal views for comprehensive evidence browsing
   - Enhanced evidence filtering and categorization capabilities

2. **Integrated Behavioral Analysis** (EKO-257)
   - Connect greenwashing analysis with behavioral patterns
   - Cross-reference findings between analysis modules
   - Enhanced report generation with comprehensive greenwashing assessment

3. **Advanced Analytics Integration**
   - Real-time processing status indicators across all modules
   - Trend analysis charts showing greenwashing patterns over time
   - Comparative analysis between entities and industry benchmarks
   - Export functionality for compliance reporting and stakeholder communication

4. **Performance Optimizations**
   - Implement virtual scrolling for large datasets across all modules
   - Add client-side caching with optimistic updates
   - Optimize database queries with better indexing strategies
   - Lazy loading of detailed analysis components

5. **Enhanced User Experience**
   - Advanced filtering UI with date ranges and pattern type categorization
   - Search functionality across analysis text and citations
   - Real-time notifications for analysis updates and new findings
   - Mobile and accessibility improvements (WCAG 2.1 compliance)

6. **Schema and Data Improvements**
   - Migrate vague language analysis to modern table schema
   - Standardize data structures across all analysis types
   - Enhanced validation in analytics pipeline
   - Improved error handling and recovery mechanisms

### Integration Roadmap

1. **Cross-Module Analysis**
   - Unified greenwashing risk scoring across all analysis types
   - Pattern correlation analysis between different greenwashing methods
   - Comprehensive entity reliability assessment dashboard

2. **Advanced Reporting**
   - Automated report generation including all greenwashing analysis findings
   - Customizable report templates for different stakeholder needs
   - API endpoints for external system integration

3. **Collaborative Features**
   - User annotations and collaborative analysis tools
   - Audit trails for administrative actions across all modules
   - Enhanced administrative workflows and approval processes

## Troubleshooting

### Common Issues

**Analysis modules not loading data**
- Verify entity and run parameters in URL (?entity=EntityId&run=latest)
- Check EntityContext initialization and data fetching status
- Ensure Supabase client is properly configured with correct project settings
- Verify user permissions and Row Level Security policies

**Empty analysis results across modules**
- Confirm entity has been processed by analytics backend pipeline
- Check analysis run completion status for selected entity/run combination
- Verify data synchronization between analytics and customer databases
- Review entity-specific processing logs for errors

**Navigation context not updating**
- Clear browser cache and localStorage for fresh session state
- Verify React Context providers are properly configured in component tree
- Check query parameter preservation across route changes
- Monitor React DevTools for context provider state

**Administrative controls not visible**
- Confirm user has admin permissions (`is_admin: true` in profiles table)
- Check AuthContext loading status and authentication state
- Verify AdminDeleteButton components have proper table permissions
- Review Supabase RLS policies for administrative access

### Performance Issues

**Slow rendering with large datasets**
- Monitor EntityContext data loading performance
- Consider implementing pagination or virtual scrolling
- Review memoization dependencies for cascade re-renders
- Check database query performance with query plans

**Memory usage growth during session**
- Review EntityContext cleanup and data caching strategies
- Monitor for memory leaks in React components
- Use React DevTools Profiler to identify performance bottlenecks
- Implement proper component unmounting cleanup

### Debug Mode

Enable comprehensive logging across all modules:
```bash
# Add debug query parameter for enhanced logging
/customer/dashboard/gw/claims?entity=EntityId&debug=true

# Monitor network requests in DevTools
# Filter for 'claims', 'promises', 'selective', 'vague' API calls

# Check React Context state
# React DevTools → Components → Search for 'EntityContext'
```

## FAQ

### User-Centric Questions

**Q: What types of greenwashing does the system detect?**
A: The system identifies four main categories: (1) Claims Analysis - verifying corporate sustainability statements against evidence, (2) Promises Tracking - monitoring commitment fulfillment, (3) Selective Highlighting - detecting cherry picking and flooding tactics, (4) Vague Language - identifying ambiguous environmental terminology.

**Q: How accurate are the AI-powered analysis results?**
A: Analysis accuracy varies by module: Claims require >50% AI confidence for display, Promises include confidence scoring for assessment reliability, Cherry Picking uses severity scoring (0-100), and Vague Language provides contextual scoring. All modules include citation traceability for verification.

**Q: Can I export greenwashing analysis results?**
A: Export functionality is currently limited. Users can copy analysis content or use browser print functionality. Comprehensive export features for compliance reporting are planned for future releases.

**Q: How often is greenwashing analysis updated?**
A: Analysis data is synchronized from the analytics database based on processing runs. Update frequency depends on entity-specific analysis schedules, document availability, and processing pipeline completion.

**Q: What permissions do I need to access different analysis modules?**
A: All users with entity access can view analysis results. Administrative features (delete controls, detailed metadata) require `admin: true` permissions with proper Row Level Security policies.

**Q: How does the system handle large amounts of analysis data?**
A: The system implements performance optimizations including display limits (e.g., top 100 claims), memoized filtering operations, lazy loading of detailed components, and responsive UI with skeleton loading states.

### Developer Questions

**Q: How do I add a new greenwashing analysis type?**
A: Create a new subdirectory under `/gw/`, implement the analysis interface following existing module patterns, update EntityContext to include the new data type, add corresponding database table integration, and extend the navigation system.

**Q: How can I customize the glass-morphism styling across modules?**
A: Modify the shared `GlassCard` component variants or override CSS classes. The design system uses Tailwind CSS with custom glass-effect utilities defined in the project's global CSS configuration.

**Q: What's the relationship between analytics and customer databases?**
A: Analytics backend processes raw documents in `ana_*` tables, then synchronizes results to customer database `xfer_*` tables. The frontend consumes `xfer_*` tables for optimized performance and user access control.

**Q: How do I test greenwashing analysis components?**
A: Use existing Playwright test patterns in `/apps/customer/tests/`. Mock EntityContext data, test module navigation, verify analysis display, check administrative controls, and ensure proper error handling.

## References

### Documentation Links
- [Next.js App Router Documentation](https://nextjs.org/docs/app/building-your-application/routing)
- [Supabase React Integration](https://supabase.com/docs/guides/getting-started/tutorials/with-react)
- [React Context API Documentation](https://react.dev/reference/react/useContext)
- [Framer Motion Animation Guide](https://www.framer.com/motion/)
- [shadcn/ui Component Library](https://ui.shadcn.com/)
- [Tailwind CSS Glass-morphism Effects](https://tailwindcss.com/docs/backdrop-blur)

### Related Code Files
- [`/apps/customer/types/claim.ts`](../../../types/claim.ts) - ClaimTypeV2 Interface Definitions
- [`/apps/customer/types/promise.ts`](../../../types/promise.ts) - PromiseTypeV2 Interface Definitions  
- [`/apps/customer/types/cherry.ts`](../../../types/cherry.ts) - CherryTypeV2 Interface Definitions
- [`/apps/customer/types/vague.ts`](../../../types/vague.ts) - VagueType Interface Definitions
- [`/apps/customer/components/context/entity/entity-context.tsx`](../../components/context/entity/entity-context.tsx) - Entity Context Provider
- [`/apps/customer/components/context/nav/nav-context.tsx`](../../components/context/nav/nav-context.tsx) - Navigation Context Provider
- [`/apps/customer/components/context/auth/auth-context.tsx`](../../components/context/auth/auth-context.tsx) - Authentication Context Provider
- [`/apps/customer/components/markdown/eko-markdown.tsx`](../../components/markdown/eko-markdown.tsx) - Markdown Renderer with Citation Support
- [`/apps/customer/components/admin.tsx`](../../components/admin.tsx) - Administrative Controls Component

### Related README Files
- [`/apps/customer/app/customer/dashboard/gw/claims/README.md`](./claims/README.md) - Claims Analysis Module Documentation
- [`/apps/customer/app/customer/dashboard/gw/promises/README.md`](./promises/README.md) - Promises Tracking Module Documentation
- [`/apps/customer/app/customer/dashboard/gw/cherry/README.md`](./cherry/README.md) - Cherry Picking Detection Module Documentation
- [`/apps/customer/app/customer/dashboard/gw/vague/README.md`](./vague/README.md) - Vague Language Analysis Module Documentation
- [`/apps/customer/app/customer/dashboard/flags/README.md`](../flags/README.md) - ESG Flags Dashboard Module
- [`/apps/customer/README.md`](../../../README.md) - Customer Application Documentation

### External Resources
- [ESG Greenwashing Detection Research](https://www.sciencedirect.com/topics/economics-econometrics-and-finance/greenwashing)
- [Corporate Sustainability Reporting Standards](https://www.globalreporting.org/standards/)
- [Environmental Claims Verification Guidelines](https://www.iso.org/standard/50584.html)
- [Glass-morphism Design System Principles](https://uxdesign.cc/glassmorphism-in-user-interfaces-1f39bb1308c9)
- [React Performance Optimization Best Practices](https://react.dev/learn/render-and-commit)

### Third-Party Dependencies
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Radix UI Primitives](https://www.radix-ui.com/primitives)
- [React 18+ Documentation](https://react.dev/reference/react)
- [PostgreSQL JSONB Documentation](https://www.postgresql.org/docs/current/datatype-json.html)
- [Supabase Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)

### Linear Issue References
- [EKO-298: Broken promises evidence expansion](https://linear.app/ekointelligence/issue/EKO-298) - High Priority
- [EKO-234: Supporting Evidence Quality](https://linear.app/ekointelligence/issue/EKO-234) - High Priority  
- [EKO-257: EKO Behavioral Analysis Integration](https://linear.app/ekointelligence/issue/EKO-257) - Medium Priority
- [EKO-232: JSON parsing error in selective highlighting](https://linear.app/ekointelligence/issue/EKO-232) - Active
- [EKO-303: Font size inconsistencies](https://linear.app/ekointelligence/issue/EKO-303) - Active
- [EKO-211: Report generation completeness](https://linear.app/ekointelligence/issue/EKO-211) - Active

---

## Changelog

### 2025-07-30
- **CREATED**: Comprehensive README.md documentation for Greenwashing Analysis Dashboard Module
- **ANALYZED**: Complete module structure including 4 specialized analysis types and central data utilities
- **DOCUMENTED**: System architecture with mermaid diagrams showing data flow and component hierarchy
- **RESEARCHED**: Dependencies including Next.js 15, React 18+, Supabase, and internal context systems
- **SPECIFIED**: Usage examples covering all analysis modules and administrative operations
- **IDENTIFIED**: Known issues from Linear tickets including evidence expansion and schema migration needs
- **PLANNED**: Future work roadmap based on Linear requirements and technical debt analysis
- **PROVIDED**: Comprehensive troubleshooting guide with common issues and debug procedures
- **CREATED**: FAQ section addressing user and developer questions about greenwashing analysis
- **COMPILED**: Extensive reference links to documentation, code files, and external resources

(c) All rights reserved ekoIntelligence 2025