/**
 * Next.js App Router User Profile Management Page Component
 *
 * This React Client Component provides a comprehensive user profile management interface within the EkoIntelligence
 * ESG analysis platform. It enables users to update their personal profile information, manage avatar images with
 * advanced editing capabilities, and view their organization details in a user-friendly glass-morphism interface.
 * The component leverages Supabase's Row Level Security (RLS) to ensure users can only access and modify their own
 * profile data while providing seamless avatar storage through Supabase Storage.
 *
 * ## Core Functionality
 * - **Profile Information Management**: Edit and update personal details including full name, username, and website
 * - **Avatar Management**: Upload, crop, rotate, and zoom avatar images with real-time preview capabilities
 * - **Organization Display**: View current organization membership (read-only for security purposes)
 * - **Automatic Profile Creation**: Creates profile records for new users during first access
 * - **Real-time Updates**: Immediate UI feedback and database synchronization for all profile changes
 * - **Mobile-Responsive Design**: Adaptive interface with mobile-specific contact form integration
 * - **Error Handling**: Comprehensive error reporting and user feedback through toast notifications
 *
 * ## Database Integration
 * **profiles Table Schema**:
 * - `id` (uuid, Primary Key, FK to auth.users): User identifier linked to Supabase authentication
 * - `updated_at` (timestamp): Last modification time for profile tracking
 * - `username` (text, unique): User's chosen username with minimum 3 character length constraint
 * - `full_name` (text): User's display name for identification throughout the platform
 * - `avatar_url` (text): Storage path reference to user's avatar image in Supabase Storage
 * - `website` (text): Optional website URL for user's external presence
 * - `organisation` (bigint, FK to acc_organisations): Reference to user's organization membership
 * - `email` (text, unique): User's email address synchronized with authentication system
 * - `name` (text): Full name field for profile display purposes
 * - `feature_flags` (text[]): Array of enabled feature flags for user-specific functionality
 * - `is_admin` (boolean): Administrative access flag (database-controlled, not user-modifiable)
 * - `welcome_message` (text): Customizable welcome message for user dashboard personalization
 *
 * **acc_organisations Table Integration**:
 * - `id` (bigint, Primary Key): Organization identifier used in profiles.organisation foreign key
 * - `name` (text): Organization display name shown in profile interface
 * - `email_domain` (text): Organization email domain for user association
 * - `entity_xid` (text): External entity identifier for ESG analysis integration
 * - `feature_flags` (text[]): Organization-level feature flags inherited by members
 *
 * ## Security & Access Control
 * - **Row Level Security (RLS)**: Supabase policies ensure users can only access their own profile data
 * - **Authentication Required**: Component requires valid Supabase session for all operations
 * - **Profile Protection**: RLS policies prevent unauthorized profile modifications and data access
 * - **Avatar Security**: Supabase Storage integration with signed URLs for secure image access
 * - **Admin Flag Protection**: is_admin field cannot be modified by users, only database administrators
 * - **Organization Read-Only**: Organization membership displayed but not editable by users for data integrity
 *
 * ## Avatar Management System
 * - **Upload Workflow**: File selection triggers advanced avatar editing dialog with real-time preview
 * - **Image Editing**: react-avatar-editor integration providing crop, zoom (1x-3x), and rotation (90° increments)
 * - **Canvas Processing**: Client-side image processing converting edited canvas to optimized JPEG format
 * - **Storage Integration**: Automatic upload to Supabase Storage 'avatars' bucket with unique naming
 * - **URL Management**: Signed URL generation for secure avatar access with 1-hour expiration
 * - **State Synchronization**: Immediate UI updates with optimistic rendering during upload process
 * - **Error Recovery**: Comprehensive error handling for upload failures with user-friendly feedback
 *
 * ## User Interface Features
 * - **Glass-morphism Design**: Consistent with EkoIntelligence design system using translucent cards
 * - **Responsive Layout**: Adaptive design from mobile (full-width) to desktop (max-width 2xl container)
 * - **Interactive Avatar**: Large avatar display with change button and fallback initial display
 * - **Form Validation**: Input validation with loading states and disabled state management
 * - **Mobile Integration**: Conditional contact form display on mobile devices for enhanced user experience
 * - **Accessibility**: Proper ARIA labels, semantic HTML, and keyboard navigation support
 * - **Visual Feedback**: Loading spinners, toast notifications, and state-aware button text
 *
 * ## State Management Architecture
 * - **Local Profile State**: React useState managing complete profile object with all editable fields
 * - **Avatar Editor State**: Separate state management for avatar dialog, file handling, zoom, and rotation
 * - **Loading State**: Global loading flag preventing concurrent operations and providing user feedback
 * - **Optimistic Updates**: Immediate UI updates before server confirmation for responsive user experience
 * - **Error Handling**: Comprehensive error state management with user-friendly toast notifications
 * - **Automatic Refresh**: Profile data refresh after successful avatar updates ensuring UI consistency
 *
 * ## Key Dependencies
 * - **React 18+**: Modern React with hooks for state management and component lifecycle
 * - **Next.js 15 App Router**: Client-side page component with modern routing and navigation
 * - **Supabase**: Database client for authentication, profile management, and secure file storage
 * - **react-avatar-editor**: Advanced avatar editing component with crop, zoom, and rotation capabilities
 * - **Lucide React**: SVG icon library providing consistent, accessible user interface icons
 * - **shadcn/ui Components**: Card, Input, Button, Dialog, Avatar, and Slider components for UI consistency
 * - **Tailwind CSS**: Utility-first CSS framework for responsive glass-morphism design implementation
 * - **useToast Hook**: Custom hook for displaying user notifications and error messages
 * - **useIsMobile Hook**: Device detection hook for responsive behavior and mobile-specific features
 *
 * ## Related Components
 * - **ContactForm Component**: Mobile-specific contact form displayed conditionally based on device type
 * - **Avatar Components**: AvatarImage, AvatarFallback providing consistent avatar display throughout app
 * - **UI Components**: Comprehensive shadcn/ui component library ensuring design system consistency
 * - **Organization Management**: Admin interfaces for managing organization membership and settings
 * - **Authentication System**: Supabase Auth integration for user session management and security
 *
 * ## System Architecture Context
 * This component integrates with the broader EkoIntelligence user management system:
 * - **User Onboarding**: Automatic profile creation for new users with sensible defaults
 * - **Organization Management**: Integration with organizational structure and membership tracking
 * - **Feature Flag System**: Support for user-specific and organization-level feature flags
 * - **Admin Dashboard**: Administrative interfaces for managing user profiles and permissions
 * - **ESG Platform Integration**: Profile data used throughout ESG analysis and reporting features
 * - **Avatar System**: Centralized avatar management with secure storage and access control
 *
 * ## Performance Considerations
 * - **Single Query Loading**: Profile and organization data loaded efficiently with minimal database queries
 * - **Image Optimization**: Client-side image processing reduces server load and improves upload speed
 * - **Signed URL Caching**: Avatar URLs cached with appropriate expiration for performance
 * - **Optimistic UI Updates**: Immediate state updates prevent loading delays during user interactions
 * - **Conditional Rendering**: Mobile-specific components loaded only when needed
 * - **Lazy Loading**: Components and dependencies loaded on-demand for optimal bundle size
 *
 * ## Data Processing Pipeline
 * 1. **Component Mount**: useEffect hook triggers profile data fetching from Supabase
 * 2. **Authentication Validation**: Supabase client validates user session and applies RLS policies
 * 3. **Profile Retrieval**: Query profiles table with organization join for complete user data
 * 4. **Auto-Creation Logic**: Creates new profile records for users without existing profiles
 * 5. **Avatar URL Generation**: Generates signed URLs for secure avatar image access
 * 6. **State Population**: Profile data populates React state for form rendering and editing
 * 7. **User Interactions**: Form submissions and avatar changes trigger optimistic updates and database operations
 *
 * ## Error Handling & Edge Cases
 * - **Authentication Failures**: Graceful handling of unauthenticated users with appropriate redirects
 * - **Missing Profiles**: Automatic profile creation for new users with sensible default values
 * - **Database Connection Issues**: Error logging with user-friendly feedback through toast notifications
 * - **Image Upload Failures**: Comprehensive error handling for avatar upload and processing issues
 * - **Network Interruptions**: Optimistic updates provide immediate feedback even during network issues
 * - **File Type Validation**: Client-side validation ensuring only image files are processed for avatars
 * - **Storage Quota Limits**: Error handling for Supabase Storage capacity and upload size limits
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts Next.js Pages and Layouts
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase Row Level Security
 * @see https://supabase.com/docs/guides/storage Supabase Storage Documentation
 * @see https://react.dev/reference/react/useState React useState Hook
 * @see https://github.com/mosch/react-avatar-editor React Avatar Editor
 * @see https://lucide.dev/icons Lucide React Icons
 * @see https://tailwindcss.com/docs/glass-morphism Tailwind Glass-morphism Design
 * @see {@link ../../supabase/client} Supabase Client Configuration
 * @see {@link ../../../components/contact-form} ContactForm Component
 * @see {@link ../../../hooks/use-mobile} Mobile Detection Hook
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This Next.js client component provides comprehensive user profile management with avatar editing, organization display, and secure database integration using Supabase RLS policies.
 * @example ```typescript
 // Component usage in app layout
 <ProfilePage />
 
 // Profile update pattern
 const { error } = await supabase
   .from('profiles')
   .upsert({
     id: profile.id,
     full_name: profile.full_name,
     username: profile.username,
     website: profile.website,
     updated_at: new Date().toISOString(),
   });

 // Avatar upload pattern
 const { error: uploadError } = await supabase.storage
   .from('avatars')
   .upload(fileName, blob, {
     contentType: 'image/jpeg',
     upsert: true
   });
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

"use client";
import {useEffect, useRef, useState} from 'react';
import {Camera, Loader2, RotateCw, ZoomIn, ZoomOut} from 'lucide-react';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Input} from '@/components/ui/input';
import {Button} from '@/components/ui/button';
import {Label} from '@/components/ui/label';
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar';
import {Dialog, DialogContent, DialogHeader, DialogTitle} from '@/components/ui/dialog';
import {Slider} from '@/components/ui/slider';
import {useToast} from '@/hooks/use-toast';
import AvatarEditor from "react-avatar-editor";

import {createClient} from "@/app/supabase/client";
import {useRouter} from "next/navigation";
import {useIsMobile} from '@/hooks/use-mobile';
import {ContactForm} from "@/components/contact-form";

const ProfilePage = () => {
    const supabase =createClient();
    const { toast } = useToast();
    const router = useRouter();

    const [loading, setLoading] = useState(false);
    const [profile, setProfile] = useState<any>({
        id: '',
        full_name: '',
        username: '',
        website: '',
        avatar_url: null,
        organisation: ''
    });

    // Avatar editor state
    const [avatarDialogOpen, setAvatarDialogOpen] = useState(false);
    const [avatarFile, setAvatarFile] = useState(null);
    const [zoom, setZoom] = useState(1.2);
    const [rotation, setRotation] = useState(0);
    const editorRef = useRef(null);

    const isMobile = useIsMobile();
    // Fetch profile data on mount
    useEffect(() => {
        getProfile();
    }, []);

    const getProfile = async () => {
        try {
            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) throw new Error('No user found');

            let { data, error } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', user.id)
                .single();

            if(data === null && user.id !== null) {
                await supabase.from('profiles').insert({
                    id: user.id,
                    avatar_url: null,
                    full_name: user.email,
                    username: user.email,
                    website: null
                });
                data= (await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', user.id)
                    .single()).data;
            }

            if (error) throw error;

            if(!data) throw new Error('No profile found');



            let profileData = {
                id: user.id,
                full_name: data.full_name || '',
                username: data.username || '',
                website: data.website || '',
                avatar_url: data.avatar_url,
                organisation: ""
            };

            const orgData= (await supabase
                .from('acc_organisations')
                .select('*')
                .eq('id',data.organisation!)
                .single()).data;

            profileData.organisation= orgData?.name || "";

            if(data.avatar_url) {
                // Get public URL
                profileData.avatar_url= (await supabase.storage
                    .from('avatars')
                    .createSignedUrl(data.avatar_url, 3600)).data?.signedUrl!;
            }

            setProfile(profileData);
        } catch (error:any) {
            toast({
                variant: "destructive",
                title: "Error loading profile",
                description: error.message
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e:any) => {
        e.preventDefault();
        try {
            setLoading(true);

            const { error } = await supabase
                .from('profiles')
                .upsert({
                    id: profile.id,
                    full_name: profile.full_name,
                    username: profile.username,
                    website: profile.website,
                    updated_at: new Date().toISOString(),
                });

            if (error) throw error;

            toast({
                title: "Profile updated",
                description: "Your profile has been successfully updated."
            });
        } catch (error: any) {
            toast({
                variant: "destructive",
                title: "Error updating profile",
                description: error.message
            });
        } finally {
            setLoading(false);
        }
    };

    const handleAvatarChange = (e:any) => {
        const file = e.target.files?.[0];
        if (!file) return;
        setAvatarFile(file);
        setAvatarDialogOpen(true);
    };

    const handleSaveAvatar = async () => {
        if (!editorRef.current) return;

        try {
            setLoading(true);

            // Convert canvas to blob
            const canvas = (editorRef.current as any).getImageScaledToCanvas();
            const blob:Blob = await new Promise((resolve) =>
                canvas.toBlob((b:Blob) => resolve(b), 'image/jpeg', 0.95)
            );

            // Create file name
            const fileName = `avatar-${profile.id}-${Date.now()}.jpg`;

            // Upload to Supabase Storage
            const { error: uploadError } = await supabase.storage
                .from('avatars')
                .upload(fileName, blob, {
                    contentType: 'image/jpeg',
                    upsert: true
                })

            if (uploadError) throw uploadError;


            // Update profile with new avatar URL
            const { error: updateError } = await supabase
                .from('profiles')
                .update({
                    avatar_url: fileName,
                    updated_at: new Date().toISOString()
                })
                .eq('id', profile.id);

            if (updateError) throw updateError;

            profile.avatar_url= (await supabase.storage
                .from('avatars')
                .createSignedUrl(fileName, 3600)).data?.signedUrl!;
            setProfile({ ...profile});
            setAvatarDialogOpen(false);

        } catch (error:any) {
            toast({
                variant: "destructive",
                title: "Error updating avatar",
                description: error.message
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="container max-w-2xl p-4" data-testid="account-page">
            <Card>
                <CardHeader>
                    <CardTitle>Account Settings</CardTitle>
                    <CardDescription>
                        Update your profile information and manage your account
                    </CardDescription>
                </CardHeader>
                <CardContent className="mx-4">
                    <form onSubmit={handleSubmit} className="space-y-8" data-testid="profile-form">
                        {/* Avatar Section */}
                        <div className="flex flex-col items-center space-y-4" data-testid="avatar-section">
                            <Avatar className="h-24 w-24" data-testid="current-avatar">
                                <AvatarImage src={profile.avatar_url || ''} />
                                <AvatarFallback className="text-lg">
                                    {profile.full_name?.charAt(0)?.toUpperCase() || 'U'}
                                </AvatarFallback>
                            </Avatar>

                            <div className="flex items-center space-x-2">
                                <Label
                                    htmlFor="avatar-upload"
                                    className="cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                                    data-testid="upload-avatar-button"
                                >
                                    <Camera className="mr-2 h-4 w-4" />
                                    Change Avatar
                                </Label>
                                <Input
                                    id="avatar-upload"
                                    type="file"
                                    accept="image/*"
                                    className="hidden"
                                    onChange={handleAvatarChange}
                                    disabled={loading}
                                    data-testid="avatar-file-input"
                                />
                            </div>
                        </div>

                        {/* Avatar Editor Dialog */}
                        <Dialog open={avatarDialogOpen} onOpenChange={setAvatarDialogOpen}>
                            <DialogContent className="sm:max-w-md">
                                <DialogHeader>
                                    <DialogTitle>Edit Avatar</DialogTitle>
                                </DialogHeader>
                                <div className="flex flex-col items-center space-y-6">
                                    {avatarFile && (
                                        <div className="relative">
                                            <AvatarEditor
                                                ref={editorRef}
                                                image={avatarFile}
                                                width={250}
                                                height={250}
                                                border={25}
                                                borderRadius={125}
                                                color={[0, 0, 0, 0.6]}
                                                scale={zoom}
                                                rotate={rotation}
                                            />
                                        </div>
                                    )}

                                    {/* Controls */}
                                    <div className="w-full space-y-4">
                                        {/* Zoom Control */}
                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <Label>Zoom</Label>
                                                <div className="flex items-center space-x-2">
                                                    <Button
                                                        variant="outline"
                                                        size="icon"
                                                        onClick={() => setZoom(Math.max(1, zoom - 0.1))}
                                                    >
                                                        <ZoomOut className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="outline"
                                                        size="icon"
                                                        onClick={() => setZoom(Math.min(3, zoom + 0.1))}
                                                    >
                                                        <ZoomIn className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                            <Slider
                                                value={[zoom]}
                                                min={1}
                                                max={3}
                                                step={0.1}
                                                onValueChange={([value]) => setZoom(value)}
                                            />
                                        </div>

                                        {/* Rotation Control */}
                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <Label>Rotation</Label>
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    onClick={() => setRotation((rotation + 90) % 360)}
                                                >
                                                    <RotateCw className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>

                                        <Button
                                            className="w-full"
                                            onClick={handleSaveAvatar}
                                            disabled={loading}
                                        >
                                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                            Save Avatar
                                        </Button>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>

                        {/* Profile Details */}
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="full-name">Full Name</Label>
                                <Input
                                    id="full-name"
                                    value={profile.full_name}
                                    onChange={e => setProfile({...profile, full_name: e.target.value})}
                                    disabled={loading}
                                    data-testid="full-name-input"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="username">Username</Label>
                                <Input
                                    id="username"
                                    value={profile.username}
                                    onChange={e => setProfile({...profile, username: e.target.value})}
                                    disabled={loading}
                                    data-testid="username-input"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="website">Website</Label>
                                <Input
                                    id="website"
                                    type="url"
                                    value={profile.website}
                                    onChange={e => setProfile({...profile, website: e.target.value})}
                                    disabled={loading}
                                    data-testid="website-input"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="organisation">Organisation</Label>
                                <Input
                                    id="organisation"
                                    value={profile.organisation}
                                    disabled={true}
                                    data-testid="organization-input"
                                />
                            </div>
                        </div>

                        <Button type="submit" disabled={loading} className="w-full" data-testid="save-profile-button">
                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin"/>}
                            Save Changes
                        </Button>
                    </form>
                </CardContent>
            </Card>
            {isMobile && <ContactForm/>}
        </div>
    );
};

export default ProfilePage;
