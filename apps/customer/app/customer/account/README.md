# Customer Account Management Module

The customer account management module provides comprehensive user account functionality within the EkoIntelligence ESG analysis platform. This module implements secure user profile management, billing support, notification systems, and contact forms using Next.js 15 App Router with Supabase authentication and a modern glass-morphism design system.

## Overview

This module serves as the central hub for customer account management, offering users complete control over their profile information, billing inquiries, system notifications, and support communications. The module integrates seamlessly with the platform's dual-database architecture, providing secure, performant access to user data while maintaining strict Row Level Security (RLS) policies for multi-tenant data isolation.

The account module exemplifies EkoIntelligence's commitment to user experience through sophisticated glass-morphism design elements, responsive layouts, and comprehensive accessibility features. All components are built with TypeScript for type safety and follow modern React patterns with Next.js 15 App Router for optimal performance.

## Specification

### System Requirements
- **Authentication**: Supabase Auth with valid user session required for all account operations
- **Database Access**: Row Level Security (RLS) policies ensure users can only access their own data
- **User Interface**: Glass-morphism design system with heavily rounded elements and translucent surfaces
- **Performance**: Client-side optimistic updates with server-side state synchronization
- **Security**: Input validation, CSRF protection, and secure file upload handling

### Route Structure
```
/customer/account/
├── page.tsx                    # Main account profile management
├── billing/
│   └── page.tsx               # Billing inquiry redirect
├── contact/
│   └── [prefix]/
│       └── page.tsx           # Dynamic contact form with categorization
└── notifications/
    └── page.tsx               # User notification center
```

### Data Architecture
- **profiles**: User profile information with avatar management
- **acc_organisations**: Organization details and membership information
- **acc_messages**: User notification system with categorized message types
- **Storage**: Avatar images stored in Supabase Storage with signed URL access

## Key Components

### 1. Main Account Page (`page.tsx`)
**Purpose**: Comprehensive user profile management with avatar editing capabilities

**Features**:
- **Profile Information Management**: Edit name, username, and website details
- **Avatar System**: Advanced image editing with crop, zoom, and rotation via react-avatar-editor
- **Organization Display**: Read-only organization membership information
- **Real-time Updates**: Optimistic UI updates with server synchronization
- **Mobile Integration**: Conditional contact form display on mobile devices

**Database Integration**:
- `profiles` table: User profile data with unique constraints and validation
- `acc_organisations` table: Organization membership details
- Supabase Storage: Secure avatar image storage with signed URLs

### 2. Billing Page (`billing/page.tsx`)
**Purpose**: Billing inquiry routing and support categorization

**Features**:
- **Smart Redirect**: Routes users to categorized contact form for billing inquiries
- **Parameter Preservation**: Maintains URL search parameters across navigation
- **Glass-morphism UI**: Consistent visual design with AlertCircle icon emphasis
- **SEO Friendly**: Server-side rendering with proper metadata

**Integration Points**:
- Routes to `/customer/account/contact/billing` with preserved query parameters
- Integrates with contact form system for support ticket categorization

### 3. Contact Form (`contact/[prefix]/page.tsx`)
**Purpose**: Dynamic contact form with category-based message routing

**Features**:
- **Dynamic Categorization**: URL prefix parameter categorizes support requests
- **Slack Integration**: Direct message routing to support team channels
- **Authentication Context**: Integrates with user authentication for personalized support
- **Form Validation**: Client-side validation with toast notification feedback

**Supported Categories**:
- `billing`: Payment and subscription inquiries
- `support`: General technical support
- `feedback`: Product feedback and suggestions
- `technical`: Bug reports and technical issues

### 4. Notifications Page (`notifications/page.tsx`)
**Purpose**: Comprehensive user notification management center

**Features**:
- **Message Display**: Chronological display of system notifications
- **Type-Specific Icons**: Visual categorization with Lucide React icons
- **Read State Management**: Mark messages as read with visual differentiation
- **Soft Delete**: Message deletion with audit trail preservation
- **Real-time Updates**: Immediate UI feedback for user actions

**Message Types**:
- `info`: General information notices
- `success`: Positive confirmations and achievements  
- `billing`: Payment and subscription notifications
- `quota`: Usage limits and warnings
- `feature`: New feature announcements
- `error`: Error alerts and problem notifications

## Dependencies

### External Services
- **Supabase**: Authentication, database, and storage services
- **Next.js 15**: App Router framework with server-side rendering
- **React 18+**: Component library with modern hooks and patterns
- **TypeScript**: Type safety and enhanced developer experience

### UI and Design
- **Tailwind CSS**: Utility-first styling for glass-morphism design
- **shadcn/ui**: Component library for consistent UI elements
- **Lucide React**: SVG icon library for visual indicators
- **react-avatar-editor**: Advanced image editing capabilities

### Internal Dependencies
- **AuthContext**: User authentication state management
- **EntityContext**: Selected entity state for navigation
- **Toast System**: User feedback and notification display
- **PageHeader**: Consistent navigation header component
- **ContactForm**: Shared contact form component for mobile integration

## Usage Examples

### Profile Management
```typescript
// Accessing user profile page
// URL: /customer/account
// Automatically loads user profile with organization details
// Provides avatar editing and profile information management

const profileData = {
  full_name: "John Doe",
  username: "johndoe",  
  website: "https://example.com",
  avatar_url: "signed-url-from-supabase-storage",
  organisation: "EkoIntelligence Inc."
};
```

### Billing Inquiries
```typescript
// Billing support routing
// URL: /customer/account/billing?source=subscription
// Redirects to: /customer/account/contact/billing?source=subscription
// Preserves query parameters for context tracking
```

### Contact Form Integration
```typescript
// Dynamic contact categorization
// URL: /customer/account/contact/Support
// Results in Slack message: "Support: <NAME_EMAIL> sent this message: [content]"

// Category-specific routing
const categories = [
  'Support',    // General support
  'Billing',    // Payment inquiries  
  'Technical',  // Bug reports
  'Feedback'    // Product feedback
];
```

### Notification Management
```typescript
// Notification center access
// URL: /customer/account/notifications
// Displays user-specific messages with management capabilities

const messageTypes = [
  'info',     // Blue info icon
  'success',  // Green checkmark
  'billing',  // Credit card icon
  'quota',    // Warning indicators
  'feature',  // Feature announcements
  'error'     // Red warning icons
];
```

## Architecture Notes

### Authentication Flow
```mermaid
graph TD
    A[User Access] --> B{Authenticated?}
    B -->|No| C[Redirect to Login]
    B -->|Yes| D[Load Account Module]
    D --> E[Apply RLS Policies]
    E --> F[Render Account Interface]
    F --> G[Enable Real-time Updates]
```

### Data Synchronization
```mermaid
graph LR
    A[User Profile] --> B[Supabase Client]
    B --> C[RLS Validation]
    C --> D[Database Update]
    D --> E[Optimistic UI]
    E --> F[Server Confirmation]
    F --> G[State Reconciliation]
```

### Avatar Management Flow
```mermaid
sequenceDiagram
    participant U as User
    participant E as Editor
    participant S as Storage
    participant D as Database
    
    U->>E: Upload Image
    E->>E: Crop/Rotate/Zoom
    U->>E: Save Avatar
    E->>S: Upload to Storage
    S->>E: Return File Path
    E->>D: Update Profile
    D->>E: Confirm Update
    E->>U: Display New Avatar
```

### Notification System
```mermaid
graph TD
    A[Admin Creates Message] --> B[acc_messages Table]
    B --> C[RLS Filter by User]
    C --> D[Notification Display]
    D --> E{User Action}
    E -->|Read| F[Update read_at]
    E -->|Delete| G[Update deleted_at]
    F --> H[Update UI State]
    G --> H
```

## Known Issues

### Current Limitations
1. **Avatar Constraints**: 
   - Maximum file size limitations inherited from Supabase Storage
   - Limited to standard image formats (JPEG, PNG, WebP)
   - No support for animated avatars or advanced formats

2. **Organization Management**:
   - Organization membership is read-only for security
   - No self-service organization switching capabilities
   - Admin-only organization management through separate interfaces

3. **Contact Form Integration**:
   - Slack webhook dependency for message routing
   - No offline message queuing for failed deliveries
   - Limited message categorization options

### Technical Debt
- Contact form could benefit from enhanced validation
- Notification pagination needed for users with many messages
- Avatar editor could support additional image manipulation features

## Future Work

### Planned Enhancements (Based on User Feedback)
1. **Enhanced Profile Management**:
   - Two-factor authentication setup interface
   - Privacy settings and data export capabilities
   - Activity log and security audit trail

2. **Advanced Notification System**:
   - Push notification support for mobile devices
   - Email digest configuration options
   - Advanced filtering and search capabilities

3. **Organization Features**:
   - Self-service organization switching
   - Team invitation and management
   - Role-based permission management

### Integration Roadmap
1. **Mobile Application Support**:
   - Native mobile app profile synchronization
   - Mobile-optimized avatar editing
   - Push notification integration

2. **Third-party Integrations**:
   - SSO provider integration (Google, Microsoft, Okta)
   - CRM system integration for contact management
   - Analytics integration for usage tracking

## Troubleshooting

### Common Issues

**Problem**: Profile changes not saving
**Solution**: 
1. Check browser console for JavaScript errors
2. Verify Supabase connection status
3. Ensure user has valid authentication session
4. Check RLS policies in database

**Problem**: Avatar upload failing
**Solution**:
1. Verify image file size (< 10MB recommended)
2. Check Supabase Storage bucket permissions
3. Ensure browser has stable internet connection
4. Try different image format (JPEG/PNG)

**Problem**: Notifications not loading
**Solution**:
1. Check database connection in browser developer tools
2. Verify RLS policies for acc_messages table  
3. Clear browser cache and reload page
4. Check for ad blockers interfering with requests

**Problem**: Contact form not submitting
**Solution**:
1. Verify Slack webhook configuration
2. Check form validation messages
3. Ensure all required fields are completed
4. Test with different message content

### Performance Optimization
- **Avatar Loading**: Implement progressive image loading for better perceived performance
- **Notification Pagination**: Add virtual scrolling for users with many notifications
- **Form Validation**: Debounce validation to reduce server requests
- **Cache Management**: Implement proper cache invalidation for profile updates

## FAQ

**Q: How do I change my organization membership?**
A: Organization membership is managed by administrators. Contact your organization admin or use the support contact form for assistance with organization changes.

**Q: Can I delete my account entirely?**
A: Account deletion must be requested through the contact form. This ensures proper data handling and compliance with data protection regulations.

**Q: Why can't I upload certain image types for my avatar?**
A: The avatar system supports standard web image formats (JPEG, PNG, WebP). Animated images and specialized formats are not supported for performance and compatibility reasons.

**Q: How are my notifications secured?**
A: All notifications use Row Level Security (RLS) policies to ensure you only see messages intended for your account. Messages are encrypted in transit and at rest.

**Q: Can I export my profile data?**
A: Data export functionality is available through the contact form. Submit a request specifying the data you need exported.

**Q: How long are notifications retained?**
A: Notifications are retained indefinitely unless manually deleted. Deleted notifications are soft-deleted for audit purposes but hidden from your view.

## References

### Documentation Links
- [Next.js App Router Documentation](https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts)
- [Supabase Authentication Guide](https://supabase.com/docs/guides/auth/server-side)
- [Supabase Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)
- [Supabase Storage Documentation](https://supabase.com/docs/guides/storage)
- [React Avatar Editor Documentation](https://github.com/mosch/react-avatar-editor)
- [Tailwind CSS Glass-morphism](https://tailwindcss.com/docs/backdrop-blur)

### Related Code Files
- [Customer Layout](/apps/customer/app/customer/layout.tsx) - Main application layout with authentication
- [Auth Context Provider](/apps/customer/components/context/auth/auth-context.tsx) - Authentication state management
- [Account Message Component](/apps/customer/components/account-messages.tsx) - Individual notification renderer
- [Contact Form Component](/apps/customer/components/contact-form.tsx) - Shared contact form implementation
- [Page Header Component](/apps/customer/components/page-header.tsx) - Consistent navigation header
- [Toast System](/apps/customer/components/ui/toaster.tsx) - User feedback notifications

### Related URLs
- [Lucide Icon Library](https://lucide.dev/icons) - SVG icons for message types and UI elements
- [shadcn/ui Components](https://ui.shadcn.com/docs/components) - UI component library
- [TypeScript Handbook](https://www.typescriptlang.org/docs/) - Type safety best practices

### API References
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript) - Database and auth operations
- [Next.js API Routes](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) - Server-side API handling

---

## Changelog

### 2025-07-31
- **Created comprehensive README.md documentation** - Initial documentation covering all account module functionality, architecture, and usage patterns
- **Documented glass-morphism design system integration** - Detailed explanation of visual design principles and implementation
- **Added troubleshooting and FAQ sections** - Common issues and solutions for user and developer reference
- **Included system architecture diagrams** - Mermaid diagrams for authentication flow, data sync, and notification system
- **Comprehensive API and dependency documentation** - Complete reference to external services and internal dependencies

---

(c) All rights reserved ekoIntelligence 2025