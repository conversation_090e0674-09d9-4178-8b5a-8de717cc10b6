/**
 * Next.js App Router Dynamic Route Page for Customer Contact Support
 *
 * This page component provides a customer support contact interface within the EKO Intelligence
 * ESG analytics platform. It implements a dynamic route system that accepts a prefix parameter
 * to categorize different types of customer inquiries (e.g., support, billing, feedback).
 * The page integrates with the platform's glass-morphism design system and provides direct
 * Slack integration for immediate customer support routing.
 *
 * ## Core Functionality
 * - **Dynamic Contact Categories**: Accepts `prefix` route parameter to categorize contact types
 * - **Authenticated Contact Form**: Integrates with user authentication for personalized support
 * - **Direct Slack Integration**: Routes customer messages directly to support team via Slack webhooks
 * - **Glass-morphism UI**: Consistent visual design with platform's translucent design system
 * - **Navigation Integration**: Updates page header and navigation context dynamically
 * - **Input Validation**: Client-side validation with toast notifications for user feedback
 *
 * ## Route Parameters
 * - **Dynamic Parameter**: `prefix` - Contact category prefix (e.g., "Support", "Billing", "Feedback")
 *   - Used as message prefix in Slack notifications: "{prefix}: The user {email} sent this message..."
 *   - Defaults to "Contact" if no prefix provided
 *   - Allows support team to quickly categorize and prioritize incoming messages
 *
 * ## User Experience Flow
 * 1. User navigates to `/customer/account/contact/[prefix]` with specific category
 * 2. Page renders with categorized contact form using the prefix parameter
 * 3. User submits message through authenticated contact form interface
 * 4. Message is routed to Slack with category prefix and user identification
 * 5. Success/error feedback provided via toast notifications
 * 6. Form resets on successful submission for additional messages
 *
 * ## Integration Points
 * - **Authentication System**: Uses AuthContext to identify users submitting contact forms
 * - **Navigation System**: Integrates with NavContext for breadcrumb and page title management
 * - **Slack Integration**: Server-side message routing via Slack webhook API
 * - **Toast System**: User feedback for form validation and submission status
 * - **Glass Design System**: Consistent styling with platform's translucent UI components
 *
 * ## Technical Architecture
 * - **Next.js 15 App Router**: Uses modern App Router with dynamic route parameters
 * - **Client Component**: Marked with 'use client' for interactive form functionality
 * - **Component Composition**: Combines ContactForm and PageHeader for complete page layout
 * - **Type Safety**: Full TypeScript integration with proper parameter typing
 * - **Responsive Design**: Mobile-first approach with glass-morphism design elements
 *
 * ## Dependencies
 * - **Next.js 15**: Latest React framework with App Router and dynamic routing
 * - **React 18+**: Modern React with hooks and client-side state management
 * - **ContactForm Component**: Handles message submission, validation, and Slack integration
 * - **PageHeader Component**: Provides consistent navigation header with glass-morphism styling
 * - **Authentication Context**: User session management and email identification
 * - **Navigation Context**: Breadcrumb and page title management system
 *
 * ## Related Components
 * - ContactForm: Main form component with Slack integration and validation
 * - PageHeader: Navigation header with glass-morphism design and responsive layout
 * - Slack Integration (server-side): Message routing to support team channels
 * - Toast System: User feedback notifications for form interactions
 * - Authentication System: User identification and session management
 *
 * ## Security Considerations
 * - User authentication required for message submission
 * - Server-side Slack webhook integration prevents client-side API key exposure
 * - Input validation and sanitization for message content
 * - Rate limiting through user authentication and form submission controls
 *
 * ## Design System Integration
 * This page follows the EKO Intelligence design system:
 * - **Glass-morphism**: Translucent surfaces with backdrop blur effects
 * - **Rounded Elements**: Generous border radii (1.5rem standard) for modern feel
 * - **Responsive Layout**: Mobile-first design with adaptive navigation
 * - **Color Integration**: Uses platform color scheme with glass-effect classes
 * - **Consistent Spacing**: Follows platform spacing guidelines and component patterns
 *
 * ## Usage Examples
 * - `/customer/account/contact/Support` - General support inquiries
 * - `/customer/account/contact/Billing` - Billing and subscription questions  
 * - `/customer/account/contact/Feedback` - Product feedback and suggestions
 * - `/customer/account/contact/Technical` - Technical issues and bug reports
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://react.dev/reference/react/use-client React Client Components
 * @see https://api.slack.com/messaging/webhooks Slack Webhook Integration
 * @see {@link ../../../../components/contact-form.tsx} ContactForm Component
 * @see {@link ../../../../components/page-header.tsx} PageHeader Component
 * @see {@link ../../../slack.ts} Slack Integration Module
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Customer support contact page with dynamic categorization and Slack integration for the EKO Intelligence ESG analytics platform
 * @example ```typescript
 * // Route: /customer/account/contact/Support
 * // Results in Slack message: "Support: <NAME_EMAIL> sent this message: [content]"
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import {ContactForm} from "@/components/contact-form";
import {PageHeader} from "@/components/page-header";

export default function ContactPage() {

    return (
        <><PageHeader/>
            <div className="container mx-auto py-10">
                <ContactForm/>
            </div>
        </>
    )
}
