/**
 * React Component for ESG Entity Analysis History Display
 *
 * This React functional component provides a comprehensive interface for displaying the historical analysis 
 * records of ESG (Environmental, Social, Governance) entities within the EkoIntelligence platform. 
 * It renders an organized table view of completed entity analyses with navigation capabilities to 
 * detailed entity dashboard reports. The component integrates with the customer database's 
 * `view_entity_analysis_runs` view to present entity analysis history in a user-friendly, glass-morphism 
 * styled interface consistent with the platform's design system.
 *
 * ## Core Functionality
 * - **Entity Analysis History Display**: Presents tabular view of historical ESG entity analyses with comprehensive metadata
 * - **Dashboard Navigation**: Direct navigation to detailed entity dashboard reports via "View Dashboard" buttons
 * - **Multi-Column Data Display**: Organized presentation of entity name, type, analysis date, and actions
 * - **Empty State Handling**: User-friendly messaging when no entity analyses are available with actionable guidance
 * - **Responsive Table Design**: Mobile-optimized horizontal scrolling with adaptive column visibility
 * - **Glass-morphism UI**: Consistent with platform's translucent, modern design aesthetic using Card components
 * - **Test Integration**: Comprehensive `data-testid` attributes for reliable automated testing and validation
 *
 * ## Database Integration
 * **Data Source**: `view_entity_analysis_runs` (Customer Database View)
 * The component consumes data from a PostgreSQL view that joins multiple tables:
 * - `cus_ana_hist_entity_runs`: Entity analysis run records with quota and user tracking
 * - `xfer_entities`: Synchronized entity information including names, types, and external identifiers  
 * - `xfer_runs`: Analysis run metadata including completion status and timestamps
 *
 * **EntityAnalysesRunType Structure** (from `view_entity_analysis_runs`):
 * - `id` (number): Unique analysis record identifier for database references
 * - `quota_id` (number): Associated quota tracking for analysis resource management and billing
 * - `run_by` (string): User identifier who initiated the analysis for audit trails
 * - `name` (string): Human-readable entity name for display (e.g., "Apple Inc.", "Microsoft Corporation")
 * - `type` (string): Entity type classification (e.g., "company", "organization", "government")
 * - `run_id` (number): Associated analysis run identifier linking to detailed results
 * - `created_at` (string): ISO timestamp of analysis completion for chronological sorting
 * - `entity_xid` (string): Entity external identifier for cross-system reference and URL routing
 *
 * ## Navigation Flow
 * **Dashboard Navigation Pattern**: 
 * `/customer/dashboard?entity={entity_xid}&run={run_id}&model=sdg`
 * - Maintains entity context and run-specific data across navigation boundaries
 * - Uses Next.js Link component with `passHref` for optimized client-side navigation
 * - Entity XID serves as primary entity identifier for dashboard data retrieval
 * - Run ID preserves specific analysis context for detailed report viewing
 * - SDG model parameter specifies the analysis framework for report generation
 *
 * ## Component Architecture
 * **Responsive Design Strategy**: 
 * - Horizontal scrolling container (`overflow-x-auto`) for mobile compatibility with complex tables
 * - Progressive column disclosure using Tailwind's responsive classes (`hidden lg:table-cell`, `hidden sm:table-cell`)
 * - Fixed-width table structure with semantic column organization for accessibility
 * - Glass-morphism Card container with header/content separation following platform patterns
 * - Hover states for interactive table rows enhancing user experience and visual feedback
 *
 * **UI Components** (shadcn/ui Integration):
 * - `Card`: Primary container with glass-morphism styling and backdrop blur effects
 * - `CardHeader`: Section header with title styling and proper semantic structure
 * - `CardTitle`: Semantic heading for accessibility, SEO, and screen reader support
 * - `CardContent`: Main content area with proper spacing, padding, and overflow handling
 * - `TableBody`, `TableCell`, `TableHead`, `TableHeader`, `TableRow`: Semantic table components
 * - `Button`: Interactive elements with consistent platform styling and hover states
 * - `Link`: Next.js optimized navigation component for client-side routing
 *
 * ## Date Formatting Integration
 * **Temporal Display**:
 * - Uses `conciseDateTime` utility from `@utils/date-utils` for consistent date formatting
 * - Adaptive date display: full date/time for older entries, concise format for recent entries
 * - Locale-aware formatting supporting internationalization (`navigator.language`)
 * - Performance-optimized with current timestamp caching via `Date.now()`
 *
 * ## Empty State Management
 * **No Data Handling**:
 * - Graceful empty state with semantic messaging and proper ARIA accessibility
 * - Actionable guidance directing users to entity analysis features and workflows
 * - Maintains table structure consistency even without data for layout stability
 * - Test-friendly empty state identification via `data-testid="no-company-analysis-message"`
 *
 * ## Testing Integration
 * **Test Identifiers** (data-testid attributes):
 * - `no-company-analysis-message`: Empty state message container for validation testing
 * - `company-analysis-entry`: Individual analysis record rows for interaction testing
 * - `company-name`: Entity name display cells for content verification
 * - `analysis-date`: Creation timestamp display cells for chronological testing
 * - `analysis-status`: Action button containers for navigation and interaction testing
 *
 * ## System Architecture Integration
 * This component fits into the broader EkoIntelligence ESG analysis ecosystem:
 * - **Analytics Backend**: Python system generates ESG entity analysis runs via analysis pipeline
 * - **Data Sync Layer**: `xfer_` tables synchronize data between analytics and customer databases
 * - **Customer Application**: This component provides user-facing access to historical analysis data
 * - **Entity Dashboard**: Navigation target providing detailed ESG scoring, flags, and analysis
 * - **Quota Management**: Integrated with platform resource tracking and billing systems
 * - **User Authentication**: RLS (Row Level Security) policies ensure data access control
 *
 * ## Performance Considerations
 * - **Client-Side Rendering**: Component optimized for fast client-side data display with minimal re-renders
 * - **Efficient Navigation**: Next.js Link component with optimized prefetching and client-side routing
 * - **Memory Efficient**: Minimal state management, props-driven rendering with no unnecessary state
 * - **Responsive Performance**: Optimized table scrolling and adaptive column visibility for mobile devices
 * - **Date Formatting**: Cached timestamp calculations to minimize repeated `Date.now()` calls
 *
 * ## Key Dependencies
 * - **Next.js 15**: Modern React framework with App Router for server-side rendering and navigation
 * - **React**: Functional component architecture with TypeScript support
 * - **shadcn/ui**: Component library providing consistent design system and accessibility features
 * - **Tailwind CSS**: Utility-first CSS framework for responsive design and glass-morphism effects
 * - **date-fns**: Date formatting utilities for internationalized temporal display
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing Next.js App Router Navigation
 * @see https://ui.shadcn.com/docs/components/card shadcn/ui Card Component Documentation  
 * @see https://ui.shadcn.com/docs/components/button shadcn/ui Button Component Documentation
 * @see https://ui.shadcn.com/docs/components/table shadcn/ui Table Component Documentation
 * @see {@link /apps/customer/types.ts} EntityAnalysesRunType Definition
 * @see {@link /packages/utils/src/date-utils.ts} Date Formatting Utilities
 * <AUTHOR>
 * @updated 2025-07-23
 * @description React component for displaying ESG entity analysis history with navigation to detailed entity dashboards
 * @example ```tsx
 * <EntityAnalysisHistory 
 *   entityAnalyses={[
 *     {
 *       id: 1,
 *       name: "Apple Inc.",
 *       type: "company",
 *       entity_xid: "apple-inc-123",
 *       run_id: 456,
 *       created_at: "2024-01-15T10:30:00Z",
 *       quota_id: 789,
 *       run_by: "user123"
 *     }
 *   ]} 
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import { EntityAnalysesRunType } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { conciseDateTime } from '@utils/date-utils'
import { Button } from '@/components/ui/button'
import React from 'react'
import Link from 'next/link'
import { TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

export function EntityAnalysisHistory({entityAnalyses}: { entityAnalyses: EntityAnalysesRunType[] }) {
    return <Card>
        <CardHeader>
            <CardTitle>Entity Analysis History</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="overflow-x-auto">
                <table className="w-full">
                    <TableHeader className="border-b">
                        <TableRow>
                            <TableHead className="px-4 py-2 text-left font-medium text-zinc-500">Name</TableHead>
                            <TableHead
                                className="hidden lg:table-cell px-4 py-2 text-left font-medium text-zinc-500">Type</TableHead>
                            <TableHead
                                className="hidden lg:table-cell px-4 py-2 text-left font-medium text-zinc-500">Date</TableHead>
                            <TableHead
                                className="hidden sm:table-cell px-4 py-2 text-right font-medium text-zinc-500">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody className="divide-y">
                        {entityAnalyses.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={4} className="text-center py-8">
                                    <div data-testid="no-company-analysis-message" className="text-gray-500">
                                        No company analyses found. Start by analyzing a company.
                                    </div>
                                </TableCell>
                            </TableRow>
                        ) : (
                            entityAnalyses.map((analysis) => (
                                <TableRow key={analysis.id} className="hover:bg-zinc-50" data-testid="company-analysis-entry">
                                    <TableCell className="px-4 py-2" data-testid="company-name">{analysis.name}</TableCell>
                                    <TableCell className="hidden lg:table-cell px-4 py-2">{analysis.type}</TableCell>
                                    <TableCell
                                        className="hidden lg:table-cell px-4 py-2" data-testid="analysis-date">{conciseDateTime(new Date(analysis.created_at!), Date.now(), navigator.language)}</TableCell>
                                    <TableCell className="hidden sm:table-cell px-4 py-2 text-right">
                                        <Link
                                            href={"/customer/dashboard?entity=" + analysis.entity_xid + "&run=" + analysis.run_id + "&model=sdg"}
                                            passHref>
                                            <Button variant="ghost" size="sm" data-testid="analysis-status">View Dashboard</Button>
                                        </Link>
                                    </TableCell>
                                </TableRow>
                            ))
                        )}
                    </TableBody>
                </table>
            </div>
        </CardContent>
    </Card>;
}
