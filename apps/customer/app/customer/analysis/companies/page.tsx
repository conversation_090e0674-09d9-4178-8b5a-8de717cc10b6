/**
 * Next.js App Router Entity Analysis Dashboard Page Component
 *
 * This React Client Component provides a comprehensive interface for managing ESG entity analysis within the
 * EkoIntelligence platform. Users can add new companies for analysis, trigger deep or quick analysis runs,
 * monitor analysis progress through a real-time queue system, and view usage quotas. The component integrates
 * with the platform's backend analytics engine to process corporate ESG documents and generate sustainability insights.
 *
 * ## Core Functionality
 * - **Company Management**: Add new companies to the platform for ESG analysis via Slack notifications
 * - **Analysis Triggering**: Initiate either deep analysis (12-48 hours) or quick analysis (5-120 minutes) for existing entities
 * - **Real-time Queue Monitoring**: Track analysis progress with live status updates and retry functionality for failed jobs
 * - **Quota Management**: Display usage statistics for entity quotas and analysis quotas with visual progress indicators
 * - **Analysis History**: View historical analysis runs with entity information and timing details
 * - **Feature Flag Integration**: Conditionally display analysis triggering interface based on user permissions
 *
 * ## Analysis Types
 * **Deep Analysis (Hours)**:
 * - Comprehensive web document discovery and analysis (12-48 hours)
 * - Searches for new corporate documents across the web
 * - Performs complete ESG statement extraction and analysis
 * - Suitable for initial company onboarding or comprehensive updates
 *
 * **Quick Analysis (Minutes)**:
 * - Limited to existing documents in the platform's database (5-120 minutes)
 * - Re-analyzes stored documents with updated models or algorithms
 * - Faster turnaround for incremental analysis updates
 * - Ideal for testing changes or getting rapid insights
 *
 * ## Database Integration
 * **Key Tables and Views**:
 * - `api_queue`: Analysis job queue with status tracking (pending, processing, completed, failed)
 * - `view_entity_analysis_runs`: Historical analysis runs joined with entity and run metadata
 * - `view_quota_used`: Real-time quota usage calculation for entities and analysis runs
 * - `xfer_entities`: Synced entity data from analytics database for customer access
 * - `cus_ana_hist_entity_runs`: Customer-facing analysis run history
 *
 * ## Real-time Features
 * **Queue Status Monitoring**:
 * - Live subscription to `api_queue` changes via Supabase real-time
 * - Automatic UI updates when analysis status changes (pending → processing → completed/failed)
 * - Toast notifications for status transitions and completion alerts
 * - Retry functionality for failed analysis jobs
 *
 * **Backend Integration**:
 * - `callBackofficeAsync()`: Triggers analysis jobs in Python analytics backend
 * - `backOfficeRequestListener()`: Real-time listener for job status updates
 * - `sendToSlack()`: Company addition requests routed through Slack for manual approval
 *
 * ## User Interface Features
 * - **Glass-morphism Design**: Consistent with EkoIntelligence design system using translucent cards
 * - **Responsive Layout**: Mobile-first design with adaptive grid layouts and table overflow handling
 * - **Visual Status Indicators**: Color-coded badges for analysis status (green=completed, amber=processing, red=failed)
 * - **Progress Visualization**: Quota usage bars with color coding (blue < 60%, yellow 60-80%, red > 80%)
 * - **Interactive Elements**: Click handlers for analysis triggering, queue retries, and entity selection
 * - **Feature Flag Controls**: Conditional display of analysis controls based on user permissions
 *
 * ## Security & Access Control
 * - **Row Level Security (RLS)**: All database queries filtered by authenticated user ID
 * - **Authentication Required**: Component requires valid Supabase session to function
 * - **Quota Enforcement**: Analysis requests subject to user/organization quota limits
 * - **Permission-based Features**: Analysis triggering controlled by feature flag system
 *
 * ## Component Architecture
 * **State Management**:
 * - `useState` hooks for local component state (quotas, queue, analyses, selected entity)
 * - `useEffect` for data fetching, real-time subscriptions, and cleanup
 * - `useRef` for form input reference without re-renders
 *
 * **Child Components**:
 * - `EntitySelector`: Dropdown for selecting entities from available companies
 * - `EntityQuotaCard` / `EntityAnalysisQuotaCard`: Visual quota usage displays
 * - `EntityAnalysisHistory`: Tabular view of historical analysis runs
 * - `Instructions`: User guidance components with helpful tips
 * - `FeatureFlag`: Conditional rendering wrapper for permission-controlled features
 *
 * ## Error Handling & User Feedback
 * - **Toast Notifications**: Success and error feedback for all user actions
 * - **Graceful Degradation**: Component handles missing data and network failures
 * - **Loading States**: Visual feedback during asynchronous operations
 * - **Retry Mechanisms**: Failed analysis jobs can be retried via UI button
 * - **Validation**: Input validation and user guidance for required fields
 *
 * ## Performance Considerations
 * - **Efficient Re-rendering**: Uses `useRef` for form inputs to avoid unnecessary renders
 * - **Selective Updates**: Real-time listeners only trigger relevant state updates
 * - **Cleanup Management**: Proper subscription cleanup to prevent memory leaks
 * - **Batched Database Operations**: Quota and queue queries optimized for performance
 *
 * @see https://nextjs.org/docs/app/building-your-application/rendering/client-components Next.js Client Components
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase Row Level Security
 * @see https://react.dev/learn/synchronizing-with-effects React useEffect Hook
 * @see {@link ../../../../../../backoffice/src/eko/analysis_v2} Backend Analysis Engine
 * @see {@link ../quotas.tsx} Quota Card Components
 * @see {@link ../entity-ana-history.tsx} Analysis History Component
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Entity analysis dashboard for managing ESG company analysis, monitoring progress, and viewing usage quotas
 * @example
 * ```typescript
 * // Usage in Next.js App Router
 * // Located at /customer/analysis/companies
 * // Requires authentication and appropriate permissions
 * export default function AnalysisPage() {
 *   return <AnalysisDashboard />;
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client"
import React, {useEffect, useRef, useState} from 'react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Building2Icon, FilePieChartIcon, RabbitIcon} from 'lucide-react';
import {sendToSlack} from "@/app/customer/slack";
import {useToast} from "@/hooks/use-toast";
import {EntitySelector} from "@/components/entity-selector";
import {useSearchParams} from "next/navigation";
import {useAuth} from "@/components/context/auth/auth-context";
import {APIQueueType, EntityAnalysesRunType, QuotaUsedType} from "@/types";
import {createClient} from "@/app/supabase/client";
import {FeatureFlag} from "@/components/feature-flag";
import {conciseDateTime} from "@utils/date-utils";
import {runAsync} from "@utils/react-utils";
import {backOfficeRequestListener, callBackofficeAsync} from "@/components/backoffice";
import {EntityAnalysisHistory} from "@/app/customer/analysis/entity-ana-history";
import {EntityAnalysisQuotaCard, EntityQuotaCard} from "@/app/customer/analysis/quotas";
import {Instructions} from "@/components/instructions";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {PageHeader} from "@/components/page-header";
import {Badge} from '@/components/ui/badge';
import {cn} from '@utils/lib/utils';

const AnalysisDashboard = () => {
    const [activeTab, setActiveTab] = useState('companies');
    const [company, setCompany] = useState<string | undefined>();
    const {toast} = useToast();
    const queryParams = useSearchParams();
    const supabase = createClient();
    const [apiQueue, setApiQueue] = useState<APIQueueType[]>([]);
    const [listeners, setListeners] = useState<any[]>([]);
    const [entityQuotaInfo, setEntityQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalysisQuotaInfo, setEntityAnalysisQuotaInfo] = useState<QuotaUsedType>();
    const auth = useAuth();
    const [entityAnalyses, setEntityAnalyses] = useState<EntityAnalysesRunType[]>([]);


    async function updateCompanies() {
        const {
            data: analyses,
            error: docAnalysesError
        } = await supabase.from('view_entity_analysis_runs').select("*").eq("run_by", auth?.user?.id!);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: 'destructive'});
        } else {
            setEntityAnalyses(analyses as EntityAnalysesRunType[]);
        }

    }


    async function addCompany() {
        let company = (ref.current as any).value;
        if (await sendToSlack("Please add new company " + company)) {
            toast({
                description: `Company ${company} added successfully, please now wait for the first analysis to complete, this can take several hours.`,
                variant: "default"
            })
        } else {
            toast({description: `Failed to add company ${company}`, variant: "destructive"})
        }


    }

    async function updateQueue(userId: string) {
        const {
            data: queue,
            error: queueError
        } = await supabase.from('api_queue').select().eq("requester", userId).eq("request_action", "analyse_entity").order("created_at", {ascending: false}).limit(10)
        setApiQueue(queue as APIQueueType[]);
    }

    useEffect(() => {
        if (auth) {

            listeners.forEach((listener) => listener.unsubscribe());
            setListeners([listenForQueueChange()]);

            runAsync(async () => {
                const userId = auth.user?.id;
                console.log(auth);
                if (!userId) return;
                await updateQueue(userId);
                const {
                    data: analysisQuota,
                    error: analysisQuotaError
                } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "entity-analysis").single();
                console.log(analysisQuota);
                if (analysisQuotaError) {
                    toast({description: analysisQuotaError.message, variant: 'destructive'});
                } else {
                    setEntityAnalysisQuotaInfo(analysisQuota as QuotaUsedType);
                }
                const {
                    data: companyQuota,
                    error: companyQuotaError
                } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "entity").single();
                if (companyQuotaError) {
                    toast({description: companyQuotaError.message, variant: 'destructive'});
                } else {
                    setEntityQuotaInfo(companyQuota as QuotaUsedType);
                }
                await updateCompanies();
            });
        }
        return () => {
            listeners.forEach((listener) => listener.unsubscribe());
        }
    }, [auth]);


    const ref = useRef(null)


    function listenForQueueChange() {
        return backOfficeRequestListener(supabase, auth.user?.id!, null, async (status, payload, error, message) => {
            updateQueue(auth.user?.id!);
            if (!payload && !error && message) {
                // Just a status update
                return;
            }
            if (error || !payload) {
                toast({description: error.message, variant: "destructive"});
            } else if (payload.status === 'completed') {
                toast({description: `Entity analysis completed successfully`});
            } else if (payload.status === 'processing') {
                toast({description: "Processing entity now, this may take from a few minutes to a few hours"});
            }
        });
    }

    async function analyseEntity(entity: string, quick: boolean) {
        const id = await callBackofficeAsync(supabase, "analyse_entity", {
            entity,
            quick
        });
        await updateQueue(auth.user?.id!);
    }

    async function clickHandler(quick: boolean) {

        if (company) {
            analyseEntity(company, quick);

        } else {
            toast({description: "Please select a company", variant: "destructive"});
            return;
        }


    }

    async function retryHandler(id: number) {
        await supabase.from('api_queue').update({status: 'pending'}).eq('id', id);
        await updateQueue(auth.user?.id!);
    }

    function colorForStatus(status: string) {
        if (status === 'completed') {
            return 'border-green-500';
        } else if (status === 'processing') {
            return 'border-amber-500';
        } else if (status === 'failed') {
            return 'border-red-500';
        } else {
            return 'border-zinc-500';
        }
    }

    if (!auth.user) return null;

    return (
        <>
            <PageHeader/>
            <div className="p-6 space-y-6 dashboard-container">
                {/* Usage Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    {entityAnalysisQuotaInfo &&
                        <EntityAnalysisQuotaCard entityAnalysisQuotaInfo={entityAnalysisQuotaInfo}/>}
                    {entityQuotaInfo && <EntityQuotaCard entityQuotaInfo={entityQuotaInfo}/>}


                </div>


                <Card>
                    <CardHeader>
                        <CardTitle>Add New Company</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-2">
                            <Input ref={ref} placeholder="Enter company name" className="max-w-sm"/>
                            <Button onClick={addCompany} variant="default">
                                <Building2Icon className="h-4 w-4 mr-2"/>
                                Add Company
                            </Button>
                        </div>
                        <Instructions>
                            Please enter the name of the company you wish to add to your company list, the initial analysis can take up
                            to 48 hours to complete due to the thorough nature of the first analysis.
                        </Instructions>
                    </CardContent>
                </Card>
                <FeatureFlag flag="analysis.user.trigger">
                    <Card>
                        <CardHeader>
                            <CardTitle>Start Company Analysis</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap space-x-2 gap-2">
                                <EntitySelector defaultEntity={undefined}
                                                onChange={(value) => setCompany(value)}/>
                                <Button onClick={() => clickHandler(false)} variant="default">
                                    <FilePieChartIcon className="h-4 w-4 mr-2"/>
                                    Deep Analysis (Hours)
                                </Button>
                                <Button onClick={() => clickHandler(true)} variant="outline">
                                    <RabbitIcon className="h-4 w-4 mr-2"/>
                                    Quick Analysis (Minutes)
                                </Button>
                            </div>
                            <Instructions>
                                A full analysis will take between 12-48 hours, depending on the size of the company, this
                                will
                                involve searching the web for new documents and analysing them. A quick analysis will take
                                between 5-120 minutes and will only analyse existing documents in our store.
                            </Instructions>
                        </CardContent>
                    </Card>
                </FeatureFlag>

                <Card>
                    <CardHeader>
                        <CardTitle>Analysis Queue</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table className="w-full">
                                <TableHeader className="border-b">
                                    <TableRow>
                                    <TableHead
                                        className="px-4 py-2 text-left font-medium text-zinc-500">Entity</TableHead>
                                        <TableHead
                                            className="px-4 py-2 text-left font-medium text-zinc-500">Type</TableHead>
                                    <TableHead
                                        className="px-4 py-2 text-left font-medium text-zinc-500">Status</TableHead>
                                    <TableHead
                                        className="hidden md:table-cell px-4 py-2 text-left font-medium text-zinc-500">Message</TableHead>
                                    <TableHead
                                        className="hidden lg:table-cell px-4 py-2 text-left font-medium text-zinc-500">Started</TableHead>
                                    <TableHead
                                        className="hidden lg:table-cell px-4 py-2 text-left font-medium text-zinc-500">Updated</TableHead>
                                    <TableHead
                                        className="hidden sm:table-cell px-4 py-2 text-left font-medium text-zinc-500">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody className="divide-y">
                                    {apiQueue.map((analysis) => (
                                            <TableRow key={analysis.id} className="hover:bg-zinc-50">
                                                <TableCell
                                                    className="px-4 py-2">{(analysis.request_data as any)?.entity || 'Unknown'}</TableCell>
                                                <TableCell
                                                    className="px-4 py-2">{(analysis.request_data as any)?.quick ? 'Quick' : 'Deep'}</TableCell>
                                                <TableCell className="px-4 py-2"><Badge variant="outline" className={cn("border-opacity-80 font-light",colorForStatus(analysis.status!))}>{analysis.status}</Badge></TableCell>
                                                <TableCell className="hidden md:table-cell px-4 py-2">{analysis.message}</TableCell>
                                                <TableCell
                                                    className="hidden lg:table-cell px-4 py-2">{conciseDateTime(new Date(analysis.created_at!), Date.now(), navigator.language)}</TableCell>
                                                <TableCell
                                                    className="hidden lg:table-cell px-4 py-2">{conciseDateTime(new Date(analysis.updated_at!), Date.now(), navigator.language)}</TableCell>
                                                <TableCell className="hidden sm:table-cell px-4 py-2 text-right">
                                                    {analysis.status === 'failed' &&
                                                        (<Button onClick={() => retryHandler(analysis.id!)}
                                                                 variant="outline" size="sm">Retry</Button>)
                                                    }
                                                </TableCell>

                                            </TableRow>
                                        )
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
                <EntityAnalysisHistory entityAnalyses={entityAnalyses}/>

            </div>
        </>
    );


};

export default AnalysisDashboard;
