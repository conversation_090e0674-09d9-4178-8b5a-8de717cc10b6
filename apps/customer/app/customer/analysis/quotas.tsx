/**
 * ESG Quota Management Card Components for Customer Dashboard
 *
 * This module provides React components for displaying quota usage information within the EkoIntelligence
 * ESG analysis platform. It offers visual quota cards with progress indicators that help users monitor
 * their consumption of various analysis services including entity analysis, document analysis, and company
 * tracking. Each component renders a glass-morphism styled card with usage visualization, progress bars,
 * and color-coded status indicators to provide immediate insight into quota utilization and remaining capacity.
 *
 * ## Core Components
 * - **EntityQuotaCard**: Displays company/entity quota usage with building icon and blue color scheme
 * - **DocQuotaCard**: Shows document analysis quota consumption with file icon and green color scheme  
 * - **EntityAnalysisQuotaCard**: Presents entity analysis quota usage with building icon and blue styling
 *
 * ## Visual Design Features
 * - **Glass-morphism UI**: Consistent with platform's translucent, modern design aesthetic using shadcn/ui Card components
 * - **Progress Visualization**: Color-coded progress bars with automatic status indication (green/yellow/red based on usage percentage)
 * - **Lucide Icons**: Professional iconography using Building2 and FileText icons for visual context
 * - **Responsive Layout**: Mobile-optimized card layout with proper spacing and typography hierarchy
 * - **Test Integration**: Comprehensive `data-testid` attributes for reliable automated testing coverage
 *
 * ## Database Integration
 * **Data Source**: `view_quota_used` (Customer Database View)
 * The components consume quota data from a PostgreSQL view that aggregates usage across multiple analysis types:
 * - **Entity Quotas**: Track company/organization limits from `cus_ana_companies` table
 * - **Document Analysis**: Monitor single document analysis usage from `cus_ana_hist_gw_single_doc_runs`
 * - **Entity Analysis**: Track comprehensive entity analysis from `cus_ana_hist_entity_runs`
 *
 * **QuotaUsedType Structure** (from `view_quota_used`):
 * - `type` (quota_item_type): Analysis type identifier ('entity', 'document-analysis', 'entity-analysis')
 * - `used` (bigint): Current usage count for the quota period
 * - `quota` (number): Maximum allowed usage for the quota period
 * - `period` (string): Quota reset period (e.g., 'monthly', 'annual')
 * - `scope` (quota_scope): Quota application scope ('user', 'organization')
 * - `profile_id` (uuid): Associated user profile for quota tracking
 *
 * ## Usage Patterns
 * **Color-Coded Status Logic**:
 * - **Green/Blue**: Usage ≤ 60% (healthy usage)
 * - **Yellow**: Usage 61-80% (approaching limit warning)
 * - **Red**: Usage > 80% (critical usage requiring attention)
 *
 * ## System Architecture
 * This component fits into the broader ESG analysis system:
 * - **Analytics Backend**: Python system processes ESG data and tracks usage in analytics database
 * - **Data Sync Layer**: `xfer_*` tables synchronize usage data between analytics and customer databases  
 * - **Quota Management**: Customer database maintains quota limits and tracks current usage
 * - **Customer Dashboard**: These components provide real-time quota visibility to end users
 * - **Usage Enforcement**: Backend services check quota limits before processing new analysis requests
 *
 * ## Integration Points
 * - **Dashboard Pages**: Used in `/customer/analysis/usage`, `/customer/analysis/companies`, `/customer/analysis/documents`
 * - **Account Management**: Quota information displayed in user account pages for subscription awareness
 * - **Admin Interface**: Administrative tools for quota adjustment and usage monitoring
 * - **Billing System**: Quota usage data feeds into subscription and billing calculations
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing Next.js App Router Documentation
 * @see https://ui.shadcn.com/docs/components/card shadcn/ui Card Component Documentation
 * @see https://lucide.dev/guide/packages/lucide-react Lucide React Icons Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * <AUTHOR>
 * @updated 2025-07-23
 * @description ESG quota management card components providing visual quota usage indicators with progress bars and status colors for entity, document, and analysis quotas
 * @example ```tsx
 * // Entity quota usage display
 * <EntityQuotaCard entityQuotaInfo={{used: 15, quota: 25, type: 'entity', period: 'monthly', scope: 'user', profile_id: 'uuid'}} />
 * 
 * // Document analysis quota display
 * <DocQuotaCard quotaInfo={{used: 8, quota: 10, type: 'document-analysis', period: 'monthly', scope: 'user', profile_id: 'uuid'}} />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import {QuotaUsedType} from "@/types";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Building2, FileText} from "lucide-react";
import React from "react";

export function EntityQuotaCard({entityQuotaInfo}: { entityQuotaInfo: QuotaUsedType }) {
    return <Card data-testid="entity-quota">
        <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" data-testid="quota-title">Companies</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4 text-blue-500"/>
                    <span className="text-2xl font-bold" data-testid="quota-used">{entityQuotaInfo.used ?? 0}</span>
                </div>
                <span className="text-sm text-zinc-500">
                of <span data-testid="quota-limit">{entityQuotaInfo.quota}</span> companies
              </span>
            </div>
            <div className="mt-2 h-2 bg-zinc-100 rounded-full">
                <div
                    className={`h-2 rounded-full ${
                        ((entityQuotaInfo.used ?? 0) / (entityQuotaInfo.quota ?? 1)) * 100 > 80 
                            ? 'bg-red-500' 
                            : ((entityQuotaInfo.used ?? 0) / (entityQuotaInfo.quota ?? 1)) * 100 > 60 
                                ? 'bg-yellow-500' 
                                : 'bg-blue-500'
                    }`}
                    data-testid="quota-progress"
                    data-progress={((entityQuotaInfo.used ?? 0) / (entityQuotaInfo.quota ?? 1)) * 100.0}
                    style={{width: `${((entityQuotaInfo.used ?? 0) / (entityQuotaInfo.quota ?? 1)) * 100.0}%`}}
                />
            </div>
        </CardContent>
    </Card>;
}

export function DocQuotaCard({quotaInfo}: { quotaInfo: QuotaUsedType }) {
    return <Card data-testid="document-analysis-quota">
        <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" data-testid="quota-title">Document Analysis Usage</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-green-500"/>
                    <span className="text-2xl font-bold" data-testid="quota-used">{quotaInfo.used}</span>
                </div>
                <span className="text-sm text-zinc-500">
                of <span data-testid="quota-limit">{quotaInfo.quota}</span> analyses
              </span>
            </div>
            <div className="mt-2 h-2 bg-zinc-100 rounded-full">
                <div
                    className={`h-2 rounded-full ${
                        ((quotaInfo.used ?? 0) / (quotaInfo.quota ?? 1)) * 100 > 80 
                            ? 'bg-red-500' 
                            : ((quotaInfo.used ?? 0) / (quotaInfo.quota ?? 1)) * 100 > 60 
                                ? 'bg-yellow-500' 
                                : 'bg-green-500'
                    }`}
                    data-testid="quota-progress"
                    data-progress={((quotaInfo.used ?? 0) / (quotaInfo.quota ?? 1)) * 100}
                    style={{width: `${((quotaInfo.used ?? 0) / (quotaInfo.quota ?? 1)) * 100}%`}}
                />
            </div>
        </CardContent>
    </Card>;
}

export function EntityAnalysisQuotaCard({entityAnalysisQuotaInfo}: { entityAnalysisQuotaInfo: QuotaUsedType }) {
    return <Card data-testid="entity-analysis-quota">
        <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" data-testid="quota-title">Company Analysis Usage</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4 text-blue-500"/>
                    <span className="text-2xl font-bold" data-testid="quota-used">{entityAnalysisQuotaInfo.used || 0}</span>
                </div>
                <span className="text-sm text-zinc-500">
                of <span data-testid="quota-limit">{entityAnalysisQuotaInfo.quota}</span> analyses
              </span>
            </div>
            <div className="mt-2 h-2 bg-zinc-100 rounded-full">
                <div
                    className={`h-2 rounded-full ${
                        ((entityAnalysisQuotaInfo.used || 0) / (entityAnalysisQuotaInfo.quota ?? 1)) * 100 > 80 
                            ? 'bg-red-500' 
                            : ((entityAnalysisQuotaInfo.used || 0) / (entityAnalysisQuotaInfo.quota ?? 1)) * 100 > 60 
                                ? 'bg-yellow-500' 
                                : 'bg-blue-500'
                    }`}
                    data-testid="quota-progress"
                    data-progress={((entityAnalysisQuotaInfo.used || 0) / (entityAnalysisQuotaInfo.quota ?? 1)) * 100}
                    style={{width: `${((entityAnalysisQuotaInfo.used || 0) / (entityAnalysisQuotaInfo.quota ?? 1)) * 100}%`}}
                />
            </div>
        </CardContent>
    </Card>;
}
