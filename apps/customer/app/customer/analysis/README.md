# ESG Analysis Management Module

The analysis module serves as the central command center for ESG (Environmental, Social, Governance) analysis operations within the EkoIntelligence platform. This module provides comprehensive interfaces for managing entity analysis workflows, document processing pipelines, and usage monitoring across the platform's sophisticated dual-database architecture.

## Overview

This module implements the customer-facing interfaces for EkoIntelligence's ESG analysis platform, enabling users to initiate, monitor, and review comprehensive corporate sustainability analyses. The module integrates three primary analysis workflows: entity (company) analysis for comprehensive ESG evaluation, document analysis for individual sustainability reports, and usage monitoring for quota and resource management.

The analysis module bridges the gap between the platform's powerful Python-based analytics backend and the customer-facing React/TypeScript frontend, providing real-time analysis status monitoring, quota management, and historical analysis browsing through a modern glass-morphism interface built with Next.js 15 and Supabase.

## Specification

### Core Analysis Workflows

**Entity Analysis Pipeline**:
- Company addition via Slack integration for manual approval workflow
- Deep analysis (12-48 hours): Comprehensive web document discovery and complete ESG analysis
- Quick analysis (5-120 minutes): Re-analysis of existing documents with updated models
- Real-time queue monitoring with retry functionality for failed analyses
- Historical analysis browsing with navigation to detailed entity dashboards

**Document Analysis Pipeline**:
- File upload interface supporting PDF, DOC, DOCX and other document formats
- URL-based document processing for web-based sustainability reports
- Entity association requirement for contextual analysis and proper data categorization
- Integration with backend Python analytics engine for ESG statement extraction
- Results integration into comprehensive entity knowledge graphs

**Usage Monitoring System**:
- Real-time quota tracking across entity, document analysis, and company quotas
- Visual progress indicators with color-coded status (green < 60%, yellow 60-80%, red > 80%)
- Historical analysis browsing with direct navigation to detailed results
- Tabbed interface organization for different analysis types and workflows

### Database Integration

The module operates across EkoIntelligence's sophisticated dual-database architecture:

**Customer Database Views**:
- `view_entity_analysis_runs`: Historical entity analysis metadata with user associations
- `view_single_doc_runs`: Document analysis history with entity context and completion status
- `view_quota_used`: Real-time quota consumption calculations across analysis types
- `api_queue`: Backend processing queue for analysis status monitoring and retry management

**Analytics Database Sync**:
- `xfer_entities`: Synchronized entity data from analytics database for customer access
- `xfer_gw_single_doc`: Document analysis results synced for real-time customer display
- `cus_ana_hist_entity_runs`: Customer-facing analysis run history with quota tracking

## Key Components

### Core Page Components

#### `/companies/page.tsx` - Entity Analysis Dashboard
- **Primary Interface**: Comprehensive entity analysis management with company addition and analysis triggering
- **Real-time Features**: Live analysis queue monitoring via Supabase real-time subscriptions
- **Analysis Types**: Deep analysis (12-48 hours) and quick analysis (5-120 minutes) with feature flag controls
- **Integration**: Slack notifications for company additions, backend API communication for analysis triggers
- **UI Features**: Glass-morphism cards, quota visualization, responsive mobile-first design

#### `/documents/page.tsx` - Document Analysis Dashboard  
- **Upload Interface**: File upload and URL processing with entity association requirements
- **Analysis History**: Comprehensive document analysis history with navigation to detailed greenwashing reports
- **Quota Integration**: Document analysis quota monitoring with visual progress indicators
- **Backend Integration**: Direct integration with Python analytics engine for ESG document processing

#### `/usage/page.tsx` - Usage Monitoring Dashboard
- **Quota Overview**: Comprehensive quota usage monitoring across all analysis types
- **Historical Browsing**: Complete analysis history with direct navigation to detailed results
- **Tabbed Organization**: Clean separation of entity analysis and document analysis workflows
- **Visual Analytics**: Color-coded progress bars and usage visualization for immediate insight

### Shared Components

#### `entity-ana-history.tsx` - Entity Analysis History Display
- **Data Integration**: Consumes `view_entity_analysis_runs` for comprehensive historical analysis data
- **Navigation**: Direct links to detailed entity dashboards with entity context preservation
- **Responsive Design**: Mobile-optimized table with progressive column disclosure
- **Date Formatting**: Intelligent temporal display with locale-aware formatting

#### `doc-ana-history.tsx` - Document Analysis History Display
- **Report Navigation**: Direct navigation to detailed greenwashing analysis reports
- **Entity Context**: Maintains entity associations through URL parameters for seamless navigation
- **Table Design**: Horizontal scrolling optimization for mobile compatibility
- **Empty State Handling**: User-friendly messaging with actionable guidance

#### `quotas.tsx` - Quota Management Cards
- **Visual Indicators**: Color-coded progress bars with automatic status indication
- **Multiple Quota Types**: Entity quotas, document analysis quotas, and company tracking quotas
- **Real-time Updates**: Automatic refresh when quota consumption changes
- **Glass-morphism Design**: Consistent platform styling with translucent card interfaces

## Dependencies

### External Services
- **Supabase**: Primary database connectivity, authentication, and real-time features
- **Next.js 15**: Modern React framework with App Router for server-side rendering
- **shadcn/ui**: Component library providing consistent design system and accessibility
- **Lucide React**: Professional iconography for visual context and user guidance

### Internal Dependencies
- **Backend Analytics Engine**: Python-based ESG analysis pipeline (`/backoffice/src/eko/analysis_v2/`)
- **Authentication System**: Supabase Auth with Row Level Security for multi-tenant access control
- **Entity Management**: Entity selection and management components (`/components/entity-selector`)
- **Feature Flag System**: Permission-based feature access control (`/components/feature-flag`)
- **Quota Management**: Real-time quota tracking and enforcement across analysis types

### Database Dependencies
- **Customer Database**: `view_entity_analysis_runs`, `view_single_doc_runs`, `view_quota_used`, `api_queue`
- **Analytics Database**: Analysis processing and results generation (synced via `xfer_` tables)
- **Authentication**: User session management and Row Level Security policy enforcement

## Usage Examples

### Entity Analysis Workflow
```typescript
// Navigate to entity analysis dashboard
// Located at: /customer/analysis/companies

// Add new company (requires manual approval)
// 1. Enter company name in input field
// 2. Click "Add Company" - triggers Slack notification
// 3. Wait for manual approval and initial analysis completion

// Trigger analysis for existing entity
// 1. Select entity from dropdown
// 2. Choose analysis type:
//    - Deep Analysis: 12-48 hours, complete web document discovery
//    - Quick Analysis: 5-120 minutes, existing document re-analysis
// 3. Monitor progress in real-time queue with retry capability
```

### Document Analysis Workflow
```typescript
// Navigate to document analysis dashboard
// Located at: /customer/analysis/documents

// Upload document for analysis
// 1. Select entity association (required)
// 2. Choose upload method:
//    - File upload: PDF, DOC, DOCX support
//    - URL processing: Web-based document analysis
// 3. Monitor analysis progress and view results
// 4. Navigate to detailed greenwashing analysis reports
```

### Usage Monitoring
```typescript
// Navigate to usage dashboard
// Located at: /customer/analysis/usage

// Monitor quota consumption
// - Entity Analysis Quota: Current usage vs. allocated limits
// - Company Quota: Available entity slots for new additions
// - Visual progress indicators with color-coded status

// Browse analysis history
// - Complete entity analysis history with direct dashboard navigation
// - Document analysis history with detailed report access
// - Tabbed organization for different analysis types
```

## Architecture Notes

### Real-time Analysis Monitoring

```mermaid
sequenceDiagram
    participant UI as Customer UI
    participant SB as Supabase
    participant BE as Python Backend
    participant AQ as API Queue

    UI->>SB: Subscribe to api_queue changes
    UI->>BE: Trigger analysis request
    BE->>AQ: Insert pending job
    AQ->>SB: Real-time notification
    SB->>UI: Status update (pending)
    
    BE->>BE: Process analysis
    BE->>AQ: Update status (processing)
    AQ->>SB: Real-time notification
    SB->>UI: Status update (processing)
    
    BE->>BE: Complete analysis
    BE->>AQ: Update status (completed)
    AQ->>SB: Real-time notification
    SB->>UI: Status update (completed)
    UI->>UI: Display completion toast
```

### Dual Database Architecture

```mermaid
graph TB
    subgraph "Analytics Database"
        PY[Python Analytics Engine]
        AD[(Analytics DB)]
        PY --> AD
    end
    
    subgraph "Sync Layer"
        SYNC[Data Synchronization]
        AD --> SYNC
    end
    
    subgraph "Customer Database"
        CD[(Customer DB)]
        SYNC --> CD
        CD --> VIEWS[Database Views]
    end
    
    subgraph "Customer Frontend"
        UI[React Components]
        VIEWS --> UI
        UI --> RLS[Row Level Security]
    end
    
    UI --> AUTH[Authentication]
    AUTH --> RLS
```

### Component State Flow

```mermaid
stateDiagram-v2
    [*] --> Loading
    Loading --> DataLoaded: Fetch quota & history
    DataLoaded --> Monitoring: Subscribe to real-time
    
    Monitoring --> AnalysisTriggered: User action
    AnalysisTriggered --> QueueUpdate: Backend processing
    QueueUpdate --> Monitoring: Status notification
    
    Monitoring --> NavigateToResults: View analysis
    NavigateToResults --> EntityDashboard: Entity analysis
    NavigateToResults --> DocumentReport: Document analysis
    
    DataLoaded --> QuotaExceeded: Quota limit reached
    QuotaExceeded --> ErrorState: Display warning
    ErrorState --> DataLoaded: Quota refreshed
```

## Known Issues

Based on Linear ticket analysis and code review:

### High Priority Issues (EKO-264: Review Analysis Data)
- **Analysis Data Rendering**: Ongoing improvements to analysis content display and formatting
- **Chart Integration**: Enhanced validation of chart data and improved error handling for missing charts
- **Report Generation**: Improvements to report title generation and dynamic content integration

### Navigation Issues (Resolved: EKO-291)
- **Claims Page Navigation**: Sidebar navigation issues were resolved for greenwashing claims pages
- **Context Preservation**: Entity context now properly preserved when switching between dashboard sections

### Performance Considerations
- **Real-time Subscription Management**: Proper cleanup of Supabase subscriptions to prevent memory leaks
- **Large Dataset Handling**: Analysis history tables may require pagination for users with extensive analysis history
- **Mobile Optimization**: Complex tables require horizontal scrolling optimization for mobile users

## Future Work

### Near-term Improvements (Q1 2025)
Based on current Linear tickets and platform roadmap:

- **Enhanced Unit Testing** (EKO-255): Transition from integration tests to unit/component tests for faster development cycles
- **Real-time Analysis Updates**: Enhanced real-time monitoring with more granular progress indicators
- **Batch Analysis Operations**: Support for analyzing multiple entities or documents simultaneously
- **Advanced Filtering**: Enhanced search and filtering capabilities for analysis history browsing

### Long-term Platform Evolution
- **Analysis Scheduling**: Automated recurring analysis for monitored entities with configurable intervals
- **Custom Analysis Models**: User-configurable analysis parameters and model selection
- **API Integration**: REST API endpoints for programmatic analysis triggering and result retrieval
- **Advanced Visualizations**: Enhanced chart integration with interactive data exploration

### Integration Enhancements
- **Webhook Support**: Real-time notifications for analysis completion via webhooks
- **Export Capabilities**: CSV/Excel export for analysis history and quota usage data
- **Admin Dashboard Integration**: Enhanced administrative tools for quota management and user oversight

## Troubleshooting

### Common Issues

**Analysis Not Starting**:
- Verify quota availability in usage dashboard
- Check feature flag permissions for analysis triggering
- Ensure entity selection is properly configured
- Review API queue for failed/stuck jobs with retry functionality

**Real-time Updates Not Working**:
- Check browser console for WebSocket connection errors
- Verify Supabase real-time subscription status
- Ensure proper cleanup of subscription listeners on component unmount
- Check Row Level Security policies for database access

**Navigation Issues**:
- Verify entity context preservation in URL parameters
- Check for proper Next.js Link component usage with client-side routing
- Ensure sidebar navigation components are properly configured

**Performance Issues**:
- Monitor real-time subscription count and cleanup
- Check for efficient database query usage with proper indexing
- Verify proper loading states and error boundaries
- Review component re-render patterns and optimization

### Debug Information

**Real-time Monitoring**:
```typescript
// Check Supabase real-time connection status
console.log('Supabase real-time status:', supabase.realtime.getChannels());

// Monitor API queue subscription
const subscription = supabase
  .channel('api_queue_changes')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'api_queue'
  }, (payload) => console.log('Queue update:', payload));
```

**Database Query Debugging**:
```typescript
// Enable detailed database query logging
const { data, error, status, statusText } = await supabase
  .from('view_entity_analysis_runs')
  .select('*')
  .eq('run_by', userId);

console.log('Query result:', { data, error, status, statusText });
```

## FAQ

### User-Centric Questions

**Q: How long does entity analysis take?**
A: Entity analysis duration depends on the type selected:
- **Deep Analysis**: 12-48 hours for comprehensive web document discovery and complete ESG analysis
- **Quick Analysis**: 5-120 minutes for re-analysis of existing documents with updated models

**Q: What file formats are supported for document analysis?**
A: The document analysis system supports PDF, DOC, DOCX, and other common document formats, as well as URL-based processing for web documents.

**Q: Why do I need to associate documents with entities?**
A: Entity association is required for contextual analysis and proper integration into the platform's knowledge graphs. This ensures accurate ESG scoring and enables cross-document analysis within entity context.

**Q: How are analysis quotas managed?**
A: Quotas are managed in three categories:
- **Entity Quota**: Number of companies you can add to the platform
- **Entity Analysis Quota**: Number of comprehensive entity analyses you can perform
- **Document Analysis Quota**: Number of individual documents you can analyze

**Q: Can I retry failed analyses?**
A: Yes, failed analyses can be retried using the "Retry" button in the analysis queue. The system will re-attempt the analysis with the same parameters.

**Q: How do I access detailed analysis results?**
A: Navigate to analysis results through:
- Entity analyses: Click "View Dashboard" to access comprehensive entity ESG dashboards
- Document analyses: Click "View Report" to access detailed greenwashing analysis reports

**Q: What happens when I reach my quota limit?**
A: When quota limits are reached, the system will prevent new analysis requests and display warning notifications. Contact your administrator to adjust quota allocations if needed.

## References

### Documentation Links
- [Next.js 15 App Router Documentation](https://nextjs.org/docs/app/building-your-application/routing)
- [Supabase JavaScript Client Documentation](https://supabase.com/docs/reference/javascript)
- [Supabase Row Level Security Guide](https://supabase.com/docs/guides/database/postgres/row-level-security)
- [shadcn/ui Component Documentation](https://ui.shadcn.com/docs/components)
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react)

### Related Code Files
- [`/apps/customer/types.ts`](../../../types.ts) - Core TypeScript type definitions
- [`/apps/customer/components/entity-selector.tsx`](../../../components/entity-selector.tsx) - Entity selection component
- [`/apps/customer/components/feature-flag.tsx`](../../../components/feature-flag.tsx) - Feature flag system
- [`/apps/customer/components/backoffice.tsx`](../../../components/backoffice.tsx) - Backend communication utilities
- [`/backoffice/src/eko/analysis_v2/`](../../../../backoffice/src/eko/analysis_v2/) - Backend analytics engine

### Third-Party Dependencies
- [React 18 Documentation](https://react.dev/) - Core React library for component architecture
- [TypeScript Handbook](https://www.typescriptlang.org/docs/) - Type system and language features
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - Utility-first CSS framework
- [PostgreSQL Documentation](https://www.postgresql.org/docs/) - Database system and advanced features

### Platform Documentation
- [`/apps/customer/README.md`](../../README.md) - Customer application overview and setup
- [`/apps/customer/CLAUDE.md`](../../CLAUDE.md) - Development guidelines and coding standards
- [`/CLAUDE.md`](../../../../CLAUDE.md) - Project-wide development guidelines and architecture

### Linear Issues
- [EKO-264: Review Analysis Data](https://linear.app/ekointelligence/issue/EKO-264/review-analysis-data) - Analysis rendering improvements
- [EKO-291: Claims Page Navigation](https://linear.app/ekointelligence/issue/EKO-291/claims-page) - Navigation fixes
- [EKO-255: Unit Testing Enhancement](https://linear.app/ekointelligence/issue/EKO-255/create-more-unit-tests-or-component-tests) - Testing strategy improvements

## Changelog

### 2025-07-31
- **Initial Documentation**: Created comprehensive README.md for analysis module
- **Component Analysis**: Documented all major components with detailed functionality descriptions
- **Architecture Documentation**: Added Mermaid diagrams for real-time monitoring, database architecture, and state flow
- **Usage Examples**: Provided detailed workflow examples for entity analysis, document analysis, and usage monitoring
- **Troubleshooting Guide**: Added common issues, debug information, and resolution steps
- **FAQ Section**: Comprehensive user-centric questions and answers for common usage scenarios

---

(c) All rights reserved ekoIntelligence 2025