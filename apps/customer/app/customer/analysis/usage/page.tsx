/**
 * Next.js App Router Analysis Usage Dashboard Page Component
 *
 * This React Client Component provides a centralized usage dashboard for monitoring ESG analysis consumption
 * within the EkoIntelligence platform. Users can view their quota usage across different analysis types (entity
 * analyses and company quotas) and access their complete analysis history. The component serves as the primary
 * interface for understanding platform usage patterns and accessing historical analysis results.
 *
 * ## Core Functionality
 * - **Quota Usage Monitoring**: Real-time display of usage statistics for entity analysis and company quotas
 * - **Visual Progress Tracking**: Color-coded progress bars showing quota consumption (green < 60%, yellow 60-80%, red > 80%)
 * - **Analysis History Browser**: Comprehensive view of all entity analysis runs with navigation to detailed results
 * - **Tabbed Interface**: Organized presentation of different analysis types (currently focused on entity analysis)
 * - **Responsive Design**: Mobile-first layout with adaptive grid systems for optimal viewing on all devices
 *
 * ## Usage Dashboard Features
 * **Quota Cards Display**:
 * - **Entity Analysis Quota**: Shows consumed vs. allocated entity analysis quota with visual progress bar
 * - **Company Quota**: Displays available company slots for adding new entities to the platform
 * - **Document Analysis Quota**: Currently hidden but available for future activation
 * - **Dynamic Color Coding**: Progress indicators change color based on usage level for instant visual feedback
 *
 * **Historical Analysis Tracking**:
 * - **Entity Analysis History**: Complete list of entity analysis runs with creation dates and entity information
 * - **Analysis Navigation**: Direct links to detailed dashboard views for each completed analysis
 * - **Run Metadata**: Displays entity names, analysis types, run IDs, and creation timestamps
 * - **Empty State Handling**: User-friendly messaging when no analyses have been completed
 *
 * ## Database Integration
 * **Key Tables and Views**:
 * - `view_entity_analysis_runs`: Historical entity analysis runs with complete metadata
 *   - Contains: run ID, entity information (name, type, XID), creation timestamps, user associations
 *   - Filtered by authenticated user ID for secure data access
 * - `view_quota_used`: Real-time quota consumption calculations
 *   - Tracks: used vs. allocated quotas, quota types (entity-analysis, entity), scope and period information
 *   - Updates automatically when new analyses are completed or entities are added
 * - `xfer_entities`: Synced entity data from analytics database
 * - `cus_ana_hist_entity_runs`: Customer-facing analysis run history
 *
 * **Data Fetching Strategy**:
 * - **Parallel Loading**: Simultaneous fetching of quota data and analysis history for optimal performance
 * - **Error Handling**: Comprehensive error management with user-friendly toast notifications
 * - **User-Scoped Queries**: All data automatically filtered by authenticated user ID through Row Level Security
 * - **Real-time Updates**: Component refreshes when user authentication state changes
 *
 * ## User Interface Architecture
 * - **Glass-morphism Design**: Consistent with EkoIntelligence design system using translucent cards and rounded elements
 * - **Responsive Grid Layout**: Adaptive 3-column grid on large screens, single column on mobile devices
 * - **Tabbed Content Organization**: Clean separation of different analysis types with future extensibility
 * - **Progress Visualization**: Visual quota usage indicators with color-coded status representation
 * - **Interactive Elements**: Clickable analysis entries that navigate to detailed dashboard views
 *
 * ## Navigation Integration
 * **Analysis Dashboard Links**:
 * - **Entity Analysis Results**: Navigation to `/customer/dashboard?entity={xid}&run={id}&model=sdg`
 * - **Deep-linked Analysis Views**: Direct access to specific analysis runs with pre-selected entity and model
 * - **SDG Model Integration**: Default model selection for Sustainable Development Goals analysis framework
 * - **Breadcrumb Context**: Maintains navigation context for seamless user experience
 *
 * ## Security & Access Control
 * - **Row Level Security (RLS)**: All database queries automatically filtered by authenticated user ID
 * - **Quota Enforcement**: Real-time quota checking ensures users cannot exceed allocated limits
 * - **User Authentication**: Integration with AuthProvider for secure session management
 * - **Permission-based Features**: Component adapts based on user permissions and admin status
 *
 * ## Performance Optimizations
 * - **Efficient Queries**: Targeted database queries with proper indexing on user ID and quota types
 * - **Conditional Rendering**: Smart component rendering based on data availability
 * - **Error Boundaries**: Graceful degradation when individual quota cards or history sections fail
 * - **Loading States**: Proper handling of async data loading with user feedback
 *
 * ## Integration Points
 * **Component Dependencies**:
 * - **EntityAnalysisHistory**: Displays tabular view of historical analysis runs
 * - **EntityAnalysisQuotaCard**: Visual quota usage display for analysis limits
 * - **EntityQuotaCard**: Company quota tracking for entity additions
 * - **PageHeader**: Consistent page header with navigation and user context
 * - **Supabase Client**: Real-time database connectivity with automatic authentication
 *
 * **Related Features**:
 * - **Entity Analysis Dashboard**: Target destination for analysis result navigation
 * - **Company Management**: Integration with entity addition and analysis triggering workflows
 * - **Quota Management**: Real-time tracking of usage across different analysis types
 * - **Authentication System**: Secure user session management and access control
 *
 * @see {@link https://react.dev/reference/react/useEffect} React useEffect Hook for lifecycle management
 * @see {@link https://react.dev/reference/react/useState} React useState Hook for state management  
 * @see {@link https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts} Next.js App Router for routing
 * @see {@link https://supabase.com/docs/reference/javascript/select} Supabase JavaScript Client for database operations
 * @see {@link https://ui.shadcn.com/docs/components/tabs} Shadcn UI Tabs for component structure
 *
 * <AUTHOR> AI Assistant
 * @updated 2025-01-23
 * @description Analysis usage dashboard with quota monitoring and history browsing for ESG analysis platform
 * @example
 * ```tsx
 * // Usage as Next.js App Router page component
 * export default function UsagePage() {
 *   return <AnalysisDashboard />
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright 2025 EkoIntelligence - ESG Analysis Platform. All rights reserved.
 */
'use client'
import React, { useEffect, useState } from 'react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { EntityAnalysisHistory } from '@/app/customer/analysis/entity-ana-history'
import { DocumentAnalysisHistory } from '@/app/customer/analysis/doc-ana-history'
import { EntityAnalysesRunType, QuotaUsedType, SingleDocAnalysesType } from '@/types'
import { runAsync } from '@utils/react-utils'
import { useAuth } from '@/components/context/auth/auth-context'
import { createClient } from '@/app/supabase/client'
import { useToast } from '@/hooks/use-toast'
import { DocQuotaCard, EntityAnalysisQuotaCard, EntityQuotaCard } from '@/app/customer/analysis/quotas'
import { PageHeader } from '@/components/page-header'

const AnalysisDashboard = () => {
    const [activeTab, setActiveTab] = useState('companies');
    const {user, admin} = useAuth();
    const {toast} = useToast();
    const [docAnalyses, setDocAnalyses] = useState<SingleDocAnalysesType[]>([]);
    const supabase = createClient();
    const [quotaInfo, setQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalyses, setEntityAnalyses] = useState<EntityAnalysesRunType[]>([]);
    const [entityQuotaInfo, setEntityQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalysisQuotaInfo, setEntityAnalysisQuotaInfo] = useState<QuotaUsedType>();

    async function updateCompanies() {
        const {
            data: analyses,
            error: docAnalysesError
        } = await supabase.from('view_entity_analysis_runs').select("*").eq("run_by", user?.id!);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: 'destructive'});
        } else {
            setEntityAnalyses(analyses as EntityAnalysesRunType[]);
        }

        const userId = user?.id;
        if (!userId) return;
        const {
            data: analysisQuota,
            error: analysisQuotaError
        } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "entity-analysis").single();
        if (analysisQuotaError) {
            toast({description: analysisQuotaError.message, variant: 'destructive'});
        } else {
            setEntityAnalysisQuotaInfo(analysisQuota as QuotaUsedType);
        }
        const {
            data: companyQuota,
            error: companyQuotaError
        } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "entity").single();
        if (companyQuotaError) {
            toast({description: companyQuotaError.message, variant: 'destructive'});
        } else {
            setEntityQuotaInfo(companyQuota as QuotaUsedType);
        }
    }


    async function updateRuns() {
        const userId = user?.id!;


        const {
            data: analyses,
            error: docAnalysesError
        } = await supabase.from('view_single_doc_runs').select("*").eq("run_by", userId);

        const {
            data: quota,
            error: quotaError
        } = await supabase.from('view_quota_used').select("*").eq("profile_id", userId);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: 'destructive'});
        } else {
            setDocAnalyses(analyses as SingleDocAnalysesType[]);
        }
        if (quotaError) {
            toast({description: quotaError.message, variant: 'destructive'});
        } else {
            const docAnalysisQuota = quota.filter(i => i.type === "document-analysis")[0];
            if (docAnalysisQuota) {
                setQuotaInfo(docAnalysisQuota as QuotaUsedType);
            }
        }

    }


    useEffect(() => {
        runAsync(async () => {
            if (!user) return;
            await updateRuns();
            await updateCompanies();
        });

    }, [user]);


    return (
        <>
            <PageHeader/>
            <div className="p-6 space-y-6 dashboard-container" data-testid="usage-page">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 w-full" data-testid="quota-cards">

                    {/*{quotaInfo && (<DocQuotaCard quotaInfo={quotaInfo}/>)}*/}
                    {entityAnalysisQuotaInfo &&
                        <EntityAnalysisQuotaCard entityAnalysisQuotaInfo={entityAnalysisQuotaInfo}/>}
                    {entityQuotaInfo && <EntityQuotaCard entityQuotaInfo={entityQuotaInfo}/>}

                </div>

                {/* Main Content Tabs */}
                <Tabs defaultValue="entity_analysis" className="space-y-4 w-full" data-testid="analysis-history">
                    <TabsList data-testid="history-tabs">
                        <TabsTrigger value="entity_analysis" data-testid="tab-company-analysis">Company Analysis</TabsTrigger>
                        {false && (<TabsTrigger value="document_analysis" data-testid="tab-document-analysis">Document Analysis</TabsTrigger>)}
                    </TabsList>


                    <TabsContent value="entity_analysis" className="space-y-4" data-testid="company-analysis-history">
                        <EntityAnalysisHistory entityAnalyses={entityAnalyses}/>
                    </TabsContent>

                    <TabsContent value="document_analysis" className="space-y-4" data-testid="document-analysis-history">
                        <DocumentAnalysisHistory docAnalyses={docAnalyses}/>
                    </TabsContent>
                </Tabs>
            </div>
        </>
    );
};

export default AnalysisDashboard;
