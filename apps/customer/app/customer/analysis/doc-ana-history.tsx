/**
 * React Component for ESG Document Analysis History Display
 *
 * This React functional component provides a comprehensive interface for displaying the historical analysis 
 * records of ESG (Environmental, Social, Governance) documents within the EkoIntelligence platform. 
 * It renders an organized table view of completed document analyses with navigation capabilities to 
 * detailed greenwashing analysis reports. The component integrates with the customer database's 
 * `view_single_doc_runs` view to present document analysis history in a user-friendly, glass-morphism 
 * styled interface consistent with the platform's design system.
 *
 * ## Core Functionality
 * - **Analysis History Display**: Presents tabular view of historical ESG document analyses
 * - **Report Navigation**: Direct navigation to detailed greenwashing analysis reports via "View Report" buttons
 * - **Entity Context**: Maintains entity context through URL parameters for consistent cross-page navigation
 * - **Empty State Handling**: User-friendly messaging when no document analyses are available
 * - **Responsive Table Design**: Mobile-optimized horizontal scrolling for complex tabular data
 * - **Glass-morphism UI**: Consistent with platform's translucent, modern design aesthetic
 * - **Test Integration**: Comprehensive `data-testid` attributes for reliable automated testing
 *
 * ## Database Integration
 * **Data Source**: `view_single_doc_runs` (Customer Database View)
 * The component consumes data from a PostgreSQL view that joins multiple tables:
 * - `cus_ana_hist_gw_single_doc_runs`: Analysis run records with metadata
 * - `xfer_entities`: Entity information including names and types  
 * - `xfer_gw_single_doc`: Document analysis results and public URLs
 *
 * **SingleDocAnalysesType Structure** (from `view_single_doc_runs`):
 * - `id` (number): Unique analysis record identifier
 * - `analysis_id` (string): Document analysis identifier used for report navigation
 * - `quota_id` (number): Associated quota tracking for analysis resource management
 * - `run_by` (string): User identifier who initiated the analysis
 * - `name` (string): Human-readable document or entity name for display
 * - `type` (string): Document type classification (e.g., "Annual Report", "Sustainability Report")
 * - `public_url` (string): Public URL to source document if available
 * - `analysis_json` (object): Complete analysis results in structured JSON format
 * - `created_at` (string): ISO timestamp of analysis completion
 * - `entity_xid` (string): Entity external identifier for cross-system reference
 *
 * ## Navigation Flow
 * **Report Navigation Pattern**: 
 * `/customer/dashboard/gw/doc/{analysis_id}?entity={entity_xid}`
 * - Maintains entity context across navigation boundaries
 * - Uses `window.location.href` for full page navigation to detailed greenwashing analysis
 * - Analysis ID serves as primary route parameter for report retrieval
 * - Entity XID preserved as query parameter for contextual breadcrumbs and navigation
 *
 * ## Component Architecture
 * **Responsive Design**: 
 * - Horizontal scrolling container (`overflow-x-auto`) for mobile compatibility
 * - Fixed-width table structure with semantic column organization
 * - Glass-morphism Card container with header/content separation
 * - Hover states for interactive table rows enhancing user experience
 *
 * **UI Components** (shadcn/ui Integration):
 * - `Card`: Primary container with glass-morphism styling
 * - `CardHeader`: Section header with title styling
 * - `CardTitle`: Semantic heading for accessibility and SEO
 * - `CardContent`: Main content area with proper spacing and padding
 * - `Button`: Interactive elements with consistent platform styling
 *
 * ## Empty State Management
 * **No Data Handling**:
 * - Graceful empty state with user-friendly messaging
 * - Semantic table structure maintained even without data
 * - Actionable guidance directing users to document analysis features
 * - Accessible empty state with proper ARIA semantics via `data-testid`
 *
 * ## Testing Integration
 * **Test Identifiers** (data-testid attributes):
 * - `no-document-analysis-message`: Empty state message container
 * - `document-analysis-entry`: Individual analysis record rows
 * - `document-title`: Document name display cells
 * - `analysis-date`: Creation timestamp display cells  
 * - `analysis-status`: Action button containers (View Report buttons)
 *
 * ## Integration Points
 * This component fits into the broader EkoIntelligence ESG analysis ecosystem:
 * - **Analytics Database**: Source data from Python backend analysis pipeline
 * - **Customer Database**: Synchronized via `xfer_` tables for frontend consumption
 * - **Greenwashing Analysis**: Links to detailed document analysis reports
 * - **Entity Dashboard**: Part of comprehensive entity analysis interface
 * - **Quota Management**: Integrated with platform resource tracking systems
 *
 * ## Performance Considerations
 * - **Client-Side Rendering**: Component optimized for fast client-side data display
 * - **Efficient Navigation**: Direct URL construction without additional API calls
 * - **Memory Efficient**: Minimal state management, props-driven rendering
 * - **Responsive Performance**: Optimized table scrolling for mobile devices
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing Next.js App Router Navigation
 * @see https://ui.shadcn.com/docs/components/card shadcn/ui Card Component Documentation  
 * @see https://ui.shadcn.com/docs/components/button shadcn/ui Button Component Documentation
 * @see {@link /apps/customer/types.ts} SingleDocAnalysesType Definition
 * <AUTHOR>
 * @updated 2025-07-23
 * @description React component for displaying ESG document analysis history with navigation to detailed greenwashing reports
 * @example ```tsx
 * <DocumentAnalysisHistory 
 *   docAnalyses={[
 *     {
 *       id: 1,
 *       analysis_id: "doc-123",
 *       name: "Q4 Sustainability Report",
 *       type: "Annual Report", 
 *       created_at: "2024-12-01T10:00:00Z",
 *       entity_xid: "company-456"
 *     }
 *   ]} 
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import {SingleDocAnalysesType} from "@/types";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Button} from "@/components/ui/button";
import React from "react";

export function DocumentAnalysisHistory({docAnalyses}: { docAnalyses: SingleDocAnalysesType[] }) {
    function onViewReport(analysis: SingleDocAnalysesType) {
        return window.location.href = ("/customer/dashboard/gw/doc/" + analysis.analysis_id + "?entity=" + analysis.entity_xid);
    }

    return <Card>
        <CardHeader>
            <CardTitle>Document Analysis History</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead className="border-b">
                    <tr className="bg-zinc-50">
                        <th className="px-4 py-2 text-left font-medium text-zinc-500">Name</th>
                        <th className="px-4 py-2 text-left font-medium text-zinc-500">Type</th>
                        <th className="px-4 py-2 text-left font-medium text-zinc-500">Date</th>
                        <th className="px-4 py-2 text-right font-medium text-zinc-500">Actions</th>
                    </tr>
                    </thead>
                    <tbody className="divide-y">
                    {docAnalyses.length === 0 ? (
                        <tr>
                            <td colSpan={4} className="text-center py-8">
                                <div data-testid="no-document-analysis-message" className="text-gray-500">
                                    No document analyses found. Start by analyzing a document.
                                </div>
                            </td>
                        </tr>
                    ) : (
                        docAnalyses.map((analysis) => (
                            <tr key={analysis.id} className="hover:bg-zinc-50" data-testid="document-analysis-entry">
                                <td className="px-4 py-2" data-testid="document-title">{analysis.name}</td>
                                <td className="px-4 py-2">{analysis.type}</td>
                                <td className="px-4 py-2" data-testid="analysis-date">{analysis.created_at}</td>
                                <td className="px-4 py-2 text-right">
                                    <Button onClick={() => onViewReport(analysis)} variant="ghost" size="sm" data-testid="analysis-status">View
                                        Report</Button>
                                </td>
                            </tr>
                        ))
                    )}
                    </tbody>
                </table>
            </div>
        </CardContent>
    </Card>;
}
