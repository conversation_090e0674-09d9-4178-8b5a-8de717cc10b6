/**
 * Next.js App Router Document Analysis Dashboard Page Component
 *
 * This React Client Component provides a comprehensive interface for ESG document analysis within the
 * EkoIntelligence platform. Users can upload documents for analysis (either by file upload or URL),
 * monitor their analysis quota usage, and view their complete document analysis history. The component
 * integrates with the platform's backend analytics engine to process corporate documents and extract
 * ESG-related insights and statements.
 *
 * ## Core Functionality
 * - **Document Upload Interface**: Upload documents via file upload or URL input for ESG analysis
 * - **Real-time Analysis Tracking**: Monitor document processing status through the analysis queue
 * - **Quota Management**: Display usage statistics for document analysis quota with visual progress indicators
 * - **Analysis History**: View comprehensive history of all document analyses with entity associations
 * - **Navigation Integration**: Seamless navigation to detailed analysis reports and entity dashboards
 *
 * ## Document Analysis Process
 * **Supported Input Methods**:
 * - **File Upload**: Direct file uploads of PDF, DOC, DOCX and other document formats
 * - **URL Processing**: Extract and analyze documents from web URLs (reports, sustainability documents, etc.)
 * - **Entity Association**: Each document must be associated with a specific company/entity for contextual analysis
 *
 * **Analysis Pipeline**:
 * 1. Document ingestion and text extraction from uploaded files or URLs
 * 2. ESG statement identification and extraction using AI/ML models
 * 3. Domain classification (Environmental, Social, Governance impact analysis)
 * 4. Statement categorization and sentiment analysis
 * 5. Results integration into entity knowledge graphs and dashboards
 *
 * ## Database Integration
 * **Key Tables and Views**:
 * - `view_single_doc_runs`: Document analysis history with entity information and processing metadata
 *   - Contains: run ID, analysis ID, document URL, entity details, creation timestamps
 *   - Filtered by user ID for Row Level Security compliance
 * - `view_quota_used`: Real-time quota usage calculation for document analysis type
 *   - Tracks: used vs. allocated quota, quota period, scope (individual/organization)
 *   - Updates automatically when new analyses are completed
 * - `api_queue`: Backend processing queue for document analysis requests (referenced by DocumentUpload)
 * - `xfer_gw_single_doc`: Synced document analysis results from analytics database
 *
 * ## User Interface Features
 * - **Glass-morphism Design**: Consistent with EkoIntelligence design system using translucent cards and rounded elements
 * - **Responsive Layout**: Mobile-first design with adaptive grid layouts for optimal viewing on all devices
 * - **Visual Progress Indicators**: Color-coded quota usage bars (green < 60%, yellow 60-80%, red > 80%)
 * - **Interactive History Table**: Clickable analysis entries that navigate to detailed report views
 * - **Real-time Updates**: Component refreshes data when new documents are uploaded and processed
 * - **Status Feedback**: Toast notifications for successful uploads, processing status, and error conditions
 *
 * ## Security & Access Control
 * - **Row Level Security (RLS)**: All database queries automatically filtered by authenticated user ID
 * - **Authentication Required**: Component requires valid Supabase session to access user data
 * - **Quota Enforcement**: Document uploads subject to user/organization quota limits
 * - **Entity-based Filtering**: Users can only view analyses for entities they have access to
 *
 * ## Component Architecture
 * **State Management**:
 * - `docAnalyses`: Array of user's document analysis history from `view_single_doc_runs`
 * - `userId`: Current authenticated user ID from Supabase auth session
 * - `quotaInfo`: Current quota usage information for document analysis type
 * - `activeTab`: Currently unused state (legacy from companies tab functionality)
 *
 * **Child Components**:
 * - `DocumentUpload`: File upload and URL input interface with entity selection and analysis queue
 * - `DocumentAnalysisHistory`: Tabular view of analysis history with navigation to detailed reports
 * - `DocQuotaCard`: Visual quota usage display with progress bar and usage statistics
 * - `PageHeader`: Standard page header component with navigation and user context
 *
 * **Data Flow**:
 * 1. Component mounts and retrieves user ID from Supabase authentication
 * 2. `updateRuns()` fetches document analyses and quota information for the authenticated user
 * 3. Document upload completion triggers data refresh via `onComplete` callback
 * 4. Analysis history displays with clickable navigation to detailed report views
 *
 * ## Error Handling & User Feedback
 * - **Toast Notifications**: Success and error feedback for data loading and document processing
 * - **Graceful Loading**: Component shows loading state until quota information is available
 * - **Database Error Handling**: Proper error messaging for failed database queries
 * - **Validation Feedback**: Document upload validation messages via DocumentUpload component
 *
 * ## Performance Considerations
 * - **Conditional Rendering**: Component only renders after quota information is loaded
 * - **Selective Updates**: Data refresh only triggered by actual document upload completion
 * - **Efficient Queries**: Uses database views optimized for user-specific data retrieval
 * - **Async Loading**: Uses `runAsync` utility for proper async/await error handling in useEffect
 *
 * @see https://nextjs.org/docs/app/building-your-application/rendering/client-components Next.js Client Components
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase Row Level Security
 * @see https://react.dev/learn/synchronizing-with-effects React useEffect Hook
 * @see {@link ../../files/document-upload.tsx} DocumentUpload Component
 * @see {@link ./doc-ana-history.tsx} DocumentAnalysisHistory Component
 * @see {@link ./quotas.tsx} DocQuotaCard Component
 * @see {@link ../../../../types/types.ts} TypeScript Type Definitions
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Document analysis dashboard for uploading documents, monitoring analysis quotas, and viewing analysis history
 * @example
 * ```typescript
 * // Usage in Next.js App Router
 * // Located at /customer/analysis/documents
 * // Requires authentication and appropriate permissions
 * export default function DocumentAnalysisPage() {
 *   return <AnalysisDashboard />;
 * }
 * 
 * // Component integrates with backend analytics engine
 * // Documents processed through Python backend for ESG analysis
 * // Results stored in customer database for real-time access
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client"
import React, {useEffect, useState} from 'react';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {DocumentUpload} from "@/app/customer/files/document-upload";
import {createClient} from "@/app/supabase/client";
import {runAsync} from "@utils/react-utils";
import {useToast} from "@/hooks/use-toast";
import {QuotaUsedType, SingleDocAnalysesType} from "@/types";
import {useRouter} from "next/navigation";
import {DocumentAnalysisHistory} from "@/app/customer/analysis/doc-ana-history";
import {DocQuotaCard} from "@/app/customer/analysis/quotas";
import {PageHeader} from "@/components/page-header";

const AnalysisDashboard = () => {
    const supabase = createClient();
    const [activeTab, setActiveTab] = useState('companies');
    const [docAnalyses, setDocAnalyses] = useState<SingleDocAnalysesType[]>([]);
    const [userId, setUserId] = useState<string>();
    const {toast} = useToast();
    const router = useRouter();
    const [quotaInfo, setQuotaInfo] = useState<QuotaUsedType>();


    async function updateRuns(userId: string) {
        const {
            data: analyses,
            error: docAnalysesError
        } = await supabase.from('view_single_doc_runs').select("*").eq("run_by", userId);

        const {
            data: quota,
            error: quotaError
        } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "document-analysis").single();
        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: 'destructive'});
        } else {
            setDocAnalyses(analyses as SingleDocAnalysesType[]);
        }
        if (quotaError) {
            toast({description: quotaError.message, variant: 'destructive'});
        } else {
            setQuotaInfo(quota as QuotaUsedType);
        }

    }

    useEffect(() => {
        runAsync(async () => {
            if (!userId) return;
            await updateRuns(userId);
        });

    }, [userId]);

    useEffect(() => {
        runAsync(async () => {
            setUserId((await supabase.auth.getUser())?.data?.user?.id!)
        });
    }, []);

    if (!quotaInfo) return null;

    return (
        <>
            <PageHeader/>

            <div className="p-6 space-y-6">

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">


                    {quotaInfo && <DocQuotaCard quotaInfo={quotaInfo}/>}

                </div>

                <DocumentAnalysisHistory docAnalyses={docAnalyses}/>

                <Card className="mb-8">
                    <CardHeader className="flex items-center justify-between">
                        <CardTitle>Upload Document</CardTitle>
                        <CardDescription>Upload a document or specify a URL to have it analysed. Please also specify
                            which
                            company this applies to.</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <DocumentUpload onComplete={(payload: any) => userId && updateRuns(userId)}/>
                    </CardContent>
                </Card>


            </div>
        </>
    );
};

export default AnalysisDashboard;
