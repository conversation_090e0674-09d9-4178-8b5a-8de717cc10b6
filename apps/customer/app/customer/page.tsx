/**
 * EkoIntelligence Customer Welcome Dashboard - Next.js App Router Client Component
 *
 * This React client component serves as the primary welcome dashboard and landing page for authenticated users
 * within the EkoIntelligence ESG (Environmental, Social, Governance) analysis platform. It provides a
 * comprehensive customer portal interface that combines quota management, quick action navigation, resource
 * access, and real-time analytics data while integrating seamlessly with the platform's dual-database
 * architecture and glass-morphism design system.
 *
 * ## Core Functionality
 * - **Welcome Portal Interface**: Personalized greeting with user profile integration and company context
 * - **Quota Management Display**: Real-time monitoring of entity analysis, company, and document analysis quotas with visual progress indicators  
 * - **Quick Action Navigation**: Direct access to key platform features including dashboard, analysis tools, and account management
 * - **Entity Context Management**: Automatic default entity selection with URL parameter management for seamless navigation
 * - **Resource & Changelog Integration**: Help resources access and platform update notifications via integrated changelog system
 * - **Authentication Integration**: Secure user context management with automatic entity association and permission-based feature access
 * - **Toast Notification System**: Real-time error handling and user feedback through comprehensive toast notification integration
 *
 * ## Next.js App Router Architecture
 * **Client Component Implementation**:
 * This component leverages Next.js 15 App Router's client component architecture to manage browser-side
 * interactivity and real-time data fetching. The welcome dashboard requires client-side rendering for:
 * - Dynamic quota data loading with Supabase real-time subscriptions
 * - URL parameter management and entity context switching
 * - Interactive user session management and authentication state handling
 * - Real-time error handling with immediate user feedback via toast notifications
 * - Progressive data loading with async operations for smooth user experience
 *
 * **Route Structure Integration**:
 * ```
 * /customer/
 * ├── layout.tsx (Server Component - AuthProvider, EntityProvider context)
 * ├── page.tsx (Client Component - welcome dashboard interface)
 * ├── dashboard/ (Sub-route - main analytics dashboard)
 * ├── analysis/ (Sub-route - analysis tools and quota management)
 * ├── documents/ (Sub-route - document management and collaboration)
 * └── account/ (Sub-route - user account and profile management)
 * ```
 *
 * ## Database Integration & ESG Data Architecture
 * **Dual-Database ESG Analysis System**: 
 * The welcome dashboard integrates with both analytics and customer databases to provide comprehensive
 * quota monitoring and analysis run tracking:
 *
 * **Customer Database Views (Primary Data Source)**:
 * - `view_entity_analysis_runs`: Tracks comprehensive entity analysis execution with user attribution
 * - `view_quota_used`: Aggregates quota consumption across entity, document, and analysis types
 * - `view_single_doc_runs`: Documents individual document analysis execution history
 * - `view_my_companies`: User-accessible entities with organizational context and quota associations
 *
 * **Database Schema Integration**:
 * ```sql
 * -- Entity Analysis Tracking
 * view_entity_analysis_runs AS (
 *   SELECT entity_runs.id, quota_id, run_by, ent.name, ent.type, 
 *          run.id AS run_id, created_at, entity_xid
 *   FROM cus_ana_hist_entity_runs entity_runs
 *   JOIN xfer_entities ent ON (ent.entity_xid = entity_runs.entity_xid)
 *   LEFT JOIN xfer_runs run ON (run.id = entity_runs.run_id)
 * );
 * 
 * -- Quota Usage Aggregation
 * view_quota_used AS (
 *   SELECT item_type AS type, COALESCE(used.count, 0) AS used,
 *          quantity AS quota, period, scope, profile_id
 *   FROM view_quota_for_customer q
 *   LEFT JOIN usage_counts used ON (q.id = used.quota_id)
 *   WHERE item_type IN ('entity', 'document-analysis', 'entity-analysis')
 * );
 * ```
 *
 * ## Component Architecture & State Management
 * **React State Management Pattern**:
 * The component implements comprehensive state management for multiple data streams:
 * - `docAnalyses`: Single document analysis run history (SingleDocAnalysesType[])
 * - `entityAnalyses`: Entity analysis execution tracking (EntityAnalysesRunType[])
 * - `quotaInfo`: Document analysis quota consumption (QuotaUsedType)
 * - `entityQuotaInfo`: Entity quota usage tracking (QuotaUsedType)
 * - `entityAnalysisQuotaInfo`: Entity analysis quota monitoring (QuotaUsedType)
 * - `activeTab`: Navigation state management for future tab-based interface
 *
 * **Async Data Fetching Pattern**:
 * ```typescript
 * // Entity analysis and quota fetching
 * async function updateCompanies() {
 *   const analyses = await supabase.from("view_entity_analysis_runs")
 *     .select("*").eq("run_by", auth.user?.id);
 *   
 *   const analysisQuota = await supabase.from("view_quota_used")
 *     .select().eq("profile_id", userId).eq("type", "entity-analysis").single();
 *   
 *   const companyQuota = await supabase.from("view_quota_used")
 *     .select().eq("profile_id", userId).eq("type", "entity").single();
 * }
 * ```
 *
 * ## Quota Management & Visualization System
 * **Real-Time Quota Monitoring**:
 * The dashboard provides comprehensive quota tracking across three analysis types:
 * - **Entity Quotas**: Company/organization addition limits via EntityQuotaCard component
 * - **Document Analysis**: Single document processing quotas via DocQuotaCard (currently disabled)
 * - **Entity Analysis**: Comprehensive entity analysis execution quotas via EntityAnalysisQuotaCard
 *
 * **Visual Quota Components Integration**:
 * - `EntityQuotaCard`: Blue-themed quota display with Building2 icon and usage progress bar
 * - `EntityAnalysisQuotaCard`: Blue-themed analysis quota with usage visualization and status indicators
 * - Color-coded progress indicators (green ≤60%, yellow 61-80%, red >80%) for immediate usage awareness
 * - Test-friendly data attributes (`data-testid`) for comprehensive automated testing coverage
 *
 * ## Authentication & User Context Integration
 * **Supabase Authentication Integration**:
 * The component seamlessly integrates with the platform's authentication system:
 * - `useAuth()` hook provides user session management and profile access
 * - Automatic entity context establishment through `view_my_companies` query
 * - URL parameter management for entity selection persistence across navigation
 * - Authentication-aware data fetching with user ID filtering for security
 *
 * **Entity Context Management**:
 * ```typescript
 * // Automatic default entity selection
 * useEffect(() => {
 *   if (!searchParams.get("entity")) {
 *     await supabase.from('view_my_companies').select('*').then(response => {
 *       const params = new URLSearchParams();
 *       params.append("entity", response.data[0].entity_xid);
 *       router.push(`?${params.toString()}`);
 *     });
 *   }
 * }, [searchParams]);
 * ```
 *
 * ## UI/UX Design & Accessibility Features
 * **Glass-Morphism Design System**:
 * The welcome dashboard implements the platform's signature glass-morphism design:
 * - Translucent card backgrounds with backdrop blur effects
 * - Heavily rounded corners (1.5rem standard) for modern, approachable aesthetics
 * - Consistent spacing and typography hierarchy using Tailwind CSS utility classes
 * - Responsive layout with mobile-first design principles and adaptive breakpoints
 *
 * **Component Layout Structure**:
 * ```jsx
 * <SimplePageHeader> (Navigation header with sidebar trigger)
 * <WelcomeMessage /> (Dismissible personalized welcome message)
 * <main className="flex-grow">
 *   <div className="flex flex-col lg:flex-row gap-6">
 *     {/\* Column 1: Navigation & Resources *\/}
 *     <QuickActions /> + <Resources />
 *     
 *     {/\* Column 2: Quota Cards & Changelog *\/}
 *     <EntityAnalysisQuotaCard /> + <EntityQuotaCard /> + <ChangelogCard />
 *   </div>
 * </main>
 * ```
 *
 * ## Integration Points & System Architecture
 * **Platform Integration Ecosystem**:
 * - **Authentication System**: AuthProvider context for secure user session management
 * - **Entity Management**: EntityProvider context for global entity state synchronization  
 * - **Navigation System**: SimplePageHeader with sidebar integration and breadcrumb navigation
 * - **Notification System**: Toast integration for error handling and user feedback
 * - **Changelog System**: ChangelogCard for platform update notifications and version tracking
 * - **Analytics Pipeline**: Data flow from analytics database through xfer_ tables to customer database
 * - **Quota Enforcement**: Backend quota validation before analysis execution with real-time updates
 *
 * ## Error Handling & Resilience Patterns
 * **Comprehensive Error Management**:
 * - Toast notification integration for database query failures with descriptive error messages
 * - Graceful fallback handling for failed quota queries with safe default values
 * - Authentication state validation with automatic entity context establishment
 * - Network resilience with loading states and retry mechanisms for failed requests
 * - Type-safe database operations with TypeScript interface validation
 *
 * ## Performance Optimization Strategies
 * **Efficient Data Loading**:
 * - `runAsync` utility for coordinated async operations with error boundary protection
 * - Selective data fetching based on authentication state to minimize unnecessary requests
 * - React Hook optimization with dependency arrays to prevent excessive re-renders
 * - Lazy loading patterns for quota data with conditional rendering based on data availability
 * - Memoized component props to optimize child component re-rendering performance
 *
 * ## Testing & Quality Assurance Integration
 * **Test-Driven Development Support**:
 * - Comprehensive `data-testid` attributes across all interactive elements for reliable E2E testing
 * - Type-safe prop interfaces enabling robust unit testing with proper mock implementation
 * - Modular component architecture supporting isolated testing of individual functionality units
 * - Error boundary compatibility for testing failure scenarios and error recovery patterns
 * - Mock-friendly async patterns for testing database interactions and quota management workflows
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing Next.js App Router Documentation
 * @see https://supabase.com/docs/guides/auth Supabase Authentication Guide
 * @see https://supabase.com/docs/guides/database/postgres PostgreSQL Views and RLS Policies
 * @see https://react.dev/reference/react/useEffect React useEffect Hook Documentation
 * @see https://ui.shadcn.com/docs/components/card shadcn/ui Card Components
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/auth/auth-context.tsx Authentication Context Provider
 * @see /Users/<USER>/worktrees/279/apps/customer/components/simple-page-header.tsx Page Header Navigation Component
 * @see /Users/<USER>/worktrees/279/apps/customer/app/customer/analysis/quotas.tsx Quota Management Card Components
 * @see /Users/<USER>/worktrees/279/apps/customer/components/welcome-message.tsx Personalized Welcome Message Component
 * @see /Users/<USER>/worktrees/279/apps/customer/components/changelog/changelog-card.tsx Changelog Integration Component
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Customer welcome dashboard providing quota monitoring, quick navigation, and personalized ESG analysis platform access with real-time data integration
 * @example
 * ```tsx
 * // Usage within customer layout - handles authentication and entity context
 * export default function CustomerLayout({ children }) {
 *   return (
 *     <AuthProvider>
 *       <EntityProvider>
 *         <div className="customer-app">
 *           {children} {/\* Welcome dashboard page rendered here *\/}
 *         </div>
 *       </EntityProvider>
 *     </AuthProvider>
 *   );
 * }
 * 
 * // Component demonstrates quota monitoring integration
 * function QuotaMonitoringExample() {
 *   const { user } = useAuth();
 *   const [quotaInfo, setQuotaInfo] = useState<QuotaUsedType>();
 *   
 *   useEffect(() => {
 *     if (user) {
 *       // Fetch quota data as demonstrated in page.tsx
 *       supabase.from("view_quota_used")
 *         .select("*")
 *         .eq("profile_id", user.id)
 *         .eq("type", "entity-analysis")
 *         .single()
 *         .then(({ data }) => setQuotaInfo(data));
 *     }
 *   }, [user]);
 *   
 *   return quotaInfo ? (
 *     <EntityAnalysisQuotaCard entityAnalysisQuotaInfo={quotaInfo} />
 *   ) : (
 *     <div>Loading quota information...</div>
 *   );
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
"use client";
import React, { useEffect, useState } from 'react'
import { SimplePageHeader } from '@/components/simple-page-header'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/components/context/auth/auth-context'
import Link from 'next/link'
import { useToast } from '@/hooks/use-toast'
import { EntityAnalysesRunType, QuotaUsedType, SingleDocAnalysesType } from '@/types'
import { createClient } from '@/app/supabase/client'
import { runAsync } from '@utils/react-utils'
import { EntityAnalysisQuotaCard, EntityQuotaCard } from '@/app/customer/analysis/quotas'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useRouter, useSearchParams } from 'next/navigation'
import { ChangelogCard } from '@/components/changelog/changelog-card'
import { WelcomeMessage } from '@/components/welcome-message'

export default function Page() {

    const [activeTab, setActiveTab] = useState("companies");
    const auth = useAuth();
    const {toast} = useToast();
    const [docAnalyses, setDocAnalyses] = useState<SingleDocAnalysesType[]>([]);
    const supabase = createClient();
    const [quotaInfo, setQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalyses, setEntityAnalyses] = useState<EntityAnalysesRunType[]>([]);
    const [entityQuotaInfo, setEntityQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalysisQuotaInfo, setEntityAnalysisQuotaInfo] = useState<QuotaUsedType>();


    async function updateCompanies() {
        const {data: analyses, error: docAnalysesError} = await supabase
            .from("view_entity_analysis_runs")
            .select("*")
            .eq("run_by", auth.user?.id!);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: "destructive"});
        } else {
            setEntityAnalyses(analyses as EntityAnalysesRunType[]);
        }

        const userId = auth.user?.id;
        if (!userId) return;
        const {data: analysisQuota, error: analysisQuotaError} = await supabase
            .from("view_quota_used")
            .select()
            .eq("profile_id", userId)
            .eq("type", "entity-analysis")
            .single();
        if (analysisQuotaError) {
            toast({description: analysisQuotaError.message, variant: "destructive"});
        } else {
            setEntityAnalysisQuotaInfo(analysisQuota as QuotaUsedType);
        }

        const {data: companyQuota, error: companyQuotaError} = await supabase
            .from("view_quota_used")
            .select()
            .eq("profile_id", userId)
            .eq("type", "entity")
            .single();
        if (companyQuotaError) {
            toast({description: companyQuotaError.message, variant: "destructive"});
        } else {
            setEntityQuotaInfo(companyQuota as QuotaUsedType);
        }
    }

    async function updateRuns() {
        const userId = auth.user?.id!;
        const {data: analyses, error: docAnalysesError} = await supabase
            .from("view_single_doc_runs")
            .select("*")
            .eq("run_by", userId);

        const {data: quota, error: quotaError} = await supabase
            .from("view_quota_used")
            .select("*")
            .eq("profile_id", userId);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: "destructive"});
        } else {
            setDocAnalyses(analyses as SingleDocAnalysesType[]);
        }
        if (quotaError) {
            toast({description: quotaError.message, variant: "destructive"});
        } else {
            setQuotaInfo(quota.filter((i) => i.type === "document-analysis")[0] as QuotaUsedType);
        }
    }
    const router = useRouter();
    const searchParams = useSearchParams();

    useEffect(() => {
        runAsync(async () => {
            if(!searchParams.get("entity")) {
                await supabase.from('view_my_companies').select('*').then((response: any) => {
                    const params = new URLSearchParams();
                    params.append("entity", response.data[0].entity_xid);
                    router.push(`?${params.toString()}`);
                });
            }

        });
    }, [searchParams]);

    useEffect(() => {
        runAsync(async () => {
            if (!auth.user) return;
            await updateRuns();
            await updateCompanies();
        });
    }, [auth]);


    return (
        <>
            <SimplePageHeader>
                <h1 className="page-heading-title">Welcome</h1>
            </SimplePageHeader>
            <div className="min-h-screen  dashboard-container bg-muted flex flex-col">
                {/* Header */}
                <header className="bg-background shadow m-4">
                    <div className="w-full mx-auto py-6 px-4">
                        <h1 className="text-2xl font-semibold ">
                            Welcome Back {auth.profile?.full_name?.split(" ")[0] || "Valued Customer"}!
                        </h1>
                    </div>
                </header>

                {/* Welcome Message */}
                <div className="mx-4">
                    <WelcomeMessage />
                </div>

                {/* Main Content */}
                <main className="flex-grow mx-auto px-4 py-8 w-full">
                    <div className="flex flex-col lg:flex-row gap-6">
                        {/* Column 1 */}
                        <div className="flex flex-col space-y-6 w-full lg:w-1/3">
                            {/* Quick Actions */}
                            <Card>
                                <CardHeader><CardTitle>Quick Actions</CardTitle></CardHeader>
                                <CardContent>
                                    <ul className="mt-4 space-y-3">
                                        <li>
                                            <Link href={"/customer/dashboard"}>
                                                <Button variant="secondary" className="w-full">
                                                    View Dashboard
                                                </Button>
                                            </Link>
                                        </li>
                                        <li>
                                            <Link href={"/customer/analysis/companies"}>
                                                <Button variant="secondary" className="w-full">
                                                    Analyse a Company
                                                </Button>
                                            </Link>
                                        </li>
                                        {false && (
                                            <li>
                                                <Link href={"/customer/analysis/documents"}>
                                                    <Button variant="secondary" className="w-full">
                                                        Analyse a Document
                                                    </Button>
                                                </Link>
                                            </li>)}
                                        <li>
                                            <Link href={"/customer/account"}>
                                                <Button variant="secondary" className="w-full">
                                                    View Account
                                                </Button>
                                            </Link>
                                        </li>
                                    </ul>
                                </CardContent>
                            </Card>

                            {/* Onboarding or Resources */}
                            <Card className="">
                                <CardHeader><CardTitle>Resources</CardTitle>
                                </CardHeader>
                                <CardContent>

                                    <p className="mt-2 text-sm">
                                        Learn how to get more from ekoIntelligence.
                                    </p>
                                    <Link href={"/help"}>
                                        <Button variant="secondary" className="mt-4">
                                            Explore Resources
                                        </Button>
                                    </Link>
                                </CardContent>
                            </Card>

                        </div>

                        {/* Column 2 */}
                        <div className="flex flex-col space-y-6 w-full lg:w-2/3">
                            {/*{quotaInfo && false && <DocQuotaCard quotaInfo={quotaInfo}/>}*/}
                            {entityAnalysisQuotaInfo && (
                                <EntityAnalysisQuotaCard entityAnalysisQuotaInfo={entityAnalysisQuotaInfo}/>
                            )}
                            {entityQuotaInfo && <EntityQuotaCard entityQuotaInfo={entityQuotaInfo}/>}

                          {/* Changelog */}
                          <ChangelogCard />
                        </div>
                    </div>
                </main>
            </div>
        </>
    );
}
