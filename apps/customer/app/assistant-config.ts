/**
 * # OpenAI Assistants API Configuration for ESG Analysis Platform
 *
 * This configuration module provides a centralized registry of OpenAI Assistant IDs and role-based routing
 * for the EkoIntelligence ESG (Environmental, Social, Governance) analysis platform. The module manages
 * specialized AI assistants optimized for different aspects of corporate sustainability document analysis,
 * including entity analysis, retrieval-augmented generation (RAG) chat functionality, and report metadata
 * extraction. Each assistant is configured with specific instructions, tools, and capabilities tailored
 * to its designated role within the ESG analysis workflow.
 *
 * ## Core Functionality
 * - **Assistant Role Management**: Maps specific ESG analysis tasks to specialized OpenAI Assistant instances
 * - **Centralized Configuration**: Single source of truth for assistant IDs across the customer application
 * - **Type-Safe Role Selection**: TypeScript union types ensure compile-time validation of assistant roles
 * - **Fallback Assistant Handling**: Provides default assistant for undefined or invalid role requests
 * - **Scalable Architecture**: Easily extensible for additional ESG analysis roles and assistant types
 *
 * ## Assistant Roles & Specializations
 *
 * ### Company Analysis Assistant (`company-analysis`)
 * - **Primary Function**: Deep-dive corporate sustainability analysis and ESG risk assessment
 * - **Capabilities**: Processes corporate filings, sustainability reports, and disclosure documents
 * - **Specialized Analysis**: Entity relationship mapping, sector-specific ESG benchmarking, and risk scoring
 * - **Assistant ID**: `asst_zytqPQBYQBJyiToOJ8ARvODF`
 * - **Use Cases**: Corporate ESG scorecards, investment risk analysis, regulatory compliance assessment
 *
 * ### RAG Chat Assistant (`rag-chat`)
 * - **Primary Function**: Interactive conversational AI with retrieval-augmented generation capabilities
 * - **Knowledge Base**: Access to comprehensive ESG knowledge corpus and historical analysis data
 * - **Capabilities**: Context-aware responses using document embeddings and vector search
 * - **Assistant ID**: `asst_iLshFtn264BsqUP54YtKggRR`
 * - **Use Cases**: User queries about ESG data, explanatory analysis, document Q&A interactions
 *
 * ### Report Metadata Assistant (`report-metadata`)
 * - **Primary Function**: Automated extraction and structuring of document metadata and key information
 * - **Processing Focus**: Document classification, key metrics extraction, and structured data generation
 * - **Capabilities**: Metadata extraction, report categorization, and data normalization
 * - **Assistant ID**: `asst_iLshFtn264BsqUP54YtKggRR` (shares configuration with RAG chat)
 * - **Use Cases**: Document indexing, report cataloging, and automated data entry workflows
 *
 * ## Technical Architecture
 *
 * ### Role-Based Assistant Selection
 * The `assistantForRole()` function implements a **strategy pattern** for assistant selection:
 * ```typescript
 * const assistantId = assistantForRole('company-analysis');
 * // Returns: 'asst_zytqPQBYQBJyiToOJ8ARvODF'
 * ```
 *
 * ### Integration with OpenAI Assistants API
 * - **Thread Management**: Each conversation creates isolated threads for context management
 * - **Tool Integration**: Assistants have access to specialized tools (Code Interpreter, File Search)
 * - **Message Handling**: Asynchronous message processing with run-based execution model
 * - **Context Retention**: Thread-based conversation history for multi-turn interactions
 *
 * ### System Integration Points
 * - **Customer Database**: Assistant responses integrate with Supabase customer database via `xfer_` tables
 * - **Analytics Pipeline**: Company analysis results feed into backend Python analytics system
 * - **Document Processing**: File uploads processed through OpenAI's file API for assistant access
 * - **Real-time Chat**: RAG assistant powers interactive chat interface with WebSocket support
 *
 * ## ESG Analysis Workflow Integration
 *
 * ### Document Analysis Pipeline
 * ```
 * Document Upload → File Processing → Assistant Analysis → Results Storage → Customer Display
 * ```
 *
 * ### Multi-Assistant Coordination
 * - **Sequential Processing**: Documents may be processed by multiple assistants for comprehensive analysis
 * - **Role Specialization**: Each assistant focuses on specific ESG analysis dimensions
 * - **Result Aggregation**: Assistant outputs combined for holistic ESG assessment
 * - **Quality Assurance**: Cross-validation between assistants for accuracy verification
 *
 * ## Configuration Management
 *
 * ### Environment-Specific Assistants
 * - **Development**: May use different assistant IDs for testing and development
 * - **Production**: Live assistant IDs optimized for customer-facing analysis
 * - **Staging**: Dedicated assistants for pre-production validation and testing
 *
 * ### Assistant Capabilities & Tools
 * Each assistant is configured in OpenAI with specific tools and capabilities:
 * - **Code Interpreter**: For quantitative analysis and data processing
 * - **File Search**: For retrieval-augmented generation with document corpus
 * - **Function Calling**: For integration with custom ESG analysis functions
 * - **Knowledge Cutoff**: Trained on latest ESG standards and regulatory frameworks
 *
 * ## Usage Examples
 *
 * ### Basic Assistant Selection
 * ```typescript
 * import { assistantForRole, type AssistantRole } from './assistant-config';
 *
 * const role: AssistantRole = 'company-analysis';
 * const assistantId = assistantForRole(role);
 * // Use assistantId with OpenAI Assistants API
 * ```
 *
 * ### Integration with OpenAI API
 * ```typescript
 * const run = await openai.beta.threads.runs.create({
 *   thread_id: threadId,
 *   assistant_id: assistantForRole('rag-chat'),
 *   instructions: "Analyze this ESG document for climate-related risks..."
 * });
 * ```
 *
 * ## Security & Best Practices
 *
 * ### Assistant ID Management
 * - **Centralized Storage**: All assistant IDs managed in single configuration file
 * - **Type Safety**: TypeScript ensures only valid roles can be requested
 * - **Fallback Handling**: Default assistant prevents application failures from invalid roles
 * - **Environment Isolation**: Different assistants for different deployment environments
 *
 * ### Data Privacy & Compliance
 * - **Thread Isolation**: Customer data separated through thread-based conversation management
 * - **Access Control**: Assistant access controlled through OpenAI API key and organization settings
 * - **Data Retention**: Thread lifecycle management aligned with customer data retention policies
 * - **Audit Trail**: All assistant interactions logged for compliance and quality monitoring
 *
 * ## Future Extensibility
 *
 * ### Additional ESG Roles
 * The configuration can easily accommodate new specialized assistants:
 * - **Regulatory Compliance**: Focused on specific regulatory framework analysis
 * - **Supply Chain**: Specialized in supply chain ESG risk assessment
 * - **Climate Risk**: Dedicated to climate scenario analysis and physical risk assessment
 * - **Social Impact**: Focused on social and governance factor analysis
 *
 * ### Multi-Model Support
 * - **Model Diversity**: Different assistants can use different underlying models (GPT-4, GPT-4 Turbo)
 * - **Capability Optimization**: Role-specific model selection for optimal performance
 * - **Cost Management**: Efficient model selection based on analysis complexity requirements
 * - **Performance Tuning**: A/B testing different models for specific ESG analysis tasks
 *
 * @see https://platform.openai.com/docs/assistants/overview OpenAI Assistants API Documentation
 * @see https://platform.openai.com/docs/assistants/tools Assistants API Tools Documentation
 * @see https://docs.anthropic.com/en/docs/about-claude Claude AI Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js API Routes
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Configuration module providing centralized OpenAI Assistant ID management and role-based routing for ESG analysis platform specialized AI assistants
 * @example ```typescript
 * // Get assistant ID for company analysis
 * const assistantId = assistantForRole('company-analysis');
 *
 * // Use with OpenAI Assistants API
 * const thread = await openai.beta.threads.create();
 * const run = await openai.beta.threads.runs.create({
 *   thread_id: thread.id,
 *   assistant_id: assistantId
 * });
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

export let assistantId = ""; // set your assistant ID here

export type AssistantRole = "company-analysis" | "rag-chat" | "report-metadata"

export function assistantForRole(role: AssistantRole): string {
    switch (role) {
        case "company-analysis":
            return "asst_zytqPQBYQBJyiToOJ8ARvODF";
        case "rag-chat":
            return "asst_iLshFtn264BsqUP54YtKggRR";
        case "report-metadata":
            return "asst_iLshFtn264BsqUP54YtKggRR";
        default:
            return "asst_iLshFtn264BsqUP54YtKggRR";
    }
}
