/**
 * Next.js App Router API Route Handler for Supabase Authentication Callback
 *
 * This route handles the OAuth callback flow for Supabase authentication using server-side rendering.
 * It processes authorization codes from external OAuth providers (Google, GitHub, etc.) and exchanges
 * them for user sessions. This endpoint is a critical part of the server-side authentication flow
 * that enables secure user login and session management within the EkoIntelligence ESG platform.
 *
 * ## Core Functionality
 * - **OAuth Callback Processing**: Handles redirect callbacks from external authentication providers
 * - **Authorization Code Exchange**: Exchanges temporary auth codes for permanent user sessions
 * - **Session Establishment**: Creates secure server-side user sessions using Supabase Auth
 * - **Automatic Redirection**: Redirects authenticated users to the admin reports dashboard
 * - **Server-Side Security**: Maintains authentication state securely on the server
 *
 * ## Authentication Flow
 * 1. User initiates login through external provider (Google, GitHub, etc.)
 * 2. Provider redirects to this callback endpoint with authorization code
 * 3. Route extracts the code from URL search parameters
 * 4. Supabase client exchanges code for user session tokens
 * 5. User is redirected to `/admin/reports` with active session
 *
 * ## Request Structure
 * - **HTTP Method**: GET
 * - **URL Parameters**: 
 *   - `code` (query parameter): Authorization code from OAuth provider
 *   - Standard OAuth state parameters handled automatically
 *
 * ## Response Behavior
 * - **Successful Authentication**: HTTP 302 redirect to `/admin/reports`
 * - **Missing Code**: Still redirects to `/admin/reports` (graceful degradation)
 * - **Session Cookies**: Automatically set via Supabase SSR client configuration
 *
 * ## Security Features
 * - **Server-Side Processing**: All token exchange occurs on the server for maximum security
 * - **Cookie-Based Sessions**: Uses secure HTTP-only cookies for session persistence
 * - **Row Level Security**: Integrates with Supabase RLS policies for data access control
 * - **CSRF Protection**: OAuth state parameter validation handled by Supabase client
 *
 * ## Integration Points
 * - **Supabase Auth**: Primary authentication service with OAuth provider integrations
 * - **Customer Database**: User profiles stored in `profiles` table linked to `auth.users`
 * - **Admin Dashboard**: Post-authentication landing page for administrative functions
 * - **RLS Policies**: Database-level security enforced through authenticated user context
 *
 * ## Related Components
 * - OAuth provider configurations in Supabase dashboard
 * - User profile management system (`profiles` table with admin flags)
 * - Admin interface for managing organizations, users, and system features
 * - Session management and logout functionality throughout the application
 *
 * @see https://supabase.com/docs/guides/auth/server-side/nextjs Supabase Server-Side Auth Guide
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://tools.ietf.org/html/rfc6749#section-4.1.2 OAuth 2.0 Authorization Code Flow
 * @see {@link ../supabase/server.ts} Supabase Server Client Configuration
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This route handles the OAuth callback flow for Supabase authentication using server-side rendering.
 * @example ```bash
 * # OAuth callback after Google authentication
 * GET /auth/callback?code=auth_code_here&state=state_value
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import {createClient} from "@/app/supabase/server";
import {NextResponse} from "next/server";

export async function GET(request: Request) {
    // The `/auth/callback` route is required for the server-side auth flow implemented
    // by the SSR package. It exchanges an auth code for the user's session.
    // https://supabase.com/docs/guides/auth/server-side/nextjs
    const requestUrl = new URL(request.url);
    const code = requestUrl.searchParams.get("code");
    const origin = requestUrl.origin;

    if (code) {
        const supabase = await createClient();
        ;
        await supabase.auth.exchangeCodeForSession(code);
    }

    // URL to redirect to after sign up process completes
    return NextResponse.redirect(`${origin}/admin/reports`);
}
