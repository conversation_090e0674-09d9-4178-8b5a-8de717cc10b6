/**
 * # Next.js 404 Not Found Page Component for EkoIntelligence Platform
 *
 * This component serves as the standardized 404 "Not Found" error page for the EkoIntelligence ESG analysis platform's
 * customer application. Built specifically for Next.js 15 App Router architecture, this component provides a professional,
 * user-friendly interface when users navigate to non-existent routes or when the `notFound()` function is explicitly
 * called from route segments. The component maintains the platform's glass-morphism design language while offering
 * clear navigation options to help users return to productive ESG analysis workflows.
 *
 * This 404 page is particularly important in the context of ESG data analysis where users may bookmark specific
 * entity reports, document links, or analysis endpoints that could become unavailable due to data updates, permission
 * changes, or content reorganization. The professional presentation ensures user confidence in the platform while
 * providing clear recovery paths for continued ESG analysis and reporting tasks.
 *
 * ## Core Functionality & User Experience Design
 *
 * ### **Professional Error Communication**
 * The component presents a clear, non-technical explanation of the 404 error that maintains the professional
 * standards expected in financial services applications. The messaging acknowledges the issue without implying
 * user error, providing helpful guidance for next steps while maintaining confidence in the platform's
 * reliability for critical ESG analysis workflows.
 *
 * ### **Visual Design Integration**
 * Implements a centered layout with glass-morphism visual elements consistent with the EkoIntelligence design
 * system. The component uses the platform's established color palette, typography hierarchy, and spatial
 * relationships to ensure visual continuity with the broader ESG analytics interface, maintaining user
 * familiarity and professional appearance during error states.
 *
 * ### **Clear Visual Hierarchy**
 * Utilizes a structured information hierarchy with:
 * - **Primary Alert Icon**: AlertCircle from Lucide React in destructive color to immediately signal the error state
 * - **Error Code Display**: Large "404" text for immediate recognition and technical clarity
 * - **User-Friendly Explanation**: Clear, professional messaging that explains the situation without technical jargon
 * - **Actionable Recovery**: Prominent button linking back to the application home for immediate workflow recovery
 *
 * ## Next.js App Router Integration
 *
 * ### **Automatic 404 Handling**
 * This component is automatically invoked by Next.js 15 App Router in two primary scenarios:
 * 1. **Route Not Found**: When users navigate to URLs that don't match any defined routes in the application
 * 2. **Explicit notFound() Calls**: When route segments explicitly call the `notFound()` function, such as
 *    when ESG entities, reports, or documents are not found in the database
 *
 * ### **File Convention Compliance**
 * Following Next.js App Router conventions, this component is named `not-found.tsx` and placed in the `app`
 * directory to serve as the root-level 404 handler. This ensures it catches all unmatched routes throughout
 * the ESG analysis application, providing consistent error handling across entity analysis, report generation,
 * document editing, and other platform features.
 *
 * ### **SEO and HTTP Response Handling**
 * The component automatically triggers proper HTTP 404 status codes for SEO compliance and browser behavior.
 * Next.js handles the HTTP response automatically, ensuring search engines properly index (or exclude) these
 * error pages while maintaining proper web standards for the financial services domain.
 *
 * ## Component Architecture & Dependencies
 *
 * ### **Next.js Link Integration**
 * Utilizes the optimized Next.js `Link` component for client-side navigation back to the home page. This
 * ensures fast navigation recovery without full page reloads, maintaining the single-page application
 * experience critical for complex ESG analysis workflows where users may have multiple data points,
 * calculations, or analysis contexts in memory.
 *
 * ### **UI Component Integration**
 * Leverages the platform's standardized `Button` component from the UI library, ensuring visual and
 * behavioral consistency with other interactive elements throughout the ESG platform. The button uses
 * the `asChild` prop pattern to compose seamlessly with the Next.js Link component while maintaining
 * all button styling and accessibility features.
 *
 * ### **Icon System Integration**
 * Uses the Lucide React icon library's `AlertCircle` icon for consistent iconography across the platform.
 * The icon provides immediate visual recognition of the error state while maintaining the professional
 * aesthetic appropriate for financial services applications focused on ESG data analysis.
 *
 * ## Responsive Design & Accessibility
 *
 * ### **Mobile-First Layout**
 * Implements a responsive layout using Tailwind CSS utilities that provides optimal viewing and interaction
 * on devices commonly used for ESG analysis, from desktop workstations to mobile devices used for field
 * data collection or executive review. The centered layout adapts fluidly across screen sizes while
 * maintaining readability and interaction accessibility.
 *
 * ### **Accessibility Features**
 * - **ARIA Compliance**: Icon includes `aria-hidden="true"` to prevent screen reader confusion with text content
 * - **Semantic Structure**: Uses proper heading hierarchy and semantic HTML for screen reader navigation
 * - **Keyboard Navigation**: Button component provides full keyboard accessibility for users with motor impairments
 * - **Color Contrast**: Destructive color scheme maintains WCAG AA compliance for visual accessibility
 * - **Focus Management**: Button receives proper focus indicators for keyboard navigation workflows
 *
 * ### **Performance Optimization**
 * The component uses minimal dependencies and efficient rendering patterns suitable for error pages that
 * need to load quickly even when the main application may be experiencing issues. The lightweight
 * implementation ensures rapid display for users who need immediate feedback and recovery options.
 *
 * ## Integration with ESG Platform Workflows
 *
 * ### **Entity Analysis Error Handling**
 * When users attempt to access non-existent entity analysis pages, this component provides graceful
 * degradation while maintaining the platform's professional appearance. Users can quickly return to
 * entity browsing and search functionality to continue their ESG analysis workflows without frustration
 * or confusion about the platform's capabilities.
 *
 * ### **Report and Document Access**
 * For bookmarked or shared links to ESG reports, analysis documents, or collaborative editing sessions
 * that may no longer be available, the component provides clear communication and immediate recovery
 * paths. This is particularly important for collaborative ESG reporting workflows where team members
 * may share links to analysis results or document drafts.
 *
 * ### **API and Data Endpoint Errors**
 * While primarily designed for route-level 404 errors, the component also serves as a fallback for
 * situations where data endpoints return 404 responses and route segments call `notFound()`. This
 * ensures consistent error presentation across different types of missing content in the ESG platform.
 *
 * ## System Architecture Context
 *
 * ### **Error Handling Strategy**
 * This component is part of a comprehensive error handling strategy that includes:
 * - **404 Not Found**: This component for missing routes and content
 * - **500 Server Errors**: Separate error boundary components for application errors
 * - **Client-Side Errors**: React error boundaries for component-level failures
 * - **Network Errors**: API error handling for data fetching failures
 *
 * ### **User Experience Flow**
 * Designed to integrate seamlessly with the overall user experience flow of ESG analysis:
 * 1. **Error Recognition**: Clear visual and textual indication of the 404 state
 * 2. **Situation Understanding**: Professional explanation that maintains user confidence
 * 3. **Recovery Action**: One-click navigation back to productive workflows
 * 4. **Workflow Continuity**: Minimal disruption to ongoing ESG analysis tasks
 *
 * ### **Brand and Design Consistency**
 * Maintains visual and experiential consistency with other error states and interface elements
 * throughout the EkoIntelligence platform. The glass-morphism effects, color choices, typography,
 * and interaction patterns align with the broader design system focused on professional financial
 * services presentation and ESG data visualization clarity.
 *
 * ## Future Enhancement Opportunities
 *
 * ### **Smart Suggestion System**
 * Future versions could analyze the requested URL to suggest alternative pages or similar entities,
 * reports, or analysis tools that might match user intent. This would be particularly valuable for
 * ESG entity analysis where users might have slight URL variations or outdated bookmarks.
 *
 * ### **Search Integration**
 * Integration with the platform's search functionality could allow users to search for ESG entities,
 * reports, or analysis tools directly from the 404 page, converting error encounters into successful
 * content discovery without requiring navigation back to the home page.
 *
 * ### **Analytics and Monitoring**
 * Enhanced integration with analytics tools could track common 404 patterns to identify broken links,
 * outdated bookmarks, or content reorganization needs within the ESG platform's information architecture.
 *
 * @see https://nextjs.org/docs/app/api-reference/file-conventions/not-found Next.js not-found.js Documentation
 * @see https://nextjs.org/docs/app/api-reference/functions/not-found Next.js notFound() Function Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating Next.js Link Component
 * @see https://lucide.dev/icons/alert-circle Lucide React AlertCircle Icon Documentation
 * @see {@link ../../components/ui/button.tsx} Button Component Integration
 * @see {@link ./layout.tsx} Root Layout Component
 * @see {@link ./error.tsx} Error Boundary Component
 * @see {@link ./global-error.tsx} Global Error Handler
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Professional 404 Not Found page component for Next.js App Router with glass-morphism design and user-friendly error recovery for ESG analysis platform
 * @example ```tsx
 * // Automatic usage by Next.js App Router for unmatched routes
 * // Example: User navigates to /non-existent-entity
 * // Result: This component renders automatically
 *
 * // Programmatic usage in route segments
 * import { notFound } from 'next/navigation'
 *
 * export default async function EntityPage({ params }) {
 *   const entity = await fetchEntity(params.id)
 *
 *   if (!entity) {
 *     notFound() // Triggers this 404 component
 *   }
 *
 *   return <EntityAnalysis entity={entity} />
 * }
 *
 * // Dynamic route handling with proper 404s
 * export default async function ReportPage({ params }) {
 *   const report = await getESGReport(params.entityId, params.reportId)
 *
 *   if (!report || !report.isPublished) {
 *     notFound() // Shows this professional 404 page
 *   }
 *
 *   return <ESGReportViewer report={report} />
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { AlertCircle } from 'lucide-react'

export default function NotFound() {
    return (
        <div className="min-h-screen bg-background flex items-center justify-center px-4">
            <div className="max-w-md w-full space-y-8 text-center">
                <div className="space-y-2">
                    <AlertCircle className="mx-auto h-12 w-12 text-destructive" aria-hidden="true"/>
                    <h1 className="text-4xl font-bold tracking-tight">404</h1>
                    <p className="text-xl font-semibold text-muted-foreground">Page not found</p>
                </div>
                <div className="text-muted-foreground">
                    <p>Sorry, we couldn't find the page you're looking for.</p>
                    <p>Please check the URL in the address bar and try again.</p>
                </div>
                <div className="pt-4">
                    <Button asChild>
                        <Link href="/">
                            Go back home
                        </Link>
                    </Button>
                </div>
            </div>
        </div>
    )
}
