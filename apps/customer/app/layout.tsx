/**
 * # Root Layout Component for EkoIntelligence ESG Analysis Platform
 *
 * This Next.js 15 App Router root layout component serves as the foundational wrapper for the entire
 * EkoIntelligence ESG (Environmental, Social, Governance) analysis platform's customer-facing application.
 * As the top-level layout component in the App Router architecture, it establishes the core HTML structure,
 * global styling foundation, authentication context, navigation infrastructure, and mobile-responsive navigation
 * elements that support the platform's sophisticated ESG data analysis, document editing, and reporting workflows.
 *
 * Built for enterprise-grade ESG analysis platforms serving financial services and sustainability-focused organizations,
 * this component integrates multiple critical system layers including Supabase authentication, Next.js 15 optimizations,
 * glass-morphism design systems, and comprehensive mobile navigation patterns required for complex ESG data workflows.
 *
 * ## Core Architecture & System Integration
 *
 * ### **Next.js 15 App Router Foundation**
 * Implements the mandatory root layout structure required by Next.js App Router, providing the essential HTML skeleton
 * that wraps all application pages, layouts, and components. Unlike the Pages Router's `_app.js` and `_document.js`
 * separation, this component combines both concerns into a single, cohesive layout that handles both application
 * initialization and document structure for the ESG analysis platform.
 *
 * ### **Global Typography and Design System**
 * Establishes the foundational typography system using Geist Sans font family, providing modern, professional typography
 * optimized for financial data display, ESG metrics visualization, and complex document editing interfaces. The font
 * selection supports both web and system font fallbacks ensuring consistent rendering across all devices and platforms
 * used by ESG analysts and financial professionals.
 *
 * ### **Authentication Infrastructure Integration**
 * While this root layout doesn't directly implement authentication logic, it provides the foundational context wrapper
 * that enables the AuthProvider (imported from `@/components/context/auth/auth-context`) to function across the entire
 * application. This architecture ensures that Supabase authentication, user profile management, admin authorization,
 * and hierarchical feature flag evaluation are available to all child components throughout the ESG analysis platform.
 *
 * ### **SEO and Metadata Foundation**
 * Establishes core SEO metadata including the application title and description specifically tailored for ESG analysis
 * and investment transparency use cases. The metadata is designed to optimize search engine visibility for sustainability
 * reporting, ESG analysis, and investment transparency keywords relevant to the platform's target audience of financial
 * professionals and sustainability analysts.
 *
 * ## ESG Platform-Specific Features
 *
 * ### **Navigation Context Architecture**
 * Integrates the NavigationProvider context that manages dynamic page titles, breadcrumb navigation, and routing state
 * across the complex ESG analysis workflows. This navigation system supports multi-level dashboard hierarchies, document
 * management flows, entity analysis processes, and administrative functions while maintaining proper navigation state
 * during complex multi-step ESG analysis tasks.
 *
 * ### **Mobile-Responsive ESG Workflows**
 * Includes the MobileBottomTabBar component that provides touch-optimized navigation for ESG analysts working on mobile
 * devices in the field or during client presentations. The mobile navigation supports quick access to dashboards, reports,
 * company analysis tools, and user account management while maintaining the professional appearance required for client-facing
 * ESG consulting work.
 *
 * ### **URL State Management for Analysis Parameters**
 * Integrates the NuqsAdapter from the nuqs library, enabling sophisticated URL-based state management for ESG analysis
 * parameters, filter configurations, time-series selections, and other complex analytical state that needs to persist
 * across navigation, browser sessions, and collaborative sharing scenarios common in ESG research and reporting workflows.
 *
 * ## Technical Architecture Layers
 *
 * ### **CSS Foundation and Glass-Morphism Design**
 * Imports the global CSS foundation (`./globals.css`) that establishes the platform's signature glass-morphism design
 * system with heavily rounded elements, translucent surfaces with backdrop blur effects, and generous border radii.
 * This design system creates a modern, approachable aesthetic while maintaining the professional appearance required
 * for financial services applications and ESG reporting interfaces.
 *
 * ### **Font Optimization Strategy**
 * Implements Next.js font optimization using both Geist Sans (primary) and Inter (fallback) font families configured
 * with optimal loading strategies. The font selection prioritizes readability of complex financial data, ESG metrics,
 * and analytical content while supporting international character sets required for global ESG analysis and reporting.
 *
 * ### **Provider Hierarchy and Context Management**
 * Establishes a carefully orchestrated provider hierarchy:
 * 1. **NuqsAdapter**: URL state management (outermost layer)
 * 2. **NavigationProvider**: Navigation and routing context
 * 3. **Application Content**: All child pages and components
 * 4. **MobileBottomTabBar**: Mobile navigation overlay (rendered outside content flow)
 *
 * This hierarchy ensures proper context propagation and prevents conflicts between different state management systems
 * while maintaining optimal performance for complex ESG data analysis workflows.
 *
 * ## Database and Backend Integration Context
 *
 * ### **Supabase PostgreSQL Foundation**
 * While not directly interfacing with the database, this component establishes the client-side foundation that enables
 * Supabase authentication and database connectivity throughout the application. The layout component's AuthProvider
 * integration connects to:
 * - **Customer Database**: User profiles, documents, and application-specific data via RLS policies
 * - **Analytics Database**: ESG data, entity scoring, claims analysis, and promises tracking
 * - **Data Synchronization**: Automated sync between analytics and customer databases via `xfer_` tables
 *
 * ### **ESG Data Pipeline Integration**
 * Provides the application-level context required for the ESG analysis pipeline including:
 * - **Entity Analysis**: Company ESG scoring and risk assessment workflows
 * - **Claims Analysis**: Corporate sustainability claims verification against historical evidence
 * - **Promises Tracking**: Monitoring corporate commitments over time with trend visualization
 * - **Document Processing**: Advanced TipTap-based collaborative document editing with AI integration
 * - **Report Generation**: Hierarchical ESG reports with AI-powered content generation
 *
 * ## Development and Production Environment Support
 *
 * ### **Environment-Aware URL Configuration**
 * Uses dynamic URL generation based on environment variables (`VERCEL_URL` for production, localhost for development)
 * ensuring proper metadata base URLs for SEO, social sharing, and canonical URL generation across different deployment
 * environments while maintaining proper ESG platform branding and search engine optimization.
 *
 * ### **Production Optimization Features**
 * Implements Next.js 15 production optimizations including:
 * - **Automatic font optimization** with preloading and font-display swap strategies
 * - **CSS optimization** with critical path CSS inlining and efficient delivery
 * - **JavaScript optimization** with proper chunking and lazy loading for ESG analysis modules
 * - **Metadata optimization** for search engines and social platforms relevant to ESG and sustainability sectors
 *
 * ## Security and Compliance Framework
 *
 * ### **Authentication Security Foundation**
 * Establishes the security context required for Supabase authentication with JWT token management, session persistence,
 * and automatic token refresh cycles. This foundation supports the platform's enterprise security requirements for
 * handling sensitive ESG data, financial information, and client analysis results in compliance with financial
 * services regulations.
 *
 * ### **Content Security and Data Protection**
 * Provides the application-level foundation for comprehensive data protection including:
 * - **RLS Policy Enforcement**: Row Level Security policies automatically filter ESG data based on user context
 * - **Feature Flag Security**: Server-side validation prevents client-side manipulation of analysis capabilities
 * - **Session Management**: Secure session storage with automatic cleanup and compliance-grade logging
 * - **API Authorization**: Foundation for server-side authorization in ESG data analysis API routes
 *
 * ## Performance and Scalability Considerations
 *
 * ### **Optimized Loading Strategy**
 * Implements strategic loading optimizations for the ESG analysis platform:
 * - **Critical resource prioritization** for authentication and core navigation components
 * - **Lazy loading preparation** for complex ESG analysis modules and visualization libraries
 * - **Font loading optimization** with preloading and fallback strategies for global typography
 * - **CSS delivery optimization** ensuring immediate visual stability for professional ESG interfaces
 *
 * ### **Mobile Performance Optimization**
 * Includes mobile-specific optimizations essential for ESG analysts working in the field:
 * - **Touch-optimized navigation** with appropriate hit targets for professional mobile use
 * - **Responsive design foundation** supporting complex ESG data visualization on small screens
 * - **Progressive enhancement** ensuring core ESG analysis functionality works across all device capabilities
 * - **Network optimization** for ESG data loading in variable connectivity environments
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts Next.js App Router Layouts
 * @see https://nextjs.org/docs/app/api-reference/file-conventions/layout Next.js Layout File Convention
 * @see https://github.com/47ng/nuqs Nuqs URL State Management Library
 * @see https://supabase.com/docs/guides/auth Supabase Authentication Documentation
 * @see https://vercel.com/docs/concepts/fonts Vercel Font Optimization
 * @see {@link /components/context/auth/auth-context.tsx} Authentication Context Provider
 * @see {@link /components/context/nav/nav-context.tsx} Navigation Context Provider
 * @see {@link /components/mobile-bottom-tab-bar.tsx} Mobile Bottom Tab Navigation
 * @see {@link /globals.css} Global CSS and Design System Foundation
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Root Layout Component for EkoIntelligence ESG Analysis Platform - provides foundational HTML structure, authentication context, navigation infrastructure, and mobile-responsive design for enterprise ESG data analysis workflows
 * @example
 * ```tsx
 * // This component is automatically used by Next.js App Router
 * // as the root layout for all pages in the application
 *
 * // Example of how child pages are rendered within this layout:
 * export default function DashboardPage() {
 *   return (
 *     <div>
 *       <h1>ESG Dashboard</h1>
 *       <p>Complex ESG analysis content rendered within the root layout</p>
 *     </div>
 *   );
 * }
 *
 * // The complete rendered structure becomes:
 * // <html lang="en" className={GeistSans.className}>
 * //   <body>
 * //     <NuqsAdapter>
 * //       <NavigationProvider>
 * //         <div>
 * //           <h1>ESG Dashboard</h1>
 * //           <p>Complex ESG analysis content...</p>
 * //         </div>
 * //         <MobileBottomTabBar />
 * //       </NavigationProvider>
 * //     </NuqsAdapter>
 * //   </body>
 * // </html>
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { GeistSans } from 'geist/font/sans'
import './globals.css'
import { Inter } from 'next/font/google'
import { NavigationProvider } from '@/components/context/nav/nav-context'
import React from 'react'
import { MobileBottomTabBar } from '@/components/mobile-bottom-tab-bar'
import { NuqsAdapter } from 'nuqs/adapters/next/app'

const inter = Inter({
    subsets: ['latin'],
    display: 'swap',
})
const defaultUrl = process.env.VERCEL_URL
    ? `https://${process.env.VERCEL_URL}`
    : "http://localhost:3000";

export const metadata = {
    metadataBase: new URL(defaultUrl),
    title: "ekoIntelligence - shining a light on investments",
    description: "ekoIntelligence provides actionable analytics of companie in a more nuanced and transparent way to help you assess their sustainability and fairness.",
};

export default async function RootLayout({
                                             children,
                                         }: {
    children: React.ReactNode;
}) {
    return (
        <html lang="en" className={GeistSans.className}>
        <body>
        <NuqsAdapter>
            <NavigationProvider>
                    {children}
                <MobileBottomTabBar />
            </NavigationProvider>
        </NuqsAdapter>
        </body>
        </html>
    );
}
