import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { createClient } from '@/app/supabase/server'

/**
 * Next.js App Router API Route Handler for TipTap Collaborative Editor Authentication
 * 
 * This API endpoint provides JWT-based authentication and authorization for TipTap 
 * collaborative editor sessions within the EkoIntelligence ESG platform's document 
 * management system. The route generates secure JSON Web Tokens that enable real-time 
 * collaborative editing with granular document-level permissions and user presence 
 * tracking for multi-user document editing workflows.
 *
 * ## Core Functionality
 * - **JWT Token Generation**: Creates signed JWT tokens for TipTap Hocuspocus server authentication with configurable expiration (1 hour)
 * - **Document-Level Permissions**: Validates user access to specific documents through database ownership checks and permission inheritance
 * - **Collaborative Session Management**: Provides user metadata for real-time presence tracking and collaborative editor user identification
 * - **Security Integration**: Leverages Supabase Row Level Security (RLS) policies for secure document access control
 * - **Multi-Permission Support**: <PERSON>les read-only, write, and admin-level document access with appropriate token claims
 * - **Owner-Based Access Control**: Automatically grants full access to document creators while respecting shared permissions
 *
 * ## TipTap Collaboration Integration
 * This route integrates with TipTap's Hocuspocus collaboration server which requires JWT authentication:
 * - **Real-Time Editing**: Enables simultaneous multi-user document editing with conflict-free collaborative resolution
 * - **User Presence**: Provides user information for displaying collaborator avatars, cursors, and selection indicators
 * - **Document Namespacing**: Uses document IDs as collaboration namespaces for session isolation and permission enforcement
 * - **Session Security**: JWT tokens ensure only authorized users can join collaborative editing sessions
 *
 * ## Request Structure
 * - **HTTP Method**: POST (primary authentication endpoint for collaborative sessions)
 * - **Content-Type**: application/json
 * - **Authentication**: Requires valid Supabase session with authenticated user
 * - **Body Parameters**:
 *   - `documentId` (required, string): UUID of the document for collaborative editing session, must exist in `doc_documents` table
 *   - `permissions` (optional, string): Requested permission level - 'read' for view-only, 'write' for full editing (default: 'write')
 *
 * ## Response Formats
 * 
 * ### Success Response (200)
 * ```json
 * {
 *   "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
 *   "user": {
 *     "id": "550e8400-e29b-41d4-a716-************",
 *     "name": "John Smith",
 *     "email": "<EMAIL>",
 *     "avatar": "https://avatar-url.com/image.jpg"
 *   },
 *   "permissions": {
 *     "allowedDocumentNames": ["doc-uuid-123"],
 *     "readonlyDocumentNames": []
 *   }
 * }
 * ```
 *
 * ### Error Responses
 * ```json
 * // 401 Unauthorized - Missing or invalid Supabase authentication
 * {
 *   "error": "Unauthorized"
 * }
 * 
 * // 400 Bad Request - Missing required documentId parameter
 * {
 *   "error": "Document ID is required"
 * }
 * 
 * // 500 Internal Server Error - Missing TIPTAP_SECRET environment variable
 * {
 *   "error": "Server configuration error"
 * }
 * 
 * // 500 Internal Server Error - JWT signing or database operation failed
 * {
 *   "error": "Failed to generate authentication token",
 *   "details": "Specific error message",
 *   "timestamp": "2025-07-23T10:30:00.000Z"
 * }
 * ```
 *
 * ## JWT Token Structure
 * Generated tokens include TipTap-compatible claims and user metadata:
 * ```json
 * {
 *   "sub": "user-uuid",
 *   "allowedDocumentNames": ["document-uuid"],
 *   "readonlyDocumentNames": [],
 *   "user": {
 *     "id": "user-uuid",
 *     "name": "Display Name",
 *     "email": "<EMAIL>",
 *     "avatar": "avatar-url"
 *   },
 *   "exp": 1642694400
 * }
 * ```
 *
 * ## Permission Logic & Access Control
 * Document access follows a hierarchical permission model:
 * - **Document Ownership**: Document creators (`created_by` matches user ID) automatically receive full write access
 * - **Explicit Permissions**: Non-owners receive access based on requested permission level ('read' or 'write')
 * - **New Document Creation**: Authenticated users can create new documents by requesting tokens for non-existent document IDs
 * - **Database Security**: Supabase RLS policies provide additional security layer preventing unauthorized access
 * - **Token Scope**: Each token is scoped to a specific document ID preventing cross-document access
 *
 * ## Database Integration
 * Operates on the `doc_documents` table in the customer database:
 * - **Primary Key**: `id` (UUID) - Document identifier used as collaboration namespace
 * - **Ownership Field**: `created_by` (UUID) - Links to `profiles.id` for ownership verification
 * - **Content Storage**: `content` (text), `data` (jsonb) - Document content and TipTap JSON structure
 * - **Access Control**: Row Level Security policies enforce user-level document access restrictions
 * - **Entity Linking**: `entity_id` (text), `run_id` (integer) - ESG entity association for contextual access
 *
 * ## Environment Configuration
 * - **TIPTAP_SECRET**: Required environment variable for JWT signing, must be cryptographically secure random string
 * - **NEXT_PUBLIC_SUPABASE_URL**: Supabase project URL for database connectivity
 * - **NEXT_PUBLIC_SUPABASE_ANON_KEY**: Supabase anonymous key for client authentication
 *
 * ## System Architecture Context
 * This route enables collaborative editing within the broader EkoIntelligence platform:
 * - **ESG Document Workflows**: Collaborative creation of ESG reports, analysis documents, and entity assessments
 * - **Multi-User Collaboration**: Real-time editing sessions for teams working on sustainability reporting
 * - **Document Lifecycle**: Integration with version control, auto-save, and document publishing workflows
 * - **Security Framework**: Consistent authentication with other platform APIs and database access patterns
 * - **Real-Time Infrastructure**: WebSocket-based collaboration through TipTap Hocuspocus server integration
 *
 * ## Security Considerations
 * - **JWT Expiration**: Tokens automatically expire after 1 hour requiring re-authentication for extended sessions
 * - **Secret Management**: JWT signing secret must be securely stored and rotated periodically in production
 * - **Input Validation**: All request parameters are validated before processing to prevent injection attacks
 * - **Error Information**: Error responses avoid exposing sensitive system information while providing debugging context
 * - **Database Security**: Multi-layer security through Supabase authentication + RLS policies + application-level checks
 *
 * ## GET Method - Testing & Debugging Endpoint
 * The GET method provides a convenient testing interface that accepts `documentId` as a query parameter
 * and delegates to the POST method with default 'write' permissions. This endpoint simplifies development
 * and debugging workflows while maintaining the same security and authentication requirements.
 * 
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://tiptap.dev/collaboration TipTap Collaborative Editing
 * @see https://github.com/ueberdosis/hocuspocus Hocuspocus Collaboration Server
 * @see https://jwt.io/introduction JSON Web Token Introduction
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security
 * <AUTHOR>
 * @updated 2025-07-23
 * @description JWT authentication endpoint for TipTap collaborative editor sessions with document-level permissions and user presence tracking
 * @example
 * ```typescript
 * // POST request for collaborative editing authentication
 * const response = await fetch('/api/tiptap/auth', {
 *   method: 'POST',
 *   headers: { 'Content-Type': 'application/json' },
 *   body: JSON.stringify({
 *     documentId: '550e8400-e29b-41d4-a716-************',
 *     permissions: 'write'
 *   })
 * });
 * 
 * const { token, user, permissions } = await response.json();
 * 
 * // Use token with TipTap collaboration provider
 * const provider = new HocuspocusProvider({
 *   url: 'ws://localhost:1234',
 *   name: documentId,
 *   token: token
 * });
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get the request body
    const body = await request.json()
    const { documentId, permissions = 'write' } = body

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Get TipTap secret from environment
    const tiptapSecret = process.env.TIPTAP_SECRET
    if (!tiptapSecret) {
      console.error('TIPTAP_SECRET environment variable is not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    // Check if user has access to the document in Supabase
    const { data: document, error: docError } = await supabase
      .from('doc_documents')
      .select('id, created_by')
      .eq('id', documentId)
      .single()

    // Determine user permissions
    let allowedDocumentNames: string[] = []
    let readonlyDocumentNames: string[] = []

    if (document) {
      // Document exists, check permissions
      const isOwner = document.created_by === user.id

      if (isOwner || permissions === 'write') {
        allowedDocumentNames = [documentId]
      } else if (permissions === 'read') {
        readonlyDocumentNames = [documentId]
      }
    } else {
      // Document doesn't exist, allow creation if user is authenticated
      allowedDocumentNames = [documentId]
    }

    // Create JWT payload according to TipTap documentation
    const payload = {
      sub: user.id, // User identifier
      allowedDocumentNames,
      readonlyDocumentNames,
      // Optional: Add user metadata for collaboration features
      user: {
        id: user.id,
        name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
        email: user.email || '',
        avatar: user.user_metadata?.avatar_url,
      },
      // Optional: Add expiration time (1 hour)
      exp: Math.floor(Date.now() / 1000) + (60 * 60),
    }

    // Generate JWT token
    const token = jwt.sign(payload, tiptapSecret)

    return NextResponse.json({
      token,
      user: payload.user,
      permissions: {
        allowedDocumentNames,
        readonlyDocumentNames,
      }
    })

  } catch (error) {
    console.error('Error generating TipTap JWT:', error)

    // Provide more specific error information
    const errorMessage = error instanceof Error ? error.message : 'Internal server error'

    return NextResponse.json(
      {
        error: 'Failed to generate authentication token',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// GET method for testing/debugging (optional)
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const documentId = searchParams.get('documentId')

  if (!documentId) {
    return NextResponse.json(
      { error: 'Document ID is required' },
      { status: 400 }
    )
  }

  // Call the POST method with default permissions
  const mockRequest = new NextRequest(request.url, {
    method: 'POST',
    body: JSON.stringify({ documentId, permissions: 'write' }),
    headers: {
      'Content-Type': 'application/json',
      ...Object.fromEntries(request.headers.entries())
    }
  })

  return POST(mockRequest)
}
