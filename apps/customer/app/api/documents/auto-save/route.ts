import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/app/supabase/server'

/**
 * Next.js App Router API Route Handler for Document Auto-Save During Navigation Events
 * 
 * This API endpoint provides reliable document auto-saving functionality specifically designed 
 * for handling save operations during user navigation events within the EkoIntelligence ESG 
 * platform's collaborative document editor. The route is optimized for use with the 
 * `navigator.sendBeacon()` API to ensure document persistence even when users navigate 
 * away from pages or close browser tabs unexpectedly.
 *
 * ## Core Functionality
 * - **Navigation-Aware Auto-Save**: Handles document saving triggered by page navigation, tab closure, and browser back/forward events
 * - **Beacon API Optimized**: Specifically designed to work with `navigator.sendBeacon()` for reliable saves during page unload
 * - **Document Ownership Verification**: Validates user permissions before allowing document modifications through Supabase RLS policies
 * - **Collaborative Editor Integration**: Works seamlessly with TipTap collaborative editor and real-time document synchronization
 * - **Conflict-Free Operations**: Ensures data integrity during concurrent editing sessions and navigation-triggered saves
 * - **Timestamp Management**: Maintains accurate `updated_at` and `updated_by` metadata for document version tracking
 *
 * ## Navigation Event Integration
 * This route is specifically designed to work with the `useNavigationAutoSave` hook which monitors:
 * - **Next.js Navigation**: App Router route changes via `usePathname` and `useSearchParams`
 * - **Browser Navigation**: Back/forward button usage and tab close events via `beforeunload`
 * - **Emergency Saves**: Critical document preservation during unexpected navigation
 *
 * ## Request Structure
 * - **HTTP Method**: POST (required for document modification operations)
 * - **Content-Type**: application/json
 * - **Authentication**: Requires valid Supabase session with authenticated user
 * - **Body Parameters**:
 *   - `documentId` (required, string): UUID of the document to save, must exist in `doc_documents` table
 *   - `content` (required, string): Updated document content in TipTap JSON format
 *   - `data` (optional, object): Additional document metadata and configuration data
 *   - `updated_at` (optional, string): ISO timestamp for the update operation (defaults to current time)
 *   - `updated_by` (optional, string): UUID of the user making the update (defaults to authenticated user)
 *
 * ## Response Formats
 * 
 * ### Success Response (200)
 * ```json
 * {
 *   "success": true,
 *   "message": "Document auto-saved successfully"
 * }
 * ```
 *
 * ### Error Responses
 * ```json
 * // 401 Unauthorized - Missing or invalid authentication
 * {
 *   "error": "Unauthorized"
 * }
 * 
 * // 400 Bad Request - Missing required parameters
 * {
 *   "error": "Missing required fields"
 * }
 * 
 * // 404 Not Found - Document does not exist or user lacks access
 * {
 *   "error": "Document not found"
 * }
 * 
 * // 500 Internal Server Error - Database operation failed
 * {
 *   "error": "Failed to save document"
 * }
 * ```
 *
 * ## Database Integration
 * Operates on the `doc_documents` table in the customer database with the following schema:
 * - **Primary Key**: `id` (UUID) - Unique document identifier
 * - **Content Fields**: `content` (text), `data` (jsonb) - Document content and metadata
 * - **Audit Fields**: `created_at`, `updated_at`, `created_by`, `updated_by` - Change tracking
 * - **Entity Linking**: `entity_id` (text), `run_id` (integer) - ESG entity association
 * - **Access Control**: `is_public` (boolean) - Public/private document visibility
 *
 * ## Security & Permissions
 * - **Row Level Security (RLS)**: Enforced through Supabase policies ensuring users can only modify their own documents
 * - **Document Ownership**: Validates `created_by` field matches authenticated user before allowing updates
 * - **Access Verification**: Performs document existence and permission check before attempting updates
 * - **Input Validation**: Validates required fields and sanitizes input data before database operations
 * - **Error Logging**: Comprehensive error logging for debugging while protecting sensitive information
 *
 * ## System Architecture Context
 * This route fits into the broader EkoIntelligence document management system:
 * - **Frontend Editor**: TipTap-based collaborative editor with real-time synchronization
 * - **Auto-Save Layer**: Multi-layered auto-save system (time-based, content-change, navigation-triggered)
 * - **Database Layer**: Supabase PostgreSQL with RLS policies for secure multi-tenant document access
 * - **ESG Integration**: Documents linked to specific ESG entities and analysis runs for contextual reporting
 * - **Collaboration System**: Real-time collaborative editing with conflict resolution and version history
 *
 * ## Performance Considerations
 * - **Beacon API Compatibility**: Designed for reliable operation during page unload scenarios
 * - **Minimal Payload**: Focused on essential document data to reduce network overhead during navigation
 * - **Database Optimization**: Uses indexed fields for fast document lookup and update operations
 * - **Error Recovery**: Graceful error handling prevents UI blocking during navigation events
 * - **Concurrent Safety**: Database triggers ensure consistent `updated_at` timestamps during concurrent saves
 *
 * ## Related Components
 * - Navigation Auto-Save Hook (`useNavigationAutoSave.ts`) - Monitors navigation events and triggers saves
 * - Document Editor (`EkoDocumentEditor.tsx`) - Main collaborative editor component with auto-save integration
 * - Supabase Client (`server.ts`) - Server-side Supabase client with cookie-based authentication
 * - Document Management System - Broader document lifecycle and permission management
 * 
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security
 * @see https://developer.mozilla.org/en-US/docs/Web/API/Navigator/sendBeacon Navigator.sendBeacon API
 * @see https://tiptap.dev/collaboration TipTap Collaborative Editing
 * <AUTHOR>
 * @updated 2025-07-23
 * @description API endpoint for handling auto-save requests during navigation events, specifically designed to work with navigator.sendBeacon() for reliable saving during page unload events.
 * @example
 * ```typescript
 * // Using with navigator.sendBeacon during page unload
 * const saveData = JSON.stringify({
 *   documentId: "550e8400-e29b-41d4-a716-************",
 *   content: JSON.stringify(editorContent),
 *   data: { lastEdit: "navigation" }
 * });
 * 
 * navigator.sendBeacon('/api/documents/auto-save', saveData);
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse the request body
    const body = await request.json()
    const { documentId, content, data, updated_at, updated_by } = body

    if (!documentId || !content) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify the user owns or has access to this document
    const { data: document, error: fetchError } = await supabase
      .from('doc_documents')
      .select('id, created_by')
      .eq('id', documentId)
      .single()

    if (fetchError || !document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }

    // Update the document
    const { error: updateError } = await supabase
      .from('doc_documents')
      .update({
        content,
        data,
        updated_at: updated_at || new Date().toISOString(),
        updated_by: updated_by || user.id,
      })
      .eq('id', documentId)

    if (updateError) {
      console.error('Auto-save update error:', updateError)
      return NextResponse.json(
        { error: 'Failed to save document' },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { success: true, message: 'Document auto-saved successfully' },
      { status: 200 }
    )
  } catch (error) {
    console.error('Auto-save API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
