/**
 * Next.js App Router API Route Handler for AI-Powered Content Summarization
 * 
 * This API endpoint provides intelligent text summarization capabilities for the EkoIntelligence ESG 
 * platform, enabling users to condense large volumes of content into concise, well-structured summaries. 
 * The route leverages Google's Gemini AI models with sophisticated validation, caching, and retry 
 * mechanisms to ensure reliable content processing and optimal performance for ESG analysis workflows.
 *
 * ## Core Functionality
 * - **Intelligent Text Summarization**: Uses Google Gemini AI models (Flash/Flash Lite) to generate professional, comprehensive summaries
 * - **Content Length Optimization**: Automatically selects appropriate model based on content size (Flash Lite for >50k chars, Flash for smaller content)
 * - **Validation Framework**: Built-in response validation with configurable retry mechanisms and quality assurance
 * - **Caching System**: Vercel KV-based caching with content hash keys to reduce API costs and improve response times
 * - **Citation Preservation**: Maintains original citations in [^citation_id] format throughout the summarization process
 * - **Professional Formatting**: Generates structured summaries suitable for ESG reporting and business documentation
 * - **Content Truncation**: Safely handles oversized content with intelligent truncation at 200,000 character limit
 * - **Quality Enforcement**: Enforces summary length constraints (100-5000 characters) and content quality standards
 *
 * ## Request Structure
 * - **HTTP Method**: POST (required for content processing operations)
 * - **Content-Type**: application/json
 * - **Timeout**: 180 seconds maximum execution time for complex content processing
 * - **Body Parameters**:
 *   - `content` (required, string): Source text content to be summarized, automatically truncated if exceeding 200,000 characters
 *   - `prompt` (optional, string): Additional context or specific summarization instructions to guide the AI
 *   - `title` (optional, string): Document title or context information to enhance summarization quality
 *
 * ## Response Format
 * 
 * ### Success Response (200)
 * ```json
 * {
 *   "text": "Professional, well-structured summary preserving key points and citations [^1234]",
 *   "metadata": {
 *     "type": "summary",
 *     "originalContentLength": 15000,
 *     "summaryLength": 450,
 *     "title": "Q4 ESG Report Summary",
 *     "prompt": "Focus on environmental impact metrics",
 *     "generatedAt": "2025-07-23T10:30:45.123Z"
 *   }
 * }
 * ```
 *
 * ### Error Responses
 * ```json
 * // 400 Bad Request - Missing or invalid content
 * {
 *   "error": "Missing required parameter: content"
 * }
 * 
 * // 400 Bad Request - Invalid content type
 * {
 *   "error": "Content must be a string"
 * }
 * 
 * // 500 Internal Server Error - Processing failure
 * {
 *   "error": "Failed to generate summary",
 *   "message": "Detailed error information"
 * }
 * ```
 *
 * ## AI Model Selection & Processing
 * The route implements intelligent model selection based on content characteristics:
 * - **Gemini Flash Lite**: Selected for content >50,000 characters for cost optimization
 * - **Gemini Flash**: Default model for standard content processing with better quality
 * - **Validation Pipeline**: Response validation with configurable retry attempts (default: 2)
 * - **Quality Enforcement**: Automatic validation of summary length (100-5000 characters)
 * - **Fallback Handling**: Graceful degradation with `acceptFinalEvalFail` for edge cases
 *
 * ## Summarization Guidelines
 * The AI follows strict professional summarization standards:
 * 1. **Comprehensive Coverage**: Captures key points and main themes from source content
 * 2. **Citation Preservation**: Maintains all original citations in [^citation_id] format
 * 3. **Professional Tone**: Uses clear, professional language suitable for business contexts
 * 4. **Structure Enforcement**: No headings, focused paragraphs, maximum 500 words
 * 5. **Context Retention**: Preserves important details and contextual information
 * 6. **ESG Focus**: Optimized for environmental, social, and governance content analysis
 *
 * ## Performance & Caching
 * - **Response Caching**: Public cache with 30-minute TTL and 1-hour stale-while-revalidate
 * - **Content-Based Cache Keys**: MD5 hash of request body ensures accurate cache hits
 * - **Vercel KV Integration**: Distributed caching system for improved global performance
 * - **Cache Prefix**: 'validated-report' namespace for organized cache management
 * - **Cost Optimization**: Caching reduces Gemini API calls and associated costs
 *
 * ## System Integration
 * This route integrates with the broader EkoIntelligence ESG analysis system:
 * - **Document Editor**: Powers summarization features in TipTap collaborative editor
 * - **Report Generation**: Supports AI-powered content generation workflows
 * - **ESG Analysis Pipeline**: Provides summary capabilities for entity analysis and reporting
 * - **Content Management**: Enables efficient processing of large ESG documents and reports
 * - **Citation Management**: Works with the platform's citation tracking and reference system
 *
 * ## Security & Validation
 * - **Input Sanitization**: Validates content type and structure before processing
 * - **Content Limits**: Enforces maximum content size to prevent abuse
 * - **Rate Limiting**: Built into validated LLM system for API protection
 * - **Error Handling**: Comprehensive error catching with detailed logging
 * - **Response Validation**: Multi-layer validation ensures output quality and format compliance
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation
 * @see https://vercel.com/docs/storage/vercel-kv Vercel KV Storage Documentation
 * @see {@link ./validated-llms.ts} Validated LLM System
 * @see {@link ./report-common.ts} ESG Report Common Instructions
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This API endpoint provides intelligent text summarization capabilities for the EkoIntelligence ESG platform, enabling users to condense large volumes of content into concise, well-structured summaries.
 * @example ```bash
curl -X POST 'http://localhost:3000/api/report/summarize' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": "Long ESG report content with citations [^1234]...",
    "title": "Q4 Environmental Impact Report",
    "prompt": "Focus on carbon emission metrics and sustainability initiatives"
  }'
```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import { NextRequest } from 'next/server'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { callValidatedLLMs, LLMModel, LLMOptions, Message } from '@/app/api/report/validated-llms'
import crypto from 'crypto'

export const maxDuration = 180;

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { content, prompt, title } = body

    //Body hash
    const bodyHash = crypto.createHash('md5').update(JSON.stringify(body)).digest('hex')


    if (!content) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameter: content' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Validate content is a string
    if (typeof content !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Content must be a string' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Truncate content if too long (to prevent token limit issues)
    const maxContentLength = 200000 // Adjust based on your needs
    const truncatedContent = content.length > maxContentLength 
      ? content.substring(0, maxContentLength) + '...[truncated]'
      : content

    console.log(`[API] /api/report/summarize called with:`, {
      contentLength: content.length,
      truncatedLength: truncatedContent.length,
      title: title || 'No title',
      hasPrompt: !!prompt
    })

    // Build the prompt for summarization
    const summaryPrompt = `
      <instructions>
      You are tasked with creating a comprehensive summary of the provided content.
      
      ${title ? `The summary is for: "${title}"` : ''}
      ${prompt ? `Additional context: ${prompt}` : ''}
      
      Please provide a well-structured summary that:
      1. Captures the key points and main themes
      2. Maintains important details and context
      3. Preserves any citations in the format [^citation_id]
      4. Is written in clear, professional language
      5. Has no headings
      6. Is no longer than 500 words
      
      The summary should be comprehensive but concise, focusing on the most important information.
      
      ${COMMON_INSTRUCTIONS}
      
      Content to summarize:
      ${truncatedContent}

      DO NOT INCLUDE TABLES OR GRAPHS IN SUMMARIES
      </instructions>
    `;
    const validator = (response: string | null) => {
      if (!response) return 'Failed to generate summary'
      if (response.length < 100) return 'Summary is too short'
      if (response.length > 5000) return 'Summary is too long at '+response.length+' characters'
      return true
    }

    // Generate summary using validated LLM call (inlined)
    const endpoint = '/api/report/summarize'
    const entityName = title || 'Summary'
    const retries = 2

    console.log(`[API] ${endpoint} called for ${entityName} with validation`)

    const messages: Message[] = [{ role: 'user', content: summaryPrompt }]

    const llmOptions: LLMOptions = {
      eval: validator,
      evalRetry: retries,
      cacheKey: `v1-${endpoint}-${bodyHash}`,
      cachePrefix: 'validated-report',
      maxOutputTokens: 16000,
      escalateTo: [LLMModel.GEMINI_FLASH], // Fallback to pro model if flash fails
      appendOnEvalFail: true,
      evalRetryMessage: 'The response should be between 50 and 400 words.',
      acceptFinalEvalFail: true
    }

    const summaryText = await callValidatedLLMs([content.length > 50_000 ? LLMModel.GEMINI_FLASH_LITE : LLMModel.GEMINI_FLASH ], messages, llmOptions)

    if (!summaryText) {
      throw new Error(`Failed to generate content for ${endpoint}`)
    }

    console.log(`[API] ${endpoint} completed for ${entityName}, length: ${summaryText.length} chars`)

    // Return the summary (citations are managed by EkoDocumentEditor)
    const response = {
      text: summaryText,
      metadata: {
        type: 'summary',
        originalContentLength: content.length,
        summaryLength: summaryText.length,
        title: title || null,
        prompt: prompt || null,
        generatedAt: new Date().toISOString(),
      }
    };

    return new Response(JSON.stringify(response), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=1800, stale-while-revalidate=3600'
      }
    });

  } catch (error) {
    console.error('[API] /api/report/summarize error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate summary', 
        message: error instanceof Error ? error.message : String(error) 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
