/**
 * LLM Tool Library for Chart Generation in ESG Analysis Platform
 *
 * This module provides a comprehensive suite of AI-powered chart generation tools designed
 * specifically for the EkoIntelligence ESG (Environmental, Social, Governance) analysis platform.
 * The library serves as a bridge between AI-generated analysis content and visual data representation,
 * enabling Large Language Models (LLMs) to dynamically create interactive charts during report generation.
 *
 * ## Core Architecture
 * **Tool-Based Framework**: Built on the validated LLM framework, each chart type is exposed as a
 * structured tool with well-defined parameters and implementations. This allows AI models to:
 * - Select appropriate visualization types based on data characteristics
 * - Generate charts with proper configurations and styling
 * - Validate data requirements before chart creation
 * - Handle errors gracefully with descriptive feedback
 *
 * **Base64 Encoding Strategy**: Charts are serialized as base64-encoded JSON strings within special
 * `<chart>` HTML elements. This approach enables:
 * - Seamless embedding within AI-generated HTML content
 * - Preservation of chart data and configuration during content processing
 * - Client-side reconstruction using React/Recharts components
 * - Version-safe serialization of complex chart specifications
 *
 * ## Supported Chart Types
 * **Area Charts**: Ideal for showing trends over time with filled regions beneath lines.
 * Best for cumulative data, volume visualization, and part-to-whole relationships over continuous ranges.
 *
 * **Bar Charts**: Perfect for categorical comparisons with discrete values.
 * Excels at showing rankings, survey results, and side-by-side comparisons across categories.
 *
 * **Line Charts**: Optimized for trend analysis and time series data.
 * Essential for tracking performance metrics, showing correlations, and displaying continuous data patterns.
 *
 * **Pie Charts**: Designed for proportional data and percentage breakdowns.
 * Effective for market share visualization, budget distributions, and categorical data where parts sum to 100%.
 *
 * **Radar Charts**: Multi-dimensional comparison across quantitative variables.
 * Ideal for performance scorecards, skill assessments, and entity comparison profiles.
 *
 * **Radial Charts**: Progress indicators and goal achievement visualization.
 * Perfect for completion rates, KPI tracking, and circular progress displays.
 *
 * ## Integration with ESG Analysis System
 * **AI Report Generation**: Charts are created during AI-powered report generation through Google
 * Gemini Pro models with function calling capabilities. The system:
 * - Analyzes ESG data patterns and determines optimal visualization approaches
 * - Generates charts with contextually appropriate titles and descriptions
 * - Applies consistent color schemes and styling based on ESG data categories
 * - Validates data completeness and chart parameter requirements
 *
 * **Client-Side Rendering**: Generated chart data is decoded and rendered using:
 * - **Recharts Library**: Modern React charting library built on D3.js for interactive visualizations
 * - **ShadCN Chart Components**: Styled chart components providing consistent theming and responsive design
 * - **Glass-morphism UI**: Platform-consistent translucent styling with backdrop blur effects
 * - **Responsive Containers**: Automatic chart resizing for different screen sizes and viewing contexts
 *
 * ## Data Processing Pipeline
 * **Input Validation**: Each chart tool performs comprehensive validation of:
 * - Data array completeness and structure consistency
 * - Required key presence in data objects (xAxisKey, yAxisKeys, nameKey, valueKey)
 * - Numeric value verification for quantitative data
 * - Array length validation for multi-series charts
 *
 * **Configuration Generation**: Auto-generates chart styling when custom config is not provided:
 * - **Color Assignment**: HSL color space with 60-degree hue shifts for optimal contrast
 * - **Label Generation**: Automatic title-case conversion of data keys for display labels
 * - **Theme Support**: Dual theme configuration with light/dark mode color variants
 * - **Accessibility**: Color choices optimized for visibility and contrast requirements
 *
 * **Error Handling**: Robust error management with descriptive messages for:
 * - Missing or malformed data arrays
 * - Invalid key references in chart configuration
 * - Type mismatches in numeric data fields
 * - Chart-specific parameter validation failures
 *
 * ## Usage in ESG Context
 * **Environmental Data**: Visualizing carbon emissions, energy consumption, waste metrics over time
 * **Social Impact**: Displaying diversity statistics, employee satisfaction scores, community investment
 * **Governance Metrics**: Showing board composition, compliance scores, risk assessments
 * **Comparative Analysis**: Multi-entity performance comparisons across ESG dimensions
 * **Trend Analysis**: Historical ESG performance tracking and future projection visualization
 * **Impact Assessment**: Positive vs negative impact distributions across categories
 *
 * ## Technical Architecture
 * **Base64 Serialization**: Charts are encoded as `Buffer.from(JSON.stringify(chartData)).toString('base64')`
 * and embedded in HTML as `<chart>base64:${encodedData}</chart>` or `<chart data-json="${encodedData}"></chart>`
 *
 * **Type Safety**: Full TypeScript support with strongly-typed interfaces for:
 * - `ChartData`: Core chart data structure with type, data, and configuration
 * - `ChartConfig`: Styling and theming configuration with nested color/label definitions
 * - Tool parameter validation through JSON Schema definitions
 *
 * **Performance Optimization**: 
 * - Minimal data transformation during chart generation
 * - Efficient color generation algorithms using mathematical hue shifts
 * - Lazy evaluation of chart configuration with caching-friendly serialization
 *
 * ## Integration Points
 * **API Layer**: Used in Next.js API routes for ESG report generation:
 * - `/api/report/entity/[entityId]/[runId]/harm/model/[model]/section/[modelSection]/route.ts`
 * - LLM function calling through Google Gemini Pro models
 * - Validation pipeline ensuring chart correctness before client delivery
 *
 * **Client Components**: Chart data decoded and rendered by:
 * - `chart-extension.tsx`: TipTap editor extension for chart display and editing
 * - Recharts-based rendering with ShadCN theming
 * - Real-time chart updates during collaborative editing sessions
 *
 * ## Security & Validation
 * **Input Sanitization**: All chart data undergoes validation before processing
 * **JSON Repair**: Utilizes `jsonrepair` library for malformed data recovery
 * **Schema Validation**: JSON Schema-based parameter validation for all tools
 * **Error Boundaries**: Graceful degradation when chart rendering fails
 *
 * @see https://recharts.org/en-US/api Recharts API Documentation  
 * @see https://ui.shadcn.com/docs/components/chart ShadCN Chart Components
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see {@link ./validated-llms.ts} LLM Tool Framework and Function Calling
 * @see {@link ../../components/editor/extensions/chart-extension.tsx} Client-side Chart Rendering
 * <AUTHOR>
 * @updated 2025-07-23
 * @description LLM tool library providing AI-powered chart generation capabilities for ESG analysis reports with support for area, bar, line, pie, radar, and radial chart types.
 * @example ```typescript
 * // Usage in LLM function calling
 * const tools = chartTools;
 * const result = await callValidatedLLMsWithTools([LLMModel.GEMINI_PRO], messages, tools);
 * 
 * // Generated chart HTML:
 * // <chart>base64:eyJ0eXBlIjoiYmFyIiwidGl0bGUiOiJFU0cgU2NvcmVzIiwiZGF0YSI6W3sibmFtZSI6IkVudmlyb25tZW50YWwiLCJzY29yZSI6ODV9XSwiY29uZmlnIjp7InNjb3JlIjp7ImxhYmVsIjoiU2NvcmUiLCJjb2xvciI6ImhzbCgwLCA3MCUsIDUwJSkifX19</chart>
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { Tool } from './validated-llms'

// Chart type definitions
export interface ChartData {
  type: 'area' | 'bar' | 'line' | 'pie' | 'radar' | 'radial'
  title?: string
  description?: string
  data: Record<string, any>[]
  config: ChartConfig
}

export interface ChartConfig {
  [key: string]: {
    label?: string
    color?: string
    theme?: {
      light: string
      dark: string
    }
  }
}

// Chart tool implementations
async function createAreaChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  xAxisKey: string
  yAxisKeys: string[]
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, xAxisKey, yAxisKeys, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!xAxisKey) {
    throw new Error('xAxisKey is required')
  }
  if (!yAxisKeys || !Array.isArray(yAxisKeys) || yAxisKeys.length === 0) {
    throw new Error('yAxisKeys must be a non-empty array')
  }
  
  // Auto-generate config for any missing keys
  const chartConfig: ChartConfig = { ...config }
  yAxisKeys.forEach((key, index) => {
    if (!chartConfig[key]) {
      const hue = (index * 60) % 360
      chartConfig[key] = {
        label: key.charAt(0).toUpperCase() + key.slice(1),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'area',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart>base64:${encodedData}</chart>`
}

async function createBarChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  xAxisKey: string
  yAxisKeys: string[]
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, xAxisKey, yAxisKeys, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!xAxisKey) {
    throw new Error('xAxisKey is required')
  }
  if (!yAxisKeys || !Array.isArray(yAxisKeys) || yAxisKeys.length === 0) {
    throw new Error('yAxisKeys must be a non-empty array')
  }
  
  // Auto-generate config for any missing keys
  const chartConfig: ChartConfig = { ...config }
  yAxisKeys.forEach((key, index) => {
    if (!chartConfig[key]) {
      const hue = (index * 60) % 360
      chartConfig[key] = {
        label: key.charAt(0).toUpperCase() + key.slice(1),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'bar',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

async function createLineChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  xAxisKey: string
  yAxisKeys: string[]
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, xAxisKey, yAxisKeys, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!xAxisKey) {
    throw new Error('xAxisKey is required')
  }
  if (!yAxisKeys || !Array.isArray(yAxisKeys) || yAxisKeys.length === 0) {
    throw new Error('yAxisKeys must be a non-empty array')
  }
  
  // Auto-generate config for any missing keys
  const chartConfig: ChartConfig = { ...config }
  yAxisKeys.forEach((key, index) => {
    if (!chartConfig[key]) {
      const hue = (index * 60) % 360
      chartConfig[key] = {
        label: key.charAt(0).toUpperCase() + key.slice(1),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'line',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

async function createPieChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  nameKey: string
  valueKey: string
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, nameKey, valueKey, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!nameKey) {
    throw new Error('nameKey is required')
  }
  if (!valueKey) {
    throw new Error('valueKey is required')
  }
  
  // Auto-generate config for pie chart segments
  const chartConfig: ChartConfig = { ...config }
  data.forEach((item, index) => {
    const name = item[nameKey]
    if (name && !chartConfig[name]) {
      const hue = (index * 40) % 360
      chartConfig[name] = {
        label: String(name),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'pie',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

async function createRadarChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  angleKey: string
  radiusKeys: string[]
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, angleKey, radiusKeys, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!angleKey) {
    throw new Error('angleKey is required')
  }
  if (!radiusKeys || !Array.isArray(radiusKeys) || radiusKeys.length === 0) {
    throw new Error('radiusKeys must be a non-empty array')
  }
  
  // Auto-generate config for any missing keys
  const chartConfig: ChartConfig = { ...config }
  radiusKeys.forEach((key, index) => {
    if (!chartConfig[key]) {
      const hue = (index * 60) % 360
      chartConfig[key] = {
        label: key.charAt(0).toUpperCase() + key.slice(1),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'radar',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

async function createRadialChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  nameKey: string
  valueKey: string
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, nameKey, valueKey, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!nameKey) {
    throw new Error('nameKey is required')
  }
  if (!valueKey) {
    throw new Error('valueKey is required')
  }
  
  // Auto-generate config for radial chart segments
  const chartConfig: ChartConfig = { ...config }
  data.forEach((item, index) => {
    const name = item[nameKey]
    if (name && !chartConfig[name]) {
      const hue = (index * 40) % 360
      chartConfig[name] = {
        label: String(name),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'radial',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

// Export the chart tools
export const chartTools: Tool[] = [
  {
    name: 'create_area_chart',
    description: 'Create an area chart with multiple data series. Best for showing data trends over time or categories with filled areas under lines. Area charts are ideal for visualizing cumulative data, showing volume changes over time, or displaying part-to-whole relationships across continuous data.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title displayed at the top of the chart'
        },
        description: {
          type: 'string',
          description: 'Chart description or subtitle displayed below the title'
        },
        data: {
          type: 'array',
          description: 'Array of data objects where each object represents a data point. Each object must contain the xAxisKey property and all yAxisKeys properties. Example: [{"month": "Jan", "revenue": 1000, "profit": 200}, {"month": "Feb", "revenue": 1500, "profit": 300}]. All numeric values should be numbers, not strings. Missing values will be treated as 0.',
          items: {
            type: 'object'
          }
        },
        xAxisKey: {
          type: 'string',
          description: 'Property name in each data object that contains the x-axis values. This is typically categorical data like dates, months, categories, or labels. Example: "month", "year", "category", "date". The values can be strings or numbers though they are typically strings.'
        },
        yAxisKeys: {
          type: 'array',
          description: 'Array of property names in each data object that contain the y-axis numeric values. Each key will create a separate area series in the chart. Example: ["revenue", "profit", "costs"]. All values for these keys must be numeric. Multiple series will be stacked on top of each other.',
          items: {
            type: 'string'
          }
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration object. Each key should match a yAxisKey and contain styling options. Format: {"keyName": {"label": "Display Name", "color": "hsl(210, 70%, 50%)"}}. Colors can be hex codes, HSL, or RGB. If not provided, colors will be auto-generated using HSL values with good contrast.'
        }
      },
      required: ['data', 'xAxisKey', 'yAxisKeys']
    },
    implementation: createAreaChart
  },
  {
    name: 'create_bar_chart',
    description: 'Create a bar chart with multiple data series. Best for comparing discrete values across categories or groups. Bar charts are ideal for showing comparisons between different categories, ranking data, or displaying survey results. Multiple series appear as grouped bars side by side.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title displayed at the top of the chart'
        },
        description: {
          type: 'string',
          description: 'Chart description or subtitle displayed below the title'
        },
        data: {
          type: 'array',
          description: 'Array of data objects where each object represents a category or group. Each object must contain the xAxisKey property and all yAxisKeys properties. Example: [{"category": "Product A", "sales": 150, "profit": 45}, {"category": "Product B", "sales": 200, "profit": 60}]. All numeric values should be numbers, not strings. Missing values will be treated as 0.',
          items: {
            type: 'object'
          }
        },
        xAxisKey: {
          type: 'string',
          description: 'Property name in each data object that contains the x-axis category labels. This is typically categorical data like product names, company names, regions, or time periods. Example: "category", "company", "region", "year". The values are usually strings but can be numbers.'
        },
        yAxisKeys: {
          type: 'array',
          description: 'Array of property names in each data object that contain the y-axis numeric values. Each key will create a separate bar series grouped together. Example: ["sales", "profit", "costs"]. All values for these keys must be numeric. Multiple series will be grouped side by side for each category.',
          items: {
            type: 'string'
          }
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration object. Each key should match a yAxisKey and contain styling options. Format: {"keyName": {"label": "Display Name", "color": "hsl(210, 70%, 50%)"}}. Colors can be hex codes, HSL, or RGB. If not provided, colors will be auto-generated using HSL values with good contrast.'
        }
      },
      required: ['data', 'xAxisKey', 'yAxisKeys']
    },
    implementation: createBarChart
  },
  {
    name: 'create_line_chart',
    description: 'Create a line chart with multiple data series. Best for showing trends, patterns, and changes over time or continuous data. Line charts are ideal for time series data, tracking performance metrics over time, showing correlations between variables, or displaying continuous data trends. Multiple series appear as separate colored lines.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title displayed at the top of the chart'
        },
        description: {
          type: 'string',
          description: 'Chart description or subtitle displayed below the title'
        },
        data: {
          type: 'array',
          description: 'Array of data objects where each object represents a data point in time or sequence. Each object must contain the xAxisKey property and all yAxisKeys properties. Example: [{"date": "2024-01", "temperature": 22, "humidity": 65}, {"date": "2024-02", "temperature": 25, "humidity": 60}]. All numeric values should be numbers, not strings. Data should be ordered by xAxisKey for proper line progression.',
          items: {
            type: 'object'
          }
        },
        xAxisKey: {
          type: 'string',
          description: 'Property name in each data object that contains the x-axis values, typically representing time or sequence. Common examples: "date", "month", "quarter", "year", "timestamp", "week". Values can be strings (dates, labels) or numbers (years, indices) -  though they are typically strings. Data points will be connected in the order they appear in the array.'
        },
        yAxisKeys: {
          type: 'array',
          description: 'Array of property names in each data object that contain the y-axis numeric values. Each key will create a separate line series in the chart. Example: ["temperature", "humidity", "pressure"]. All values for these keys must be numeric. Multiple lines will be drawn on the same chart with different colors.',
          items: {
            type: 'string'
          }
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration object. Each key should match a yAxisKey and contain styling options. Format: {"keyName": {"label": "Display Name", "color": "hsl(210, 70%, 50%)"}}. Colors can be hex codes, HSL, or RGB. If not provided, colors will be auto-generated using HSL values with good contrast.'
        }
      },
      required: ['data', 'xAxisKey', 'yAxisKeys']
    },
    implementation: createLineChart
  },
  {
    name: 'create_pie_chart',
    description: 'Create a pie chart to show proportions and percentages of a whole. Best for displaying how individual parts contribute to the total, market share distributions, budget breakdowns, or categorical data where parts sum to 100%. Pie charts work best with 2-7 categories for readability.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title displayed at the top of the chart'
        },
        description: {
          type: 'string',
          description: 'Chart description or subtitle displayed below the title'
        },
        data: {
          type: 'array',
          description: 'Array of data objects where each object represents a segment or slice of the pie. Each object must contain both nameKey and valueKey properties. Example: [{"category": "Marketing", "budget": 5000}, {"category": "Development", "budget": 8000}, {"category": "Sales", "budget": 3000}]. Values should be positive numbers. The total of all values will represent 100% of the pie.',
          items: {
            type: 'object'
          }
        },
        nameKey: {
          type: 'string',
          description: 'Property name in each data object that contains the segment labels or names. This will be used for the legend and segment labels. Example: "category", "type", "department", "region". Values should be strings that clearly identify each segment.'
        },
        valueKey: {
          type: 'string',
          description: 'Property name in each data object that contains the numeric values for each segment. These values determine the size of each pie slice. Example: "value", "count", "amount", "budget", "sales". All values must be positive numbers. The relative size of each slice is calculated as (value / total) * 100%.'
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration object. Each key should match a value from the nameKey field and contain styling options. Format: {"segmentName": {"label": "Display Name", "color": "hsl(210, 70%, 50%)"}}. Colors can be hex codes, HSL, or RGB. If not provided, colors will be auto-generated using HSL values with good contrast.'
        }
      },
      required: ['data', 'nameKey', 'valueKey']
    },
    implementation: createPieChart
  },
  {
    name: 'create_radar_chart',
    description: 'Create a radar chart (spider/web chart) to display multivariate data across multiple quantitative variables. Best for comparing multiple metrics simultaneously across different entities, showing strengths/weaknesses profiles, performance scorecards, or skill assessments. Each spoke represents a different metric, and each polygon represents a different entity.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title displayed at the top of the chart'
        },
        description: {
          type: 'string',
          description: 'Chart description or subtitle displayed below the title'
        },
        data: {
          type: 'array',
          description: 'Array of data objects where each object represents a measurement point across multiple metrics. Each object must contain the angleKey property and all radiusKeys properties. Example: [{"metric": "Speed", "company_a": 8, "company_b": 6}, {"metric": "Quality", "company_a": 7, "company_b": 9}]. All numeric values should be numbers on the same scale (e.g., 0-10, 0-100).',
          items: {
            type: 'object'
          }
        },
        angleKey: {
          type: 'string',
          description: 'Property name in each data object that contains the axis/spoke labels around the radar. Each unique value creates a spoke emanating from the center. Example: "metric", "dimension", "skill", "category". Values should be strings that clearly identify each measurement dimension.'
        },
        radiusKeys: {
          type: 'array',
          description: 'Array of property names in each data object that contain the numeric values for each entity being compared. Each key creates a separate polygon on the radar. Example: ["company_a", "company_b", "team_1"]. All values should be numeric and ideally on the same scale for meaningful comparison. Each entity will be drawn as a colored polygon.',
          items: {
            type: 'string'
          }
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration object. Each key should match a radiusKey and contain styling options. Format: {"entityName": {"label": "Display Name", "color": "hsl(210, 70%, 50%)"}}. Colors can be hex codes, HSL, or RGB. If not provided, colors will be auto-generated using HSL values with good contrast.'
        }
      },
      required: ['data', 'angleKey', 'radiusKeys']
    },
    implementation: createRadarChart
  },
  {
    name: 'create_radial_chart',
    description: 'Create a radial chart (donut/progress/semi-circle chart) to show progress, completion rates, or goal achievement. Best for displaying progress toward targets, completion percentages, KPI achievements, or circular progress indicators. Similar to pie charts but with a hollow center, often used for single metric visualization or progress tracking.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title displayed at the top of the chart'
        },
        description: {
          type: 'string',
          description: 'Chart description or subtitle displayed below the title'
        },
        data: {
          type: 'array',
          description: 'Array of data objects where each object represents a segment or progress indicator. Each object must contain both nameKey and valueKey properties. Example: [{"phase": "Completed", "progress": 75}, {"phase": "Remaining", "progress": 25}] or [{"goal": "Sales Target", "achievement": 85}]. Values typically represent percentages or progress amounts.',
          items: {
            type: 'object'
          }
        },
        nameKey: {
          type: 'string',
          description: 'Property name in each data object that contains the segment labels or category names. This will be used for the legend and center label. Example: "category", "phase", "status", "goal", "metric". Values should be strings that clearly identify each segment or progress indicator.'
        },
        valueKey: {
          type: 'string',
          description: 'Property name in each data object that contains the numeric values representing progress, completion, or magnitude. Example: "progress", "completion", "percentage", "achievement", "value". Values should be positive numbers. For progress indicators, values often represent percentages (0-100) or completion ratios.'
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration object. Each key should match a value from the nameKey field and contain styling options. Format: {"segmentName": {"label": "Display Name", "color": "hsl(210, 70%, 50%)"}}. Colors can be hex codes, HSL, or RGB. For progress charts, consider using color gradients from red (low) to green (high). If not provided, colors will be auto-generated.'
        }
      },
      required: ['data', 'nameKey', 'valueKey']
    },
    implementation: createRadialChart
  }
]
