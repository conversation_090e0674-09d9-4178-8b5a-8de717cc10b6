/**
 * # Next.js App Router API Route Handler for ESG Entity Impact Section Analysis
 *
 * This API endpoint generates comprehensive analysis of specific ESG (Environmental, Social,
 * Governance) impact sections for entities by analyzing their ESG flags data. The route processes
 * corporate sustainability impact data, evaluates flag analysis against impact criteria, 
 * and provides AI-powered content generation to create structured positive/negative impact assessments.
 *
 * ## Core Functionality
 * - **ESG Flag Processing**: Filters and analyzes ESG flags data by entity, run, model, and specific section
 * - **Impact Analysis**: Evaluates positive and negative impacts using importance-based filtering (impact scores)
 * - **AI-Powered Content Generation**: Uses Google Gemini Pro with tool support for professional ESG analysis
 * - **Chart Integration**: Supports dynamic chart generation through integrated chart tools
 * - **Validation Framework**: Validates AI responses for correct citation format and heading structure
 * - **Section-Specific Analysis**: Processes specific model sections (e.g., environment, social, governance)
 *
 * ## Request Parameters
 * - **Route Parameters**: 
 *   - `entityId` (entity identifier)
 *   - `runId` (analysis run identifier)
 *   - `model` (ESG model type)
 *   - `modelSection` (specific section identifier)
 * - **Query Parameters**: 
 *   - `includeDisclosures` (boolean flag for disclosure data inclusion)
 *
 * ## Response Format
 * Returns structured JSON containing:
 * - `text`: HTML-formatted impact analysis with positive/negative sections
 * - `citations`: Array of source document citations with internal reference IDs
 * - `metadata`: Analysis metadata including entity info, flag counts, timestamps
 * - `notApplicable`: Boolean flag when no relevant data exists for the section
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side API routes and dynamic segments
 * - **Google Generative AI (Gemini Pro)**: LLM integration with tool support for content generation
 * - **Chart Tools**: Integrated chart generation capabilities for data visualization
 * - **Validated LLMs**: Abstracted LLM client with validation, caching, and error handling
 * - **Data Fetchers**: Database access layer for entities, runs, flags, and model sections
 * - **Supabase**: Database client with RLS security for ESG data access
 *
 * ## System Architecture
 * This route fits into the broader ESG analysis system:
 * - **Analytics Backend**: Python system generates ESG flags and model sections
 * - **Data Sync Layer**: `xfer_` tables synchronize data between analytics and customer databases  
 * - **API Layer**: This route provides customer-facing access to section-specific impact analysis
 * - **Frontend**: Customer dashboard consumes this API for report generation and display
 * - **Caching Layer**: Vercel KV provides response caching for performance optimization
 *
 * ## Security & Performance
 * - Database access through Supabase RLS (Row Level Security) policies
 * - Response caching with intelligent cache keys for improved performance
 * - Request timeout configured at 600 seconds for complex AI analysis
 * - Comprehensive error handling with structured JSON error responses
 * - Input validation and sanitization for all route and query parameters
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation  
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see {@link ../data-fetchers.ts} Data Fetching Functions
 * @see {@link ../validated-llms.ts} LLM Integration Layer
 * @see {@link ../charts-tools.ts} Chart Generation Tools
 * @see {@link ../report-common.ts} Shared Report Utilities
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This API endpoint generates impact analysis reports for specific ESG model sections by analyzing entity flag data and creating structured positive/negative impact assessments.
 * @example ```bash
 * curl -X GET 'http://localhost:3000/api/report/entity/ABC123/latest/harm/model/ekoIntelligence/section/environmental?includeDisclosures=true'
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { NextRequest } from 'next/server'
import { FlagTypeV2, ModelSectionType } from '@/types'
import { truncate } from '@/utils/text-utils'
import { COMMON_INSTRUCTIONS, PROMPT_PRESERVE_CITATIONS } from '@/app/api/report/report-common'
import { callValidatedLLMsWithTools, LLMModel, LLMOptions, Message } from '@/app/api/report/validated-llms'
import { chartTools } from '@/app/api/report/charts-tools'
import { fetchEntityData, fetchFlagsData, fetchModelSectionsData, fetchRunData } from '@/app/api/report/data-fetchers'
import { hasFeature } from '@/utils/feature-flags'


export const maxDuration = 600;

// Regexes as per ECMA-262 / PCRE
const VALID_BLOCKS_RE   = /^(?:\s*<h4>(?:Positive|Negative)\s+Impacts<\/h4>\s*)+$/i;
const INVALID_TAG_RE    = /<h4>(?!(?:Positive|Negative)\s+Impacts<\/h4>)[^<]*<\/h4>/gi;

/**
 * Check if `html` is composed ONLY of allowed <h4> blocks.
 * @param {string} html
 * @returns {boolean}
 */
function isOnlyAllowedHeadings(html:string) {
  return VALID_BLOCKS_RE.test(html);
}

/**
 * Find all disallowed <h4>…</h4> tags in a larger HTML string.
 * @param {string} html
 * @returns {string[]}  // Array of the full <h4>…</h4> matches
 */
function findDisallowedHeadings(html:string) {
  const matches = [];
  let m;
  while ((m = INVALID_TAG_RE.exec(html)) !== null) {
    matches.push(m[0]);
  }
  return matches;
}

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ entityId: string; runId: string; model: string; modelSection: string }> }
) {
  try {
    // Get the parameters from the route
    const params = await context.params;
    const { entityId, runId, model, modelSection } = params;

    const { searchParams } = new URL(request.url)
    const includeDisclosures = searchParams.get('includeDisclosures') === 'true'

    if (!entityId || !runId || !model || !modelSection) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: entityId, runId, model, modelSection' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    console.log(`[API] /api/report/entity/${entityId}/${runId}/harm/model/${model}/section/${modelSection} called with:`, {
      entityId,
      runId,
      model,
      modelSection,
      includeDisclosures
    })

    // Fetch entity data
    const entityData = await fetchEntityData(entityId)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(runId, entityId)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch all flags for this entity and run
    const allFlags = await fetchFlagsData({
      entity: entityId,
      runId: runData.id.toString(),
      model,
      includeDisclosures
    })

    // Filter flags for this specific section
    const flags = allFlags.filter((flag: FlagTypeV2) =>
      flag.model.model_sections[model] === modelSection
    ).sort((a: FlagTypeV2, b: FlagTypeV2) => -(a.model?.impact || 0) + (b.model?.impact || 0)).slice(0, 40)

    // Fetch model sections to get section name
    const modelSections = await fetchModelSectionsData(model)
    const modelSectionData = modelSections.find((ms: ModelSectionType) => ms.section === modelSection)
    const sectionName = modelSectionData?.title || modelSection.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())

    const entityName = entityData.name
    const modelName = model

    console.log(`[API] Processing section:`, {
      sectionName,
      entityName,
      modelName,
      runData,
      flagsCount: flags.length
    })

    if (flags.length === 0) {
      // Return notApplicable response if no flags found
      return new Response(JSON.stringify({
        text: `STATUS:NOT_APPLICABLE`,
        citations: [],
        notApplicable: true,
        metadata: {
          entityId,
          modelSection,
          sectionName,
          entityName,
          modelName,
          flagsCount: 0,
          citationCount: 0,
          generatedAt: new Date().toISOString(),
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Build the prompt for this section
    const flagSummaries = flags.map((flag: FlagTypeV2) => {
      const analysis = truncate(flag.flag_analysis || '', 2000)
      return `<flag title='${flag.model?.flag_title}' impact-score='${flag.model?.impact || 'N/A'}'><analysis>${analysis}</analysis></flag>`
    }).join('\n\n')


    const chartsText = hasFeature('report.content.charts') ? `6. Use charts to visualize data when appropriate, do not create charts for flag data, but data within the source material. don't over use them. You have access to chart creation tools that can create:
         - Area charts for trends over time
         - Bar charts for comparing values across categories
         - Line charts for showing changes over time
         - Pie charts for showing proportions
         - Radar charts for comparing multiple metrics
         - Radial charts for progress indicators
` : ''

    const promptText = `
      <instructions>
      You are analyzing the ${sectionName} impacts for ${entityName} based on the following evidence.
      
      Please provide a comprehensive analysis that:
      1. Includes the key ${sectionName.toLowerCase()} impacts and issues
      2. Identifies patterns and trends in the evidence
      3. Highlights the most significant positive and negative impacts
      4. Provides context about the severity and scope of impacts
      5. Maintains all citation references in the format [^citation_id]
      ${chartsText}
      
      * The only headings are 'Positive Impacts' and 'Negative Impacts' start with a summary. NO OTHER HEADINGS.
      * Stick to about 500 words.
      * Focus on factual analysis based on the evidence provided.
      
      ${COMMON_INSTRUCTIONS}
      ${PROMPT_PRESERVE_CITATIONS}
      
<example>
      
<p>John Lewis Partnership demonstrates a dual approach to good health and well-being, with proactive internal policies and community contributions standing in contrast to significant product safety issues and a major negative ESG assessment. The company's initiatives positively impact employee and community welfare, while product-related incidents and business associations create substantial risks.</p>


<h4>Positive Impacts</h4>

<p>John Lewis has established several programs to support the well-being of its employees, known as Partners. Key initiatives include the enhancement of its mental health services, which have demonstrably improved employee well-being [^12342392], and the promotion of healthy eating within the workforce [^12342342]. In response to the global pandemic, the company implemented comprehensive COVID-19 safety measures to protect staff and customers .</p>

<p>The Partnership extends its focus on well-being to the wider community through significant charitable acts. These include a <strong>£1.2 million donation to the Ukraine Crisis Appeal</strong> and contributions to the children's charity Barnardos through its Christmas campaign sales [^456465]. A smaller-scale donation involved gifting its Christmas display bears to the Caldecott Foundation.</p>

<h4>Negative Impacts</h4>

<p>The most significant negative factor is a poor ESG assessment resulting from John Lewis's association with Waitrose and the corresponding sale of tobacco products . This association poses a fundamental conflict with the company's stated health and well-being objectives.</p>

<p>Customer safety has been compromised by several product recalls. Notable incidents include the recall of a Safari highchair due to a choking hazard for children  and the withdrawal of mattress toppers that posed a fire risk [^973947]. Furthermore, the company conducted <em>"significant product withdrawals and recalls"</em> during the 2015/16 period, indicating a potential systemic issue with product safety at that time  [^93347].</p>
      </example>
      </instructions>
      
      Evidence for ${entityName}  - ${sectionName}:
      
      <evidence>
      ${flagSummaries}
      </evidence>
      <important>The only headings are 'Positive Impacts' and 'Negative Impacts' start with a summary. NO OTHER HEADINGS ALLOWED.</important>

    `;

    // Extract citations from the flags used in this section first (for validation)
    const validCitations = flags
      .filter((flag: FlagTypeV2) => flag.model?.citations && flag.model.citations.length > 0)
      .flatMap((flag: FlagTypeV2) => flag.model.citations)
      .filter((citation: any, index: number, self: any[]) =>
        index === self.findIndex((c: any) => c.doc_page_id === citation.doc_page_id)
      ); // Remove duplicates

    // Create validation function for charts and citations
    const createValidator = (validCitationIds: string[]) => {
      return (response: string | null): boolean | string => {
        if (!response) {
          return 'Response is null or empty'
        }
        if(response.indexOf("<h1>") > -1) {
          return 'Response contains <h1> tag, they are not allowed, only <h4>Positive Impacts</h4> and <h4>Negative Impacts</h4> are allowed.';
        }

        if(response.indexOf("<html/?>") > -1) {
          return 'Response contains invalid <html> tag, they are not allowed.';
        }

        if(response.indexOf("<body/?>") > -1) {
          return 'Response contains <body> tag, they are not allowed.';
        }

        //Only  <h4>Positive Impacts</h4> and <h4>Negative Impacts</h4> are allowed.
        if(findDisallowedHeadings(response).length > 0) {
          return `Response contains tags ${findDisallowedHeadings(response)}, they are not allowed. Only <h4>Positive Impacts</h4> and <h4>Negative Impacts</h4> are allowed.`
        }

        // 1. Validate chart JSON if present
        const chartMatches = response.match(/<chart[^>]*>([\s\S]*?)<\/chart>/g)
        if (chartMatches) {
          for (const chartMatch of chartMatches) {
            // Check if this is a base64-encoded chart (from chart tools)
            const base64Match = chartMatch.match(/data-json="([^"]+)"/)
            if (base64Match) {
              // Validate base64-encoded chart data
              try {
                const base64Data = base64Match[1]
                const jsonString = Buffer.from(base64Data, 'base64').toString('utf-8')
                JSON.parse(jsonString)
              } catch (error) {
                return `Chart contains invalid base64-encoded JSON: ${error instanceof Error ? error.message : 'Unknown parsing error'}`
              }
            } else {
              // Legacy format: plain JSON inside chart tags
              const jsonContent = chartMatch.replace(/<\/?chart[^>]*>/g, '').trim()
              if (jsonContent) {
                try {
                  JSON.parse(jsonContent)
                } catch (error) {
                  return `Chart contains invalid JSON: ${error instanceof Error ? error.message : 'Unknown parsing error'}`
                }
              }
            }
          }
        }

        // 2. Validate citations if present
        const citationMatches = response.match(/\[\^(\d+)]/g)
        if (citationMatches) {
          for (const citationMatch of citationMatches) {
            const citationId = citationMatch.replace(/\[\^(\d+)]/, '$1')
            if (!validCitationIds.includes(citationId)) {
              return `Citation [^${citationId}] is not valid. Valid citations are: ${validCitationIds.join(', ')}`
            }
          }
        }

        return true
      }
    }

    // Get citation IDs for validation
    const validCitationIds = validCitations.map((citation: any) => citation.doc_page_id?.toString() || '')
      .filter(id => id !== '')

    // Generate content using tool-enabled LLM with chart and citation validation
    const messages: Message[] = [{ role: 'user', content: promptText }];
    
    const llmOptions: LLMOptions = {
      cacheKey: `v2-entity-${entityId}-run-${runId}-model-${model}-section-${modelSection}`,
      cachePrefix: 'entity-section-tools',
      maxOutputTokens: 16000,
      escalateTo: [LLMModel.GEMINI_PRO],
      appendOnEvalFail: true,
      eval: createValidator(validCitationIds),
      evalRetryMessage: 'Please try again.'
    };

    const text = await callValidatedLLMsWithTools(
      [LLMModel.GEMINI_PRO],
      messages,
      chartTools,
      llmOptions
    );

    if (!text) {
      throw new Error('Failed to generate entity section content');
    }

    const citations = validCitations

    // Return JSON response with text and citations
    const response = {
      text,
      citations,
      metadata: {
        entityId,
        modelSection,
        sectionName,
        entityName,
        modelName,
        runId: runData.id,
        flagsCount: flags.length,
        citationCount: citations.length,
        includeDisclosures,
        generatedAt: new Date().toISOString(),
      }
    };

    return new Response(JSON.stringify(response), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400'
      }
    });

  } catch (error) {
    console.error(`[API] /api/report/entity/harm/model/section error:`, error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate section report', 
        message: error instanceof Error ? error.message : String(error) 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
