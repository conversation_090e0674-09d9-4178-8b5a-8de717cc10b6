/**
 * Next.js App Router API Route Handler for ESG Report Introduction Generation
 *
 * This API endpoint generates comprehensive introduction sections for ESG (Environmental, Social,
 * Governance) reports within the EkoIntelligence platform. The route creates contextual introductions
 * that provide stakeholders with essential background information about entities and the analytical
 * framework being used for their ESG assessment.
 *
 * ## Core Functionality
 * - **Entity Context**: Provides comprehensive background about the entity being analyzed
 * - **ESG Framework Introduction**: Explains the analytical model and methodology being employed
 * - **Report Scope Setting**: Sets clear expectations for report coverage and content structure
 * - **Professional Tone**: Maintains diplomatic, objective language suitable for investor communications
 * - **AI Content Generation**: Uses Google Gemini 2.5 Flash for professional ESG report writing
 * - **Stakeholder-Focused**: Tailored content for investors, auditors, and ESG professionals
 *
 * ## Request Parameters
 * - **Route Parameters**: `entityId` (entity identifier), `runId` (analysis run identifier or 'latest')
 * - **Query Parameters**: 
 *   - `model` (ESG analysis model, defaults to 'ekoIntelligence')
 *   - `includeDisclosures` (boolean flag for disclosure inclusion, currently unused in introduction)
 *
 * ## Response Format
 * Returns plain text content (Content-Type: text/plain) containing:
 * - Professional HTML-formatted introduction (2-3 paragraphs)
 * - Entity industry context and business description
 * - ESG framework methodology explanation
 * - Report scope and coverage expectations
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side API routes and dynamic parameters
 * - **Google Generative AI (Gemini 2.5)**: LLM integration for professional content generation
 * - **Custom Data Fetchers**: Abstracted database access layer for entity and run information
 * - **Report Common**: Shared ESG reporting instructions and style guidelines
 * - **Cache Utilities**: Performance optimization through intelligent cache key generation
 *
 * ## System Architecture
 * This route fits into the broader ESG analysis ecosystem:
 * - **Analytics Backend**: Python system processes entity data and generates comprehensive ESG analysis
 * - **Data Sync Layer**: `xfer_entities` and `xfer_runs` tables synchronize data between analytics and customer databases
 * - **API Layer**: This route provides customer-facing access to report introduction generation
 * - **Frontend**: Customer dashboard consumes this API for report assembly and display
 * - **Content Pipeline**: Introduction serves as the opening component of multi-section ESG reports
 *
 * ## Related Components
 * - Entity Management System (analytics backend for entity data processing)
 * - ESG Analysis Pipeline (backend Python analytics for comprehensive ESG evaluation)
 * - Report Generation Framework (multi-section report assembly and rendering)
 * - Customer Dashboard Report Viewer (frontend consumption of generated content)
 *
 * ## Security & Performance
 * - Database access through Supabase RLS (Row Level Security) policies for multi-tenant data isolation
 * - Intelligent caching via cache utilities for improved response times
 * - Request timeout configured at 180 seconds for AI content generation
 * - Comprehensive error handling with structured JSON error responses and appropriate HTTP status codes
 * - Input validation for entity and run existence before content generation
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js App Router Route Handlers
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation  
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see {@link ../../../../data-fetchers.ts} Data Fetchers for ESG Analytics
 * @see {@link ../../../report-common.ts} Common ESG Report Instructions
 * @see {@link ../../../gemini-client.ts} Gemini AI Client Integration
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This API endpoint generates professional ESG report introductions using AI-powered content generation with entity context and framework explanations.
 * @example ```bash
  curl -X GET 'http://localhost:3000/api/report/entity/ABC123/latest/introduction?model=ekoIntelligence&includeDisclosures=false'
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { NextRequest } from 'next/server'
import { createCacheKey } from '@/utils/cache-utils'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { AI_MODEL_NAME } from '@/app/api/report/gemini-client'
import { generateReportContent } from '@/app/api/report/gemini-client'
import { fetchEntityData, fetchRunData } from '../../../../data-fetchers'

export const maxDuration = 180;

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ entityId: string; runId: string }> }
) {
  try {
    // Get the parameters from the route
    const params = await context.params;
    const { entityId, runId } = params;

    const { searchParams } = new URL(request.url)
    const model = searchParams.get('model') || 'ekoIntelligence'
    const includeDisclosures = searchParams.get('includeDisclosures') === 'true'

    if (!entityId || !runId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: entityId, runId' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    console.log(`[API] /api/report/entity/${entityId}/${runId}/introduction called with:`, {
      entityId,
      runId,
      model,
      includeDisclosures
    })

    // Fetch entity data
    const entityData = await fetchEntityData(entityId)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(runId, entityId)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const entityName = entityData.name
    const entityDescription = entityData.description
    const modelName = model

    // Create a cache key for logging purposes
    const cacheKey = createCacheKey('intro', {
      entityName,
      entityDescription,
      modelName
    });

    const promptText = `
      <instructions>

      ${COMMON_INSTRUCTIONS}
      
      Write a comprehensive introduction for an ESG report about ${entityName}, a company described as: "${entityDescription}".
      The report uses the ${modelName} model for analysis.

      Your introduction should:
      1. Provide context about ${entityName} and its industry
      2. Explain the purpose of this ESG report
      3. Briefly mention the ${modelName} framework being used
      4. Set expectations for what the report will cover

      Write in a professional, objective tone suitable for investors and stakeholders. The introduction should be 2-3 paragraphs.
      </instructions>
    `;

    // Generate content using our Gemini client
    const text = await generateReportContent({
      modelName: AI_MODEL_NAME,
      prompt: `You are a professional ESG report writer for a leading auditing firm specializing in environmental, social, and governance matters. You are objective and factual but diplomatic when criticizing companies. You are not conversational.\n\n${promptText}`,
      endpoint: `/api/report/entity/${entityId}/${runId}/introduction`,
      entityName,
    });

    // Return the text response
    return new Response(text, {
      headers: { 'Content-Type': 'text/plain' }
    });
  } catch (error) {
    console.error('[API] /api/report/entity/[entityId]/[runId]/introduction error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to generate introduction', message: error instanceof Error ? error.message : String(error) }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
