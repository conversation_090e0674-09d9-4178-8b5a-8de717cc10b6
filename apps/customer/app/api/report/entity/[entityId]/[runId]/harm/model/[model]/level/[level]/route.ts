/**
 * Next.js App Router API Route Handler for ESG Harm Level Report Generation
 *
 * This API endpoint generates comprehensive AI-powered ESG (Environmental, Social,
 * Governance) harm analysis reports for specific organizational levels within the EkoIntelligence platform.
 * It processes effect flags grouped by model sections and generates structured analysis using
 * Google's Gemini AI with professional ESG reporting standards.
 *
 * ## Core Functionality
 * - **Level-Specific Analysis**: Generates targeted harm analysis reports for specific ESG levels (environmental, social, governance)
 * - **Effect Flag Processing**: Analyzes and aggregates effect flags grouped by model sections with impact scoring
 * - **AI Content Generation**: Uses Google Gemini 2.5 Flash for professional ESG report writing
 * - **Citation Preservation**: Maintains standardized citation format [^citation_id] throughout analysis
 * - **Comprehensive Filtering**: Supports disclosure filtering and impact-based prioritization (top 40 flags)
 * - **Edge Case Handling**: Returns NOT_APPLICABLE status when no relevant data is found
 *
 * ## Request Parameters
 * - **Route Parameters**: `entityId` (entity identifier), `runId` (analysis run identifier or 'latest'),
 *   `model` (ESG analysis model), `level` (impact level category)
 * - **Query Parameters**: `includeDisclosures` (boolean flag for disclosure statement inclusion)
 *
 * ## Response Format
 * Returns structured JSON containing:
 * - `text`: HTML-formatted harm analysis report with comprehensive level assessment
 * - `citations`: Array of source document citations with internal reference IDs
 * - `metadata`: Analysis metadata including entity info, flag counts, section information, timestamps
 * - `notApplicable`: Boolean flag indicating when no relevant data exists for analysis
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side API routes and dynamic parameters
 * - **Supabase**: Database client for ESG data access with Row Level Security policies
 * - **Google Generative AI (Gemini 2.5)**: LLM integration for professional report content generation
 * - **Custom Data Fetchers**: Abstracted database access layer for flags, entity, model sections, and run data
 *
 * ## Related Components
 * - Effect Flag Analysis System (backend Python analytics pipeline)
 * - ESG Model Section Configuration and Management
 * - Customer-facing harm analysis dashboard and report viewer
 * - Citation Management and Document Reference System
 *
 * ## Security & Performance
 * - Database access through Supabase RLS (Row Level Security) policies for multi-tenant data isolation
 * - Response caching via Next.js Edge Runtime (1-hour cache, 24-hour stale-while-revalidate)
 * - Request timeout configured at 180 seconds for complex AI analysis operations
 * - Comprehensive error handling with structured JSON error responses and appropriate HTTP status codes
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js App Router Route Handlers
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see {@link ../data-fetchers.ts} Data Fetchers for ESG Analytics
 * @see {@link ../gemini-client.ts} Gemini AI Client Integration
 * @see {@link ../report-common.ts} ESG Report Writing Standards
 * <AUTHOR>
 * @updated 2025-07-22
 * @description API endpoint generating AI-powered ESG harm analysis reports for specific organizational levels with comprehensive citation management and professional reporting standards
 * @example ```bash
 * curl -X GET 'http://localhost:3000/api/report/entity/fgdX7xx/latest/harm/model/demise/level/environmental?includeDisclosures=true'
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { NextRequest } from 'next/server'
import { FlagTypeV2, ModelSectionType } from '@/types'
import { truncate } from '@/utils/text-utils'
import { COMMON_INSTRUCTIONS, PROMPT_PRESERVE_CITATIONS } from '@/app/api/report/report-common'
import { FANCY_AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'
import {
  fetchEntityData,
  fetchFlagsData,
  fetchModelSectionsData,
  fetchRunData,
} from '../../../../../../../../data-fetchers'

export const maxDuration = 180;

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ entityId: string; runId: string; model: string; level: string }> }
) {
  try {
    // Get the parameters from the route
    const params = await context.params;
    const { entityId, runId, model, level } = params;

    const { searchParams } = new URL(request.url)
    const includeDisclosures = searchParams.get('includeDisclosures') === 'true'

    if (!entityId || !runId || !model || !level) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: entityId, runId, model, level' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    console.log(`[API] /api/report/entity/${entityId}/${runId}/harm/model/${model}/level/${level} called with:`, {
      entityId,
      runId,
      model,
      level,
      includeDisclosures
    })

    // Fetch entity data
    const entityData = await fetchEntityData(entityId)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(runId, entityId)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch all flags for this entity and run
    const allFlags = await fetchFlagsData({
      entity: entityId,
      runId: runData.id.toString(),
      model,
      includeDisclosures
    })

    // Fetch model sections to get all sections for this level
    const modelSections = await fetchModelSectionsData(model)
    const sectionsInLevel = modelSections.filter((ms: ModelSectionType) => {
      // Handle both direct level property and data.level
      const sectionLevel = ms.level || (ms.data?.level as string | undefined);
      return sectionLevel === level;
    })

    if (sectionsInLevel.length === 0) {
      return new Response(
        JSON.stringify({ error: `No sections found for level: ${level} in model: ${model}` }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const sectionIds = sectionsInLevel.map((ms: ModelSectionType) => ms.section)

    // Filter flags for all sections in this level
    const flags = allFlags.filter((flag: FlagTypeV2) =>
      sectionIds.includes(flag.model.model_sections[model])
    ).sort((a: FlagTypeV2, b: FlagTypeV2) => -(a.model?.impact || 0) + (b.model?.impact || 0)).slice(0, 40)

    const levelName = level.charAt(0).toUpperCase() + level.slice(1)
    const entityName = entityData.name
    const modelName = model

    console.log(`[API] Processing level:`, {
      levelName,
      entityName,
      modelName,
      sectionsCount: sectionsInLevel.length,
      flagsCount: flags.length,
      sectionIds
    })

    if (flags.length === 0) {
      // Return notApplicable response if no flags found
      return new Response(JSON.stringify({
        text: `STATUS:NOT_APPLICABLE`,
        citations: [],
        notApplicable: true,
        metadata: {
          entityId,
          level,
          levelName,
          entityName,
          modelName,
          runId: runData.id,
          sectionsCount: sectionsInLevel.length,
          flagsCount: 0,
          citationCount: 0,
          includeDisclosures,
          generatedAt: new Date().toISOString(),
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Build the prompt for this level
    const flagSummaries = flags.map((flag: FlagTypeV2) => {
      const summary = truncate(flag.model?.flag_summary || '', 500)
      const analysis = truncate(flag.model?.flag_analysis || '', 300)
      return `**${flag.model?.flag_title}** (Impact: ${flag.model?.impact || 'N/A'})\n${summary}\n${analysis}`
    }).join('\n\n')

    const promptText = `
      <instructions>
      You are analyzing the ${levelName} level impacts for ${entityName} based on the following evidence.

      This analysis covers all ${levelName.toLowerCase()} sections including: ${sectionsInLevel.map((s: ModelSectionType) => s.title || s.section).join(', ')}.

      Please provide a comprehensive analysis that:
      1. Summarizes the key ${levelName.toLowerCase()} impacts and issues across all relevant sections
      2. Identifies patterns and trends in the evidence
      3. Highlights the most significant positive and negative impacts
      4. Provides context about the severity and scope of impacts
      5. Maintains all citation references in the format [^citation_id]

      Structure your response with clear headings and organize the information logically.
      Focus on factual analysis based on the evidence provided.

      Evidence for ${levelName} level:
      ${flagSummaries}

      ${COMMON_INSTRUCTIONS}
      ${PROMPT_PRESERVE_CITATIONS}
      </instructions>
    `;

    // Generate content using our Gemini client
    const text = await generateReportContent({
      modelName: FANCY_AI_MODEL_NAME,
      prompt: promptText,
      endpoint: `/api/report/entity/${entityId}/${runId}/harm/model/${model}/level/${level}`,
      entityName: `${entityName} - ${levelName} Level`,
    });

    // Extract citations from the flags used in this level
    const citations = flags
      .filter((flag: FlagTypeV2) => flag.model?.citations && flag.model.citations.length > 0)
      .flatMap((flag: FlagTypeV2) => flag.model.citations)
      .filter((citation: any, index: number, self: any[]) =>
        index === self.findIndex((c: any) => c.doc_page_id === citation.doc_page_id)
      ); // Remove duplicates

    // Return JSON response with text and citations
    const response = {
      text,
      citations,
      metadata: {
        entityId,
        level,
        levelName,
        entityName,
        modelName,
        runId: runData.id,
        sectionsCount: sectionsInLevel.length,
        flagsCount: flags.length,
        citationCount: citations.length,
        includeDisclosures,
        sectionIds,
        generatedAt: new Date().toISOString(),
      }
    };

    return new Response(JSON.stringify(response), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400'
      }
    });

  } catch (error) {
    console.error(`[API] /api/report/entity/harm/model/level error:`, error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate level report', 
        message: error instanceof Error ? error.message : String(error) 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
