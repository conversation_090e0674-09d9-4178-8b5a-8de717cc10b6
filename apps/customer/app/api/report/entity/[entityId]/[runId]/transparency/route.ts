/**
 * Next.js App Router API Route Handler for ESG Entity Transparency Analysis
 *
 * This API endpoint generates comprehensive transparency analysis reports for ESG (Environmental, Social,
 * Governance) entities by analyzing patterns of cherry picking and information flooding in corporate
 * communications. The route examines how entities selectively present information to stakeholders,
 * identifying potential transparency issues and communication manipulation patterns.
 *
 * ## Core Functionality
 * - **Cherry Picking Detection**: Identifies instances where entities selectively highlight positive information while omitting relevant context
 * - **Information Flooding Analysis**: Detects patterns where entities overwhelm stakeholders with excessive information to obscure key issues
 * - **Transparency Pattern Recognition**: Analyzes disclosure, transparency, communication, reporting, and statement patterns
 * - **Selective Communication Assessment**: Evaluates authenticity and completeness of entity communications
 * - **AI-Powered Content Generation**: Uses Google Gemini 2.5 Flash for professional ESG report writing with citation preservation
 * - **Risk-Based Analysis**: Prioritizes high-severity transparency concerns for stakeholder attention
 *
 * ## Request Parameters
 * - **Route Parameters**: `entityId` (entity identifier), `runId` (analysis run identifier or 'latest')
 * - **Query Parameters**: 
 *   - `model` (analysis model, defaults to 'ekoIntelligence', currently unused in processing)
 *   - `includeDisclosures` (boolean flag for disclosure inclusion, currently unused in transparency analysis)
 *
 * ## Response Format
 * Returns structured JSON containing:
 * - `text`: HTML-formatted transparency analysis report with professional ESG content
 * - `citations`: Array of source document citations with internal reference IDs from analyzed statements
 * - `metadata`: Analysis metadata including entity info, cherry/flooding counts, timestamps, and filtering criteria
 *
 * ## Data Processing Pipeline
 * 1. **Data Retrieval**: Fetches cherry picking and flooding data from `xfer_selective` table
 * 2. **Transparency Filtering**: Searches for transparency-related keywords in analysis, reason, and label fields
 * 3. **Content Assembly**: Builds structured prompts with cherry picking examples, flooding patterns, and analysis summaries
 * 4. **Risk Prioritization**: Sorts by severity scores and focuses on highest-risk transparency issues
 * 5. **AI Generation**: Uses Gemini 2.5 Flash for professional transparency analysis writing
 * 6. **Citation Extraction**: Consolidates and deduplicates citations from analyzed communication patterns
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side API routes and dynamic parameters
 * - **Supabase**: Database client for customer database access with RLS security policies
 * - **Google Generative AI (Gemini 2.5)**: LLM integration for professional ESG report content generation
 * - **Data Fetchers**: Abstracted database access layer for cherry/flooding, entity, and run data
 * - **Report Common**: Shared ESG reporting instructions, style guidelines, and citation formatting
 * - **Text Utils**: Token-based text truncation with smart breakpoints for handling large content
 *
 * ## System Architecture
 * This route fits into the broader ESG analysis system:
 * - **Analytics Backend**: Python system generates cherry picking and flooding analysis using DEMISE model and stores in analytics database
 * - **Data Sync Layer**: `xfer_selective` table synchronizes cherry/flooding data between analytics and customer databases
 * - **API Layer**: This route provides customer-facing access to transparency analysis generation
 * - **Frontend**: Customer dashboard consumes this API for report generation and transparency assessment display
 * - **Caching Layer**: Gemini client provides response caching for performance optimization
 *
 * ## Related Components
 * - Cherry Picking Detection System (backend Python analytics for selective highlighting identification)
 * - Information Flooding Analysis System (backend system for information overload pattern detection)  
 * - ESG Communication Assessment Pipeline (comprehensive entity communication evaluation framework)
 * - Customer-facing transparency dashboard and report viewer (frontend consumption interface)
 *
 * ## Database Schema Integration
 * **xfer_selective Table**: Cherry picking and flooding analysis data with the following structure:
 * - Core columns: `id`, `run_id`, `entity_xid`, `label`, `analysis`, `explanation`, `reason`
 * - JSON model: Contains complete analysis including `model` type ('cherry_picking' or 'flooding'), `score`, `severity`, `confidence`, `authenticity`
 * - Statement context: `negative_statements` and `positive_statements` arrays with full metadata
 * - Citations: Complete citation arrays with internal reference IDs for source traceability
 * **xfer_entities Table**: Entity metadata with name, description, and comprehensive entity information
 * **xfer_runs Table**: Analysis run metadata for versioning and temporal analysis tracking
 *
 * ## Security & Performance
 * - Database access through Supabase RLS (Row Level Security) policies for multi-tenant data isolation
 * - Response caching via Vercel KV for improved performance and reduced AI API costs
 * - Request timeout configured at 180 seconds for complex transparency analysis generation
 * - Comprehensive error handling with structured JSON error responses and appropriate HTTP status codes
 * - Input validation for entity and run existence before expensive AI content generation
 * - Token-based text truncation to manage large analysis content within API limits
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see {@link ../../../../data-fetchers.ts} Cherry Picking and Flooding Data Fetchers
 * @see {@link ../../../report-common.ts} ESG Report Common Instructions
 * @see {@link ../../../gemini-client.ts} Gemini AI Client Integration
 * @see {@link @/utils/text-utils} Text Utilities for Content Truncation
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This API endpoint generates transparency analysis reports for ESG entities by analyzing cherry picking and information flooding patterns in corporate communications.
 * @example ```bash
   curl -X GET 'http://localhost:3000/api/report/entity/ABC123/latest/transparency?model=ekoIntelligence&includeDisclosures=true'
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { NextRequest } from 'next/server'
import { CherryTypeV2 } from '@/types/cherry'
import { truncate } from '@/utils/text-utils'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { FANCY_AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'
import { fetchCherryData, fetchEntityData, fetchRunData } from '../../../../data-fetchers'

export const maxDuration = 180;

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ entityId: string; runId: string }> }
) {
  try {
    // Get the parameters from the route
    const params = await context.params;
    const { entityId, runId } = params;

    const { searchParams } = new URL(request.url)
    const model = searchParams.get('model') || 'ekoIntelligence'
    const includeDisclosures = searchParams.get('includeDisclosures') === 'true'

    if (!entityId || !runId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: entityId, runId' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    console.log(`[API] /api/report/entity/${entityId}/${runId}/transparency called with:`, {
      entityId,
      runId,
      model,
      includeDisclosures
    })

    // Fetch entity data
    const entityData = await fetchEntityData(entityId)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(runId, entityId)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch all cherry picking and flooding data for this entity and run
    const allCherryData = await fetchCherryData({
      entity: entityId,
      runId: runData.id.toString(),
      model: 'all', // For transparency analysis, we want all models
      includeDisclosures
    })

    const entityName = entityData.name

    console.log(`[API] Processing transparency analysis for:`, {
      entityName,
      cherryDataCount: allCherryData.length
    })

    // Filter for transparency-related cherry picking and flooding instances
    // Look for instances that involve disclosure, transparency, or communication patterns
    const transparencyRelatedData = allCherryData.filter((cherry: CherryTypeV2) => {
      const analysis = cherry.model?.analysis?.toLowerCase() || '';
      const reason = cherry.model?.reason?.toLowerCase() || '';
      const label = cherry.label?.toLowerCase() || '';

      return analysis.includes('disclosure') ||
             analysis.includes('transparency') ||
             analysis.includes('communication') ||
             analysis.includes('reporting') ||
             analysis.includes('statement') ||
             reason.includes('disclosure') ||
             reason.includes('transparency') ||
             reason.includes('communication') ||
             label.includes('disclosure') ||
             label.includes('transparency') ||
             label.includes('communication');
    })
      .sort((a, b) => (b.model?.severity || 0) - (a.model?.severity || 0))
      .slice(0, 20); // Limit to top 20 most severe instances

    if (transparencyRelatedData.length === 0) {
      // Return basic response if no transparency-related cherry picking/flooding found
      return new Response(JSON.stringify({
        text: `# Transparency Analysis for ${entityName}\n\nNo selective highlighting patterns related to transparency or disclosure were detected in the current analysis. This may indicate either strong transparency practices with consistent communication patterns, or limited disclosure coverage in the analyzed materials.`,

        citations: [],
        metadata: {
          entityId,
          entityName,
          analysisType: 'transparency',
          cherryDataCount: 0,
          citationCount: 0,
          generatedAt: new Date().toISOString(),
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Build the prompt for transparency analysis using cherry picking/flooding data
    const cherryAnalyses = transparencyRelatedData.map((cherry: CherryTypeV2) => {
      const analysis = truncate(cherry.model?.analysis || '', 600)
      const reason = truncate(cherry.model?.reason || '', 300)
      const modelType = cherry.model?.model === 'cherry_picking' ? 'Cherry Picking' : 'Information Flooding'

      return `**${cherry.label}** (${modelType} - Severity: ${cherry.model?.severity || 'N/A'}, Confidence: ${cherry.model?.confidence || 'N/A'}%)\n\n**Analysis:** ${analysis}\n\n**Reason:** ${reason}`
    }).join('\n\n---\n\n')

    const promptText = `
      <instructions>
      You are analyzing the transparency and disclosure practices of ${entityName} based on selective highlighting patterns detected in their communications.

      The evidence below shows instances of cherry picking and information flooding related to transparency, disclosure, and communication practices. These patterns can indicate potential issues with balanced and transparent communication.

      Please provide a comprehensive transparency analysis that covers:

      ## Selective Highlighting Patterns
      - Analyze cherry picking instances where positive information may be emphasized while negative information is downplayed
      - Evaluate information flooding patterns where excessive positive information may obscure important negative disclosures
      - Assess the impact of these patterns on stakeholder understanding

      ## Communication Balance and Transparency
      - Evaluate whether communications provide balanced perspectives
      - Assess the completeness and clarity of disclosures in context of selective highlighting
      - Identify potential gaps in transparent communication

      ## Stakeholder Impact
      - Analyze how selective highlighting patterns may affect stakeholder decision-making
      - Evaluate the potential for misleading or incomplete information presentation
      - Assess the authenticity and reliability of communications

      ## Recommendations for Improvement
      - Suggest ways to improve communication balance and transparency
      - Recommend practices to avoid selective highlighting issues
      - Propose enhanced disclosure practices

      Structure your response with clear headings and provide specific examples from the evidence.
      Maintain all citation references in the format [^citation_id].
      Focus on factual analysis based on the selective highlighting patterns provided.

      Evidence from Selective Highlighting Analysis:
      ${cherryAnalyses}


      ${COMMON_INSTRUCTIONS}
      </instructions>
    `;

    // Generate content using our Gemini client
    const text = await generateReportContent({
      modelName: FANCY_AI_MODEL_NAME,
      prompt: promptText,
      endpoint: `/api/report/entity/${entityId}/${runId}/transparency`,
      entityName: `${entityName} - Transparency Analysis`,
    });

    // Extract citations from the cherry picking/flooding data used in this analysis
    const citations = transparencyRelatedData
      .filter((cherry: CherryTypeV2) => cherry.model?.citations && cherry.model.citations.length > 0)
      .flatMap((cherry: CherryTypeV2) => cherry.model.citations)
      .filter((citation: any, index: number, self: any[]) =>
        index === self.findIndex((c: any) => c.doc_page_id === citation.doc_page_id)
      ); // Remove duplicates

    // Return JSON response with text and citations
    const response = {
      text,
      citations,
      metadata: {
        entityId,
        entityName,
        analysisType: 'transparency',
        runId: runData.id,
        cherryDataCount: transparencyRelatedData.length,
        citationCount: citations.length,
        includeDisclosures,
        generatedAt: new Date().toISOString(),
      }
    };

    return new Response(JSON.stringify(response), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error(`[API] /api/report/entity/transparency error:`, error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate transparency analysis', 
        message: error instanceof Error ? error.message : String(error) 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
