/**
 * Next.js App Router API Route Handler for ESG Entity Reliability Analysis
 *
 * This API endpoint generates comprehensive reliability analysis reports for ESG (Environmental, Social,
 * Governance) entities by analyzing their historical claims and promises against actual performance.
 * The route processes corporate sustainability statements, evaluates claim verification outcomes,
 * and provides AI-powered analysis to assess the reliability and credibility of entity commitments.
 *
 * ## Core Functionality
 * - **Claims Analysis**: Evaluates accuracy of past corporate claims with importance-based filtering (≥30)
 * - **Promises Tracking**: Monitors corporate commitments and delivery against stated targets (confidence ≥0.3)
 * - **Reliability Assessment**: Identifies patterns in statement accuracy and commitment fulfillment over time
 * - **Risk Analysis**: Highlights potential greenwashing, misleading claims, and credibility concerns
 * - **AI-Powered Content Generation**: Uses Google Gemini LLM for professional ESG report writing
 * - **Citation Integration**: Preserves document citations and source references throughout analysis
 *
 * ## Request Parameters
 * - **Route Parameters**: `entityId` (entity identifier), `runId` (analysis run identifier or 'latest')
 * - **Query Parameters**: 
 *   - `model` (analysis model, defaults to 'ekoIntelligence', currently unused in processing)
 *   - `includeDisclosures` (boolean flag for disclosure inclusion in claims and promises data)
 *
 * ## Response Format
 * Returns structured JSON containing:
 * - `text`: HTML-formatted reliability analysis report with professional ESG content
 * - `citations`: Array of source document citations with internal reference IDs from claims/promises
 * - `metadata`: Analysis metadata including entity info, counts, timestamps, and configuration
 *
 * ## Data Processing Pipeline
 * 1. **Data Retrieval**: Fetches claims and promises from `xfer_claims` and `xfer_promises` tables
 * 2. **Filtering**: Applies significance thresholds (importance ≥30 for claims, confidence ≥0.3 for promises)
 * 3. **Ranking**: Sorts by importance/confidence and limits to top 20 most significant items
 * 4. **Content Assembly**: Builds structured prompt with claim verdicts, promise tracking, and conclusions
 * 5. **AI Generation**: Uses Gemini 2.5 Flash for professional reliability analysis writing
 * 6. **Citation Extraction**: Consolidates and deduplicates citations from analyzed statements
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side API routes and dynamic parameters
 * - **Supabase**: Database client for customer database access with RLS security policies
 * - **Google Generative AI (Gemini 2.5)**: LLM integration for professional ESG report content generation
 * - **Data Fetchers**: Abstracted database access layer for claims, promises, entity, and run data
 * - **Report Common**: Shared ESG reporting instructions, style guidelines, and citation formatting
 *
 * ## System Architecture
 * This route fits into the broader ESG analysis system:
 * - **Analytics Backend**: Python system generates claims/promises analysis and stores in analytics database
 * - **Data Sync Layer**: `xfer_claims` and `xfer_promises` tables synchronize data between analytics and customer databases
 * - **API Layer**: This route provides customer-facing access to reliability analysis generation
 * - **Frontend**: Customer dashboard consumes this API for report generation and display
 * - **Caching Layer**: Gemini client provides response caching for performance optimization
 *
 * ## Related Components
 * - Claims vs Evidence Analysis System (backend Python analytics for historical claim verification)
 * - Promise Tracking and Fulfillment Analysis (backend system for commitment monitoring)
 * - ESG Entity Risk Scoring and Assessment Pipeline (comprehensive entity evaluation framework)
 * - Customer-facing reliability dashboard and report viewer (frontend consumption interface)
 *
 * ## Database Schema Integration
 * **xfer_claims Table**: Claims analysis data with model JSONB containing text, verdict, confidence, importance, citations
 * **xfer_promises Table**: Promises analysis data with model JSONB containing text, confidence, citations
 * **xfer_entities Table**: Entity metadata with name, description, and comprehensive entity information
 * **xfer_runs Table**: Analysis run metadata for versioning and temporal analysis tracking
 *
 * ## Security & Performance
 * - Database access through Supabase RLS (Row Level Security) policies for multi-tenant data isolation
 * - Response caching via Vercel KV for improved performance and reduced AI API costs
 * - Request timeout configured at 180 seconds for complex reliability analysis generation
 * - Comprehensive error handling with structured JSON error responses and appropriate HTTP status codes
 * - Input validation for entity and run existence before expensive AI content generation
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see {@link ../../../../data-fetchers.ts} Claims and Promises Data Fetchers
 * @see {@link ../../../report-common.ts} ESG Report Common Instructions
 * @see {@link ../../../gemini-client.ts} Gemini AI Client Integration
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This API endpoint generates reliability analysis reports for ESG entities by analyzing their historical claims and promises against actual performance.
 * @example ```bash
  curl -X GET 'http://localhost:3000/api/report/entity/ABC123/latest/reliability?model=ekoIntelligence&includeDisclosures=true'
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { NextRequest } from 'next/server'
import { truncate } from '@/utils/text-utils'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { FANCY_AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'
import { fetchClaimsData, fetchEntityData, fetchPromisesData, fetchRunData } from '../../../../data-fetchers'

export const maxDuration = 180;

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ entityId: string; runId: string }> }
) {
  try {
    // Get the parameters from the route
    const params = await context.params;
    const { entityId, runId } = params;

    const { searchParams } = new URL(request.url)
    const model = searchParams.get('model') || 'ekoIntelligence'
    const includeDisclosures = searchParams.get('includeDisclosures') === 'true'

    if (!entityId || !runId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: entityId, runId' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    console.log(`[API] /api/report/entity/${entityId}/${runId}/reliability called with:`, {
      entityId,
      runId,
      model,
      includeDisclosures
    })

    // Fetch entity data
    const entityData = await fetchEntityData(entityId)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(runId, entityId)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch claims and promises data
    const claimsData = await fetchClaimsData({
      entity: entityId,
      runId: runData.id.toString(),
      model: 'all',
      includeDisclosures
    })
    const promisesData = await fetchPromisesData({
      entity: entityId,
      runId: runData.id.toString(),
      model: 'all',
      includeDisclosures
    })

    const entityName = entityData.name

    console.log(`[API] Processing reliability analysis for:`, {
      entityName,
      claimsCount: claimsData.length,
      promisesCount: promisesData.length
    })

    if (claimsData.length === 0 && promisesData.length === 0) {
      // Return basic response if no claims or promises found
      return new Response(JSON.stringify({
        text: `# Reliability Analysis for ${entityName}\n\nNo claims or promises data available for reliability analysis. This may indicate limited forward-looking statements or commitments in the analyzed materials.`,
        citations: [],
        metadata: {
          entityId,
          entityName,
          analysisType: 'reliability',
          claimsCount: 0,
          promisesCount: 0,
          citationCount: 0,
          generatedAt: new Date().toISOString(),
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Filter and sort claims by importance
    const significantClaims = claimsData
      .filter(claim => claim.model.importance >= 30) // Only include important claims
      .sort((a, b) => b.model.importance - a.model.importance)
      .slice(0, 20);

    // Filter and sort promises by confidence
    const significantPromises = promisesData
      .filter(promise => promise.model.confidence >= 0.3) // Only include confident promises
      .sort((a, b) => b.model.confidence - a.model.confidence)
      .slice(0, 20);

    // Build the prompt for reliability analysis
    const claimsSummaries = significantClaims.map(claim => {
      const summary = truncate(claim.summary || '', 400)
      const conclusion = truncate(claim.conclusion || '', 300)
      return `**Claim**: ${claim.model.text}\n**Verdict**: ${claim.model.verdict}\n**Summary**: ${summary}\n**Conclusion**: ${conclusion}\n**Confidence**: ${claim.model.confidence}\n**Importance**: ${claim.model.importance}`
    }).join('\n\n')

    const promisesSummaries = significantPromises.map(promise => {
      const summary = truncate(promise.summary || '', 400)
      const conclusion = truncate(promise.conclusion || '', 300)
      return `**Promise**: ${promise.model.text}\n**Summary**: ${summary}\n**Conclusion**: ${conclusion}\n**Confidence**: ${promise.model.confidence}`
    }).join('\n\n')

    const promptText = `
      <instructions>
      You are analyzing the reliability of statements and commitments made by ${entityName} based on claims analysis and promises tracking.
      
      Please provide a comprehensive reliability analysis that covers:
      
      ## Claims Analysis
      - Evaluate the accuracy and validity of past claims
      - Assess patterns in claim verification outcomes
      - Identify areas where claims were substantiated vs. unsubstantiated
      - Analyze the confidence levels and importance ratings
      
      ## Promises and Commitments
      - Review the tracking of promises and commitments
      - Assess delivery against stated timelines and targets
      - Evaluate the specificity and measurability of commitments
      - Analyze confidence levels in promise fulfillment
      
      ## Reliability Patterns
      - Identify trends in statement accuracy over time
      - Assess consistency between claims and actual performance
      - Evaluate the organization's track record of meeting commitments
      
      ## Risk Assessment
      - Highlight areas of concern regarding statement reliability
      - Identify potential greenwashing or misleading claims
      - Assess the credibility of future commitments based on past performance
      
      Structure your response with clear headings and provide specific examples from the evidence.
      Maintain all citation references in the format [^citation_id].
      Focus on factual analysis based on the evidence provided.
      
      ${significantClaims.length > 0 ? `Claims Analysis Data:\n${claimsSummaries}\n\n` : ''}
      ${significantPromises.length > 0 ? `Promises Analysis Data:\n${promisesSummaries}\n\n` : ''}
      
      ${COMMON_INSTRUCTIONS}
      </instructions>
    `;

    // Generate content using our Gemini client
    const text = await generateReportContent({
      modelName: FANCY_AI_MODEL_NAME,
      prompt: promptText,
      endpoint: `/api/report/entity/${entityId}/${runId}/reliability`,
      entityName: `${entityName} - Reliability Analysis`,
    });

    // Extract citations from claims and promises
    const claimsCitations = significantClaims
      .filter(claim => claim.model.citations && claim.model.citations.length > 0)
      .flatMap(claim => claim.model.citations);

    const promisesCitations = significantPromises
      .filter(promise => promise.model.citations && promise.model.citations.length > 0)
      .flatMap(promise => promise.model.citations);

    // Combine and deduplicate citations
    const allCitations = [...claimsCitations, ...promisesCitations];
    const citations = allCitations.filter((citation: any, index: number, self: any[]) =>
      index === self.findIndex((c: any) => c.doc_page_id === citation.doc_page_id)
    );

    // Return JSON response with text and citations
    const response = {
      text,
      citations,
      metadata: {
        entityId,
        entityName,
        analysisType: 'reliability',
        runId: runData.id,
        claimsCount: significantClaims.length,
        promisesCount: significantPromises.length,
        citationCount: citations.length,
        includeDisclosures,
        generatedAt: new Date().toISOString(),
      }
    };

    return new Response(JSON.stringify(response), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error(`[API] /api/report/entity/reliability error:`, error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate reliability analysis', 
        message: error instanceof Error ? error.message : String(error) 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
