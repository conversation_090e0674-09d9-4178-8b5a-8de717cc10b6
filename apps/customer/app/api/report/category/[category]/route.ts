/**
 * Next.js App Router API Route Handler for ESG Category Summary Analysis
 *
 * This API endpoint generates comprehensive AI-powered category analysis summaries for ESG 
 * (Environmental, Social, Governance) entities by processing section summaries within specific 
 * ESG categories. The route synthesizes multiple section analyses into cohesive category-level 
 * insights using Google Gemini AI with professional ESG reporting standards.
 *
 * ## Core Functionality
 * - **Category-Specific Analysis**: Processes Environmental, Social, or Governance category data
 * - **Section Integration**: Synthesizes multiple section summaries into cohesive category analysis
 * - **AI-Powered Synthesis**: Uses Google Gemini 2.5 Flash for professional ESG report writing
 * - **Structured Output**: Returns standardized markdown-formatted analysis with consistent sectioning
 * - **Citation Preservation**: Maintains document citations in format [^citation_id] throughout analysis
 * - **Ontology Integration**: Applies comprehensive ESG ontology for impact assessment and recommendations
 *
 * ## Request Parameters
 * - **Route Parameters**: `category` (must be one of: 'environmental', 'social', 'governance')
 * - **Query Parameters**: 
 *   - `entity` (required): Entity identifier for the analysis target
 *   - `run` (defaults to 'latest'): Analysis run identifier
 *   - `model` (defaults to 'ekoIntelligence'): Analysis model to use
 *   - `sectionSummaries` (JSON string): Section data to synthesize into category summary
 *
 * ## Response Format
 * Returns plain text with structured markdown content including:
 * - **Summary**: 2-3 paragraph comprehensive overview with citations
 * - **Key Positives**: 3-5 bullet points highlighting positive aspects with citations
 * - **Key Negatives**: 3-5 bullet points identifying concerning areas with citations  
 * - **Key Risks**: 3-5 bullet points outlining potential future risks with citations
 * - **Key Opportunities**: 3-5 bullet points describing improvement opportunities with citations
 * - **Impact of Actions**: 3-5 ontology-based impact assessments with citations
 * - **Recommendations**: 3-5 ontology-informed suggestions with citations
 *
 * ## Data Processing Pipeline
 * 1. **Parameter Validation**: Validates category against allowed values and required parameters
 * 2. **Entity/Run Lookup**: Fetches entity and run data for context and validation
 * 3. **Section Processing**: Parses and structures section summaries from query parameters
 * 4. **Prompt Construction**: Builds comprehensive analysis prompt with ontology integration
 * 5. **AI Generation**: Uses Gemini 2.5 Flash with professional ESG analyst persona
 * 6. **Text Processing**: Applies truncation and formatting for optimal output
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side API routes and dynamic parameters
 * - **Supabase**: Database client for customer database access with RLS security policies
 * - **Google Generative AI (Gemini 2.5)**: LLM integration for professional ESG analysis generation
 * - **Data Fetchers**: Abstracted database access layer for entity and run metadata
 * - **Report Common**: Shared ESG reporting instructions, style guidelines, and professional standards
 * - **Ontology System**: Comprehensive sustainability and ESG impact assessment framework
 *
 * ## System Architecture
 * This route fits into the broader ESG analysis ecosystem:
 * - **Analytics Backend**: Python system generates section-level analyses stored in analytics database
 * - **Data Sync Layer**: `xfer_*` tables synchronize processed data between analytics and customer databases
 * - **API Layer**: This route provides customer-facing category-level summary generation
 * - **Frontend**: Customer dashboard aggregates section data and consumes this API for category views
 * - **Caching Layer**: Gemini client provides response caching for improved performance and cost optimization
 *
 * ## Related Components
 * - Section-Level Analysis Generation System (backend Python analytics for detailed section processing)
 * - ESG Model Section Configuration and Management (defines section structures and relationships)
 * - Customer-facing Category Dashboard and Report Viewer (frontend consumption interface)
 * - ESG Ontology and Impact Assessment Framework (comprehensive sustainability knowledge base)
 *
 * ## Database Schema Integration
 * **xfer_entities Table**: Entity metadata with name, description, and comprehensive organizational information
 * **xfer_runs Table**: Analysis run metadata for versioning, temporal analysis, and data lineage tracking
 * **Section Data Flow**: Receives processed section summaries as input rather than direct database access
 *
 * ## Security & Performance
 * - Database access through Supabase RLS (Row Level Security) policies for secure multi-tenant data isolation
 * - Response caching via Gemini client for performance optimization and reduced AI API costs
 * - Request timeout configured at 180 seconds for complex category synthesis operations
 * - Input validation for category values and required parameters before expensive AI processing
 * - Comprehensive error handling with structured JSON error responses and appropriate HTTP status codes
 * - Text truncation to 100K characters to optimize LLM processing and prevent token limit issues
 *
 * ## ESG Category Coverage
 * - **Environmental**: Climate change, biodiversity, resource use, pollution, sustainability practices
 * - **Social**: Human rights, labor practices, community impact, stakeholder relations, social equity
 * - **Governance**: Corporate governance, transparency, ethics, risk management, accountability
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers Documentation
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Integration Guide
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Database Documentation
 * @see {@link ../data-fetchers.ts} Entity and Run Data Fetchers
 * @see {@link ../report-common.ts} ESG Report Common Instructions and Standards
 * @see {@link ../gemini-client.ts} Gemini AI Client Integration Layer
 * @see {@link ../ontology.ts} Comprehensive ESG and Sustainability Ontology Framework
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This API endpoint generates AI-powered ESG category analysis summaries by synthesizing section-level data into comprehensive category insights with professional reporting standards.
 * @example ```bash
 * curl -X GET 'http://localhost:3000/api/report/category/environmental?entity=ABC123&run=latest&model=ekoIntelligence&sectionSummaries={"section1":"Climate analysis...","section2":"Biodiversity assessment..."}'
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { NextRequest } from 'next/server'
import { truncate } from '@/utils/text-utils'
import { createCacheKey } from '@/utils/cache-utils'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { FANCY_AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'
import { ONTOLOGY } from '@/app/api/report/ontology'
import { fetchEntityData, fetchRunData } from '../../data-fetchers'

export const maxDuration = 180;

// Define valid categories
const VALID_CATEGORIES = ['environmental', 'social', 'governance'];

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ category: string }> }
) {
  try {
    // Get the category from the route parameter
    const params = await context.params;
    const { category } = params;

    // Validate the category
    if (!VALID_CATEGORIES.includes(category)) {
      return new Response(
        JSON.stringify({ error: `Invalid category: ${category}. Must be one of: ${VALID_CATEGORIES.join(', ')}` }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const { searchParams } = new URL(request.url)
    const entity = searchParams.get('entity')
    const run = searchParams.get('run') || 'latest'
    const model = searchParams.get('model') || 'ekoIntelligence'

    // Get section summaries from query params (they should be passed as JSON string)
    const sectionSummariesParam = searchParams.get('sectionSummaries')
    let sectionSummaries = {}
    if (sectionSummariesParam) {
      try {
        sectionSummaries = JSON.parse(sectionSummariesParam)
      } catch (e) {
        console.error('Error parsing sectionSummaries:', e)
      }
    }

    if (!entity) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameter: entity' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch entity data
    const entityData = await fetchEntityData(entity)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(run, entity)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const entityName = entityData.name
    const modelName = model

    console.log(`[API] /api/report/category/${category} called with:`, {
      entityName,
      modelName,
      sectionCount: Object.keys(sectionSummaries).length
    })

    // Create a cache key for logging purposes
    const sectionKeys = Object.keys(sectionSummaries).sort().join(',');
    const cacheKey = createCacheKey(category, {
      sectionKeys,
      entityName,
      modelName
    });

    // Prepare section content for the prompt
    const sectionContent = Object.entries(sectionSummaries)
      .map(([sectionId, content]) => {
        return `
Section: ${sectionId}
Content: ${content}
        `.trim();
      }).join('\n\n');


    // Create a combined prompt for all parts
    const promptText = `

<ontology>
${ONTOLOGY}
</ontology>

<instructions>
${COMMON_INSTRUCTIONS}

You are analyzing the ${category.charAt(0).toUpperCase() + category.slice(1)} category of an ESG report about ${entityName} using the ${modelName} model.

Here are the section summaries for this category:

${sectionContent}

Please provide the following in your response, using the exact formatting specified::

### Summary
A comprehensive summary that identifies key themes and patterns across all sections, highlights the most significant findings, provides context and implications, and maintains a balanced, objective tone. This should be 2-3 paragraphs and include citations in the format [^1234] where appropriate.

### Key Positives
A bullet-point list of 3-5 key POSITIVE aspects identified in these sections. Include citations in the format [^1234] where appropriate.

### Key Negatives
A bullet-point list of 3-5 key NEGATIVE aspects identified in these sections. Include citations in the format [^1234] where appropriate.

### Key Risks
A bullet-point list of 3-5 key RISKS (potential future negative outcomes or challenges) identified in these sections. Include citations in the format [^1234] where appropriate.

### Key Opportunities
A bullet-point list of 3-5 key OPPORTUNITIES (potential future positive developments or areas for improvement) identified in these sections. Include citations in the format [^1234] where appropriate.

### Impact of Actions
Using the ontology, evaluate the impact of the actions described in these sections. Provide a bullet-point list of 3-5 key findings. Include citations in the format [^1234] where appropriate.
DO NOT REFER TO THE ONTOLOGY. This is internal and the user does not need to understand it.

### Recommendations
Using the ontology, provide a bullet-point list of 3-5 key recommendations. Include citations in the format [^1234] where appropriate.
DO NOT REFER TO THE ONTOLOGY. This is internal and the user does not need to understand it.

IMPORTANT: Use exactly "###" (three hash symbols) for all section headings, not "##" (two hash symbols).

DO NOT ADD ANY OTHER HEADINGS, PLEASE, PLEASE I BEG YOU!
</instructions>
    `;

    // Truncate if needed
    const truncatedPrompt = truncate(promptText, 100000)!;

    // Generate content using our Gemini client
    const text = await generateReportContent({
      modelName: FANCY_AI_MODEL_NAME,
      prompt: `You are a professional ESG report writer for a leading auditing firm specializing in environmental, social, and governance matters. You are objective and factual but diplomatic when criticizing companies. You are not conversational.\n\n${truncatedPrompt}`,
      endpoint: `/api/report/category/${category}`,
      entityName,
    });

    // Return the text response
    return new Response(text, {
      headers: { 'Content-Type': 'text/plain' }
    });
  } catch (error) {
    // Get the category from the route parameter for error reporting
    let categoryValue = 'unknown';
    try {
      const params = await context.params;
      categoryValue = params.category || 'unknown';
    } catch (e) {
      console.error('Error resolving params:', e);
    }
    
    console.error(`[API] /api/report/category/${categoryValue} error:`, error);
    return new Response(
      JSON.stringify({ error: `Failed to summarize ${categoryValue} category`, message: error instanceof Error ? error.message : String(error) }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
