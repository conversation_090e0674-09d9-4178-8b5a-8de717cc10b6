/**
 * ESG Report Generation Standards for EkoIntelligence Platform
 *
 * Provides core standardized instructions and citation handling for AI-powered ESG report generation.
 * This module serves as the central authority for professional ESG reporting standards, ensuring
 * all AI-generated content meets auditing firm quality requirements and maintains proper academic
 * citation formatting throughout the platform's dual-database architecture.
 *
 * ## Core Functionality
 * - **Professional Standards Enforcement**: Maintains objective, diplomatic tone suitable for investors and stakeholders
 * - **Citation Format Standardization**: Enforces academic-style citations in [^1234] format linking to internal ESG documents
 * - **HTML Semantic Structure**: Ensures all AI-generated content uses proper HTML markup for web display
 * - **Version Management**: Implements instruction versioning system for cache invalidation and content consistency
 * - **Style Guide Compliance**: Enforces professional writing standards including active voice, third person, and present tense
 *
 * ## Database Architecture Integration
 * 
 * ### Dual-Database System
 * - **Analytics Database**: Python backend processes ESG documents and generates analysis in `ana_*` tables
 * - **Customer Database**: Frontend accesses synchronized data through `xfer_*` tables for optimized query performance
 * - **Citation Traceability**: Citations reference original ESG documents via internal IDs maintained across both databases
 *
 * ### Data Flow Integration
 * 1. **Backend Analysis**: Python processes corporate ESG documents using NLP and ML models
 * 2. **Data Synchronization**: Results transferred to customer database `xfer_*` tables
 * 3. **Report Generation**: Frontend APIs use these standards to generate professional ESG reports
 * 4. **AI Content Creation**: Google Gemini models apply these instructions for consistent output quality
 *
 * ## AI Integration Architecture
 * 
 * ### Google Gemini Integration
 * - **Primary Models**: Gemini 2.5 Flash (speed/cost) and Gemini 2.5 Pro (quality/complexity)
 * - **Validated Framework**: Integrates with `validated-llms.ts` for retry logic, caching, and model escalation
 * - **Response Validation**: Ensures minimum citation requirements and content quality standards
 * - **Caching Strategy**: Vercel KV caching with version-based cache keys for performance optimization
 *
 * ### Multi-Route Integration
 * Used across 9+ API routes including:
 * - Entity introductions, reliability, transparency, and harm analysis
 * - Content summarization with citation preservation
 * - Category-specific report generation
 * - Dynamic chart generation with contextual descriptions
 *
 * ## ESG Compliance & Quality Assurance
 * 
 * ### Professional Standards
 * - **Regulatory Compliance**: Output suitable for ESG regulatory requirements and audit trails
 * - **Academic Rigor**: Maintains source attribution standards expected in sustainability reporting
 * - **Investor Communication**: Professional tone appropriate for financial stakeholders and boards
 * - **Audit Readiness**: Citations provide complete traceability to original ESG documents
 *
 * ### Content Quality Controls
 * - **Objectivity Enforcement**: Prevents speculation, hypotheses, or unsupported claims
 * - **Temporal Accuracy**: Maintains precise time periods, locations, and quantitative information
 * - **Source Attribution**: All claims must reference original ESG documents with proper citations
 * - **Greenwashing Detection**: Supports platform's core mission of identifying misleading ESG claims
 *
 * ## Performance & Scalability
 * 
 * ### Caching & Optimization
 * - **Version-based Caching**: Instruction versioning enables selective cache invalidation
 * - **Response Caching**: Typical cache hit rates >70% reducing API costs and latency
 * - **Edge Computing**: Next.js 15 Edge Runtime for global low-latency response
 * - **Concurrent Processing**: Handles multiple report generation requests simultaneously
 *
 * ### Technical Integration
 * - **Type Safety**: Full TypeScript integration with generated database types
 * - **Error Handling**: Fail-fast architecture with comprehensive error propagation
 * - **Monitoring**: Integration with platform logging and performance monitoring systems
 * - **Collaborative Editing**: Supports TipTap editor integration for real-time document collaboration
 *
 * @see https://docs.anthropic.com/en/docs/build-with-claude/prompt-engineering/system-prompts
 * @see https://ai.google.dev/gemini-api/docs/system-instruction
 * @see {@link /apps/customer/app/api/report/gemini-client.ts} AI content generation client
 * @see {@link /apps/customer/app/api/report/data-fetchers.ts} ESG data retrieval layer
 * @see {@link /apps/customer/app/api/report/validated-llms.ts} LLM validation framework
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Core ESG report generation standards and citation handling for AI-powered professional reports
 * @example
 * ```typescript
 * import { COMMON_INSTRUCTIONS, PROMPT_PRESERVE_CITATIONS } from './report-common'
 * 
 * // Standard usage in AI report generation
 * const reportContent = await generateCitedReportContent({
 *   systemPrompt: COMMON_INSTRUCTIONS,
 *   userPrompt: `Generate ESG analysis for ${entityName}. ${PROMPT_PRESERVE_CITATIONS}`,
 *   data: esgAnalysisData,
 *   minCitations: 3
 * })
 * 
 * // Result includes properly formatted citations:
 * // "Tesla's sustainability claims have improved [^2156] but concerns remain about battery lifecycle [^3847]"
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

export const PROMPT_PRESERVE_CITATIONS = 'Please preserve citations and quotes from the source where possible. Example citation is: Barclays has been profiting from global warming [^3468]. The citation should always be in the format [^1234] where 1234 is our internal ID.'
const instructionsVersion = 7
export const COMMON_INSTRUCTIONS = `
<system>You are a professional ESG report writer for a leading auditing firm specializing in environmental, social, and governance matters. You are objective and factual but diplomatic when criticizing companies. You always cite your sources, preserve facts and include direct quotations from the text. You are not conversational.</system>

<ignore-this>This is version ${instructionsVersion} of the instructions.</ignore-this>

<p>Please follow this style guide:</p>

<style-notes>
<ul>
<li>Remain upbeat but matter of fact, you are writing a report for a professional client.</li>
<li>Write in a professional, objective tone suitable for investors and stakeholders.</li>
<li>Write in clear, concise language.</li>
<li>Write in the present tense.</li>
<li>Write in the active voice.</li>
<li>Write in the third person.</li>
<li>Never refer to information that was provided in the prompt to you.
So do not say 'In the text it says...' or 'the provided data', 'the provided information' or 'the supplied context' etc.
The customer cannot see this data, refer only to the cited source material where applicable otherwise just speak as if from experience.
You are writing a report not responding to chat. The customer cannot read what you read, they can only see the report you write and the documents you cite.
<li>Use HTML formatting for all content structure including headings, emphasis, and lists.</li>
<li>Use HTML table syntax for all tabular data.</li>
</ul>
</style-notes>
<guidelines>
<ul>
    <li>If summarizing information, do not add any additional information.</li>
    <li>Maintain objectivity based solely on provided information.</li>
    <li>Do not extrapolate or generalize. Do not hypothesize or imagine.</li>
    <li>Please keep all time period, location and quantity information in your analysis.</li>
    <li>Please pay attention to the time period the events are happening in. Do make sure to retain this information in your analysis The year today is ${new Date().getFullYear()}</li>
    <li>${PROMPT_PRESERVE_CITATIONS}</li>
    </ul>
</guidelines>

<h2>HTML Formatting Guidelines</h2>

<p>Use the following HTML elements for content structure:</p>
<ul>
<li> Emphasis: Use &lt;strong&gt; for bold text and &lt;em&gt; for italics</li>
<li> Lists: Use &lt;ul&gt; and &lt;li&gt; for bullet points, &lt;ol&gt; and &lt;li&gt; for numbered lists</li>
<li> Quotes: Use &lt;blockquote&gt; for quoted material</li>
<li> Paragraphs: Use &lt;p&gt; tags for paragraph breaks</li>
<li> Tables: Use proper HTML table structure with &lt;table&gt;, &lt;thead&gt;, &lt;tbody&gt;, &lt;tr&gt;, &lt;th&gt;, &lt;td&gt; </li>
<li> Links: Use &lt;a href="URL"&gt; for hyperlinks </li>
</ul>

<p>Important: Generate clean, semantic HTML without any markdown syntax. Do not use markdown formatting like # for headings, ** for bold, or * for lists.</p>

<ignore-this>This is version ${instructionsVersion} of the instructions.</ignore-this>
`
