/**
 * Supabase ESG Data Fetchers for Report Generation API
 *
 * This module provides a comprehensive set of data fetching utilities for the EkoIntelligence ESG analysis platform.
 * It serves as the primary data access layer for ESG (Environmental, Social, Governance) report generation APIs,
 * handling secure database queries through Supabase to retrieve entities, analysis runs, effect flags, claims,
 * promises, cherry-picking instances, and model sections from the customer database.
 *
 * ## Core Functionality
 * - **Entity Data Management**: Retrieves corporate entity information with descriptions extracted from JSONB models
 * - **Analysis Run Tracking**: Manages ESG analysis execution metadata with support for 'latest' run resolution
 * - **Effect Flags Processing**: Fetches sustainability effect flags with disclosure filtering capabilities
 * - **Claims Analysis Data**: Provides access to corporate claim verification results with importance filtering
 * - **Promises Tracking Data**: Retrieves corporate commitment tracking data with confidence-based sorting
 * - **Cherry-Picking Detection**: Accesses selective highlighting analysis for greenwashing detection
 * - **Model Sections Retrieval**: Fetches standardized ESG model section definitions for report structure
 *
 * ## Database Architecture Integration
 * This module operates within EkoIntelligence's dual-database architecture:
 * - **Analytics Database**: Python backend processes ESG documents and generates analysis results
 * - **Customer Database**: Supabase PostgreSQL stores user-facing data synchronized via `xfer_` tables
 * - **Data Sync Layer**: Automated synchronization ensures customer database contains latest analytics results
 * - **Row Level Security**: All queries protected by Supabase RLS policies for authenticated user access
 *
 * ## Key Data Tables
 * - **`xfer_entities`**: Corporate entities with extracted descriptions from JSONB model data
 * - **`xfer_runs`**: Analysis execution metadata tracking scope, targets, and completion status
 * - **`xfer_flags`**: ESG effect flags with extracted summaries, analysis text, and statement references
 * - **`xfer_claims`**: Corporate claim verification results with importance scores and verification status
 * - **`xfer_promises`**: Corporate commitment tracking with confidence scores and fulfillment analysis
 * - **`xfer_selective`**: Cherry-picking/selective highlighting detection with reasoning and explanations
 * - **`xfer_model_sections`**: Standardized ESG model section definitions for consistent report structure
 *
 * ## Performance Optimizations
 * - **Selective Column Queries**: Fetches only required columns to minimize data transfer
 * - **Extracted Text Fields**: Pre-extracted text fields from JSONB models for faster text search and display
 * - **Indexed Filtering**: Utilizes database indexes on entity_xid, run_id, and other frequently queried fields
 * - **Confidence-Based Sorting**: Client-side sorting by confidence scores for optimal data presentation
 * - **Disclosure Filtering**: Optional filtering to exclude disclosure-only flags for focused analysis
 *
 * ## Security & Access Control
 * - **Supabase RLS Policies**: All data access controlled by Row Level Security policies
 * - **Authenticated Access**: Claims and flags require authenticated user sessions
 * - **Admin Controls**: Specialized policies for administrative data management operations
 * - **Data Isolation**: Entity-based data isolation ensures users see only relevant information
 *
 * ## Report Generation Integration
 * These fetchers support various ESG report types:
 * - **Entity Analysis Reports**: Comprehensive entity assessment using flags, claims, and promises
 * - **Reliability Analysis**: Claim verification and promise tracking for credibility assessment
 * - **Transparency Reports**: Disclosure analysis and communication effectiveness evaluation
 * - **Harm Analysis**: ESG impact assessment using effect flags and model sections
 * - **Category Reports**: Sector-specific analysis using cherry-picking and selective highlighting data
 *
 * ## Type Safety & Data Validation
 * - **TypeScript Integration**: Full type safety with generated database types from Supabase
 * - **JSONB Model Handling**: Proper typing for complex JSONB model fields containing analysis results
 * - **Null Safety**: Comprehensive null handling for optional fields and missing data scenarios
 * - **Data Consistency**: Ensures data integrity across synchronized customer and analytics databases
 *
 * ## Usage Patterns
 * These fetchers are typically used in Next.js API routes to:
 * 1. Validate entity and run parameters from route segments
 * 2. Fetch comprehensive data sets for specific entities and analysis runs
 * 3. Apply business logic filtering (importance thresholds, confidence scores)
 * 4. Prepare structured data for AI-powered report generation
 * 5. Extract citations and metadata for report referencing and transparency
 *
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://www.typescriptlang.org/docs/handbook/2/objects.html TypeScript Object Types
 * @see /apps/customer/app/api/report/entity/ ESG Report Generation APIs  
 * @see /apps/customer/types/ TypeScript Type Definitions
 * @see /tmp/db/customer/schemas/public/tables/ Database Schema Documentation
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Supabase ESG data fetching utilities for comprehensive report generation with security, performance optimization, and type safety
 * @example
 * ```typescript
 * // Fetch entity data with description extraction
 * const entity = await fetchEntityData('AAPL_US');
 * 
 * // Get latest analysis run for entity
 * const run = await fetchRunData('latest', 'AAPL_US');
 * 
 * // Fetch effect flags with disclosure filtering
 * const flags = await fetchFlagsData({ entity: 'AAPL_US', runId: '123', model: 'ekoIntelligence', includeDisclosures: false });
 * 
 * // Get claims data sorted by importance
 * const claims = await fetchClaimsData({ entity: 'AAPL_US', runId: '123', model: 'ekoIntelligence' });
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createClient } from '@/app/supabase/server'
import { FlagTypeV2, ModelSectionType } from '@/types'
import { ClaimTypeV2 } from '@/types/claim'
import { PromiseTypeV2 } from '@/types/promise'
import { CherryTypeV2 } from '@/types/cherry'

export interface ReportDataParams {
  entity: string
  runId: string
  model: string
  includeDisclosures?: boolean
}

export interface EntityData {
  entity_xid: string
  name: string
  description: string
}

export interface RunData {
  id: number
}

/**
 * Fetch entity data from xfer_entities table
 */
export async function fetchEntityData(entityXid: string): Promise<EntityData | null> {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('xfer_entities')
    .select('entity_xid, name, model')
    .eq('entity_xid', entityXid)
    .single()

  if (error) {
    console.error('Error fetching entity data:', error)
    return null
  }

  // Extract description from the model JSON
  let description = ''
  if (data.model && typeof data.model === 'object') {
    const model = data.model as any
    // Try to get description from the first base entity
    if (model.base_entities && Array.isArray(model.base_entities) && model.base_entities.length > 0) {
      description = model.base_entities[0].description || ''
    }
    // Fallback to top-level description if it exists
    if (!description && model.description) {
      description = model.description
    }
  }

  return {
    entity_xid: data.entity_xid,
    name: data.name,
    description: description
  } as EntityData
}

/**
 * Fetch run data - if runId is 'latest', get the most recent run
 */
export async function fetchRunData(runId: string, entityXid: string): Promise<RunData | null> {
  const supabase = await createClient()

  if (runId === 'latest') {
    const { data, error } = await supabase
      .from('xfer_runs')
      .select('id')
      .eq('scope', 'entity')
      .eq('target', entityXid)
      .order('id', { ascending: false })
      .limit(1)
      .single()

    if (error) {
      console.error('Error fetching latest run:', error)
      return null
    }

    return data as RunData
  } else {
    // Validate that the specific run exists
    const { data, error } = await supabase
      .from('xfer_runs')
      .select('id')
      .eq('id', parseInt(runId))
      .single()

    if (error) {
      console.error('Error fetching run data:', error)
      return null
    }

    return data as RunData
  }
}

/**
 * Fetch flags data from xfer_flags table with optimized column selection
 */
export async function fetchFlagsData(params: ReportDataParams): Promise<FlagTypeV2[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_flags')
    .select(`
      id, 
      run_id, 
      entity_xid, 
      flag_type, 
      flag_summary, 
      flag_analysis,
      flag_statements,
      model
    `)
    .eq('run_id', parseInt(params.runId))
    .eq('entity_xid', params.entity)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching flags data:', error)
    return []
  }

  let flags = data as unknown as FlagTypeV2[]

  // Filter out disclosure-only flags if includeDisclosures is false
  if (!params.includeDisclosures) {
    flags = flags.filter(flag => {
      // Keep all red flags and non-disclosure-only green flags
      return flag.model?.flag_type === 'red' || !flag.model?.is_disclosure_only
    })
  }

  return flags
}

/**
 * Fetch claims data from xfer_claims table with optimized column selection
 */
export async function fetchClaimsData(params: ReportDataParams): Promise<ClaimTypeV2[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_claims')
    .select(`
      id, 
      run_id, 
      statement_id, 
      entity_xid, 
      verified, 
      importance,
      summary,
      model
    `)
    .eq('run_id', parseInt(params.runId))
    .eq('entity_xid', params.entity)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching claims data:', error)
    return []
  }

  return data as unknown as ClaimTypeV2[]
}

/**
 * Fetch promises data from xfer_promises table with optimized column selection
 */
export async function fetchPromisesData(params: ReportDataParams): Promise<PromiseTypeV2[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_promises')
    .select(`
      id, 
      run_id, 
      statement_id, 
      entity_xid, 
      kept,
      summary,
      conclusion,
      statement_text,
      model
    `)
    .eq('run_id', parseInt(params.runId))
    .eq('entity_xid', params.entity)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching promises data:', error)
    return []
  }

  let promises = data as unknown as PromiseTypeV2[]

  // Sort by confidence if available
  promises.sort((a, b) => {
    const modelA = a.model as any
    const modelB = b.model as any
    const confA = modelA && typeof modelA === 'object' && 'confidence' in modelA ? modelA.confidence : 0
    const confB = modelB && typeof modelB === 'object' && 'confidence' in modelB ? modelB.confidence : 0
    return confB - confA
  })

  return promises
}

/**
 * Fetch cherry data from xfer_selective table with optimized column selection
 */
export async function fetchCherryData(params: ReportDataParams): Promise<CherryTypeV2[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_selective')
    .select(`
      id, 
      run_id, 
      entity_xid, 
      label,
      analysis,
      explanation,
      reason,
      model
    `)
    .eq('run_id', parseInt(params.runId))
    .eq('entity_xid', params.entity)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching cherry data:', error)
    return []
  }

  return data as unknown as CherryTypeV2[]
}

/**
 * Fetch model sections data from xfer_model_sections table
 */
export async function fetchModelSectionsData(model: string): Promise<ModelSectionType[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_model_sections')
    .select('*')
    .eq('model', model)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching model sections data:', error)
    return []
  }

  return data as unknown as ModelSectionType[]
}
