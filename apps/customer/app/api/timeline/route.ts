/**
 * Next.js App Router API Route Handler for AI-Powered ESG Timeline Generation
 * doc-by-claude
 *
 * This API endpoint provides intelligent timeline generation capabilities for ESG (Environmental, Social, 
 * Governance) events using Google's Gemini 2.5 Flash model with intelligent caching and data processing. 
 * The route transforms complex structured ESG data (claims, promises, effect flags, cherry-picked statements) 
 * into chronologically sorted timeline events suitable for visualization and analysis in the EkoIntelligence platform.
 *
 * ## Core Functionality
 * - **AI-Powered Timeline Generation**: Uses Google Gemini 2.5 Flash for intelligent event extraction and timeline creation
 * - **ESG Data Processing**: Processes structured ESG entities data including claims, promises, effect flags, and cherry-picked statements
 * - **Chronological Sorting**: Automatically sorts generated events by year in ascending order (oldest first)
 * - **Intelligent Caching**: Vercel KV-based caching with MD5 hash keys and version prefixes for performance optimization
 * - **Bookend Event Selection**: Ensures timelines include both earliest and latest dated events for temporal context
 * - **Flexible Year Handling**: Processes events from all years including recent/future dates (2024, 2025+)
 * - **JSON Response Validation**: Robust JSON extraction and parsing with comprehensive error handling
 * - **Content Truncation**: Automatic prompt truncation to 100,000 characters to fit model limits
 *
 * ## Request Structure
 * - **HTTP Method**: POST (required for complex ESG data processing)
 * - **Content-Type**: application/json
 * - **Timeout**: 180 seconds maximum execution time for complex timeline generation
 * - **Body Parameters**:
 *   - `preamble` (string): Additional context or instructions for timeline generation
 *   - `obj` (object): Structured ESG data object containing entities, claims, promises, effect flags, etc.
 *   - `version` (string, optional): Version identifier for cache management and backward compatibility
 *   - `includeDisclosures` (boolean): Flag indicating whether to include disclosure-related events in timeline
 *
 * ## Response Format
 * Returns JSON object with `response` property containing chronologically sorted array of timeline events:
 * ```json
 * {
 *   "response": [
 *     {
 *       "year": "2020",
 *       "event": "Carbon Neutrality Commitment",
 *       "summary": "Company announces carbon neutral by 2030 goal",
 *       "description": "Detailed description of the sustainability commitment and implementation strategy",
 *       "source": "promise",
 *       "source_id": "promise_12345"
 *     }
 *   ]
 * }
 * ```
 *
 * ## Timeline Event Structure
 * Each generated timeline event contains:
 * - **year**: Event year extracted from source data (string format)
 * - **event**: Brief, descriptive event title suitable for timeline display
 * - **summary**: Concise summary of the event for quick understanding
 * - **description**: Detailed description providing full context and implications
 * - **source**: Source type categorization (`flag`, `claim`, `promise`, `cherry`, `other`)
 * - **source_id**: Unique identifier linking back to original data source for traceability
 *
 * ## AI Model Configuration
 * - **Model**: Google Gemini 2.5 Flash (optimized for speed and structured data processing)
 * - **Prompt Engineering**: Specialized historian persona with ESG domain expertise
 * - **Response Format**: Enforced JSON array output with comprehensive validation
 * - **Event Selection**: 6-12 most significant events with mandatory bookend inclusion
 * - **Temporal Coverage**: All available years included without filtering or limitation
 * - **Source Attribution**: Maintains traceability to original ESG analysis data
 *
 * ## ESG Data Processing Capabilities
 * - **Effect Flags**: Processes ESG impact assessments and risk flags with temporal analysis
 * - **Claims Analysis**: Converts corporate sustainability claims into timeline events with verification context
 * - **Promise Tracking**: Transforms corporate commitments and promises into trackable timeline milestones
 * - **Cherry-Picked Statements**: Incorporates significant cherry-picked statements and selective highlighting
 * - **Multi-Entity Support**: Handles complex entity relationships and hierarchical data structures
 * - **Year-over-Year Analysis**: Captures trends and changes across multiple reporting periods
 *
 * ## Performance & Caching Strategy
 * - **Intelligent Cache Keys**: MD5 hash of input parameters with version prefix (v1.16-gemini-timeline-)
 * - **Cache Versioning**: Version prefixes enable selective cache invalidation for improved data consistency
 * - **Vercel KV Integration**: Distributed Redis-compatible caching for global performance optimization
 * - **Cache Hit Performance**: ~50-100ms response time for cached timeline requests
 * - **API Call Performance**: 2-5 seconds for new timeline generation with comprehensive processing
 * - **Error Recovery**: Graceful fallback handling with detailed error context preservation
 *
 * ## JSON Processing & Validation
 * - **Markdown Code Block Extraction**: Handles responses wrapped in ```json markdown blocks
 * - **Array Boundary Detection**: Sophisticated JSON array extraction using bracket matching
 * - **Parse Validation**: Comprehensive JSON parsing with structured error reporting
 * - **Format Enforcement**: Validates response structure and array format requirements
 * - **Error Contextualization**: Detailed error messages for debugging and monitoring
 *
 * ## System Integration
 * This route integrates with the broader EkoIntelligence ESG analysis ecosystem:
 * - **ESG Analysis Pipeline**: Processes outputs from backend Python analysis systems
 * - **Dashboard Visualization**: Powers timeline components in analytical dashboards
 * - **Report Generation**: Provides chronological context for comprehensive ESG reports
 * - **Data Exploration**: Supports interactive data exploration and drill-down capabilities
 * - **Audit Trails**: Maintains full traceability from timeline events back to source analysis
 *
 * ## Security & Validation
 * - **API Key Security**: Uses environment variables for secure Google AI API access
 * - **Input Validation**: Validates ESG data structure and content before AI processing
 * - **Response Sanitization**: Comprehensive JSON validation and sanitization
 * - **Error Boundaries**: Structured error handling with appropriate HTTP status codes
 * - **Cache Security**: Secure hash-based cache keys prevent unauthorized data access
 * - **Content Filtering**: Built-in Google AI safety filters for appropriate content generation
 *
 * ## Error Handling & Monitoring
 * - **Comprehensive Logging**: Detailed request/response logging for debugging and performance monitoring
 * - **Structured Error Responses**: Consistent JSON error format with meaningful messages
 * - **Graceful Degradation**: Fallback behavior for AI API failures and parsing errors
 * - **Performance Metrics**: Request timing and cache hit rate monitoring
 * - **Alert Integration**: Error patterns trigger monitoring alerts for operational visibility
 *
 * ## Usage Context
 * This endpoint is typically used for:
 * - Generating visual timelines for ESG entity analysis dashboards
 * - Creating chronological narratives from complex ESG analysis results
 * - Supporting executive reporting with temporal ESG performance insights
 * - Enabling interactive data exploration through timeline visualization
 * - Providing historical context for current ESG performance evaluation
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers Documentation
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation
 * @see https://vercel.com/docs/storage/vercel-kv Vercel KV Storage Documentation
 * @see {@link ../report/gemini-client.ts} Google Gemini client implementation
 * @see {@link ../../../utils/text-utils.ts} Text processing utilities
 * @see {@link ../../supabase/server.ts} Supabase server client configuration
 * <AUTHOR>
 * @updated 2025-07-23
 * @description AI-powered timeline generation API for ESG events using Google Gemini with caching, chronological sorting, and comprehensive JSON validation for EkoIntelligence platform integration
 * @example ```bash
curl -X POST 'http://localhost:3000/api/timeline' \
  -H 'Content-Type: application/json' \
  -d '{
    "preamble": "Focus on environmental and carbon reduction initiatives",
    "obj": {
      "entity": "Microsoft Corporation", 
      "claims": [
        {"year": "2020", "claim": "Carbon negative by 2030", "id": "claim_001"},
        {"year": "2021", "claim": "Remove all historical carbon by 2050", "id": "claim_002"}
      ],
      "promises": [
        {"year": "2022", "promise": "$1B climate innovation fund", "id": "promise_001"}
      ]
    },
    "version": "1.16",
    "includeDisclosures": true
  }'
```
 * @docgen This comprehensive documentation covers the AI-powered timeline generation API used throughout the EkoIntelligence ESG analysis platform
 * @copyright 2025 EkoIntelligence - ESG Analysis Platform
 */

import { kv } from '@vercel/kv'
import crypto from 'crypto'
import { truncate } from '@utils/text-utils'
import { createClient } from '@/app/supabase/server'
import { AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'

/**
 * Maximum execution duration for timeline generation requests in seconds.
 * Set to 180 seconds (3 minutes) to accommodate complex ESG data processing
 * and AI model response times for comprehensive timeline generation.
 */
export const maxDuration = 180;

/**
 * Global caching configuration flag for timeline generation.
 * When enabled, timeline responses are cached using Vercel KV to improve
 * performance and reduce AI API costs for repeated requests.
 */
const caching = true;

/**
 * POST Request Handler for AI-Powered ESG Timeline Generation
 * 
 * Processes structured ESG data objects and generates chronologically sorted timeline events
 * using Google Gemini 2.5 Flash model with intelligent caching and comprehensive validation.
 * This function serves as the main entry point for timeline generation requests from the
 * EkoIntelligence platform's frontend components and analytics dashboards.
 * 
 * ## Request Processing Workflow
 * 1. **Input Parsing**: Extracts and validates request body parameters (preamble, obj, version, includeDisclosures)
 * 2. **Cache Key Generation**: Creates versioned MD5 hash cache key from input parameters for efficient caching
 * 3. **Cache Lookup**: Checks Vercel KV cache for existing timeline response to avoid redundant AI processing
 * 4. **AI Generation**: If cache miss, constructs historian prompt and calls Google Gemini for timeline generation
 * 5. **Response Processing**: Extracts JSON array from AI response with robust parsing and validation
 * 6. **Chronological Sorting**: Sorts timeline events by year in ascending order (oldest events first)
 * 7. **Cache Storage**: Stores validated timeline response in Vercel KV for future requests
 * 8. **Response Delivery**: Returns formatted JSON response with timeline event array
 * 
 * ## Caching Strategy Implementation
 * - **Cache Key Format**: `gemini-timeline-{MD5_HASH}` where hash includes version prefix and all parameters
 * - **Version Management**: Uses "1.16:" prefix for cache invalidation after chronological ordering fixes
 * - **Parameter Inclusion**: Hash includes obj content and includeDisclosures flag for accurate cache hits
 * - **Performance Optimization**: Cache hits return immediately without AI API calls (~50ms response)
 * - **Cost Reduction**: Prevents redundant AI API charges for identical timeline requests
 * 
 * ## AI Prompt Engineering
 * The function constructs a sophisticated historian prompt that:
 * - **Domain Expertise**: Positions AI as ESG-specialized historian for accurate analysis
 * - **Output Format**: Enforces strict JSON array response format with specified event properties
 * - **Event Selection**: Requests 6-12 most significant events across all available years
 * - **Bookend Requirements**: Mandates inclusion of earliest and latest dated events for context
 * - **Source Attribution**: Requires proper source type and ID attribution for traceability
 * - **Year Inclusivity**: Explicitly includes recent and future years (2024, 2025+) without filtering
 * 
 * ## JSON Response Processing
 * Implements robust JSON extraction and validation:
 * - **Markdown Handling**: Strips ```json code block wrappers from AI responses
 * - **Array Detection**: Locates JSON array boundaries using bracket matching algorithms
 * - **Parse Validation**: Comprehensive JSON.parse with structured error handling
 * - **Format Verification**: Validates array structure and rejects malformed responses
 * - **Error Context**: Provides detailed error messages for debugging and monitoring
 * 
 * ## Error Handling Architecture
 * - **Parse Errors**: Returns HTTP 500 with "Failed to generate valid timeline" for JSON issues
 * - **Processing Errors**: Returns HTTP 500 with "Failed to process timeline data" for sorting/filtering failures
 * - **API Errors**: Returns HTTP 500 with "Failed to fetch response from Google Gemini" for AI API failures
 * - **Logging Integration**: All errors logged with context for operational monitoring
 * - **Graceful Degradation**: Structured error responses maintain API contract consistency
 * 
 * ## Integration Points
 * This function integrates with key platform components:
 * - **Frontend Timeline Components**: Provides data for visual timeline displays in dashboards
 * - **Report Generation System**: Supplies chronological context for comprehensive ESG reports
 * - **Analytics Pipeline**: Processes structured data from backend ESG analysis systems
 * - **Caching Infrastructure**: Leverages Vercel KV for distributed caching across deployments
 * - **AI Model Management**: Uses centralized Gemini client for consistent AI interactions
 * 
 * @param request - Next.js Request object containing timeline generation parameters
 * @param request.body - JSON request body with the following structure:
 *   - preamble (string): Additional context or instructions for timeline generation
 *   - obj (object): Structured ESG data containing entities, claims, promises, effect flags
 *   - version (string, optional): Version identifier for cache management
 *   - includeDisclosures (boolean): Flag for including disclosure-related events
 * 
 * @returns Promise resolving to Next.js Response object with JSON timeline data or error
 *   - Success (200): `{ response: TimelineEvent[] }` with chronologically sorted events
 *   - Error (500): `{ error: string }` with descriptive error message for client handling
 * 
 * @throws {Error} JSON parsing errors when AI response format is invalid
 * @throws {Error} Google Gemini API errors for authentication, quota, or network issues
 * @throws {Error} Cache operation errors for Vercel KV storage failures
 * 
 * @example
 * ```typescript
 * // Successful timeline generation response
 * {
 *   "response": [
 *     {
 *       "year": "2020",
 *       "event": "Carbon Neutrality Commitment",
 *       "summary": "Microsoft announces carbon negative by 2030",
 *       "description": "Microsoft commits to being carbon negative by 2030 and removing all historical emissions by 2050",
 *       "source": "promise",
 *       "source_id": "msft_carbon_promise_2020"
 *     },
 *     {
 *       "year": "2024", 
 *       "event": "Renewable Energy Milestone",
 *       "summary": "Achieved 100% renewable energy across global operations",
 *       "description": "Microsoft reaches 100% renewable energy milestone ahead of schedule",
 *       "source": "claim",
 *       "source_id": "msft_renewable_claim_2024"
 *     }
 *   ]
 * }
 * ```
 * 
 * @see {@link https://nextjs.org/docs/app/api-reference/functions/request} Next.js Request Object
 * @see {@link https://nextjs.org/docs/app/api-reference/functions/response} Next.js Response Object
 * @see {@link ../report/gemini-client.ts#generateReportContent} Gemini content generation function
 * @see {@link https://vercel.com/docs/storage/vercel-kv/kv-reference} Vercel KV API Reference
 */
export async function POST(request: Request) {
    const { preamble, obj, version, includeDisclosures } = await request.json();
    const supabase = await createClient();

    // Create a cache key using the preamble, object content, and includeDisclosures setting
    // Updated version to 1.16 to clear cache after fixing chronological ordering
    const cacheKey = `gemini-timeline-${crypto.createHash('md5').update("1.16:" + JSON.stringify(obj) + ":" + includeDisclosures).digest('hex')}`;

    // Check cache first
    const cachedData = await kv.hgetall(cacheKey);
    if (cachedData && caching) {
        console.log("cachedData", cachedData);
        return new Response(JSON.stringify({
            response: cachedData.timeline,
        }));
    }

    try {
        console.log("obj", obj);
        console.log("includeDisclosures setting:", includeDisclosures);
        let promptText = `You are a historian tasked with creating timelines from structured data. Given the input JSON below, generate a JSON array where each element represents an event with the following properties:
- \`year\`: The year of the event (from the input data). Include ALL years present in the data, including recent years like 2024, 2025 etc.
- \`event\`: A brief title of the event.
- \`summary\`: A concise summary of the event.
- \`description\`: A detailed description of the event.
- \`source\`: The source type (\`flag\`, \`claim\`, \`promise\`, \`cherry\`, or \`other\`).
- \`source_id\`: The unique identifier of the source.

IMPORTANT: Include events from ALL years present in the input data. Do not limit or filter by year. If the data contains events from 2024, 2025, or future years, include them.

PRIORITY: Always include 'bookend' events - the earliest dated event and the most recent dated event from the input data. These provide important temporal context for the timeline.

Ensure the response contains no additional text or metadata—just the JSON array. Do not infer or include information not present in the input data. Aim for between 6 and 12 events, selecting the most significant events across ALL available years, ensuring you include the earliest and latest events as bookends.

Here is the input JSON:
\`\`\`json
${JSON.stringify(obj, null, 2)}
\`\`\`

${preamble}`;

        promptText = truncate(promptText, 100000)!;

        // Generate content using the Gemini client directly
        const text = await generateReportContent({
            modelName: AI_MODEL_NAME,
            prompt: `You are a historian who generates accurate JSON timelines from structured data. Only respond with the JSON array.\n\n${promptText}`,
            endpoint: '/api/timeline',
            entityName: 'Timeline',
        });

        console.log("Received complete response");

        // Extract the JSON array from the response
        let responseContent = text.trim();

        // Try to extract JSON if it's wrapped in markdown code blocks
        if (responseContent.includes('```json')) {
            responseContent = responseContent.split('```json')[1].split('```')[0].trim();
        } else if (responseContent.includes('```')) {
            responseContent = responseContent.split('```')[1].split('```')[0].trim();
        }

        // Find the JSON array in the response
        const startIndex = responseContent.indexOf('[');
        const endIndex = responseContent.lastIndexOf(']') + 1;

        if (startIndex >= 0 && endIndex > startIndex) {
            responseContent = responseContent.substring(startIndex, endIndex);
        }

        // Ensure it's a valid JSON array
        if (!responseContent.startsWith('[') || !responseContent.endsWith(']')) {
            console.error('Response is not a valid JSON array:', responseContent);
            return new Response(
                JSON.stringify({ error: 'Failed to generate valid timeline' }),
                { status: 500, headers: { 'Content-Type': 'application/json' } }
            );
        }

        try {
            const timeline = JSON.parse(responseContent);
            console.log("Parsed timeline before sorting:", timeline);

            // Sort timeline events chronologically by year (ascending order - oldest first)
            const sortedTimeline = timeline.sort((a: any, b: any) => {
                const yearA = parseInt(a.year) || 0;
                const yearB = parseInt(b.year) || 0;
                return yearA - yearB;
            });

            console.log("Sorted timeline:", sortedTimeline);

            // Cache the sorted timeline
            await kv.hset(cacheKey, { timeline: sortedTimeline });

            // Return the sorted timeline as JSON
            return new Response(
                JSON.stringify({ response: sortedTimeline }),
                { headers: { 'Content-Type': 'application/json' } }
            );
        } catch (error) {
            console.error('Error parsing or filtering timeline:', error);
            return new Response(
                JSON.stringify({ error: 'Failed to process timeline data' }),
                { status: 500, headers: { 'Content-Type': 'application/json' } }
            );
        }
    } catch (error) {
        console.error('Error with Google Gemini API:', error);
        return new Response(
            JSON.stringify({ error: 'Failed to fetch response from Google Gemini' }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}
