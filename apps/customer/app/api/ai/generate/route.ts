/**
 * Next.js App Router API Route Handler for AI-Powered Writing Assistant
 * 
 * This API endpoint provides AI-powered writing assistance and text generation capabilities 
 * for document editing within the EkoIntelligence ESG platform. The route leverages Google's 
 * Gemini 2.0 Flash Experimental model to help users improve writing, fix grammar and spelling, 
 * generate content, and enhance document quality through both streaming and non-streaming AI responses.
 *
 * ## Core Functionality
 * - **AI Writing Assistant**: Intelligent text generation and editing using Google Gemini 2.0 Flash Experimental
 * - **Document Context Awareness**: Incorporates full document content and selected text for contextual assistance
 * - **Dual Response Modes**: Supports both streaming and non-streaming text generation based on client needs
 * - **Edge Runtime Optimized**: Runs on Vercel Edge Runtime for fast global response times
 * - **Grammar & Spelling Correction**: Focused on improving text quality while preserving author voice
 * - **Contextual Text Enhancement**: Uses document context to provide relevant and coherent improvements
 * - **Selective Text Processing**: Can work with specific text selections or entire document content
 *
 * ## Request Structure
 * - **HTTP Method**: POST
 * - **Runtime**: Edge (Vercel Edge Runtime for optimal performance)
 * - **Content-Type**: application/json
 * - **Body Parameters**:
 *   - `prompt` (required, string): User's instruction for the AI assistant (e.g., "Fix grammar", "Improve clarity")
 *   - `context` (optional, string): Additional context or instructions for the AI
 *   - `selectedText` (optional, string): Specific text selection to work with
 *   - `documentContent` (optional, string): Full document content for context (truncated to 2000 chars)
 *   - `stream` (optional, boolean): Whether to return streaming response (default: false)
 *
 * ## Response Formats
 * The endpoint supports two response modes based on the `stream` parameter:
 *
 * ### Non-Streaming Response (Default)
 * Returns completed text generation as JSON:
 * ```json
 * {
 *   "text": "Improved or generated text content",
 *   "success": true
 * }
 * ```
 *
 * ### Streaming Response (`stream: true`)
 * Returns a text stream response with progressive text generation, suitable for real-time UI updates
 * and improved user experience during longer text generation tasks.
 *
 * ## AI Assistant Guidelines
 * The system prompt configures the AI to follow specific writing assistant principles:
 * 1. **Focused Improvements**: When working with selected text, provide only the improved version
 * 2. **Format Preservation**: Maintain original structure and formatting unless asked to change
 * 3. **Concise Output**: Be focused on the specific task without meta-commentary
 * 4. **Voice Preservation**: Maintain the author's original voice and style during corrections
 * 5. **Error-Focused Corrections**: Fix spelling and grammar while preserving meaning
 * 6. **Clarity Enhancement**: Improve readability while maintaining authorial intent
 *
 * ## Model Configuration
 * - **Model**: Google Gemini 2.0 Flash Experimental (`gemini-2.0-flash-exp`)
 * - **Temperature**: 0.3 (balanced creativity and consistency)
 * - **Max Output Tokens**: 1,000 tokens (suitable for focused text improvements)
 * - **Provider**: Vercel AI SDK with Google AI integration
 *
 * ## Context Handling
 * - **Selected Text Priority**: Processes specific text selections with highest priority
 * - **Document Context**: Uses full document content (first 2000 characters) for contextual understanding
 * - **Additional Context**: Incorporates user-provided context for more targeted assistance
 * - **Smart Truncation**: Automatically truncates long document content to fit within token limits
 *
 * ## System Architecture Integration
 * This route integrates with the broader EkoIntelligence document editing system:
 * - **TipTap Editor**: Supports the advanced TipTap-based document editor
 * - **Collaborative Editing**: Works alongside real-time collaborative editing features
 * - **ESG Document Processing**: Optimized for ESG reports, sustainability documents, and analytical content
 * - **Customer App Integration**: Part of the customer-facing application suite
 * - **AI-Powered Content Pipeline**: Complements other AI features like claims analysis and report generation
 *
 * ## Error Handling
 * Comprehensive error handling with structured responses:
 * - **Input Validation**: Validates required prompt parameter
 * - **AI Generation Errors**: Catches and reports AI model failures
 * - **Network Timeouts**: Handles model timeout and connectivity issues
 * - **Structured Error Responses**: Returns JSON error objects with success flags
 *
 * ## Performance Characteristics
 * - **Edge Runtime**: Optimized for fast global response times
 * - **Streaming Support**: Reduces perceived latency for longer generations
 * - **Model Selection**: Uses fast Gemini Flash model for responsive interactions
 * - **Context Optimization**: Intelligent document content truncation for performance
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://sdk.vercel.ai/docs Vercel AI SDK Documentation
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation  
 * @see https://vercel.com/docs/functions/edge-functions Vercel Edge Runtime
 * @see {@link ../edit-document/route.ts} AI Document Editing Route
 * @see {@link ../chat/route.ts} AI Chat Interface Route
 * <AUTHOR>
 * @updated 2025-07-23
 * @description AI-powered writing assistant API for text generation, grammar correction, and content improvement within the EkoIntelligence ESG platform
 * @example ```bash
 * curl -X POST 'http://localhost:3000/api/ai/generate' \
 *   -H 'Content-Type: application/json' \
 *   -d '{
 *     "prompt": "Fix grammar and improve clarity",
 *     "selectedText": "The companies performance was good but they need improve there sustainability practices.",
 *     "stream": false
 *   }'
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { NextRequest, NextResponse } from 'next/server'
import { google } from '@ai-sdk/google'
import { generateText, streamText } from 'ai'

export const runtime = 'edge'

interface GenerateRequest {
  prompt: string
  context?: string
  selectedText?: string
  documentContent?: string
  stream?: boolean
}

export async function POST(request: NextRequest) {
  try {
    const body: GenerateRequest = await request.json()
    const { prompt, context, selectedText, documentContent, stream = false } = body

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      )
    }

    // Build the system prompt for the AI assistant
    const systemPrompt = `You are an AI writing assistant helping with document editing. Follow these guidelines:

1. If working with selected text, provide only the improved version of that text
2. Maintain the original format and structure unless specifically asked to change it
3. Be concise and focused on the specific task
4. Do not add explanations or meta-commentary unless requested
5. For spelling corrections, fix errors while preserving the original meaning and style
6. For grammar improvements, enhance clarity while maintaining the author's voice
7. Return only the corrected/improved text, nothing else`

    // Build the user prompt with context
    let userPrompt = `Task: ${prompt}`

    if (selectedText) {
      userPrompt += `\n\nSelected text to work with: "${selectedText}"`
    }

    if (context) {
      userPrompt += `\n\nAdditional context: ${context}`
    }

    if (documentContent) {
      userPrompt += `\n\nFull document context (for reference): ${documentContent.substring(0, 2000)}${documentContent.length > 2000 ? '...' : ''}`
    }

    // Use Gemini Flash model for fast responses
    const model = google('gemini-2.0-flash-exp')

    if (stream) {
      // Return streaming response
      const result = streamText({
        model,
        system: systemPrompt,
        prompt: userPrompt,
        temperature: 0.3,
        maxTokens: 1000,
      })

      return result.toTextStreamResponse()
    } else {
      // Return non-streaming response
      const result = await generateText({
        model,
        system: systemPrompt,
        prompt: userPrompt,
        temperature: 0.3,
        maxTokens: 1000,
      })

      return NextResponse.json({
        text: result.text,
        success: true
      })
    }
  } catch (error) {
    console.error('AI generation error:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'AI generation failed',
        success: false
      },
      { status: 500 }
    )
  }
}
