/**
 * Next.js App Router API Route Handler for AI-Powered Document Chat Interface
 * doc-by-claude
 *
 * This API endpoint provides intelligent conversational AI capabilities for document editing and analysis
 * within the EkoIntelligence ESG platform. The route enables real-time streaming conversations between
 * users and Google Gemini AI models, supporting both general chat interactions and direct document
 * editing through JSON Patch operations on TipTap editor schemas.
 *
 * ## Core Functionality
 * - **Streaming AI Chat**: Real-time conversational interface using Google Gemini 2.0 Flash Experimental model
 * - **Document Context Integration**: Incorporates TipTap document content and schema for context-aware responses
 * - **Dual Response Modes**: Supports both conversational responses and direct document editing operations
 * - **JSON Patch Document Editing**: Enables AI to make precise document modifications using RFC 6902 JSON Patch standard
 * - **Robust JSON Parsing**: Uses jsonrepair library to handle malformed AI responses gracefully
 * - **Schema-Aware Editing**: Validates all document modifications against provided TipTap editor schema
 * - **Conversation History Management**: Maintains context with the last 5 conversation messages
 *
 * ## Request Structure
 * - **HTTP Method**: POST
 * - **Content-Type**: application/json
 * - **Body Parameters**:
 *   - `messages` (required): Array of conversation messages with role ('user'|'assistant'|'system') and content
 *   - `documentContent` (optional): Stringified JSON of current TipTap document structure
 *   - `documentId` (optional): Unique identifier for the document being edited
 *   - `schema` (optional): Serialized TipTap schema defining allowed nodes and marks for document editing
 *
 * ## Response Formats
 * The AI can respond in two distinct modes:
 * 
 * ### Chat Mode Response
 * For conversational interactions, questions, explanations, and suggestions:
 * ```json
 * {
 *   "type": "chat",
 *   "content": "Conversational response content"
 * }
 * ```
 *
 * ### Edit Mode Response  
 * For direct document modifications when user requests edits:
 * ```json
 * {
 *   "type": "edit", 
 *   "description": "Brief description of the edit operation",
 *   "patch": [
 *     {"op": "add", "path": "/content/0", "value": {"type": "paragraph", "content": [{"type": "text", "text": "New content"}]}},
 *     {"op": "replace", "path": "/content/1/content/0/text", "value": "Updated text"},
 *     {"op": "remove", "path": "/content/2"}
 *   ]
 * }
 * ```
 *
 * ## Streaming Protocol
 * The endpoint uses Server-Sent Events (SSE) for real-time streaming:
 * - **Stream Chunks**: Incremental AI response deltas with timestamps
 * - **Final Response**: Complete parsed response with success status and message metadata
 * - **Error Handling**: Graceful error responses with fallback to chat mode
 * - **Completion Signal**: '[DONE]' marker indicates stream completion
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with Edge Runtime support for optimal performance
 * - **AI SDK (Vercel)**: Unified interface for LLM integration with streaming text generation capabilities  
 * - **Google AI SDK**: Direct integration with Gemini 2.0 Flash Experimental model via @ai-sdk/google
 * - **JSONRepair**: Robust JSON parsing and repair for handling malformed AI model responses
 * - **TipTap Editor Schema**: Rich text editor framework with extensible node and mark system
 * - **JSON Patch (RFC 6902)**: Standard for expressing precise document modification operations
 *
 * ## Technical Architecture
 * - **Edge Runtime**: Deployed on Vercel Edge Functions for global low-latency response
 * - **Streaming Response**: Uses ReadableStream for real-time AI response delivery
 * - **Schema Validation**: Ensures all AI-generated edits conform to TipTap editor capabilities
 * - **Error Recovery**: Multiple fallback mechanisms for JSON parsing and response validation
 * - **Context Management**: Intelligent prompt construction with document state and conversation history
 *
 * ## Integration with EkoIntelligence Platform
 * This route serves as the AI backend for the document editor within the ESG analysis platform:
 * - **Document Editor**: TipTap-based collaborative editor with AI assistance features
 * - **ESG Analysis**: Context-aware AI that understands ESG terminology and analysis patterns
 * - **Real-time Collaboration**: Integrates with Y.js collaborative editing infrastructure
 * - **Report Generation**: Supports AI-assisted content creation for ESG reports and analysis
 *
 * ## Security and Performance
 * - **Edge Runtime**: Reduced latency and improved global performance
 * - **Input Validation**: Comprehensive request validation with detailed error responses
 * - **Rate Limiting**: Inherits from platform-level rate limiting and authentication
 * - **Schema Enforcement**: Prevents unauthorized document structure modifications
 * - **Token Management**: Configurable temperature (0.3) and token limits (2000) for consistent responses
 *
 * @see {@link https://sdk.vercel.ai/docs/ai-sdk-core/generating-text#streaming-text-generation} AI SDK streaming text generation
 * @see {@link https://ai.google.dev/models/gemini} Google Gemini 2.0 Flash model documentation  
 * @see {@link https://tiptap.dev/guide/custom-extensions} TipTap custom extensions and schema
 * @see {@link https://datatracker.ietf.org/doc/html/rfc6902} RFC 6902 JSON Patch specification
 * @see {@link https://nextjs.org/docs/app/building-your-application/routing/route-handlers} Next.js App Router route handlers
 * @see {@link https://vercel.com/docs/functions/edge-functions} Vercel Edge Functions runtime
 *
 * <AUTHOR>  
 * @updated 2025-07-23
 * @docgen doc-by-claude
 * @copyright 2025 EkoIntelligence Ltd. All rights reserved.
 *
 * @example
 * ```typescript
 * // Chat request for conversational interaction
 * const chatResponse = await fetch('/api/ai/chat', {
 *   method: 'POST',
 *   headers: { 'Content-Type': 'application/json' },
 *   body: JSON.stringify({
 *     messages: [
 *       { role: 'user', content: 'Explain the concept of greenwashing in ESG reporting' }
 *     ]
 *   })
 * });
 *
 * // Document editing request with TipTap context
 * const editResponse = await fetch('/api/ai/chat', {
 *   method: 'POST', 
 *   headers: { 'Content-Type': 'application/json' },
 *   body: JSON.stringify({
 *     messages: [
 *       { role: 'user', content: 'Add a heading "ESG Analysis Summary" at the beginning' }
 *     ],
 *     documentContent: JSON.stringify(tiptapDocument),
 *     documentId: 'doc-123',
 *     schema: tiptapSchema
 *   })
 * });
 * ```
 */

import { NextRequest, NextResponse } from 'next/server'
import { google } from '@ai-sdk/google'
import { streamText } from 'ai'
import { jsonrepair } from 'jsonrepair'

export const runtime = 'edge'

interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  tool_calls?: any[]
}

interface SerializedSchema {
  nodes: Record<string, any>
  marks: Record<string, any>
}

interface ChatRequest {
  messages: ChatMessage[]
  documentContent?: string
  documentId?: string
  schema?: SerializedSchema | null // Serialized TipTap schema
}

export async function POST(request: NextRequest) {
  try {
    const body: ChatRequest = await request.json()
    const { messages, documentContent, documentId, schema } = body

    // Log schema information for verification (temporary)
    console.log('AI Chat API - Schema received:', {
      hasSchema: !!schema,
      schemaNodes: schema?.nodes ? Object.keys(schema.nodes) : 'No nodes',
      schemaMarks: schema?.marks ? Object.keys(schema.marks) : 'No marks'
    })

    if (!messages || messages.length === 0) {
      return NextResponse.json(
        { error: 'Messages are required' },
        { status: 400 }
      )
    }

    // Get the latest user message
    const userMessage = messages[messages.length - 1]
    if (userMessage.role !== 'user') {
      return NextResponse.json(
        { error: 'Last message must be from user' },
        { status: 400 }
      )
    }

    // Build conversation context
    const conversationHistory = messages
      .slice(-5) // Keep last 5 messages for context
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n')

    // Parse document content if it's a string
    let documentJson
    try {
      documentJson = documentContent ? JSON.parse(documentContent) : null
    } catch {
      documentJson = null
    }

    // Build the system prompt
    const systemPrompt = `You are an AI assistant helping with document editing and writing. You can either provide conversational responses or make direct edits to documents.

You must respond with a JSON object in one of these two formats:

1. For conversational responses (questions, explanations, suggestions):
{
  "type": "chat",
  "content": "Your conversational response here"
}

2. For document edits (when user asks to add, edit, modify, insert, remove, etc.):
{
  "type": "edit",
  "description": "Brief description of what you're doing",
  "patch": [array of JSON Patch operations]
}

Examples of JSON Patch operations:
- Add content: {"op": "add", "path": "/content/0", "value": {"type": "paragraph", "content": [{"type": "text", "text": "New text"}]}}
- Replace content: {"op": "replace", "path": "/content/0/content/0/text", "value": "Updated text"}
- Remove content: {"op": "remove", "path": "/content/1"}

Important rules:
- Always return valid JSON in one of the two formats above
- For edits, ensure all patch paths are valid for the current document structure
- Use ONLY node and mark types that are defined in the provided TipTap schema
- Respect the schema's content models and attribute definitions
- For headings, include attrs: {"level": 1-6}
- For text nodes, use {"type": "text", "text": "content"}
- When creating new nodes, ensure they conform to the schema's content rules
- Be helpful and concise in your responses`

    // Build the user prompt with context
    let userPrompt = `${userMessage.content}

Current document structure (TipTap JSON):
${documentJson ? JSON.stringify(documentJson, null, 2) : 'No document content provided'}

TipTap Editor Schema:
${schema ? JSON.stringify(schema, null, 2) : 'No schema provided'}

Conversation history:
${conversationHistory}`

    // Use Gemini Flash model for fast responses
    const model = google('gemini-2.0-flash-exp')

    // Create streaming response
    const result = streamText({
      model,
      system: systemPrompt,
      prompt: userPrompt,
      temperature: 0.3,
      maxTokens: 2000,
    })

    // Create a custom streaming response that accumulates the AI response
    // and then parses it as JSON when complete
    const encoder = new TextEncoder()
    let accumulatedText = ''

    const responseStream = new ReadableStream({
      async start(controller) {
        try {
          // Stream the AI response and accumulate it
          for await (const delta of result.textStream) {
            accumulatedText += delta

            // Send streaming chunks to show progress
            const streamChunk = {
              type: 'stream',
              delta: delta,
              timestamp: new Date().toISOString()
            }
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(streamChunk)}\n\n`))
          }

          // Parse the complete response
          let aiResponse
          try {
            // Clean up the accumulated text and try to parse as JSON
            const cleanedText = accumulatedText.trim()

            // Check if it looks like JSON (starts with { and ends with })
            if (cleanedText.startsWith('{') && cleanedText.endsWith('}')) {
              // Use jsonrepair to handle malformed JSON
              const repairedJson = jsonrepair(cleanedText)
              aiResponse = JSON.parse(repairedJson)
            } else {
              // If it doesn't look like JSON, treat as plain text chat response
              aiResponse = {
                type: 'chat',
                content: cleanedText
              }
            }
          } catch (parseError) {
            console.error('Failed to parse AI response as JSON:', accumulatedText, parseError)
            // Fallback to treating as chat response
            aiResponse = {
              type: 'chat',
              content: accumulatedText.trim()
            }
          }

          // Validate response structure
          if (!aiResponse.type || (aiResponse.type !== 'chat' && aiResponse.type !== 'edit')) {
            // Fallback to chat response
            aiResponse = {
              type: 'chat',
              content: accumulatedText.trim()
            }
          }

          // Send the final parsed response
          const finalResponse = {
            type: 'final',
            aiResponse: {
              type: aiResponse.type,
              message: {
                role: 'assistant',
                content: aiResponse.type === 'chat' ? aiResponse.content : aiResponse.description,
                timestamp: new Date().toISOString()
              },
              ...(aiResponse.type === 'edit' && {
                patch: aiResponse.patch,
                description: aiResponse.description
              }),
              success: true
            }
          }

          controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalResponse)}\n\n`))
          controller.enqueue(encoder.encode('data: [DONE]\n\n'))
          controller.close()
        } catch (error) {
          console.error('Streaming error:', error)
          const errorResponse = {
            type: 'error',
            error: error instanceof Error ? error.message : 'Streaming failed',
            timestamp: new Date().toISOString()
          }
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorResponse)}\n\n`))
          controller.close()
        }
      }
    })

    return new Response(responseStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Transfer-Encoding': 'chunked',
      },
    })
  } catch (error) {
    console.error('AI chat error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'AI chat failed',
        success: false
      },
      { status: 500 }
    )
  }
}
