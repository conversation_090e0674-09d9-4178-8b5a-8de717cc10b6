/**
 * Next.js App Router API Route Handler for AI-Powered ESG Report Summarization
 * 
 * This API endpoint provides intelligent ESG report summarization capabilities for the EkoIntelligence 
 * platform, enabling users to consolidate multiple report sections into a comprehensive, cohesive 
 * narrative. The route leverages OpenAI's GPT-4o model with sophisticated caching mechanisms to 
 * ensure efficient processing of large ESG documents while maintaining citation integrity and 
 * professional formatting standards.
 *
 * ## Core Functionality
 * - **Report Section Consolidation**: Merges multiple ESG report sections into a seamless 2000-word comprehensive report
 * - **Citation Preservation**: Maintains original citations in [^citation_id] format throughout the consolidation process
 * - **Professional ESG Writing**: Uses specialized GPT-4o prompting for objective, diplomatic corporate ESG reporting
 * - **Caching System**: Vercel KV-based caching with content hash keys to reduce API costs and improve response times
 * - **Markdown Formatting**: Preserves and enhances markdown formatting for professional document presentation
 * - **Repetition Removal**: Intelligently removes redundant content while preserving all factual details
 * - **Content Integration**: Joins disparate sections into a flowing, coherent narrative suitable for stakeholder review
 * - **Diplomatic Tone**: Maintains objective criticism while preserving professional client relationships
 *
 * ## Request Structure
 * - **HTTP Method**: POST (required for complex content processing operations)
 * - **Content-Type**: application/json
 * - **Timeout**: 180 seconds maximum execution time for comprehensive report processing
 * - **Body Parameters**:
 *   - `sections` (required, array): Array of ESG report sections to be consolidated, each containing text content with embedded citations
 *
 * ## Response Format
 * 
 * ### Success Response (200)
 * ```json
 * {
 *   "response": "Comprehensive ESG report narrative with preserved citations [^1234] and professional formatting..."
 * }
 * ```
 *
 * ### Error Responses
 * ```json
 * // 500 Internal Server Error - OpenAI API failure
 * {
 *   "error": "Failed to fetch response from OpenAI"
 * }
 * 
 * // 400 Bad Request - Invalid request structure
 * {
 *   "error": "Invalid sections data provided"
 * }
 * ```
 *
 * ## AI Model Configuration & Processing
 * The route implements specialized ESG report writing using OpenAI's advanced models:
 * - **Model**: GPT-4o for superior reasoning and context understanding in ESG domains
 * - **System Prompt**: Configured for objective, diplomatic corporate ESG report writing
 * - **Target Length**: Optimized for comprehensive 2000-word reports suitable for executive review
 * - **Content Processing**: Intelligent deduplication and narrative flow optimization
 * - **Professional Standards**: Maintains factual accuracy while ensuring diplomatic presentation
 *
 * ## ESG Report Writing Guidelines
 * The AI follows strict professional ESG reporting standards:
 * 1. **Objective Analysis**: Provides factual, evidence-based assessment of ESG performance
 * 2. **Citation Integrity**: Preserves all original citations in standardized [^citation_id] format
 * 3. **Diplomatic Criticism**: Maintains professional tone while addressing performance gaps
 * 4. **Comprehensive Coverage**: Ensures all critical ESG topics are adequately addressed
 * 5. **Stakeholder Communication**: Optimized for investor, regulator, and management consumption
 * 6. **Markdown Preservation**: Maintains structured formatting for professional presentation
 * 7. **Factual Accuracy**: No loss of factual detail, only redundancy removal
 * 8. **Narrative Flow**: Creates seamless transitions between consolidated sections
 *
 * ## Performance & Caching
 * - **Content-Based Cache Keys**: MD5 hash of sections array ensures accurate cache hits
 * - **Vercel KV Integration**: Distributed caching system for improved global performance
 * - **Cache Strategy**: Persistent caching of processed reports to minimize API costs
 * - **Cost Optimization**: Caching reduces OpenAI API calls for identical section combinations
 * - **Response Time**: Cached responses provide near-instantaneous delivery for repeated requests
 *
 * ## System Integration
 * This route integrates with the broader EkoIntelligence ESG analysis ecosystem:
 * - **Document Editor**: Powers report consolidation features in TipTap collaborative editor
 * - **ESG Analysis Pipeline**: Supports final report generation from multiple analysis outputs
 * - **Citation System**: Works with platform's citation tracking and reference management
 * - **Report Management**: Enables efficient consolidation of sectioned ESG analysis results
 * - **Client Communication**: Produces stakeholder-ready reports from technical analysis data
 *
 * ## Security & Validation
 * - **Input Sanitization**: Validates sections array structure and content before processing
 * - **API Key Security**: Uses environment variables for secure OpenAI API key management
 * - **Error Handling**: Comprehensive error catching with detailed logging for troubleshooting
 * - **Cache Security**: Secure hash-based cache keys prevent unauthorized access to cached content
 * - **Rate Limiting**: Relies on OpenAI's built-in rate limiting for API protection
 *
 * ## Usage Context
 * This endpoint is typically used in the final stages of ESG report generation, where:
 * - Multiple analysis sections have been generated by different AI models or analysis pipelines
 * - Citations have been properly formatted and embedded in section content
 * - A comprehensive, stakeholder-ready report is needed for distribution
 * - Professional presentation and diplomatic tone are required for client relationships
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://platform.openai.com/docs/api-reference/chat OpenAI Chat Completions API
 * @see https://vercel.com/docs/storage/vercel-kv Vercel KV Storage Documentation
 * @see {@link ../report/summarize/route.ts} Related report summarization endpoint
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This API endpoint provides intelligent ESG report summarization capabilities for the EkoIntelligence platform, enabling users to consolidate multiple report sections into a comprehensive, cohesive narrative.
 * @example ```bash
curl -X POST 'http://localhost:3000/api/summarize-report' \
  -H 'Content-Type: application/json' \
  -d '{
    "sections": [
      {
        "title": "Environmental Impact",
        "content": "Company reduced carbon emissions by 15% [^1234]..."
      },
      {
        "title": "Social Responsibility", 
        "content": "Employee satisfaction increased to 87% [^5678]..."
      }
    ]
  }'
```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import OpenAI from 'openai';
import {NextResponse} from 'next/server';
import {kv} from "@vercel/kv";
import crypto from 'crypto';
import Anthropic from "@anthropic-ai/sdk";

export const maxDuration =180;

export async function POST(request: Request) {
    const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
    });

    const anthropic = new Anthropic();
    const {sections} = await request.json();

    // Create a cache key using the preamble and object content
    const cacheKey = `14-${crypto.createHash('md5').update(JSON.stringify(sections)).digest('hex')}`;

    return await kv.hgetall(cacheKey).then(async (cachedData: any) => {
        if (cachedData) {
            return NextResponse.json({
                response: cachedData.response,
            });
        }


        try {
            const completion = await openai.chat.completions.create({
                model: 'gpt-4o',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a corporate ESG report writer, you are objective and factual but knowing you are criticising your clients, you remain diplomatic.'
                    },
                    {
                        role: 'user',
                        content: `
                        Please preserve citations and quotes from the source where possible. Example citation is: "Barclays has been profitting from global warming [^3468]".
                        Please join the following text together into a seamless single long report (aim for 2000 words), preserving the markdown formatting and citations and supplying the improved text as output without any commentary. 
                        Do not lose any factual detail from the report only remove repetition.
                         
                         <sections>
                        ${JSON.stringify(sections, null, 2)}
                        </sections>
                        `
                    }
                ],
            });

            const responseContent = completion.choices[0].message.content;


            await kv.hset(cacheKey, {response:responseContent!});

            return NextResponse.json({
                response: responseContent,
            });
        } catch (error) {
            console.error('Error with OpenAI API:', error);
            return NextResponse.json(
                {error: 'Failed to fetch response from OpenAI'},
                {status: 500}
            );
        }
    });
}
