/**
 * Next.js App Router API Route Handler for AI-Powered ESG Content Summarization
 *
 * This API endpoint provides intelligent summarization capabilities for ESG (Environmental, Social, 
 * Governance) content using Google's Gemini 2.5 Flash model with streaming response functionality. 
 * The route processes complex ESG data objects and generates professional, diplomatic summaries 
 * suitable for client reports while preserving citations and maintaining analytical objectivity.
 *
 * ## Core Functionality
 * - **AI-Powered Summarization**: Uses Google Gemini 2.5 Flash for fast, accurate ESG content processing
 * - **Streaming Response**: Provides real-time text generation with chunked data delivery
 * - **Citation Preservation**: Maintains original source citations in [^citation_id] format when requested
 * - **Professional Tone**: Configured for diplomatic, objective ESG reporting suitable for client consumption
 * - **Caching System**: Vercel KV-based caching with MD5 hash keys for performance optimization
 * - **Content Truncation**: Automatic text truncation to 400,000 characters to fit model limits
 * - **ESG Specialization**: Optimized prompting for environmental, social, and governance content analysis
 *
 * ## Request Structure
 * - **HTTP Method**: POST (required for complex data processing)
 * - **Content-Type**: application/json
 * - **Timeout**: 180 seconds maximum execution time for complex ESG analysis
 * - **Body Structure**: Single nested `prompt` object containing:
 *   - `preamble` (string): Custom instructions for summary generation context
 *   - `keepCitations` (boolean): Whether to preserve [^citation_id] format citations
 *   - `obj` (object): ESG data object to be summarized (entities, statements, analysis results)
 *   - `key` (string): Unique identifier for cache key generation
 *
 * ## Response Format
 * Returns streaming AI SDK data stream with real-time text generation, compatible with 
 * React hooks like `useChat` and `useCompletion` for frontend integration.
 *
 * ## AI Model Configuration
 * - **Model**: Google Gemini 2.5 Flash (optimized for speed and accuracy)
 * - **Temperature**: 0.2 (low variability for consistent, factual reporting)
 * - **Max Tokens**: 4,096 (sufficient for comprehensive ESG summaries)
 * - **System Prompt**: Configured as professional ESG auditing firm report writer
 * - **Response Cleaning**: Removes XML-style output tags for clean markdown delivery
 *
 * ## ESG Analysis Features
 * - **Entity Analysis**: Processes corporate ESG performance data and statements
 * - **Claims Processing**: Analyzes corporate sustainability claims and commitments
 * - **Impact Assessment**: Summarizes environmental and social impact evaluations
 * - **Risk Analysis**: Consolidates ESG risk factors and materiality assessments
 * - **Stakeholder Communication**: Optimized for investor and regulatory reporting
 *
 * ## Performance & Caching
 * - **Content-Based Cache Keys**: MD5 hash of input parameters ensures accurate cache hits
 * - **Vercel KV Integration**: Distributed caching for improved global performance
 * - **Selective Caching**: Currently disabled (caching = false) for development/testing
 * - **Stream Processing**: Asynchronous text processing with real-time delivery
 * - **Error Handling**: Graceful fallback with detailed error logging
 *
 * ## System Integration
 * This route integrates with the broader EkoIntelligence ESG analysis ecosystem:
 * - **Data Fetchers**: Processes output from various ESG data collection systems
 * - **Report Generation**: Powers summary sections in comprehensive ESG reports
 * - **Dashboard Components**: Provides summarized content for analytical dashboards
 * - **Citation System**: Works with platform's reference and source tracking
 * - **Document Processing**: Supports collaborative document editing workflows
 *
 * ## Security & Validation
 * - **API Key Security**: Uses environment variables for secure Google AI API access
 * - **Input Validation**: Validates prompt structure and content before processing
 * - **Error Boundaries**: Comprehensive error catching with structured JSON responses
 * - **Content Filtering**: Built-in Google AI safety filters for appropriate content
 * - **Cache Security**: Secure hash-based cache keys prevent unauthorized access
 *
 * ## Usage Context
 * This endpoint is typically used for:
 * - Generating executive summaries from detailed ESG analysis data
 * - Creating client-ready content from technical analysis outputs
 * - Processing large datasets into digestible narrative summaries
 * - Supporting real-time dashboard content generation
 * - Enabling collaborative report writing with AI assistance
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js Route Handlers
 * @see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation
 * @see https://sdk.vercel.ai/docs/ai-sdk-core/stream-text AI SDK streamText Documentation
 * @see https://vercel.com/docs/storage/vercel-kv Vercel KV Storage Documentation
 * @see {@link ../../report/summarize/route.ts} Related report summarization endpoint
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This API endpoint provides AI-powered summarization of ESG content using Google Gemini with streaming responses and citation preservation for professional client reporting.
 * @example ```bash
curl -X POST 'http://localhost:3000/api/summarize' \
  -H 'Content-Type: application/json' \
  -d '{
    "prompt": {
      "preamble": "Provide a comprehensive analysis of environmental impact",
      "keepCitations": true,
      "obj": {
        "entity": "Microsoft Corporation",
        "claims": ["Carbon neutral by 2030 [^1234]", "100% renewable energy [^5678]"],
        "analysis": "Strong environmental commitments with measurable targets"
      },
      "key": "msft-env-analysis"
    }
  }'
```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import { streamText } from 'ai'
import { google } from '@ai-sdk/google'
import { kv } from '@vercel/kv'
import crypto from 'crypto'
import { truncate } from '@utils/text-utils'

export const maxDuration = 180;
const caching = false;

export async function POST(request: Request) {
    const { preamble, keepCitations, obj, key } = JSON.parse((await request.json()).prompt);

    // Create a cache key using the preamble and object content
    const cacheKey = `gemini-flash-${key}-${crypto.createHash('md5').update(JSON.stringify({preamble, keepCitations, obj})).digest('hex')}`;

    // Check cache first
    const cachedData = await kv.hgetall(cacheKey);
    if (cachedData && caching) {
        return new Response(JSON.stringify({
            response: cachedData.response,
        }));
    }

    const citations = keepCitations
        ? "Please preserve source where possible. Example citation is: Citations look like: [^3468]. Please do not summarize them at the end, like footnotes, I will do that."
      : 'Please don\'t include the citations in the form of [^123]';

    try {
        console.log("obj", obj);
        let promptText = `
            <instructions>
            ${citations}
            ${preamble}
            <note>Remain upbeat but matter of fact, you are writing a report for a client.</note>
            </instructions>

            Please summarize in prose form, in markdown from the information contained within the <input> tags.
            Now please provide your response in markdown format but do not include an intro or preamble of any kind.

           <input>${JSON.stringify(obj, null, 2)}</input>

            Please summarize in prose form, in markdown from the information contained within the <input> tags.
            DO NOT include any additional information that is not present in the supplied text.
            DO NOT add any formatting or headings.

            ${preamble}
        `;

        promptText = truncate(promptText, 400000)!;

        // Use the AI SDK's streamText function with Gemini Flash Lite
        const result = await streamText({
            model: google('gemini-2.5-flash'),
            prompt: `You are a professional and highly paid report writer for a leading auditing firm specialising in ESG related matters, you are objective and factual but knowing you are criticising your clients, you remain diplomatic. You are not conversational.\n\n${promptText}`,
            temperature: 0.2,
            maxTokens: 4096,
            onFinish: async ({ text }) => {
                console.log("text", text);
                // Clean up the response and store in cache
                const cleanedResponse = text
                    .replace(/[\s\S]*<o>/g, '')
                    .replace(/<\/output>[\s\S]*/g, '')
                    .replace(/<\/o>[\s\S]*/g, '');

                await kv.hset(cacheKey, { response: cleanedResponse });
            },
            onChunk: async ({ chunk }) => {
                console.log("chunk", chunk);
            },
            onError: async ({ error }) => {
                console.error("error", error);
            }
        });

        // console.log("result", result);

        // Return the streaming response
        return result.toDataStreamResponse();
    } catch (error) {
        console.error('Error with Google Gemini API:', error);
        return new Response(
            JSON.stringify({ error: 'Failed to fetch response from Google Gemini' }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}
