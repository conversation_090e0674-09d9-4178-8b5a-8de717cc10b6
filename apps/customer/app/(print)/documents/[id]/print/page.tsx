/**
 * Next.js App Router Print-Optimized Document Viewer Page
 *
 * This Next.js client component provides a specialized print-optimized viewing experience for documents
 * within the ESG Intelligence platform. It renders documents in a printer-friendly format with
 * proper styling for PDF export, dynamic title management for meaningful PDF naming, and seamless
 * integration with the platform's advanced TipTap-based document editor system.
 *
 * ## Core Functionality
 * - **Document Retrieval**: Fetches document data from Supabase `doc_documents` table using document ID parameter
 * - **Print Optimization**: Specialized CSS styling with A4 portrait layout, proper margins, and print-specific classes
 * - **PDF Naming**: Dynamically sets `window.document.title` based on document content for meaningful PDF filenames
 * - **Editor Integration**: Renders documents using EkoDocumentEditor in read-only view mode with disabled interactions
 * - **User Interface**: Provides print button, close window button, and comprehensive loading/error state handling
 * - **Browser Events**: Handles print dialog lifecycle events (beforeprint, afterprint) for enhanced user experience
 *
 * ## Request Flow
 * 1. **Route Parameter Extraction**: Extracts document ID from Next.js dynamic route parameter `[id]`
 * 2. **Document Loading**: Queries Supabase customer database for document metadata, content, and associated data
 * 3. **Title Management**: Sets browser window title to document title or "Untitled Document" for PDF naming
 * 4. **Render Preparation**: Initializes EkoDocumentEditor with document context and read-only configuration
 * 5. **Print Handling**: Manages print dialog events and provides user controls for printing and window closure
 *
 * ## Print Features
 * - **A4 Portrait Layout**: CSS `@page` configuration with 25mm top margin, 20mm sides/bottom margins
 * - **Glass-morphism Preservation**: Maintains platform design consistency with `eko-print-mode` styling
 * - **Content-Optimized Display**: Ensures proper text contrast and layout for print media
 * - **Header Management**: Shows document metadata in screen view, hides for actual printing
 * - **Responsive Behavior**: Adapts layout for both screen preview and print output
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with client-side navigation and dynamic routing
 * - **Supabase**: Database client for secure document data retrieval with Row Level Security (RLS)
 * - **EkoDocumentEditor**: Advanced TipTap-based rich text editor with collaborative features and AI integration
 * - **DocumentProvider**: React context provider for document state management and editor configuration
 * - **Skeleton UI Components**: Loading state components from shadcn/ui for smooth user experience
 *
 * ## System Architecture
 * This print page fits into the broader document management system:
 * - **Document Storage**: Documents stored in `doc_documents` table with content, metadata, and versioning
 * - **Editor System**: EkoDocumentEditor provides rich text rendering with citations, AI content, and collaboration
 * - **Print Workflow**: Accessed via export utilities that save document before opening print dialog
 * - **PDF Generation**: Browser's built-in print-to-PDF functionality with optimized styling
 * - **Route Isolation**: Located in `(print)` route group for dedicated print layout separate from main navigation
 *
 * ## Security & Performance
 * - **Database Access**: Supabase RLS policies ensure users can only access authorized documents
 * - **Client-Side Rendering**: Uses 'use client' directive for browser-specific functionality (window.print, title setting)
 * - **Error Handling**: Comprehensive error states with user-friendly messages and recovery options
 * - **Loading States**: Skeleton UI components provide immediate feedback during document retrieval
 * - **Memory Management**: Proper cleanup of event listeners and component state on unmount
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js App Router Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation  
 * @see https://tiptap.dev/docs/editor/introduction TipTap Editor Documentation
 * @see {@link ../../components/editor/EkoDocumentEditor.tsx} EkoDocumentEditor Component
 * @see {@link ../../components/editor/context/DocumentContext.tsx} Document Context Provider
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Print-optimized document viewer with PDF export functionality and dynamic title management for meaningful file naming
 * @example ```bash
   # Open document print dialog
   window.open('/documents/doc123/print', '_blank');
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import React, { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { createClient } from '@/app/supabase/client'
import { EkoDocumentEditor } from '@/components/editor/EkoDocumentEditor'
import { DocumentProvider } from '@/components/editor/context/DocumentContext'
import { Skeleton } from '@/components/ui/skeleton'

interface Document {
  id: string
  title?: string | null
  content?: string | null
  initial_content?: string | null
  data?: any
  metadata?: any
  created_at?: string | null
  updated_at?: string | null
  entity_id: string
  run_id: number
}

export default function DocumentPrintPage() {
  const params = useParams()
  const [document, setDocument] = useState<Document | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const documentId = params.id as string
  const supabase = createClient()

  // Load document data
  useEffect(() => {
    const loadDocument = async () => {
      if (!documentId) return

      try {
        setLoading(true)
        const { data, error } = await supabase
          .from('doc_documents')
          .select('id, title, content, initial_content, data, metadata, created_at, updated_at, entity_id, run_id')
          .eq('id', documentId)
          .single()

        if (error) {
          throw error
        }

        setDocument(data)
      } catch (err) {
        console.error('Error loading document:', err)
        setError(err instanceof Error ? err.message : 'Failed to load document')
      } finally {
        setLoading(false)
      }
    }

    loadDocument()
  }, [documentId, supabase])

  // Update document title for PDF naming
  useEffect(() => {
    if (document?.title) {
      // Set the page title to the document title for PDF naming
      window.document.title = document.title
    } else if (document && !document.title) {
      // Handle untitled documents
      window.document.title = 'Untitled Document'
    }
    // Note: We don't set a title during loading to avoid flickering
  }, [document])

  // Note: Auto-print removed - users can now view the document before choosing to print

  // Handle print dialog events
  useEffect(() => {
    const handleAfterPrint = () => {
      // Close the window after printing
      // window.close()
    }

    const handleBeforePrint = () => {
      // Optional: Any pre-print setup
      console.log('Preparing to print document:', documentId)
    }

    // Add event listeners
    window.addEventListener('afterprint', handleAfterPrint)
    window.addEventListener('beforeprint', handleBeforePrint)

    // Cleanup
    return () => {
      window.removeEventListener('afterprint', handleAfterPrint)
      window.removeEventListener('beforeprint', handleBeforePrint)
    }
  }, [documentId])

  if (loading) {
    return (
      <div className="min-h-screen bg-white p-8">
        <div className="max-w-4xl mx-auto space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-4/5" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-red-600 mb-2">Error</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.close()}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            Close Window
          </button>
        </div>
      </div>
    )
  }

  if (!document) {
    return (
      <div className="min-h-screen bg-white p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-2">Document Not Found</h1>
          <p className="text-gray-600 mb-4">The requested document could not be found.</p>
          <button
            onClick={() => window.close()}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            Close Window
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white print:bg-white print:text-black print:m-0 print:p-0 eko-print-mode">
      {/* Print-specific styles using Tailwind */}
      <style jsx global>{`
       
          @page {
          size: A4 portrait;
          margin: 25mm 20mm 20mm 20mm;
        }
      `}</style>

      <div className="w-full h-full">
        {/* Document header - only show in non-print view */}
        <div className="print:hidden p-8 border-b">
          <h1 className="text-3xl font-bold mb-2 text-black print:text-black">
            {document.title || 'Untitled Document'}
          </h1>
          {document.updated_at && (
            <p className="text-sm text-gray-600 mb-4 print:text-black">
              Last updated: {new Date(document.updated_at).toLocaleDateString()}
            </p>
          )}

          {/* Print button for non-print view */}
          <div className="mt-4 print:hidden">
            <button
              onClick={() => window.print()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mr-2 print:hidden"
              data-testid="print-document-button"
            >
              Print Document
            </button>
            <button
              onClick={() => window.close()}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 print:hidden"
            >
              Close Window
            </button>
          </div>
        </div>

        {/* Document content using EkoDocumentEditor in view mode */}
        <div className="w-full h-full print:w-full print:h-auto print:max-w-none">
          <DocumentProvider
            documentId={document.id}
            initialEntity={document.entity_id!!}
            initialRun={document.run_id ? document.run_id.toString() : 'latest'}
            onSave={() => {}} // No-op for print mode
          >
            <EkoDocumentEditor
              documentId={document.id}
              citations={document.metadata?.citations || []}
              initialContent={document.initial_content || document.content || ''}
              initialData={document.data}
              viewMode={true}
              editable={false}
              showToolbar={false}
              showCollaboration={false}
              className="w-full h-full print:p-0 print:m-0 print:w-full print:max-w-none print:text-black"
            />
          </DocumentProvider>
        </div>
      </div>
    </div>
  )
}
