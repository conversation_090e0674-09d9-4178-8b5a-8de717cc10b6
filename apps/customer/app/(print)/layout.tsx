/**
 * Next.js App Router Print Layout for EkoIntelligence ESG Analysis Platform
 *
 * This specialized layout component provides authentication-enforced print functionality for document
 * export within the EkoIntelligence ESG analysis platform. It serves as the foundational layout for
 * the `(print)` route group, ensuring secure document access while providing an optimized print
 * environment with clean styling for PDF generation and professional document export.
 *
 * ## Core Functionality
 * - **Authentication Enforcement**: Server-side authentication check using Supabase Auth
 * - **Print Optimization**: Clean, print-friendly styling with white background and black text
 * - **Route Group Isolation**: Dedicated layout for print routes separate from main application navigation
 * - **Context Provision**: Provides AuthProvider context for editor components that require user authentication
 * - **Redirect Protection**: Automatically redirects unauthenticated users to login with return URL preservation
 *
 * ## Authentication Flow
 * **Server-Side Security**: Uses Supabase server client to validate user authentication before rendering
 * any print content. This ensures that only authenticated users can access document print functionality,
 * maintaining the platform's security model while providing seamless integration with the document
 * editor system that requires user context for proper functionality.
 *
 * **Session Validation Process**:
 * 1. **Server Client Initialization**: Creates Supabase server client with cookie-based session handling
 * 2. **User Authentication Check**: Validates current user session using `supabase.auth.getUser()`
 * 3. **Redirect on Failure**: Redirects to login page with `/customer` as return URL if no authenticated user
 * 4. **Context Provision**: Wraps authenticated content with AuthProvider for editor component compatibility
 *
 * ## Route Group Architecture
 * This layout is part of the `(print)` route group in Next.js App Router, which provides route
 * isolation without affecting URL structure. This allows print-specific layouts to exist alongside
 * the main application layout while sharing the same URL patterns:
 *
 * - **Route Isolation**: Print routes have dedicated styling and context separate from main app
 * - **URL Preservation**: URLs like `/documents/[id]/print` work naturally without `/print` prefix
 * - **Layout Inheritance**: Inherits base functionality while providing print-specific optimizations
 * - **Component Compatibility**: Maintains compatibility with editor components that expect AuthProvider
 *
 * ## Print Optimization Features
 * **Clean Print Styling**: The layout provides a minimalist white background with black text that
 * ensures optimal readability and professional appearance in both screen preview and PDF export.
 * Print-specific CSS classes ensure consistent rendering across different browsers and print
 * scenarios while maintaining the platform's document quality standards.
 *
 * **Browser Compatibility**: Print styles are optimized for:
 * - Chrome/Chromium print-to-PDF functionality
 * - Safari print preview and PDF export
 * - Firefox print dialog compatibility
 * - Cross-browser print margin consistency
 *
 * ## Integration Context
 * This layout serves the broader document management ecosystem:
 * - **Document Export**: Enables PDF generation from rich-text documents with proper authentication
 * - **Editor Integration**: Provides necessary authentication context for TipTap editor components
 * - **Security Compliance**: Maintains Row Level Security (RLS) policies through authenticated sessions
 * - **User Experience**: Seamless print functionality without compromising security or functionality
 *
 * ## Technical Architecture
 * **Server-Side Rendering**: Uses Next.js 15 App Router's server components for authentication
 * validation before client-side rendering. This ensures security checks happen on the server,
 * preventing unauthorized access attempts and maintaining session integrity throughout the
 * print workflow.
 *
 * **Authentication Provider Pattern**: Wraps print content with AuthProvider to ensure editor
 * components have access to user context, profile data, and feature flags needed for proper
 * document rendering and security enforcement.
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-groups Next.js Route Groups Documentation
 * @see https://supabase.com/docs/guides/auth/server-side Supabase Server-Side Authentication
 * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/print CSS Print Media Documentation
 * @see /Users/<USER>/worktrees/279/apps/customer/app/supabase/server.ts Supabase Server Client Configuration
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/auth/auth-context.tsx Authentication Context Provider
 * @see /Users/<USER>/worktrees/279/apps/customer/app/(print)/documents/[id]/print/page.tsx Document Print Page Implementation
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Server-side authenticated print layout providing secure document export functionality with clean print optimization
 * @example
 * ```typescript
 * // Print layout usage - automatically applied to (print) route group
 * // URL: /documents/abc123/print
 * // Layout: app/(print)/layout.tsx (this file)
 * // Page: app/(print)/documents/[id]/print/page.tsx
 * 
 * // Authentication check happens server-side
 * const user = await supabase.auth.getUser();
 * if (!user) redirect('/login?next=/customer');
 * 
 * // Clean print environment with authentication context
 * return (
 *   <AuthProvider>
 *     <div className="print-optimized-container">
 *       {/\* Print page content with editor authentication *\/}
 *     </div>
 *   </AuthProvider>
 * );
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import '../globals.css'
import { Inter } from 'next/font/google'
import { createClient } from '@/app/supabase/server'
import { redirect } from 'next/navigation'
import { AuthProvider } from '@/components/context/auth/auth-context'

const inter = Inter({
    subsets: ['latin'],
    display: 'swap',
})

export default async function PrintLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const supabase = await createClient();

    const user = (await supabase.auth.getUser()).data.user;
    if (!user) {
        return redirect("/login?next=/customer");
    }

    return (
        <AuthProvider>
            <div className="min-h-screen w-full bg-white text-black print:bg-white print:text-black">
                {children}
            </div>
        </AuthProvider>
    );
}
