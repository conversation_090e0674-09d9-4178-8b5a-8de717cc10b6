/**
 * Next.js App Router Public Document Sharing Page Component
 *
 * This React Server Component provides secure public access to shared documents within the EkoIntelligence
 * ESG analysis platform. It handles document retrieval, validation, and rendering for public consumption,
 * implementing comprehensive security checks and user-friendly error handling to ensure only authorized 
 * documents can be viewed by anonymous users.
 *
 * ## Core Functionality
 * - **Public Document Access**: Enables secure sharing of ESG reports and analysis documents via unique URLs
 * - **Dual Security Model**: Validates documents using both `is_public` column and legacy `metadata.isPublic` property
 * - **Row Level Security (RLS)**: Leverages Supabase RLS policies to enforce database-level access controls
 * - **SEO Optimization**: Generates dynamic metadata for document sharing with search engine indexing controls
 * - **Citation Support**: Preserves and displays document citations from metadata for academic/professional use
 * - **Graceful Error Handling**: Provides user-friendly 404 pages for missing or private documents
 *
 * ## Request Parameters
 * - **Route Parameter**: `id` (UUID) - The document identifier for the shared document
 * - **Database Fields**: Retrieves comprehensive document data including title, content, metadata, and timestamps
 *
 * ## Security Architecture
 * The component implements multiple security layers:
 * 1. **Initial Existence Check**: Verifies document exists before checking public status
 * 2. **Public Status Validation**: Confirms `is_public = true` OR `metadata.isPublic = true` for backwards compatibility
 * 3. **RLS Policy Enforcement**: Database query respects `is_public = true` constraint automatically
 * 4. **Error State Management**: Returns null for unauthorized access, triggering Next.js notFound()
 *
 * ## Data Processing Pipeline
 * 1. **Parameter Resolution**: Awaits dynamic route parameters using Next.js 15 async params pattern
 * 2. **Document Retrieval**: Calls `getPublicDocument()` function for comprehensive document fetching
 * 3. **Public Status Check**: Validates public accessibility using dual validation approach
 * 4. **Content Preparation**: Extracts citations from metadata and prepares content for viewer component
 * 5. **Component Rendering**: Passes document data to `PublicDocumentViewer` for secure display
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side components and async params support
 * - **Supabase Client**: Database client for secure document retrieval with RLS enforcement
 * - **PublicDocumentViewer**: Custom TipTap-based read-only document renderer with citation support
 * - **Next.js Metadata API**: Dynamic SEO metadata generation with search engine indexing controls
 *
 * ## Related Components
 * - Document Editor System (main document editing interface for authenticated users)
 * - Document Management Dashboard (document sharing controls and permissions interface)
 * - Citation and Reference Management (academic citation system for ESG documents)
 * - User Authentication and Permissions (document ownership and sharing access controls)
 *
 * ## System Architecture
 * This component fits into the broader EkoIntelligence document sharing system:
 * - **Document Database**: `doc_documents` table in Supabase with RLS policies and indexing
 * - **Analytics Backend**: Python system generates ESG analysis documents stored in analytics database
 * - **Data Sync Layer**: Document content synchronized from analytics to customer database
 * - **Public Sharing API**: This component provides secure public access to shared documents
 * - **Frontend Interface**: Document editor and management interfaces for authenticated users
 *
 * ## Database Schema Integration
 * **doc_documents Table Structure**:
 * - `id` (UUID, Primary Key): Unique document identifier used in public sharing URLs
 * - `title` (Text): Document title for display and SEO metadata generation
 * - `content` (Text): Legacy document content field (fallback for older documents)
 * - `initial_content` (Text): Primary document content field for initial document state
 * - `data` (JSONB): Rich document data including TipTap editor content structure
 * - `metadata` (JSONB): Document metadata including citations, entity_id, run_id, and legacy isPublic flag
 * - `is_public` (Boolean): Primary public access control flag with database index
 * - `entity_id` (Text): ESG entity identifier for document association and context
 * - `run_id` (Integer): Analysis run identifier for versioning and temporal tracking
 * - `created_at`/`updated_at` (Timestamps): Document lifecycle tracking for audit trails
 * - `created_by`/`updated_by` (UUID, FK to profiles): User ownership and modification tracking
 *
 * ## Security & Performance
 * - **Row Level Security**: Supabase RLS policies automatically filter documents based on public status
 * - **Input Validation**: Comprehensive UUID validation and existence checks before expensive operations
 * - **Error Boundary**: Graceful error handling with appropriate HTTP status codes (404 for missing documents)
 * - **Database Indexing**: `is_public` column has dedicated B-tree index for fast public document queries
 * - **Comprehensive Logging**: Detailed console logging for document access debugging and audit trails
 * - **SEO Controls**: `robots: { index: false, follow: false }` prevents search engine indexing of shared documents
 *
 * ## Error Handling Scenarios
 * - **Document Not Found**: Returns 404 page when document ID doesn't exist in database
 * - **Private Document Access**: Returns 404 page when document is not marked as public
 * - **Database Connection Errors**: Handles Supabase connection failures gracefully
 * - **Invalid UUID Format**: Processes malformed document IDs without server errors
 * - **Missing Metadata**: Handles documents with incomplete or missing metadata gracefully
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://nextjs.org/docs/app/api-reference/file-conventions/page Next.js Page Components
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase Row Level Security
 * @see https://nextjs.org/docs/app/api-reference/functions/generate-metadata Next.js Metadata API
 * @see {@link ../../../components/editor/PublicDocumentViewer.tsx} Public Document Viewer Component
 * @see {@link ../../../supabase/server.ts} Supabase Server Client Configuration
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This Next.js App Router page component provides secure public access to shared ESG analysis documents with comprehensive security validation and user-friendly error handling.
 * @example ```bash
  # Access a public document
  curl https://app.ekointelligence.com/share/public/documents/123e4567-e89b-12d3-a456-************
  
  # Document must have is_public = true or metadata.isPublic = true
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { PublicDocumentViewer } from '@/components/editor/PublicDocumentViewer'
import { createClient } from '@/app/supabase/server'

interface PublicDocumentPageProps {
  params: Promise<{
    id: string
  }>
}

// This function fetches the document data using Supabase client
async function getPublicDocument(id: string) {
  try {
    console.log(`[PUBLIC DOCUMENT] Fetching document with ID: ${id}`)
    const supabase = await createClient()

    // First, check if the document exists at all (for debugging)
    const { data: documentExists, error: existsError } = await supabase
      .from('doc_documents')
      .select('id, is_public, metadata')
      .eq('id', id)
      .single()

    if (existsError) {
      console.error(`[PUBLIC DOCUMENT] Document ${id} not found:`, existsError)
      return null
    }

    console.log(`[PUBLIC DOCUMENT] Document ${id} exists. is_public: ${documentExists.is_public}, metadata:`, documentExists.metadata)

    // Check both is_public column and metadata.isPublic for backwards compatibility
    const isDocumentPublic = documentExists.is_public === true || 
                            (documentExists.metadata as any)?.isPublic === true

    if (!isDocumentPublic) {
      console.error(`[PUBLIC DOCUMENT] Document ${id} is not public. is_public: ${documentExists.is_public}, metadata.isPublic: ${(documentExists.metadata as any)?.isPublic}`)
      return null
    }

    // Query the document with public access check
    const { data: document, error } = await supabase
      .from('doc_documents')
      .select('id, title, content, initial_content, data, metadata, created_at, updated_at, is_public')
      .eq('id', id)
      .eq('is_public', true) // Only fetch if document is public
      .single()

    if (error) {
      console.error(`[PUBLIC DOCUMENT] Error fetching public document ${id}:`, error)
      return null
    }

    console.log(`[PUBLIC DOCUMENT] Successfully fetched public document ${id}`)
    return document
  } catch (error) {
    console.error(`[PUBLIC DOCUMENT] Unexpected error fetching document ${id}:`, error)
    return null
  }
}

// Generate metadata for the page
export async function generateMetadata({ params }: PublicDocumentPageProps): Promise<Metadata> {
  const { id } = await params
  const document = await getPublicDocument(id)

  if (!document) {
    return {
      title: 'Document Not Found',
      description: 'The requested document could not be found or is not publicly accessible.',
    }
  }

  return {
    title: document.title || 'Shared Document',
    description: 'A publicly shared document',
    robots: {
      index: false, // Don't index shared documents
      follow: false,
    },
  }
}

export default async function PublicDocumentPage({ params }: PublicDocumentPageProps) {
  const { id } = await params
  console.log(`[PUBLIC DOCUMENT PAGE] Rendering page for document ID: ${id}`)
  
  const document = await getPublicDocument(id)

  if (!document) {
    console.log(`[PUBLIC DOCUMENT PAGE] Document not found, returning 404`)
    notFound()
  }

  // Extract citations from metadata if available
  const citations = (document.metadata as any)?.citations || []
  
  console.log(`[PUBLIC DOCUMENT PAGE] Rendering PublicDocumentViewer for document: ${document.title}`)

  return (
    <PublicDocumentViewer
      documentId={id}
      title={document.title || undefined}
      content={(document.initial_content || document.content) || undefined}
      data={document.data || undefined}
      citations={citations}
    />
  )
}
