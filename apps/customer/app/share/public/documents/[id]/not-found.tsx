/**
 * Next.js App Router Custom 404 Not Found Page for Public Shared Documents
 *
 * This Next.js App Router custom error page component handles 404 "Not Found" errors specifically
 * for the public document sharing feature within the ESG Intelligence platform. It provides
 * a user-friendly error experience when shared documents cannot be accessed, displaying clear
 * explanations of potential causes and offering navigation back to the main application.
 *
 * ## Core Functionality
 * - **Custom 404 Handling**: Replaces default Next.js 404 page for `/share/public/documents/[id]` route
 * - **User-Friendly Messaging**: Clear explanation of why the document cannot be accessed
 * - **Navigation Recovery**: Provides branded button to return to application homepage
 * - **Glass-morphism Design**: Consistent with platform's design system using Tailwind CSS classes
 * - **Responsive Layout**: Mobile-first design that adapts to different screen sizes
 * - **Accessibility**: Semantic HTML structure with proper headings and keyboard navigation
 *
 * ## Trigger Conditions
 * This not-found page is displayed when:
 * 1. **Invalid Document ID**: User accesses a document ID that doesn't exist in the database
 * 2. **Privacy Restrictions**: Document exists but `is_public` field is set to `false` or `metadata.isPublic` is `false`
 * 3. **Deleted Documents**: Document was previously public but has been deleted from `doc_documents` table
 * 4. **Database Errors**: Supabase query failures or connection issues prevent document retrieval
 * 5. **Malformed URLs**: Invalid document ID format or corrupted sharing links
 *
 * ## Related Components
 * - **Public Document Page**: `page.tsx` in same directory calls `notFound()` when document unavailable
 * - **Public Document Viewer**: `PublicDocumentViewer` component renders valid public documents
 * - **Document Sharing System**: Backend logic that sets `is_public` flag and manages sharing permissions
 * - **Supabase Database**: `doc_documents` table with public access controls and metadata
 *
 * ## System Architecture
 * This error page fits into the document sharing ecosystem:
 * - **Route Structure**: Located in `/share/public/documents/[id]/not-found.tsx` for route-specific error handling
 * - **Error Flow**: `page.tsx` fetches document → checks public access → calls `notFound()` if unavailable
 * - **Next.js Integration**: Uses Next.js App Router `not-found.tsx` file convention for automatic error handling
 * - **Design System**: Maintains platform consistency with glass-morphism styling and Tailwind CSS
 * - **User Experience**: Provides clear error messaging without exposing sensitive information
 *
 * ## Design Pattern
 * - **Centered Layout**: Full-screen centered error message with proper spacing and typography
 * - **Information Hierarchy**: 404 error code → descriptive heading → explanation → possible causes → action button
 * - **Visual Consistency**: Uses platform color scheme (`bg-background`, `text-foreground`, `text-muted-foreground`)
 * - **Interactive Elements**: Primary-styled button with hover states and focus management
 * - **Contextual Help**: Bulleted list explaining common reasons for document access failures
 *
 * ## Security Considerations
 * - **Privacy Protection**: Does not reveal whether documents exist or are simply private
 * - **Information Disclosure**: Generic error messaging prevents enumeration of valid document IDs
 * - **Access Control**: Works in conjunction with Row Level Security (RLS) policies in Supabase
 * - **Error Handling**: Graceful degradation when database queries fail or return unauthorized results
 *
 * @see https://nextjs.org/docs/app/api-reference/file-conventions/not-found Next.js not-found.js File Convention
 * @see https://nextjs.org/docs/app/api-reference/functions/not-found Next.js notFound() Function
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security
 * @see {@link ./page.tsx} Public Document Page Component
 * @see {@link ../../../../components/editor/PublicDocumentViewer.tsx} Public Document Viewer
 * <AUTHOR>
 * @updated 2025-07-22
 * @description Custom 404 error page for public shared documents that cannot be accessed due to privacy settings, deletion, or invalid IDs
 * @example
 * ```tsx
 * // Triggered when page.tsx calls notFound():
 * if (!document || !isDocumentPublic) {
 *   notFound() // Renders this not-found.tsx component
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center space-y-6 max-w-md mx-auto px-4">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold text-foreground">404</h1>
          <h2 className="text-xl font-semibold text-foreground">Document Not Found</h2>
          <p className="text-muted-foreground">
            The document you're looking for doesn't exist or is not publicly accessible.
          </p>
        </div>
        
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            This could happen if:
          </p>
          <ul className="text-sm text-muted-foreground text-left space-y-1">
            <li>• The document has been deleted</li>
            <li>• The document is no longer shared publicly</li>
            <li>• The link is incorrect or expired</li>
          </ul>
        </div>

        <div className="pt-4">
          <Link
            href="/"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
          >
            Go to Homepage
          </Link>
        </div>
      </div>
    </div>
  )
}
