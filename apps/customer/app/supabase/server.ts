/**
 * # Supabase Server Client Factory for Next.js SSR and API Routes
 *
 * This file provides a factory function for creating Supabase server client instances in the EkoIntelligence
 * customer application. It serves as the primary server-side interface for accessing the customer database
 * from Next.js Server Components, API routes, and server-side operations that require authentication context
 * and database access within the ESG (Environmental, Social, Governance) analysis platform.
 *
 * ## Core Functionality
 * - **Server Client Creation**: Factory function for Supabase server client instances with enhanced cookie management
 * - **<PERSON>ie Handling**: Advanced cookie management for authentication state persistence across server requests
 * - **SSR Integration**: Enables server-side rendering with authenticated database access and user context
 * - **Type Safety**: Leverages generated TypeScript database types for compile-time validation and IDE support
 * - **Authentication Context**: Provides authenticated user context for RLS (Row Level Security) policy enforcement
 * - **Error Handling**: Graceful handling of cookie manipulation errors in various Next.js component contexts
 *
 * ## Authentication Flow & Cookie Management
 * The factory implements Supabase's recommended SSR authentication pattern:
 * 1. **Cookie Retrieval**: Reads authentication cookies from Next.js headers using async `cookies()` API
 * 2. **Server Client Creation**: Creates Supabase server client with custom cookie handlers for SSR context
 * 3. **Session Management**: Handles authentication token refresh and session validation on server-side
 * 4. **Cookie Synchronization**: Manages cookie setting/removal with proper error handling for component boundaries
 * 5. **Request Context**: Maintains authentication state across server component tree and API operations
 *
 * ## System Architecture Integration
 * This server client integrates with the broader EkoIntelligence platform:
 * - **Analytics Backend**: Python system processes ESG documents and generates analysis in analytics database
 * - **Data Sync Layer**: `xfer_*` tables synchronize processed ESG data from analytics to customer database
 * - **Customer Database**: Supabase PostgreSQL with user accounts, documents, and synchronized ESG insights
 * - **Server Components**: Next.js server components use this client for authenticated data fetching
 * - **API Routes**: Backend API endpoints leverage this client for database operations with user context
 * - **Middleware**: Session management middleware works with this client for authentication persistence
 *
 * ## Database Schema Access
 * The server client provides authenticated access to customer database tables:
 * - **User Management**: `profiles`, `acc_organisations`, `acc_quota` for account and organization operations
 * - **Document System**: `doc_documents`, `doc_versions`, `doc_comments` for collaborative document management
 * - **ESG Analytics**: `xfer_entities`, `xfer_claims`, `xfer_promises`, `xfer_flags` for synchronized analytics data
 * - **Customer Analytics**: `cus_ana_companies`, `cus_ana_hist_*` for tracking customer-specific analysis runs
 * - **Real-time Features**: Support for real-time subscriptions and collaborative editing operations
 *
 * ## Cookie Management Strategy
 * The implementation follows Supabase SSR best practices for cookie handling:
 * - **Async Cookie Access**: Uses Next.js 15 async `cookies()` API with proper `await` patterns
 * - **Type Casting**: Handles Next.js cookie type complications with `UnsafeUnwrappedCookies` casting
 * - **Error Resilience**: Try/catch blocks around cookie operations to handle Server Component limitations
 * - **Silent Failures**: Graceful degradation when cookie operations fail in restrictive component contexts
 * - **Session Persistence**: Ensures authentication state persists across server-side operations
 *
 * ## Security & RLS Integration
 * - **Row Level Security**: Server client respects database RLS policies based on authenticated user context
 * - **User Authentication**: Validates and maintains user sessions for secure database access
 * - **Permission Enforcement**: Database queries automatically filtered by user permissions and organization access
 * - **Session Validation**: Server-side session verification prevents unauthorized access to sensitive ESG data
 * - **Environment Variables**: Secure configuration through `NEXT_PUBLIC_SUPABASE_URL` and `NEXT_PUBLIC_SUPABASE_ANON_KEY`
 *
 * ## Next.js Context Compatibility
 * - **Server Components**: Works seamlessly in Next.js App Router server components for data fetching
 * - **API Routes**: Provides authenticated database access for backend API endpoints
 * - **Middleware**: Compatible with session management middleware for authentication persistence
 * - **Error Boundaries**: Graceful handling of cookie manipulation errors in various rendering contexts
 * - **Static Generation**: Compatible with Next.js static generation while maintaining dynamic authentication
 *
 * ## Performance Considerations
 * - **Connection Pooling**: Leverages Supabase connection pooling for efficient database access
 * - **Query Optimization**: Type-safe queries prevent N+1 problems and optimize database operations
 * - **Caching Integration**: Works with Next.js caching strategies for improved performance
 * - **Session Reuse**: Efficient session management reduces authentication overhead
 * - **Edge Runtime**: Compatible with Next.js Edge Runtime for global distribution
 *
 * ## Error Handling Patterns
 * The implementation includes comprehensive error handling:
 * - **Cookie Operation Failures**: Silent failures when Server Components cannot manipulate cookies
 * - **Authentication Errors**: Graceful handling of invalid or expired authentication tokens
 * - **Database Connectivity**: Proper error propagation for database connection issues
 * - **Session Expiry**: Automatic session refresh with fallback for expired authentication
 * - **Environment Issues**: Clear error messages for missing or invalid configuration
 *
 * ## Related Components
 * - Browser client factory in `app/supabase/client.ts` for React component database access
 * - Session management middleware in `app/supabase/middleware.ts` for authentication persistence
 * - Database type definitions in `database.types.ts` generated from Supabase schema
 * - Main application middleware in `middleware.ts` for request interception and session management
 * - Authentication components throughout the application for user session management
 *
 * @see https://supabase.com/docs/guides/auth/server-side/nextjs Supabase Next.js SSR Authentication Guide
 * @see https://supabase.com/docs/reference/javascript/initializing Supabase JavaScript Client Documentation
 * @see https://nextjs.org/docs/app/api-reference/functions/cookies Next.js Cookies API Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see {@link ./client.ts} Browser-side Supabase client factory for React components
 * @see {@link ./middleware.ts} Supabase session management middleware
 * @see {@link ../database.types.ts} Generated TypeScript database types
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Factory function for creating Supabase server client instances with advanced cookie management for Next.js SSR authentication
 * @example ```typescript
 * import { createClient } from '@/app/supabase/server'
 *
 * export default async function ServerComponent() {
 *   const supabase = await createClient()
 *
 *   // Query ESG entities with user context
 *   const { data: entities, error } = await supabase
 *     .from('xfer_entities')
 *     .select('*')
 *     .limit(10)
 *
 *   if (error) throw error
 *
 *   return <div>{entities.map(entity => entity.name)}</div>
 * }
 *
 * // API Route usage
 * export async function GET() {
 *   const supabase = await createClient()
 *   const { data: user } = await supabase.auth.getUser()
 *
 *   if (!user) {
 *     return Response.json({ error: 'Unauthorized' }, { status: 401 })
 *   }
 *
 *   // Perform authenticated database operations
 *   return Response.json({ user })
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { type CookieOptions, createServerClient } from '@supabase/ssr'
import { cookies, type UnsafeUnwrappedCookies } from 'next/headers'
import { Database } from '@/database.types'


export const createClient = async () => {
    const cookieStore = (((await cookies()) as unknown as UnsafeUnwrappedCookies) as unknown as UnsafeUnwrappedCookies);

    return createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
            cookies: {
                get(name: string) {
                    return cookieStore.get(name)?.value;
                },
                set(name: string, value: string, options: CookieOptions) {
                    try {
                        cookieStore.set({name, value, ...options});
                    } catch (error) {
                        // The `set` method was called from a Server Component.
                        // This can be ignored if you have middleware refreshing
                        // user sessions.
                    }
                },
                remove(name: string, options: CookieOptions) {
                    try {
                        cookieStore.set({name, value: "", ...options});
                    } catch (error) {
                        // The `delete` method was called from a Server Component.
                        // This can be ignored if you have middleware refreshing
                        // user sessions.
                    }
                },
            },
        },
    );
};
