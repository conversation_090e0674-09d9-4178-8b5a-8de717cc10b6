/**
 * # Supabase Browser Client Factory for ESG Analysis Platform
 *
 * This file provides a factory function for creating Supabase browser client instances in the EkoIntelligence
 * customer application. It serves as the primary client-side interface for accessing the customer database,
 * handling authentication, real-time subscriptions, and ESG (Environmental, Social, Governance) data queries
 * within the React/Next.js frontend environment.
 *
 * ## Core Functionality
 * - **Browser Client Creation**: Factory function for Supabase browser client instances with TypeScript type safety
 * - **Database Integration**: Connects to customer database containing user accounts, ESG reports, and synchronized analytics data
 * - **Authentication Support**: Enables client-side authentication flows, session management, and user verification
 * - **Real-time Features**: Supports real-time subscriptions for collaborative document editing and live data updates
 * - **Type Safety**: Leverages generated TypeScript database types for compile-time validation and IDE autocompletion
 *
 * ## System Architecture Context
 * This browser client fits into the broader EkoIntelligence platform architecture:
 * - **Analytics Backend**: Python system processes ESG documents and generates analysis stored in analytics database
 * - **Data Sync Layer**: `xfer_*` tables synchronize processed ESG data from analytics to customer database
 * - **Customer Database**: Supabase PostgreSQL instance containing user data, documents, and synchronized ESG insights
 * - **Frontend Layer**: React components use this client for authentication, data fetching, and real-time collaboration
 * - **Editor System**: TipTap collaborative editor leverages client for document persistence and real-time updates
 *
 * ## Database Schema Integration
 * The client connects to customer database tables including:
 * - **User Management**: `profiles`, `acc_organisations`, `acc_quota` for account and organization data
 * - **Document System**: `doc_documents`, `doc_versions`, `doc_comments` for collaborative document editing
 * - **ESG Data**: `xfer_entities`, `xfer_claims`, `xfer_promises`, `xfer_flags` for synchronized analytics results
 * - **Analytics History**: `cus_ana_companies`, `cus_ana_hist_*` for tracking analysis runs and historical data
 *
 * ## Environment Configuration
 * - **NEXT_PUBLIC_SUPABASE_URL**: Public Supabase project URL for client connections
 * - **NEXT_PUBLIC_SUPABASE_ANON_KEY**: Anonymous (public) API key for unauthenticated access and RLS
 * - **Database Types**: Generates TypeScript types from `database.types.ts` for compile-time safety
 *
 * ## Usage Patterns
 * - **Component Integration**: React components import and use this client for data operations
 * - **Authentication Flows**: Login, signup, password reset, and session management
 * - **Real-time Subscriptions**: Document collaboration, live updates, and notification systems
 * - **Query Operations**: Fetching ESG entity data, reports, claims, promises, and effect flags
 * - **File Operations**: Document uploads, collaborative editing, and version management
 *
 * ## Security Features
 * - **Row Level Security (RLS)**: Database policies ensure users only access authorized data
 * - **Anonymous Access**: Public API key enables unauthenticated read access to non-sensitive data
 * - **Session Management**: Automatic token refresh and secure authentication state handling
 * - **Client-side Validation**: TypeScript types prevent invalid database operations at compile time
 *
 * ## Related Components
 * - Server-side client factory in `app/supabase/server.ts` for SSR and API routes
 * - Middleware configuration in `app/supabase/middleware.ts` for session refresh
 * - Database type definitions in `database.types.ts` generated from Supabase schema
 * - Authentication components throughout the application for user management
 *
 * @see https://supabase.com/docs/reference/javascript/initializing Supabase JavaScript Client Documentation
 * @see https://supabase.com/docs/guides/auth/server-side/nextjs Supabase Next.js SSR Guide
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see {@link ../server.ts} Server-side Supabase client factory
 * @see {@link ../middleware.ts} Supabase middleware for session management
 * @see {@link ../../database.types.ts} Generated TypeScript database types
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Factory function for creating Supabase browser client instances with TypeScript type safety for ESG analysis platform frontend
 * @example ```typescript
 * import { createClient } from '@/app/supabase/client'
 *
 * const supabase = createClient()
 *
 * // Authenticate user
 * const { data, error } = await supabase.auth.signInWithPassword({
 *   email: '<EMAIL>',
 *   password: 'password'
 * })
 *
 * // Query ESG entities
 * const { data: entities } = await supabase
 *   .from('xfer_entities')
 *   .select('*')
 *   .limit(10)
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createBrowserClient } from '@supabase/ssr'
import { Database } from '../../database.types'
import { SupabaseClient } from '@supabase/supabase-js'
import { GenericSchema } from '@supabase/supabase-js/dist/main/lib/types'

export const createClient: () => SupabaseClient<Database, "public" extends keyof Database ? "public" : (string & keyof Database), Database["public"] extends GenericSchema ? Database["public"] : any> = () =>
    createBrowserClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    );
