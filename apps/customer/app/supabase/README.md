# Supabase Integration Module

## Overview

The Supabase integration module serves as the core database abstraction layer for the EkoIntelligence ESG (Environmental, Social, Governance) analysis customer application. This module provides TypeScript-safe Supabase client factories, session management middleware, and comprehensive database type definitions for both browser-side and server-side operations within the Next.js 15 App Router environment.

The module abstracts the complexity of Supabase authentication, cookie management, and database operations while ensuring seamless integration between the customer-facing application and the underlying PostgreSQL database containing user accounts, collaborative documents, and synchronized ESG analytics data.

## Specification

### Core Requirements

1. **Client Factory Pattern**: Provide separate client factories for browser and server environments
2. **TypeScript Safety**: Full type coverage for all database operations using generated database types
3. **Authentication Integration**: Seamless Supabase Auth integration with session persistence
4. **SSR Compatibility**: Full server-side rendering support with cookie-based session management
5. **Middleware Integration**: Automatic session refresh and authentication state persistence
6. **Error Handling**: Graceful degradation when Supa<PERSON> is not configured or unavailable

### Authentication Flow

1. User authentication through <PERSON><PERSON><PERSON> Auth (email/password, OAuth)
2. Session tokens stored in HTTP-only cookies for security
3. Middleware intercepts requests to refresh expired sessions
4. Server components access authenticated user context
5. Client components maintain real-time authentication state
6. Row Level Security (RLS) policies enforce data access controls

### Database Schema Integration

The module integrates with a comprehensive customer database schema including:

- **User Management**: `profiles`, `acc_organisations`, `acc_quota` for account management
- **Document System**: `doc_documents`, `doc_versions`, `doc_comments` for collaborative editing
- **ESG Analytics**: `xfer_*` tables containing synchronized ESG analysis results
- **Real-time Features**: Support for collaborative document editing via Hocuspocus

## Key Components

### 1. `client.ts` - Browser Client Factory

**Role**: Creates Supabase browser client instances for React components and client-side operations

**Key Features**:
- TypeScript-safe client creation using generated database types
- Browser-optimized configuration for client-side authentication
- Integration with React components for real-time subscriptions
- Support for authentication flows and session management

**Usage Context**: React components, hooks, client-side data fetching, real-time subscriptions

### 2. `server.ts` - Server Client Factory

**Role**: Creates Supabase server client instances for Next.js Server Components and API routes

**Key Features**:
- Advanced cookie management for SSR authentication
- Async cookie handling compatible with Next.js 15
- Error-resistant cookie operations for Server Component boundaries
- Integration with API routes and server-side data fetching

**Usage Context**: Server Components, API routes, middleware, server-side authentication

### 3. `middleware.ts` - Session Management Middleware

**Role**: Handles automatic session refresh and cookie synchronization for authentication persistence

**Key Features**:
- Intercepts requests before reaching page components
- Automatic session validation and refresh using `supabase.auth.getUser()`
- Cookie synchronization between request and response objects
- Graceful error handling for development and production environments

**Usage Context**: Next.js middleware chain, session persistence, authentication validation

### 4. `database.types.ts` - Type Definitions Re-Export

**Role**: Centralized re-export of auto-generated Supabase database types

**Key Features**:
- Clean import paths for database types throughout the application
- Comprehensive TypeScript coverage for all tables, views, and enums
- Helper types for common database operations
- Synchronization with actual database schema structure

**Usage Context**: Type imports, database operations, schema validation

## Dependencies

### External Dependencies

| Dependency | Purpose | Documentation |
|------------|---------|---------------|
| `@supabase/ssr` | Server-side rendering support and cookie management | [Supabase SSR Docs](https://supabase.com/docs/guides/auth/server-side/nextjs) |
| `@supabase/supabase-js` | Core Supabase JavaScript client library | [Supabase JS Docs](https://supabase.com/docs/reference/javascript) |
| `next/headers` | Next.js headers API for cookie management | [Next.js Headers API](https://nextjs.org/docs/app/api-reference/functions/cookies) |
| `next/server` | Next.js server utilities for middleware | [Next.js Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware) |

### Internal Dependencies

| Module | Relationship | Purpose |
|--------|--------------|---------|
| `../../database.types` | Import dependency | Source of comprehensive database type definitions |
| Application middleware | Integration point | Uses session management middleware for authentication |
| React components | Consumer | Components use browser client for data operations |
| API routes | Consumer | Server endpoints use server client for authenticated operations |

### Environment Variables

| Variable | Purpose | Required |
|----------|---------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL for client connections | Yes |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Anonymous API key for public access and RLS | Yes |

## Usage Examples

### Browser Client Usage

```typescript
import { createClient } from '@/app/supabase/client'

// React component usage
export function EntityList() {
  const supabase = createClient()
  const [entities, setEntities] = useState([])

  useEffect(() => {
    async function fetchEntities() {
      const { data, error } = await supabase
        .from('xfer_entities')
        .select('id, short_name, eko_score')
        .limit(10)
      
      if (error) throw error
      setEntities(data)
    }
    
    fetchEntities()
  }, [])

  return <div>{entities.map(entity => ...)}</div>
}
```

### Server Component Usage

```typescript
import { createClient } from '@/app/supabase/server'

// Server Component usage
export default async function ServerComponent() {
  const supabase = await createClient()

  // Fetch user profile
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    return <div>Please log in</div>
  }

  // Fetch user-specific data with RLS
  const { data: documents } = await supabase
    .from('doc_documents')
    .select('*')
    .order('updated_at', { ascending: false })

  return <DocumentList documents={documents} />
}
```

### API Route Usage

```typescript
import { createClient } from '@/app/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const supabase = await createClient()

  // Validate authentication
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  // Perform authenticated database operations
  const { data: entities } = await supabase
    .from('xfer_entities')
    .select('*')
    .limit(20)

  return NextResponse.json({ entities })
}
```

### Middleware Integration

```typescript
import { updateSession } from '@/app/supabase/middleware'
import { NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // Automatically refresh sessions and manage cookies
  return await updateSession(request)
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

## Architecture Notes

### System Integration Architecture

```mermaid
graph TD
    A[Next.js Application] --> B[Supabase Integration Module]
    B --> C[Browser Client Factory]
    B --> D[Server Client Factory]
    B --> E[Session Middleware]
    B --> F[Database Types]
    
    C --> G[React Components]
    C --> H[Client-side Hooks]
    C --> I[Real-time Subscriptions]
    
    D --> J[Server Components]
    D --> K[API Routes]
    D --> L[Server-side Data Fetching]
    
    E --> M[Session Refresh]
    E --> N[Cookie Management]
    E --> O[Authentication Persistence]
    
    F --> P[Type Safety]
    F --> Q[Schema Validation]
    F --> R[IDE Support]
    
    B --> S[Supabase PostgreSQL Database]
    S --> T[User Management Tables]
    S --> U[Document System Tables]
    S --> V[ESG Analytics Tables]
    S --> W[Real-time Features]
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant B as Browser
    participant M as Middleware
    participant S as Server Component
    participant DB as Supabase DB
    participant A as Analytics Backend

    U->>B: Navigate to page
    B->>M: Request intercepted
    M->>DB: Validate/refresh session
    DB-->>M: Session status
    M->>S: Forward with auth context
    S->>DB: Query user data (RLS applied)
    DB-->>S: Filtered data
    S-->>B: Render with data
    B-->>U: Display page

    Note over A,DB: Background sync process
    A->>DB: Sync analytics results to xfer_ tables
```

### Authentication State Management

```mermaid
stateDiagram-v2
    [*] --> Unauthenticated
    Unauthenticated --> Authenticating: Login attempt
    Authenticating --> Authenticated: Success
    Authenticating --> Unauthenticated: Failure
    Authenticated --> SessionRefresh: Token near expiry
    SessionRefresh --> Authenticated: Refresh success
    SessionRefresh --> Unauthenticated: Refresh failure
    Authenticated --> Unauthenticated: Logout
```

## Known Issues

### Cookie Management in Server Components

The server client factory includes try/catch blocks around cookie operations because Next.js Server Components have limitations on cookie manipulation. This is handled gracefully with silent failures when middleware is properly configured for session refresh.

**Status**: Working as intended with documented workaround
**Impact**: Low - middleware handles session refresh automatically
**Related**: [Next.js Server Components limitations](https://nextjs.org/docs/app/building-your-application/rendering/server-components#static-and-dynamic-rendering)

### Development Environment Configuration

Linear issues EKO-295, EKO-296, EKO-297 indicate recurring problems with missing Supabase environment variables during development setup.

**Status**: Ongoing configuration issue
**Impact**: Medium - blocks local development setup
**Solution**: Ensure `NEXT_PUBLIC_SUPABASE_URL` and `NEXT_PUBLIC_SUPABASE_ANON_KEY` are properly configured in `.env.local`

### Auto-Save CORS Issues

Linear issue EKO-219 reported CORS policy blocking collaborative document auto-save functionality.

**Status**: Resolved
**Impact**: High - broke collaborative editing
**Resolution**: Supabase CORS configuration updated to allow localhost during development

## Future Work

### Planned Improvements

1. **Enhanced Error Handling** (Priority: Medium)
   - Implement comprehensive error boundary system for Supabase operations
   - Add retry logic for transient network failures
   - Improve error messaging for authentication failures

2. **Performance Optimization** (Priority: Low)
   - Implement connection pooling optimization
   - Add query result caching for frequently accessed data
   - Optimize real-time subscription management

3. **Development Experience** (Priority: High)
   - Create CLI command for local Supabase setup validation
   - Add comprehensive logging for debugging authentication issues
   - Implement automated database type regeneration in CI/CD

### Integration Enhancements

1. **PDF Generation Integration** (Linear: EKO-239)
   - Enhance server client to support document rendering operations
   - Add Supabase Storage integration for PDF file management
   - Implement secure file access with time-limited URLs

2. **Changelog System** (Linear: EKO-269)
   - Extend database types for new `app_changelog` table
   - Add real-time subscription support for changelog updates
   - Implement toast notification system integration

3. **User Management CLI** (Linear: EKO-266)
   - Extend server client with admin-level operations
   - Add support for organization and quota management
   - Implement secure user creation and management workflows

## Troubleshooting

### Common Issues

**Q: Getting "Your project's URL and Key are required to create a Supabase client!" error**
A: Ensure environment variables are properly set:
```bash
# .env.local
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

**Q: Server Components cannot set cookies error**
A: This is expected behavior. The middleware handles session refresh automatically. The error is caught and ignored in server client factory.

**Q: Authentication state not persisting across page reloads**
A: Verify that middleware is properly configured and the `updateSession` function is being called on all routes.

**Q: RLS policies blocking data access**
A: Ensure user is properly authenticated and database policies allow access for the user's role and organization.

### Debugging Steps

1. **Verify Environment Configuration**
   ```bash
   # Check environment variables are loaded
   console.log(process.env.NEXT_PUBLIC_SUPABASE_URL)
   console.log(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)
   ```

2. **Test Client Creation**
   ```typescript
   import { createClient } from '@/app/supabase/client'
   
   try {
     const supabase = createClient()
     console.log('Client created successfully')
   } catch (error) {
     console.error('Client creation failed:', error)
   }
   ```

3. **Verify Authentication State**
   ```typescript
   const supabase = createClient()
   const { data: { user }, error } = await supabase.auth.getUser()
   console.log('User:', user, 'Error:', error)
   ```

4. **Test Database Connection**
   ```typescript
   const { data, error } = await supabase
     .from('profiles')
     .select('id')
     .limit(1)
   console.log('Database test:', { data, error })
   ```

## FAQ

**Q: When should I use the browser client vs server client?**
A: Use browser client in React components, hooks, and client-side code. Use server client in Server Components, API routes, and server-side operations.

**Q: How does the middleware affect performance?**
A: The middleware adds minimal overhead (~1-2ms) per request for session validation. It prevents more expensive authentication failures later.

**Q: Can I use both clients in the same component?**
A: No, Server Components should use server client, and client components should use browser client. Don't mix them.

**Q: How often are database types regenerated?**
A: Types should be regenerated whenever the database schema changes. Consider setting up automated regeneration in your CI/CD pipeline.

**Q: What happens if Supabase is unavailable?**
A: The middleware includes try/catch blocks that allow the application to continue functioning with degraded authentication capabilities.

**Q: How do I handle real-time subscriptions?**
A: Use the browser client in React components with `useEffect` to manage subscription lifecycle:
```typescript
useEffect(() => {
  const subscription = supabase
    .channel('documents')
    .on('postgres_changes', { event: '*', schema: 'public', table: 'doc_documents' }, handleChange)
    .subscribe()
  
  return () => supabase.removeChannel(subscription)
}, [])
```

## References

### Documentation Links
- [Supabase Next.js SSR Guide](https://supabase.com/docs/guides/auth/server-side/nextjs)
- [Supabase JavaScript Client Documentation](https://supabase.com/docs/reference/javascript/initializing)
- [Supabase Database Types Generation](https://supabase.com/docs/guides/api/rest/generating-types)
- [Next.js Middleware Documentation](https://nextjs.org/docs/app/building-your-application/routing/middleware)
- [Next.js Cookies API](https://nextjs.org/docs/app/api-reference/functions/cookies)

### Related Code Files
- [`client.ts`](./client.ts) - Browser client factory implementation
- [`server.ts`](./server.ts) - Server client factory implementation  
- [`middleware.ts`](./middleware.ts) - Session management middleware
- [`database.types.ts`](./database.types.ts) - Type definitions re-export
- [`../../database.types.ts`](../../database.types.ts) - Comprehensive database types
- [`../../middleware.ts`](../../middleware.ts) - Application middleware integration

### External Resources
- [Supabase Official Documentation](https://supabase.com/docs)
- [Next.js App Router Documentation](https://nextjs.org/docs/app)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

### Linear Issues
- [EKO-295: Missing Supabase Configuration](https://linear.app/ekointelligence/issue/EKO-295)
- [EKO-296: Missing Supabase Configuration](https://linear.app/ekointelligence/issue/EKO-296)
- [EKO-297: Missing Supabase Configuration](https://linear.app/ekointelligence/issue/EKO-297)
- [EKO-219: Auto-Save CORS Issues](https://linear.app/ekointelligence/issue/EKO-219)
- [EKO-239: PDF Generation Integration](https://linear.app/ekointelligence/issue/EKO-239)
- [EKO-269: Changelog System](https://linear.app/ekointelligence/issue/EKO-269)
- [EKO-266: User Management CLI](https://linear.app/ekointelligence/issue/EKO-266)

---

## Changelog

### 2025-07-31
- **Created**: Initial comprehensive README.md for Supabase integration module
- **Added**: Complete documentation of client factories, middleware, and type system
- **Added**: Architecture diagrams for system integration and data flow
- **Added**: Troubleshooting guide and FAQ section
- **Added**: Usage examples for all major components
- **Added**: Integration with Linear issues and future work planning

---

(c) All rights reserved ekoIntelligence 2025