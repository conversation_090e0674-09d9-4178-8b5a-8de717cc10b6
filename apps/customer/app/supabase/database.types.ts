/**
 * # Supabase Database Types Re-Export for ESG Analysis Platform
 *
 * This file serves as a re-export module for Supabase generated TypeScript database types within the
 * EkoIntelligence ESG (Environmental, Social, Governance) analysis platform's Supabase integration layer.
 * It provides a centralized access point for database type definitions used throughout the customer application's
 * Supabase client connections, authentication flows, and data operations while maintaining clean import paths
 * and consistent type safety across the application.
 *
 * ## Core Functionality
 * - **Type Re-Export**: Central re-export point for comprehensive database type definitions
 * - **Import Path Optimization**: Simplified import paths for Supabase-related components and utilities
 * - **Type Safety**: Ensures consistent TypeScript type definitions across all Supabase operations
 * - **Schema Synchronization**: Maintains alignment with auto-generated database schema types
 * - **Development Workflow**: Supports automated type generation and regeneration workflows
 *
 * ## Database Schema Context
 * The re-exported types provide comprehensive coverage for the EkoIntelligence customer database schema:
 *
 * ### User Management & Organizations
 * - **Authentication**: User profiles, sessions, and account management tables
 * - **Organizations**: Company accounts, user roles, and organizational hierarchy
 * - **Quotas & Usage**: API usage tracking, limits, and billing-related data structures
 * - **Messaging**: System notifications and user communication tables
 *
 * ### Document Collaboration System
 * - **Documents**: Core document metadata, content, and ownership structures
 * - **Versioning**: Document version history and change tracking tables
 * - **Collaboration**: Real-time editing operations, comments, and user presence
 * - **Permissions**: Granular access control and sharing mechanisms
 * - **Rendering**: Document export, PDF generation, and display caching
 *
 * ### ESG Analytics Integration
 * - **Transfer Tables**: Synchronized ESG analysis results from analytics backend
 * - **Entity Data**: Company profiles, ESG scores, and risk assessments
 * - **Claims Analysis**: Corporate ESG claims verification and tracking
 * - **Promises Tracking**: Commitment monitoring and fulfillment analysis
 * - **Effect Flags**: ESG risk indicators and environmental impact flags
 * - **Predictive Analytics**: Forecasting models and trend analysis results
 *
 * ## Type Generation Workflow
 * The database types are automatically generated using the Supabase CLI from the PostgreSQL schema:
 * ```bash
 * # Generate types from Supabase project
 * supabase gen types typescript --project-id "$PROJECT_REF" > database.types.ts
 *
 * # Generate types from local development environment
 * supabase gen types typescript --local > database.types.ts
 * ```
 *
 * ## System Architecture Integration
 * This type re-export module fits into the broader EkoIntelligence platform architecture:
 * - **Analytics Backend**: Python system processes ESG documents and generates insights
 * - **Data Synchronization**: `xfer_*` tables bridge analytics and customer databases
 * - **Customer Database**: Supabase PostgreSQL with user data and synchronized ESG results
 * - **Frontend Layer**: React/Next.js components consume these types for type-safe operations
 * - **Collaboration Layer**: Real-time document editing with type-safe collaborative operations
 *
 * ## Usage Patterns
 * This re-export enables clean imports throughout the Supabase integration layer:
 * ```typescript
 * // Clean import from Supabase directory
 * import { Database } from '@/app/supabase/database.types'
 *
 * // Type-safe client creation
 * const client = createBrowserClient<Database>(url, key)
 *
 * // Type-safe database operations
 * const { data } = await client.from('xfer_entities').select('*')
 * ```
 *
 * ## Related Components
 * - Supabase client factory (`client.ts`) - Browser client creation with type safety
 * - Server client factory (`server.ts`) - Server-side client for SSR and API routes
 * - Middleware configuration (`middleware.ts`) - Session refresh and authentication
 * - Root database types (`../../database.types.ts`) - Comprehensive generated type definitions
 * - Authentication context throughout the application for user management
 *
 * ## Maintenance & Updates
 * - **Schema Changes**: Regenerate types when database schema is modified
 * - **Type Validation**: Ensure generated types align with actual database structure
 * - **Version Control**: Track type changes alongside schema migrations
 * - **CI/CD Integration**: Automated type generation in deployment pipelines
 * - **Development Sync**: Keep local and production types synchronized
 *
 * ## Security Considerations
 * - **Row Level Security**: Types reflect RLS policies for data access control
 * - **Authentication Integration**: Types support Supabase auth for user context
 * - **Data Validation**: Compile-time type checking prevents invalid database operations
 * - **Privacy Protection**: Types respect data privacy and access restrictions
 *
 * @see https://supabase.com/docs/guides/api/rest/generating-types Supabase Type Generation Guide
 * @see https://supabase.com/docs/reference/javascript/typescript-support Supabase TypeScript Support
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see {@link ./client.ts} Supabase browser client factory
 * @see {@link ./server.ts} Supabase server client factory
 * @see {@link ../../database.types.ts} Comprehensive database type definitions
 * <AUTHOR>
 * @updated 2025-07-25
 * @description Re-export module for Supabase generated TypeScript database types providing centralized access and clean import paths
 * @example ```typescript
 * import { Database, Tables, Enums } from '@/app/supabase/database.types'
 * import { createBrowserClient } from '@supabase/ssr'
 *
 * // Create type-safe client
 * const client = createBrowserClient<Database>(
 *   process.env.NEXT_PUBLIC_SUPABASE_URL!,
 *   process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
 * )
 *
 * // Type-safe database query
 * const { data: entities } = await client
 *   .from('xfer_entities')
 *   .select('short_name, eko_score, entity_type')
 *   .limit(10)
 *
 * // Use helper types for cleaner code
 * type Entity = Tables<'xfer_entities'>
 * type EntityInsert = TablesInsert<'xfer_entities'>
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

// Re-export all database types from the root level comprehensive types file
export * from '../../database.types'
