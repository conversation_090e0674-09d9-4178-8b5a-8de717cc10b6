/**
 * # Supabase Session Management Middleware for Next.js
 *
 * This middleware file provides session management functionality for the EkoIntelligence customer application
 * by integrating Supabase authentication with Next.js Server-Side Rendering (SSR). It handles automatic
 * session refresh, cookie management, and authentication state persistence across server and client
 * components in the Next.js App Router environment.
 *
 * ## Core Functionality
 * - **Session Refresh**: Automatically refreshes expired Supabase authentication sessions on server-side requests
 * - **Cookie Management**: Handles Supabase authentication cookies with proper get/set/remove operations
 * - **SSR Integration**: Enables seamless authentication state sharing between server and client components
 * - **Request Forwarding**: Passes through requests while maintaining authentication context
 * - **Error Handling**: Gracefully handles Supabase client creation failures during development setup
 * - **Security Headers**: Maintains request headers and authentication state across the application
 *
 * ## Authentication Flow
 * 1. **Request Interception**: Middleware intercepts incoming requests before they reach page components
 * 2. **Session Validation**: Creates Supabase server client to validate current authentication session
 * 3. **Cookie Synchronization**: Ensures authentication cookies are synchronized between request and response
 * 4. **Session Refresh**: Calls `supabase.auth.getUser()` to refresh expired sessions automatically
 * 5. **Request Continuation**: Forwards request to destination with updated authentication context
 * 6. **Response Headers**: Ensures response includes updated authentication cookies and headers
 *
 * ## System Architecture
 * This middleware integrates with the broader EkoIntelligence authentication system:
 * - **Customer Database**: Supabase PostgreSQL with user profiles, organizations, and access controls
 * - **Row Level Security (RLS)**: Database policies that depend on authenticated user context
 * - **Client Components**: React components that consume authentication state via Supabase browser client
 * - **Server Components**: Next.js server components that access user data via Supabase server client
 * - **API Routes**: Backend API endpoints that require authenticated user context for ESG data access
 * - **Collaborative Editor**: Real-time document editing that requires persistent authentication sessions
 *
 * ## Cookie Management Strategy
 * The middleware implements Supabase's recommended cookie handling pattern:
 * - **Request Cookies**: Reads authentication cookies from incoming requests using `request.cookies.get()`
 * - **Response Cookies**: Sets updated authentication cookies on responses using `response.cookies.set()`
 * - **Cookie Options**: Respects security settings, expiration, and domain configuration for authentication cookies
 * - **Synchronization**: Ensures both request and response objects have consistent cookie state
 * - **Chunked Cookies**: Handles large authentication tokens that may be split across multiple cookies
 *
 * ## Security Considerations
 * - **Authentication Validation**: Validates user sessions on every server-side request
 * - **Secure Transmission**: Maintains secure cookie settings and HTTPS requirements
 * - **Session Expiry**: Automatically handles session expiration and refresh without user intervention
 * - **Request Integrity**: Preserves original request headers while adding authentication context
 * - **Error Isolation**: Gracefully handles authentication failures without breaking application functionality
 *
 * ## Development vs Production
 * - **Try/Catch Block**: Includes error handling for development setup where Supabase may not be configured
 * - **Environment Variables**: Requires `NEXT_PUBLIC_SUPABASE_URL` and `NEXT_PUBLIC_SUPABASE_ANON_KEY`
 * - **Graceful Degradation**: Returns unmodified response if Supabase client creation fails
 * - **Tutorial Integration**: Compatible with Supabase onboarding tutorials and development workflows
 *
 * ## Related Components
 * - Server-side Supabase client factory (`app/supabase/server.ts`) for API routes and server components
 * - Browser-side Supabase client factory (`app/supabase/client.ts`) for React components
 * - Main application middleware (`middleware.ts`) that delegates to this session management function
 * - Authentication components throughout the application that depend on session state
 * - Database RLS policies that enforce user-based access controls
 *
 * ## Performance Optimization
 * - **Minimal Overhead**: Lightweight session validation with minimal performance impact
 * - **Request Streaming**: Maintains request/response streaming capabilities
 * - **Cookie Efficiency**: Optimized cookie handling for minimal bandwidth usage
 * - **Caching Integration**: Works with Next.js caching strategies and Edge Runtime
 * - **Session Persistence**: Reduces authentication round trips through effective session management
 *
 * ## Error Scenarios
 * - **Missing Environment Variables**: Gracefully handles missing Supabase configuration
 * - **Network Issues**: Handles Supabase connectivity problems during session refresh
 * - **Expired Sessions**: Automatically refreshes expired authentication sessions
 * - **Invalid Cookies**: Handles corrupted or invalid authentication cookies
 * - **Database Connectivity**: Manages database connection issues during user validation
 *
 * @see https://supabase.com/docs/guides/auth/server-side/nextjs Supabase Next.js SSR Authentication Guide
 * @see https://nextjs.org/docs/app/building-your-application/routing/middleware Next.js Middleware Documentation
 * @see https://supabase.com/docs/reference/javascript/auth-getuser Supabase getUser API Reference
 * @see {@link ../server.ts} Server-side Supabase client factory for API routes and server components
 * @see {@link ../client.ts} Browser-side Supabase client factory for React components
 * @see {@link ../../middleware.ts} Main application middleware that uses this session management
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Supabase session management middleware for Next.js SSR authentication and cookie handling
 * @example ```typescript
 * import { updateSession } from '@/app/supabase/middleware'
 * import { NextRequest } from 'next/server'
 *
 * export async function middleware(request: NextRequest) {
 *   return await updateSession(request)
 * }
 *
 * // The middleware will automatically:
 * // 1. Create Supabase server client
 * // 2. Validate and refresh user session
 * // 3. Update authentication cookies
 * // 4. Forward request with auth context
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { type CookieOptions, createServerClient } from '@supabase/ssr'
import { type NextRequest, NextResponse } from 'next/server'

export const updateSession = async (request: NextRequest) => {
    // This `try/catch` block is only here for the interactive tutorial.
    // Feel free to remove once you have Supabase connected.
    try {
        // Create an unmodified response
        let response = NextResponse.next({
            request: {
                headers: request.headers,
            },
        });

        const supabase = createServerClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
            {
                cookies: {
                    get(name: string) {
                        return request.cookies.get(name)?.value;
                    },
                    set(name: string, value: string, options: CookieOptions) {
                        // If the cookie is updated, update the cookies for the request and response
                        request.cookies.set({
                            name,
                            value,
                            ...options,
                        });
                        response = NextResponse.next({
                            request: {
                                headers: request.headers,
                            },
                        });
                        response.cookies.set({
                            name,
                            value,
                            ...options,
                        });
                    },
                    remove(name: string, options: CookieOptions) {
                        // If the cookie is removed, update the cookies for the request and response
                        request.cookies.set({
                            name,
                            value: "",
                            ...options,
                        });
                        response = NextResponse.next({
                            request: {
                                headers: request.headers,
                            },
                        });
                        response.cookies.set({
                            name,
                            value: "",
                            ...options,
                        });
                    },
                },
            },
        );

        // This will refresh session if expired - required for Server Components
        // https://supabase.com/docs/guides/auth/server-side/nextjs
        await supabase.auth.getUser();

        return response;
    } catch (e) {
        // If you are here, a Supabase client could not be created!
        // This is likely because you have not set up environment variables.
        // Check out http://localhost:3000 for Next Steps.
        return NextResponse.next({
            request: {
                headers: request.headers,
            },
        });
    }
};
