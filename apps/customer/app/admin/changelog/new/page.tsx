/**
 * Next.js Admin Changelog Entry Creation Page - New Changelog Entry Form Interface
 *
 * This Admin interface provides a comprehensive form for creating new changelog entries within the
 * EkoIntelligence ESG analysis platform. The page enables administrators to document application
 * changes, feature releases, bug fixes, and improvements with structured categorization and
 * date-based organization for transparent communication with users about platform evolution.
 *
 * ## Core Functionality
 * - **Changelog Entry Creation**: Streamlined form interface for adding new application changelog entries
 * - **Entry Type Categorization**: Support for 'feature', 'fix', and 'improvement' entry types with clear descriptions
 * - **Date Management**: Date picker interface with default to current date and manual date selection capability
 * - **Form Validation**: Client-side validation ensuring all required fields are completed before submission
 * - **Admin Security**: Restricted access through admin authentication and database RLS policies
 * - **Glass-morphism UI**: Implements platform's signature translucent design with rounded elements
 *
 * ## Entry Type System
 * The page supports three distinct changelog entry categories:
 * - **Feature**: New functionality, major capabilities, or entirely new features for user-facing improvements
 * - **Improvement**: Enhancements to existing features, performance improvements, or UX/UI refinements
 * - **Fix**: Bug fixes, corrections to existing functionality, security patches, or minor corrections
 *
 * ## Database Integration
 * Integrates with the Supabase PostgreSQL app_changelog table through the customer database:
 * - **app_changelog table**: Stores changelog records with type, description, date, and user association
 * - **Field Mapping**: Form title maps to description field to match database schema expectations
 * - **User Association**: Automatically associates entries with authenticated admin user (created_by)
 * - **RLS Security**: Row Level Security policies ensure only admin users can create changelog entries
 * - **Data Validation**: Server-side validation through Supabase with comprehensive error handling
 *
 * ## Admin Panel Architecture
 * This page is part of the comprehensive admin system at `/admin/changelog` route:
 * - **Changelog Management**: Returns to main changelog listing page after successful entry creation
 * - **User Management**: Links to broader admin user and organization management interfaces
 * - **Content Management**: Part of application content management system for transparent communication
 * - **Audit Trail**: Changelog entries serve as historical record of platform development and changes
 *
 * ## Form Architecture & State Management
 * - **React State Management**: useState hooks for title, type, date, and submission state
 * - **Form Validation**: Client-side validation with user-friendly error messages and visual feedback
 * - **Submission Flow**: Async form submission with loading states and comprehensive error handling
 * - **Success Feedback**: Toast notifications and automatic navigation on successful entry creation
 * - **Date Handling**: HTML date input with ISO string formatting and current date default
 *
 * ## UI Components & Design System
 * Implements the platform's glass-morphism design system:
 * - **GlassCard Components**: Translucent form container and information guide card with backdrop blur
 * - **Form Controls**: shadcn/ui Input, Select, Label, and Button components with consistent styling
 * - **Icon Integration**: Lucide React icons (ArrowLeft, Plus, FileText, Calendar) for visual navigation cues
 * - **Responsive Design**: Mobile-first approach with max-width container and flexible form layout
 * - **Visual Hierarchy**: Clear section separation with entry type guide and form submission controls
 *
 * ## Technical Implementation Details
 * - **Next.js 15 App Router**: Client Component with 'use client' directive for interactive functionality
 * - **Supabase Integration**: Direct database operations using createClient() for authenticated requests
 * - **TypeScript Support**: Fully typed form state with proper type assertion for database operations
 * - **Error Handling**: Comprehensive try-catch blocks with user-friendly error messages
 * - **Navigation Control**: useRouter for programmatic navigation and Link components for static navigation
 * - **Toast Notifications**: Sonner integration for user feedback on form submission success/failure
 *
 * ## Security & Access Control
 * - **Admin Authentication**: Requires authenticated user session through Supabase auth.getUser()
 * - **RLS Policies**: Database-level security through "Only admins can access app_changelog" RLS policy
 * - **User Validation**: Prevents submission without valid authenticated admin user session
 * - **Input Sanitization**: Form data validation and trimming to prevent malicious input
 * - **Error Boundary**: Graceful error handling for authentication and database operation failures
 *
 * ## System Integration
 * This component fits into the broader ESG analysis platform administration:
 * - **Content Management**: Transparent communication about platform changes and improvements
 * - **User Experience**: Provides users visibility into platform development and feature releases
 * - **Admin Workflow**: Streamlined process for documenting development work and user-facing changes  
 * - **Audit Trail**: Historical record of platform evolution for compliance and user communication
 * - **Release Management**: Integration with development workflow for documenting feature releases
 *
 * ## Form Submission Workflow
 * 1. **Validation**: Client-side validation ensures title and type fields are completed
 * 2. **Authentication**: Verifies current user session and admin status before submission
 * 3. **Data Preparation**: Formats form data with proper field mapping (title -> description)
 * 4. **Database Insert**: Supabase insert operation with comprehensive error handling
 * 5. **Success Handling**: Toast notification and navigation back to changelog listing
 * 6. **Error Recovery**: User-friendly error messages with form state preservation for retry
 *
 * ## Performance Considerations
 * - **Client-Side Validation**: Immediate feedback without server round-trips for basic validation
 * - **Optimized Imports**: Selective component imports to minimize bundle size
 * - **State Management**: Minimal re-renders through efficient useState implementation
 * - **Database Operations**: Single insert operation with batch field updates
 * - **Navigation Efficiency**: Programmatic navigation only after successful operations
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts Next.js Pages and Layouts
 * @see https://supabase.com/docs/reference/javascript/auth-getuser Supabase Auth getUser Method
 * @see https://ui.shadcn.com/docs/components/form shadcn/ui Form Components
 * @see https://lucide.dev/icons/ Lucide React Icons
 * @see {@link ../page.tsx} Main Changelog Management Page
 * @see {@link ../../../components/ui/glass-card.tsx} GlassCard Component
 * @see {@link ../../../supabase/client.ts} Supabase Client Configuration
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This Admin interface provides a comprehensive form for creating new changelog entries within the EkoIntelligence ESG analysis platform with structured categorization and validation.
 * @example ```tsx
 * // Navigate to new changelog entry form
 * <Link href="/admin/changelog/new">
 *   <Plus className="h-4 w-4 mr-2" />
 *   Add Entry
 * </Link>
 * 
 * // Example changelog entry creation
 * const newEntry = {
 *   date: '2025-07-23',
 *   description: 'Added dark mode support',
 *   type: 'feature'
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import { useState } from 'react'
import { createClient } from '@/app/supabase/client'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { ArrowLeft, Plus, FileText, Calendar } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

export default function NewChangelogPage() {
  const [title, setTitle] = useState('')
  const [type, setType] = useState<'feature' | 'fix' | 'improvement' | ''>('')
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [saving, setSaving] = useState(false)
  
  const router = useRouter()
  const supabase = createClient()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!title.trim() || !type) {
      toast.error('Please fill in all required fields')
      return
    }

    setSaving(true)
    
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        toast.error('You must be logged in to create changelog entries')
        return
      }

      const { error } = await supabase
        .from('app_changelog')
        .insert({
          date: selectedDate,
          description: title.trim(), // Use title as description to match expected schema
          type
        } as any)

      if (error) throw error
      toast.success('Changelog entry created successfully')
      router.push('/admin/changelog')
    } catch (error) {
      console.error('Error creating changelog entry:', error)
      toast.error('Failed to create changelog entry')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/changelog">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Changelog
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <FileText className="h-8 w-8 mr-3 text-primary" />
          Add Changelog Entry
        </h1>
        <p className="text-muted-foreground">
          Create a new entry for the application changelog
        </p>
      </div>

      <div className="max-w-2xl">
        <GlassCard>
          <CardHeader>
            <CardTitle>New Changelog Entry</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Brief title of the change"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Date *</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="date"
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Type *</Label>
                <Select value={type} onValueChange={(value: any) => setType(value)} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select entry type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="feature">Feature - New functionality</SelectItem>
                    <SelectItem value="improvement">Improvement - Enhancement to existing feature</SelectItem>
                    <SelectItem value="fix">Fix - Bug fixes and corrections</SelectItem>
                  </SelectContent>
                </Select>
              </div>


              <div className="flex items-center space-x-4">
                <Button type="submit" disabled={saving}>
                  <Plus className="h-4 w-4 mr-2" />
                  {saving ? 'Creating...' : 'Create Entry'}
                </Button>
                <Button type="button" variant="outline" asChild>
                  <Link href="/admin/changelog">
                    Cancel
                  </Link>
                </Button>
              </div>
            </form>
          </CardContent>
        </GlassCard>

        <GlassCard className="mt-6">
          <CardHeader>
            <CardTitle>Entry Types Guide</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <strong>Feature:</strong> New functionality, major new capabilities, or entirely new features.
              </div>
              <div>
                <strong>Improvement:</strong> Enhancements to existing features, performance improvements, or UX improvements.
              </div>
              <div>
                <strong>Fix:</strong> Bug fixes, corrections to existing functionality, or minor fixes.
              </div>
            </div>
          </CardContent>
        </GlassCard>
      </div>
    </div>
  )
}