/**
 * Admin Changelog Management Dashboard Page for ESG Intelligence Platform
 *
 * This Next.js 15 App Router Server Component provides a comprehensive administrative interface for 
 * managing application changelog entries within the EkoIntelligence ESG (Environmental, Social, 
 * Governance) analysis platform. Designed exclusively for system administrators with elevated 
 * privileges, this dashboard enables viewing, creating, and managing changelog entries that track 
 * feature releases, bug fixes, and system improvements across the platform.
 *
 * ## Core Functionality
 * - **Changelog Overview**: Statistical dashboard showing total entries and type distribution
 * - **Recent Entries Display**: Chronological listing of the latest 50 changelog entries with full metadata
 * - **Entry Type Visualization**: Color-coded categorization of features, fixes, and improvements
 * - **Administrative Actions**: Quick access to create new entries and edit existing ones
 * - **Entry Type Guidelines**: Educational interface explaining proper classification of changelog entries
 * - **Glass-morphism UI**: Modern translucent design consistent with platform aesthetic
 *
 * ## Database Integration
 * **Supabase Integration**: Leverages Supabase PostgreSQL with Row Level Security (RLS) policies
 * ensuring only administrators (profiles.is_admin = true) can access changelog functionality.
 * The service connects to the `app_changelog` table structure:
 * ```sql
 * CREATE TABLE app_changelog (
 *   id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
 *   version varchar(50),                    -- Optional version identifier
 *   title varchar(255) NOT NULL,           -- Entry title/headline
 *   description text,                       -- Detailed change description
 *   type varchar(50) DEFAULT 'feature',    -- Entry classification (feature|fix|improvement)
 *   date timestamp DEFAULT now(),          -- Entry creation timestamp
 *   created_by uuid REFERENCES profiles(id) -- Admin user who created entry
 * );
 * ```
 *
 * ## Authentication & Authorization
 * **Admin-Only Access**: Protected by Supabase RLS policy restricting access to users with
 * `is_admin = true` in their profile. Unauthorized users cannot view or modify changelog data.
 * **Server-Side Authentication**: Uses Next.js 15 App Router server-side authentication with
 * Supabase SSR client for secure database operations without exposing sensitive data.
 *
 * ## Entry Type Classification System
 * **Feature Entries**: New functionality, enhancements, and major system improvements
 * - Visual Indicator: Blue badge with "default" variant styling
 * - Use Cases: New admin pages, dashboard features, integration capabilities
 *
 * **Fix Entries**: Bug fixes, corrections, and minor system improvements
 * - Visual Indicator: Green badge with "secondary" variant styling  
 * - Use Cases: Error corrections, UI fixes, data accuracy improvements
 *
 * **Improvement Entries**: Enhancements to existing functionality and features
 * - Visual Indicator: Purple badge with "outline" variant styling
 * - Use Cases: Performance optimizations, UX improvements, workflow enhancements
 *
 * ## UI Architecture
 * **Glass-morphism Design System**: Implements the platform's signature translucent glass-like
 * surfaces with backdrop blur effects, heavily rounded corners (1.5rem border radius), and
 * subtle depth through shadows and animations. The design maintains accessibility while
 * providing a modern, approachable interface.
 *
 * **Responsive Grid Layout**: Three-section dashboard with overview statistics (1 column),
 * recent entries list (3 columns), and entry type guidelines (full width) that adapts
 * seamlessly across desktop, tablet, and mobile viewports.
 *
 * **Interactive Elements**: Hover animations on action buttons, subtle lift effects on cards,
 * and smooth transitions that provide visual feedback and improve user experience.
 *
 * ## System Integration
 * This changelog management system integrates with the broader EkoIntelligence platform:
 * - **Admin Dashboard**: Central hub for all administrative functions including user management,
 *   organization settings, feature flags, and quota management
 * - **Version Control**: Tracks platform evolution alongside document versioning system
 * - **Audit Trail**: Maintains comprehensive change history for compliance and debugging
 * - **User Communication**: Provides transparent communication channel for platform updates
 *
 * ## Performance Considerations
 * **Optimized Queries**: Limits changelog retrieval to 50 most recent entries with efficient
 * date-based ordering to prevent performance issues with large changelog datasets.
 * **Server-Side Rendering**: Leverages Next.js 15 App Router SSR for optimal initial page load
 * times and SEO optimization while maintaining interactive client-side functionality.
 * **Caching Strategy**: Benefits from Next.js automatic caching of Server Component data with
 * appropriate cache invalidation on data mutations.
 *
 * ## Navigation Flow
 * **Entry Creation**: `/admin/changelog/new` - Form interface for creating new changelog entries
 * **Entry Editing**: `/admin/changelog/[id]/edit` - Edit interface for existing entries
 * **Delete Functionality**: In-page delete action (currently UI-only, requires implementation)
 * **Parent Navigation**: Accessible from main admin dashboard at `/admin`
 *
 * ## Related Components
 * - Admin layout and navigation system for privileged user interfaces
 * - GlassCard component providing consistent glass-morphism styling
 * - Supabase server client for secure database operations and authentication
 * - shadcn/ui components (Card, Button, Badge) for consistent design system adherence
 *
 * @see https://nextjs.org/docs/app/building-your-application/rendering/server-components Next.js Server Components
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security  
 * @see https://ui.shadcn.com/docs/components Shadcn UI Components
 * @see {@link ../../../components/ui/glass-card.tsx} GlassCard Component
 * @see {@link ../../supabase/server.ts} Supabase Server Client
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Admin dashboard for managing application changelog entries with statistical overview, recent entries display, and entry type classification system
 * @example
 * ```tsx
 * // Accessed via /admin/changelog route
 * // Displays recent changelog entries with admin controls
 * // Requires is_admin = true in user profile
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createClient } from '@/app/supabase/server'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { FileText, Plus, Edit, Trash2, Calendar } from 'lucide-react'
import Link from 'next/link'

export default async function ChangelogPage() {
  const supabase = await createClient()

  // Get changelog entries
  const { data: changelogEntries } = await supabase
    .from('app_changelog')
    .select('*')
    .order('date', { ascending: false })
    .limit(50)

  // Calculate stats
  const statsByType = changelogEntries?.reduce((acc, entry) => {
    acc[entry.type] = (acc[entry.type] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  const totalEntries = changelogEntries?.length || 0

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Changelog</h1>
          <p className="text-muted-foreground">
            Manage application changelog entries
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            {totalEntries} entries
          </Badge>
          <Button asChild>
            <Link href="/admin/changelog/new">
              <Plus className="h-4 w-4 mr-2" />
              Add Entry
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <GlassCard>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2 text-primary" />
              Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-3xl font-bold">{totalEntries}</p>
              <p className="text-sm text-muted-foreground">Total Entries</p>
            </div>
            <div className="space-y-2">
              {Object.entries(statsByType).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{type}</span>
                  <Badge variant="secondary" className="text-xs">
                    {count}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </GlassCard>

        <div className="md:col-span-3">
          <GlassCard>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2 text-primary" />
                Recent Entries
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {changelogEntries && changelogEntries.length > 0 ? (
                  changelogEntries.map((entry) => (
                    <div key={entry.id} className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge 
                              variant={
                                entry.type === 'feature' ? 'default' :
                                entry.type === 'fix' ? 'secondary' :
                                entry.type === 'improvement' ? 'outline' : 'outline'
                              }
                              className="text-xs"
                            >
                              {entry.type}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {new Date(entry.date).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-sm text-gray-900 leading-relaxed">
                            {entry.description}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/admin/changelog/${entry.id}/edit`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-8">
                    No changelog entries yet.
                  </p>
                )}
              </div>
            </CardContent>
          </GlassCard>
        </div>
      </div>

      <GlassCard>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-primary" />
            Entry Types
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="default">Feature</Badge>
                <span className="text-sm text-blue-800">New functionality</span>
              </div>
              <p className="text-xs text-blue-600">
                Use for new features, enhancements, and major improvements
              </p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="secondary">Fix</Badge>
                <span className="text-sm text-green-800">Bug fixes</span>
              </div>
              <p className="text-xs text-green-600">
                Use for bug fixes, corrections, and minor improvements
              </p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="outline">Improvement</Badge>
                <span className="text-sm text-purple-800">Enhancements</span>
              </div>
              <p className="text-xs text-purple-600">
                Use for improvements to existing functionality and features
              </p>
            </div>
          </div>
        </CardContent>
      </GlassCard>
    </div>
  )
}