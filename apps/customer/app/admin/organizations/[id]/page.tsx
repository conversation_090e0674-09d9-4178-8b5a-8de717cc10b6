/**
 * Next.js App Router Admin Interface for Organization Management
 *
 * This administrative page component provides comprehensive organization management capabilities for
 * platform administrators within the EkoIntelligence ESG analysis platform. The interface serves
 * as a central hub for organization administration, allowing authorized admin users to view
 * organization details and access related management functions including member administration,
 * quota management, and feature flag configuration.
 *
 * ## Core Functionality
 * - **Organization Overview**: Displays comprehensive organization information and metadata
 * - **Member Management**: Access to organization member list and user administration tools
 * - **Quota Administration**: Management of resource quotas and usage limits for the organization
 * - **Feature Flag Control**: Configuration of organization-level feature flags and permissions
 * - **Navigation Hub**: Central access point to all organization-specific administrative functions
 * - **Real-time Data**: Server-side rendering ensures fresh organization data on each page load
 *
 * ## Organization Information Display
 * The component presents essential organization data in an accessible format:
 * - **Organization Name**: Primary organization identifier and display name
 * - **Email Domain**: Associated email domain for user authentication and filtering
 * - **Entity Identifier**: External entity reference (entity_xid) for ESG analysis integration
 * - **Member Count**: Number of users associated with the organization
 * - **Quota Summary**: Overview of assigned resource quotas and current usage limits
 * - **Feature Flags**: Active organization-level feature flags and their current status
 *
 * ## Route Parameters
 * - **Dynamic Route**: `/admin/organizations/[id]` where `[id]` is the organization identifier
 * - **URL Structure**: Uses Next.js 15 App Router dynamic segments for organization-specific management
 * - **Parameter Validation**: Validates organization ID is numeric and organization exists in database
 * - **Server-Side Processing**: Organization ID processed server-side for security and performance
 *
 * ## Database Operations
 * - **Organizations Query**: Fetches organization data from `acc_organisations` table by ID
 * - **Members Query**: Counts organization members from `profiles` table with matching organization field
 * - **Quota Query**: Retrieves quota information from `acc_quota` table for organization overview
 * - **Foreign Key Relations**: Leverages database relationships for efficient data retrieval
 * - **Row Level Security**: Supabase RLS policies ensure admin users can only access authorized data
 *
 * ## Security & Access Control
 * - **Server Component**: Runs on server-side for enhanced security and data privacy
 * - **Admin Authorization**: Page accessible only to users with admin privileges (is_admin = true)
 * - **RLS Enforcement**: Database-level security policies restrict data access to authorized users
 * - **Parameter Validation**: Server-side validation prevents unauthorized organization access
 * - **Error Handling**: Graceful handling of invalid organization IDs and access violations
 *
 * ## User Interface Design
 * - **Glass-morphism Styling**: Consistent with platform design system using translucent GlassCard components
 * - **Card-based Layout**: Information organized in distinct cards for clarity and visual hierarchy
 * - **Action Buttons**: Quick access buttons to related administrative functions with clear labeling
 * - **Responsive Design**: Layout adapts to different screen sizes maintaining usability across devices
 * - **Icon Integration**: Lucide React icons provide visual cues for different administrative functions
 *
 * ## Component Architecture
 * - **Server Component**: Leverages Next.js 15 App Router Server Components for optimal performance
 * - **Async Data Fetching**: Uses async/await pattern for database queries during server-side rendering
 * - **Promise-based Parameters**: Handles Next.js 15 async params pattern for dynamic route parameters
 * - **Static Imports**: Components and utilities imported statically for optimal bundling
 * - **Error Boundaries**: Implements error handling for database query failures and invalid parameters
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side rendering and dynamic routing
 * - **Supabase Server Client**: Server-side database client with built-in authentication and RLS
 * - **TypeScript**: Full type safety for organization data structures and component props
 * - **shadcn/ui Components**: Design system components including Cards, Buttons for consistent UI
 * - **Lucide React Icons**: Icon library providing visual elements for administrative functions
 * - **Glass Card Component**: Custom glass-morphic card component for platform design consistency
 *
 * ## System Architecture Integration
 * This component serves as the central hub for organization administration within the EkoIntelligence platform:
 * - **Admin Dashboard**: Core component of the `/admin` section for comprehensive platform administration
 * - **Organization Hierarchy**: Manages organizational structure and member relationships
 * - **Resource Management**: Provides access to quota and usage management for enterprise control
 * - **Feature Control**: Enables organization-level feature flag management for customized experiences
 * - **User Administration**: Gateway to member management and user permission configuration
 *
 * ## Navigation & User Flow
 * - **Admin Dashboard Entry**: Accessible from main admin dashboard organization listing
 * - **Sub-function Access**: Provides navigation to specialized management pages (members, quotas, flags)
 * - **Breadcrumb Integration**: Supports breadcrumb navigation for hierarchical admin interface
 * - **Return Navigation**: Back navigation to organization list and broader admin functions
 *
 * ## Related Administrative Functions
 * - Organization Members Management (`./members/page.tsx`) - User administration for the organization
 * - Organization Quota Management (`./quotas/page.tsx`) - Resource limit and usage administration
 * - Organization Feature Flags (`./flags/page.tsx`) - Organization-level feature configuration
 * - Admin Dashboard (`/admin`) - Main administrative interface and organization listing
 *
 * ## Data Flow & Server-Side Processing
 * - **Request Processing**: Organization ID extracted from dynamic route parameters server-side
 * - **Database Queries**: Organization and related data fetched during server-side rendering
 * - **Data Validation**: Server-side validation ensures organization exists and user has access
 * - **Response Generation**: Complete page rendered server-side with organization data embedded
 * - **Error States**: Server-side error handling for missing organizations or access violations
 *
 * ## Database Schema Integration
 * **acc_organisations Table**: Organization master data and configuration
 * - `id: bigint` - Primary key for organization identification and route parameter matching
 * - `name: string | null` - Human-readable organization name for display and identification
 * - `email_domain: string | null` - Email domain for user authentication and filtering
 * - `entity_xid: string | null` - External entity reference for ESG analysis integration
 * - `feature_flags: string[]` - Array of organization-level feature flags and permissions
 * **profiles Table**: User profiles and organization membership relationships
 * - `organisation: bigint` - Foreign key reference to acc_organisations for membership tracking
 * **acc_quota Table**: Resource quotas and limits for organization management
 * - `organisation: bigint` - Foreign key reference for quota ownership and administration
 *
 * ## Performance Considerations
 * - **Server-Side Rendering**: Component rendered server-side for optimal performance and SEO
 * - **Single Database Query**: Efficient data fetching minimizes database round trips
 * - **Static Asset Optimization**: Icons and components bundled optimally for fast loading
 * - **Caching Strategy**: Leverages Next.js caching for improved response times on repeated requests
 * - **Selective Data Loading**: Only fetches necessary organization data to minimize transfer overhead
 *
 * ## Error Handling & Edge Cases
 * - **Invalid Organization ID**: Graceful handling of non-existent or inaccessible organizations
 * - **Database Connection Issues**: Proper error states for database connectivity problems
 * - **Authorization Failures**: Clear feedback for users without appropriate admin permissions
 * - **Missing Data**: Fallback displays for organizations with incomplete or missing information
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://nextjs.org/docs/app/building-your-application/rendering/server-components Server Components
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see https://supabase.com/docs/reference/javascript/select Supabase Query Operations
 * @see {@link ../supabase/server.ts} Supabase Server Client Configuration
 * @see {@link ../../../components/ui/glass-card.tsx} Glass Card UI Component
 * @see {@link ./members/page.tsx} Organization Members Management
 * @see {@link ./quotas/page.tsx} Organization Quota Management
 * @see {@link ./flags/page.tsx} Organization Feature Flags Management
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This administrative page component provides comprehensive organization management capabilities for platform administrators within the EkoIntelligence ESG analysis platform.
 * @example ```typescript
 * // Usage in Next.js App Router
 * // Route: /admin/organizations/123
 * // Automatically renders with organization ID 123
 * 
 * // Database operations:
 * // - Fetches organization by ID from acc_organisations table
 * // - Retrieves organization details including name, email_domain, entity_xid
 * // - Counts organization members from profiles table
 * // - Queries quota information for organization overview
 * 
 * // Administrative functions accessed:
 * // - Member management via ./members/page.tsx
 * // - Quota administration via ./quotas/page.tsx  
 * // - Feature flag configuration via ./flags/page.tsx
 * 
 * // Security: Admin-only access with RLS enforcement
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createClient } from '@/app/supabase/server'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Building, Users, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export default async function OrganizationDetailPage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const supabase = await createClient()
  const resolvedParams = await params
  const orgId = parseInt(resolvedParams.id)
  
  if (isNaN(orgId)) {
    notFound()
  }

  const { data: organization } = await supabase
    .from('acc_organisations')
    .select('*')
    .eq('id', orgId)
    .single()

  if (!organization) {
    notFound()
  }

  const { data: members } = await supabase
    .from('profiles')
    .select(`
      id, full_name, email, avatar_url
    `)
    .eq('organisation', orgId)

  const { data: quotas } = await supabase
    .from('acc_quota')
    .select('*')
    .eq('organisation', orgId)

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/organizations">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organizations
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <Building className="h-8 w-8 mr-3 text-primary" />
          {organization.name}
        </h1>
        <p className="text-muted-foreground">
          Organization details and management
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <GlassCard>
          <CardHeader>
            <CardTitle>Organization Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Name</label>
              <p className="text-sm text-muted-foreground">{organization.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Email Domain</label>
              <p className="text-sm text-muted-foreground">{organization.email_domain}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Entity XID</label>
              <p className="text-sm text-muted-foreground">{organization.entity_xid || 'Not set'}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Created</label>
              <p className="text-sm text-muted-foreground">
                {new Date(organization.created_at).toLocaleDateString()}
              </p>
            </div>
          </CardContent>
        </GlassCard>

        <GlassCard>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start" asChild>
              <Link href={`/admin/organizations/${orgId}/members`}>
                <Users className="h-4 w-4 mr-2" />
                Manage Members ({members?.length || 0})
              </Link>
            </Button>
            <Button className="w-full justify-start" variant="outline" asChild>
              <Link href={`/admin/organizations/${orgId}/flags`}>
                Manage Feature Flags ({organization.feature_flags?.length || 0})
              </Link>
            </Button>
            <Button className="w-full justify-start" variant="outline" asChild>
              <Link href={`/admin/organizations/${orgId}/quotas`}>
                Manage Quotas ({quotas?.length || 0})
              </Link>
            </Button>
          </CardContent>
        </GlassCard>
      </div>

      <div className="grid gap-6">
        <GlassCard>
          <CardHeader>
            <CardTitle>Feature Flags</CardTitle>
          </CardHeader>
          <CardContent>
            {organization.feature_flags && organization.feature_flags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {organization.feature_flags.map((flag: string, index: number) => (
                  <Badge key={index} variant="secondary">
                    {flag}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No feature flags configured for this organization.
              </p>
            )}
          </CardContent>
        </GlassCard>

        <GlassCard>
          <CardHeader>
            <CardTitle>Quotas</CardTitle>
          </CardHeader>
          <CardContent>
            {quotas && quotas.length > 0 ? (
              <div className="space-y-2">
                {quotas.map((quota) => (
                  <div key={quota.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="text-sm font-medium">{quota.item_type}</span>
                    <Badge variant="outline">{quota.quantity}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No quotas configured for this organization.
              </p>
            )}
          </CardContent>
        </GlassCard>
      </div>
    </div>
  )
}