/**
 * Next.js App Router Admin Interface for Organization Feature Flag Management
 *
 * This administrative page component provides comprehensive feature flag management capabilities for
 * organization administrators within the EkoIntelligence ESG analysis platform. The interface allows
 * authorized admin users to configure, validate, and maintain feature flags that control access to
 * various platform features and functionalities at the organizational level.
 *
 * ## Core Functionality
 * - **Feature Flag Management**: Add, remove, and modify feature flags for organization-level feature control
 * - **Pattern Validation**: Enforces structured feature flag naming conventions with regex validation
 * - **Real-time Updates**: Optimistic UI updates with database synchronization and error handling
 * - **Multi-Pattern Support**: Supports standard flags, wildcard patterns, and negation flags
 * - **Visual Flag Classification**: Color-coded badges and descriptions for different flag types
 * - **Batch Operations**: Save all changes at once with atomic database updates
 *
 * ## Feature Flag Patterns
 * The system supports multiple feature flag pattern types:
 * - **Standard Flags** (`feature.name`): Basic feature toggles for specific functionality
 * - **Wildcard Flags** (`feature.name.*`): Enables all sub-features within a feature namespace
 * - **Negation Flags** (`!feature.name`): Explicitly disables features or overrides defaults
 * - **Hierarchical Naming** (`api.v2.endpoints`): Structured naming for complex feature organization
 *
 * ## Route Parameters
 * - **Dynamic Route**: `/admin/organizations/[id]/flags` where `[id]` is the organization identifier
 * - **URL Structure**: Uses Next.js 15 App Router dynamic segments for organization-specific flag management
 * - **Parameter Validation**: Validates organization ID exists and user has appropriate permissions
 *
 * ## State Management
 * - **Organization State**: Manages current organization data including name and feature flag arrays
 * - **Form State**: Handles new flag input, validation states, and user interactions
 * - **Loading States**: Provides user feedback during data fetching and saving operations
 * - **Optimistic Updates**: Updates UI immediately while synchronizing with database in background
 *
 * ## Data Operations
 * - **Database Table**: `acc_organisations` with `feature_flags: string[] | null` column
 * - **CRUD Operations**: Create, read, update operations on organization feature flag arrays
 * - **Validation Layer**: Client-side regex validation before database updates
 * - **Error Handling**: Comprehensive error catching with user-friendly toast notifications
 *
 * ## Security & Access Control
 * - **Row Level Security**: Supabase RLS policies restrict access to user's own organization
 * - **Admin Authorization**: Page accessible only to users with admin privileges in the system
 * - **Data Validation**: Server-side and client-side validation of feature flag patterns
 * - **Audit Trail**: All changes are logged through standard database update mechanisms
 *
 * ## User Interface Design
 * - **Glass-morphism Styling**: Consistent with platform design system using translucent GlassCard components
 * - **Responsive Layout**: Grid-based layout that adapts to different screen sizes and devices
 * - **Interactive Elements**: Hover states, loading indicators, and immediate visual feedback
 * - **Accessibility**: Proper ARIA labels, keyboard navigation, and semantic HTML structure
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side rendering and dynamic routing
 * - **Supabase Client**: Database operations with built-in authentication and RLS security
 * - **React Hooks**: useState, useEffect, use for state management and side effects
 * - **UI Components**: shadcn/ui components for consistent design system integration
 * - **Toast Notifications**: Sonner for user feedback on success and error states
 *
 * ## System Architecture Integration
 * This component fits into the broader EkoIntelligence admin management system:
 * - **Admin Dashboard**: Part of the `/admin` section for platform administration
 * - **Organization Management**: Integrates with broader organization management workflows
 * - **Feature Control System**: Connects to backend feature flag evaluation and control systems
 * - **User Permission System**: Relies on profile-based admin authorization and organization membership
 * - **Database Schema**: Uses customer database `acc_organisations` table for persistent storage
 *
 * ## Related Components
 * - Organization Detail Page (parent route for comprehensive organization management)
 * - Admin User Management (for managing organization members and their permissions)  
 * - Feature Flag Evaluation System (backend logic that processes and applies feature flags)
 * - Admin Dashboard Navigation (main entry point for administrative functions)
 *
 * ## Database Schema Integration
 * **acc_organisations Table**: Organization master data with feature flag storage
 * - `id: number` - Primary key for organization identification
 * - `name: string | null` - Human-readable organization name for display
 * - `feature_flags: string[] | null` - PostgreSQL array of feature flag strings
 * **profiles Table**: Links users to organizations for RLS policy enforcement
 * **RLS Policies**: Restrict organization access to members only through profile relationships
 *
 * ## Performance & Optimization
 * - Client-side validation reduces server round trips and provides immediate user feedback
 * - Optimistic updates ensure responsive UI while maintaining data consistency
 * - Batch save operations minimize database transactions and improve performance
 * - React state management prevents unnecessary re-renders and maintains smooth interactions
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see https://react.dev/reference/react/hooks React Hooks Reference
 * @see {@link ../../../../supabase/client.ts} Supabase Client Configuration
 * @see {@link ../../../../../components/ui/glass-card.tsx} Glass-morphism Card Component
 * @see {@link ../page.tsx} Parent Organization Management Page
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This administrative page component provides comprehensive feature flag management capabilities for organization administrators within the EkoIntelligence ESG analysis platform.
 * @example ```typescript
 * // Usage in Next.js App Router
 * // Route: /admin/organizations/123/flags
 * // Automatically renders with organization ID 123
 * export default function OrganizationFlagsPage({ params }: { params: Promise<{ id: string }> })
 * 
 * // Example feature flag patterns:
 * // Standard: "dashboard.analytics"
 * // Wildcard: "api.v2.*" 
 * // Negation: "!legacy.features"
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import { useState, useEffect, use } from 'react'
import { createClient } from '@/app/supabase/client'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Plus, X, Settings, Building } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { notFound } from 'next/navigation'

interface Organization {
  id: number
  name: string | null
  feature_flags: string[] | null
}

export default function OrganizationFlagsPage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [newFlag, setNewFlag] = useState('')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  
  const router = useRouter()
  const supabase = createClient()
  const resolvedParams = use(params)
  const orgId = parseInt(resolvedParams.id)

  useEffect(() => {
    if (!isNaN(orgId)) {
      fetchOrganization()
    }
  }, [orgId])

  const fetchOrganization = async () => {
    if (isNaN(orgId)) return
    
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('acc_organisations')
        .select('*')
        .eq('id', orgId)
        .single()

      if (error) throw error
      if (!data) {
        notFound()
        return
      }

      setOrganization(data)
    } catch (error) {
      console.error('Error fetching organization:', error)
      toast.error('Failed to load organization')
    } finally {
      setLoading(false)
    }
  }

  const handleAddFlag = () => {
    if (!newFlag.trim() || !organization) return

    const flags = organization.feature_flags || []
    if (flags.includes(newFlag.trim())) {
      toast.error('Flag already exists')
      return
    }

    setOrganization({
      ...organization,
      feature_flags: [...flags, newFlag.trim()]
    })
    setNewFlag('')
  }

  const handleRemoveFlag = (flagToRemove: string) => {
    if (!organization) return

    setOrganization({
      ...organization,
      feature_flags: (organization.feature_flags || []).filter(flag => flag !== flagToRemove)
    })
  }

  const handleSave = async () => {
    if (!organization) return

    setSaving(true)
    try {
      const { error } = await supabase
        .from('acc_organisations')
        .update({
          feature_flags: organization.feature_flags || []
        })
        .eq('id', orgId!)

      if (error) throw error

      toast.success('Feature flags updated successfully')
      router.refresh()
    } catch (error) {
      console.error('Error updating feature flags:', error)
      toast.error('Failed to update feature flags')
    } finally {
      setSaving(false)
    }
  }

  const validateFlag = (flag: string) => {
    // Check for valid patterns: x.y.z, !x.y.z, x.y.*, etc.
    const patterns = [
      /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*(\.\*)?$/, // x.y.z or x.y.*
      /^![a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*(\.\*)?$/, // !x.y.z or !x.y.*
    ]
    
    return patterns.some(pattern => pattern.test(flag))
  }

  const getFlagTypeColor = (flag: string) => {
    if (flag.startsWith('!')) return 'destructive'
    if (flag.endsWith('.*')) return 'secondary'
    return 'default'
  }

  const getFlagDescription = (flag: string) => {
    if (flag.startsWith('!')) return 'Negation flag (disabled)'
    if (flag.endsWith('.*')) return 'Wildcard flag (matches all sub-features)'
    return 'Standard feature flag'
  }

  if (loading) {
    return <div>Loading...</div>
  }

  if (!organization) {
    return notFound()
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/admin/organizations/${orgId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organization
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <Building className="h-8 w-8 mr-3 text-primary" />
          {organization.name} - Feature Flags
        </h1>
        <p className="text-muted-foreground">
          Manage feature flags for this organization
        </p>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <GlassCard>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Current Feature Flags ({(organization.feature_flags || []).length})</span>
                <Button onClick={handleSave} disabled={saving}>
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {(organization.feature_flags || []).map((flag, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Badge variant={getFlagTypeColor(flag)}>
                        {flag}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {getFlagDescription(flag)}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveFlag(flag)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                {(!organization.feature_flags || organization.feature_flags.length === 0) && (
                  <p className="text-center text-muted-foreground py-8">
                    No feature flags configured for this organization.
                  </p>
                )}
              </div>
            </CardContent>
          </GlassCard>
        </div>

        <div className="space-y-6">
          <GlassCard>
            <CardHeader>
              <CardTitle>Add Feature Flag</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="newFlag">Flag Pattern</Label>
                <div className="flex space-x-2">
                  <Input
                    id="newFlag"
                    value={newFlag}
                    onChange={(e) => setNewFlag(e.target.value)}
                    placeholder="feature.new.awesome"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddFlag()}
                  />
                  <Button 
                    onClick={handleAddFlag}
                    disabled={!newFlag.trim() || !validateFlag(newFlag.trim())}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {newFlag && !validateFlag(newFlag) && (
                  <p className="text-xs text-red-500">
                    Invalid flag pattern
                  </p>
                )}
              </div>
            </CardContent>
          </GlassCard>

          <GlassCard>
            <CardHeader>
              <CardTitle>Flag Patterns</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2 text-xs">
                <div>
                  <Badge variant="default" className="text-xs mb-1">feature.name</Badge>
                  <p className="text-muted-foreground">Standard feature flag</p>
                </div>
                <div>
                  <Badge variant="secondary" className="text-xs mb-1">feature.name.*</Badge>
                  <p className="text-muted-foreground">Wildcard (all sub-features)</p>
                </div>
                <div>
                  <Badge variant="destructive" className="text-xs mb-1">!feature.name</Badge>
                  <p className="text-muted-foreground">Negation (disable feature)</p>
                </div>
                <div>
                  <Badge variant="outline" className="text-xs mb-1">api.v2.endpoints</Badge>
                  <p className="text-muted-foreground">Hierarchical naming</p>
                </div>
              </div>
            </CardContent>
          </GlassCard>
        </div>
      </div>
    </div>
  )
}