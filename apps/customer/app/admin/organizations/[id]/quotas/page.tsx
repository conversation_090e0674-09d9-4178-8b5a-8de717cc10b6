/**
 * Next.js App Router Admin Interface for Organization Quota Management
 *
 * This administrative page component provides comprehensive quota management capabilities for
 * organization administrators within the EkoIntelligence ESG analysis platform. The interface allows
 * authorized admin users to view, create, update, and remove quota limits for various resource types
 * within an organization, enabling granular control over platform usage and resource allocation.
 *
 * ## Core Functionality
 * - **Quota Listing**: Displays current organization quotas with human-readable labels and quantities
 * - **Quota Creation**: Add new quota limits for available resource types via dropdown selection
 * - **Quota Updates**: Inline editing of quota quantities with immediate database synchronization
 * - **Quota Removal**: Delete quota entries with optimistic UI updates and error handling
 * - **Resource Type Management**: Support for entities, documents, API calls, storage, users, reports, and exports
 * - **Real-time Updates**: Optimistic UI updates with database synchronization and toast notifications
 *
 * ## Quota Resource Types
 * The system supports seven distinct quota types for comprehensive resource management:
 * - **Entity Analysis**: Limits for ESG entity analysis operations and processing
 * - **Document Processing**: Quotas for document uploads, analysis, and processing operations
 * - **API Calls**: Rate limiting and usage quotas for API endpoint access and consumption
 * - **Storage (GB)**: File storage limits for documents, reports, and media assets
 * - **User Accounts**: Maximum number of user accounts allowed within the organization
 * - **Custom Reports**: Limits for generating custom ESG reports and analytics
 * - **Data Exports**: Quotas for exporting data in various formats (CSV, PDF, Excel)
 *
 * ## Route Parameters
 * - **Dynamic Route**: `/admin/organizations/[id]/quotas` where `[id]` is the organization identifier
 * - **URL Structure**: Uses Next.js 15 App Router dynamic segments for organization-specific quota management
 * - **Parameter Validation**: Validates organization ID is numeric and organization exists in database
 * - **Navigation**: Seamless integration with organization management breadcrumb navigation
 *
 * ## Database Operations
 * - **Organizations Table**: `acc_organisations` - Stores organization master data and metadata
 * - **Quotas Table**: `acc_quota` - Quota definitions with item types, quantities, and scope settings
 * - **Quota Queries**: Fetches quotas by matching `organisation` field to organization ID
 * - **CRUD Operations**: Full create, read, update, delete operations for quota management
 * - **Data Validation**: Enforces positive numeric values and prevents duplicate quota types
 *
 * ## Security & Access Control
 * - **Row Level Security**: Supabase RLS policies restrict quota access to authorized admin users only
 * - **Admin Authorization**: Page accessible only to users with admin privileges in the system
 * - **Foreign Key Constraints**: Database enforces referential integrity between quotas and organizations
 * - **Input Validation**: Client-side and server-side validation for quota quantities and types
 *
 * ## User Interface Design
 * - **Glass-morphism Styling**: Consistent with platform design system using translucent GlassCard components
 * - **Three-Column Layout**: Current quotas list, add quota form, and quota types reference panel
 * - **Inline Editing**: Direct quantity editing with onBlur save functionality for seamless updates
 * - **Responsive Design**: Adapts to different screen sizes with responsive grid and component layouts
 * - **Interactive Elements**: Hover states, loading indicators, and confirmation dialogs for operations
 *
 * ## Component Architecture
 * - **Client Component**: Uses 'use client' directive for interactive quota management operations
 * - **State Management**: React useState hooks manage quotas, form inputs, and loading states
 * - **Effect Hooks**: useEffect for data fetching on component mount and organization changes
 * - **Event Handlers**: Comprehensive handlers for add, update, and remove quota operations
 * - **Optimistic Updates**: Immediate UI updates with proper error handling and reversion
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side rendering and dynamic routing
 * - **Supabase Client**: Database operations with built-in authentication and RLS security
 * - **React Hooks**: useState, useEffect, and useRouter for state management and navigation
 * - **shadcn/ui Components**: Design system components including Cards, Buttons, Inputs, Selects
 * - **Lucide React Icons**: Icon library for visual elements and user interface enhancement
 * - **Sonner**: Toast notification system for user feedback on operations and errors
 *
 * ## System Architecture Integration
 * This component fits into the broader EkoIntelligence admin management system:
 * - **Admin Dashboard**: Part of the `/admin` section for comprehensive platform administration
 * - **Organization Management**: Integrates with organization detail pages and related admin functions
 * - **Resource Control**: Enables fine-grained control over platform resource consumption
 * - **Usage Monitoring**: Supports usage tracking and billing integration for enterprise clients
 * - **Database Layer**: Uses customer database for quota definitions and organizational relationships
 *
 * ## Related Components
 * - Organization Detail Page (parent route providing organizational context and navigation)
 * - Organization Members Management (sibling admin page for user management)
 * - Organization Feature Flags (sibling admin page for feature control)
 * - Quota Monitoring Dashboard (related analytics and usage tracking components)
 * - Admin Navigation System (breadcrumb navigation and admin dashboard integration)
 *
 * ## Data Flow & State Management
 * - **Initial Data Load**: Organization and quota data fetched via useEffect on component mount
 * - **Form State**: Add quota form managed with controlled inputs and validation
 * - **Optimistic Updates**: Add/remove operations update local state immediately with database sync
 * - **Error Handling**: Comprehensive error catching with user-friendly toast notifications
 * - **Loading States**: Visual loading indicators during data fetching and save operations
 *
 * ## Database Schema Integration
 * **acc_organisations Table**: Organization master data and relationship anchor
 * - `id: bigint` - Primary key for organization identification and foreign key relationships
 * - `name: string | null` - Human-readable organization name for display in quota context
 * **acc_quota Table**: Quota definitions and limits for resource management
 * - `id: bigint` - Primary key for quota identification and management operations
 * - `organisation: bigint` - Foreign key to acc_organisations for ownership relationship
 * - `item_type: quota_item_type` - Enum defining quota resource type (entities, documents, etc.)
 * - `quantity: number` - Numeric limit value for the specified resource type
 * - `created_at: timestamp` - Audit trail for quota creation and management history
 *
 * ## Performance Considerations
 * - **Client-Side Rendering**: Component uses client-side rendering for interactive quota management
 * - **Optimistic Updates**: Immediate UI feedback reduces perceived latency for user operations
 * - **Selective Queries**: Only fetches necessary quota and organization data to minimize transfer
 * - **Efficient State Updates**: Local state management reduces unnecessary re-renders and API calls
 * - **Input Debouncing**: Quantity updates use onBlur to prevent excessive database writes
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://nextjs.org/docs/app/building-your-application/rendering/client-components Client Components
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see https://supabase.com/docs/reference/javascript/select Supabase Query Operations
 * @see {@link ../../../../supabase/client.ts} Supabase Client Configuration
 * @see {@link ../../../../components/ui/glass-card.tsx} Glass Card UI Component
 * @see {@link ../page.tsx} Parent Organization Management Page
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This administrative page component provides comprehensive quota management capabilities for organization administrators within the EkoIntelligence ESG analysis platform.
 * @example ```typescript
 * // Usage in Next.js App Router
 * // Route: /admin/organizations/123/quotas
 * // Automatically renders with organization ID 123
 * 
 * // Database operations:
 * // - Fetches organization by ID from acc_organisations table
 * // - Queries quotas where acc_quota.organisation = orgId
 * // - Supports CRUD operations for quota management
 * 
 * // Quota types supported:
 * // - entities: Entity Analysis quotas
 * // - documents: Document Processing quotas  
 * // - api_calls: API rate limiting quotas
 * // - storage: Storage limit quotas (GB)
 * // - users: User account quotas
 * // - reports: Custom report generation quotas
 * // - exports: Data export quotas
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/app/supabase/client'
import { GlassCard } from '@/components/ui/glass-card'
import { CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowLeft, Building, Plus, Settings, X } from 'lucide-react'
import Link from 'next/link'
import { notFound, useRouter } from 'next/navigation'
import { toast } from 'sonner'

interface Organization {
  id: number
  name: string | null
}

interface Quota {
  id: number
  item_type: string
  quantity: number
  created_at: string
}

const QUOTA_TYPES = [
  { value: 'entities', label: 'Entity Analysis' },
  { value: 'documents', label: 'Document Processing' },
  { value: 'api_calls', label: 'API Calls' },
  { value: 'storage', label: 'Storage (GB)' },
  { value: 'users', label: 'User Accounts' },
  { value: 'reports', label: 'Custom Reports' },
  { value: 'exports', label: 'Data Exports' },
]

function OrganizationQuotasPageContent({ orgId }: { orgId: number }) {
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [quotas, setQuotas] = useState<Quota[]>([])
  const [newQuotaType, setNewQuotaType] = useState('')
  const [newQuotaQuantity, setNewQuotaQuantity] = useState('')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    fetchData()
  }, [orgId])

  const fetchData = async () => {
    setLoading(true)
    try {
      // Fetch organization
      const { data: org, error: orgError } = await supabase
        .from('acc_organisations')
        .select('id, name')
        .eq('id', orgId)
        .single()

      if (orgError) throw orgError
      if (!org) {
        notFound()
        return
      }

      setOrganization(org)

      // Fetch quotas
      const { data: quotaData, error: quotaError } = await supabase
        .from('acc_quota')
        .select('*')
        .eq('organisation', orgId)
        .order('item_type')

      if (quotaError) throw quotaError
      setQuotas(quotaData || [])

    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load organization data')
    } finally {
      setLoading(false)
    }
  }

  const handleAddQuota = async () => {
    if (!newQuotaType || !newQuotaQuantity || !organization) return

    const quantity = parseInt(newQuotaQuantity)
    if (isNaN(quantity) || quantity < 0) {
      toast.error('Please enter a valid quantity')
      return
    }

    // Check if quota type already exists
    if (quotas.some(q => q.item_type === newQuotaType)) {
      toast.error('Quota for this item type already exists')
      return
    }

    setSaving(true)
    try {
      const { data, error } = await supabase
        .from('acc_quota')
        .insert({
          organisation: orgId,
          item_type: newQuotaType as any,
          quantity: quantity
        })
        .select()
        .single()

      if (error) throw error

      setQuotas([...quotas, data])
      setNewQuotaType('')
      setNewQuotaQuantity('')
      toast.success('Quota added successfully')
    } catch (error) {
      console.error('Error adding quota:', error)
      toast.error('Failed to add quota')
    } finally {
      setSaving(false)
    }
  }

  const handleUpdateQuota = async (quotaId: number, newQuantity: number) => {
    if (isNaN(newQuantity) || newQuantity < 0) {
      toast.error('Please enter a valid quantity')
      return
    }

    try {
      const { error } = await supabase
        .from('acc_quota')
        .update({ quantity: newQuantity })
        .eq('id', quotaId)

      if (error) throw error

      setQuotas(quotas.map(q => 
        q.id === quotaId ? { ...q, quantity: newQuantity } : q
      ))
      toast.success('Quota updated successfully')
    } catch (error) {
      console.error('Error updating quota:', error)
      toast.error('Failed to update quota')
    }
  }

  const handleRemoveQuota = async (quotaId: number) => {
    try {
      const { error } = await supabase
        .from('acc_quota')
        .delete()
        .eq('id', quotaId)

      if (error) throw error

      setQuotas(quotas.filter(q => q.id !== quotaId))
      toast.success('Quota removed successfully')
    } catch (error) {
      console.error('Error removing quota:', error)
      toast.error('Failed to remove quota')
    }
  }

  const getQuotaLabel = (itemType: string) => {
    const quota = QUOTA_TYPES.find(q => q.value === itemType)
    return quota ? quota.label : itemType
  }

  if (loading) {
    return <div>Loading...</div>
  }

  if (!organization) {
    return notFound()
  }

  const availableQuotaTypes = QUOTA_TYPES.filter(
    type => !quotas.some(q => q.item_type === type.value)
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/admin/organizations/${orgId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organization
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <Building className="h-8 w-8 mr-3 text-primary" />
          {organization.name} - Quotas
        </h1>
        <p className="text-muted-foreground">
          Manage quota limits for this organization
        </p>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <GlassCard>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-primary" />
                Current Quotas ({quotas.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {quotas.length > 0 ? (
                  quotas.map((quota) => (
                    <div key={quota.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div>
                          <p className="font-medium">{getQuotaLabel(quota.item_type)}</p>
                          <p className="text-sm text-muted-foreground">{quota.item_type}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Input
                          type="number"
                          min="0"
                          className="w-24 text-center"
                          defaultValue={quota.quantity}
                          onBlur={(e) => {
                            const newValue = parseInt(e.target.value)
                            if (newValue !== quota.quantity) {
                              handleUpdateQuota(quota.id, newValue)
                            }
                          }}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveQuota(quota.id)}
                        >
                          <X className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-center text-muted-foreground py-8">
                    No quotas configured for this organization.
                  </p>
                )}
              </div>
            </CardContent>
          </GlassCard>
        </div>

        <div className="space-y-6">
          <GlassCard>
            <CardHeader>
              <CardTitle>Add Quota</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="quotaType">Quota Type</Label>
                <Select value={newQuotaType} onValueChange={setNewQuotaType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select quota type" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableQuotaTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="0"
                  value={newQuotaQuantity}
                  onChange={(e) => setNewQuotaQuantity(e.target.value)}
                  placeholder="Enter quota limit"
                />
              </div>

              <Button
                onClick={handleAddQuota}
                disabled={!newQuotaType || !newQuotaQuantity || saving}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                {saving ? 'Adding...' : 'Add Quota'}
              </Button>
            </CardContent>
          </GlassCard>

          <GlassCard>
            <CardHeader>
              <CardTitle>Quota Types</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2 text-xs">
                {QUOTA_TYPES.map((type) => (
                  <div key={type.value} className="p-2 bg-gray-50 rounded">
                    <p className="font-medium">{type.label}</p>
                    <p className="text-muted-foreground">{type.value}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </GlassCard>
        </div>
      </div>
    </div>
  )
}

export default async function OrganizationQuotasPage({
                                                       params,
                                                     }: {
  params: Promise<{ id: string }>
}) {
  const { id } = await params
  const orgId = parseInt(id)

  return <OrganizationQuotasPageContent orgId={orgId} />
}
