/**
 * Next.js App Router Admin Interface for Organization Member Management
 *
 * This administrative page component provides comprehensive member management capabilities for
 * organization administrators within the EkoIntelligence ESG analysis platform. The interface allows
 * authorized admin users to view, add, and remove organization members, managing the relationship
 * between user profiles and organizational structures for access control and collaboration.
 *
 * ## Core Functionality  
 * - **Member Listing**: Displays current organization members with profile information and admin status
 * - **Member Addition**: Add existing platform users to the organization via dropdown selection
 * - **Member Removal**: Remove members from organization with confirmation dialog and optimistic updates
 * - **Profile Integration**: Deep linking to individual user profile management pages for extended admin tasks  
 * - **Real-time Updates**: Optimistic UI updates with database synchronization and error handling
 * - **Admin Badge Display**: Visual indicators showing system administrator status for members
 *
 * ## Member Management Operations
 * The system supports comprehensive member management:
 * - **Add Members**: Select from available users without organizations to add as members
 * - **Remove Members**: Detach users from organization (sets `organisation` field to `null`)
 * - **View Members**: Display member profiles with avatars, names, emails, and admin status
 * - **Profile Navigation**: Direct links to individual user management pages for detailed operations
 *
 * ## Route Parameters
 * - **Dynamic Route**: `/admin/organizations/[id]/members` where `[id]` is the organization identifier
 * - **URL Structure**: Uses Next.js 15 App Router dynamic segments for organization-specific member management
 * - **Parameter Validation**: Validates organization ID is numeric and organization exists in database
 *
 * ## Database Operations
 * - **Organizations Table**: `acc_organisations` - Stores organization master data and metadata
 * - **Profiles Table**: `profiles` - User profile data with `organisation` foreign key relationship
 * - **Member Queries**: Fetches members by matching `organisation` field to organization ID
 * - **Available Users**: Queries users with `organisation IS NULL` for addition candidates
 * - **Membership Management**: Updates `profiles.organisation` field for add/remove operations
 *
 * ## Security & Access Control
 * - **Row Level Security**: Supabase RLS policies restrict organization access to members only  
 * - **Admin Authorization**: Page accessible only to users with admin privileges in the system
 * - **Foreign Key Constraints**: Database enforces referential integrity between profiles and organizations
 * - **Cascading Updates**: Organization deletions properly handle member relationship cleanup
 *
 * ## User Interface Design
 * - **Glass-morphism Styling**: Consistent with platform design system using translucent GlassCard components
 * - **Grid Layout**: Two-column layout with member list on left and add member form on right
 * - **Member Cards**: Individual member cards showing avatar, name, email, and action buttons
 * - **Responsive Design**: Adapts to different screen sizes with responsive grid and component layouts
 * - **Interactive Elements**: Hover states, loading indicators, and confirmation dialogs for destructive actions
 *
 * ## Component Architecture
 * - **Server Component**: Uses React Server Components for initial data fetching and rendering
 * - **Client Components**: AddMemberForm and RemoveMemberButton handle interactive operations
 * - **State Management**: Client components manage form state and optimistic updates independently
 * - **Navigation Integration**: Back button and profile links provide seamless admin workflow navigation
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side rendering and dynamic routing
 * - **Supabase Server Client**: Database operations with built-in authentication and RLS security
 * - **React Server Components**: Server-side rendering for optimal performance and SEO
 * - **shadcn/ui Components**: Design system components for consistent styling and behavior
 * - **Lucide React Icons**: Icon library for visual elements and user interface enhancement
 *
 * ## System Architecture Integration
 * This component fits into the broader EkoIntelligence admin management system:
 * - **Admin Dashboard**: Part of the `/admin` section for comprehensive platform administration
 * - **Organization Management**: Integrates with organization detail pages and related admin functions
 * - **User Management System**: Connects to individual user profile management and permission systems  
 * - **Access Control Layer**: Supports organizational access control and feature flag management
 * - **Database Layer**: Uses customer database for user profiles and organizational relationships
 *
 * ## Related Components
 * - Organization Detail Page (parent route providing organizational context and navigation)
 * - Individual User Management Pages (linked via profile buttons for detailed user administration)
 * - AddMemberForm Component (client-side form for adding users to organization membership)
 * - RemoveMemberButton Component (confirmation dialog and removal functionality for members)
 * - Admin Navigation System (breadcrumb navigation and admin dashboard integration)
 *
 * ## Data Flow & State Management
 * - **Server-Side Rendering**: Initial organization and member data fetched server-side for performance
 * - **Client-Side Updates**: Add/remove operations handled by client components with optimistic updates  
 * - **Database Synchronization**: All changes persisted to PostgreSQL with proper transaction handling
 * - **Error Handling**: Comprehensive error catching with user-friendly toast notifications
 * - **Navigation Refresh**: Router.refresh() ensures data consistency after member operations
 *
 * ## Database Schema Integration
 * **acc_organisations Table**: Organization master data and relationship anchor
 * - `id: bigint` - Primary key for organization identification and foreign key relationships
 * - `name: string | null` - Human-readable organization name for display in member context
 * **profiles Table**: User profile data with organizational relationship
 * - `id: uuid` - Primary key linking to auth.users for authentication integration  
 * - `organisation: bigint | null` - Foreign key to acc_organisations, null indicates unaffiliated user
 * - `full_name: string | null` - Display name for member listing and identification
 * - `email: string | null` - Contact information and unique identifier for users
 * - `avatar_url: string | null` - Profile image URL for member avatar display
 * - `is_admin: boolean` - System administrator flag for privilege indication
 *
 * ## Performance Considerations
 * - **Server Components**: Initial rendering happens server-side for faster page loads and better SEO
 * - **Selective Queries**: Only fetches necessary fields to minimize data transfer and processing
 * - **Optimistic Updates**: Client components provide immediate UI feedback while syncing with database
 * - **Efficient Joins**: Database queries use indexed foreign key relationships for optimal performance
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://nextjs.org/docs/app/building-your-application/rendering/server-components React Server Components  
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see https://supabase.com/docs/reference/javascript/auth Supabase Authentication
 * @see {@link ../../../supabase/server.ts} Supabase Server Client Configuration
 * @see {@link ../../../../components/admin/AddMemberForm.tsx} Add Member Form Component
 * @see {@link ../../../../components/admin/RemoveMemberButton.tsx} Remove Member Button Component
 * @see {@link ../page.tsx} Parent Organization Management Page
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This administrative page component provides comprehensive member management capabilities for organization administrators within the EkoIntelligence ESG analysis platform.
 * @example ```typescript
 * // Usage in Next.js App Router
 * // Route: /admin/organizations/123/members
 * // Automatically renders with organization ID 123
 * export default async function OrganizationMembersPage({ params }: { params: Promise<{ id: string }> })
 * 
 * // Database operations:
 * // - Fetches organization by ID from acc_organisations table
 * // - Queries members where profiles.organisation = orgId
 * // - Lists available users where profiles.organisation IS NULL  
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createClient } from '@/app/supabase/server'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Users, ArrowLeft, UserPlus, ExternalLink } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { AddMemberForm } from '@/components/admin/AddMemberForm'
import { RemoveMemberButton } from '@/components/admin/RemoveMemberButton'

export default async function OrganizationMembersPage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const supabase = await createClient()
  const resolvedParams = await params
  const orgId = parseInt(resolvedParams.id)
  
  if (isNaN(orgId)) {
    notFound()
  }

  const { data: organization } = await supabase
    .from('acc_organisations')
    .select('*')
    .eq('id', orgId)
    .single()

  if (!organization) {
    notFound()
  }

  const { data: members } = await supabase
    .from('profiles')
    .select(`
      id, full_name, email, avatar_url, is_admin
    `)
    .eq('organisation', orgId)

  const { data: allUsers } = await supabase
    .from('profiles')
    .select('id, full_name, email, avatar_url')
    .is('organisation', null)
    .order('full_name')

  // Available users are those without an organization
  const availableUsers = allUsers || []

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/admin/organizations/${orgId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organization
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <Users className="h-8 w-8 mr-3 text-primary" />
          {organization.name} Members
        </h1>
        <p className="text-muted-foreground">
          Manage organization members and their roles
        </p>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <GlassCard>
            <CardHeader>
              <CardTitle>Current Members ({members?.length ?? 0})</CardTitle>
            </CardHeader>
            <CardContent>
              {members && members.length > 0 ? (
                <div className="space-y-4">
                  {members.map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarImage src={member.avatar_url || undefined} />
                          <AvatarFallback>
                            {member.full_name?.split(' ').map(n => n[0]).join('') || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{member.full_name}</p>
                          <p className="text-sm text-muted-foreground">{member.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {member.is_admin && (
                          <Badge variant="default">System Admin</Badge>
                        )}
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/users/${member.id}`}>
                            <ExternalLink className="h-4 w-4" />
                          </Link>
                        </Button>
                        <RemoveMemberButton 
                          userId={member.id} 
                          userName={member.full_name || 'Unknown'}
                          organizationId={orgId}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground py-8">
                  No members in this organization yet.
                </p>
              )}
            </CardContent>
          </GlassCard>
        </div>

        <div>
          <GlassCard>
            <CardHeader>
              <CardTitle>Add Member</CardTitle>
            </CardHeader>
            <CardContent>
              <AddMemberForm 
                organizationId={orgId}
                availableUsers={availableUsers}
              />
            </CardContent>
          </GlassCard>
        </div>
      </div>
    </div>
  )
}