# Organization Administration Module

## Overview

The Organization Administration Module (`/apps/customer/app/admin/organizations/[id]`) provides comprehensive
administrative capabilities for managing individual organizations within the EkoIntelligence ESG analysis platform. This
module serves as the central hub for organization-specific administrative functions, enabling authorized admin users to
manage members, feature flags, quotas, and organizational settings through a unified interface.

Built using Next.js 15 App Router with server-side rendering and Supabase for authentication and database operations,
the module implements a secure, role-based administrative system with glass-morphism design aesthetics for a modern,
intuitive user experience.

## Specification

### Core Requirements

- **Admin-Only Access**: All pages require `is_admin = true` in user profile for access authorization
- **Organization-Specific Management**: All operations scoped to a single organization identified by dynamic route
  parameter `[id]`
- **Real-time Updates**: Optimistic UI updates with immediate database synchronization and error handling
- **Responsive Design**: Consistent layout and functionality across desktop, tablet, and mobile devices
- **Security**: Row Level Security (RLS) policies enforce data access restrictions at the database level

### Functional Specifications

1. **Organization Overview**: Display organization details, member count, quota summary, and feature flag status
2. **Member Management**: Add/remove organization members with profile integration and role management
3. **Feature Flag Control**: Configure organization-level feature flags with pattern validation and real-time updates
4. **Quota Administration**: Manage resource quotas for entities, documents, API calls, storage, users, reports, and
   exports
5. **Navigation Integration**: Breadcrumb navigation and seamless linking between related administrative functions

### Technical Specifications

- **Framework**: Next.js 15 App Router with TypeScript for type safety
- **Database**: Supabase PostgreSQL with Row Level Security policies
- **Authentication**: Supabase Auth with profile-based role management
- **State Management**: React hooks with optimistic updates and error handling
- **UI Components**: shadcn/ui design system with glass-morphism styling

## Key Components

### Primary Pages

- **`page.tsx`** - Organization overview dashboard with quick actions and summary information
- **`members/page.tsx`** - Member management interface for adding/removing organization users
- **`flags/page.tsx`** - Feature flag configuration with pattern validation and real-time editing
- **`quotas/page.tsx`** - Quota management for resource limits and usage controls

### Related Components

- **`/components/admin/AddMemberForm.tsx`** - Client-side form for adding users to organizations
- **`/components/admin/RemoveMemberButton.tsx`** - Secure member removal with confirmation dialogs
- **`/components/ui/glass-card.tsx`** - Glass-morphism styled card component for consistent UI

### Database Tables

- **`acc_organisations`** - Organization master data including name, email domain, entity references, and feature flags
- **`profiles`** - User profiles with organization membership via foreign key relationship
- **`acc_quota`** - Resource quotas with item types, quantities, and organization associations

## Dependencies

### External Services

- **Supabase**: Backend-as-a-Service for authentication, database operations, and Row Level Security
- **PostgreSQL**: Primary database for organizational data and user profiles

### Framework Dependencies

- **Next.js 15**: React framework with App Router for server-side rendering and dynamic routing
- **React 18**: Frontend library with hooks for state management and component lifecycle
- **TypeScript**: Type safety and enhanced developer experience

### UI Dependencies

- **shadcn/ui**: Design system components (Cards, Buttons, Inputs, Selects, Badges)
- **Lucide React**: Icon library for visual elements and user interface enhancement
- **Sonner**: Toast notification system for user feedback on operations

### Development Dependencies

- **ESLint**: Code linting and style enforcement
- **Prettier**: Code formatting for consistent style
- **PostCSS**: CSS processing for Tailwind CSS integration

## Usage Examples

### Accessing Organization Administration

```typescript
// Navigate to organization administration
// URL: /admin/organizations/123
// Automatically loads organization with ID 123

// Component renders with:
// - Organization details and metadata
// - Quick action buttons for member/quota/flag management
// - Summary cards with current statistics
```

### Managing Organization Members

```typescript
// Add member workflow
// 1. Navigate to /admin/organizations/123/members
// 2. Select user from dropdown of available users
// 3. Click "Add Member" to associate user with organization

// Remove member workflow
// 1. Click remove button next to member in list
// 2. Confirm removal in dialog
// 3. User's organization field set to null
```

### Configuring Feature Flags

```typescript
// Feature flag patterns supported:
// - "feature.name" - Standard feature flag
// - "feature.name.*" - Wildcard flag (all sub-features)
// - "!feature.name" - Negation flag (disabled)

// Example flags:
const flags = [
  "dashboard.analytics",
  "api.v2.*",
  "!legacy.features"
];
```

### Managing Resource Quotas

```typescript
// Quota types available:
const quotaTypes = [
  { value: 'entities', label: 'Entity Analysis' },
  { value: 'documents', label: 'Document Processing' },
  { value: 'api_calls', label: 'API Calls' },
  { value: 'storage', label: 'Storage (GB)' },
  { value: 'users', label: 'User Accounts' },
  { value: 'reports', label: 'Custom Reports' },
  { value: 'exports', label: 'Data Exports' }
];
```

## Architecture Notes

### Component Architecture

```mermaid
graph TD
    A[Organization Admin Root] --> B[Overview Dashboard]
    A --> C[Members Management]
    A --> D[Feature Flags]
    A --> E[Quota Management]
    
    B --> F[Organization Details Card]
    B --> G[Quick Actions Card]
    B --> H[Feature Flags Summary]
    B --> I[Quotas Summary]
    
    C --> J[Member List Card]
    C --> K[Add Member Form]
    C --> L[Remove Member Button]
    
    D --> M[Current Flags Card]
    D --> N[Add Flag Form]
    D --> O[Flag Patterns Guide]
    
    E --> P[Current Quotas Card]
    E --> Q[Add Quota Form]
    E --> R[Quota Types Reference]
```

### Database Schema Relationships

```mermaid
erDiagram
    acc_organisations ||--o{ profiles : "has members"
    acc_organisations ||--o{ acc_quota : "has quotas"
    
    acc_organisations {
        bigint id PK
        string name
        string email_domain
        string entity_xid
        string[] feature_flags
        timestamp created_at
    }
    
    profiles {
        uuid id PK
        bigint organisation FK
        string full_name
        string email
        string avatar_url
        boolean is_admin
    }
    
    acc_quota {
        bigint id PK
        bigint organisation FK
        quota_item_type item_type
        number quantity
        timestamp created_at
    }
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as Admin User
    participant C as Client Component
    participant S as Server Component
    participant DB as Supabase DB
    
    U->>S: Navigate to /admin/organizations/123
    S->>DB: Fetch organization data
    DB-->>S: Return organization details
    S-->>U: Render organization overview
    
    U->>C: Add member action
    C->>DB: Update profiles table
    DB-->>C: Confirm update
    C-->>U: Show success toast
    C->>C: Refresh page data
```

## Known Issues

### Critical Issues

1. **Role Selection Bug**: In `AddMemberForm.tsx:50`, role selection is collected but not persisted to database
    - **Impact**: New members cannot be assigned admin roles during addition
    - **Workaround**: Manually update user roles after addition via user management pages
    - **Fix Required**: Add role field to database update operation

### Minor Issues

1. **Loading States**: Some components lack loading indicators during async operations
2. **Error Handling**: Generic error messages could be more specific for better user guidance
3. **Validation**: Client-side validation could be enhanced with more comprehensive checks

### Technical Debt

1. **Code Duplication**: Similar form patterns across components could be extracted into reusable utilities
2. **Type Safety**: Some components use `any` types that could be properly typed
3. **Performance**: Large organization member lists could benefit from pagination or virtualization

## Future Work

### Planned Enhancements (Based on EKO-279)

1. **Virtual Entity Management**: Interface for managing allowed virtual entities per organization
2. **Message Center**: Admin interface for sending messages to organization members
3. **Enhanced Profile Management**: Direct editing of user profiles from organization context
4. **Audit Logging**: Track all administrative actions for compliance and security

### Technical Improvements

1. **Real-time Updates**: WebSocket integration for real-time collaboration on administrative changes
2. **Bulk Operations**: Support for bulk member addition/removal and quota management
3. **Advanced Search**: Search and filter capabilities for large organization member lists
4. **Data Export**: Export organization data for reporting and compliance purposes

### User Experience Enhancements

1. **Improved Navigation**: Enhanced breadcrumb navigation with context preservation
2. **Keyboard Shortcuts**: Keyboard navigation support for power users
3. **Mobile Optimization**: Enhanced mobile experience for on-the-go administration
4. **Accessibility**: WCAG 2.1 AA compliance for screen readers and assistive technologies

## Troubleshooting

### Common Issues

**Issue: Cannot access organization administration pages**

- **Cause**: User lacks admin privileges
- **Solution**: Verify `is_admin = true` in user profile table
- **Alternative**: Contact system administrator for role assignment

**Issue: Feature flags not saving**

- **Cause**: Invalid flag pattern or database permission error
- **Solution**: Verify flag follows pattern: `x.y.z`, `x.y.*`, or `!x.y.z`
- **Debug**: Check browser console for validation errors

**Issue: Members not appearing in organization**

- **Cause**: RLS policies or organization ID mismatch
- **Solution**: Verify user has correct organization assignment in profiles table
- **Debug**: Check Supabase logs for RLS policy violations

**Issue: Quota updates not persisting**

- **Cause**: Invalid quantity values or database constraints
- **Solution**: Ensure quantity is positive integer and quota type is valid
- **Debug**: Verify acc_quota table constraints and foreign key relationships

### Performance Issues

**Slow loading on large organizations**

- **Cause**: Large member lists or quota configurations
- **Solution**: Implement pagination or virtualization for member lists
- **Optimization**: Add database indexes on frequently queried fields

**Memory usage growing during navigation**

- **Cause**: React state not cleaning up properly
- **Solution**: Implement proper cleanup in useEffect hooks
- **Monitor**: Use React DevTools to track component re-renders

### Database Issues

**RLS policy violations**

- **Cause**: User attempting to access unauthorized organization data
- **Solution**: Review and update RLS policies for admin access patterns
- **Debug**: Enable RLS logging in Supabase dashboard

**Foreign key constraint violations**

- **Cause**: Attempting to create records with invalid organization references
- **Solution**: Validate organization exists before creating related records
- **Prevention**: Add client-side validation for organization ID parameters

## FAQ

### User-Centric Questions

**Q: How do I add a new member to an organization?**
A: Navigate to the organization's member management page, select a user from the dropdown, and click "Add Member". Only
users without existing organization assignments will appear in the dropdown.

**Q: What do the different feature flag patterns mean?**
A: Standard flags (`feature.name`) enable specific features, wildcard flags (`feature.name.*`) enable all sub-features,
and negation flags (`!feature.name`) explicitly disable features.

**Q: Can I set unlimited quotas for an organization?**
A: Yes, you can set high quota values, but consider platform resource limits and billing implications when setting
organization quotas.

**Q: How do I remove someone from an organization?**
A: Click the remove button (X) next to the member's name in the member list, then confirm the removal in the dialog.
This will disassociate the user from the organization.

**Q: Why can't I see certain organizations in the admin interface?**
A: Admin access is controlled by Row Level Security policies. You can only manage organizations you have administrative
access to, based on your user role and permissions.

**Q: What happens to a user's data when they're removed from an organization?**
A: The user's profile remains intact, but their organization association is removed. Their access to
organization-specific data and features will be revoked.

**Q: How do I know if a feature flag is working correctly?**
A: Feature flags are evaluated in real-time throughout the application. You can test flag functionality by toggling
flags and observing the corresponding feature availability in the customer interface.

**Q: Can I assign different roles to organization members?**
A: Currently, role assignment has a known issue (see Known Issues section). Member roles can be managed through the
individual user administration pages after adding them to the organization.

## References

### Documentation Links

- [Next.js 15 App Router Documentation](https://nextjs.org/docs/app/building-your-application/routing)
- [Supabase Authentication Guide](https://supabase.com/docs/guides/auth)
- [Supabase Row Level Security](https://supabase.com/docs/guides/database/postgres/row-level-security)
- [shadcn/ui Component Library](https://ui.shadcn.com/)

### Related Code Files

- `/apps/customer/app/admin/organizations/[id]/page.tsx` - Organization overview dashboard
- `/apps/customer/app/admin/organizations/[id]/members/page.tsx` - Member management interface
- `/apps/customer/app/admin/organizations/[id]/flags/page.tsx` - Feature flag configuration
- `/apps/customer/app/admin/organizations/[id]/quotas/page.tsx` - Quota management interface
- `/apps/customer/components/admin/AddMemberForm.tsx` - Member addition form component
- `/apps/customer/components/admin/RemoveMemberButton.tsx` - Member removal component
- `/apps/customer/app/supabase/server.ts` - Supabase server client configuration
- `/apps/customer/app/supabase/client.ts` - Supabase client configuration

### Database Schema References

- `acc_organisations` table schema and relationships
- `profiles` table structure and organization associations
- `acc_quota` table quota management configuration
- Supabase RLS policies for administrative access control

### External References

- [PostgreSQL Foreign Key Documentation](https://www.postgresql.org/docs/current/ddl-constraints.html#DDL-CONSTRAINTS-FK)
- [React Hooks Reference](https://react.dev/reference/react/hooks)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react)

### Linear Issues

- [EKO-279: Admin Pages](https://linear.app/ekointelligence/issue/EKO-279/admin-pages) - Primary implementation ticket
- [EKO-136: Feature Flags](https://linear.app/ekointelligence/issue/EKO-136/feature-flags) - Feature flag system
  implementation

---

## Changelog

### 2025-07-27

- **Initial Documentation**: Created comprehensive README.md for organization administration module
- **Architecture Documentation**: Added component architecture diagrams and database schema relationships
- **Troubleshooting Guide**: Documented common issues and resolution steps
- **Future Work Planning**: Outlined planned enhancements and technical improvements based on Linear ticket EKO-279

---

(c) All rights reserved ekoIntelligence 2025
