/**
 * Admin Organizations Management Dashboard for EkoIntelligence Platform
 * 
 * This Next.js 15 App Router Server Component provides a comprehensive administrative interface for 
 * managing organizations within the EkoIntelligence ESG (Environmental, Social, Governance) analysis 
 * platform. Designed exclusively for system administrators with elevated privileges, this dashboard 
 * enables viewing organization details, member counts, feature flags, and provides navigation to 
 * detailed organization management functions including member management, feature flag configuration, 
 * and quota administration.
 *
 * ## Core Functionality
 * - **Organization Overview**: Grid layout displaying all organizations with key metadata and statistics
 * - **Member Count Display**: Real-time member count calculations with user icon indicators
 * - **Feature Flag Summary**: Visual badges showing active feature flag counts for each organization
 * - **Navigation Hub**: Quick access buttons to organization-specific management pages (members, flags, details)
 * - **Entity XID Integration**: Display of entity external identifiers for analytics system integration
 * - **Glass-morphism UI**: Modern translucent design consistent with platform aesthetic standards
 *
 * ## Database Integration & Schema
 * **Supabase PostgreSQL Integration**: Leverages Supabase with Row Level Security (RLS) policies ensuring 
 * only administrators (profiles.is_admin = true) can access organization management functionality.
 * 
 * **Primary Table - acc_organisations**:
 * ```sql
 * CREATE TABLE acc_organisations (
 *   id bigint PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
 *   created_at timestamp with time zone DEFAULT now() NOT NULL,
 *   uuid uuid DEFAULT gen_random_uuid(),              -- Universal unique identifier
 *   name text,                                        -- Organization display name
 *   email_domain text,                               -- Email domain for auto-user assignment
 *   entity_xid text,                                 -- External entity ID for analytics integration
 *   feature_flags text[] DEFAULT '{}'::text[]       -- Array of enabled feature flags
 * );
 * ```
 * 
 * **Associated Table - profiles**:
 * ```sql
 * CREATE TABLE profiles (
 *   id uuid PRIMARY KEY REFERENCES auth.users(id),
 *   organisation bigint REFERENCES acc_organisations(id), -- FK to organization
 *   email text,
 *   first_name text,
 *   last_name text,
 *   is_admin boolean DEFAULT false,                       -- Admin privilege flag
 *   created_at timestamp with time zone DEFAULT now()
 * );
 * ```
 *
 * **Row Level Security Policy**: 
 * ```sql
 * CREATE POLICY "Enable select for auth users on own orgs" ON acc_organisations 
 *   FOR SELECT TO authenticated USING (
 *     id IN (SELECT profiles.organisation FROM profiles WHERE profiles.id = auth.uid())
 *   );
 * ```
 *
 * ## Organization Statistics & Metrics
 * **Member Count Calculation**: The component performs real-time aggregation of user profiles 
 * associated with each organization, providing accurate member counts without additional database 
 * queries through efficient data joins and client-side processing.
 * 
 * **Feature Flag Analysis**: Displays the count of active feature flags per organization, enabling 
 * administrators to quickly assess feature distribution and configuration complexity across 
 * different organizational units.
 *
 * ## Navigation & User Experience
 * **Three-tier Navigation Structure**:
 * 1. **Members Management**: Direct access to `/admin/organizations/[id]/members` for user administration
 * 2. **Feature Flags**: Link to `/admin/organizations/[id]/flags` for feature toggle configuration
 * 3. **Organization Details**: Access to `/admin/organizations/[id]` for comprehensive organization settings
 * 
 * **Visual Design Features**:
 * - Glass-morphism cards with hover animation effects for enhanced interactivity
 * - Color-coded badges using shadcn/ui components for consistent visual hierarchy
 * - Lucide React icons (Building, Users, ExternalLink) for intuitive interface navigation
 * - Responsive grid layout adapting to various screen sizes and device orientations
 *
 * ## System Architecture Integration
 * This component serves as the central hub in the broader EkoIntelligence admin system:
 * - **Analytics Backend**: Python system processes ESG data and populates entity_xid mappings
 * - **User Management**: Integrates with Supabase Auth for user authentication and profile management
 * - **Feature Flag System**: Connects to feature flag infrastructure for A/B testing and gradual rollouts
 * - **Quota Management**: Links to organization-specific quota administration for usage tracking
 * - **Audit System**: Organization changes are logged for compliance and security monitoring
 *
 * ## Security & Access Control
 * - **Admin-Only Access**: Requires `is_admin = true` in user profile for page access
 * - **RLS Integration**: Database-level security policies prevent unauthorized data access
 * - **Supabase SSR**: Server-side rendering ensures secure data fetching without client exposure
 * - **Organization Isolation**: Users can only view organizations they are associated with (via RLS)
 *
 * ## Performance Optimizations
 * - **Single Query Strategy**: Efficiently fetches organization data with member count joins
 * - **Client-side Aggregation**: Reduces database load by processing member counts in React
 * - **Optimistic Loading**: Uses Next.js 15 App Router for fast page transitions
 * - **Static Generation**: Utilizes ISR (Incremental Static Regeneration) where applicable
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages Next.js 15 App Router Pages
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see https://ui.shadcn.com/docs/components/card shadcn/ui Card Components
 * @see {@link ./[id]/page.tsx} Organization Details Page
 * @see {@link ./[id]/members/page.tsx} Organization Members Management
 * @see {@link ./[id]/flags/page.tsx} Organization Feature Flags
 * @see {@link ../../users/page.tsx} User Administration Dashboard
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This dashboard provides administrative access to organization management, member statistics, and feature flag configuration within the EkoIntelligence ESG analysis platform.
 * @example ```bash
# Access the organizations dashboard
curl -X GET 'http://localhost:3000/admin/organizations' \
  -H 'Authorization: Bearer <admin-user-token>'
```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createClient } from '@/app/supabase/server'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Building, Users, ExternalLink } from 'lucide-react'
import Link from 'next/link'

export default async function OrganizationsPage() {
  const supabase = await createClient()
  
  const { data: organizations } = await supabase
    .from('acc_organisations')
    .select(`
      *,
      profiles!profiles_organisation_fkey(count)
    `)
    .order('name')

  const { data: memberCounts } = await supabase
    .from('profiles')
    .select('organisation')
    .not('organisation', 'is', null)

  const memberCountMap = memberCounts?.reduce((acc, member) => {
    if (member.organisation) {
      acc[member.organisation] = (acc[member.organisation] || 0) + 1
    }
    return acc
  }, {} as Record<number, number>) || {}

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Organizations</h1>
          <p className="text-muted-foreground">
            Manage organizations and their settings
          </p>
        </div>
      </div>

      <div className="grid gap-6">
        {organizations?.map((org) => (
          <GlassCard key={org.id} className="transition-all duration-200 hover:shadow-lg">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Building className="h-6 w-6 text-primary" />
                  <div>
                    <CardTitle className="text-xl">{org.name}</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {org.email_domain}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">
                    <Users className="h-3 w-3 mr-1" />
                    {memberCountMap[org.id] || 0} members
                  </Badge>
                  <Badge variant="outline">
                    {org.feature_flags?.length || 0} flags
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Entity XID</p>
                  <p className="text-sm text-muted-foreground">
                    {org.entity_xid || 'Not set'}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/admin/organizations/${org.id}/members`}>
                      <Users className="h-4 w-4 mr-2" />
                      Members
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/admin/organizations/${org.id}/flags`}>
                      Flags
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/admin/organizations/${org.id}`}>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Details
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </GlassCard>
        ))}
      </div>
    </div>
  )
}