/**
 * Admin Messages Management Dashboard for EkoIntelligence Platform
 * 
 * This Next.js 15 App Router Server Component provides a comprehensive administrative interface for 
 * managing user messages within the EkoIntelligence ESG (Environmental, Social, Governance) analysis 
 * platform. Designed exclusively for system administrators with elevated privileges, this dashboard 
 * enables viewing message statistics, monitoring recent message activity, and providing quick access 
 * to message composition functionality for platform-wide user communication.
 *
 * ## Core Functionality
 * - **Message Overview**: Real-time statistics dashboard showing total messages, unread counts, and read message counts
 * - **Recent Message Activity**: Displays the 20 most recent messages with sender, recipient, and read status information
 * - **Message Composition**: Quick access buttons for composing messages to all users, specific domains, or custom recipients
 * - **Message Status Tracking**: Visual indicators for read/unread status with color-coded message states
 * - **User Context Integration**: Links message data with user profiles including names, emails, and avatar information
 * - **Glass-morphism UI**: Modern translucent design consistent with platform aesthetic standards
 *
 * ## Database Integration & Schema
 * **Supabase PostgreSQL Integration**: Leverages Supabase with Row Level Security (RLS) policies ensuring 
 * only administrators (profiles.is_admin = true) can access message management functionality.
 * 
 * **Primary Table - acc_messages**:
 * ```sql
 * CREATE TABLE acc_messages (
 *   id bigint PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
 *   created_at timestamp with time zone DEFAULT now() NOT NULL,
 *   type acc_message_type,                          -- Message type enum (general, alert, etc.)
 *   sender_name text,                               -- Name of message sender (admin or system)
 *   message text,                                   -- Message content body
 *   recipient uuid NOT NULL REFERENCES profiles(id), -- Foreign key to user profile
 *   read_at timestamp with time zone,               -- NULL = unread, timestamp = read
 *   deleted_at timestamp with time zone,            -- Soft delete timestamp
 *   title text                                      -- Message subject/title
 * );
 * ```
 * 
 * **Performance Optimizations**:
 * - `idx_acc_messages_active`: Composite index on (recipient, created_at) WHERE deleted_at IS NULL
 * - `idx_acc_messages_unread`: Index on (recipient, read_at) WHERE read_at IS NULL for fast unread queries
 * - `idx_acc_messages_recipient_created`: Index for efficient recipient-based message retrieval
 * - `idx_acc_messages_type`: Type-based message filtering optimization
 *
 * **Row Level Security**: 
 * ```sql
 * CREATE POLICY "Enable users to view their own data only" ON acc_messages 
 *   TO authenticated USING (auth.uid() = recipient) WITH CHECK (auth.uid() = recipient);
 * ```
 *
 * ## Message Statistics & Analytics
 * **Real-time Metrics**: The component calculates and displays live message statistics:
 * - **Total Messages**: Complete count of all messages in the system (excluding soft-deleted)
 * - **Unread Messages**: Messages where `read_at` IS NULL, highlighting pending user notifications
 * - **Read Messages**: Calculated difference showing successfully delivered messages
 * - **Message Activity**: Chronological display of recent message activity with user context
 *
 * ## User Profile Integration
 * **Profile Data Enrichment**: Messages are joined with the `profiles` table to provide:
 * ```sql
 * SELECT acc_messages.*, profiles(full_name, email, avatar_url)
 * FROM acc_messages
 * WHERE deleted_at IS NULL
 * ORDER BY created_at DESC
 * ```
 * This join provides human-readable recipient information including full names and email addresses
 * for improved administrative context and message tracking capabilities.
 *
 * ## Authentication & Authorization
 * **Admin-Only Access**: Protected by admin layout wrapper component ensuring only users with 
 * `is_admin = true` in their profile can access message management interfaces.
 * **Server-Side Authentication**: Uses Next.js 15 App Router server-side authentication with 
 * Supabase SSR client for secure database operations without exposing sensitive user data.
 * **Secure Message Queries**: All database queries respect RLS policies and admin privilege validation.
 *
 * ## Message Composition Integration
 * **Multi-Modal Messaging**: Provides quick access to different message composition modes:
 * - **Broadcast Messages**: Send messages to all platform users using wildcard recipient patterns
 * - **Domain-Based Messages**: Target all users with specific email domains (e.g., @company.com)
 * - **Custom Messages**: Individual user targeting with personalized message content
 * - **Message Templates**: Pre-configured message types for common administrative communications
 *
 * ## Glass-morphism Design System
 * **Visual Design Standards**: Implements the EkoIntelligence design system featuring:
 * - **Translucent Cards**: Glass-morphism effect cards with backdrop blur and subtle transparency
 * - **Rounded Corners**: Generous border radii (1.5rem standard) for approachable interface design
 * - **Hover Animations**: Subtle lift effects and shadow transitions for interactive feedback
 * - **Color-Coded Status**: Green indicators for read messages, red for unread, maintaining visual hierarchy
 * - **Responsive Layout**: Grid-based layout adapting from mobile to desktop viewing experiences
 *
 * ## Key Dependencies & Framework Integration
 * - **Next.js 15 App Router**: Modern React framework with server-side components and async data fetching
 * - **Supabase Client (@/app/supabase/server)**: PostgreSQL database client with built-in RLS and SSR support
 * - **Shadcn/ui Components**: Consistent UI component library providing accessible, themeable interface elements
 * - **Lucide React Icons**: Feather-inspired icon library for modern, accessible interface symbols
 * - **GlassCard Component**: Custom glass-morphism card implementation ensuring design system consistency
 *
 * ## Related Administrative Components
 * - **Message Composition** (`/admin/messages/compose`): Full-featured message editor with recipient selection
 * - **Organization Management** (`/admin/organizations`): User organization management affecting message targeting
 * - **User Management** (`/admin/users`): Individual user profile management with message history access
 * - **Admin Dashboard Layout**: Wrapping layout component enforcing admin authentication and navigation
 *
 * ## Performance Considerations
 * **Efficient Query Patterns**: 
 * - Uses `LIMIT 20` for recent messages to prevent excessive data loading
 * - Leverages database indexes for fast unread message counting
 * - Server-side rendering reduces client-side JavaScript and improves initial page load
 * **Optimized Data Fetching**: Single query with JOIN reduces database round-trips while providing enriched user context
 *
 * ## Future Enhancement Opportunities
 * - **Message Search**: Full-text search capabilities across message content and recipient information
 * - **Message Analytics**: Delivery rates, open rates, and user engagement metrics dashboard
 * - **Message Templates**: Pre-configured message templates for common administrative communications
 * - **Bulk Operations**: Multi-select message management with bulk read/delete operations
 * - **Message Scheduling**: Delayed message delivery for planned communications and announcements
 *
 * @see {@link /admin/messages/compose} Message composition interface for creating new messages
 * @see {@link /admin/organizations} Organization management affecting message recipient targeting  
 * @see {@link /admin/users} User management interface with individual message history access
 * @see {@link @/components/ui/glass-card} Glass-morphism card component for consistent visual design
 * <AUTHOR>
 * @updated 2025-07-23
 * @docgen doc-by-claude
 * @copyright © 2025 EkoIntelligence. All rights reserved.
 */

import { createClient } from '@/app/supabase/server'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MessageSquare, Send, Users, Mail } from 'lucide-react'
import Link from 'next/link'

export default async function MessagesPage() {
  const supabase = await createClient()
  
  const { data: recentMessages } = await supabase
    .from('acc_messages')
    .select(`
      *,
      profiles(full_name, email, avatar_url)
    `)
    .order('created_at', { ascending: false })
    .limit(20)

  const { data: messageStats } = await supabase
    .from('acc_messages')
    .select('id, read_at, type')

  const unreadCount = messageStats?.filter(msg => !msg.read_at).length || 0
  const totalCount = messageStats?.length || 0

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Messages</h1>
          <p className="text-muted-foreground">
            Send and manage messages to users
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            {unreadCount} unread
          </Badge>
          <Badge variant="secondary">
            {totalCount} total
          </Badge>
          <Button asChild>
            <Link href="/admin/messages/compose">
              <Send className="h-4 w-4 mr-2" />
              Compose Message
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <GlassCard>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="h-5 w-5 mr-2 text-primary" />
              Message Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium">Total Messages</p>
                <p className="text-2xl font-bold">{totalCount}</p>
              </div>
              <Mail className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div>
                <p className="text-sm font-medium">Unread Messages</p>
                <p className="text-2xl font-bold text-red-600">{unreadCount}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-red-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div>
                <p className="text-sm font-medium">Read Messages</p>
                <p className="text-2xl font-bold text-green-600">{totalCount - unreadCount}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </GlassCard>

        <div className="md:col-span-2">
          <GlassCard>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-2 text-primary" />
                Recent Messages
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentMessages && recentMessages.length > 0 ? (
                  recentMessages.map((message) => (
                    <div key={message.id} className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
                      <div className="flex-shrink-0">
                        {message.read_at ? (
                          <div className="w-3 h-3 bg-green-500 rounded-full" />
                        ) : (
                          <div className="w-3 h-3 bg-red-500 rounded-full" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium truncate">
                            {message.title || 'No title'}
                          </p>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              {message.type || 'general'}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {new Date(message.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          To: {message.profiles?.full_name || 'Unknown'} ({message.profiles?.email})
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          From: {message.sender_name || 'System'}
                        </p>
                        {message.message && (
                          <p className="text-sm text-gray-700 mt-2 line-clamp-2">
                            {message.message}
                          </p>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-8">
                    No messages sent yet.
                  </p>
                )}
              </div>
            </CardContent>
          </GlassCard>
        </div>
      </div>

      <GlassCard>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Send className="h-5 w-5 mr-2 text-primary" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Button variant="outline" className="justify-start h-auto p-4" asChild>
              <Link href="/admin/messages/compose">
                <div className="text-left">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    <span className="font-medium">Send to All Users</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Send a message to all users in the system
                  </p>
                </div>
              </Link>
            </Button>
            <Button variant="outline" className="justify-start h-auto p-4" asChild>
              <Link href="/admin/messages/compose">
                <div className="text-left">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="font-medium">Send to Domain</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Send to all users with a specific email domain
                  </p>
                </div>
              </Link>
            </Button>
            <Button variant="outline" className="justify-start h-auto p-4" asChild>
              <Link href="/admin/messages/compose">
                <div className="text-left">
                  <div className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    <span className="font-medium">Custom Message</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Send a custom message to selected users
                  </p>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </GlassCard>
    </div>
  )
}