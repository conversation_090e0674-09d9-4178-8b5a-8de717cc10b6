/**
 * Next.js Admin Message Composition Page - Advanced Multi-Recipient Messaging Interface
 *
 * This Admin interface provides a sophisticated email-like composition system for sending messages to
 * platform users within the EkoIntelligence ESG analysis platform. The page enables administrators
 * to send targeted communications with advanced recipient selection patterns, intelligent autocomplete,
 * and comprehensive message categorization for effective user engagement and platform management.
 *
 * ## Core Functionality
 * - **Multi-Recipient Messaging**: Advanced recipient system supporting individual users, domain groups, and broadcast patterns
 * - **Intelligent Autocomplete**: Real-time user search with debounced queries and keyboard navigation support
 * - **Recipient Pattern Matching**: Support for 'all' (broadcast), '@domain.com' (domain-based), and '<EMAIL>' (individual)
 * - **Message Type Categorization**: Six message types (info, success, billing, quota, feature, error) with clear purpose definitions
 * - **Form Validation**: Comprehensive client-side validation ensuring all required fields and recipients are properly configured
 * - **Email-Like Interface**: Familiar composition layout with To/Subject/Body structure for intuitive administrative usage
 * - **Glass-morphism UI**: Platform-consistent translucent design with glass-effect cards and rounded elements
 *
 * ## Recipient Selection System
 * The page implements a sophisticated recipient targeting system:
 * - **Individual Selection**: Search and select specific users by name or email with real-time autocomplete
 * - **Domain Broadcasting**: Use '@domain.com' to target all users with matching email domains
 * - **Universal Broadcasting**: Use 'all' keyword to send messages to every registered user
 * - **Pattern Resolution**: Server-side resolution of patterns to actual user IDs with deduplication
 * - **Visual Feedback**: Badge-based recipient display with individual removal capabilities
 *
 * ## Database Integration
 * Integrates with multiple database tables through the Supabase customer database:
 * - **acc_messages table**: Primary storage for messages with type, sender, recipient, and content fields
 * - **profiles table**: User lookup for autocomplete and recipient resolution with email/name matching
 * - **Field Mapping**: Maps form fields to database schema (title→title, message→message, senderName→sender_name)
 * - **RLS Security**: Row Level Security policies ensure users can only access their own messages
 * - **Batch Insertion**: Efficient bulk message creation for multiple recipients with transaction support
 *
 * ## Message Type System
 * Supports six distinct message categories with specific use cases:
 * - **info**: General information, updates, announcements, or platform news
 * - **success**: Positive notifications, achievements, confirmations, or completed actions
 * - **billing**: Payment notifications, subscription updates, invoice information, or billing issues
 * - **quota**: Usage limits, quota warnings, capacity notifications, or limit-related messages
 * - **feature**: Feature announcements, capability updates, new functionality, or enhancement notifications
 * - **error**: Error notifications, system issues, problem alerts, or failure communications
 *
 * ## User Experience Features
 * - **Keyboard Navigation**: Full arrow key navigation through autocomplete suggestions with Enter/Escape handling
 * - **Real-time Search**: Debounced user search (300ms) with minimum 2-character requirement for performance
 * - **Visual Feedback**: Loading states, recipient count display, and comprehensive error messaging
 * - **Accessibility**: Semantic HTML structure with proper ARIA labels and keyboard interaction support
 * - **Responsive Design**: Mobile-friendly layout with adaptive form elements and touch-optimized controls
 * - **Pattern Guidance**: Helper card explaining recipient pattern syntax with visual examples
 *
 * ## System Architecture
 * This page fits into the broader admin system architecture:
 * - **Admin Authentication**: Requires admin-level authentication through Supabase Auth with is_admin profile flag
 * - **Customer Database**: Operates on customer database for user management and message delivery
 * - **Message Delivery**: Messages are stored for user retrieval rather than real-time push notifications
 * - **Admin Interface**: Part of comprehensive admin panel with user management, quotas, and system configuration
 * - **Navigation Integration**: Connected to main admin messages list with breadcrumb navigation and back links
 *
 * ## Security & Performance
 * - **Admin Access Control**: Restricted to authenticated users with admin privileges through RLS policies
 * - **Input Validation**: Client-side validation with server-side verification for all form inputs
 * - **SQL Injection Protection**: Parameterized queries through Supabase client with type safety
 * - **Rate Limiting**: Debounced search queries to prevent excessive database calls during autocomplete
 * - **Efficient Queries**: Optimized user lookups with LIMIT clauses and indexed columns (email, full_name)
 * - **Error Handling**: Comprehensive error catching with user-friendly error messages and logging
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with client-side routing and state management
 * - **React Hooks**: useState, useEffect, useRef for component state and lifecycle management
 * - **Supabase**: Database client for user queries, message insertion, and authentication
 * - **Lucide React**: Icon library for UI elements (Send, Mail, Users, ArrowLeft, X icons)
 * - **Sonner**: Toast notification system for user feedback and error reporting
 * - **Tailwind CSS**: Utility-first styling with glass-morphism design system integration
 *
 * ## Related Components
 * - Admin Messages List Page (parent navigation)
 * - User Profile Management System
 * - Admin Dashboard with quota and user management
 * - Message viewing interface for end users
 * - Admin authentication and authorization system
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/route-handlers Next.js App Router
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://react.dev/reference/react/hooks React Hooks Reference
 * @see https://ui.shadcn.com/docs/components Shadcn/ui Component Library
 * @see {@link ../../page.tsx} Admin Messages List Page
 * @see {@link ../../../quotas/page.tsx} Admin Quotas Management
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This Admin interface provides a sophisticated email-like composition system for sending messages to platform users with advanced recipient selection patterns and intelligent autocomplete.
 * @example ```typescript
 * // Navigate to compose page
 * router.push('/admin/messages/compose');
 * 
 * // Send to all users
 * setRecipientsList(['all']);
 * 
 * // Send to domain
 * setRecipientsList(['@example.com']);
 * 
 * // Send to specific users
 * setRecipientsList(['<EMAIL>', '<EMAIL>']);
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'

import { useState, useEffect, useRef } from 'react'
import { createClient } from '@/app/supabase/client'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Send, Users, Mail, X } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

interface UserSuggestion {
  id: string
  email: string | null
  full_name: string | null
}

export default function ComposeMessagePage() {
  const [title, setTitle] = useState('')
  const [message, setMessage] = useState('')
  const [senderName, setSenderName] = useState('Admin')
  const [messageType, setMessageType] = useState('info')
  const [recipientInput, setRecipientInput] = useState('')
  const [loading, setLoading] = useState(false)
  const [recipientsList, setRecipientsList] = useState<string[]>([])
  const [userSuggestions, setUserSuggestions] = useState<UserSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1)
  
  const router = useRouter()
  const supabase = createClient()
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // Fetch user suggestions based on input
  useEffect(() => {
    const searchUsers = async () => {
      if (recipientInput.length < 2) {
        setUserSuggestions([])
        setShowSuggestions(false)
        return
      }

      // Don't search if it's a special pattern
      if (recipientInput === 'all' || recipientInput.startsWith('@')) {
        setUserSuggestions([])
        setShowSuggestions(false)
        return
      }

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('id, email, full_name')
          .not('email', 'is', null)
          .or(`email.ilike.%${recipientInput}%,full_name.ilike.%${recipientInput}%`)
          .limit(10)

        if (error) throw error
        
        setUserSuggestions(data || [])
        setShowSuggestions(true)
        setSelectedSuggestionIndex(-1)
      } catch (error) {
        console.error('Error fetching users:', error)
      }
    }

    const debounce = setTimeout(searchUsers, 300)
    return () => clearTimeout(debounce)
  }, [recipientInput, supabase])

  const handleAddRecipient = (recipient?: string) => {
    const recipientToAdd = recipient || recipientInput.trim()
    if (recipientToAdd && !recipientsList.includes(recipientToAdd)) {
      setRecipientsList([...recipientsList, recipientToAdd])
      setRecipientInput('')
      setShowSuggestions(false)
      setSelectedSuggestionIndex(-1)
    }
  }

  const handleRemoveRecipient = (index: number) => {
    setRecipientsList(recipientsList.filter((_, i) => i !== index))
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedSuggestionIndex(prev => 
          prev < userSuggestions.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedSuggestionIndex >= 0) {
          const selectedUser = userSuggestions[selectedSuggestionIndex]
          if (selectedUser.email) {
            handleAddRecipient(selectedUser.email)
          }
        } else {
          handleAddRecipient()
        }
        break
      case 'Escape':
        e.preventDefault()
        setShowSuggestions(false)
        setSelectedSuggestionIndex(-1)
        break
    }
  }

  const handleSuggestionClick = (user: UserSuggestion) => {
    if (user.email) {
      handleAddRecipient(user.email)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!title || !message) {
      toast.error('Please fill in all required fields')
      return
    }

    if (recipientsList.length === 0) {
      toast.error('Please add at least one recipient')
      return
    }

    setLoading(true)
    
    try {
      // Get all users based on recipient patterns
      const resolvedRecipients = new Set<string>()
      
      for (const pattern of recipientsList) {
        if (pattern === 'all') {
          // Send to all users
          const { data: allUsers } = await supabase
            .from('profiles')
            .select('id')
          
          allUsers?.forEach(user => resolvedRecipients.add(user.id))
        } else if (pattern.startsWith('@')) {
          // Send to all users with domain
          const domain = pattern.slice(1)
          const { data: domainUsers } = await supabase
            .from('profiles')
            .select('id')
            .ilike('email', `%${domain}`)
          
          domainUsers?.forEach(user => resolvedRecipients.add(user.id))
        } else if (pattern.includes('@')) {
          // Send to specific email
          const { data: specificUser } = await supabase
            .from('profiles')
            .select('id')
            .eq('email', pattern)
            .single()
          
          if (specificUser) {
            resolvedRecipients.add(specificUser.id)
          }
        }
      }

      if (resolvedRecipients.size === 0) {
        toast.error('No valid recipients found')
        return
      }

      // Send message to all resolved recipients
      const messages = Array.from(resolvedRecipients).map(recipientId => ({
        title,
        message,
        sender_name: senderName,
        type: messageType as any, // Type assertion for enum
        recipient: recipientId
      }))

      const { error } = await supabase
        .from('acc_messages')
        .insert(messages)

      if (error) {
        throw error
      }

      toast.success(`Message sent to ${resolvedRecipients.size} recipients`)
      router.push('/admin/messages')
    } catch (error) {
      console.error('Error sending message:', error)
      toast.error('Failed to send message')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/messages">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Messages
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <Send className="h-8 w-8 mr-3 text-primary" />
          Compose Message
        </h1>
        <p className="text-muted-foreground">
          Send a message to users or organizations
        </p>
      </div>

      <div className="max-w-4xl">
        <GlassCard>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="h-5 w-5 mr-2" />
              Compose Message
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email-like layout: Recipients first */}
              <div className="space-y-2">
                <Label htmlFor="recipients">To</Label>
                <div className="relative">
                  <div className="min-h-[40px] p-2 border rounded-md flex flex-wrap gap-2 items-center">
                    {recipientsList.map((recipient, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {recipient}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 w-4 h-4"
                          onClick={() => handleRemoveRecipient(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                    <Input
                      ref={inputRef}
                      id="recipients"
                      value={recipientInput}
                      onChange={(e) => setRecipientInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder={recipientsList.length === 0 ? "Type name, email, or 'all'" : ""}
                      className="border-0 shadow-none focus-visible:ring-0 flex-1 min-w-[200px]"
                      onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                      onFocus={() => recipientInput.length >= 2 && setShowSuggestions(true)}
                    />
                  </div>
                  
                  {/* Autocomplete suggestions */}
                  {showSuggestions && userSuggestions.length > 0 && (
                    <div 
                      ref={suggestionsRef}
                      className="absolute top-full left-0 right-0 z-50 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto"
                    >
                      {userSuggestions.map((user, index) => (
                        <div
                          key={user.id}
                          className={`p-3 cursor-pointer hover:bg-gray-50 border-b last:border-b-0 ${
                            index === selectedSuggestionIndex ? 'bg-blue-50' : ''
                          }`}
                          onClick={() => handleSuggestionClick(user)}
                        >
                          <div className="font-medium">{user.full_name || user.email || 'Unknown User'}</div>
                          {user.full_name && user.email && (
                            <div className="text-sm text-gray-500">{user.email}</div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <div className="text-xs text-muted-foreground">
                  Use 'all' for all users, @domain.com for domain users, or search by name/email
                </div>
              </div>

              {/* Subject line */}
              <div className="space-y-2">
                <Label htmlFor="title">Subject *</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter subject line"
                  required
                />
              </div>

              {/* Sender and Message Type in a row */}
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="senderName">From</Label>
                  <Input
                    id="senderName"
                    value={senderName}
                    onChange={(e) => setSenderName(e.target.value)}
                    placeholder="Admin"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="messageType">Message Type</Label>
                  <Select value={messageType} onValueChange={setMessageType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="billing">Billing</SelectItem>
                      <SelectItem value="quota">Quota</SelectItem>
                      <SelectItem value="feature">Feature</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              {/* Message body */}
              <div className="space-y-2">
                <Label htmlFor="message">Message *</Label>
                <Textarea
                  id="message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Enter your message here..."
                  rows={12}
                  required
                />
              </div>
              
              {/* Send button */}
              <div className="flex justify-between items-center pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  {recipientsList.length > 0 && `Will send to ${recipientsList.length} recipient(s)`}
                </div>
                <Button type="submit" disabled={loading}>
                  <Send className="h-4 w-4 mr-2" />
                  {loading ? 'Sending...' : 'Send Message'}
                </Button>
              </div>
            </form>
          </CardContent>
        </GlassCard>

        {/* Help card */}
        <GlassCard className="mt-6">
          <CardHeader>
            <CardTitle>Recipient Patterns</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">all</Badge>
                <span className="text-sm">All users</span>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">@example.com</Badge>
                <span className="text-sm">All users with domain</span>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary"><EMAIL></Badge>
                <span className="text-sm">Specific user</span>
              </div>
            </div>
          </CardContent>
        </GlassCard>
      </div>
    </div>
  )
}