/**
 * Next.js App Router Admin Feature Flags Management Page Component
 *
 * This React Server Component provides comprehensive feature flag management capabilities within the 
 * EkoIntelligence ESG analysis platform's administrative interface. It enables system administrators 
 * to view, monitor, and navigate to detailed management interfaces for feature flags across both 
 * organizations and individual users, implementing a centralized dashboard for feature flag governance.
 *
 * ## Core Functionality
 * - **Centralized Flag Overview**: Displays all active feature flags across the entire system with count metrics
 * - **Organization Flag Management**: Lists all organizations with their assigned feature flags and flag counts
 * - **Individual User Flags**: Shows users with custom feature flags, including organizational context
 * - **Navigation Hubs**: Provides direct links to detailed flag management interfaces for organizations and users
 * - **Flag Analytics**: Aggregates and displays total flag usage statistics across the platform
 * - **Glass-morphism UI**: Implements the platform's signature translucent design system with hover effects
 *
 * ## Data Sources & Security
 * **Database Tables**:
 * - `acc_organisations`: Organization-level feature flags stored as `text[]` array in `feature_flags` column
 * - `profiles`: User-level feature flags stored as `text[]` array with `is_admin` access control
 * 
 * **Security Model**:
 * - Requires authenticated admin access (enforced by admin layout wrapper)
 * - Uses Supabase RLS policies to restrict access to authorized organization data
 * - Implements proper data filtering to show only users with actual feature flags assigned
 *
 * ## Feature Flag Architecture
 * Feature flags in the EkoIntelligence platform support multiple formats:
 * - **Standard Flags**: Simple boolean feature toggles (e.g., `new-dashboard`, `advanced-analytics`)
 * - **Negation Flags**: Inverse feature controls using `!x.y.z` syntax to disable specific features
 * - **Wildcard Flags**: Pattern-based flags using `x.y.*` syntax for feature group management
 * - **Hierarchical Flags**: Nested feature organization following dot notation conventions
 *
 * ## Key Dependencies
 * - **Next.js 15 App Router**: Modern React framework with server-side components and async data fetching
 * - **Supabase Client**: PostgreSQL database client with Row Level Security for secure data access
 * - **Shadcn/ui Components**: Consistent UI component library with glass-morphism theming
 * - **Lucide React Icons**: Feather-inspired icon library for modern, accessible interface elements
 * - **GlassCard Component**: Custom glass-morphism card implementation for platform design consistency
 *
 * ## Related Components & System Integration
 * - **Organization Flag Editor** (`/admin/organizations/[id]/flags`): Detailed flag management for specific organizations
 * - **User Flag Editor** (`/admin/users/[id]/flags`): Individual user flag assignment and management interface
 * - **Admin Dashboard Layout**: Wrapping layout component that enforces admin authentication and navigation
 * - **Feature Flag Evaluation System**: Client and server-side flag evaluation logic throughout the application
 *
 * ## System Architecture
 * This component fits into the broader EkoIntelligence administrative system:
 * - **Admin Interface Layer**: Part of comprehensive admin tooling for platform management
 * - **Feature Flag System**: Central control point for platform-wide feature toggle management
 * - **Customer Database**: Direct integration with Supabase customer database for real-time flag data
 * - **Authentication Layer**: Integrates with Supabase Auth for admin role verification and access control
 * - **UI Design System**: Implements glass-morphism design patterns consistent across admin interfaces
 *
 * ## Database Schema Integration
 * **acc_organisations Table**:
 * - `feature_flags` (text[]): Array of feature flag strings with default empty array
 * - Supports hierarchical organization structure with email domain associations
 * - Includes RLS policies for organization-specific data access control
 *
 * **profiles Table**:
 * - `feature_flags` (text[]): User-specific feature flag overrides with default empty array
 * - `is_admin` (boolean): Administrative privilege flag for access control
 * - `organisation` (bigint): Foreign key relationship to acc_organisations for context
 * - RLS policies prevent users from modifying their own admin status
 *
 * ## Performance & Security Considerations
 * - **Efficient Queries**: Optimized database queries with specific column selection to minimize data transfer
 * - **Server-Side Rendering**: All data fetching occurs server-side for improved performance and SEO
 * - **Flag Deduplication**: Client-side Set-based deduplication ensures unique flag display across sources
 * - **Lazy Navigation**: Uses Next.js Link component for optimized client-side routing to detail pages
 * - **Access Control**: Enforces admin-only access through layout-level authentication checks
 *
 * ## User Experience Features
 * - **Visual Flag Indicators**: Color-coded badges showing flag counts and status for quick scanning
 * - **Contextual Information**: Shows organizational context for user flags to aid in management decisions
 * - **Quick Navigation**: External link icons and hover states provide clear interaction affordances
 * - **Responsive Design**: Glass-morphism cards adapt to different screen sizes with consistent spacing
 * - **Empty State Handling**: Graceful display when no flags are configured in the system
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts Next.js App Router Pages
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase Row Level Security
 * @see https://ui.shadcn.com/docs/components/badge Shadcn/ui Badge Component
 * @see https://lucide.dev/guide/packages/lucide-react Lucide React Icons
 * @see {@link ../../organizations/[id]/flags/page.tsx} Organization Flag Management
 * @see {@link ../../users/[id]/flags/page.tsx} User Flag Management
 * <AUTHOR>
 * @updated 2025-07-23
 * @description This Next.js App Router admin page provides centralized feature flag management with organization and user flag overview, navigation to detailed management interfaces, and comprehensive flag analytics.
 * @example ```bash
  # Admin access to feature flags dashboard
  curl -H "Authorization: Bearer admin_token" 'https://app.ekointelligence.com/admin/flags'
  
  # Shows all organizations with flags like: ['new-dashboard', 'advanced-analytics', '!legacy-reports']
  # Shows users with individual flags like: ['beta-features', 'premium-access']
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createClient } from '@/app/supabase/server'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Settings, Building, Users, ExternalLink } from 'lucide-react'
import Link from 'next/link'

export default async function FeatureFlagsPage() {
  const supabase = await createClient()
  
  const { data: organizations } = await supabase
    .from('acc_organisations')
    .select('id, name, feature_flags')
    .order('name')

  const { data: users } = await supabase
    .from('profiles')
    .select(`
      id, full_name, email, feature_flags,
      acc_organisations!profiles_organisation_fkey(name)
    `)
    .not('feature_flags', 'is', null)
    .order('full_name')

  // Get all unique feature flags
  const allFlags = new Set<string>()
  organizations?.forEach(org => {
    org.feature_flags?.forEach(flag => allFlags.add(flag))
  })
  users?.forEach(user => {
    user.feature_flags?.forEach(flag => allFlags.add(flag))
  })

  const flagsArray = Array.from(allFlags).sort()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Feature Flags</h1>
          <p className="text-muted-foreground">
            Manage feature flags for organizations and users
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            {flagsArray.length} total flags
          </Badge>
        </div>
      </div>

      <div className="grid gap-6">
        <GlassCard>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2 text-primary" />
              All Feature Flags
            </CardTitle>
          </CardHeader>
          <CardContent>
            {flagsArray.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {flagsArray.map((flag) => (
                  <Badge key={flag} variant="secondary" className="text-sm">
                    {flag}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No feature flags configured in the system.
              </p>
            )}
          </CardContent>
        </GlassCard>

        <GlassCard>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2 text-primary" />
              Organization Feature Flags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {organizations?.map((org) => (
                <div key={org.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Building className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{org.name}</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {org.feature_flags && org.feature_flags.length > 0 ? (
                          org.feature_flags.map((flag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {flag}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-xs text-muted-foreground">No flags</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">
                      {org.feature_flags?.length || 0} flags
                    </Badge>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/admin/organizations/${org.id}/flags`}>
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </GlassCard>

        <GlassCard>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-primary" />
              User Feature Flags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {users?.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Users className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{user.full_name}</p>
                      <p className="text-xs text-muted-foreground">
                        {user.email} • {user.acc_organisations?.name || 'No organization'}
                      </p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {user.feature_flags && user.feature_flags.length > 0 ? (
                          user.feature_flags.map((flag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {flag}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-xs text-muted-foreground">No flags</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">
                      {user.feature_flags?.length || 0} flags
                    </Badge>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/admin/users/${user.id}/flags`}>
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </GlassCard>
      </div>
    </div>
  )
}