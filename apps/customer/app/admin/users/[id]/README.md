# Individual User Administration Module

## Overview

The Individual User Administration Module (`/apps/customer/app/admin/users/[id]`) provides comprehensive administrative
capabilities for managing individual user accounts within the EkoIntelligence ESG analysis platform. This module serves
as the central hub for user-specific administrative functions, enabling authorized admin users to view detailed user
profiles, manage personal feature flags, configure individual quotas, and oversee user-specific settings through a
unified, intuitive interface.

Built using Next.js 15 App Router with server-side rendering and Supabase for authentication and database operations,
the module implements a secure, role-based administrative system with glass-morphism design aesthetics for a modern,
professional user experience.

## Specification

### Core Requirements

- **Admin-Only Access**: All pages require `is_admin = true` in user profile for access authorization
- **User-Specific Management**: All operations scoped to a single user identified by dynamic route parameter `[id]` (
  UUID)
- **Real-time Updates**: Optimistic UI updates with immediate database synchronization and comprehensive error handling
- **Responsive Design**: Consistent layout and functionality across desktop, tablet, and mobile devices
- **Security**: Row Level Security (RLS) policies enforce data access restrictions at the database level

### Functional Specifications

1. **User Profile Overview**: Display comprehensive user details, organization membership, admin status, and account
   metadata
2. **Personal Feature Flag Management**: Configure user-specific feature flags with pattern validation, inheritance
   display, and real-time updates
3. **Individual Quota Administration**: Manage personal resource quotas independent of organizational quotas
4. **Navigation Integration**: Breadcrumb navigation and seamless linking between related administrative functions
5. **Organization Context**: Display and manage user's organization membership with direct navigation to org management

### Technical Specifications

- **Framework**: Next.js 15 App Router with TypeScript for type safety and async params handling
- **Database**: Supabase PostgreSQL with Row Level Security policies and foreign key relationships
- **Authentication**: Supabase Auth with profile-based role management and session handling
- **State Management**: React hooks with optimistic updates, error recovery, and comprehensive loading states
- **UI Components**: shadcn/ui design system with glass-morphism styling and responsive grid layouts

## Key Components

### Primary Pages

- **`page.tsx`** - User overview dashboard with profile information, organization details, feature flags summary, and
  personal quotas display
- **`flags/page.tsx`** - Personal feature flag management interface with pattern validation, inheritance display, and
  real-time editing
- **`quotas/page.tsx`** - Individual quota management for personal resource limits independent of organizational quotas

### Related Components

- **Avatar Integration**: Profile image display with automatic fallback to user initials
- **Glass-morphism Cards**: Translucent, rounded card components for organized information display
- **Badge System**: Color-coded visual indicators for admin status, feature flags, and quota information
- **Toast Notifications**: Real-time feedback system for user actions and error states

### Database Tables

- **`profiles`** - Primary user data including personal details, organization membership, admin status, and feature
  flags
- **`acc_organisations`** - Organization master data linked via foreign key for membership context
- **`acc_quota`** - Personal resource quotas with customer-specific item types and quantity limits

## Dependencies

### External Services

- **Supabase**: Backend-as-a-Service for authentication, database operations, and Row Level Security
- **PostgreSQL**: Primary database for user profiles, organization data, and quota management

### Framework Dependencies

- **Next.js 15**: React framework with App Router for server-side rendering and dynamic routing
- **React 18**: Frontend library with hooks for state management and component lifecycle
- **TypeScript**: Type safety and enhanced developer experience with interface definitions

### UI Dependencies

- **shadcn/ui**: Design system components (Cards, Buttons, Inputs, Selects, Badges, Avatars)
- **Lucide React**: Icon library for visual elements and user interface enhancement
- **Sonner**: Toast notification system for user feedback on operations and error states

### Development Dependencies

- **ESLint**: Code linting and style enforcement
- **Prettier**: Code formatting for consistent style
- **PostCSS**: CSS processing for Tailwind CSS integration

## Usage Examples

### Accessing User Administration

```typescript
// Navigate to user administration
// URL: /admin/users/550e8400-e29b-41d4-a716-************
// Automatically loads user with UUID

// Component renders with:
// - Complete user profile information
// - Organization membership details
// - Personal feature flags and inherited flags
// - Individual quota configurations
```

### Managing Personal Feature Flags

```typescript
// Feature flag patterns supported:
// - "feature.name" - Standard feature flag
// - "feature.name.*" - Wildcard flag (all sub-features)
// - "!feature.name" - Negation flag (disabled)

// Example personal flags:
const personalFlags = [
  "dashboard.advanced",
  "api.premium.access",
  "!legacy.features"
];

// Adding a new flag programmatically
const newFlag = 'reports.custom.export'
setUser(prev => prev ? {
  ...prev, 
  feature_flags: [...(prev.feature_flags || []), newFlag]
} : null)
```

### Managing Personal Resource Quotas

```typescript
// Personal quota types available:
const quotaTypes = [
  { value: 'entities', label: 'Entity Analysis' },
  { value: 'documents', label: 'Document Processing' },
  { value: 'api_calls', label: 'API Calls' },
  { value: 'storage', label: 'Storage (GB)' },
  { value: 'reports', label: 'Custom Reports' },
  { value: 'exports', label: 'Data Exports' }
];

// Add a new quota programmatically
const newQuota = {
  customer: userId,
  item_type: 'entities',
  quantity: 100,
  organisation: 0 // Personal quota marker
}
```

## Architecture Notes

### Component Architecture

```mermaid
graph TD
    A[User Admin Root] --> B[User Overview Dashboard]
    A --> C[Feature Flags Management]
    A --> D[Quota Management]
    
    B --> E[Profile Information Card]
    B --> F[Organization & Access Card]
    B --> G[Feature Flags Summary]
    B --> H[Personal Quotas Summary]
    B --> I[Management Action Buttons]
    
    C --> J[Personal Flags Card]
    C --> K[Organization Inherited Flags]
    C --> L[User Information Sidebar]
    C --> M[Add Flag Form]
    C --> N[Flag Patterns Guide]
    
    D --> O[Personal Quotas Card]
    D --> P[User Information Sidebar]
    D --> Q[Add Quota Form]
    D --> R[Quota Types Reference]
```

### Database Schema Relationships

```mermaid
erDiagram
    profiles ||--o| acc_organisations : "member of"
    profiles ||--o{ acc_quota : "has personal quotas"
    
    profiles {
        uuid id PK
        bigint organisation FK
        string full_name
        string email
        string avatar_url
        string username
        string website
        boolean is_admin
        string[] feature_flags
        timestamp updated_at
        string welcome_message
    }
    
    acc_organisations {
        bigint id PK
        string name
        string email_domain
        string entity_xid
        string[] feature_flags
        timestamp created_at
    }
    
    acc_quota {
        bigint id PK
        uuid customer FK
        bigint organisation
        quota_item_type item_type
        number quantity
        timestamp created_at
    }
```

### Feature Flag Inheritance Flow

```mermaid
sequenceDiagram
    participant U as Admin User
    participant C as Client Component
    participant S as Server Component
    participant DB as Supabase DB
    
    U->>S: Navigate to /admin/users/[id]/flags
    S->>DB: Fetch user profile with org flags
    DB-->>S: Return user + inherited org flags
    S-->>C: Render flags with inheritance
    
    U->>C: Add personal flag
    C->>C: Validate flag pattern
    C->>C: Update local state (optimistic)
    C->>DB: Update user feature_flags array
    DB-->>C: Confirm update
    C-->>U: Show success toast
    
    Note over C,DB: Error handling reverts optimistic update
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as Admin User
    participant SC as Server Component
    participant CC as Client Component
    participant DB as Supabase DB
    
    U->>SC: Navigate to /admin/users/[id]
    SC->>DB: Fetch user profile + organization
    DB-->>SC: Return complete user data
    SC-->>U: Render user overview
    
    U->>CC: Navigate to flags/quotas
    CC->>DB: Fetch user-specific data
    DB-->>CC: Return current configuration
    CC-->>U: Render management interface
    
    U->>CC: Modify flags/quotas
    CC->>CC: Optimistic UI update
    CC->>DB: Persist changes
    DB-->>CC: Confirm operation
    CC-->>U: Show feedback toast
```

## Known Issues

### Critical Issues

1. **Async Params Handling**: `quotas/page.tsx:475-483` uses client-side params resolution for Next.js 15 compatibility
    - **Impact**: Additional loading state required for parameter extraction
    - **Workaround**: useEffect hook resolves params asynchronously
    - **Future Fix**: Align with server component pattern when Next.js improves async params support

### Minor Issues

1. **Loading States**: Some transitions between admin sections could benefit from skeleton loading states
2. **Error Boundaries**: Generic error handling could be enhanced with component-specific error boundaries
3. **Validation Feedback**: Real-time validation feedback could be more granular for complex feature flag patterns

### Technical Debt

1. **Code Duplication**: Similar form patterns across flags/quotas components could be extracted into reusable utilities
2. **Type Safety**: Some database query results use type assertions that could be properly typed
3. **Performance**: Large feature flag lists could benefit from virtualization for users with extensive flag
   configurations

## Future Work

### Planned Enhancements (Based on EKO-279)

1. **Profile Editor**: Direct editing of user profile information including is_admin status modification
2. **Virtual Entity Management**: Interface for managing allowed virtual entities for individual users
3. **Message Center Integration**: Direct messaging interface for sending acc_messages to specific users
4. **Advanced User Search**: Enhanced search and filtering capabilities across user administration

### Technical Improvements

1. **Real-time Synchronization**: WebSocket integration for real-time updates when multiple admins manage the same user
2. **Audit Logging**: Comprehensive audit trail for all user administration actions and changes
3. **Bulk Operations**: Support for bulk flag/quota operations across multiple users
4. **Data Export**: Export user configuration data for backup and compliance purposes

### User Experience Enhancements

1. **Enhanced Navigation**: Context-preserving navigation between user and organization administration
2. **Keyboard Navigation**: Full keyboard accessibility for power users
3. **Mobile Optimization**: Enhanced mobile interface for on-the-go user administration
4. **Advanced Filtering**: Filter and sort capabilities for users with complex flag/quota configurations

## Troubleshooting

### Common Issues

**Issue: Cannot access user administration pages**

- **Cause**: Insufficient admin privileges
- **Solution**: Verify `is_admin = true` in your user profile
- **Debug**: Check Supabase RLS policies and authentication status

**Issue: Feature flags not saving**

- **Cause**: Invalid flag pattern or database permission error
- **Solution**: Verify flag follows pattern: `x.y.z`, `x.y.*`, or `!x.y.z`
- **Debug**: Check browser console for validation errors and toast notifications

**Issue: Quotas not updating**

- **Cause**: Invalid quantity values or constraint violations
- **Solution**: Ensure quantity is positive integer and quota type is valid
- **Debug**: Verify acc_quota table constraints and customer field references

**Issue: User not found (404 error)**

- **Cause**: Invalid user UUID or user does not exist
- **Solution**: Verify UUID format and user existence in profiles table
- **Debug**: Check URL parameter extraction and database query results

### Performance Issues

**Slow loading on user with many flags/quotas**

- **Cause**: Large arrays or complex database queries
- **Solution**: Consider pagination for extensive flag lists
- **Optimization**: Add database indexes on frequently queried user fields

**Memory issues during navigation**

- **Cause**: React state not properly cleaned up
- **Solution**: Implement proper cleanup in useEffect hooks
- **Monitor**: Use React DevTools to track component re-renders and memory usage

### Database Issues

**RLS policy violations**

- **Cause**: User attempting to access unauthorized user data
- **Solution**: Review and update RLS policies for admin access patterns
- **Debug**: Enable RLS logging in Supabase dashboard for detailed policy evaluation

**Foreign key constraint violations**

- **Cause**: Attempting to create quotas with invalid user references
- **Solution**: Validate user UUID exists before creating related records
- **Prevention**: Add client-side validation for user ID parameters

## FAQ

### User-Centric Questions

**Q: How do I add feature flags to a specific user?**
A: Navigate to the user's flags management page, enter a valid flag pattern in the input field, and click the add
button. Flags are validated in real-time and must follow supported patterns.

**Q: What's the difference between personal and organization feature flags?**
A: Personal flags are specific to the individual user and can override organization defaults. Organization flags are
inherited by all members but can be overridden by personal flags.

**Q: How do personal quotas relate to organization quotas?**
A: Personal quotas are independent limits specific to the individual user. They operate separately from
organization-wide quotas and provide granular control over individual resource usage.

**Q: Can I remove a user's admin privileges from this interface?**
A: Currently, admin status modification is not available in this interface. This functionality is planned for future
releases as part of the comprehensive profile editor.

**Q: Why can't I see some users in the admin interface?**
A: Admin access is controlled by Row Level Security policies. You can only manage users you have administrative access
to, based on your role and permissions.

**Q: What happens when I delete a user's feature flag?**
A: Removing a personal flag immediately reverts the user to organization-level defaults for that feature. The change
takes effect in real-time across the platform.

**Q: How do I know if a user's quota is being enforced?**
A: Personal quotas are enforced at the API and backend level. You can monitor quota usage through the analytics system
and usage tracking interfaces.

**Q: Can I copy feature flags from one user to another?**
A: Currently, flag copying is not available in the interface. You can manually replicate flag configurations or use the
planned bulk operations feature when available.

## References

### Documentation Links

- [Next.js 15 App Router Documentation](https://nextjs.org/docs/app/building-your-application/routing)
- [Next.js Dynamic Routes](https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes)
- [Supabase Authentication Guide](https://supabase.com/docs/guides/auth)
- [Supabase Row Level Security](https://supabase.com/docs/guides/database/postgres/row-level-security)
- [shadcn/ui Component Library](https://ui.shadcn.com/)
- [React Hooks Reference](https://react.dev/reference/react/hooks)

### Related Code Files

- `/apps/customer/app/admin/users/[id]/page.tsx` - User overview dashboard
- `/apps/customer/app/admin/users/[id]/flags/page.tsx` - Personal feature flag management
- `/apps/customer/app/admin/users/[id]/quotas/page.tsx` - Individual quota management
- `/apps/customer/app/admin/users/page.tsx` - User list and navigation entry point
- `/apps/customer/app/admin/organizations/[id]/page.tsx` - Related organization management
- `/apps/customer/app/supabase/server.ts` - Supabase server client configuration
- `/apps/customer/app/supabase/client.ts` - Supabase client configuration
- `/apps/customer/components/ui/glass-card.tsx` - Glass-morphism card component

### Database Schema References

- `profiles` table - Primary user data and feature flags storage
- `acc_organisations` table - Organization membership and inherited flags
- `acc_quota` table - Personal and organizational quota management
- Supabase RLS policies - Administrative access control and data security

### External References

- [PostgreSQL UUID Data Type](https://www.postgresql.org/docs/current/datatype-uuid.html)
- [PostgreSQL Arrays](https://www.postgresql.org/docs/current/arrays.html)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react)
- [Sonner Toast Library](https://sonner.emilkowal.ski/)

### Linear Issues

- [EKO-279: Admin Pages](https://linear.app/ekointelligence/issue/EKO-279/admin-pages) - Primary implementation ticket
  for complete admin system
- Related admin functionality tickets referenced in parent admin system

### README Files

- `/apps/customer/app/admin/organizations/[id]/README.md` - Organization administration module documentation
- `/apps/customer/tests/CLAUDE.md` - Testing guidelines for customer app components

---

## Changelog

### 2025-07-27

- **Initial Documentation**: Created comprehensive README.md for individual user administration module
- **Architecture Documentation**: Added component architecture diagrams, database relationships, and data flow sequences
- **Feature Analysis**: Documented feature flag inheritance patterns and personal quota management systems
- **Troubleshooting Guide**: Comprehensive issue resolution guide with performance and database considerations
- **Future Work Planning**: Outlined planned enhancements based on Linear ticket EKO-279 requirements

---

(c) All rights reserved ekoIntelligence 2025
