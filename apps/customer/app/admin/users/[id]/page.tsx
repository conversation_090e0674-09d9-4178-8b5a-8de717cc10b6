/**
 * Next.js App Router Admin User Detail Management Page
 *
 * This administrative page component provides comprehensive user profile management and administration
 * capabilities within the EkoIntelligence ESG analysis platform. The interface serves as a detailed
 * view for individual user administration, allowing authorized admin users to view comprehensive user
 * information, manage user permissions, and access user-specific administrative functions including
 * feature flag management and quota administration.
 *
 * ## Core Functionality
 * - **User Profile Display**: Comprehensive view of user profile information and account details
 * - **Organization Integration**: Shows user's organization membership and provides organization management links
 * - **Admin Status Management**: Displays current admin privileges and system administrator status
 * - **Feature Flag Overview**: Lists user-specific feature flags and their current activation status
 * - **Quota Management**: Displays personal quotas assigned to the user with quantity information
 * - **Navigation Controls**: Quick access to feature flag and quota management interfaces
 * - **Real-time Data**: Server-side rendering ensures fresh user data on each page load
 *
 * ## User Information Display
 * The component presents comprehensive user data in an organized, accessible format:
 * - **Profile Information**: Full name, email, username, avatar, and website details
 * - **Account Metadata**: Last updated timestamp and account creation information  
 * - **Organization Membership**: Associated organization details with direct navigation to org management
 * - **Administrative Status**: Clear indication of admin privileges and system access levels
 * - **Feature Flags**: Complete list of user-specific feature flags with visual badge display
 * - **Personal Quotas**: Individual resource quotas with item types and quantity limits
 *
 * ## Route Parameters & Dynamic Routing
 * - **Dynamic Route**: `/admin/users/[id]` where `[id]` is the user's UUID identifier
 * - **Next.js 15 Compatibility**: Uses async params pattern for Next.js 15 App Router compatibility
 * - **Parameter Processing**: Server-side extraction and validation of user ID from route parameters
 * - **URL Structure**: RESTful URL pattern for user-specific administrative operations
 * - **Error Handling**: Automatic 404 response for invalid or non-existent user IDs
 *
 * ## Database Schema & Operations
 * - **Primary Query**: Fetches user data from `profiles` table using UUID-based lookup
 * - **Organization Join**: Left joins with `acc_organisations` table for organization information
 * - **Quota Retrieval**: Separate query to `acc_quota` table for user-specific quota information
 * - **Foreign Key Relations**: Leverages database relationships for efficient data aggregation
 * - **Row Level Security**: Supabase RLS policies ensure admin users access only authorized data
 * - **Data Types**: Handles UUID primary keys, text arrays for feature flags, and boolean admin flags
 *
 * ## Security & Access Control
 * - **Server Component**: Executes server-side for enhanced security and performance optimization
 * - **Admin Authorization**: Page requires admin privileges (is_admin = true) for access control
 * - **RLS Enforcement**: Database-level security policies restrict data access to authorized administrators
 * - **Parameter Validation**: Server-side validation prevents unauthorized user profile access
 * - **Session Management**: Integrates with Supabase authentication for secure session handling
 * - **Data Privacy**: Sensitive user information protected through proper access controls
 *
 * ## User Interface Design & Experience  
 * - **Glass-morphism Styling**: Consistent with platform design system using translucent card components
 * - **Card-based Layout**: Information organized in logical sections for improved readability
 * - **Avatar Integration**: User profile images with automatic fallback to initials display
 * - **Badge System**: Visual indicators for admin status, feature flags, and quota information
 * - **Action Buttons**: Quick access controls for feature flag and quota management workflows
 * - **Responsive Design**: Mobile-friendly layout with grid-based responsive organization
 * - **Loading States**: Optimized server-side rendering reduces perceived loading times
 *
 * ## Integration Points & System Architecture
 * - **Admin Dashboard**: Part of comprehensive admin interface for platform management
 * - **Organization Management**: Seamless navigation to organization administration interfaces
 * - **Feature Flag System**: Integration with platform-wide feature flag management system
 * - **Quota System**: Connection to resource quota management and usage tracking systems
 * - **User Management**: Central hub for individual user administration and account management
 * - **Analytics Integration**: Foundation for user activity tracking and administrative analytics
 *
 * ## Related Admin Components
 * - Feature Flag Management Page (`/admin/users/[id]/flags`) - User-specific feature configuration
 * - Quota Management Page (`/admin/users/[id]/quotas`) - Individual resource quota administration
 * - Organization Detail Page (`/admin/organizations/[id]`) - Organization-level management interface
 * - User List Page (`/admin/users`) - Overview of all platform users for administrative selection
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Integration
 * @see https://ui.shadcn.com/docs/components/avatar Shadcn/ui Avatar Component
 * @see /admin/organizations/[id]/page.tsx Organization Management Page
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Administrative user detail management page for comprehensive user profile administration and system management within the EkoIntelligence platform
 * @example ```typescript
 * // Access user detail page
 * router.push('/admin/users/550e8400-e29b-41d4-a716-************')
 * 
 * // Navigate to feature flag management
 * <Link href={`/admin/users/${userId}/flags`}>Manage Feature Flags</Link>
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createClient } from '@/app/supabase/server'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { User, ArrowLeft, Shield, Settings, Building } from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'

async function UserDetailPageContent({ userId }: { userId: string }) {
  const supabase = await createClient()
  
  const { data: user } = await supabase
    .from('profiles')
    .select(`
      *,
      acc_organisations!profiles_organisation_fkey(id, name, email_domain)
    `)
    .eq('id', userId)
    .single()

  if (!user) {
    notFound()
  }

  const { data: quotas } = await supabase
    .from('acc_quota')
    .select('*')
    .eq('customer', userId)

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/users">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <User className="h-8 w-8 mr-3 text-primary" />
          {user.full_name || 'Unknown User'}
        </h1>
        <p className="text-muted-foreground">
          User profile and permissions management
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <GlassCard>
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Avatar className="h-16 w-16">
                <AvatarImage src={user.avatar_url || undefined} />
                <AvatarFallback className="text-lg">
                  {user.full_name?.split(' ').map(n => n[0]).join('') || 'U'}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-lg">{user.full_name}</p>
                <p className="text-sm text-muted-foreground">{user.email}</p>
                {user.is_admin && (
                  <Badge variant="default" className="mt-1">
                    <Shield className="h-3 w-3 mr-1" />
                    System Admin
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <div>
                <label className="text-sm font-medium">Username</label>
                <p className="text-sm text-muted-foreground">{user.username || 'Not set'}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Website</label>
                <p className="text-sm text-muted-foreground">{user.website || 'Not set'}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Last Updated</label>
                <p className="text-sm text-muted-foreground">
                  {user.updated_at ? new Date(user.updated_at).toLocaleDateString() : 'Never'}
                </p>
              </div>
            </div>
          </CardContent>
        </GlassCard>

        <GlassCard>
          <CardHeader>
            <CardTitle>Organization & Access</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Organization</label>
              {user.acc_organisations ? (
                <div className="flex items-center space-x-2 mt-1">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">{user.acc_organisations.name}</p>
                    <p className="text-xs text-muted-foreground">{user.acc_organisations.email_domain}</p>
                  </div>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/admin/organizations/${user.acc_organisations.id}`}>
                      View Organization
                    </Link>
                  </Button>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No organization assigned</p>
              )}
            </div>
            
            <div>
              <label className="text-sm font-medium">Admin Status</label>
              <div className="mt-1">
                {user.is_admin ? (
                  <Badge variant="default">
                    <Shield className="h-3 w-3 mr-1" />
                    System Administrator
                  </Badge>
                ) : (
                  <Badge variant="secondary">Regular User</Badge>
                )}
              </div>
            </div>
          </CardContent>
        </GlassCard>
      </div>

      <div className="grid gap-6">
        <GlassCard>
          <CardHeader>
            <CardTitle>Feature Flags</CardTitle>
          </CardHeader>
          <CardContent>
            {user.feature_flags && user.feature_flags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {user.feature_flags.map((flag: string, index: number) => (
                  <Badge key={index} variant="secondary">
                    {flag}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No feature flags configured for this user.
              </p>
            )}
          </CardContent>
        </GlassCard>

        <GlassCard>
          <CardHeader>
            <CardTitle>Personal Quotas</CardTitle>
          </CardHeader>
          <CardContent>
            {quotas && quotas.length > 0 ? (
              <div className="space-y-2">
                {quotas.map((quota) => (
                  <div key={quota.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="text-sm font-medium">{quota.item_type}</span>
                    <Badge variant="outline">{quota.quantity}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No personal quotas configured for this user.
              </p>
            )}
          </CardContent>
        </GlassCard>
      </div>

      <div className="flex justify-end space-x-2">
        <Button variant="outline" asChild>
          <Link href={`/admin/users/${userId}/flags`}>
            <Settings className="h-4 w-4 mr-2" />
            Manage Feature Flags
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href={`/admin/users/${userId}/quotas`}>
            <Settings className="h-4 w-4 mr-2" />
            Manage Quotas
          </Link>
        </Button>
      </div>
    </div>
  )
}

export default async function UserDetailPage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const { id } = await params
  return <UserDetailPageContent userId={id} />
}