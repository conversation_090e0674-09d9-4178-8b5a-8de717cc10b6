/**
 * Next.js Admin User Quotas Management Page - Personal Quota Administration Interface
 *
 * This Admin interface enables administrators to manage personal quota limits for individual users
 * within the EkoIntelligence ESG analysis platform. The page provides comprehensive quota management
 * functionality including viewing, adding, updating, and removing personal quotas that control
 * access to platform resources like entity analysis, document processing, API calls, and storage.
 *
 * ## Core Functionality
 * - **Personal Quota Management**: Configure individual user quota limits separate from organizational quotas
 * - **Multi-Type Quota Support**: Handles various quota types including entities, documents, API calls, storage, reports, and exports
 * - **Real-time Updates**: Immediate quota modifications with optimistic UI updates and error handling
 * - **User Profile Integration**: Displays user information with avatar, name, email, and organizational membership
 * - **Admin Security**: Requires admin privileges through RLS policies and is_admin profile flag validation
 * - **Glass-morphism UI**: Implements the platform's signature translucent, rounded design system
 *
 * ## Quota Type System
 * The page manages personal quotas for different resource categories:
 * - **Entity Analysis**: Limits for ESG entity processing and analysis operations
 * - **Document Processing**: Controls document analysis and parsing capabilities  
 * - **API Calls**: Rate limiting for programmatic access to platform features
 * - **Storage (GB)**: Personal storage allocation for documents and data
 * - **Custom Reports**: Limits on report generation and customization features
 * - **Data Exports**: Controls for data export functionality and formats
 *
 * ## Database Integration
 * Integrates with Supabase PostgreSQL through the customer database schema:
 * - **acc_quota table**: Stores quota records with item_type, quantity, customer, and organisation fields
 * - **profiles table**: User profile data including full_name, email, avatar_url, and organizational links
 * - **acc_organisations table**: Organization data through foreign key relationships
 * - **RLS Security**: Row Level Security policies ensure admin-only access to quota management
 *
 * ## Admin Panel Architecture
 * This page is part of the comprehensive admin system at `/admin` route:
 * - **User Management**: Links to user profile editing and organizational membership
 * - **Organizational Quotas**: Separate interface for organization-level quota management
 * - **Feature Flags**: Integration with user and organizational feature flag management
 * - **Virtual Entities**: Management of allowed virtual entities for users/organizations
 * - **Message System**: Interface for sending acc_messages to users
 *
 * ## UI Components & Design
 * Implements the platform's glass-morphism design system:
 * - **GlassCard Components**: Translucent cards with rounded corners and backdrop blur
 * - **Responsive Layout**: Mobile-first design with lg:grid-cols-3 responsive grid
 * - **Real-time Feedback**: Toast notifications for all CRUD operations
 * - **Loading States**: Comprehensive loading indicators during data operations
 * - **Error Handling**: Graceful error states with user-friendly messaging
 *
 * ## Technical Implementation Details
 * - **Next.js 15 App Router**: Uses async params handling for Next.js 15 compatibility
 * - **Client Component**: Marked with 'use client' for interactive state management
 * - **Supabase Integration**: Real-time database operations with optimistic updates
 * - **Type Safety**: Full TypeScript integration with interface definitions
 * - **State Management**: React hooks for local state with Supabase synchronization
 * - **Navigation Integration**: Breadcrumb navigation and admin panel routing
 *
 * ## Security & Access Control
 * - **Admin Authentication**: Requires is_admin = true in user profile
 * - **RLS Policies**: Database-level security through Row Level Security
 * - **User Context**: Operates within authenticated Supabase user context  
 * - **Input Validation**: Client and server-side validation for quota values
 * - **Error Boundaries**: Comprehensive error handling for security failures
 *
 * ## System Integration
 * This component fits into the broader ESG analysis platform:
 * - **Analytics Backend**: Quota limits control access to Python backend processing
 * - **Data Sync Layer**: Personal quotas synchronized between customer and analytics databases
 * - **API Gateway**: Quota enforcement at API layer for rate limiting and resource control
 * - **Usage Tracking**: Integration with acc_usage table for quota consumption monitoring
 * - **Billing System**: Potential integration with billing for quota-based pricing models
 *
 * ## Performance Considerations
 * - **Optimistic Updates**: UI updates immediately with server synchronization
 * - **Minimal Re-renders**: Efficient state updates to prevent unnecessary renders
 * - **Data Fetching**: Single useEffect for initial data loading with error recovery
 * - **Memory Management**: Proper cleanup of event listeners and subscriptions
 * - **Loading Optimization**: Progressive loading with skeleton states
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes Next.js Dynamic Routes
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation  
 * @see https://react.dev/reference/react/useState React useState Hook
 * @see https://tailwindcss.com/docs/backdrop-blur Tailwind Backdrop Blur
 * @see {@link /apps/customer/app/admin/organizations/[id]/quotas/page.tsx} Organization Quotas Management
 * @see {@link /apps/customer/app/admin/users/[id]/page.tsx} User Profile Management
 * @see {@link /apps/customer/components/ui/glass-card.tsx} GlassCard Component
 * <AUTHOR>
 * @updated 2025-07-22
 * @description This Admin interface enables administrators to manage personal quota limits for individual users within the EkoIntelligence ESG analysis platform.
 * @example ```tsx
 * // Navigate to user quotas management
 * <Link href={`/admin/users/${userId}/quotas`}>
 *   Manage User Quotas
 * </Link>
 * 
 * // Add a new quota programmatically
 * const newQuota = {
 *   customer: userId,
 *   item_type: 'entities',
 *   quantity: 100,
 *   organisation: 0
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/app/supabase/client'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowLeft, Plus, X, User, Settings } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { notFound } from 'next/navigation'

interface UserProfile {
  id: string
  full_name: string | null
  email: string | null
  avatar_url: string | null
  acc_organisations?: {
    id: number
    name: string | null
  } | null
}

interface Quota {
  id: number
  item_type: string
  quantity: number
  created_at: string
}

const QUOTA_TYPES = [
  { value: 'entities', label: 'Entity Analysis' },
  { value: 'documents', label: 'Document Processing' },
  { value: 'api_calls', label: 'API Calls' },
  { value: 'storage', label: 'Storage (GB)' },
  { value: 'reports', label: 'Custom Reports' },
  { value: 'exports', label: 'Data Exports' },
]

function UserQuotasPageContent({ userId }: { userId: string }) {
  const [user, setUser] = useState<UserProfile | null>(null)
  const [quotas, setQuotas] = useState<Quota[]>([])
  const [newQuotaType, setNewQuotaType] = useState('')
  const [newQuotaQuantity, setNewQuotaQuantity] = useState('')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    fetchData()
  }, [userId])

  const fetchData = async () => {
    setLoading(true)
    try {
      // Fetch user
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select(`
          id, full_name, email, avatar_url,
          acc_organisations!profiles_organisation_fkey(id, name)
        `)
        .eq('id', userId)
        .single()

      if (userError) throw userError
      if (!userData) {
        notFound()
        return
      }

      setUser(userData)

      // Fetch quotas
      const { data: quotaData, error: quotaError } = await supabase
        .from('acc_quota')
        .select('*')
        .eq('customer', userId)
        .order('item_type')

      if (quotaError) throw quotaError
      setQuotas(quotaData || [])

    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load user data')
    } finally {
      setLoading(false)
    }
  }

  const handleAddQuota = async () => {
    if (!newQuotaType || !newQuotaQuantity || !user) return

    const quantity = parseInt(newQuotaQuantity)
    if (isNaN(quantity) || quantity < 0) {
      toast.error('Please enter a valid quantity')
      return
    }

    // Check if quota type already exists
    if (quotas.some(q => q.item_type === newQuotaType)) {
      toast.error('Quota for this item type already exists')
      return
    }

    setSaving(true)
    try {
      const { data, error } = await supabase
        .from('acc_quota')
        .insert({
          customer: userId,
          item_type: newQuotaType as any,
          quantity: quantity,
          organisation: 0 // Required field with default value
        })
        .select()
        .single()

      if (error) throw error

      setQuotas([...quotas, data])
      setNewQuotaType('')
      setNewQuotaQuantity('')
      toast.success('Quota added successfully')
    } catch (error) {
      console.error('Error adding quota:', error)
      toast.error('Failed to add quota')
    } finally {
      setSaving(false)
    }
  }

  const handleUpdateQuota = async (quotaId: number, newQuantity: number) => {
    if (isNaN(newQuantity) || newQuantity < 0) {
      toast.error('Please enter a valid quantity')
      return
    }

    try {
      const { error } = await supabase
        .from('acc_quota')
        .update({ quantity: newQuantity })
        .eq('id', quotaId)

      if (error) throw error

      setQuotas(quotas.map(q => 
        q.id === quotaId ? { ...q, quantity: newQuantity } : q
      ))
      toast.success('Quota updated successfully')
    } catch (error) {
      console.error('Error updating quota:', error)
      toast.error('Failed to update quota')
    }
  }

  const handleRemoveQuota = async (quotaId: number) => {
    try {
      const { error } = await supabase
        .from('acc_quota')
        .delete()
        .eq('id', quotaId)

      if (error) throw error

      setQuotas(quotas.filter(q => q.id !== quotaId))
      toast.success('Quota removed successfully')
    } catch (error) {
      console.error('Error removing quota:', error)
      toast.error('Failed to remove quota')
    }
  }

  const getQuotaLabel = (itemType: string) => {
    const quota = QUOTA_TYPES.find(q => q.value === itemType)
    return quota ? quota.label : itemType
  }

  if (loading) {
    return <div>Loading...</div>
  }

  if (!user) {
    return notFound()
  }

  const availableQuotaTypes = QUOTA_TYPES.filter(
    type => !quotas.some(q => q.item_type === type.value)
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/admin/users/${userId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to User
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center">
          <User className="h-8 w-8 mr-3 text-primary" />
          {user.full_name} - Quotas
        </h1>
        <p className="text-muted-foreground">
          Manage personal quota limits for this user
        </p>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <GlassCard>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-primary" />
                Personal Quotas ({quotas.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {quotas.length > 0 ? (
                  quotas.map((quota) => (
                    <div key={quota.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div>
                          <p className="font-medium">{getQuotaLabel(quota.item_type)}</p>
                          <p className="text-sm text-muted-foreground">{quota.item_type}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Input
                          type="number"
                          min="0"
                          className="w-24 text-center"
                          defaultValue={quota.quantity}
                          onBlur={(e) => {
                            const newValue = parseInt(e.target.value)
                            if (newValue !== quota.quantity) {
                              handleUpdateQuota(quota.id, newValue)
                            }
                          }}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveQuota(quota.id)}
                        >
                          <X className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-center text-muted-foreground py-8">
                    No personal quotas configured for this user.
                  </p>
                )}
              </div>
            </CardContent>
          </GlassCard>
        </div>

        <div className="space-y-6">
          <GlassCard>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-3">
                <Avatar>
                  <AvatarImage src={user.avatar_url || undefined} />
                  <AvatarFallback>
                    {user.full_name?.split(' ').map(n => n[0]).join('') || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{user.full_name}</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                  {user.acc_organisations && (
                    <p className="text-xs text-muted-foreground">
                      {user.acc_organisations.name}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </GlassCard>

          <GlassCard>
            <CardHeader>
              <CardTitle>Add Quota</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="quotaType">Quota Type</Label>
                <Select value={newQuotaType} onValueChange={setNewQuotaType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select quota type" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableQuotaTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="0"
                  value={newQuotaQuantity}
                  onChange={(e) => setNewQuotaQuantity(e.target.value)}
                  placeholder="Enter quota limit"
                />
              </div>

              <Button
                onClick={handleAddQuota}
                disabled={!newQuotaType || !newQuotaQuantity || saving}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                {saving ? 'Adding...' : 'Add Quota'}
              </Button>
            </CardContent>
          </GlassCard>

          <GlassCard>
            <CardHeader>
              <CardTitle>Quota Types</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2 text-xs">
                {QUOTA_TYPES.map((type) => (
                  <div key={type.value} className="p-2 bg-gray-50 rounded">
                    <p className="font-medium">{type.label}</p>
                    <p className="text-muted-foreground">{type.value}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </GlassCard>
        </div>
      </div>
    </div>
  )
}

export default function UserQuotasPage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const [userId, setUserId] = useState<string | null>(null)

  useEffect(() => {
    const resolveParams = async () => {
      const { id } = await params
      setUserId(id)
    }
    resolveParams()
  }, [params])

  if (!userId) {
    return <div>Loading...</div>
  }

  return <UserQuotasPageContent userId={userId} />
}