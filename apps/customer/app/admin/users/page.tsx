/**
 * Admin Users Management Dashboard for EkoIntelligence Platform
 *
 * This Next.js 15 App Router Server Component provides a comprehensive administrative interface 
 * for viewing and managing all platform users within the EkoIntelligence ESG (Environmental, 
 * Social, Governance) analysis system. Designed exclusively for system administrators, this 
 * dashboard displays user profiles in categorized views with admin-specific functionality 
 * including user navigation, status monitoring, and feature flag oversight.
 *
 * ## Core Functionality
 * - **User List Display**: Comprehensive grid view of all platform users with profile information
 * - **Admin vs Regular User Segregation**: Separate sections highlighting administrative privileges
 * - **User Profile Cards**: Avatar, name, email, organization membership, and admin status display
 * - **Feature Flag Indicators**: Visual badges showing active feature flag counts per user
 * - **Organization Integration**: Display of user organization membership with contextual information
 * - **Navigation Controls**: Direct links to individual user detail pages for deeper management
 * - **Real-time Statistics**: Dynamic user count badges showing admin vs regular user distribution
 *
 * ## Database Integration & Schema
 * **Supabase PostgreSQL Integration**: Utilizes Supabase server-side client with Row Level 
 * Security (RLS) policies ensuring only authenticated administrators can access user management 
 * functionality. The component performs server-side data fetching for optimal security and performance.
 *
 * **Primary Query - profiles Table**:
 * ```sql
 * SELECT 
 *   profiles.*,
 *   acc_organisations.name as org_name
 * FROM profiles
 * LEFT JOIN acc_organisations ON profiles.organisation = acc_organisations.id
 * ORDER BY profiles.full_name ASC;
 * ```
 *
 * **Database Schema - profiles Table**:
 * ```sql
 * CREATE TABLE profiles (
 *   id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
 *   full_name text,                                   -- User's display name
 *   email text UNIQUE,                               -- Primary email address
 *   avatar_url text,                                 -- Profile image URL
 *   username text UNIQUE,                            -- System username
 *   organisation bigint REFERENCES acc_organisations(id), -- FK to organization
 *   is_admin boolean DEFAULT false,                  -- Administrative privilege flag
 *   feature_flags text[] DEFAULT '{}',              -- Array of enabled feature flags
 *   updated_at timestamp with time zone,            -- Last profile update
 *   website text,                                    -- Optional user website
 *   welcome_message text                             -- Custom dashboard message
 * );
 * ```
 *
 * **Associated Schema - acc_organisations Table**:
 * ```sql
 * CREATE TABLE acc_organisations (
 *   id bigint PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
 *   name text NOT NULL,                              -- Organization display name
 *   email_domain text,                               -- Email domain for auto-assignment
 *   entity_xid text,                                 -- External entity identifier
 *   feature_flags text[] DEFAULT '{}',              -- Organization-level feature flags
 *   created_at timestamp with time zone DEFAULT now()
 * );
 * ```
 *
 * ## User Interface Design & Architecture
 * **Glass-morphism Design System**: Implements the platform's signature translucent, frosted 
 * glass-like aesthetic using custom `GlassCard` components with backdrop blur effects and 
 * subtle shadow depth. The interface maintains consistency with the broader platform design language.
 *
 * **Responsive Grid Layout**: 
 * - **Admin Users Section**: Highlighted with primary color scheme and shield icons
 * - **Regular Users Section**: Standard secondary styling with user icons
 * - **User Cards**: Individual profile cards with avatar, contact info, and organization context
 * - **Badge System**: Visual indicators for admin status, feature flag counts, and organization membership
 * - **Navigation Elements**: Quick-access buttons linking to detailed user management pages
 *
 * ## Access Control & Security
 * - **Server Component Architecture**: Executes entirely server-side for enhanced security
 * - **Admin Authorization**: Requires is_admin = true for page access via middleware/RLS policies
 * - **Row Level Security**: Database-level access controls restrict data visibility
 * - **Session Validation**: Integrates with Supabase authentication system
 * - **Data Privacy**: Protects sensitive user information through proper authorization layers
 * - **Audit Trail**: Component interactions can be logged for administrative oversight
 *
 * ## System Integration Points
 * - **User Detail Pages**: Navigation to `/admin/users/{userId}` for comprehensive user management
 * - **Organization Management**: Integration with organization administration system
 * - **Feature Flag System**: Visual indicators connecting to platform feature flag management
 * - **Quota Management**: Foundation for user resource quota administration
 * - **Authentication System**: Deep integration with Supabase Auth for user session management
 * - **Admin Dashboard**: Part of comprehensive administrative interface for platform governance
 *
 * ## Related Administrative Components
 * - Individual User Detail Page (`/admin/users/[id]/page.tsx`) - Comprehensive user profile management
 * - User Feature Flag Management (`/admin/users/[id]/flags/page.tsx`) - User-specific feature configuration
 * - User Quota Management (`/admin/users/[id]/quotas/page.tsx`) - Individual resource allocation
 * - Organization Management (`/admin/organizations/page.tsx`) - Organization-level administration
 * - Admin Dashboard (`/admin/page.tsx`) - Central administrative hub and navigation
 *
 * ## Performance Considerations
 * - **Server-Side Rendering**: Optimized data fetching with Next.js 15 App Router
 * - **Database Query Optimization**: Single join query for efficient user and organization data retrieval
 * - **Component Separation**: Admin and regular users processed separately for UI performance
 * - **Image Optimization**: Avatar images handled through Next.js Image optimization pipeline
 * - **Caching Strategy**: Leverages Next.js automatic static optimization where appropriate
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts Next.js App Router Pages
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase Row Level Security
 * @see https://ui.shadcn.com/docs/components/badge Shadcn/ui Badge Component Documentation
 * @see /admin/users/[id]/page.tsx Individual User Detail Management Page
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Administrative dashboard for comprehensive platform user management, displaying all users with admin/regular categorization and navigation to detailed user management functions
 * @example ```typescript
 * // Navigate to user management dashboard
 * router.push('/admin/users')
 * 
 * // Access individual user detail page
 * <Link href={`/admin/users/${user.id}`}>
 *   <ExternalLink className="h-4 w-4" />
 * </Link>
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { createClient } from '@/app/supabase/server'
import { GlassCard } from '@/components/ui/glass-card'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Users, ExternalLink, Search, Shield } from 'lucide-react'
import Link from 'next/link'

export default async function UsersPage() {
  const supabase = await createClient()
  
  const { data: users } = await supabase
    .from('profiles')
    .select(`
      id, full_name, email, avatar_url, is_admin, organisation, feature_flags,
      acc_organisations!profiles_organisation_fkey(name)
    `)
    .order('full_name')

  const adminUsers = users?.filter(user => user.is_admin) || []
  const regularUsers = users?.filter(user => !user.is_admin) || []

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Users</h1>
          <p className="text-muted-foreground">
            Manage user profiles and permissions
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            <Shield className="h-3 w-3 mr-1" />
            {adminUsers.length} admins
          </Badge>
          <Badge variant="secondary">
            <Users className="h-3 w-3 mr-1" />
            {regularUsers.length} users
          </Badge>
        </div>
      </div>

      <div className="grid gap-6">
        {adminUsers.length > 0 && (
          <GlassCard>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2 text-primary" />
                Admin Users ({adminUsers.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {adminUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 bg-primary/5 rounded-lg border border-primary/20">
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={user.avatar_url || undefined} />
                        <AvatarFallback>
                          {user.full_name?.split(' ').map(n => n[0]).join('') || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.full_name}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                        {user.acc_organisations && (
                          <p className="text-xs text-muted-foreground">
                            {user.acc_organisations.name}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="default">
                        <Shield className="h-3 w-3 mr-1" />
                        Admin
                      </Badge>
                      {user.feature_flags && user.feature_flags.length > 0 && (
                        <Badge variant="outline">
                          {user.feature_flags.length} flags
                        </Badge>
                      )}
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/admin/users/${user.id}`}>
                          <ExternalLink className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </GlassCard>
        )}

        <GlassCard>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-primary" />
              Regular Users ({regularUsers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {regularUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={user.avatar_url || undefined} />
                      <AvatarFallback>
                        {user.full_name?.split(' ').map(n => n[0]).join('') || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{user.full_name}</p>
                      <p className="text-sm text-muted-foreground">{user.email}</p>
                      {user.acc_organisations && (
                        <p className="text-xs text-muted-foreground">
                          {user.acc_organisations.name}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {user.feature_flags && user.feature_flags.length > 0 && (
                      <Badge variant="outline">
                        {user.feature_flags.length} flags
                      </Badge>
                    )}
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/admin/users/${user.id}`}>
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </GlassCard>
      </div>
    </div>
  )
}