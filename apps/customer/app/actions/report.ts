/**
 * # Next.js Server Actions Type Definitions for ESG Report Summaries
 * 
 * This file contains TypeScript interface definitions for structured ESG (Environmental, Social, Governance) 
 * report summary data used throughout the EkoIntelligence customer application's reporting system. These types 
 * define the expected shape of data returned by AI-powered analysis endpoints that generate comprehensive 
 * summaries for different aspects of corporate sustainability reporting and assessment.
 * 
 * ## Core Functionality
 * - **Category Analysis Structure**: Defines comprehensive data model for ESG category summaries with risk/opportunity assessment
 * - **Reliability Assessment Format**: Structures claims vs evidence analysis and promise tracking evaluations
 * - **Transparency Evaluation Model**: Defines cherry-picking detection and information flooding analysis format
 * - **AI-Generated Content Support**: Provides type safety for LLM-generated report content and examples
 * - **Model Section Integration**: Supports dynamic content mapping for different analytical frameworks (DEMISE, TREVR)
 * 
 * ## System Architecture Context
 * These type definitions are designed to work with the broader ESG analysis pipeline:
 * - **Analytics Backend**: Python system generates raw ESG analysis stored in `ana_*` tables
 * - **Data Sync Layer**: `xfer_*` tables synchronize processed data between analytics and customer databases
 * - **API Layer**: Next.js API routes (`/api/report/*`) use these types for structured report generation
 * - **AI Integration**: Google Gemini and Anthropic Claude LLMs generate content matching these interfaces
 * - **Frontend Components**: TipTap editor and dashboard components consume typed report data
 * 
 * ## Type Definitions Overview
 * 
 * ### CategorySummary
 * Comprehensive ESG category analysis (Environmental, Social, Governance) with:
 * - Overall summary assessment and key findings
 * - Positive aspects and achievements identification
 * - Negative impacts and concerns highlighting
 * - Risk assessment and mitigation strategies
 * - Opportunity identification for improvement
 * - Model-specific sections for different analytical frameworks
 * 
 * ### ReliabilitySummary
 * Corporate commitment reliability assessment including:
 * - Overall reliability evaluation and credibility scoring
 * - Claims analysis with accuracy verification and examples
 * - Promises tracking with fulfillment assessment and case studies
 * - Historical performance patterns and trend analysis
 * 
 * ### TransparencySummary
 * Information transparency and communication quality evaluation:
 * - Overall transparency assessment and disclosure quality
 * - Cherry-picking detection with selective disclosure analysis
 * - Examples of misleading or incomplete information presentation
 * - Communication integrity scoring and recommendations
 * 
 * ## Integration with Report API Endpoints
 * These types are intended for use with the following API routes:
 * - `/api/report/category/[category]/` - Environmental, Social, Governance category analysis
 * - `/api/report/entity/[entityId]/[runId]/reliability/` - Corporate reliability assessment
 * - `/api/report/entity/[entityId]/[runId]/transparency/` - Transparency and disclosure evaluation
 * 
 * ## AI Content Generation Pipeline
 * The types support AI-powered content generation workflow:
 * 1. **Data Extraction**: Raw ESG data fetched from synchronized `xfer_*` tables
 * 2. **Context Assembly**: Relevant claims, promises, and effect flags compiled for analysis
 * 3. **LLM Processing**: Google Gemini or Claude generates structured content matching these interfaces
 * 4. **Type Validation**: Generated content validated against TypeScript interfaces
 * 5. **Frontend Consumption**: Typed data consumed by React components for report display
 * 
 * ## Usage in Document Templates
 * These types integrate with the dynamic document template system:
 * - `<report-section>` components reference reliability and transparency endpoints
 * - Professional ESG report formatting with consistent data structure
 * - Citation management and source attribution for AI-generated content
 * - Template-based report generation with type-safe data binding
 * 
 * ## Future Enhancements
 * - **Validation Schema**: Addition of runtime validation using Zod or similar library
 * - **Localization Support**: Multi-language report generation with type-safe translation keys
 * - **Custom Metrics**: Extensible model sections for client-specific analytical frameworks
 * - **Real-time Updates**: WebSocket integration for live report updates during analysis
 * 
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations Next.js Server Actions Documentation
 * @see https://www.typescriptlang.org/docs/handbook/interfaces.html TypeScript Interface Documentation
 * @see {@link /apps/customer/app/api/report} Report API Routes
 * @see {@link /apps/customer/components/editor/templates} Document Template System
 * <AUTHOR>
 * @updated 2025-07-24
 * @description TypeScript interface definitions for structured ESG report summary data used in AI-powered analysis endpoints
 * @example ```typescript
// Example usage in API route
import type { CategorySummary, ReliabilitySummary } from '@/app/actions/report'

export async function generateCategoryReport(): Promise<CategorySummary> {
  return {
    summary: "Environmental performance shows mixed results...",
    positives: "Strong renewable energy adoption...",
    negatives: "Increased carbon emissions in Q3...",
    risks: "Regulatory compliance challenges...",
    opportunities: "Green technology investments...",
    model_sections: {
      demise: "Domain analysis reveals...",
      trevr: "Time-based trends indicate..."
    }
  }
}
```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use server'


export type CategorySummary = {
  summary: string;
  positives: string;
  negatives: string;
  risks: string;
  opportunities: string;
  model_sections: Record<string, string>; // This was added to the result after AI call
};

export type ReliabilitySummary = {
  summary: string;
  claims: {
    summary: string;
    examples: string[];
  };
  promises: {
    summary: string;
    examples: string[];
  };
};

export type TransparencySummary = {
  summary: string;
  cherry_picking: { // Note: key was 'cherry_picking' in original JSON structure
    summary: string;
    examples: string[];
  };
};
