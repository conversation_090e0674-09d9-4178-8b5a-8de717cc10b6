# Next.js App Router Directory - EkoIntelligence ESG Analysis Platform

## Overview

The `/apps/customer/app` directory is the core Next.js 15 App Router implementation that powers the EkoIntelligence ESG (Environmental, Social, Governance) analysis platform. This directory serves as the main routing and page structure foundation for a sophisticated customer-facing application that provides comprehensive ESG analytics, document collaboration, real-time reporting, and administrative functionality.

This App Router implementation leverages Next.js 15's advanced features including Server Components, Client Components, parallel routes, intercepting routes, and intelligent caching to deliver enterprise-grade ESG analysis workflows. The platform serves financial institutions, sustainability consultants, and corporate ESG teams with sophisticated tools for entity analysis, claims verification, promise tracking, and collaborative report generation.

Built on a modern tech stack including Supabase PostgreSQL with Row Level Security, TipTap collaborative editing, Google Gemini AI integration, and a dual-database architecture, this directory houses the complete frontend infrastructure that transforms complex ESG data into actionable insights through an intuitive glass-morphism design system.

## Specification

### Next.js 15 App Router Architecture

The directory implements Next.js 15 App Router conventions with sophisticated routing patterns:

**Core App Router Features**:
- **Server Components**: Primary rendering strategy for authentication, data fetching, and initial page loads
- **Client Components**: Strategic use for interactivity, real-time features, and browser-specific functionality
- **Parallel Routes**: Modal system implementation using `@modal` directory for overlay interfaces
- **Intercepting Routes**: Dynamic route interception for seamless modal experiences (e.g., `(.)flags/[id]`)
- **Dynamic Routing**: Entity-based routing with parameters for ESG analysis context
- **Force Dynamic Rendering**: Authentication routes use `dynamic = 'force-dynamic'` for security

**Application Structure Standards**:
```
app/
├── layout.tsx                    # Root layout with authentication infrastructure
├── page.tsx                      # Authentication gateway and routing controller
├── globals.css                   # Glass-morphism design system foundation
├── (print)/                      # Print layout group for PDF exports
├── actions/                      # Server Actions for form processing
├── admin/                        # Administrative interface modules
├── api/                          # API Route Handlers for backend integration
├── auth/                         # Authentication callback handlers
├── customer/                     # Main customer application
├── login/                        # Authentication interface
├── share/                        # Public document sharing routes
└── supabase/                     # Database and authentication configuration
```

### ESG Platform Technical Requirements

**Authentication and Security Architecture**:
- Supabase Auth integration with server-side session validation
- Row Level Security (RLS) policy enforcement for multi-tenant data isolation
- Admin privilege detection via email domain validation (`@ekointelligence.com`)
- Secure HTTP-only cookie-based session management
- Feature flag system for progressive feature rollout and access control

**Database Integration Specifications**:
- **Customer Database**: Supabase PostgreSQL with user profiles, documents, and application data
- **Analytics Database**: Backend Python system processing complex ESG analysis
- **Data Synchronization**: Automated sync via `xfer_*` tables between analytics and customer databases
- **Real-time Updates**: Supabase real-time subscriptions for live data synchronization

**API Architecture Requirements**:
- Next.js App Router Route Handlers for server-side API endpoints
- Google Gemini AI integration for intelligent content generation and analysis
- AWS S3 integration for secure document storage and processing
- Vercel KV caching for performance optimization of expensive AI operations
- TipTap Cloud collaboration infrastructure for real-time document editing

## Key Components

### Root Application Infrastructure

| Component | Purpose | Key Features | Technologies |
|-----------|---------|--------------|-------------|
| **`layout.tsx`** | Root application wrapper | HTML structure, font optimization, context providers, mobile navigation | Next.js 15, Geist Sans, NuqsAdapter |
| **`page.tsx`** | Authentication gateway | Server-side auth validation, intelligent routing, session management | Supabase Auth, Next.js redirects |
| **`globals.css`** | Design system foundation | Glass-morphism utilities, Tailwind CSS, responsive patterns | CSS, Tailwind CSS |
| **`not-found.tsx`** | Error handling | Custom 404 page with navigation context | Next.js error boundaries |
| **`error.tsx`** | Global error boundary | Application-wide error handling and recovery | Next.js error components |

### Feature Modules Architecture

| Module Directory | Core Functionality | Integration Points | Database Tables |
|------------------|-------------------|-------------------|-----------------|
| **`admin/`** | System administration | User management, organization control, feature flags | `profiles`, `acc_organisations`, `acc_quota` |
| **`customer/`** | Main ESG platform | Dashboard analytics, document editing, entity analysis | `xfer_*` tables, `doc_documents` |
| **`api/`** | Backend services | AI report generation, file processing, data analysis | All databases via Supabase |
| **`auth/`** | Authentication flow | OAuth callbacks, session management | Supabase Auth tables |
| **`login/`** | User authentication | Login interface, password reset, registration | Supabase Auth |

### Specialized Route Groups

| Route Group | Purpose | Implementation | Use Cases |
|-------------|---------|----------------|-----------|
| **`(print)/`** | PDF export routes | Print-optimized layouts without navigation | Document export, report printing |
| **`share/`** | Public document access | Unauthenticated document viewing | Public report sharing, stakeholder access |
| **`actions/`** | Server Actions | Form processing, data mutations | Document operations, user interactions |
| **`api/`** | API Route Handlers | RESTful endpoints, AI integration | External integrations, data processing |

### API Route Handlers Structure

The `/api` directory implements comprehensive API functionality:

```mermaid
graph TB
    subgraph "API Routes"
        A[AI Services] --> B[/api/ai/]
        C[Document Processing] --> D[/api/documents/]
        E[File Management] --> F[/api/files/]
        G[Report Generation] --> H[/api/report/]
        I[Data Analysis] --> J[/api/summarize/]
        K[Timeline Data] --> L[/api/timeline/]
    end
    
    subgraph "Core Services"
        B --> M[Chat, Edit Document, Generate]
        D --> N[Auto-save, Version Control]
        F --> O[S3 Upload, URL Processing]
        H --> P[Entity Reports, Harm Analysis]
        J --> Q[Content Summarization]
        L --> R[Timeline Visualization]
    end
    
    subgraph "External Integration"
        M --> S[Google Gemini AI]
        O --> T[AWS S3 Storage]
        P --> U[Analytics Database]
        Q --> V[Vercel KV Cache]
    end
```

## Dependencies

### Core Framework Stack

**Next.js 15 App Router Dependencies**:
- **Next.js 15.3.2**: Latest React framework with App Router, Server Components, and advanced caching
- **React 19.0.0**: Concurrent rendering, Suspense boundaries, and server/client component architecture
- **TypeScript 5.8.3**: Comprehensive type safety with strict configuration and advanced type features
- **Tailwind CSS 3.4.1**: Utility-first styling with custom glass-morphism classes and responsive design

**Database and Authentication Stack**:
- **Supabase (latest)**: PostgreSQL database with Row Level Security policies and real-time subscriptions
- **@supabase/ssr**: Server-side rendering integration for secure authentication
- **Supabase Auth**: Integrated authentication with automatic token management and session handling
- **Supabase Storage**: File storage for documents and media assets with signed URL access

### UI Framework and Design System

**Component Library Dependencies**:
- **@radix-ui/**: Comprehensive primitive components (Dialog, Dropdown, Toast, etc.) for accessibility
- **shadcn/ui**: Consistent component library built on Radix UI with customizable styling
- **Lucide React 0.460.0**: Modern SVG icon library with comprehensive ESG-themed icons
- **Class Variance Authority 0.7.1**: Type-safe component variant management
- **Tailwind Merge 2.6.0**: Intelligent Tailwind class merging for component composition

**Rich Text Editing Stack**:
- **TipTap 2.12.0**: Complete ecosystem including core, extensions, and React integration
- **@tiptap/extension-collaboration**: Real-time collaborative editing with conflict resolution
- **Y.js 13.6.27**: Conflict-free replicated data types for collaborative editing
- **@hocuspocus/provider 2.15.2**: WebSocket provider for TipTap collaboration infrastructure

### AI and External Service Integration

**Artificial Intelligence Dependencies**:
- **@google/generative-ai 0.24.1**: Google Gemini API integration for content generation
- **@ai-sdk/google 1.2.18**: AI SDK for advanced Google AI model orchestration
- **@anthropic-ai/sdk 0.51.0**: Claude AI integration for content analysis and generation
- **OpenAI 4.100.0**: GPT model integration for comparative analysis

**Cloud Services Integration**:
- **@aws-sdk/client-s3 3.812.0**: AWS S3 client for secure document storage
- **@aws-sdk/s3-request-presigner**: Presigned URL generation for direct browser uploads
- **@vercel/kv 2.0.0**: Distributed caching for performance optimization
- **@vercel/blob 1.1.1**: Blob storage integration for media assets

### Development and Testing Stack

**Testing Framework Dependencies**:
- **@playwright/test 1.52.0**: End-to-end testing framework for comprehensive user workflow testing
- **Vitest 3.2.4**: Fast unit testing framework with TypeScript support
- **@testing-library/react 16.3.0**: React component testing utilities with accessibility focus
- **Jest 30.0.2**: JavaScript testing framework for unit and integration tests

**Development Tools**:
- **ESLint**: Code linting with next/recommended configuration
- **Prettier**: Consistent code formatting across the entire application
- **Turbo**: Monorepo build system for optimized development workflows
- **PostCSS**: CSS processing pipeline with Tailwind CSS integration

## Usage Examples

### Basic Application Navigation and Routing

```typescript
// Root page authentication gateway
// URL: / (automatically redirects based on authentication)

// Unauthenticated user flow:
// 1. Visit "/" → Redirected to "/login?next=%2Fcustomer"
// 2. Complete login → Redirected to "/customer"

// Authenticated user flow:
// 1. Visit "/" → Immediately redirected to "/customer"

// Server-side authentication validation
import { createClient } from '@/app/supabase/server'
import { redirect } from 'next/navigation'

export default async function AuthenticationGateway() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login?next=" + encodeURIComponent("/customer"));
  } else {
    redirect("/customer");
  }
  
  return null; // Component never renders - pure routing logic
}
```

### ESG Dashboard Integration with Entity Context

```typescript
// Customer dashboard with entity-aware routing
// URL: /customer/dashboard?entity=AAPL&run=latest&model=ekoIntelligence

import { EntityProvider } from '@/components/context/entity/entity-context'

function ESGDashboard() {
  return (
    <EntityProvider>
      <div className="glass-effect-lit rounded-3xl p-6">
        <EntityModelRunSelector />
        <DashboardTabs>
          <TabContent value="flags">
            <FlagsAnalysisModule />
          </TabContent>
          <TabContent value="greenwashing">
            <GreenwashingDetectionModule />
          </TabContent>
          <TabContent value="predictive">
            <PredictiveAnalyticsModule />
          </TabContent>
        </DashboardTabs>
      </div>
    </EntityProvider>
  );
}

// Entity context automatically manages:
// - Selected entity state (e.g., "AAPL")
// - Analysis model configuration
// - Run parameter management
// - URL parameter synchronization
```

### API Route Handler Implementation

```typescript
// ESG report generation API endpoint
// File: /api/report/entity/[entityId]/[runId]/introduction/route.ts

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/app/supabase/server'
import { generateReportContent } from '@/app/api/report/gemini-client'

export const runtime = 'nodejs'
export const maxDuration = 180 // 3 minutes for complex AI generation

export async function GET(
  request: NextRequest,
  { params }: { params: { entityId: string; runId: string } }
) {
  try {
    // Server-side authentication validation
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch ESG data with RLS policy enforcement
    const entityData = await fetchEntityData(supabase, params.entityId)
    const runData = await fetchRunData(supabase, params.runId)

    // AI-powered content generation with validation
    const content = await generateReportContent({
      entityData,
      runData,
      reportType: 'introduction',
      model: 'gemini-2.5-flash',
      cacheKey: `intro-${params.entityId}-${params.runId}`
    })

    return NextResponse.json({
      text: content,
      metadata: {
        entityId: params.entityId,
        runId: params.runId,
        generatedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Report generation error:', error)
    return NextResponse.json(
      { error: 'Report generation failed' }, 
      { status: 500 }
    )
  }
}
```

### Document Collaboration Integration

```typescript
// TipTap collaborative document editing
// File: /customer/documents/[id]/page.tsx

import { EkoDocumentEditor } from '@/components/editor/EkoDocumentEditor'
import { useNavigationAutoSave } from '@/components/editor/hooks/useNavigationAutoSave'

export default function DocumentEditingPage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const [document, setDocument] = useState<DocumentType>()
  const [editor, setEditor] = useState<Editor>()

  // Navigation-aware auto-save integration
  useNavigationAutoSave({
    editor,
    documentId: params.id,
    enabled: !!editor && !!document
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <EkoDocumentEditor
        documentId={params.id}
        onDocumentLoad={setDocument}
        onEditorCreate={setEditor}
        collaborative={true}
        aiToolsEnabled={true}
        features={{
          tableOfContents: true,
          versionHistory: true,
          citationManagement: true,
          exportTools: true
        }}
      />
    </div>
  )
}
```

### Admin Interface with Feature Flag Integration

```typescript
// Administrative interface with two-stage authentication
// File: /admin/layout.tsx

import { createClient } from '@/app/supabase/server'
import { redirect } from 'next/navigation'

export default async function AdminLayout({
  children
}: {
  children: React.ReactNode
}) {
  const supabase = await createClient()
  
  // Stage 1: Session authentication
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  // Stage 2: Admin privilege validation
  const { data: profile } = await supabase
    .from('profiles')
    .select('is_admin, email')
    .eq('id', user.id)
    .single()

  // Email domain-based admin detection
  const isAdmin = profile?.is_admin || 
    (profile?.email?.endsWith('@ekointelligence.com') ?? false)

  if (!isAdmin) redirect('/customer')

  return (
    <div className="admin-layout">
      <AdminNavigation />
      <main className="admin-content">
        {children}
      </main>
    </div>
  )
}
```

## Architecture Notes

### Next.js 15 App Router Implementation Strategy

The application leverages Next.js 15's advanced routing capabilities with a sophisticated architecture:

```mermaid
graph TB
    subgraph "App Router Architecture"
        A[Root Layout] --> B[Authentication Gateway]
        A --> C[Global Context Providers]
        A --> D[Mobile Navigation]
        
        B --> E{User Authenticated?}
        E -->|No| F[Login Page]
        E -->|Yes| G[Customer Application]
        
        G --> H[Customer Layout]
        H --> I[Dashboard Routes]
        H --> J[Document Routes]
        H --> K[Analysis Routes]
        
        I --> L[@modal Parallel Routes]
        I --> M[Intercepting Routes]
        
        subgraph "Admin Routes"
            N[Admin Layout] --> O[User Management]
            N --> P[Organization Control]
            N --> Q[Feature Flags]
        end
        
        subgraph "API Routes"
            R[Route Handlers] --> S[AI Services]
            R --> T[Data Processing]
            R --> U[File Management]
        end
    end
```

**Server Component Strategy**:
- **Layout Components**: Implement authentication checks, context providers, and navigation infrastructure
- **Page Components**: Handle data fetching, initial rendering, and SEO optimization
- **API Route Handlers**: Process server-side logic, database queries, and external service integration

**Client Component Architecture**:
- **Interactive Elements**: Forms, modals, real-time subscriptions, and user input handling
- **Context Providers**: State management for entity selection, navigation, and user preferences
- **Dynamic Components**: Chart visualizations, collaborative editing, and real-time updates

### Database Integration Architecture

The platform implements a sophisticated dual-database pattern:

```mermaid
graph LR
    subgraph "Analytics Database (Python Backend)"
        A[Raw ESG Data] --> B[Analysis Pipeline]
        B --> C[Entity Processing]
        B --> D[Claims Analysis]
        B --> E[Promises Tracking]
        C --> F[ana_* tables]
        D --> F
        E --> F
        F --> G[xfer_* tables]
    end
    
    subgraph "Customer Database (Supabase)"
        G --> H[Data Sync Process]
        H --> I[xfer_* customer tables]
        I --> J[App Router Pages]
        K[User Data] --> L[profiles, documents]
        L --> J
    end
    
    subgraph "Next.js App Router"
        J --> M[Server Components]
        J --> N[API Route Handlers]
        M --> O[Client Components]
        N --> P[External APIs]
    end
```

**Data Flow Architecture**:
1. **Analytics Processing**: Python backend processes raw ESG data, performs complex analysis
2. **Data Synchronization**: Automated sync transfers processed data to customer database
3. **Real-time Access**: Next.js components access customer database with RLS policy enforcement
4. **User Interaction**: Client components interact with real-time data through Supabase subscriptions

### Authentication and Security Framework

The application implements enterprise-grade security:

```mermaid
sequenceDiagram
    participant U as User
    participant L as Layout (Server)
    participant S as Supabase Auth
    participant DB as Customer Database
    participant A as App Router Page
    
    U->>L: Navigate to protected route
    L->>S: Validate session via getUser()
    S->>L: Return user session or null
    
    alt User authenticated
        L->>DB: Query user profile with RLS
        DB->>L: Return user data + permissions
        L->>A: Render with auth context
        A->>U: Display authorized content
    else No valid session
        L->>U: Redirect to /login?next=current_path
    end
```

**Security Implementation**:
- **Server-Side Authentication**: All authentication validation occurs on server components
- **Row Level Security**: Database-level policy enforcement prevents unauthorized data access
- **Feature Flag Security**: Server-side validation prevents client-side manipulation
- **Session Management**: HTTP-only cookies with automatic refresh and cleanup

### Glass-Morphism Design System Implementation

The platform implements a sophisticated visual design system:

```css
/* Core glass-morphism utilities */
.glass-effect-lit {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 1.5rem; /* 24px standard */
}

.glass-effect-subtle {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-effect-brand-strong-lit {
  background: rgba(59, 130, 246, 0.15);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
}
```

**Design Pattern Implementation**:
- **Consistent Border Radius**: 1.5rem standard across all interactive elements
- **Translucent Backgrounds**: Varying opacity levels based on content importance
- **Backdrop Blur Effects**: Performance-optimized blur with browser compatibility fallbacks
- **Color-Coded Interactions**: Brand colors for actions, complementary colors for states

## Known Issues

### Active Development Issues

Based on analysis of Linear tickets and codebase examination:

**EKO-238: Agentic Scraper (In Progress)**
- **Description**: Ongoing development of intelligent web scraping system for ESG data collection
- **Impact**: Affects data ingestion quality and entity analysis completeness
- **Status**: In Progress - Current branch `feature/eko-238`
- **Components Affected**: Document processing pipeline, entity analysis workflows

**EKO-226: AI Disclaimer Implementation (Recently Resolved)**
- **Issue**: Required prominent disclaimer about AI-generated content limitations
- **Resolution**: Implemented comprehensive footer disclaimer with user-controlled dismissal
- **Impact**: Enhanced user transparency and regulatory compliance

### Technical Debt and Maintenance Items

**Component Architecture Improvements**:
1. **Large Component Refactoring**: Root layout and main dashboard components exceed recommended size limits
2. **Context Provider Optimization**: Entity context could be split into more granular providers
3. **Error Boundary Enhancement**: Need more specific error boundaries for individual modules
4. **Performance Optimization**: Large datasets need virtualization and progressive loading

**Browser Compatibility Considerations**:
- **Glass-morphism Dependencies**: Requires `backdrop-filter` CSS support (Chrome 76+, Firefox 103+, Safari 9+)
- **Modern JavaScript Features**: Requires ES2020+ support for optimal functionality
- **WebSocket Support**: Real-time collaboration requires WebSocket support for full functionality

**Security and Compliance Items**:
- **Content Security Policy**: Enhanced CSP headers for improved security posture
- **Audit Logging**: Comprehensive user action logging for compliance requirements
- **Data Retention**: Automated cleanup of expired sessions and temporary data

## Future Work

### Immediate Priorities (Q2 2025)

**Enhanced Performance Optimization**:
- **Virtual Scrolling**: Implementation for large datasets in flags, claims, and analysis views
- **Progressive Data Loading**: Intelligent prefetching based on user behavior patterns
- **Edge Caching**: CDN optimization for static assets and frequently accessed data
- **Bundle Optimization**: Advanced code splitting and lazy loading strategies

**Advanced AI Integration**:
- **Contextual AI Assistants**: In-app guidance system for complex analysis workflows
- **Automated Content Enhancement**: Real-time content suggestions and quality improvements
- **Predictive Analytics**: User behavior-based recommendations and insights
- **Natural Language Interface**: Conversational queries for data exploration

### Medium-Term Enhancements (Q3-Q4 2025)

**Collaborative Features Enhancement**:
- **Real-time Multi-user Sessions**: Advanced collaboration beyond document editing
- **Advanced Permissions**: Granular access control for different user roles and responsibilities
- **Workflow Automation**: Automated approval processes and notification systems
- **Integration Hub**: Centralized management for third-party service integrations

**Enterprise Features Development**:
- **Advanced Analytics Dashboard**: Customizable metrics and KPI tracking
- **Reporting Automation**: Scheduled report generation and distribution
- **Compliance Management**: Automated compliance checking and audit trail generation
- **Multi-tenant Enhancements**: Advanced organization isolation and resource management

### Long-Term Vision (2025-2026)

**Platform Evolution**:
- **Micro-frontend Architecture**: Module federation for independent feature development
- **Advanced Mobile Experience**: Progressive web app features and offline capabilities
- **Machine Learning Integration**: Predictive modeling and automated trend detection
- **Global Localization**: Multi-language support and regional compliance features

**Industry Integration**:
- **Regulatory Compliance**: Automated TCFD, EU Taxonomy, and SEC disclosure compliance
- **Third-party Integrations**: Enhanced connectivity with external ESG data providers
- **API Ecosystem**: Public API development for customer integrations
- **White-label Solutions**: Customizable platform deployment for enterprise clients

## Troubleshooting

### Common Issues and Resolution Steps

**Application Loading and Authentication Problems**:

```bash
# Debug sequence for authentication issues:
1. Check browser console for JavaScript errors and network failures
2. Verify Supabase connection: Network tab should show successful auth requests
3. Confirm user session: localStorage should contain 'sb-*' keys
4. Test authentication flow: Clear localStorage, attempt fresh login
5. Verify RLS policies: Ensure user can access required data tables

# Resolution steps:
- Clear browser cache and localStorage completely
- Verify environment variables are properly configured
- Check Supabase project authentication settings
- Confirm database RLS policies allow user access
```

**Real-time Features and WebSocket Issues**:

```bash
# Debug real-time connectivity:
1. Check WebSocket connection status in Network tab
2. Verify Supabase real-time is enabled in project settings
3. Monitor browser console for subscription errors
4. Test subscription cleanup on component unmount
5. Verify user permissions for subscribed tables

# Common fixes:
- Restart browser to reset WebSocket connections
- Check for ad blockers interfering with WebSocket traffic
- Verify Supabase project real-time configuration
- Review subscription setup for proper error handling
```

**ESG Data Loading and Display Issues**:

```typescript
// Performance monitoring and optimization
const debugDataLoading = () => {
  console.log('Entity context state:', {
    selectedEntity,
    currentRun,
    analysisModel,
    loadingStates: {
      flagsLoading,
      claimsLoading,
      promisesLoading
    }
  });

  // Database query optimization
  const optimizeQueries = async () => {
    // Add indexes for common query patterns
    await supabase.sql`
      CREATE INDEX IF NOT EXISTS idx_xfer_flags_entity_run 
      ON xfer_flags(entity_xid, run_id);
      
      CREATE INDEX IF NOT EXISTS idx_xfer_claims_entity_run 
      ON xfer_claims(entity_xid, run_id);
    `;
  };
};
```

**Build and Deployment Troubleshooting**:

```bash
# Build troubleshooting sequence:
1. Run `tsc --noEmit` to check for TypeScript errors
2. Verify all dependencies are properly installed with correct versions
3. Check for circular imports causing build failures
4. Review component imports for correct file paths
5. Validate environment variables are properly configured

# Common build fixes:
pnpm clean                    # Clear Next.js cache
rm -rf node_modules .next     # Fresh dependency installation
pnpm install                  # Reinstall dependencies
pnpm build                    # Test production build

# Environment validation:
echo $NEXT_PUBLIC_SUPABASE_URL          # Verify Supabase URL
echo $NEXT_PUBLIC_SUPABASE_ANON_KEY     # Verify Supabase key
echo $GOOGLE_GENERATIVE_AI_API_KEY      # Verify AI integration
```

**Database Connection and Migration Issues**:

```sql
-- Check database connectivity and performance
SELECT 
  schemaname,
  tablename,
  n_tup_ins as inserts,
  n_tup_upd as updates,
  n_tup_del as deletes
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC;

-- Verify RLS policies are working correctly
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual 
FROM pg_policies 
WHERE schemaname = 'public';

-- Check xfer_* table synchronization status
SELECT 
  table_name,
  COUNT(*) as record_count,
  MAX(updated_at) as last_update
FROM information_schema.tables t
JOIN information_schema.columns c ON c.table_name = t.table_name
WHERE t.table_name LIKE 'xfer_%'
  AND c.column_name = 'updated_at'
GROUP BY table_name;
```

## FAQ

### User-Centric Questions and Answers

**Q: How do I navigate between different ESG analysis sections while keeping my company context?**
A: The application automatically preserves your entity selection (company), analysis model, and run parameters across all navigation. When you select a company, all menu items maintain this context through URL parameters (`?entity=AAPL&run=latest&model=ekoIntelligence`). This context persists across browser sessions and allows direct link sharing.

**Q: What's the difference between Server Components and Client Components in the ESG platform?**
A: Server Components handle authentication, data fetching, and initial page rendering on the server for optimal security and performance. Client Components manage interactivity like real-time chart updates, collaborative document editing, and user input handling. The platform strategically uses both for optimal user experience and security.

**Q: How does the collaborative document editing system work?**
A: The platform uses TipTap editor with Y.js for conflict-free collaborative editing. Multiple users can edit documents simultaneously with real-time synchronization, automatic conflict resolution, and presence indicators. Changes are automatically saved and synchronized across all connected users through WebSocket connections.

**Q: Why do some dashboard sections load faster than others?**
A: The platform uses progressive loading - different analysis types (flags, claims, promises, predictions) load independently from separate database views. This allows you to start exploring available data while other sections continue loading, preventing any single slow query from blocking the entire interface.

**Q: How do I share my ESG analysis reports with external stakeholders?**
A: Use the share functionality in the Documents section to generate public sharing links. These links provide read-only access to specific reports without requiring authentication, making them suitable for client presentations and stakeholder communication while maintaining security.

**Q: What file types can I upload for ESG document analysis?**
A: The platform supports PDF files up to 100MB, with URL-based document processing for web sustainability reports. Additional formats (DOC, DOCX, TXT) are being developed. All files are securely processed through AWS S3 with presigned URLs for direct browser uploads.

### Developer and Administrative Questions

**Q: How do I add a new analysis module to the customer dashboard?**
A: Create a new directory under `/customer/dashboard/` with your module's `page.tsx`, add routing configuration, implement the analysis interface using existing context providers, update navigation components, and test modal integration for detail overlays.

**Q: How does the entity context system work across the application?**
A: EntityContext is a React Context provider managing entity selection, model configuration, and analysis data globally. It automatically syncs with URL parameters and provides loading states for all analysis types. Access it via `useEntity()` hook in any component within the customer application.

**Q: How do I implement new API Route Handlers for ESG data processing?**
A: Create route files in the `/api` directory following Next.js App Router conventions, implement proper authentication validation using Supabase server client, add error handling and response formatting, integrate with database using RLS policies, and include appropriate caching strategies.

**Q: How does the authentication flow work for different user types?**
A: All users authenticate through Supabase Auth. Admin status is determined by email domain (`@ekointelligence.com`) or database flags. Regular users see standard ESG features, while admins get additional management capabilities. RLS policies ensure data isolation between organizations.

**Q: How do I optimize performance for large ESG datasets?**
A: Implement progressive loading with `useMemo` and `useCallback`, consider virtual scrolling for large lists, optimize database queries with proper indexing, implement client-side caching, and use Suspense boundaries strategically to prevent render blocking.

**Q: How does the dual-database architecture affect frontend development?**
A: The analytics database (Python backend) handles complex ESG processing, while the customer database (Supabase) serves optimized data to the frontend. Data flows through `xfer_*` tables. Frontend developers primarily work with customer database views and should understand that complex analysis happens asynchronously.

**Q: How do I customize the glass-morphism design system?**
A: Modify the custom `glass-effect-*` utility classes in the global CSS, ensure browser compatibility with `backdrop-filter` property, maintain consistent border radius standards (1.5rem), and test across different devices and browsers for optimal visual effects.

**Q: How do I add new real-time features using Supabase subscriptions?**
A: Use Supabase real-time subscriptions with proper cleanup in `useEffect`, subscribe to specific tables/rows, implement loading states, handle connection failures gracefully, ensure RLS policies allow subscriptions, and always clean up subscriptions on component unmount.

## References

### Documentation Links

**Next.js Framework and React**:
- [Next.js 15 App Router Documentation](https://nextjs.org/docs/app) - Modern React framework with Server Components
- [React 19 Documentation](https://react.dev/) - Latest React features including concurrent rendering
- [Next.js Parallel Routes](https://nextjs.org/docs/app/building-your-application/routing/parallel-routes) - Modal systems and intercepting routes
- [TypeScript 5.8 Documentation](https://www.typescriptlang.org/docs/) - Advanced TypeScript features and configuration

**Database and Authentication**:
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript) - Database operations and real-time features
- [Supabase Authentication Guide](https://supabase.com/docs/guides/auth/server-side) - Server-side authentication implementation
- [Supabase Row Level Security](https://supabase.com/docs/guides/auth/row-level-security) - Multi-tenant security model
- [PostgreSQL Documentation](https://www.postgresql.org/docs/current/) - Advanced database features and optimization

**UI Framework and Design System**:
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - Utility-first CSS framework
- [Radix UI Primitives](https://www.radix-ui.com/primitives) - Accessible component foundations
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react) - SVG icon system
- [CSS Backdrop Filter](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter) - Glass-morphism implementation

### Related Code Files

**Core Application Structure**:
- [`/apps/customer/app/layout.tsx`](./layout.tsx) - Root application layout with authentication context
- [`/apps/customer/app/page.tsx`](./page.tsx) - Authentication gateway and routing controller
- [`/apps/customer/app/globals.css`](./globals.css) - Glass-morphism design system foundation
- [`/apps/customer/app/supabase/server.ts`](./supabase/server.ts) - Supabase server client factory
- [`/apps/customer/app/supabase/middleware.ts`](./supabase/middleware.ts) - Authentication middleware

**Feature Module Structure**:
- [`/apps/customer/app/admin/README.md`](./admin/README.md) - Administrative interface documentation
- [`/apps/customer/app/customer/README.md`](./customer/README.md) - Main customer application documentation
- [`/apps/customer/app/api/report/README.md`](./api/report/README.md) - ESG report generation API documentation
- [`/apps/customer/app/login/README.md`](./login/README.md) - Authentication interface documentation
- [`/apps/customer/app/supabase/README.md`](./supabase/README.md) - Database configuration documentation

**Supporting Infrastructure**:
- [`/apps/customer/types/index.ts`](../../types/index.ts) - TypeScript interface definitions
- [`/apps/customer/components/context/auth/auth-context.tsx`](../../components/context/auth/auth-context.tsx) - Authentication context provider
- [`/apps/customer/components/context/entity/entity-context.tsx`](../../components/context/entity/entity-context.tsx) - Entity selection context
- [`/apps/customer/utils/react-utils.ts`](../../utils/react-utils.ts) - Utility functions and helpers
- [`/apps/customer/hooks/use-navigation-with-params.ts`](../../hooks/use-navigation-with-params.ts) - Parameter-aware navigation

### Third-Party Dependencies and Resources

**AI and Machine Learning Integration**:
- [Google Gemini API Documentation](https://ai.google.dev/gemini-api/docs) - AI content generation integration
- [Anthropic Claude API](https://docs.anthropic.com/claude/reference/getting-started) - Advanced AI analysis capabilities
- [OpenAI API Documentation](https://platform.openai.com/docs) - GPT model integration
- [Vercel AI SDK](https://sdk.vercel.ai/docs) - Multi-provider AI integration framework

**Rich Text Editing and Collaboration**:
- [TipTap Editor Documentation](https://tiptap.dev/docs/editor/introduction) - Collaborative rich text editing
- [Y.js Documentation](https://docs.yjs.dev/) - Conflict-free replicated data types
- [Hocuspocus Documentation](https://tiptap.dev/hocuspocus/introduction) - Real-time collaboration backend
- [ProseMirror Documentation](https://prosemirror.net/docs/) - Rich text editing foundation

**Cloud Services and Infrastructure**:
- [AWS S3 Documentation](https://docs.aws.amazon.com/s3/) - Secure document storage system
- [Vercel KV Documentation](https://vercel.com/docs/storage/vercel-kv) - Distributed caching solution
- [Vercel Deployment](https://vercel.com/docs/deployments) - Production deployment guide
- [Next.js Edge Runtime](https://nextjs.org/docs/app/api-reference/edge) - Edge computing optimization

### ESG and Sustainability Framework References

**Industry Standards and Compliance**:
- [UN Sustainable Development Goals](https://sdgs.un.org/goals) - Official SDG framework implementation
- [SASB Standards](https://sasb.org/standards/) - Sustainability accounting standards
- [GRI Sustainability Reporting](https://www.globalreporting.org/standards/) - Global reporting framework
- [TCFD Recommendations](https://www.fsb-tcfd.org/) - Climate disclosure framework
- [EU Taxonomy Regulation](https://ec.europa.eu/info/business-economy-euro/banking-and-finance/sustainable-finance/eu-taxonomy-sustainable-activities_en) - European sustainability classification

**ESG Data and Analysis Resources**:
- [CDP (formerly Carbon Disclosure Project)](https://www.cdp.net/) - Environmental disclosure system
- [MSCI ESG Research](https://www.msci.com/our-solutions/esg-investing) - ESG rating methodologies
- [Sustainalytics ESG Risk Ratings](https://www.sustainalytics.com/esg-research) - Risk assessment frameworks
- [Bloomberg ESG Data](https://www.bloomberg.com/professional/solution/esg-data/) - Financial ESG integration

### Platform Architecture Resources

**Monorepo and Development Environment**:
- [`/CLAUDE.md`](../../../CLAUDE.md) - Project-wide development guidelines
- [`/apps/customer/README.md`](../../README.md) - Customer application overview
- [`/backoffice/README.md`](../../../backoffice/README.md) - Python analytics backend documentation
- [`/packages/ui/README.md`](../../../packages/ui/README.md) - Shared UI component library

**Testing and Quality Assurance**:
- [`/apps/customer/tests/`](../../tests/) - Playwright E2E test suite
- [Playwright Documentation](https://playwright.dev/) - End-to-end testing framework
- [Vitest Documentation](https://vitest.dev/) - Fast unit testing framework
- [Testing Library Documentation](https://testing-library.com/) - Component testing utilities

**Linear Project Management Integration**:
- [EKO-238: Agentic Scraper](https://linear.app/ekointelligence/issue/EKO-238) - Current development focus
- [EKO-226: AI Disclaimer](https://linear.app/ekointelligence/issue/EKO-226) - User transparency enhancement
- [EKO-175: Navigation Auto-save](https://linear.app/ekointelligence/issue/EKO-175) - Document preservation improvement
- [EKO-176: Print Dialog Fix](https://linear.app/ekointelligence/issue/EKO-176) - PDF export functionality

---

## Changelog

### 2025-07-31

- **CREATED**: Comprehensive README.md documentation for Next.js App Router directory
- **ANALYZED**: Complete App Router architecture including authentication, routing, and component organization
- **DOCUMENTED**: All major route groups (admin, customer, api, auth, login) with cross-references to existing documentation
- **RESEARCHED**: Linear ticket integration for current development context (EKO-238, EKO-226, EKO-175, EKO-176)
- **SPECIFIED**: Next.js 15 App Router implementation with Server/Client Component architecture
- **DETAILED**: Dual-database architecture with analytics backend synchronization
- **MAPPED**: Glass-morphism design system implementation with CSS examples
- **PROVIDED**: Comprehensive troubleshooting guide with code examples and database queries
- **CREATED**: FAQ section addressing both user workflows and developer implementation
- **COMPILED**: Extensive reference documentation linking to framework docs, internal files, and external resources
- **INCLUDED**: Mermaid architecture diagrams for routing, database integration, and API structure
- **INTEGRATED**: Context7 Next.js documentation for accurate framework reference
- **ESTABLISHED**: Future work roadmap based on platform evolution requirements

---

(c) All rights reserved ekoIntelligence 2025