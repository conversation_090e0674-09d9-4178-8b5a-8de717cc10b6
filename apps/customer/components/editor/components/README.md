# Editor Components

## Overview

This directory contains the React UI components that make up the rich text editor interface in the EkoIntelligence ESG document analysis platform. These components provide a comprehensive editing experience built on top of TipTap (a headless rich-text editor framework) and are specifically designed for collaborative ESG document creation, analysis, and review workflows.

The components in this directory handle the interactive UI layer of the editor, including floating menus, context menus, AI command interfaces, collaboration indicators, and error boundaries. They work together to create a modern, glass-morphism styled editing interface that supports real-time collaboration, AI-powered content enhancement, and sophisticated document formatting capabilities.

## Specification

### Core Requirements
- **TipTap Integration**: All components must integrate seamlessly with TipTap's editor and extension system
- **Collaborative Editing**: Support for real-time collaborative features including cursor indicators, typing awareness, and presence management
- **AI Integration**: Components must support AI-powered writing assistance and content generation workflows
- **Glass-Morphism Design**: Consistent implementation of the platform's translucent, rounded design language
- **Accessibility**: Full keyboard navigation, screen reader compatibility, and ARIA attribute support
- **Error Resilience**: Robust error handling to prevent component failures from crashing the editor
- **Performance**: Optimized rendering and memory management for smooth editing experiences

### Technical Standards
- **React + TypeScript**: Functional components with comprehensive TypeScript interfaces
- **Test Coverage**: Each component must have corresponding unit tests with comprehensive edge case coverage
- **Event Handling**: Proper event cleanup and memory management to prevent leaks
- **State Management**: Efficient state handling with minimal re-renders
- **Portal Rendering**: Context menus and floating elements use React portals for proper DOM hierarchy

## Key Components

### AISlashCommandsList.tsx
**Purpose**: Renders a keyboard-navigable dropdown list of AI commands that appears when users type "/ai" in the editor.

**Key Features**:
- Keyboard navigation with arrow keys and Enter
- AI command execution through parent component callbacks
- Integration with TipTap's suggestion system
- Accessibility support with proper ARIA attributes

**Usage**: Used within TipTap's suggestion system to provide AI-powered text operations like content improvement, grammar correction, and custom transformations.

### BubbleMenuErrorBoundary.tsx
**Purpose**: React Error Boundary component providing robust error handling specifically for TipTap BubbleMenu components.

**Key Features**:
- Catches JavaScript errors during render lifecycle
- Graceful degradation with optional fallback UI
- Developer debugging support with comprehensive error logging
- Production safety with silent failure mode

**Usage**: Wraps BubbleMenu components to prevent individual menu failures from crashing the entire editor.

### ContextBubbleMenu.tsx
**Purpose**: Comprehensive floating context menu that appears when text is selected in the TipTap editor.

**Key Features**:
- Text selection detection and formatting tools
- Document structure management (headings, lists)
- Advanced features (links, comments, mathematical expressions)
- Clipboard operations integration
- Error handling and DOM safety validation

**Usage**: Primary text formatting interface that provides rich editing capabilities through a floating menu system.

### ContextMenu.tsx
**Purpose**: Right-click context menu component providing comprehensive document editing capabilities.

**Key Features**:
- Hierarchical menu structure with submenu support
- Selection-aware functionality with dynamic menu states
- Text highlighting system with color options
- Mathematical expression and emoji support
- Integration with collaborative features

**Usage**: Secondary editing interface accessed via right-click, offering extensive formatting and content insertion tools.

### CursorIndicator.tsx
**Purpose**: Suite of real-time collaboration components including cursor tracking, comment highlighting, typing indicators, and user presence.

**Key Features**:
- **CursorIndicator**: Real-time cursor position tracking with animated indicators
- **CommentHighlight**: Interactive comment threading with resolution states
- **TypingIndicator**: Live typing awareness for multiple users
- **CollaboratorPresence**: Avatar-based presence system

**Usage**: Enables real-time collaborative editing with visual indicators for multi-user document workflows.

### RightClickContextMenu.tsx
**Purpose**: Advanced right-click context menu with sophisticated content insertion and ESG-specific functionality.

**Key Features**:
- Dynamic position and viewport management
- Advanced content insertion (charts, report sections, multi-column layouts)
- ESG-specific components integration
- Extension availability checking with graceful degradation
- Performance optimization with memoized menu generation

**Usage**: Power-user interface for advanced document structure creation and content insertion.

## Dependencies

### Core Dependencies
- **@tiptap/react**: Headless rich-text editor framework providing the foundation for all editor functionality
- **React**: Component framework with hooks, portals, and error boundaries
- **TypeScript**: Type safety and developer experience
- **Lucide React**: Icon library providing consistent iconography across all menu components

### UI Dependencies
- **@/components/ui/context-menu**: Radix UI context menu primitives for robust menu behavior
- **@/lib/utils**: Utility functions including className merging (cn function)
- **Tailwind CSS**: Utility-first CSS framework for consistent styling and glass-morphism effects

### Editor Extensions
- **TipTap Extensions**: Various TipTap extensions for formatting, structure, and advanced features
- **Custom Extensions**: Platform-specific extensions for charts, report sections, and ESG functionality

### Collaborative Infrastructure
- **WebSocket Layer**: Real-time communication for cursor positions and presence updates
- **Y.js**: Conflict-free replicated data types for collaborative editing
- **Supabase**: Backend integration for comment state and user management

## Usage Examples

### Basic Bubble Menu Integration
```tsx
import { EditorContextBubbleMenu } from './ContextBubbleMenu'
import { BubbleMenuErrorBoundary } from './BubbleMenuErrorBoundary'

function DocumentEditor() {
  const editor = useEditor({
    extensions: [StarterKit, /* other extensions */],
    content: ''
  })

  return (
    <div>
      <EditorContent editor={editor} />
      {editor && (
        <BubbleMenuErrorBoundary>
          <EditorContextBubbleMenu
            editor={editor}
            onAddComment={(position) => handleCommentAddition(position)}
          />
        </BubbleMenuErrorBoundary>
      )}
    </div>
  )
}
```

### AI Slash Commands Setup
```tsx
import { AISlashCommandsList } from './AISlashCommandsList'

// Used within TipTap suggestion system
const suggestion = Suggestion.configure({
  suggestion: {
    items: ({ query }) => aiCommands.filter(item => 
      item.title.toLowerCase().includes(query.toLowerCase())
    ),
    render: () => {
      let component: ReactRenderer
      let popup: Instance[]

      return {
        onStart: (props) => {
          component = new ReactRenderer(AISlashCommandsList, {
            props: { 
              items: props.items, 
              command: props.command 
            },
            editor: props.editor,
          })
        },
        onUpdate: (props) => {
          component.updateProps({ 
            items: props.items, 
            command: props.command 
          })
        },
        onExit: () => {
          component?.destroy()
        }
      }
    }
  }
})
```

### Collaborative Features
```tsx
import { 
  CursorIndicator, 
  CollaboratorPresence, 
  TypingIndicator 
} from './CursorIndicator'

function CollaborativeEditor() {
  const { cursors, collaborators, typingUsers } = useCollaboration()

  return (
    <div className="relative">
      <EditorContent editor={editor} />
      
      {/* Real-time cursors */}
      {cursors.map(cursor => (
        <CursorIndicator
          key={cursor.user.id}
          user={cursor.user}
          position={cursor.position}
        />
      ))}
      
      {/* User presence */}
      <CollaboratorPresence users={collaborators} />
      
      {/* Typing indicators */}
      <TypingIndicator users={typingUsers} />
    </div>
  )
}
```

## Architecture Notes

### Component Hierarchy
```mermaid
graph TB
    A[TipTap Editor] --> B[EditorContent]
    A --> C[BubbleMenuErrorBoundary]
    C --> D[ContextBubbleMenu]
    A --> E[EditorContextMenu]
    A --> F[RightClickContextMenu]
    A --> G[Collaboration Components]
    
    G --> H[CursorIndicator]
    G --> I[CollaboratorPresence]
    G --> J[TypingIndicator]
    G --> K[CommentHighlight]
    
    D --> L[AI Integration]
    F --> M[Advanced Content]
    
    L --> N[AISlashCommandsList]
    M --> O[Chart Components]
    M --> P[Report Sections]
    M --> Q[Multi-Column Layouts]
```

### Event Flow Architecture
```mermaid
sequenceDiagram
    participant U as User
    participant E as Editor
    participant C as Components
    participant A as AI System
    participant B as Backend
    
    U->>E: Text Selection
    E->>C: Selection Event
    C->>C: Show Context Menu
    
    U->>C: AI Command
    C->>A: Execute AI Operation
    A->>E: Insert Generated Content
    
    U->>C: Add Comment
    C->>B: Store Comment
    B->>C: Broadcast to Collaborators
    C->>C: Update UI
```

### State Management Flow
```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> TextSelected: User selects text
    Idle --> RightClick: User right-clicks
    Idle --> AICommand: User types /ai
    
    TextSelected --> ShowBubbleMenu
    ShowBubbleMenu --> ExecuteFormat: Format command
    ShowBubbleMenu --> AddComment: Comment command
    
    RightClick --> ShowContextMenu
    ShowContextMenu --> InsertContent: Insert command
    ShowContextMenu --> AdvancedFormat: Format command
    
    AICommand --> ShowAIList
    ShowAIList --> ExecuteAI: AI command selected
    
    ExecuteFormat --> Idle
    AddComment --> Idle
    InsertContent --> Idle
    AdvancedFormat --> Idle
    ExecuteAI --> Idle
```

## Known Issues

### Current Limitations
1. **Browser Compatibility**: Some clipboard operations use deprecated `document.execCommand()` which may not work in all browsers
2. **Extension Dependencies**: Advanced features require specific TipTap extensions that may not be available in all configurations
3. **Mobile Support**: Context menus and bubble menus have limited touch device optimization
4. **Performance**: Large documents with many collaborative cursors may experience performance degradation

### TODO Items Found in Code
1. **Chart Integration**: Fallback chart creation uses HTML insertion when chart extensions are unavailable
2. **Error Handling**: Some extension availability checks could be more robust
3. **Memory Management**: Event listener cleanup could be more comprehensive in some components
4. **Accessibility**: Some dynamic content needs better screen reader support

## Future Work

Based on code analysis and platform requirements, the following enhancements are planned:

### Short-term Improvements
1. **Modern Clipboard API**: Replace deprecated `document.execCommand()` with modern Clipboard API
2. **Touch Support**: Improve mobile and tablet editing experience
3. **Performance Optimization**: Implement virtual scrolling for large documents
4. **Enhanced Error Boundaries**: More granular error handling with recovery options

### Medium-term Features
1. **Voice Commands**: Integration with speech-to-text for accessibility
2. **Advanced AI Features**: More sophisticated AI-powered content generation
3. **Custom Themes**: User-configurable color schemes and layouts
4. **Plugin System**: Architecture for third-party component extensions

### Long-term Vision
1. **Offline Support**: Local editing with sync when connectivity returns
2. **Advanced Analytics**: Usage analytics for editing patterns and efficiency
3. **Multi-Document Editing**: Tabbed interface for multiple document editing
4. **Version Control Integration**: Git-like versioning for document history

## Troubleshooting

### Common Issues

**Q: Bubble menu not appearing on text selection**
A: Check that the editor is properly initialized and not destroyed. Verify that `BubbleMenuErrorBoundary` is not catching and hiding errors.

**Q: Right-click context menu appears outside viewport**
A: The `RightClickContextMenu` component includes viewport boundary detection. If issues persist, check that the document body has proper dimensions.

**Q: AI commands not executing**
A: Verify that the AI provider is properly configured and that the parent component is handling the command callbacks correctly.

**Q: Collaborative features not working**
A: Ensure WebSocket connections are established and that the collaboration provider (Y.js) is properly initialized.

**Q: Performance issues with large documents**
A: Consider enabling lazy loading for collaboration features and reducing the number of real-time cursors displayed simultaneously.

### Debug Steps
1. **Check Console**: Look for error messages in browser developer console
2. **Verify Extensions**: Ensure all required TipTap extensions are loaded
3. **Test Isolation**: Test components individually to isolate issues
4. **Network Inspection**: Check WebSocket connections for collaboration features
5. **Memory Profiling**: Use browser dev tools to identify memory leaks

## FAQ

**Q: How do I add a new AI command to the slash menu?**
A: Add the command definition to the AI commands array and ensure the parent component handles the new command type in its callback.

**Q: Can I customize the appearance of the bubble menu?**
A: Yes, the components use Tailwind CSS classes. Modify the className props or create custom CSS overrides.

**Q: How do I handle custom TipTap extensions in these components?**
A: The components check for extension availability using `editor.extensionManager.extensions.find()`. Add similar checks for your custom extensions.

**Q: Is there a way to disable specific menu items?**
A: Yes, the menu generation logic includes conditional rendering based on editor state and available extensions.

**Q: How do I test these components?**
A: Each component has corresponding test files. Use the existing patterns with mocked TipTap editor instances and user interactions.

## References

### Documentation Links
- [TipTap Documentation](https://tiptap.dev/docs/editor/introduction) - Core editor framework
- [TipTap BubbleMenu Extension](https://tiptap.dev/docs/editor/api/extensions/bubble-menu) - Bubble menu integration
- [TipTap Suggestion Extension](https://tiptap.dev/docs/editor/extensions/functionality/suggestion) - AI command system foundation
- [React Error Boundaries](https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary) - Error handling patterns
- [Radix UI Context Menu](https://www.radix-ui.com/primitives/docs/components/context-menu) - Context menu primitives

### Related Code Files
- [../EkoDocumentEditor.tsx](../EkoDocumentEditor.tsx) - Main document editor component
- [../extensions/](../extensions/) - Custom TipTap extensions
- [../hooks/](../hooks/) - Editor-related React hooks
- [../services/](../services/) - Editor service layer
- [../../ui/context-menu.tsx](../../ui/context-menu.tsx) - UI component library

### Third-party Dependencies
- [Lucide React Icons](https://lucide.dev/) - Icon library used throughout components
- [Y.js Documentation](https://docs.yjs.dev/) - Collaborative editing framework
- [Tailwind CSS](https://tailwindcss.com/docs) - Utility-first CSS framework
- [Tippy.js](https://atomiks.github.io/tippyjs/) - Tooltip and popover library

### Testing Resources
- [Testing Library](https://testing-library.com/docs/react-testing-library/intro) - Component testing utilities
- [Vitest](https://vitest.dev/) - Test runner used in the project
- [Mock Service Worker](https://mswjs.io/) - API mocking for integration tests

---

## Changelog

### 2025-07-31
- Created comprehensive README.md documentation
- Analyzed all component files and their interdependencies
- Documented architecture patterns and usage examples
- Added troubleshooting guide and FAQ section
- Included Mermaid diagrams for visual architecture representation

(c) All rights reserved ekoIntelligence 2025