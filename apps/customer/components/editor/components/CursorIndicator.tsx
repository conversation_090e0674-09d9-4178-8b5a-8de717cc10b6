/**
 * Real-Time Collaborative Editor Cursor and Presence Indicators
 *
 * This React TypeScript module provides a comprehensive suite of UI components for displaying
 * real-time collaboration features within the TipTap rich text editor system. The components
 * enable visual representation of collaborative editing sessions, including live cursor positions,
 * user presence indicators, typing states, and comment highlighting for ESG document analysis
 * workflows. These components are essential for multi-user document editing experiences where
 * multiple stakeholders collaborate on ESG reports, sustainability assessments, and compliance
 * documentation in real-time.
 *
 * ## Core Components
 * - **CursorIndicator**: Real-time cursor position tracking with animated visual indicators
 * - **CommentHighlight**: Interactive comment threading and resolution system for document review
 * - **TypingIndicator**: Live typing awareness showing which users are actively editing
 * - **CollaboratorPresence**: Avatar-based presence system displaying active document participants
 *
 * ## Real-Time Collaboration Features
 * **Live Cursor Tracking**: The CursorIndicator component provides pixel-precise cursor positioning:
 * - **Position Synchronization**: Real-time cursor coordinates (x, y) transmitted via WebSocket connections
 * - **User Identification**: Color-coded cursors with user names for multi-participant document editing
 * - **Smooth Animation**: CSS transitions (100ms duration) for fluid cursor movement across the editor
 * - **Accessibility Support**: Screen reader compatible with data-testid attributes for automated testing
 * - **Performance Optimization**: Pointer-events disabled to prevent interference with editor interactions
 *
 * ## Comment and Review System
 * **Interactive Comment Highlighting**: CommentHighlight enables collaborative document review:
 * - **Visual Distinction**: Yellow highlighting for active comments, gray for resolved discussions
 * - **Click Interaction**: Clickable spans trigger comment threads and discussion panels
 * - **Resolution State**: Visual differentiation between active and resolved comment threads
 * - **Accessibility**: Border indicators and opacity changes provide visual cues for comment status
 * - **Thread Management**: Integration with document commenting system for ESG compliance workflows
 *
 * ## Typing Awareness System
 * **Live Typing Indicators**: TypingIndicator shows real-time editing activity:
 * - **Multiple Users**: Handles 1-N users typing simultaneously with intelligent text formatting
 * - **Animated Dots**: Bouncing animation indicators (150ms stagger) provide visual feedback
 * - **Smart Messaging**: Context-aware text ("John is typing", "John and Mary are typing", "John and 2 others")
 * - **Empty State Handling**: Graceful null rendering when no users are actively typing
 * - **Performance**: Lightweight rendering with conditional display logic
 *
 * ## User Presence Management
 * **Collaborative Awareness**: CollaboratorPresence displays active document participants:
 * - **Avatar System**: User avatars with fallback to initials for visual participant identification
 * - **Active Status**: Green indicator dots showing real-time document engagement
 * - **Color Coding**: Personalized user colors for consistent identity across cursor and presence
 * - **Responsive Layout**: Flexible avatar arrangement adapting to varying numbers of collaborators
 * - **Accessibility**: Comprehensive alt text and title attributes for screen reader compatibility
 *
 * ## TipTap Editor Integration
 * **Rich Text Editor Compatibility**: Designed specifically for TipTap collaborative editing:
 * - **Selection Tracking**: Cursor indicators follow TipTap selection state and document positions
 * - **Extension Support**: Compatible with TipTap collaboration extensions and Y.js document synchronization
 * - **Editor Overlay**: Positioned absolutely to overlay editor content without disrupting text flow
 * - **Event Handling**: Integrates with TipTap editor events for cursor position updates
 * - **State Management**: Works with React context providers for collaborative editor state
 *
 * ## ESG Document Workflow Integration
 * **Specialized Business Logic**: Tailored for ESG (Environmental, Social, Governance) document collaboration:
 * - **Compliance Review**: Comment highlighting supports regulatory compliance document review workflows
 * - **Multi-Stakeholder**: Presence indicators accommodate diverse stakeholder participation (legal, finance, sustainability)
 * - **Audit Trail**: Real-time indicators provide transparency for document editing audit trails
 * - **Version Control**: Integration with document versioning system for collaborative change tracking
 * - **Approval Workflows**: Comment resolution states support document approval and sign-off processes
 *
 * ## Technical Architecture
 * **React Component Design**: Modern functional components with TypeScript interfaces:
 * - **Props Interface**: Strongly typed component interfaces for user objects, positions, and states
 * - **CSS-in-JS**: Inline styles with CSS custom properties for dynamic user color theming
 * - **Responsive Design**: Tailwind CSS classes for consistent styling and responsive behavior
 * - **Performance**: React.memo optimization potential and efficient re-rendering patterns
 * - **Testing Support**: Comprehensive data-testid attributes for automated testing frameworks
 *
 * ## System Integration Points
 * **Backend Infrastructure**: Connects to broader collaboration infrastructure:
 * - **WebSocket Layer**: Real-time communication for cursor positions and presence updates
 * - **Database Sync**: Comment state and resolution status synchronized with Supabase backend
 * - **Authentication**: User identification and avatar management through customer authentication system
 * - **Permissions**: Document access control integration for comment and editing permissions
 * - **Analytics**: User engagement tracking for collaborative editing session analysis
 *
 * @see https://tiptap.dev/api/extensions/collaboration TipTap Collaboration Extension
 * @see https://docs.yjs.dev/ Y.js Real-time Collaboration Framework
 * @see https://tailwindcss.com/docs/animation Tailwind CSS Animation Documentation
 * @see /apps/customer/components/editor/_docs/collaboration-details.md Editor Collaboration Architecture
 * @see /apps/customer/components/editor/hooks/usePresence.ts Presence Hook Implementation
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Real-time collaborative editor cursor tracking, user presence indicators, comment highlighting, and typing awareness components for TipTap rich text editor ESG document workflows
 * @example
 * ```tsx
 * // Real-time cursor tracking
 * <CursorIndicator 
 *   user={{id: 'user123', name: 'John Doe', color: '#3B82F6'}} 
 *   position={{x: 150, y: 200}} 
 * />
 *
 * // Comment highlighting with resolution
 * <CommentHighlight isResolved={false} onClick={handleCommentClick}>
 *   This text has an active comment thread
 * </CommentHighlight>
 *
 * // Typing awareness
 * <TypingIndicator users={[{id: '1', name: 'Alice'}, {id: '2', name: 'Bob'}]} />
 *
 * // User presence avatars
 * <CollaboratorPresence users={[
 *   {id: '1', name: 'Alice Johnson', color: '#10B981', isActive: true},
 *   {id: '2', name: 'Bob Smith', avatar: '/avatars/bob.png', color: '#8B5CF6', isActive: false}
 * ]} />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import React from 'react'
import { cn } from '@utils/lib/utils'

interface CursorIndicatorProps {
  user: {
    id: string
    name: string
    color?: string
  }
  position: {
    x: number
    y: number
  }
  className?: string
}

export function CursorIndicator({ user, position, className }: CursorIndicatorProps) {
  const cursorColor = user.color || '#3B82F6'
  
  return (
    <div
      className={cn(
        'absolute pointer-events-none z-50 transition-all duration-100 ease-out',
        className
      )}
      style={{
        left: position.x,
        top: position.y,
        color: cursorColor,
      }}
      data-testid="cursor-indicator"
      data-user-id={user.id}
      data-user-name={user.name}
    >
      {/* Cursor line */}
      <div 
        className="w-0.5 h-5 animate-pulse"
        style={{ backgroundColor: cursorColor }}
      />
      
      {/* User name label */}
      <div
        className="absolute -top-6 left-0 transform -translate-x-1/2 px-2 py-1 rounded text-xs font-medium text-white whitespace-nowrap"
        style={{ backgroundColor: cursorColor }}
      >
        {user.name}
      </div>
    </div>
  )
}

interface CommentHighlightProps {
  children: React.ReactNode
  isResolved?: boolean
  onClick?: () => void
  className?: string
}

export function CommentHighlight({ 
  children, 
  isResolved = false, 
  onClick, 
  className 
}: CommentHighlightProps) {
  return (
    <span
      className={cn(
        'relative cursor-pointer transition-colors duration-200',
        isResolved 
          ? 'bg-gray-100 dark:bg-gray-800/30 border-l-2 border-gray-300 dark:border-gray-600 pl-1 opacity-60' 
          : 'bg-yellow-100 dark:bg-yellow-900/30 border-l-2 border-yellow-400 dark:border-yellow-600 pl-1 hover:bg-yellow-200 dark:hover:bg-yellow-900/50',
        'comment-highlight',
        className
      )}
      onClick={onClick}
      data-resolved={isResolved}
    >
      {children}
    </span>
  )
}

interface TypingIndicatorProps {
  users: Array<{
    id: string
    name: string
    color?: string
  }>
  className?: string
}

export function TypingIndicator({ users, className }: TypingIndicatorProps) {
  if (users.length === 0) return null

  const displayText = users.length === 1 
    ? `${users[0].name} is typing...`
    : users.length === 2
    ? `${users[0].name} and ${users[1].name} are typing...`
    : `${users[0].name} and ${users.length - 1} others are typing...`

  return (
    <div className={cn(
      'inline-flex items-center gap-2 px-3 py-1 bg-muted rounded-full text-xs text-muted-foreground',
      className
    )}>
      <span>{displayText}</span>
      <div className="flex gap-1">
        <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
    </div>
  )
}

interface CollaboratorPresenceProps {
  users: Array<{
    id: string
    name: string
    avatar?: string
    color?: string
    isActive?: boolean
  }>
  className?: string
}

export function CollaboratorPresence({ users, className }: CollaboratorPresenceProps) {
  return (
    <div className={cn('flex items-center gap-1', className)}>
      {users.map((user) => (
        <div
          key={user.id}
          className="relative"
          data-testid="collaborator-avatar"
        >
          <div
            className="w-6 h-6 rounded-full border-2 border-background flex items-center justify-center text-xs font-medium text-white"
            style={{ backgroundColor: user.color || '#3B82F6' }}
            title={user.name}
          >
            {user.avatar ? (
              <img 
                src={user.avatar} 
                alt={user.name}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              user.name.charAt(0).toUpperCase()
            )}
          </div>
          
          {/* Active indicator */}
          {user.isActive && (
            <div className="absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-500 border border-background rounded-full" />
          )}
        </div>
      ))}
    </div>
  )
}
