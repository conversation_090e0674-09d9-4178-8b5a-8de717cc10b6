/**
 * TipTap Editor Right-Click Context Menu Component for Comprehensive Document Editing
 *
 * This React component provides a sophisticated right-click context menu that appears when users right-click
 * anywhere within the TipTap rich text editor in the EkoIntelligence ESG document analysis platform. The component
 * offers an extensive array of text formatting, document structure manipulation, and advanced content editing tools
 * through an intuitive context menu interface. It serves as a comprehensive editing interface for applying rich
 * text formatting, managing document structure, creating links, adding comments, and accessing advanced features
 * like mathematical expressions and emoji insertion during ESG report creation and analysis workflows.
 *
 * ## Core Functionality
 * - **Right-Click Activation**: Appears on right-click anywhere within the editor content area
 * - **Comprehensive Text Formatting**: Bold, italic, underline, strikethrough, inline code, and text highlighting with color options
 * - **Document Structure Management**: Paragraph, heading levels (H1, H2, H3), bullet lists, ordered lists, and blockquotes
 * - **Text Alignment Control**: Left, center, right, and justify alignment options for improved document layout
 * - **Advanced Typography**: Superscript and subscript formatting for scientific and mathematical content
 * - **Clipboard Operations**: Copy, cut, and paste operations with native browser command integration
 * - **Interactive Features**: Link creation with URL prompts, comment insertion, mathematical expressions, and emoji picker trigger
 *
 * ## TipTap Editor Integration
 * **Radix UI Context Menu Integration**: Built on top of Radix UI's robust context menu primitives:
 * - **Native Context Menu Behavior**: Leverages browser's right-click event handling with custom menu rendering
 * - **Editor State Integration**: Directly manipulates TipTap editor state through command chaining API
 * - **Selection-Aware Functionality**: Enables/disables menu items based on current text selection state
 * - **ProseMirror Foundation**: Seamlessly integrates with ProseMirror's document model and state management
 * - **Extension Compatibility**: Works with all TipTap extensions including custom editor extensions
 *
 * ## Menu Structure & Organization
 * **Hierarchical Menu Layout**: Organized into logical sections with visual separators for improved usability:
 * 1. **Clipboard Operations**: Copy, cut, paste for fundamental text manipulation
 * 2. **Text Formatting Section**: Bold, italic, underline, strikethrough, inline code for text emphasis
 * 3. **Advanced Typography**: Superscript, subscript for scientific notation and mathematical expressions
 * 4. **Highlight Submenu**: Default highlight and color-specific highlighting (yellow, red, green, blue) with removal option
 * 5. **Document Structure Section**: Paragraph and heading conversions for content organization
 * 6. **List Management**: Bullet lists, ordered lists, and blockquotes for structured content
 * 7. **Text Alignment Submenu**: Left, center, right, justify alignment options
 * 8. **Interactive Elements**: Link creation and comment insertion for collaboration
 * 9. **Advanced Features**: Mathematical expressions, collapsible sections, and emoji insertion
 *
 * ## User Experience & Accessibility
 * **Contextual Menu Behavior**: Smart menu state management for optimal user experience:
 * - **Selection-Based States**: Menu items automatically enable/disable based on text selection
 * - **Visual Feedback**: Keyboard shortcuts displayed for quick reference and power user efficiency
 * - **Consistent Icons**: Lucide React icons provide clear visual indicators for all menu actions
 * - **Hover States**: Interactive feedback with background color changes on menu item hover
 * - **Logical Grouping**: Related functionality grouped together with visual separators
 *
 * ## Advanced Features Integration
 * **Text Highlighting System**:
 * - **Default Highlighting**: Standard text highlighting with editor's default color scheme
 * - **Color-Specific Highlighting**: Yellow (#fef3c7), Red (#fecaca), Green (#bbf7d0), Blue (#bfdbfe) options
 * - **Highlight Removal**: Dedicated option to remove all highlighting from selected text
 * - **Visual Color Indicators**: Menu items display actual color swatches for user clarity
 *
 * **Mathematical Expression Support**:
 * - **Inline Math Integration**: Triggers TipTap's setInlineMath command for LaTeX expression insertion
 * - **Scientific Content**: Enables complex mathematical notation in ESG reports and technical analysis
 * - **Seamless Integration**: Works with TipTap's math extension for equation rendering
 *
 * **Collaborative Features**:
 * - **Comment Integration**: Position-based comment insertion for document collaboration
 * - **Team Workflow Support**: Facilitates multi-user document review and annotation processes
 * - **Optional Comment Functionality**: Comment menu item only appears when onAddComment callback is provided
 *
 * ## Clipboard & Browser Integration
 * **Native Browser Commands**: Utilizes deprecated but widely supported execCommand API:
 * - **Copy Operation**: `document.execCommand('copy')` for copying selected text to clipboard
 * - **Cut Operation**: `document.execCommand('cut')` for cutting selected text to clipboard
 * - **Paste Operation**: `document.execCommand('paste')` for pasting clipboard content
 * - **Fallback Support**: Provides consistent behavior across different browsers and platforms
 * - **Security Considerations**: Works within browser security constraints for clipboard access
 *
 * ## Link Management & External References
 * **Interactive Link Creation**: Sophisticated link insertion workflow:
 * - **URL Prompt Dialog**: Native browser prompt for URL input with user-friendly interface
 * - **Selection Extension**: Uses TipTap's extendMarkRange to properly handle link boundaries
 * - **Link State Management**: Integrates with TipTap's link extension for consistent link handling
 * - **Cancellation Support**: Gracefully handles user cancellation of link creation dialog
 * - **Validation Ready**: Foundation for future URL validation and preview functionality
 *
 * ## System Architecture Integration
 * **ESG Platform Integration**: Core component of the document editing ecosystem:
 * - **Document Editor**: Primary editing interface for comprehensive ESG analysis reports
 * - **Report Generation**: Supports creation of detailed corporate sustainability assessments
 * - **Collaborative Workflows**: Enables multi-user document creation and peer review processes
 * - **Content Management**: Integrates with document versioning, auto-save, and revision tracking
 * - **AI Integration**: Provides foundation for AI-powered content enhancement and generation features
 * - **Glass-Morphism Design**: Consistent with platform's modern translucent design language
 *
 * ## Performance & Optimization
 * **Efficient State Management**: Optimized for smooth editor performance during intensive editing:
 * - **Selection State Caching**: Caches text selection state to avoid repeated editor queries
 * - **Conditional Rendering**: Menu items only render when contextually relevant to current state
 * - **Event Handling Optimization**: Minimal event handler creation with proper cleanup
 * - **Memory Efficiency**: No unnecessary state retention or memory leaks in menu operations
 * - **DOM Efficiency**: Lightweight DOM structure with minimal manipulation overhead
 *
 * ## Security & Data Protection
 * **Safe User Input Handling**: Robust input validation and sanitization:
 * - **URL Input Validation**: Foundation for validating and sanitizing user-provided URLs
 * - **XSS Prevention**: Safe handling of user input through TipTap's built-in sanitization
 * - **Command Injection Protection**: All editor commands use TipTap's safe command API
 * - **Content Security**: Integrates with platform's content security policies
 *
 * ## Testing & Quality Assurance
 * **Comprehensive Test Coverage**: Extensive test suite ensuring reliability:
 * - **Component Rendering Tests**: Validates proper menu structure and item rendering
 * - **User Interaction Tests**: Verifies all click handlers and keyboard shortcuts
 * - **State Management Tests**: Ensures proper enable/disable states based on selection
 * - **Edge Case Handling**: Tests for empty selections, invalid inputs, and error conditions
 * - **Accessibility Testing**: Validates screen reader compatibility and keyboard navigation
 *
 * @see https://tiptap.dev/docs/editor/api/editor TipTap Editor API Documentation
 * @see https://www.radix-ui.com/primitives/docs/components/context-menu Radix UI Context Menu Documentation
 * @see https://prosemirror.net/docs/guide/ ProseMirror Editor Guide
 * @see https://lucide.dev/ Lucide React Icons Library
 * @see {@link ../ui/context-menu.tsx} Radix UI Context Menu Components
 * @see {@link ./ContextMenu.test.tsx} Comprehensive Test Suite
 * @see {@link ../EkoDocumentEditor.tsx} Main Document Editor Component
 * <AUTHOR>
 * @updated 2025-07-23
 * @description TipTap Editor right-click context menu providing comprehensive text formatting, document structure, and advanced editing capabilities for ESG document creation
 * @example
 * ```tsx
 * // Basic usage within TipTap editor setup
 * import { EditorContextMenu } from './ContextMenu'
 *
 * function DocumentEditor() {
 *   const editor = useEditor({
 *     extensions: [StarterKit],
 *     content: '<p>Your document content</p>'
 *   })
 *
 *   return (
 *     <div>
 *       <EditorContextMenu editor={editor}>
 *         <EditorContent editor={editor} />
 *       </EditorContextMenu>
 *     </div>
 *   )
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import React from 'react'
import { Editor } from '@tiptap/react'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuShortcut,
  ContextMenuLabel,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Code,
  Highlighter,
  Copy,
  Scissors,
  Clipboard,
  Link,
  MessageSquare,
  Heading1,
  Heading2,
  Heading3,
  Type,
  List,
  ListOrdered,
  Quote,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Palette,
  Superscript,
  Subscript,
  Smile,
  FileText,
  Calculator,
  ChevronDown,
} from 'lucide-react'

interface EditorContextMenuProps {
  editor: Editor
  children: React.ReactNode
  onAddComment?: (position: number) => void
}

export function EditorContextMenu({ editor, children, onAddComment }: EditorContextMenuProps) {
  const handleCopy = () => {
    document.execCommand('copy')
  }

  const handleCut = () => {
    document.execCommand('cut')
  }

  const handlePaste = () => {
    document.execCommand('paste')
  }

  const handleAddLink = () => {
    const url = window.prompt('Enter URL:')
    if (url) {
      editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
    }
  }

  const handleAddComment = () => {
    const { from } = editor.state.selection
    onAddComment?.(from)
  }

  const handleHeadingChange = (level: 1 | 2 | 3) => {
    editor.chain().focus().toggleHeading({ level }).run()
  }

  const handleTextAlign = (alignment: 'left' | 'center' | 'right' | 'justify') => {
    editor.chain().focus().setTextAlign(alignment).run()
  }

  const handleHighlight = (color?: string) => {
    if (color) {
      editor.chain().focus().toggleHighlight({ color }).run()
    } else {
      editor.chain().focus().toggleHighlight().run()
    }
  }

  const isTextSelected = !editor.state.selection.empty

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent className="w-64">
        {/* Basic Clipboard Operations */}
        <ContextMenuItem onClick={handleCopy} disabled={!isTextSelected}>
          <Copy className="w-4 h-4" />
          Copy
          <ContextMenuShortcut>⌘C</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem onClick={handleCut} disabled={!isTextSelected}>
          <Scissors className="w-4 h-4" />
          Cut
          <ContextMenuShortcut>⌘X</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem onClick={handlePaste}>
          <Clipboard className="w-4 h-4" />
          Paste
          <ContextMenuShortcut>⌘V</ContextMenuShortcut>
        </ContextMenuItem>

        <ContextMenuSeparator />

        {/* Text Formatting */}
        <ContextMenuLabel>Text Formatting</ContextMenuLabel>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleBold().run()}
          disabled={!isTextSelected}
        >
          <Bold className="w-4 h-4" />
          Bold
          <ContextMenuShortcut>⌘B</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleItalic().run()}
          disabled={!isTextSelected}
        >
          <Italic className="w-4 h-4" />
          Italic
          <ContextMenuShortcut>⌘I</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          disabled={!isTextSelected}
        >
          <Underline className="w-4 h-4" />
          Underline
          <ContextMenuShortcut>⌘U</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleStrike().run()}
          disabled={!isTextSelected}
        >
          <Strikethrough className="w-4 h-4" />
          Strikethrough
          <ContextMenuShortcut>⌘⇧X</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleCode().run()}
          disabled={!isTextSelected}
        >
          <Code className="w-4 h-4" />
          Inline Code
          <ContextMenuShortcut>⌘E</ContextMenuShortcut>
        </ContextMenuItem>

        {/* Highlight Submenu */}
        <ContextMenuSub>
          <ContextMenuSubTrigger disabled={!isTextSelected}>
            <Highlighter className="w-4 h-4" />
            Highlight
          </ContextMenuSubTrigger>
          <ContextMenuSubContent>
            <ContextMenuItem onClick={() => handleHighlight()}>
              <Highlighter className="w-4 h-4" />
              Default Highlight
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleHighlight('#fef3c7')}>
              <div className="w-4 h-4 rounded bg-yellow-200 border" />
              Yellow
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleHighlight('#fecaca')}>
              <div className="w-4 h-4 rounded bg-red-200 border" />
              Red
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleHighlight('#bbf7d0')}>
              <div className="w-4 h-4 rounded bg-green-200 border" />
              Green
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleHighlight('#bfdbfe')}>
              <div className="w-4 h-4 rounded bg-blue-200 border" />
              Blue
            </ContextMenuItem>
            <ContextMenuItem onClick={() => editor.chain().focus().unsetHighlight().run()}>
              <Palette className="w-4 h-4" />
              Remove Highlight
            </ContextMenuItem>
          </ContextMenuSubContent>
        </ContextMenuSub>

        {/* Superscript/Subscript */}
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleSuperscript().run()}
          disabled={!isTextSelected}
        >
          <Superscript className="w-4 h-4" />
          Superscript
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleSubscript().run()}
          disabled={!isTextSelected}
        >
          <Subscript className="w-4 h-4" />
          Subscript
        </ContextMenuItem>

        <ContextMenuSeparator />

        {/* Block Types */}
        <ContextMenuLabel>Convert to</ContextMenuLabel>
        <ContextMenuItem onClick={() => editor.chain().focus().setParagraph().run()}>
          <Type className="w-4 h-4" />
          Paragraph
        </ContextMenuItem>
        <ContextMenuItem onClick={() => handleHeadingChange(1)}>
          <Heading1 className="w-4 h-4" />
          Heading 1
        </ContextMenuItem>
        <ContextMenuItem onClick={() => handleHeadingChange(2)}>
          <Heading2 className="w-4 h-4" />
          Heading 2
        </ContextMenuItem>
        <ContextMenuItem onClick={() => handleHeadingChange(3)}>
          <Heading3 className="w-4 h-4" />
          Heading 3
        </ContextMenuItem>
        <ContextMenuItem onClick={() => editor.chain().focus().toggleBulletList().run()}>
          <List className="w-4 h-4" />
          Bullet List
        </ContextMenuItem>
        <ContextMenuItem onClick={() => editor.chain().focus().toggleOrderedList().run()}>
          <ListOrdered className="w-4 h-4" />
          Numbered List
        </ContextMenuItem>
        <ContextMenuItem onClick={() => editor.chain().focus().toggleBlockquote().run()}>
          <Quote className="w-4 h-4" />
          Quote
        </ContextMenuItem>

        <ContextMenuSeparator />

        {/* Text Alignment */}
        <ContextMenuSub>
          <ContextMenuSubTrigger>
            <AlignLeft className="w-4 h-4" />
            Text Align
          </ContextMenuSubTrigger>
          <ContextMenuSubContent>
            <ContextMenuItem onClick={() => handleTextAlign('left')}>
              <AlignLeft className="w-4 h-4" />
              Left
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleTextAlign('center')}>
              <AlignCenter className="w-4 h-4" />
              Center
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleTextAlign('right')}>
              <AlignRight className="w-4 h-4" />
              Right
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleTextAlign('justify')}>
              <AlignJustify className="w-4 h-4" />
              Justify
            </ContextMenuItem>
          </ContextMenuSubContent>
        </ContextMenuSub>

        <ContextMenuSeparator />

        {/* Links and Comments */}
        <ContextMenuItem onClick={handleAddLink} disabled={!isTextSelected}>
          <Link className="w-4 h-4" />
          Add Link
          <ContextMenuShortcut>⌘K</ContextMenuShortcut>
        </ContextMenuItem>
        {onAddComment && (
          <ContextMenuItem onClick={handleAddComment} disabled={!isTextSelected}>
            <MessageSquare className="w-4 h-4" />
            Add Comment
            <ContextMenuShortcut>⌘⇧M</ContextMenuShortcut>
          </ContextMenuItem>
        )}

        <ContextMenuSeparator />

        {/* Advanced Features */}
        <ContextMenuLabel>Insert</ContextMenuLabel>
        <ContextMenuItem onClick={() => editor.chain().focus().setInlineMath('').run()}>
          <Calculator className="w-4 h-4" />
          Math Expression
          <ContextMenuShortcut>⌘⇧M</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem onClick={() => editor.chain().focus().toggleDetails().run()}>
          <ChevronDown className="w-4 h-4" />
          Collapsible Section
          <ContextMenuShortcut>⌘⇧D</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem onClick={() => {
          // Trigger emoji picker by inserting colon
          editor.chain().focus().insertContent(':').run()
        }}>
          <Smile className="w-4 h-4" />
          Emoji
          <ContextMenuShortcut>:</ContextMenuShortcut>
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  )
}
