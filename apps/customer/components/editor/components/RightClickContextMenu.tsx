/**
 * TipTap Editor Right-Click Context Menu Component for Enhanced Document Editing
 *
 * This React component provides a comprehensive right-click context menu that appears when users right-click
 * within the TipTap rich text editor in the EkoIntelligence ESG document analysis platform. The component
 * offers an extensive array of text formatting operations, document structure manipulation, advanced content
 * insertion capabilities, and specialized reporting tools through an intuitive right-click interface. It serves
 * as a powerful secondary user interface for accessing editor functionality beyond the standard toolbar,
 * providing context-sensitive operations for text selection, content insertion, and document enhancement
 * during ESG report creation and collaborative document editing workflows.
 *
 * ## Core Functionality
 * - **Context-Sensitive Menu**: Dynamically shows different options based on text selection state and cursor position
 * - **Clipboard Operations**: Native browser copy, cut, and paste operations with proper text selection handling
 * - **Text Formatting**: Complete text formatting suite including headings, lists, bold, italic, and text styling
 * - **Advanced Content Insertion**: Charts, report sections, tables, columns, images, and specialized ESG components
 * - **Document Structure**: Multi-column layouts, table of contents, references, and hierarchical content organization
 * - **AI Integration**: AI-powered content suggestions and automated content generation capabilities
 *
 * ## TipTap Editor Integration
 * **Right-Click Event Handling**: Built specifically for TipTap's editor DOM integration:
 * - **Native Context Menu Override**: Intercepts browser's default right-click behavior within editor boundaries
 * - **Position-Aware Operations**: Captures exact cursor position for precise content insertion and manipulation
 * - **Editor State Integration**: Directly manipulates editor state through TipTap's command chaining API
 * - **Extension Compatibility**: Seamlessly integrates with custom TipTap extensions (Charts, Report sections, etc.)
 * - **ProseMirror Foundation**: Leverages ProseMirror's robust document model and position-based operations
 * - **Selection State Management**: Intelligently handles both text selection and cursor position contexts
 *
 * ## Menu Structure & Organization
 * **Hierarchical Menu System**: Organized into logical sections with submenu support:
 * 1. **Clipboard Operations**: Copy, cut, paste, and select all for basic text manipulation
 * 2. **Text Formatting Submenu**: Headings (H1-H3), bullet lists, numbered lists for content structure
 * 3. **Link & Comment Tools**: Hyperlink creation and collaborative commenting for team workflows
 * 4. **Advanced Insert Submenu**: Comprehensive content insertion including:
 *    - **Report Components**: Table of contents, report sections, report summaries, report groups
 *    - **Layout Tools**: Multi-column layouts (2-4 columns, ratio-based, centered)
 *    - **Data Visualization**: Tables, charts for ESG data presentation
 *    - **Rich Media**: Images, videos, and interactive content
 *    - **Technical Content**: Code blocks, mathematical expressions, collapsible sections
 * 5. **AI Enhancement**: AI-powered content suggestions and automated improvements
 *
 * ## Advanced Content Insertion System
 * **ESG Report-Specific Components**:
 * - **Chart Integration**: Custom chart components with ECharts integration for data visualization
 * - **Report Sections**: Hierarchical report structure with API endpoint binding for dynamic content
 * - **Report Summaries**: AI-powered content summarization with configurable prompting
 * - **Report Groups**: Logical grouping of related content sections for better organization
 * - **Table of Contents**: Dynamic heading extraction with automatic linking and navigation
 * - **References System**: Citation management and academic-style referencing for ESG reports
 *
 * ## Dynamic Position & Viewport Management
 * **Intelligent Positioning System**: Sophisticated viewport-aware positioning for optimal user experience:
 * - **Viewport Boundary Detection**: Automatically adjusts menu position to stay within browser viewport
 * - **Right/Bottom Edge Handling**: Repositions menu when it would overflow viewport boundaries
 * - **Smart Offset Calculation**: Accounts for menu dimensions and adds proper padding from edges
 * - **Responsive Positioning**: Adapts to different screen sizes and device orientations
 * - **Z-Index Management**: High z-index (50) ensures menu appears above all other interface elements
 * - **Portal Rendering**: Uses React createPortal for proper DOM hierarchy and stacking context
 *
 * ## Context-Sensitive Menu Logic
 * **Dynamic Menu Item Generation**: Menu items change based on current editor state and selection:
 * - **Text Selection Context**: When text is selected, shows formatting options, copy/cut operations
 * - **Empty Selection Context**: When no text selected, focuses on insertion and document structure tools
 * - **Extension Availability**: Dynamically checks for TipTap extension availability before showing related options
 * - **Graceful Degradation**: Provides fallback implementations when specialized extensions are unavailable
 * - **Error Handling**: Comprehensive try-catch blocks for robust operation during extension failures
 *
 * ## Multi-Column Layout Integration
 * **Advanced Layout System**: Supports sophisticated multi-column document layouts:
 * - **Equal Column Layouts**: 2, 3, and 4 column layouts with equal widths for balanced content
 * - **Ratio-Based Layouts**: Asymmetric layouts with 1:2 and 2:1 ratios for emphasis and hierarchy
 * - **Centered Column Layout**: Single centered column for focused content presentation
 * - **Responsive Design**: Column layouts adapt to different screen sizes and print layouts
 * - **Content Flow**: Proper content flow and editing experience across column boundaries
 *
 * ## Chart & Data Visualization
 * **Integrated Chart System**: Sophisticated data visualization capabilities for ESG reporting:
 * - **ECharts Integration**: Full-featured charting using Apache ECharts library
 * - **Base64 Data Encoding**: Secure chart data transmission and storage
 * - **Chart Types**: Support for bar charts, line charts, pie charts, and custom visualizations
 * - **Dynamic Chart Creation**: Real-time chart creation with sample data and customizable options
 * - **Fallback Handling**: HTML-based fallbacks when chart extensions are unavailable
 *
 * ## Event Handling & User Interaction
 * **Comprehensive Event Management**: Robust event handling for smooth user experience:
 * - **Right-Click Detection**: Captures contextmenu events within editor boundaries only
 * - **Click Outside Handling**: Automatically hides menu when user clicks elsewhere
 * - **Keyboard Navigation**: Full keyboard support with Escape key for menu dismissal
 * - **Touch Device Support**: Responsive design that works on both desktop and mobile devices
 * - **Event Cleanup**: Proper event listener cleanup to prevent memory leaks
 *
 * ## Glass-Morphism Design Integration
 * **Modern UI Aesthetics**: Implements the platform's glass-morphism design system:
 * - **Translucent Background**: White background with subtle transparency for modern glass effect
 * - **Rounded Corners**: Consistent heavily rounded design language (rounded-lg) throughout
 * - **Shadow Effects**: Subtle drop shadows (shadow-lg) for depth and visual separation
 * - **Hover States**: Interactive feedback with smooth background color transitions
 * - **Submenu Styling**: Consistent styling across nested submenus with proper z-index layering
 * - **Visual Hierarchy**: Clear visual separation between menu sections using subtle dividers
 *
 * ## Error Handling & Stability
 * **Robust Error Protection**: Comprehensive error handling for stable editor operation:
 * - **Extension Availability Checks**: Validates TipTap extension availability before attempting operations
 * - **Editor State Validation**: Ensures editor is available and not destroyed before manipulation
 * - **Try-Catch Protection**: Extensive error logging with component-specific error identification
 * - **Graceful Degradation**: Provides fallback behaviors when advanced features are unavailable
 * - **Console Logging**: Detailed error logging for debugging and development support
 * - **Safe DOM Operations**: Validates DOM connectivity and prevents operations on invalid states
 *
 * ## Performance Optimization
 * **Efficient Rendering & Memory Management**: Optimized for smooth editor performance:
 * - **useMemo Optimization**: Menu items generation is memoized to prevent unnecessary recalculations
 * - **useCallback Optimization**: All event handlers are memoized to prevent unnecessary re-renders
 * - **Conditional Rendering**: Menu only renders when visible, reducing DOM overhead
 * - **Event Listener Management**: Proper cleanup of event listeners to prevent memory leaks
 * - **Lazy Menu Generation**: Menu items are only generated when menu is about to be displayed
 *
 * ## Accessibility & User Experience
 * **Comprehensive Accessibility**: Designed for inclusive user interaction:
 * - **Screen Reader Support**: Proper ARIA attributes and semantic HTML structure throughout
 * - **Keyboard Navigation**: Full keyboard accessibility for all menu functions and submenus
 * - **Focus Management**: Logical focus flow during keyboard navigation with proper focus trapping
 * - **Visual Feedback**: Clear hover states and active state indicators for all interactive elements
 * - **Disabled States**: Proper visual and functional disabled states for context-sensitive features
 * - **High Contrast Support**: Design works well with high contrast accessibility settings
 *
 * ## System Architecture Integration
 * **ESG Platform Integration**: Core component of the EkoIntelligence document editing ecosystem:
 * - **Document Editor**: Primary editing interface for ESG report creation and analysis
 * - **Collaborative Workflows**: Supports multi-user document creation and review processes
 * - **Report Generation**: Essential tool for creating structured ESG analysis reports
 * - **Content Management**: Integrates with document versioning, auto-save, and conflict resolution
 * - **AI Integration**: Provides foundation for AI-powered content generation and enhancement
 * - **Template System**: Works with document templates for consistent report formatting
 *
 * ## Extension Integration Architecture
 * **Custom TipTap Extension Support**: Designed to work with platform-specific editor extensions:
 * - **Chart Extension**: Integration with custom chart rendering and data visualization
 * - **Report Section Extension**: Hierarchical report structure with API endpoint integration
 * - **Columns Extension**: Multi-column layout support with responsive design
 * - **Table Extension**: Enhanced table functionality beyond standard TipTap tables
 * - **Math Extension**: Mathematical expression support for technical ESG content
 * - **References Extension**: Citation and bibliography management for academic-style reports
 *
 * ## API Integration & Data Flow
 * **Backend Integration**: Connects with EkoIntelligence backend services:
 * - **Report API Endpoints**: Report sections connect to specific API endpoints for dynamic content
 * - **Entity Data**: Integration with ESG entity database for contextual content insertion
 * - **Analytics Database**: Connection to analytics backend through customer database sync
 * - **User Authentication**: Respects user permissions and organizational access controls
 * - **Real-time Collaboration**: Works with Y.js collaborative editing infrastructure
 *
 * @see https://tiptap.dev/docs/editor/extensions/functionality/bubble-menu TipTap BubbleMenu Extension Documentation
 * @see https://tiptap.dev/docs/editor/api/commands TipTap Commands API Reference
 * @see https://prosemirror.net/docs/ref/#view.EditorView.posAtCoords ProseMirror Position Calculation
 * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/contextmenu_event MDN Context Menu Event Documentation
 * @see {@link ../extensions/ColumnsExtension} Custom Columns Extension Implementation
 * @see {@link ../../hooks/useNavigationAutoSave} Auto-save Hook Integration
 * <AUTHOR>
 * @updated 2025-07-23
 * @description TipTap Editor Right-Click Context Menu Component for Enhanced Document Editing with ESG-specific functionality
 * @example
 * ```tsx
 * import { RightClickContextMenu } from './RightClickContextMenu';
 *
 * <RightClickContextMenu
 *   editor={editor}
 *   onAddComment={(position) => handleAddComment(position)}
 *   onInsertChart={() => handleChartInsertion()}
 *   onInsertReportSection={() => handleReportSection()}
 *   onInsertTableOfContents={() => handleTOC()}
 *   onAISuggestion={() => handleAISuggestion()}
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { Editor } from '@tiptap/react'
import { createPortal } from 'react-dom'
import { ColumnLayout } from '../extensions/ColumnsExtension'
import {
  Copy,
  Scissors,
  Clipboard,
  Link,
  MessageSquare,
  Type,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  BarChart3,
  FileText,
  BookOpen,
  Sparkles,
  MoreHorizontal,
  ChevronRight,
  Table,
  Columns,
  Quote,
  Code,
  Minus,
  Image,
  Calculator,
  FolderOpen,
  Users,
  Layers,
  AlignLeft,
} from 'lucide-react'

interface MenuItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  action: () => void
  disabled?: boolean
  separator?: boolean
  submenu?: MenuItem[]
}

interface RightClickContextMenuProps {
  editor: Editor
  onAddComment?: (position: number) => void
  onInsertChart?: () => void
  onInsertReportSection?: () => void
  onInsertTableOfContents?: () => void
  onAISuggestion?: () => void
}

interface Position {
  x: number
  y: number
}

export function RightClickContextMenu({
  editor,
  onAddComment,
  onInsertChart,
  onInsertReportSection,
  onInsertTableOfContents,
  onAISuggestion,
}: RightClickContextMenuProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 })
  const [contextMenuPosition, setContextMenuPosition] = useState<number>(0)
  const [adjustedPosition, setAdjustedPosition] = useState<Position>({ x: 0, y: 0 })

  // Get the current selection state
  const isTextSelected = !editor.state.selection.empty
  const canPaste = true // We'll assume paste is always available

  const handleCopy = useCallback(() => {
    document.execCommand('copy')
    setIsVisible(false)
  }, [])

  const handleCut = useCallback(() => {
    document.execCommand('cut')
    setIsVisible(false)
  }, [])

  const handlePaste = useCallback(() => {
    document.execCommand('paste')
    setIsVisible(false)
  }, [])

  const handleSelectAll = useCallback(() => {
    editor.chain().focus().selectAll().run()
    setIsVisible(false)
  }, [editor])

  const handleAddLink = useCallback(() => {
    const url = window.prompt('Enter URL:')
    if (url) {
      editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
    }
    setIsVisible(false)
  }, [editor])

  const handleAddComment = useCallback(() => {
    if (onAddComment) {
      onAddComment(contextMenuPosition)
    }
    setIsVisible(false)
  }, [onAddComment, contextMenuPosition])

  const handleHeading = useCallback((level: 1 | 2 | 3) => {
    editor.chain().focus().toggleHeading({ level }).run()
    setIsVisible(false)
  }, [editor])

  const handleList = useCallback((ordered: boolean = false) => {
    if (ordered) {
      editor.chain().focus().toggleOrderedList().run()
    } else {
      editor.chain().focus().toggleBulletList().run()
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertChart = useCallback(() => {
    if (onInsertChart) {
      onInsertChart()
    } else {
      // Default chart insertion using HTML
      try {
        const chartId = `chart-${Date.now()}`
        console.log('Attempting to insert chart with ID:', chartId)
        
        // Check if the extension is available
        const extensionExists = editor.extensionManager.extensions.find(ext => ext.name === 'chart')
        console.log('Chart extension exists:', !!extensionExists)
        
        // Create sample chart data
        const chartData = {
          title: { text: 'New Chart' },
          xAxis: { type: 'category', data: ['A', 'B', 'C'] },
          yAxis: { type: 'value' },
          series: [{ type: 'bar', data: [10, 20, 30] }]
        }
        const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
        
        // Use HTML insertion instead of JSON object
        const htmlContent = `<chart id="${chartId}" data-json="${encodedData}" data-type="chart"></chart>`
        
        const result = editor.chain().focus().insertContent(htmlContent).run()
        
        console.log('Insert result:', result)
      } catch (error) {
        console.error('Chart extension not available:', error)
      }
    }
    setIsVisible(false)
  }, [editor, onInsertChart])

  const handleInsertReportSection = useCallback(() => {
    if (onInsertReportSection) {
      onInsertReportSection()
    } else {
      // Default report section insertion using JSON node structure (same as toolbar)
      try {
        const sectionId = `section-${Date.now()}`
        const title = 'New Report Section'
        console.log('Attempting to insert report section with ID:', sectionId)
        
        // Check if the extension is available
        const extensionExists = editor.extensionManager.extensions.find(ext => ext.name === 'reportSection')
        console.log('ReportSection extension exists:', !!extensionExists)
        
        // Use JSON node structure (same as toolbar implementation)
        const nodeContent = {
          type: 'reportSection',
          attrs: {
            id: sectionId,
            title: title,
            endpoint: '/api/reports/[ENTITY_ID]/[RUN_ID]/data',
            prompt: null,
          },
          content: [
            {
              type: 'heading',
              attrs: { level: 2 },
              content: [
                {
                  type: 'text',
                  text: title
                }
              ]
            }
          ]
        }
        
        console.log('Inserting node content:', nodeContent)
        
        const result = editor.chain().focus().insertContent(nodeContent).run()
        
        console.log('Insert result:', result)
        
        // Fallback to HTML if JSON didn't work
        if (!result) {
          console.log('JSON insertion failed, trying HTML approach...')
          const htmlContent = `<report-section id="${sectionId}" title="${title}" endpoint="/api/reports/[ENTITY_ID]/[RUN_ID]/data" data-type="reportSection"><h2>${title}</h2></report-section>`
          editor.chain().focus().insertContent(htmlContent).run()
        }
      } catch (error) {
        console.error('Error inserting report section:', error)
      }
    }
    setIsVisible(false)
  }, [editor, onInsertReportSection])

  const handleInsertReportSummary = useCallback(() => {
    const summaryId = `summary-${Date.now()}`
    const title = 'New Report Summary'
    
    try {
      const nodeContent = {
        type: 'reportSummary',
        attrs: {
          id: summaryId,
          title: title,
          prompt: null,
          summarize: null,
        },
        content: [
          {
            type: 'heading',
            attrs: { level: 2 },
            content: [
              {
                type: 'text',
                text: title || 'Report Summary'
              }
            ]
          }
        ]
      }
      
      editor.chain().focus().insertContent(nodeContent).run()
    } catch (error) {
      console.error('Error inserting report summary:', error)
      
      // Fallback to HTML insertion
      const content = `<report-summary id="${summaryId}" title="${title}"><h2>${title}</h2></report-summary>`
      editor.chain().focus().insertContent(content).run()
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertReportGroup = useCallback(() => {
    const groupId = `group-${Date.now()}`
    const title = 'New Report Group'
    
    try {
      const nodeContent = {
        type: 'reportGroup',
        attrs: {
          id: groupId,
          title: title,
        },
        content: [
          {
            type: 'heading',
            attrs: { level: 2 },
            content: [
              {
                type: 'text',
                text: title || 'Report Group'
              }
            ]
          }
        ]
      }
      
      editor.chain().focus().insertContent(nodeContent).run()
    } catch (error) {
      console.error('Error inserting report group:', error)
      
      // Fallback to HTML insertion
      const content = `<report-group id="${groupId}" title="${title}"><h2>${title}</h2></report-group>`
      editor.chain().focus().insertContent(content).run()
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertColumns = useCallback((layout: ColumnLayout) => {
    try {
      editor.chain().focus().insertColumns(layout).run()
    } catch (error) {
      console.error('Columns extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertTable = useCallback(() => {
    try {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
    } catch (error) {
      console.error('Table extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertReferences = useCallback(() => {
    try {
      editor.chain().focus().insertContent({
        type: 'references',
        attrs: { id: 'references' }
      }).run()
    } catch (error) {
      console.error('References extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertDetails = useCallback(() => {
    try {
      editor.chain().focus().setDetails().run()
    } catch (error) {
      console.error('Details extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertCodeBlock = useCallback(() => {
    try {
      editor.chain().focus().toggleCodeBlock().run()
    } catch (error) {
      console.error('Code block not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertBlockquote = useCallback(() => {
    try {
      editor.chain().focus().toggleBlockquote().run()
    } catch (error) {
      console.error('Blockquote not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertHorizontalRule = useCallback(() => {
    try {
      editor.chain().focus().setHorizontalRule().run()
    } catch (error) {
      console.error('Horizontal rule not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertImage = useCallback(() => {
    const url = window.prompt('Enter image URL:')
    if (url) {
      try {
        editor.chain().focus().setImage({ src: url }).run()
      } catch (error) {
        console.error('Image extension not available:', error)
      }
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertMath = useCallback(() => {
    try {
      editor.chain().focus().setInlineMath('').run()
    } catch (error) {
      console.error('Math extension not available:', error)
    }
    setIsVisible(false)
  }, [editor])

  const handleInsertTableOfContents = useCallback(() => {
    if (onInsertTableOfContents) {
      onInsertTableOfContents()
    } else {
      // Default TOC insertion - would need a specific extension
      console.log('Table of Contents insertion requested')
    }
    setIsVisible(false)
  }, [onInsertTableOfContents])

  const handleAISuggestion = useCallback(() => {
    if (onAISuggestion) {
      onAISuggestion()
    } else {
      // Default AI suggestion behavior
      console.log('AI suggestion requested')
    }
    setIsVisible(false)
  }, [onAISuggestion])

  // Build the menu items based on context
  const menuItems: MenuItem[] = useMemo(() => {
    const items: MenuItem[] = []

    // Basic clipboard operations
    if (isTextSelected) {
      items.push({
        id: 'copy',
        label: 'Copy',
        icon: Copy,
        action: handleCopy,
      })
      items.push({
        id: 'cut',
        label: 'Cut',
        icon: Scissors,
        action: handleCut,
      })
    }

    if (canPaste) {
      items.push({
        id: 'paste',
        label: 'Paste',
        icon: Clipboard,
        action: handlePaste,
      })
    }

    if (items.length > 0) {
      items.push({
        id: 'separator1',
        label: '',
        icon: Type,
        action: () => {},
        separator: true,
      })
    }

    items.push({
      id: 'selectAll',
      label: 'Select All',
      icon: Type,
      action: handleSelectAll,
    })

    // Text formatting (only show if text is selected)
    if (isTextSelected) {
      items.push({
        id: 'separator2',
        label: '',
        icon: Type,
        action: () => {},
        separator: true,
      })

      items.push({
        id: 'formatting',
        label: 'Format',
        icon: Type,
        action: () => {},
        submenu: [
          {
            id: 'heading1',
            label: 'Heading 1',
            icon: Heading1,
            action: () => handleHeading(1),
          },
          {
            id: 'heading2',
            label: 'Heading 2',
            icon: Heading2,
            action: () => handleHeading(2),
          },
          {
            id: 'heading3',
            label: 'Heading 3',
            icon: Heading3,
            action: () => handleHeading(3),
          },
          {
            id: 'bulletList',
            label: 'Bullet List',
            icon: List,
            action: () => handleList(false),
          },
          {
            id: 'orderedList',
            label: 'Numbered List',
            icon: ListOrdered,
            action: () => handleList(true),
          },
        ],
      })

      items.push({
        id: 'addLink',
        label: 'Add Link',
        icon: Link,
        action: handleAddLink,
      })

      if (onAddComment) {
        items.push({
          id: 'addComment',
          label: 'Add Comment',
          icon: MessageSquare,
          action: handleAddComment,
        })
      }
    }

    // Insert menu (always available)
    items.push({
      id: 'separator3',
      label: '',
      icon: Type,
      action: () => {},
      separator: true,
    })

    items.push({
      id: 'insert',
      label: 'Insert',
      icon: MoreHorizontal,
      action: () => {},
      submenu: [
        {
          id: 'insertTOC',
          label: 'Table of Contents',
          icon: BookOpen,
          action: handleInsertReferences,
        },
        {
          id: 'insertReportSummary',
          label: 'Report Summary',
          icon: FileText,
          action: handleInsertReportSummary,
        },
        {
          id: 'insertReportSection',
          label: 'Report Section',
          icon: Layers,
          action: handleInsertReportSection,
        },
        {
          id: 'insertReportGroup',
          label: 'Report Group',
          icon: Users,
          action: handleInsertReportGroup,
        },
        {
          id: 'insertColumns',
          label: 'Multi-Columns',
          icon: Columns,
          action: () => {},
          submenu: [
            {
              id: 'columns2Equal',
              label: '2 Columns (Equal)',
              icon: Columns,
              action: () => handleInsertColumns('equal-2'),
            },
            {
              id: 'columns3Equal',
              label: '3 Columns (Equal)',
              icon: Columns,
              action: () => handleInsertColumns('equal-3'),
            },
            {
              id: 'columns4Equal',
              label: '4 Columns (Equal)',
              icon: Columns,
              action: () => handleInsertColumns('equal-4'),
            },
            {
              id: 'columns1-2',
              label: '2 Columns (1:2 Ratio)',
              icon: Columns,
              action: () => handleInsertColumns('ratio-1-2'),
            },
            {
              id: 'columns2-1',
              label: '2 Columns (2:1 Ratio)',
              icon: Columns,
              action: () => handleInsertColumns('ratio-2-1'),
            },
            {
              id: 'columnsCentered',
              label: 'Centered Column',
              icon: AlignLeft,
              action: () => handleInsertColumns('centered'),
            },
          ],
        },
        {
          id: 'insertTable',
          label: 'Table',
          icon: Table,
          action: handleInsertTable,
        },
        {
          id: 'insertChart',
          label: 'Chart',
          icon: BarChart3,
          action: handleInsertChart,
        },
        {
          id: 'separator4',
          label: '',
          icon: Type,
          action: () => {},
          separator: true,
        },
        {
          id: 'insertDetails',
          label: 'Collapsible Section',
          icon: FolderOpen,
          action: handleInsertDetails,
        },
        {
          id: 'insertCodeBlock',
          label: 'Code Block',
          icon: Code,
          action: handleInsertCodeBlock,
        },
        {
          id: 'insertBlockquote',
          label: 'Quote',
          icon: Quote,
          action: handleInsertBlockquote,
        },
        {
          id: 'insertMath',
          label: 'Math Expression',
          icon: Calculator,
          action: handleInsertMath,
        },
        {
          id: 'insertHorizontalRule',
          label: 'Horizontal Rule',
          icon: Minus,
          action: handleInsertHorizontalRule,
        },
        {
          id: 'insertImage',
          label: 'Image',
          icon: Image,
          action: handleInsertImage,
        },
      ],
    })

    // AI features
    if (onAISuggestion) {
      items.push({
        id: 'aiSuggestion',
        label: 'AI Suggestions',
        icon: Sparkles,
        action: handleAISuggestion,
      })
    }

    return items
  }, [
    isTextSelected,
    canPaste,
    handleCopy,
    handleCut,
    handlePaste,
    handleSelectAll,
    handleAddLink,
    handleAddComment,
    handleHeading,
    handleList,
    handleInsertChart,
    handleInsertReportSection,
    handleInsertReportSummary,
    handleInsertReportGroup,
    handleInsertColumns,
    handleInsertTable,
    handleInsertReferences,
    handleInsertDetails,
    handleInsertCodeBlock,
    handleInsertBlockquote,
    handleInsertHorizontalRule,
    handleInsertImage,
    handleInsertMath,
    handleInsertTableOfContents,
    handleAISuggestion,
    onAddComment,
    onAISuggestion,
  ])

  // Calculate adjusted position to keep menu within viewport
  useEffect(() => {
    if (!isVisible) return

    const menuWidth = 192 // min-w-48 = 12rem = 192px
    const menuHeight = 500 // Approximate max height
    const padding = 8

    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let adjustedX = position.x
    let adjustedY = position.y

    // Check right boundary
    if (adjustedX + menuWidth + padding > viewportWidth) {
      adjustedX = viewportWidth - menuWidth - padding
    }

    // Check bottom boundary
    if (adjustedY + menuHeight + padding > viewportHeight) {
      adjustedY = viewportHeight - menuHeight - padding
    }

    // Ensure menu doesn't go off the left or top
    adjustedX = Math.max(padding, adjustedX)
    adjustedY = Math.max(padding, adjustedY)

    setAdjustedPosition({ x: adjustedX, y: adjustedY })
  }, [position, isVisible])

  // Handle right-click events
  useEffect(() => {
    const handleContextMenu = (event: MouseEvent) => {
      // Only handle right-clicks within the editor
      const editorElement = editor.view.dom
      if (!editorElement.contains(event.target as Node)) {
        return
      }

      event.preventDefault()
      
      // Get the position in the editor where the right-click occurred
      const pos = editor.view.posAtCoords({ left: event.clientX, top: event.clientY })
      if (pos) {
        setContextMenuPosition(pos.pos)
      }

      setPosition({ x: event.clientX, y: event.clientY })
      setIsVisible(true)
    }

    const handleClick = () => {
      setIsVisible(false)
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsVisible(false)
      }
    }

    document.addEventListener('contextmenu', handleContextMenu)
    document.addEventListener('click', handleClick)
    document.addEventListener('keydown', handleEscape)

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu)
      document.removeEventListener('click', handleClick)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [editor])

  // Render the context menu
  const renderMenuItem = (item: MenuItem) => {
    if (item.separator) {
      return (
        <div key={item.id} className="h-px bg-gray-200 my-1" />
      )
    }

    const Icon = item.icon
    const hasSubmenu = item.submenu && item.submenu.length > 0

    return (
      <div key={item.id} className="relative group">
        <button
          data-testid={`context-menu-${item.id}`}
          onClick={hasSubmenu ? undefined : item.action}
          disabled={item.disabled}
          className={`
            w-full flex items-center gap-2 px-3 py-2 text-sm text-left
            hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed
            ${hasSubmenu ? 'cursor-default' : 'cursor-pointer'}
          `}
        >
          <Icon className="w-4 h-4 text-gray-600" />
          <span className="flex-1">{item.label}</span>
          {hasSubmenu && <ChevronRight className="w-4 h-4 text-gray-400" />}
        </button>

        {hasSubmenu && (
          <div
            data-testid={`context-submenu-${item.id}`}
            className="absolute left-full top-0 ml-1 hidden group-hover:block z-50"
          >
            <div className="bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-48">
              {item.submenu!.map(subItem => renderMenuItem(subItem))}
            </div>
          </div>
        )}
      </div>
    )
  }

  if (!isVisible || !editor || editor.isDestroyed) {
    return null
  }

  return createPortal(
    <div
      data-testid="right-click-context-menu"
      className="fixed bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-48 z-50"
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y,
      }}
      onContextMenu={(e) => e.preventDefault()}
    >
      {menuItems.map(item => renderMenuItem(item))}
    </div>,
    document.body
  )
}
