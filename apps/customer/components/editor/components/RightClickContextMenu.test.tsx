import React from 'react'
import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest'
import { act, cleanup, fireEvent, render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Editor } from '@tiptap/react'
import { RightClickContextMenu } from './RightClickContextMenu'

// Mock Buffer for Node.js compatibility
global.Buffer = Buffer

// Mock window.prompt
Object.defineProperty(window, 'prompt', {
  writable: true,
  value: vi.fn(),
})

// Mock document.execCommand
Object.defineProperty(document, 'execCommand', {
  writable: true,
  value: vi.fn(),
})

// Mock react-dom createPortal
vi.mock('react-dom', () => ({
  createPortal: vi.fn((children, container) => {
    // For testing, just render the children directly
    return children
  }),
}))

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  Copy: () => <span data-testid="copy-icon">📋</span>,
  Scissors: () => <span data-testid="scissors-icon">✂️</span>,
  Clipboard: () => <span data-testid="clipboard-icon">📎</span>,
  Link: () => <span data-testid="link-icon">🔗</span>,
  MessageSquare: () => <span data-testid="message-square-icon">💬</span>,
  Type: () => <span data-testid="type-icon">T</span>,
  Heading1: () => <span data-testid="heading1-icon">H1</span>,
  Heading2: () => <span data-testid="heading2-icon">H2</span>,
  Heading3: () => <span data-testid="heading3-icon">H3</span>,
  List: () => <span data-testid="list-icon">•</span>,
  ListOrdered: () => <span data-testid="list-ordered-icon">1.</span>,
  BarChart3: () => <span data-testid="bar-chart3-icon">📊</span>,
  FileText: () => <span data-testid="file-text-icon">📄</span>,
  BookOpen: () => <span data-testid="book-open-icon">📖</span>,
  Sparkles: () => <span data-testid="sparkles-icon">✨</span>,
  MoreHorizontal: () => <span data-testid="more-horizontal-icon">⋯</span>,
  ChevronRight: () => <span data-testid="chevron-right-icon">→</span>,
  Table: () => <span data-testid="table-icon">⊞</span>,
  Columns: () => <span data-testid="columns-icon">⧇</span>,
  Quote: () => <span data-testid="quote-icon">"</span>,
  Code: () => <span data-testid="code-icon">&lt;/&gt;</span>,
  Minus: () => <span data-testid="minus-icon">-</span>,
  Image: () => <span data-testid="image-icon">🖼</span>,
  Calculator: () => <span data-testid="calculator-icon">🧮</span>,
  FolderOpen: () => <span data-testid="folder-open-icon">📁</span>,
  Users: () => <span data-testid="users-icon">👥</span>,
  Layers: () => <span data-testid="layers-icon">⧉</span>,
  AlignLeft: () => <span data-testid="align-left-icon">⊣</span>,
}))

// Mock TipTap Editor with comprehensive Editor interface properties
const createMockEditor = (overrides = {}) => {
  const chainMethods = {
    focus: vi.fn().mockReturnThis(),
    selectAll: vi.fn().mockReturnThis(),
    extendMarkRange: vi.fn().mockReturnThis(),
    setLink: vi.fn().mockReturnThis(),
    toggleHeading: vi.fn().mockReturnThis(),
    toggleBulletList: vi.fn().mockReturnThis(),
    toggleOrderedList: vi.fn().mockReturnThis(),
    insertContent: vi.fn().mockReturnThis(),
    insertTable: vi.fn().mockReturnThis(),
    setImage: vi.fn().mockReturnThis(),
    setDetails: vi.fn().mockReturnThis(),
    toggleCodeBlock: vi.fn().mockReturnThis(),
    toggleBlockquote: vi.fn().mockReturnThis(),
    setHorizontalRule: vi.fn().mockReturnThis(),
    setInlineMath: vi.fn().mockReturnThis(),
    insertColumns: vi.fn().mockReturnThis(),
    run: vi.fn(() => true),
  };

  return {
    state: {
      selection: {
        empty: true,
        from: 0,
        to: 0,
      },
      doc: {
        textBetween: vi.fn(() => ''),
        content: { size: 100 },
      },
      schema: {},
    },
    view: {
      dom: document.createElement('div'),
      posAtCoords: vi.fn(() => ({ pos: 10 })),
    },
    extensionManager: {
      extensions: [],
    },
    commands: {
      focus: vi.fn().mockReturnThis(),
      selectAll: vi.fn().mockReturnThis(),
      extendMarkRange: vi.fn().mockReturnThis(),
      setLink: vi.fn().mockReturnThis(),
      toggleHeading: vi.fn().mockReturnThis(),
      toggleBulletList: vi.fn().mockReturnThis(),
      toggleOrderedList: vi.fn().mockReturnThis(),
      insertContent: vi.fn().mockReturnThis(),
      insertTable: vi.fn().mockReturnThis(),
      setImage: vi.fn().mockReturnThis(),
      setDetails: vi.fn().mockReturnThis(),
      toggleCodeBlock: vi.fn().mockReturnThis(),
      toggleBlockquote: vi.fn().mockReturnThis(),
      setHorizontalRule: vi.fn().mockReturnThis(),
      setInlineMath: vi.fn().mockReturnThis(),
      insertColumns: vi.fn().mockReturnThis(),
    },
    chain: vi.fn(() => chainMethods),
    isDestroyed: false,
    commandManager: {
      commands: {},
      hasCommand: vi.fn(() => false),
    },
    css: vi.fn(),
    schema: {},
    isFocused: false,
    isEditable: true,
    isEmpty: false,
    options: {},
    storage: {},
    getHTML: vi.fn(() => '<p></p>'),
    getJSON: vi.fn(() => ({ type: 'doc', content: [] })),
    getText: vi.fn(() => ''),
    setContent: vi.fn(),
    clearContent: vi.fn(),
    insertContent: vi.fn(),
    isActive: vi.fn(() => false),
    can: vi.fn(() => ({ undo: () => true, redo: () => true })),
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    setEditable: vi.fn(),
    setOptions: vi.fn(),
    destroy: vi.fn(),
    createExtensionManager: vi.fn(),
    registerPlugin: vi.fn(),
    unregisterPlugin: vi.fn(),
    createNodeViews: vi.fn(),
    captureTransaction: vi.fn(),
    dispatchTransaction: vi.fn(),
    getAttributes: vi.fn(() => ({})),
    getCharacterCount: vi.fn(() => 0),
    injectCSS: vi.fn(),
    removeAllListeners: vi.fn(),
    isInitialized: true,
    extensionStorage: {},
    createCommandManager: vi.fn(),
    createSchema: vi.fn(),
    ...overrides,
  } as unknown as Editor;
}

describe('RightClickContextMenu Component', () => {
  let mockEditor: any
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    mockEditor = createMockEditor()
    user = userEvent.setup()
    vi.clearAllMocks()
    window.prompt = vi.fn()
    document.execCommand = vi.fn()
    
    // Ensure document.body exists for portal
    if (!document.body) {
      document.body = document.createElement('body')
    }
  })

  afterEach(() => {
    cleanup()
  })

  describe('Basic Rendering', () => {
    test('renders with minimal required props', () => {
      render(<RightClickContextMenu editor={mockEditor} />)
      
      // Should not render menu initially (not visible)
      expect(screen.queryByTestId('right-click-context-menu')).not.toBeInTheDocument()
    })

    test('does not render when editor is destroyed', () => {
      const destroyedEditor = createMockEditor({ isDestroyed: true })
      render(<RightClickContextMenu editor={destroyedEditor} />)
      
      expect(screen.queryByTestId('right-click-context-menu')).not.toBeInTheDocument()
    })

    test('does not render when editor is null', () => {
      // The component will try to access editor.state.selection.empty, so we need a mock that handles this
      const nullishEditor = {
        state: { selection: { empty: true } },
        view: { dom: null },
        isDestroyed: false,
      }
      
      render(<RightClickContextMenu editor={nullishEditor as any} />)
      
      expect(screen.queryByTestId('right-click-context-menu')).not.toBeInTheDocument()
    })
  })

  describe('Context Menu Visibility', () => {
    test('shows context menu on right-click within editor', async () => {
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      // Mock the event target to be within the editor
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('right-click-context-menu')).toBeInTheDocument()
    })

    test('hides context menu on regular click', async () => {
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      // First show the menu
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('right-click-context-menu')).toBeInTheDocument()
      
      // Then hide it with a click
      act(() => {
        document.dispatchEvent(new MouseEvent('click', { bubbles: true }))
      })

      expect(screen.queryByTestId('right-click-context-menu')).not.toBeInTheDocument()
    })

    test('hides context menu on Escape key', async () => {
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      // First show the menu
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('right-click-context-menu')).toBeInTheDocument()
      
      // Then hide it with Escape
      act(() => {
        document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape' }))
      })

      expect(screen.queryByTestId('right-click-context-menu')).not.toBeInTheDocument()
    })
  })

  describe('Clipboard Operations', () => {
    test('shows copy and cut options when text is selected', () => {
      const editorWithSelection = createMockEditor({
        state: {
          selection: {
            empty: false,
            from: 0,
            to: 5,
          },
        },
        view: {
          dom: document.createElement('div'),
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithSelection} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorWithSelection.view.dom,
        enumerable: true,
      })
      
      editorWithSelection.view.dom.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-copy')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-cut')).toBeInTheDocument()
    })

    test('calls document.execCommand when copy is clicked', async () => {
      const editorWithSelection = createMockEditor({
        state: {
          selection: {
            empty: false,
            from: 0,
            to: 5,
          },
        },
        view: {
          dom: document.createElement('div'),
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithSelection} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorWithSelection.view.dom,
        enumerable: true,
      })
      
      editorWithSelection.view.dom.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-copy')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-copy'))
      
      expect(document.execCommand).toHaveBeenCalledWith('copy')
    })

    test('calls document.execCommand when cut is clicked', async () => {
      const editorWithSelection = createMockEditor({
        state: {
          selection: {
            empty: false,
            from: 0,
            to: 5,
          },
        },
        view: {
          dom: document.createElement('div'),
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithSelection} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorWithSelection.view.dom,
        enumerable: true,
      })
      
      editorWithSelection.view.dom.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-cut')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-cut'))
      
      expect(document.execCommand).toHaveBeenCalledWith('cut')
    })

    test('shows paste option and calls execCommand when clicked', async () => {
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-paste')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-paste'))
      
      expect(document.execCommand).toHaveBeenCalledWith('paste')
    })
  })

  describe('Select All Functionality', () => {
    test('calls editor selectAll when select all is clicked', async () => {
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-selectAll')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-selectAll'))
      
      expect(editorWithDOM.chain).toHaveBeenCalled()
    })
  })

  describe('Text Formatting', () => {
    test('shows formatting submenu when text is selected', () => {
      const editorWithSelection = createMockEditor({
        state: {
          selection: {
            empty: false,
            from: 0,
            to: 5,
          },
        },
        view: {
          dom: document.createElement('div'),
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithSelection} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorWithSelection.view.dom,
        enumerable: true,
      })
      
      editorWithSelection.view.dom.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-formatting')).toBeInTheDocument()
      expect(screen.getByTestId('context-submenu-formatting')).toBeInTheDocument()
    })

    test('calls editor toggleHeading when heading options are clicked', async () => {
      const editorWithSelection = createMockEditor({
        state: {
          selection: {
            empty: false,
            from: 0,
            to: 5,
          },
        },
        view: {
          dom: document.createElement('div'),
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithSelection} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorWithSelection.view.dom,
        enumerable: true,
      })
      
      editorWithSelection.view.dom.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-heading1')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-heading1'))
      
      expect(editorWithSelection.chain).toHaveBeenCalled()
    })
  })

  describe('Insert Menu', () => {
    test('shows insert submenu with various options', () => {
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-insert')).toBeInTheDocument()
      expect(screen.getByTestId('context-submenu-insert')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-insertChart')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-insertTable')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-insertReportSection')).toBeInTheDocument()
    })

    test('calls editor insertTable when table option is clicked', async () => {
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-insertTable')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-insertTable'))
      
      expect(editorWithDOM.chain).toHaveBeenCalled()
    })

    test('prompts for image URL when image option is clicked', async () => {
      const mockPrompt = vi.fn().mockReturnValue('https://example.com/image.png')
      window.prompt = mockPrompt
      
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-insertImage')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-insertImage'))
      
      expect(mockPrompt).toHaveBeenCalledWith('Enter image URL:')
      expect(editorWithDOM.chain).toHaveBeenCalled()
    })
  })

  describe('Callback Props', () => {
    test('calls onAddComment when add comment is clicked', async () => {
      const mockOnAddComment = vi.fn()
      const editorWithSelection = createMockEditor({
        state: {
          selection: {
            empty: false,
            from: 0,
            to: 5,
          },
        },
        view: {
          dom: document.createElement('div'),
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithSelection} onAddComment={mockOnAddComment} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorWithSelection.view.dom,
        enumerable: true,
      })
      
      editorWithSelection.view.dom.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-addComment')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-addComment'))
      
      expect(mockOnAddComment).toHaveBeenCalledWith(10)
    })

    test('calls onAISuggestion when AI suggestion is clicked', async () => {
      const mockOnAISuggestion = vi.fn()
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} onAISuggestion={mockOnAISuggestion} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-aiSuggestion')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-aiSuggestion'))
      
      expect(mockOnAISuggestion).toHaveBeenCalled()
    })

    test('calls onInsertChart when chart is inserted', async () => {
      const mockOnInsertChart = vi.fn()
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} onInsertChart={mockOnInsertChart} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-insertChart')).toBeInTheDocument()

      await user.click(screen.getByTestId('context-menu-insertChart'))
      
      expect(mockOnInsertChart).toHaveBeenCalled()
    })
  })

  describe('Menu Positioning', () => {
    test('adjusts menu position to stay within viewport', () => {
      // Mock window dimensions
      Object.defineProperty(window, 'innerWidth', { writable: true, configurable: true, value: 800 })
      Object.defineProperty(window, 'innerHeight', { writable: true, configurable: true, value: 600 })
      
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      // Right-click near the right edge of the viewport
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 750, // Near right edge
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      const menu = screen.getByTestId('right-click-context-menu')
      expect(menu).toBeInTheDocument()
      // Menu should be positioned to avoid going off-screen
      expect(menu.style.left).not.toBe('750px')
    })
  })

  describe('Error Handling', () => {
    test('handles editor command failures gracefully', () => {
      const editorWithFailingCommands = createMockEditor({
        chain: vi.fn(() => ({
          focus: vi.fn().mockReturnThis(),
          selectAll: vi.fn().mockReturnThis(),
          run: vi.fn(() => false), // Return false to indicate failure instead of throwing
        })),
        view: {
          dom: document.createElement('div'),
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithFailingCommands} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorWithFailingCommands.view.dom,
        enumerable: true,
      })
      
      editorWithFailingCommands.view.dom.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-selectAll')).toBeInTheDocument()
      
      // Try to click the selectAll button - it should handle the error gracefully
      act(() => {
        fireEvent.click(screen.getByTestId('context-menu-selectAll'))
      })
      
      // Verify the command was attempted
      expect(editorWithFailingCommands.chain).toHaveBeenCalled()
    })

    test('handles missing extensions gracefully', async () => {
      const editorWithoutExtensions = createMockEditor({
        extensionManager: {
          extensions: [],
        },
        view: {
          dom: document.createElement('div'),
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithoutExtensions} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorWithoutExtensions.view.dom,
        enumerable: true,
      })
      
      editorWithoutExtensions.view.dom.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('context-menu-insertChart')).toBeInTheDocument()
      
      // Should be able to click chart option without crashing
      await user.click(screen.getByTestId('context-menu-insertChart'))
      
      expect(editorWithoutExtensions.chain).toHaveBeenCalled()
    })
  })

  describe('Accessibility', () => {
    test('prevents default context menu on the context menu itself', () => {
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('right-click-context-menu')).toBeInTheDocument()
      
      const menu = screen.getByTestId('right-click-context-menu')
      const contextMenuEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        cancelable: true,
      })
      
      const preventDefaultSpy = vi.spyOn(contextMenuEvent, 'preventDefault')

      act(() => {
        fireEvent(menu, contextMenuEvent)
      })
      
      expect(preventDefaultSpy).toHaveBeenCalled()
    })

    test('provides proper test IDs for all menu items', () => {
      const editorContainer = document.createElement('div')
      const editorWithDOM = createMockEditor({
        state: {
          selection: {
            empty: false,
            from: 0,
            to: 5,
          },
        },
        view: {
          dom: editorContainer,
          posAtCoords: vi.fn(() => ({ pos: 10 })),
        },
      })

      render(<RightClickContextMenu editor={editorWithDOM} onAddComment={vi.fn()} onAISuggestion={vi.fn()} />)
      
      const rightClickEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        clientX: 100,
        clientY: 100,
      })
      
      Object.defineProperty(rightClickEvent, 'target', {
        value: editorContainer,
        enumerable: true,
      })
      
      editorContainer.contains = vi.fn(() => true)

      act(() => {
        document.dispatchEvent(rightClickEvent)
      })

      expect(screen.getByTestId('right-click-context-menu')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-copy')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-cut')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-paste')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-selectAll')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-formatting')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-addLink')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-addComment')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-insert')).toBeInTheDocument()
      expect(screen.getByTestId('context-menu-aiSuggestion')).toBeInTheDocument()
    })
  })
})
