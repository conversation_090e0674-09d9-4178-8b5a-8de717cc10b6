/**
 * # ESG Application Changelog Display Component - Real-Time Update Feed & User Notifications
 *
 * This React component provides a comprehensive changelog display system for the ESG analytics
 * platform, featuring real-time updates, user notification management, and interactive engagement
 * tracking. The component serves as the primary interface for communicating application updates,
 * bug fixes, new features, and improvements to users through an elegant glass-morphism card design
 * with expand/collapse functionality and personalized read tracking.
 *
 * ## Core Functionality
 * - **Real-Time Updates**: Displays changelog entries with live database synchronization via Supabase
 * - **User Read Tracking**: Persistent localStorage-based system for tracking viewed entries per user
 * - **Interactive Engagement**: Click-to-mark-read functionality with visual feedback and keyboard accessibility
 * - **Expandable Content**: Smart truncation showing 5 entries by default with expandable view for complete history
 * - **Loading States**: Skeleton loading animation during data fetching for improved user experience
 * - **Feature Flag Integration**: Conditional rendering based on `welcome.changelog` feature flag
 *
 * ## ESG Platform Integration
 * **Changelog Data Source**: Integrates with `app_changelog` Supabase table containing:
 * - **Entry Types**: 'fix', 'feature', 'improvement' classifications with corresponding icons and badge colors
 * - **Temporal Filtering**: Displays last 30 days of changelog entries, limited to 20 most recent
 * - **Real-Time Sync**: Live updates through Supabase postgres_changes subscription
 * - **Toast Notifications**: Automatic notifications for new changelog entries during active sessions
 *
 * ## Component Architecture
 * **Props Interface**: Minimal design focused on styling flexibility:
 * - **className**: Optional string for additional CSS classes and styling customization
 *
 * ## Data Management & State
 * **useChangelog Hook Integration**: Leverages custom hook for comprehensive changelog management:
 * - **entries**: Array of ChangelogEntry objects from database with type, date, description
 * - **loading**: Boolean state for initial data fetching and skeleton display
 * - **error**: Error handling for failed API calls and network issues
 * - **lastSeenId**: localStorage-persisted user tracking for unread entry highlighting
 * - **markAsRead**: Function for updating user's last seen entry with persistent storage
 * - **Real-Time Subscription**: Live database change detection with toast notification system
 *
 * ## UI/UX Design Integration
 * **Glass-Morphism Card System**: Implements platform's signature translucent design:
 * - **Card Container**: `glass-card` class providing frosted glass effect with backdrop blur
 * - **Entry Highlighting**: Blue-tinted background (`border-blue-200 bg-blue-50/50`) for unread entries
 * - **Visual Indicators**: Small blue dot indicator for new entries, prominent but non-intrusive
 * - **Responsive Layout**: Flexible spacing and typography optimized for various screen sizes
 * - **Hover States**: Subtle interactive feedback on clickable entries for improved usability
 *
 * ## Interactive Features & Accessibility
 * **Entry Interaction System**: Comprehensive click and keyboard navigation:
 * - **Click Handling**: Click anywhere on entry to mark as read with visual state update
 * - **Keyboard Support**: Enter and Space key support for accessibility compliance
 * - **Focus Management**: Proper tabIndex and focus indicators for screen readers
 * - **Expand/Collapse**: Toggle button showing entry count with ChevronUp/ChevronDown icons
 *
 * ## Entry Type Classification & Visual Design
 * **Type-Based Icon & Badge System**: Semantic visual classification for changelog entries:
 * - **Fix Entries**: CheckCircle icon with 'destructive' red badge variant for bug fixes
 * - **Feature Entries**: Zap icon with 'default' blue badge variant for new functionality
 * - **Improvement Entries**: AlertCircle icon with 'secondary' gray badge variant for enhancements
 * - **Default Entries**: Calendar icon with 'outline' badge variant for miscellaneous updates
 *
 * ## Date Formatting & Temporal Display
 * **date-fns Integration**: Consistent date formatting using `format(new Date(entry.date), 'MMM d, yyyy')`:
 * - **Readable Format**: Human-friendly date display (e.g., "Jul 24, 2025")
 * - **Timezone Handling**: Proper UTC date parsing and local timezone display
 * - **Consistency**: Uniform date formatting across all changelog entries
 *
 * ## Loading States & Error Handling
 * **Skeleton Loading Animation**: Professional loading experience during data fetch:
 * - **Animated Placeholders**: 3 skeleton entries with pulsing gray rectangles
 * - **Realistic Sizing**: Width variations (3/4 and 1/2) mimicking actual content structure
 * - **Loading Card**: Same glass-card styling maintained during loading state
 * - **Error Handling**: Graceful degradation for network failures and empty states
 *
 * ## Performance Optimization
 * - **Smart Truncation**: Default 5-entry display reduces initial render complexity
 * - **Conditional Rendering**: Feature flag prevents unnecessary rendering when disabled
 * - **Efficient Re-renders**: useState for expand state prevents unnecessary changelog hook calls
 * - **Debounced Updates**: Real-time subscription properly debounces rapid database changes
 *
 * @see {@link https://lucide.dev/ | Lucide React Icons} - Icon library for changelog entry type indicators
 * @see {@link https://date-fns.org/ | date-fns} - Date formatting and manipulation utilities
 * @see {@link https://ui.shadcn.com/ | shadcn/ui} - UI component library for cards, badges, and buttons
 * @see {@link https://supabase.com/ | Supabase} - Real-time database and authentication backend
 *
 * <AUTHOR>
 * @updated 2025-07-24
 * @docgen doc-by-claude - Comprehensive documentation generated through systematic analysis
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AlertCircle, Calendar, CheckCircle, ChevronDown, ChevronUp, Zap } from 'lucide-react'
import { format } from 'date-fns'
import { FeatureFlag } from '@/components/feature-flag'
import { useChangelog } from '@/hooks/use-changelog'
import { useState } from 'react'

/**
 * Props interface for ChangelogCard component configuration
 *
 * @interface ChangelogCardProps
 */
interface ChangelogCardProps {
  /** Optional CSS class string for additional styling customization */
  className?: string
}

/**
 * Determines the appropriate Lucide React icon component for a changelog entry type
 *
 * Maps changelog entry types to their corresponding visual icons to provide immediate
 * visual context for users about the nature of each update. Uses semantic icon selection
 * where CheckCircle represents completed fixes, Zap represents new features, AlertCircle
 * represents improvements, and Calendar serves as the fallback for unrecognized types.
 *
 * @param {string} type - The changelog entry type ('fix', 'feature', 'improvement', or other)
 * @returns {JSX.Element} Lucide React icon component with standardized 4x4 sizing
 *
 * @example
 * ```tsx
 * const fixIcon = getTypeIcon('fix') // Returns <CheckCircle className="h-4 w-4" />
 * const featureIcon = getTypeIcon('feature') // Returns <Zap className="h-4 w-4" />
 * ```
 */
const getTypeIcon = (type: string) => {
  switch (type) {
    case 'fix':
      return <CheckCircle className="h-4 w-4" />
    case 'feature':
      return <Zap className="h-4 w-4" />
    case 'improvement':
      return <AlertCircle className="h-4 w-4" />
    default:
      return <Calendar className="h-4 w-4" />
  }
}

/**
 * Determines the appropriate shadcn/ui Badge variant for a changelog entry type
 *
 * Maps changelog entry types to their corresponding badge color variants to provide
 * consistent visual categorization across the interface. Uses semantic color coding
 * where destructive (red) indicates critical fixes, default (blue) indicates new features,
 * secondary (gray) indicates improvements, and outline serves as a neutral fallback.
 *
 * @param {string} type - The changelog entry type ('fix', 'feature', 'improvement', or other)
 * @returns {string} Badge variant string for shadcn/ui Badge component styling
 *
 * @example
 * ```tsx
 * const fixVariant = getTypeBadgeVariant('fix') // Returns 'destructive'
 * const featureVariant = getTypeBadgeVariant('feature') // Returns 'default'
 * ```
 */
const getTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'fix':
      return 'destructive'
    case 'feature':
      return 'default'
    case 'improvement':
      return 'secondary'
    default:
      return 'outline'
  }
}

/**
 * Main ChangelogCard component for displaying application updates and changes
 *
 * Renders a comprehensive changelog interface featuring real-time updates, user read tracking,
 * and interactive expand/collapse functionality. The component integrates with the useChangelog
 * hook to fetch and manage changelog data from Supabase, provides skeleton loading states,
 * and implements feature flag conditional rendering. Supports both mouse and keyboard interaction
 * for accessibility compliance.
 *
 * @param {ChangelogCardProps} props - Component configuration object
 * @param {string} [props.className] - Optional CSS classes for styling customization
 * @returns {JSX.Element} Complete changelog card component with real-time updates
 *
 * @example
 * ```tsx
 * // Basic usage with default styling
 * <ChangelogCard />
 *
 * // With custom CSS classes
 * <ChangelogCard className="mb-6 shadow-lg" />
 * ```
 */
export function ChangelogCard({ className }: ChangelogCardProps) {
  const { entries, loading, error, lastSeenId, markAsRead } = useChangelog()
  const [isExpanded, setIsExpanded] = useState(false)

  const displayedEntries = isExpanded ? entries : entries.slice(0, 5)
  const hasMoreEntries = entries.length > 5

  if (loading) {
    return (
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Changelog
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <FeatureFlag flag="welcome.changelog">
      <Card className={`glass-card ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Changelog
          </CardTitle>
        </CardHeader>
        <CardContent>
          {entries.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No recent changes to display.
            </p>
          ) : (
            <>
              <div className="space-y-3">
                {displayedEntries.map((entry) => {
                  const isNew = lastSeenId === null || entry.id > lastSeenId

                  return (
                    <div
                      key={entry.id}
                      className={`p-3 rounded-lg border transition-colors ${
                        isNew
                          ? 'border-blue-200 bg-blue-50/50'
                          : 'border-gray-200 bg-white/50'
                      }`}
                      role="button"
                      tabIndex={0}
                      onClick={() => markAsRead(entry.id)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          markAsRead(entry.id);
                        }
                      }}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getTypeIcon(entry.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge
                              variant={getTypeBadgeVariant(entry.type)}
                              className="text-xs"
                            >
                              {entry.type}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(entry.date), 'MMM d, yyyy')}
                            </span>
                            {isNew && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                          <p className="text-sm text-gray-900 leading-relaxed">
                            {entry.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>

              {hasMoreEntries && (
                <div className="flex justify-center pt-3 border-t border-gray-200/50 mt-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="text-sm text-muted-foreground hover:text-foreground"
                  >
                    {isExpanded ? (
                      <>
                        Show less
                        <ChevronUp className="ml-1 h-4 w-4" />
                      </>
                    ) : (
                      <>
                        Show more ({entries.length - 5} more)
                        <ChevronDown className="ml-1 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </FeatureFlag>
  )
}
