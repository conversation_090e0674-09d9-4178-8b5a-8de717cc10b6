import { render, screen, waitFor } from '@testing-library/react'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ChangelogCard } from './changelog-card'
import { createClient } from '@/app/supabase/client'
import { useToast } from '@/hooks/use-toast'

// Mock dependencies
vi.mock('@/components/context/auth/auth-context')
vi.mock('@/app/supabase/client')
vi.mock('@/hooks/use-toast')
vi.mock('@/components/feature-flag', () => ({
  FeatureFlag: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}))

const mockSupabase = {
  from: vi.fn(),
  channel: vi.fn(),
  removeChannel: vi.fn(),
}

const mockToast = vi.fn()

describe('ChangelogCard', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock createClient
    vi.mocked(createClient).mockReturnValue(mockSupabase as any)

    // Mock useToast
    vi.mocked(useToast).mockReturnValue({
      toast: mockToast,
      dismiss: vi.fn(),
      toasts: [],
    })

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('renders changelog card with loading state', async () => {
    // Mock Supabase query to return a promise that doesn't resolve immediately
    const mockQuery = {
      select: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnValue(new Promise(() => {
      })), // Never resolves
    }

    mockSupabase.from.mockReturnValue(mockQuery)
    mockSupabase.channel.mockReturnValue({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
    })

    render(<ChangelogCard />)

    expect(screen.getByText('Changelog')).toBeInTheDocument()
    expect(screen.getAllByText('Changelog')).toHaveLength(1)

    // Should show loading skeleton
    expect(document.querySelectorAll('.animate-pulse')).toHaveLength(3)
  })

  it('renders changelog entries when data is loaded', async () => {
    const mockEntries = [
      {
        id: 1,
        date: '2025-07-18',
        type: 'feature',
        description: 'Added new changelog feature',
        created_at: '2025-07-18T10:00:00Z',
        updated_at: '2025-07-18T10:00:00Z',
      },
      {
        id: 2,
        date: '2025-07-17',
        type: 'fix',
        description: 'Fixed login issue',
        created_at: '2025-07-17T10:00:00Z',
        updated_at: '2025-07-17T10:00:00Z',
      },
    ]

    const mockQuery = {
      select: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue({ data: mockEntries, error: null }),
    }

    mockSupabase.from.mockReturnValue(mockQuery)
    mockSupabase.channel.mockReturnValue({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
    })

    render(<ChangelogCard />)

    await waitFor(() => {
      expect(screen.getByText('Added new changelog feature')).toBeInTheDocument()
      expect(screen.getByText('Fixed login issue')).toBeInTheDocument()
    })

    // Check that badges are rendered
    expect(screen.getByText('feature')).toBeInTheDocument()
    expect(screen.getByText('fix')).toBeInTheDocument()

    // Should not show expand button with only 2 entries
    expect(screen.queryByText('Show more')).not.toBeInTheDocument()
  })

  it('renders empty state when no entries exist', async () => {
    const mockQuery = {
      select: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue({ data: [], error: null }),
    }

    mockSupabase.from.mockReturnValue(mockQuery)
    mockSupabase.channel.mockReturnValue({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
    })

    render(<ChangelogCard />)

    await waitFor(() => {
      expect(screen.getByText('No recent changes to display.')).toBeInTheDocument()
    })
  })

  it('handles error state gracefully', async () => {
    const mockQuery = {
      select: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockRejectedValue(new Error('Database error')),
    }

    mockSupabase.from.mockReturnValue(mockQuery)
    mockSupabase.channel.mockReturnValue({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
    })

    render(<ChangelogCard />)

    await waitFor(() => {
      expect(screen.getByText('No recent changes to display.')).toBeInTheDocument()
    })
  })

  it('sets up real-time subscription for new entries', () => {
    const mockChannel = {
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
    }

    mockSupabase.channel.mockReturnValue(mockChannel)
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue({ data: [], error: null }),
    })

    render(<ChangelogCard />)

    expect(mockSupabase.channel).toHaveBeenCalledWith('changelog-changes')
    expect(mockChannel.on).toHaveBeenCalledWith(
      'postgres_changes',
      { event: 'INSERT', schema: 'public', table: 'app_changelog' },
      expect.any(Function),
    )
    expect(mockChannel.subscribe).toHaveBeenCalled()
  })

  it('shows expand/collapse functionality when more than 5 entries exist', async () => {
    // Create 7 mock entries
    const mockEntries = Array.from({ length: 7 }, (_, i) => ({
      id: i + 1,
      date: '2025-07-18',
      type: 'feature',
      description: `Feature ${i + 1}`,
      created_at: '2025-07-18T10:00:00Z',
      updated_at: '2025-07-18T10:00:00Z',
    }))

    const mockQuery = {
      select: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue({ data: mockEntries, error: null }),
    }

    mockSupabase.from.mockReturnValue(mockQuery)
    mockSupabase.channel.mockReturnValue({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
    })

    render(<ChangelogCard />)

    await waitFor(() => {
      // Should show first 5 entries
      expect(screen.getByText('Feature 1')).toBeInTheDocument()
      expect(screen.getByText('Feature 5')).toBeInTheDocument()
      // Should not show 6th and 7th entries initially
      expect(screen.queryByText('Feature 6')).not.toBeInTheDocument()
      expect(screen.queryByText('Feature 7')).not.toBeInTheDocument()
    })

    // Should show expand button
    expect(screen.getByText('Show more (2 more)')).toBeInTheDocument()
  })
})
