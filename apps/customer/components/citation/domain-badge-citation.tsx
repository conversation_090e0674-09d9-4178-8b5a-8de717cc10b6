/**
 * # Domain Badge Citation Component - Compact Citation Display System
 *
 * This React component provides a compact, badge-style citation display system for ESG document
 * references throughout the customer application. Inspired by ChatGPT's citation style, it renders
 * clean domain-based badges with rich tooltip information for source attribution and document
 * metadata display. Built with Next.js 15, React.memo optimization, and Shadcn UI components
 * for consistent glass-morphism styling and responsive design.
 *
 * ## Core Functionality
 * - **Compact Badge Display**: Clean domain-based badges with standardized styling and hover effects
 * - **Rich Tooltip Information**: Comprehensive metadata display including title, authors, year, and page numbers
 * - **Flexible Display Modes**: Toggle between badge format and simple superscript number display
 * - **Performance Optimized**: React.memo implementation prevents unnecessary re-renders
 * - **URL Processing**: Smart domain extraction and display name generation for clean presentation
 * - **Accessibility Ready**: Full tooltip provider integration and keyboard navigation support
 *
 * ## ESG Platform Integration
 * **Citation System Context**: Core component for ESG document attribution and source credibility:
 * - **Document Sources**: References ESG reports, sustainability documents, and corporate filings
 * - **Entity Attribution**: Links citations to specific companies and organizations via author metadata
 * - **Analytics Database**: Integrates with document processing pipeline from backoffice analytics system
 * - **Customer Database**: Displays citation data synchronized via xfer_ tables from analytics backend
 * - **Admin Features**: Conditional citation link behavior based on user admin status and permissions
 * - **Source Credibility**: Visual presentation of document reliability and source authority information
 *
 * ## Component Architecture
 * **Props Interface**: Comprehensive configuration for flexible citation display across the platform:
 * - **citation**: CitationType - Complete citation object with document metadata and attribution
 * - **admin**: boolean - User admin status controlling citation link behavior and access permissions
 * - **showYear**: boolean (optional, default: false) - Controls display of publication year in tooltip
 * - **display**: number (optional) - Alternative numeric display mode showing citation reference number
 *
 * **CitationType Structure**: Rich metadata object containing complete document information:
 * - **doc_page_id**: number - Unique identifier for specific document page reference
 * - **url**: string - Internal system URL for document page access and navigation
 * - **page**: number - Zero-indexed page number for precise document location
 * - **score**: number - Document relevance or quality score for citation ranking
 * - **title**: string - Document title or heading for primary identification
 * - **doc_id**: number - Document identifier linking to parent document record
 * - **public_url**: string - External URL for document access and domain extraction
 * - **credibility**: number - Source credibility score for reliability assessment
 * - **doc_name**: string - Document filename or alternative document identifier
 * - **year**: number (optional) - Publication year for temporal document context
 * - **authors**: AuthorType[] - Array of author objects with entity information and metadata
 *
 * ## Display Modes & Styling
 * **Badge Mode** (default): Clean domain-based badge with rounded corners and hover effects:
 * - Glass-morphism styling with translucent background and backdrop blur effects
 * - Hover state transitions with secondary color overlay for interactive feedback
 * - Focus ring support for keyboard navigation and accessibility compliance
 * - Responsive text sizing and padding for consistent appearance across devices
 *
 * **Numeric Mode** (when display prop provided): Minimal superscript-style reference number:
 * - Compact superscript positioning with text-xs sizing for minimal visual impact
 * - Clean typography integration within body text and content flows
 * - Maintains tooltip functionality while reducing visual prominence
 * - Optimal for dense text content with multiple citation references
 *
 * ## URL Processing & Domain Intelligence
 * **Smart Domain Extraction**: Advanced URL processing for clean domain presentation:
 * - **extractDomain()**: Removes protocols, www prefixes, and path components for clean display
 * - **getDomainDisplayName()**: Intelligent TLD removal and subdomain handling for friendly names
 * - **Multi-TLD Support**: Handles complex domains like co.uk, com.au for accurate processing
 * - **Fallback Protection**: Graceful error handling for malformed URLs and edge cases
 * - **Brand Recognition**: Clean domain names improve source recognition and credibility
 *
 * ## Tooltip System & User Experience
 * **Rich Metadata Display**: Comprehensive information presentation in accessible tooltip format:
 * - **Document Title**: Primary document identification with fallback to doc_name or domain
 * - **Publication Year**: Optional year display based on showYear prop configuration
 * - **Page Reference**: Clear page number indication for precise document location
 * - **Author Attribution**: Deduplicated author list with proper comma separation
 * - **Responsive Positioning**: Tooltip positioning adapts to viewport constraints and content flow
 * - **Background Styling**: Consistent glass-morphism background matching application design
 *
 * ## Performance & Optimization
 * **React.memo Implementation**: Optimized rendering prevents unnecessary component updates:
 * - **Shallow Comparison**: Default memo comparison for props change detection
 * - **Display Name**: Proper component naming for React DevTools debugging
 * - **Conditional Rendering**: Early return for invalid citations reduces processing overhead
 * - **URL Processing Cache**: Efficient domain extraction with minimal recalculation
 *
 * ## System Architecture & Data Flow
 * **ESG Document Processing Pipeline**: Integration with comprehensive document analysis workflow:
 * - **Analytics Backend**: Python-based document processing and citation extraction system
 * - **Data Synchronization**: Citation data flows from analytics database to customer database
 * - **Real-Time Updates**: Supabase integration enables live citation updates and modifications
 * - **Admin Features**: Administrative access controls for enhanced citation management capabilities
 * - **Link Generation**: Dynamic URL generation via citationLink utility for admin/user distinction
 *
 * ## Related Components & Integration
 * **Citation Ecosystem**: Part of comprehensive citation and reference management system:
 * - **Citation.tsx**: Primary citation component for detailed document references
 * - **CompactCitation**: Alternative compact citation format for different use cases
 * - **URL Utilities**: Shared domain processing functions for consistent behavior
 * - **Badge Component**: Shadcn UI badge with customizable variants and styling
 * - **Tooltip System**: Radix UI tooltip primitives for accessible information display
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating Next.js Link Component
 * @see https://ui.shadcn.com/docs/components/badge Shadcn UI Badge Component
 * @see https://ui.shadcn.com/docs/components/tooltip Shadcn UI Tooltip Component
 * @see https://react.dev/reference/react/memo React.memo Performance Optimization
 * @see {@link ../citation.tsx} Main Citation Component
 * @see {@link ../../utils/url-utils.ts} URL Processing Utilities
 * <AUTHOR>
 * @updated 2025-07-24
 * @description This component provides compact, badge-style citation display for ESG document references with rich tooltip metadata and flexible display modes.
 * @example ```tsx
 * // Basic badge citation display
 * <DomainBadgeCitation 
 *   citation={citationData} 
 *   admin={false} 
 * />
 *
 * // Numeric citation with year display
 * <DomainBadgeCitation 
 *   citation={citationData} 
 *   admin={true} 
 *   showYear={true}
 *   display={1}
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import React from 'react'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { extractDomain, getDomainDisplayName } from '@/utils/url-utils'
import { citationLink, CitationType } from '@/components/citation'

interface DomainBadgeCitationProps {
  citation: CitationType
  admin: boolean
  showYear?: boolean
  display?:number
}

/**
 * A compact badge-style citation that displays the domain name
 * Similar to the ChatGPT citation style
 */
export const DomainBadgeCitation = React.memo<DomainBadgeCitationProps>(({
                                                                           citation,
                                                                           admin,
                                                                           showYear = false,
                                                                           display,
                                                                         }) => {
  if (!citation || !citation.public_url) {
    return null
  }

  const domain = extractDomain(citation.public_url)
  const displayName = getDomainDisplayName(domain)
  const url = citationLink(citation, admin)
  const fullTitle = citation.title || citation.doc_name || domain
  const year = citation.year ? ` (${citation.year})` : ''

  let authors = citation.authors.map((author: any) => author.name)

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {display ? (<Link href={url} className="inline-flex align-super text-xs mr-1"><span
              className="">{display}</span></Link>) :
              (<Link href={url} className="inline-flex"><Badge
                variant="outline"
              className="inline-flex items-center rounded-full border px-1 py-0.5 text-xs font-medium transition-colors hover:bg-secondary/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer"
            >
                {displayName}
              </Badge></Link>)}
        </TooltipTrigger>
        <TooltipContent className="bg-background">
          <p className="text-xs">
            {fullTitle}{showYear ? year : ''} p.{citation.page}<br/>
            {citation.authors && (
              <span className="text-muted-foreground">
                {authors.filter((item, index) => authors.indexOf(item) === index).join(', ')}
              </span>
            )}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
})

DomainBadgeCitation.displayName = 'DomainBadgeCitation'
