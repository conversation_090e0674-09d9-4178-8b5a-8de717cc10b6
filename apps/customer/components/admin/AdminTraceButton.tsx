/**
 * # Admin Trace Button - ESG Flag Processing Trace Viewer
 *
 * This React client component provides administrative users with detailed debugging and analysis
 * capabilities for ESG (Environmental, Social, Governance) flag processing pipelines. The component
 * fetches and displays comprehensive trace data from the analytics backend, including processing
 * steps, LLM calls, performance metrics, and quality assessments. Built specifically for admin
 * users to debug, monitor, and analyze the ESG flag generation process.
 *
 * ## Core Functionality
 * - **Admin-Only Access**: Visible only to authenticated administrative users via role-based access control
 * - **Trace Data Fetching**: Retrieves processing trace JSON from customer database (`xfer_flags.trace_json`)
 * - **Modal Integration**: Launches comprehensive TraceModal for detailed trace data visualization
 * - **Glass-morphism UI**: Integrates with platform's signature glass-morphism design system
 * - **Loading States**: Provides visual feedback during trace data loading with spinning animation
 * - **Error Handling**: Displays error messages for failed trace data retrieval attempts
 *
 * ## ESG Flag Processing System Integration
 * **Analytics Pipeline Trace Data**: The component accesses trace data generated by the backend ESG analysis system:
 * - **Processing Steps**: Detailed breakdown of flag creation pipeline stages
 * - **LLM Integration**: Traces of AI model calls, token usage, and costs during flag analysis
 * - **Performance Metrics**: Processing duration, step-by-step timing, and bottleneck identification
 * - **Quality Metrics**: Confidence scores, data completeness, and processing error logging
 * - **Source Data**: Original statement data, entity information, and effect analysis inputs
 *
 * ## Database Integration
 * **Customer Database Schema**: Interfaces with Supabase PostgreSQL customer database:
 * - **Table**: `xfer_flags` - Synchronized ESG flag data from analytics database
 * - **Column**: `trace_json` - JSONB column containing complete processing trace information
 * - **Access Control**: Row Level Security (RLS) policies restrict access to authenticated users
 * - **Admin Verification**: Component respects admin role verification through auth context
 *
 * ## Component Architecture
 * **Props Interface**: Minimal props focused on flag identification and styling:
 * - **flagId**: Numeric identifier for the ESG flag in the database
 * - **flagTitle**: Human-readable flag title for display purposes
 * - **className**: Optional CSS classes for layout positioning and styling customization
 *
 * ## Authentication & Authorization
 * **Role-Based Access Control**: Implements proper admin-only access patterns:
 * - **Admin Check**: Uses `useAuth` hook to verify current user has administrative privileges
 * - **Null Rendering**: Returns null immediately if user lacks admin access
 * - **Security Layer**: Component-level security preventing non-admin access to trace data
 * - **Auth Context**: Integrates with platform's comprehensive authentication system
 *
 * ## UI/UX Design Integration
 * **Glass-morphism Button Design**: Follows platform design system standards:
 * - **Glass Effects**: Uses `glass-effect-strong` and `glass-effect-brand` for visual consistency
 * - **Hover States**: Smooth transitions and opacity changes on user interaction
 * - **Positioning**: Absolute positioning for overlay placement on flag cards
 * - **Icon System**: Lucide React `Activity` icon for trace data representation
 * - **Loading Animation**: CSS-based spinning loader for user feedback during data fetching
 *
 * ## Error Management
 * **Comprehensive Error Handling**: Robust error states and user feedback:
 * - **Database Errors**: Handles Supabase query failures with descriptive error messages
 * - **Missing Data**: Graceful handling when trace data is not available for a flag
 * - **Network Issues**: Catch-all error handling for network and unexpected failures
 * - **User Communication**: Error messages displayed in glass-morphism error overlay
 *
 * ## Performance Considerations
 * **Lazy Loading Pattern**: Trace data loaded only when user explicitly requests it:
 * - **On-Demand Fetching**: Database query executed only on button click
 * - **Modal Rendering**: TraceModal component rendered but remains hidden until needed
 * - **State Management**: Minimal state management for optimal performance
 * - **Query Optimization**: Single database query for complete trace data retrieval
 *
 * ## System Architecture
 * This component fits into the broader ESG analysis ecosystem:
 * - **Analytics Backend**: Python system generates comprehensive trace data during flag processing
 * - **Data Sync Layer**: `xfer_flags` table synchronizes trace data between analytics and customer databases
 * - **Admin Interface**: This component provides admin access to backend processing transparency
 * - **Quality Assurance**: Enables debugging of flag generation issues and performance optimization
 * - **Audit Trail**: Provides complete audit trail for ESG flag creation and analysis processes
 *
 * @see https://react.dev/reference/react/useState React useState Hook Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://lucide.dev/icons/activity Lucide Activity Icon Documentation
 * @see {@link ./TraceModal.tsx} TraceModal Component
 * @see {@link ../../context/auth/auth-context.tsx} Authentication Context
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Admin-only component for viewing ESG flag processing trace data with comprehensive debugging capabilities
 * @example ```tsx
 * <AdminTraceButton 
 *   flagId={12345} 
 *   flagTitle="Carbon Emissions Reduction Commitment"
 *   className="custom-positioning"
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import { useState } from 'react'
import { Activity } from 'lucide-react'
import { useAuth } from '@/components/context/auth/auth-context'
import { createClient } from '@/app/supabase/client'
import { cn } from '@utils/lib/utils'
import { TraceData, TraceModal } from './TraceModal'

export type { TraceData }

interface AdminTraceButtonProps {
  flagId: number
  flagTitle: string
  className?: string
}


export function AdminTraceButton({ flagId, flagTitle, className }: AdminTraceButtonProps) {
  const { admin } = useAuth()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [traceData, setTraceData] = useState<TraceData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  if (!admin) return null

  const handleTraceClick = async (e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()

    setIsLoading(true)
    setError(null)

    try {
      const supabase = createClient()

      // Get trace data directly from xfer_flags table
      const { data, error } = await supabase
        .from('xfer_flags')
        .select('trace_json')
        .eq('id', flagId)
        .single()

      if (error) {
        console.error('Error loading trace data:', error)
        setError('Failed to load trace data')
        return
      }

      if (data && (data as any).trace_json) {
        setTraceData((data as any).trace_json)
        setIsModalOpen(true)
      } else {
        setError('No trace data available for this flag')
      }
    } catch (err) {
      console.error('Error loading trace data:', err)
      setError('Failed to load trace data')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <button
        onClick={handleTraceClick}
        disabled={isLoading}
        type="button"
        data-testid="admin-trace-button"
        className={cn(
          "absolute top-2 right-12 z-20 p-1.5 rounded-lg pointer-events-auto",
          "glass-effect-strong backdrop-blur-md",
          "text-blue-400 hover:text-blue-300",
          "hover:glass-effect-brand transition-all duration-200",
          "shadow-md hover:shadow-lg",
          "border border-white/10 hover:border-blue-400/30",
          "opacity-0 group-hover:opacity-100",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          className
        )}
        title="View trace data"
      >
        {isLoading ? (
          <div className="animate-spin w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full" />
        ) : (
          <Activity className="w-4 h-4" />
        )}
      </button>

      {error && (
        <div className="absolute top-12 right-2 z-30 p-2 rounded-lg glass-effect-strong backdrop-blur-md border border-red-400/30 text-red-400 text-xs whitespace-nowrap">
          {error}
        </div>
      )}

      <TraceModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        traceData={traceData}
        flagTitle={flagTitle}
        flagId={flagId}
      />
    </>
  )
}
