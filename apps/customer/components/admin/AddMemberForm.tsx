/**
 * # Organization Member Management Form Component for EkoIntelligence ESG Platform
 *
 * This client-side React component provides administrators with a comprehensive interface for adding users to
 * organizations within the EkoIntelligence ESG analysis platform. It serves as a core administrative tool for
 * managing organization membership, user roles, and access control in the customer-facing application.
 *
 * ## Core Functionality
 * - **User Selection**: Interactive dropdown for selecting from available users with name and email display
 * - **Role Assignment**: Admin/member role selection interface for organization-level permissions
 * - **Database Integration**: Direct Supabase client integration for real-time profile updates
 * - **User Feedback**: Loading states, error handling, and toast notifications for optimal UX
 * - **Form Validation**: Client-side validation ensuring required fields are completed
 *
 * ## System Architecture Context
 * This component fits into the broader EkoIntelligence platform administrative system:
 * - **Admin Interface**: Part of the administrative dashboard for organization management
 * - **User Management**: Integrates with Supabase authentication and profile system
 * - **Database Layer**: Updates customer database `profiles` table with organization associations
 * - **Authorization Model**: Supports multi-tenant B2B SaaS organizational structure
 * - **Design System**: Uses shadcn/ui components with glass-morphism aesthetic
 *
 * ## Database Schema Integration
 * The component interacts with the customer database schema:
 * - **`profiles` Table**: Updates user organization association via `organisation` field
 * - **User Data Fields**: Leverages `id`, `full_name`, `email`, `avatar_url` for user selection
 * - **Organization Linkage**: Foreign key relationship to `acc_organisations` table
 * - **Row Level Security**: Operates within Supabase RLS policies for secure data access
 *
 * ## Props Interface
 * - **`organizationId`**: Target organization ID for member association (required)
 * - **`availableUsers`**: Array of user objects with profile information for selection
 *
 * ## State Management
 * - **`selectedUserId`**: Currently selected user for organization addition
 * - **`role`**: Selected role (member/admin) for the user being added
 * - **`loading`**: Form submission state to prevent double-submission and show feedback
 *
 * ## User Experience Features
 * - **Progressive Enhancement**: Proper loading states and disabled states during operations
 * - **Error Handling**: Comprehensive error catching with user-friendly toast notifications
 * - **Success Feedback**: Confirmation messages and form reset on successful operations
 * - **Accessibility**: Full ARIA support through Radix UI primitives and semantic HTML
 * - **Responsive Design**: Works seamlessly across mobile and desktop devices
 *
 * ## Security Considerations
 * - **Client-side Validation**: Basic validation prevents empty submissions
 * - **Row Level Security**: Relies on Supabase RLS policies for authorization
 * - **Type Safety**: TypeScript interfaces prevent type-related vulnerabilities
 * - **⚠️ CRITICAL ISSUE**: Role selection is collected but not persisted to database (requires immediate fix)
 *
 * ## Performance Characteristics
 * - **Lightweight Component**: Minimal state and efficient rendering
 * - **Direct Database Access**: Uses Supabase client for real-time updates
 * - **Page Refresh Pattern**: Uses router.refresh() for data consistency (could be optimized)
 * - **No Caching**: Direct database queries without optimization layer
 *
 * ## Known Issues & Technical Debt
 * 1. **CRITICAL**: Role field is collected in UI but not saved to database (security/functional bug)
 * 2. **Performance**: Uses router.refresh() causing full page re-renders instead of selective updates
 * 3. **Error Handling**: Generic error messages don't differentiate between error types
 * 4. **Scalability**: No search/filtering for large user lists in organizations
 *
 * ## Usage Patterns
 * - **Admin Dashboards**: Primary use in organization management interfaces
 * - **Bulk Operations**: Can be used for individual user additions (not bulk import)
 * - **Real-time Updates**: Immediate database updates with user feedback
 * - **Form Integration**: Standalone form component for embedding in admin layouts
 *
 * ## Related Components
 * - Button component from shadcn/ui with glass-morphism variants
 * - Select components built on Radix UI primitives for accessibility
 * - Label component for proper form accessibility
 * - Supabase client factory for database operations
 * - Toast notification system via Sonner for user feedback
 *
 * @see https://ui.shadcn.com/docs/components/select shadcn/ui Select Component Documentation
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase RLS Documentation
 * @see https://www.radix-ui.com/primitives/docs/components/select Radix UI Select Primitive
 * @see {@link ../../ui/button} shadcn/ui Button component with glass-morphism variants
 * @see {@link ../../ui/select} shadcn/ui Select component built on Radix UI
 * @see {@link ../../../app/supabase/client} Supabase client factory for database operations
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Administrative form component for adding users to organizations with role assignment in ESG analysis platform
 * @example ```tsx
 * import { AddMemberForm } from '@/components/admin/AddMemberForm'
 *
 * const availableUsers = [
 *   { id: 'user-1', full_name: 'John Doe', email: '<EMAIL>', avatar_url: null },
 *   { id: 'user-2', full_name: 'Jane Smith', email: '<EMAIL>', avatar_url: null }
 * ]
 *
 * <AddMemberForm 
 *   organizationId={123} 
 *   availableUsers={availableUsers} 
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { createClient } from '@/app/supabase/client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { UserPlus } from 'lucide-react'

interface AddMemberFormProps {
  organizationId: number
  availableUsers: Array<{
    id: string
    full_name: string | null
    email: string | null
    avatar_url: string | null
  }>
}

export function AddMemberForm({ organizationId, availableUsers }: AddMemberFormProps) {
  const [selectedUserId, setSelectedUserId] = useState<string>('')
  const [role, setRole] = useState<string>('member')
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedUserId) {
      toast.error('Please select a user')
      return
    }

    setLoading(true)
    
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          organisation: organizationId
        })
        .eq('id', selectedUserId)

      if (error) {
        throw error
      }

      toast.success('Member added successfully')
      setSelectedUserId('')
      setRole('member')
      router.refresh()
    } catch (error) {
      console.error('Error adding member:', error)
      toast.error('Failed to add member')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="user">Select User</Label>
        <Select value={selectedUserId} onValueChange={setSelectedUserId}>
          <SelectTrigger>
            <SelectValue placeholder="Choose a user..." />
          </SelectTrigger>
          <SelectContent>
            {availableUsers.map((user) => (
              <SelectItem key={user.id} value={user.id}>
                <div className="flex items-center space-x-2">
                  <span>{user.full_name || 'Unknown'}</span>
                  <span className="text-xs text-muted-foreground">
                    ({user.email})
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="role">Role</Label>
        <Select value={role} onValueChange={setRole}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="member">Member</SelectItem>
            <SelectItem value="admin">Admin</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Button type="submit" disabled={loading || !selectedUserId} className="w-full">
        <UserPlus className="h-4 w-4 mr-2" />
        {loading ? 'Adding...' : 'Add Member'}
      </Button>
    </form>
  )
}
