/**
 * # Administrative Delete Button Component for EkoIntelligence ESG Platform
 *
 * This critical administrative component provides secure, role-based deletion functionality for records
 * in the EkoIntelligence ESG analysis platform. Designed exclusively for admin users, it implements
 * comprehensive security through Supabase Row Level Security (RLS) policies while maintaining a
 * consistent glass-morphism design aesthetic with interactive hover states and confirmation dialogs.
 *
 * ## Core Functionality
 * - **Secure Deletion**: Admin-only deletion with RLS policy enforcement for maximum security
 * - **Interactive UI**: Glass-morphism styled button with hover effects and visual feedback
 * - **Confirmation Dialog**: Custom confirmation flow to prevent accidental deletions
 * - **Real-time Feedback**: Toast notifications for operation status and error handling
 * - **Visual Element Management**: Automatic hiding of deleted items with DOM manipulation
 * - **Type Safety**: Full TypeScript integration with proper error handling
 *
 * ## System Architecture Context
 * This component serves as a critical part of the EkoIntelligence platform's administrative interface:
 * - **ESG Data Management**: Enables deletion of analysis results, effect flags, and entity data
 * - **Admin Dashboard**: Integrates with administrative panels for data management workflows
 * - **Customer Application**: Part of the customer-facing app built on Next.js 15 App Router
 * - **Authentication System**: Relies on AuthContext for admin role verification
 * - **Database Layer**: Operates on customer database with automated analytics sync via xfer_ tables
 * - **Security Model**: Implements defense-in-depth through client auth + server RLS policies
 *
 * ## Database Schema Integration
 * The component integrates with the customer database schema and Row Level Security:
 * - **xfer_flags Table**: Primary target for effect flag deletions with "Admins can delete flags" policy
 * - **Analytics Sync**: Deletions in customer DB automatically sync to analytics database
 * - **RLS Enforcement**: PostgreSQL policies ensure only admin users can perform deletions
 * - **Audit Trail**: Database triggers can log deletion events for compliance tracking
 * - **Foreign Key Cascade**: Related records properly cleaned up through database constraints
 *
 * ## Props Interface
 * - **`tableName`**: Target database table name for deletion operation (required)
 * - **`recordId`**: Unique identifier of record to delete (required, number or string)
 * - **`recordType`**: Human-readable description of record type for user feedback (required)
 * - **`onDeleted`**: Optional callback function executed after successful deletion
 * - **`className`**: Additional CSS classes for component styling customization
 *
 * ## State Management
 * - **`isDeleting`**: Loading state preventing double-clicks and providing user feedback
 * - **`showConfirm`**: Controls visibility of custom confirmation dialog
 * - **Auth Context**: Consumes admin status from AuthProvider for role-based access control
 * - **Toast State**: Manages success/error notifications through custom toast hook
 *
 * ## Security Architecture
 * **Multi-Layer Security Implementation**:
 * - **Client-Side Authorization**: Admin role check prevents UI rendering for non-admin users
 * - **Server-Side Enforcement**: Supabase RLS policies provide ultimate security enforcement
 * - **Database Policies**: PostgreSQL RLS policies like "Admins can delete flags" ensure access control
 * - **Type Safety**: TypeScript prevents common security vulnerabilities through compile-time checks
 * - **Input Validation**: Props validation ensures proper data types and required fields
 * - **Error Handling**: Comprehensive error catching prevents information disclosure
 *
 * ## User Experience Features
 * **Glass-Morphism Design Pattern**:
 * - **Visual Consistency**: Matches platform's translucent, rounded design aesthetic
 * - **Interactive States**: Hover effects with subtle transformations for better UX
 * - **Loading Feedback**: Button disabled state and loading text during operations
 * - **Custom Confirmation**: Two-step confirmation process with Yes/No buttons
 * - **Toast Notifications**: Success/error feedback using consistent notification system
 * - **Responsive Design**: Works seamlessly across mobile and desktop devices
 * - **Event Propagation**: Proper event handling to prevent unintended interactions
 *
 * ## Performance Characteristics
 * - **Lightweight Component**: Minimal state and efficient rendering with no unnecessary re-renders
 * - **Direct Database Access**: Uses Supabase client for optimal performance without middleware
 * - **DOM Manipulation**: Direct element hiding for immediate visual feedback
 * - **Memory Management**: Proper cleanup of event listeners and component state
 * - **Network Optimization**: Single database call per deletion with proper error handling
 *
 * ## Integration Patterns
 * **Administrative Workflow Integration**:
 * - **Data Tables**: Commonly used in admin tables for row-level delete actions
 * - **Detail Views**: Embedded in record detail pages for administrative management
 * - **Hover Overlays**: Appears as overlay button on hover with group-hover patterns
 * - **Bulk Operations**: Can be used individually as part of bulk management interfaces
 * - **Notification System**: Uses platform-wide toast notifications for consistent UX
 *
 * ## Error Handling Strategy
 * **Comprehensive Error Management**:
 * - **Network Errors**: Graceful handling of Supabase connection failures with user feedback
 * - **Permission Errors**: Clear messaging when RLS policies prevent deletion
 * - **Database Errors**: Proper error logging while preventing sensitive information disclosure
 * - **State Recovery**: Component state properly reset after errors to prevent stuck states
 * - **User Guidance**: Toast notifications provide actionable error messages when possible
 *
 * ## Known Technical Considerations
 * 1. **DOM Manipulation**: Uses direct DOM manipulation for hiding elements (could be replaced with React state management)
 * 2. **Custom Confirmation**: Uses custom confirmation dialog instead of browser-native confirm()
 * 3. **Single Record Focus**: Designed for individual deletions, not bulk operations
 * 4. **Event Propagation**: Complex event handling to prevent bubble-up in nested components
 * 5. **Positioning**: Absolutely positioned overlay requires parent with relative positioning
 *
 * ## Usage Patterns
 * **Typical Implementation Scenarios**:
 * ```typescript
 * // In admin data tables with hover overlay
 * <div className="relative group">
 *   <FlagCard flag={flag} />
 *   <AdminDeleteButton
 *     tableName="xfer_flags"
 *     recordId={flag.id}
 *     recordType="Effect Flag"
 *     onDeleted={() => fetchFlags()}
 *   />
 * </div>
 *
 * // In record detail views
 * <AdminDeleteButton
 *   tableName="xfer_entities"
 *   recordId={entity.id}
 *   recordType="ESG Entity"
 *   className="top-4 right-4"
 *   onDeleted={() => router.push('/admin/entities')}
 * />
 *
 * // With custom styling
 * <AdminDeleteButton
 *   tableName="xfer_reports"
 *   recordId={report.id}
 *   recordType="Analysis Report"
 *   className="opacity-100" // Always visible
 * />
 * ```
 *
 * ## Related Components & Dependencies
 * - **AuthContext**: Provides admin role verification and user authentication state
 * - **Supabase Client**: Database client for secure record deletion operations
 * - **Toast Hook**: Custom notification system for user feedback and error reporting
 * - **CN Utility**: Tailwind CSS class name utility for conditional styling
 * - **Lucide Icons**: Trash2 icon for visual consistency with platform icon system
 * - **React State**: useState for managing deletion and confirmation states
 *
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase RLS Documentation
 * @see https://ui.shadcn.com/docs/components/button shadcn/ui Button Component Documentation
 * @see https://nextjs.org/docs/app/api-reference/functions/use-router Next.js useRouter Hook
 * @see {@link ../../context/auth/auth-context} AuthContext for admin role verification
 * @see {@link ../../../hooks/use-toast} Custom toast notification system
 * @see {@link ../../../app/supabase/client} Supabase client factory for database operations
 * @see {@link ../../../utils/lib/utils} CN utility for conditional class names
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Administrative delete button component with RLS security and glass-morphism design for ESG platform data management
 * @example ```tsx
 * import { AdminDeleteButton } from '@/components/admin/AdminDeleteButton'
 *
 * // Basic usage in admin table with hover overlay
 * <div className="relative group">
 *   <EffectFlagCard flag={flag} />
 *   <AdminDeleteButton
 *     tableName="xfer_flags"
 *     recordId={flag.id}
 *     recordType="Effect Flag"
 *     onDeleted={() => refetchFlags()}
 *   />
 * </div>
 *
 * // With custom positioning and callback
 * <div className="relative group">
 *   <EntityCard entity={entity} />
 *   <AdminDeleteButton
 *     tableName="xfer_entities"
 *     recordId={entity.id}
 *     recordType="ESG Entity"
 *     className="top-4 right-4"
 *     onDeleted={() => {
 *       setEntities(prev => prev.filter(e => e.id !== entity.id))
 *       toast.success('Entity deleted successfully')
 *     }}
 *   />
 * </div>
 *
 * // In a data management interface
 * {admin && (
 *   <AdminDeleteButton
 *     tableName="xfer_reports"
 *     recordId={report.id}
 *     recordType="Analysis Report"
 *     onDeleted={() => router.push('/admin/reports')}
 *   />
 * )}
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import React, { useState } from 'react'
import { Trash2 } from 'lucide-react'
import { createClient } from '@/app/supabase/client'
import { useAuth } from '@/components/context/auth/auth-context'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@utils/lib/utils'

interface AdminDeleteButtonProps {
  /** The table name to delete from (e.g., 'xfer_flags') */
  tableName: string
  /** The ID of the record to delete */
  recordId: number | string
  /** Human-readable name for the record type (e.g., 'flag', 'promise') */
  recordType: string
  /** Optional callback after successful deletion */
  onDeleted?: () => void
  /** Optional custom className */
  className?: string
}
export const AdminDeleteButton: React.FC<AdminDeleteButtonProps> = ({
  tableName,
  recordId,
  recordType,
  onDeleted,
  className,
}) => {
  const { admin } = useAuth()
  const { toast } = useToast()
  const [isDeleting, setIsDeleting] = useState(false)
  const [showConfirm, setShowConfirm] = useState(false)
  const supabase = createClient()

  // Don't render anything if user is not admin
  if (!admin) {
    return null
  }

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    
    if (!showConfirm) {
      setShowConfirm(true)
      return
    }

    setIsDeleting(true)
    try {
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq('id', recordId)

      if (error) {
        console.error('Delete error:', error)
        toast({
          description: `Failed to delete ${recordType}: ${error.message}`,
          variant: "destructive"
        })
        return
      }

      toast({
        description: `${recordType.charAt(0).toUpperCase() + recordType.slice(1)} deleted successfully`,
      })

      // Hide the parent element (visual hack)
      const deleteButton = document.querySelector(`[data-record-id="${recordId}"]`)?.closest('.relative.group') as HTMLElement
      if (deleteButton) {
        deleteButton.style.display = 'none'
      }

      // Call optional callback
      onDeleted?.()
      
    } catch (err) {
      console.error('Unexpected error:', err)
      toast({
        description: `Unexpected error deleting ${recordType}`,
        variant: "destructive"
      })
    } finally {
      setIsDeleting(false)
      setShowConfirm(false)
    }
  }

  const handleCancel = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    setShowConfirm(false)
  }

  return (
    <div 
      className={cn(
        "absolute top-2 right-2 z-20 pointer-events-none",
        "opacity-0 group-hover:opacity-100 transition-opacity duration-200",
        className
      )}
      data-record-id={recordId}
    >
      {!showConfirm ? (
        // Delete icon
        <button
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            e.nativeEvent.stopImmediatePropagation()
            handleDelete(e)
          }}
          disabled={isDeleting}
          className={cn(
            "p-1.5 rounded-lg pointer-events-auto",
            "glass-effect-strong backdrop-blur-md",
            "text-red-400 hover:text-red-300",
            "hover:glass-effect-brand transition-all duration-200",
            "shadow-md hover:shadow-lg",
            "border border-white/10 hover:border-red-400/30",
            isDeleting && "opacity-50 cursor-not-allowed"
          )}
          title={`Delete ${recordType}`}
        >
          <Trash2 className="w-4 h-4" />
        </button>
      ) : (
        // Confirmation dialog
        <div 
          className={cn(
            "p-2 rounded-lg min-w-[120px] pointer-events-auto",
            "glass-effect-strong backdrop-blur-md",
            "border border-red-400/30",
            "shadow-lg"
          )}
          onClick={(e) => e.stopPropagation()}
        >
          <p className="text-xs text-white/90 mb-2 text-center">
            Delete {recordType}?
          </p>
          <div className="flex gap-1">
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                e.nativeEvent.stopImmediatePropagation()
                handleDelete(e)
              }}
              disabled={isDeleting}
              className={cn(
                "px-2 py-1 text-xs rounded",
                "bg-red-500/80 hover:bg-red-500 text-white",
                "transition-colors duration-200",
                isDeleting && "opacity-50 cursor-not-allowed"
              )}
            >
              {isDeleting ? 'Deleting...' : 'Yes'}
            </button>
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                e.nativeEvent.stopImmediatePropagation()
                handleCancel(e)
              }}
              disabled={isDeleting}
              className={cn(
                "px-2 py-1 text-xs rounded",
                "bg-gray-500/80 hover:bg-gray-500 text-white",
                "transition-colors duration-200"
              )}
            >
              No
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
