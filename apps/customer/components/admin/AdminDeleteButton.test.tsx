import React from 'react'
import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react'
import { AdminDeleteButton } from './AdminDeleteButton'
import { useAuth } from '@/components/context/auth/auth-context'
import type { User } from '@supabase/supabase-js'
import type { ProfileType } from '@/types'

// Define AuthContextType for testing
interface AuthContextType {
  user: User | null;
  admin: boolean;
  profile: ProfileType | null;
  hasFeature: (flagName: string) => boolean;
}

// Mock utils
vi.mock('@utils/lib/utils', () => ({
  cn: vi.fn((...classes: any[]) => classes.filter(Boolean).join(' ')),
}))

// Mock Lucide React
vi.mock('lucide-react', () => ({
  Trash2: vi.fn(({ className }: { className?: string }) => 
    React.createElement('svg', { 
      className, 
      'data-testid': 'trash-icon',
      'aria-hidden': 'true'
    })
  ),
}))

// Mock Supabase client
const mockDelete = vi.fn()
const mockEq = vi.fn()
const mockFrom = vi.fn()

const mockSupabaseClient = {
  from: mockFrom,
}

vi.mock('@/app/supabase/client', () => ({
  createClient: () => mockSupabaseClient,
}))

// Mock auth context
vi.mock('@/components/context/auth/auth-context', () => ({
  useAuth: vi.fn(),
}))

// Mock toast hook
const mockToast = vi.fn()
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: mockToast }),
}))

// Mock user object
const mockUser: User = {
  id: 'test-user',
  email: '<EMAIL>',
  app_metadata: {},
  user_metadata: {},
  aud: 'authenticated',
  created_at: '2024-01-01T00:00:00.000Z',
}

// Mock profile object
const mockProfile: ProfileType = {
  id: 'test-user',
  avatar_url: null,
  email: '<EMAIL>',
  feature_flags: [],
  full_name: 'Test User',
  is_admin: true,
  name: 'Test User',
  organisation: null,
  updated_at: null,
  username: null,
  website: null,
  welcome_message: null,
}

beforeEach(() => {
  // Setup Supabase mock chain
  mockEq.mockReturnValue({ data: null, error: null })
  mockDelete.mockReturnValue({ eq: mockEq })
  mockFrom.mockReturnValue({ delete: mockDelete })

  // Clear all mocks
  vi.clearAllMocks()
})

afterEach(() => {
  cleanup()
  vi.clearAllMocks()
})

describe('AdminDeleteButton Component', () => {
  const defaultProps = {
    tableName: 'test_table',
    recordId: 123,
    recordType: 'record',
  }

  describe('Admin Visibility Logic', () => {
    test('renders nothing when user is not admin', () => {
      vi.mocked(useAuth).mockReturnValue({ 
        user: null, 
        admin: false, 
        profile: null, 
        hasFeature: vi.fn().mockReturnValue(false) 
      })
      
      const { container } = render(<AdminDeleteButton {...defaultProps} />)
      
      expect(container.firstChild).toBeNull()
    })

    test('renders delete button when user is admin', () => {
      vi.mocked(useAuth).mockReturnValue({ 
        user: mockUser, 
        admin: true, 
        profile: mockProfile, 
        hasFeature: vi.fn().mockReturnValue(true) 
      })
      
      render(<AdminDeleteButton {...defaultProps} />)
      
      expect(screen.getByTestId('trash-icon')).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeInTheDocument()
    })
  })

  describe('Basic Rendering and Props', () => {
    beforeEach(() => {
      vi.mocked(useAuth).mockReturnValue({ 
        user: mockUser, 
        admin: true, 
        profile: mockProfile, 
        hasFeature: vi.fn().mockReturnValue(true) 
      })
    })

    test('renders with correct default structure', () => {
      const { container } = render(<AdminDeleteButton {...defaultProps} />)
      
      const wrapper = container.firstChild as HTMLElement
      expect(wrapper).toHaveAttribute('data-record-id', '123')
      expect(wrapper).toHaveClass('absolute', 'top-2', 'right-2', 'z-20')
    })

    test('applies custom className correctly', () => {
      const { container } = render(
        <AdminDeleteButton {...defaultProps} className="custom-class" />
      )
      
      const wrapper = container.firstChild as HTMLElement
      expect(wrapper).toHaveClass('custom-class')
    })

    test('shows correct title attribute', () => {
      render(<AdminDeleteButton {...defaultProps} recordType="flag" />)
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('title', 'Delete flag')
    })

    test('handles string recordId', () => {
      const { container } = render(
        <AdminDeleteButton {...defaultProps} recordId="abc123" />
      )
      
      const wrapper = container.firstChild as HTMLElement
      expect(wrapper).toHaveAttribute('data-record-id', 'abc123')
    })
  })

  describe('Delete Confirmation Workflow', () => {
    beforeEach(() => {
      vi.mocked(useAuth).mockReturnValue({ 
        user: mockUser, 
        admin: true, 
        profile: mockProfile, 
        hasFeature: vi.fn().mockReturnValue(true) 
      })
    })

    test('shows confirmation dialog on first click', () => {
      render(<AdminDeleteButton {...defaultProps} recordType="promise" />)
      
      const deleteButton = screen.getByRole('button')
      fireEvent.click(deleteButton)
      
      expect(screen.getByText('Delete promise?')).toBeInTheDocument()
      expect(screen.getByText('Yes')).toBeInTheDocument()
      expect(screen.getByText('No')).toBeInTheDocument()
    })

    test('cancels confirmation and returns to delete button', () => {
      render(<AdminDeleteButton {...defaultProps} />)
      
      // Click to show confirmation
      const deleteButton = screen.getByRole('button')
      fireEvent.click(deleteButton)
      
      // Click No to cancel
      const noButton = screen.getByText('No')
      fireEvent.click(noButton)
      
      // Should return to delete button state
      expect(screen.getByTestId('trash-icon')).toBeInTheDocument()
      expect(screen.queryByText('Delete record?')).not.toBeInTheDocument()
    })

    test('prevents event propagation on all clicks', () => {
      render(<AdminDeleteButton {...defaultProps} />)
      
      const deleteButton = screen.getByRole('button')
      
      // Spy on the event methods
      const preventDefaultSpy = vi.spyOn(Event.prototype, 'preventDefault')
      const stopPropagationSpy = vi.spyOn(Event.prototype, 'stopPropagation')
      
      fireEvent.click(deleteButton)
      
      expect(preventDefaultSpy).toHaveBeenCalled()
      expect(stopPropagationSpy).toHaveBeenCalled()
      
      // Clean up spies
      preventDefaultSpy.mockRestore()
      stopPropagationSpy.mockRestore()
    })
  })

  describe('Delete Operation and Supabase Integration', () => {
    beforeEach(() => {
      vi.mocked(useAuth).mockReturnValue({ 
        user: mockUser, 
        admin: true, 
        profile: mockProfile, 
        hasFeature: vi.fn().mockReturnValue(true) 
      })
    })

    test('performs successful delete operation', async () => {
      mockEq.mockResolvedValue({ data: null, error: null })
      
      render(<AdminDeleteButton {...defaultProps} />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(mockFrom).toHaveBeenCalledWith('test_table')
        expect(mockDelete).toHaveBeenCalled()
        expect(mockEq).toHaveBeenCalledWith('id', 123)
      })
      
      expect(mockToast).toHaveBeenCalledWith({
        description: 'Record deleted successfully',
      })
    })

    test('handles delete error correctly', async () => {
      const errorMessage = 'Delete failed'
      mockEq.mockResolvedValue({ data: null, error: { message: errorMessage } })
      
      render(<AdminDeleteButton {...defaultProps} recordType="flag" />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          description: `Failed to delete flag: ${errorMessage}`,
          variant: 'destructive',
        })
      })
    })

    test('handles unexpected errors', async () => {
      mockEq.mockRejectedValue(new Error('Network error'))
      
      render(<AdminDeleteButton {...defaultProps} recordType="promise" />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          description: 'Unexpected error deleting promise',
          variant: 'destructive',
        })
      })
    })

    test('shows loading state during delete', async () => {
      // Create a promise that we can control
      let resolveDelete: (value: any) => void
      const deletePromise = new Promise((resolve) => {
        resolveDelete = resolve
      })
      mockEq.mockReturnValue(deletePromise)
      
      render(<AdminDeleteButton {...defaultProps} />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      fireEvent.click(screen.getByText('Yes'))
      
      // Check loading state
      expect(screen.getByText('Deleting...')).toBeInTheDocument()
      
      // Resolve the promise
      resolveDelete!({ data: null, error: null })
      
      await waitFor(() => {
        expect(screen.queryByText('Deleting...')).not.toBeInTheDocument()
      })
    })

    test('disables buttons during delete operation', async () => {
      let resolveDelete: (value: any) => void
      const deletePromise = new Promise((resolve) => {
        resolveDelete = resolve
      })
      mockEq.mockReturnValue(deletePromise)
      
      render(<AdminDeleteButton {...defaultProps} />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      const yesButton = screen.getByText('Yes')
      const noButton = screen.getByText('No')
      
      fireEvent.click(yesButton)
      
      // Check that buttons are disabled
      expect(yesButton).toBeDisabled()
      expect(noButton).toBeDisabled()
      
      // Resolve the promise
      resolveDelete!({ data: null, error: null })
      
      await waitFor(() => {
        expect(screen.queryByText('Deleting...')).not.toBeInTheDocument()
      })
    })
  })

  describe('DOM Manipulation and Callbacks', () => {
    beforeEach(() => {
      vi.mocked(useAuth).mockReturnValue({ 
        user: mockUser, 
        admin: true, 
        profile: mockProfile, 
        hasFeature: vi.fn().mockReturnValue(true) 
      })
    })

    test('hides parent element after successful delete', async () => {
      mockEq.mockResolvedValue({ data: null, error: null })
      
      const mockParentElement = { style: { display: '' } }
      const mockElementWithClosest = {
        closest: vi.fn().mockReturnValue(mockParentElement)
      }
      
      // Mock document.querySelector to return our element
      const originalQuerySelector = document.querySelector
      document.querySelector = vi.fn().mockReturnValue(mockElementWithClosest)
      
      render(<AdminDeleteButton {...defaultProps} recordId="test123" />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(document.querySelector).toHaveBeenCalledWith('[data-record-id="test123"]')
        expect(mockElementWithClosest.closest).toHaveBeenCalledWith('.relative.group')
        expect(mockParentElement.style.display).toBe('none')
      })
      
      // Restore original
      document.querySelector = originalQuerySelector
    })

    test('calls onDeleted callback after successful delete', async () => {
      mockEq.mockResolvedValue({ data: null, error: null })
      const onDeleted = vi.fn()
      
      render(<AdminDeleteButton {...defaultProps} onDeleted={onDeleted} />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(onDeleted).toHaveBeenCalledTimes(1)
      })
    })

    test('does not call onDeleted on delete error', async () => {
      mockEq.mockResolvedValue({ data: null, error: { message: 'Error' } })
      const onDeleted = vi.fn()
      
      render(<AdminDeleteButton {...defaultProps} onDeleted={onDeleted} />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith(
          expect.objectContaining({ variant: 'destructive' })
        )
      })
      
      expect(onDeleted).not.toHaveBeenCalled()
    })
  })

  describe('Record Type Capitalization', () => {
    beforeEach(() => {
      vi.mocked(useAuth).mockReturnValue({ 
        user: mockUser, 
        admin: true, 
        profile: mockProfile, 
        hasFeature: vi.fn().mockReturnValue(true) 
      })
    })

    test('capitalizes record type in success message', async () => {
      mockEq.mockResolvedValue({ data: null, error: null })
      
      render(<AdminDeleteButton {...defaultProps} recordType="flag" />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          description: 'Flag deleted successfully',
        })
      })
    })

    test('handles empty record type', async () => {
      mockEq.mockResolvedValue({ data: null, error: null })
      
      render(<AdminDeleteButton {...defaultProps} recordType="" />)
      
      // Show confirmation and confirm delete
      fireEvent.click(screen.getByRole('button'))
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          description: ' deleted successfully',
        })
      })
    })
  })

  describe('Component State Management', () => {
    beforeEach(() => {
      vi.mocked(useAuth).mockReturnValue({ 
        user: mockUser, 
        admin: true, 
        profile: mockProfile, 
        hasFeature: vi.fn().mockReturnValue(true) 
      })
    })

    test('resets confirmation state after successful delete', async () => {
      mockEq.mockResolvedValue({ data: null, error: null })
      
      render(<AdminDeleteButton {...defaultProps} />)
      
      // Show confirmation
      fireEvent.click(screen.getByRole('button'))
      expect(screen.getByText('Delete record?')).toBeInTheDocument()
      
      // Confirm delete
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(screen.getByTestId('trash-icon')).toBeInTheDocument()
        expect(screen.queryByText('Delete record?')).not.toBeInTheDocument()
      })
    })

    test('resets confirmation state after error', async () => {
      mockEq.mockResolvedValue({ data: null, error: { message: 'Error' } })
      
      render(<AdminDeleteButton {...defaultProps} />)
      
      // Show confirmation
      fireEvent.click(screen.getByRole('button'))
      expect(screen.getByText('Delete record?')).toBeInTheDocument()
      
      // Confirm delete (will fail)
      fireEvent.click(screen.getByText('Yes'))
      
      await waitFor(() => {
        expect(screen.getByTestId('trash-icon')).toBeInTheDocument()
        expect(screen.queryByText('Delete record?')).not.toBeInTheDocument()
      })
    })
  })
})