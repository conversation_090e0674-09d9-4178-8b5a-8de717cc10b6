/**
 * # Organization Member Removal Component for EkoIntelligence ESG Platform
 *
 * This critical administrative component provides secure, role-based member removal functionality for
 * organizations within the EkoIntelligence ESG analysis platform. Designed exclusively for admin users,
 * it implements comprehensive security through Supabase Row Level Security (RLS) policies while
 * maintaining a consistent glass-morphism design aesthetic with interactive hover states and
 * confirmation dialogs for safe organization member management.
 *
 * ## Core Functionality
 * - **Secure Member Removal**: Admin-only member removal with RLS policy enforcement for maximum security
 * - **Interactive UI**: Ghost variant button with hover effects and visual feedback using AlertDialog
 * - **Confirmation Dialog**: AlertDialog-based confirmation flow to prevent accidental member removals
 * - **Real-time Feedback**: Toast notifications for operation status and comprehensive error handling
 * - **Page Refresh**: Automatic router refresh to update UI state after successful member removal
 * - **Type Safety**: Full TypeScript integration with proper error handling and null safety
 *
 * ## System Architecture Context
 * This component serves as a critical part of the EkoIntelligence platform's organization management interface:
 * - **ESG Organization Management**: Enables removal of users from ESG analysis organizations and teams
 * - **Admin Dashboard**: Integrates with administrative panels for organization member management workflows
 * - **Customer Application**: Part of the customer-facing app built on Next.js 15 App Router architecture
 * - **Authentication System**: Relies on AuthContext for admin role verification and session management
 * - **Database Layer**: Operates on customer database with organization membership stored in profiles table
 * - **Security Model**: Implements defense-in-depth through client auth + server RLS policies for data protection
 *
 * ## Database Schema Integration
 * The component integrates with the customer database schema and Row Level Security policies:
 * - **profiles Table**: Primary target for member removal with organisation field set to null
 * - **Organization Relationship**: Foreign key reference to acc_organisations table for membership tracking
 * - **RLS Enforcement**: PostgreSQL policies ensure only authorized users can modify organization memberships
 * - **User Authentication**: Links to auth.users table via profiles.id foreign key for session validation
 * - **Data Integrity**: Cascading updates maintain referential integrity across organization-related tables
 * - **Admin Verification**: is_admin boolean flag restricts access to administrative functions
 *
 * ## Props Interface
 * - **`userId`**: UUID identifier of the user to remove from organization (required)
 * - **`userName`**: Human-readable display name of user for confirmation dialog (required)
 * - **`organizationId`**: Numeric identifier of the organization for validation (required)
 *
 * ## State Management
 * - **`loading`**: Loading state preventing double-clicks and providing user feedback during removal
 * - **Auth Context**: Consumes admin status from AuthProvider for role-based access control
 * - **Toast State**: Manages success/error notifications through Sonner toast notification system
 * - **Router State**: Uses Next.js useRouter for programmatic page refresh after operations
 *
 * ## Security Architecture
 * **Multi-Layer Security Implementation**:
 * - **Client-Side Authorization**: Admin role check prevents UI rendering for non-admin users
 * - **Server-Side Enforcement**: Supabase RLS policies provide ultimate security enforcement at database level
 * - **Database Policies**: PostgreSQL RLS policies ensure only authorized updates to user profiles
 * - **Type Safety**: TypeScript prevents common security vulnerabilities through compile-time validation
 * - **Input Validation**: Props validation ensures proper data types and required fields are present
 * - **Error Handling**: Comprehensive error catching prevents information disclosure to unauthorized users
 *
 * ## User Experience Features
 * **Glass-Morphism Design Pattern**:
 * - **Visual Consistency**: Ghost button variant matches platform's translucent design aesthetic
 * - **Interactive States**: Hover effects with subtle transformations for improved user experience
 * - **Loading Feedback**: Button disabled state and loading text during removal operations
 * - **AlertDialog Confirmation**: Professional two-step confirmation process with Cancel/Remove buttons
 * - **Toast Notifications**: Success/error feedback using consistent platform notification system
 * - **Responsive Design**: Works seamlessly across mobile and desktop devices with proper touch targets
 * - **Accessibility**: Proper ARIA attributes and keyboard navigation support through Radix UI primitives
 *
 * ## Performance Characteristics
 * - **Lightweight Component**: Minimal state and efficient rendering with optimized re-render patterns
 * - **Direct Database Access**: Uses Supabase client for optimal performance without unnecessary middleware
 * - **Single Operation**: One database update per removal with atomic transaction handling
 * - **Memory Management**: Proper cleanup of component state and event listeners on unmount
 * - **Network Optimization**: Single database call per removal with comprehensive error handling
 *
 * ## Integration Patterns
 * **Organization Management Workflow Integration**:
 * - **Member Lists**: Commonly used in organization member tables for individual removal actions
 * - **User Cards**: Embedded in user profile cards within organization management interfaces
 * - **Bulk Operations**: Can be used individually as part of larger organization management workflows
 * - **Admin Dashboards**: Integrated into administrative panels for organization oversight
 * - **Notification System**: Uses platform-wide toast notifications for consistent user experience
 *
 * ## Error Handling Strategy
 * **Comprehensive Error Management**:
 * - **Network Errors**: Graceful handling of Supabase connection failures with clear user feedback
 * - **Permission Errors**: Clear messaging when RLS policies prevent organization membership changes
 * - **Database Errors**: Proper error logging while preventing sensitive information disclosure
 * - **State Recovery**: Component state properly reset after errors to prevent stuck loading states
 * - **User Guidance**: Toast notifications provide actionable error messages when possible for resolution
 *
 * ## Known Technical Considerations
 * 1. **Router Refresh**: Uses router.refresh() for immediate UI updates (alternative: state management)
 * 2. **AlertDialog Integration**: Uses Radix UI AlertDialog for accessible confirmation flows
 * 3. **Single Member Focus**: Designed for individual member removal, not bulk organization operations
 * 4. **Admin-Only Access**: Requires admin privileges for access, hidden from non-admin users
 * 5. **Organization Context**: Assumes user is part of an organization that admin has authority over
 *
 * ## Usage Patterns
 * **Typical Implementation Scenarios**:
 * ```typescript
 * // In organization member management interface
 * <div className="space-y-4">
 *   {members.map(member => (
 *     <div key={member.id} className="flex items-center justify-between p-4 glass-effect rounded-xl">
 *       <div>
 *         <p className="font-medium">{member.name}</p>
 *         <p className="text-sm text-muted-foreground">{member.email}</p>
 *       </div>
 *       <RemoveMemberButton
 *         userId={member.id}
 *         userName={member.name}
 *         organizationId={organization.id}
 *       />
 *     </div>
 *   ))}
 * </div>
 *
 * // In admin user profile view
 * <div className="flex justify-between items-center">
 *   <UserProfileCard user={user} />
 *   <RemoveMemberButton
 *     userId={user.id}
 *     userName={user.name}
 *     organizationId={user.organisation}
 *   />
 * </div>
 *
 * // With organization context validation
 * <RemoveMemberButton
 *   userId={selectedUser.id}
 *   userName={selectedUser.full_name || selectedUser.username}
 *   organizationId={currentOrganization.id}
 * />
 * ```
 *
 * ## Related Components & Dependencies
 * - **AuthContext**: Provides admin role verification and user authentication state management
 * - **Supabase Client**: Database client for secure profile updates and organization membership changes
 * - **Sonner Toast**: Toast notification system for user feedback and comprehensive error reporting
 * - **AlertDialog**: Radix UI AlertDialog primitives for accessible confirmation modal dialogs
 * - **Button Component**: shadcn/ui Button with ghost variant for consistent platform styling
 * - **Router Hook**: Next.js useRouter for programmatic navigation and page refresh functionality
 * - **Lucide Icons**: Trash2 icon for visual consistency with platform icon system
 *
 * @see https://supabase.com/docs/guides/auth/row-level-security Supabase RLS Documentation
 * @see https://ui.shadcn.com/docs/components/alert-dialog shadcn/ui AlertDialog Component Documentation
 * @see https://nextjs.org/docs/app/api-reference/functions/use-router Next.js useRouter Hook Documentation
 * @see https://sonner.emilkowal.ski/ Sonner Toast Library Documentation
 * @see {@link ../../context/auth/auth-context} AuthContext for admin role verification
 * @see {@link ../../../app/supabase/client} Supabase client factory for database operations
 * @see {@link ../../../components/ui/alert-dialog} AlertDialog component for confirmation flows
 * @see {@link ../../../components/ui/button} Button component with variant support
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Organization member removal component with RLS security and AlertDialog confirmation for ESG platform organization management
 * @example ```tsx
 * import { RemoveMemberButton } from '@/components/admin/RemoveMemberButton'
 *
 * // Basic usage in organization member list
 * <div className="space-y-4">
 *   {organizationMembers.map(member => (
 *     <div key={member.id} className="flex items-center justify-between p-4 glass-effect rounded-xl">
 *       <div className="flex items-center space-x-3">
 *         <Avatar>
 *           <AvatarImage src={member.avatar_url} />
 *           <AvatarFallback>{member.name?.[0]}</AvatarFallback>
 *         </Avatar>
 *         <div>
 *           <p className="font-medium">{member.name}</p>
 *           <p className="text-sm text-muted-foreground">{member.email}</p>
 *         </div>
 *       </div>
 *       <RemoveMemberButton
 *         userId={member.id}
 *         userName={member.name || 'Unknown User'}
 *         organizationId={currentOrganization.id}
 *       />
 *     </div>
 *   ))}
 * </div>
 *
 * // In admin confirmation context
 * {isAdmin && currentUser.organisation && (
 *   <div className="border-t pt-4 mt-4">
 *     <h3 className="text-lg font-semibold mb-2">Remove from Organization</h3>
 *     <p className="text-sm text-muted-foreground mb-4">
 *       This will remove the user from your organization and revoke their access.
 *     </p>
 *     <RemoveMemberButton
 *       userId={targetUser.id}
 *       userName={targetUser.name}
 *       organizationId={targetUser.organisation}
 *     />
 *   </div>
 * )}
 *
 * // With member management table integration
 * <Table>
 *   <TableBody>
 *     {members.map(member => (
 *       <TableRow key={member.id}>
 *         <TableCell>{member.name}</TableCell>
 *         <TableCell>{member.email}</TableCell>
 *         <TableCell>{member.role}</TableCell>
 *         <TableCell className="text-right">
 *           <RemoveMemberButton
 *             userId={member.id}
 *             userName={member.name}
 *             organizationId={organization.id}
 *           />
 *         </TableCell>
 *       </TableRow>
 *     ))}
 *   </TableBody>
 * </Table>
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

'use client'

import { Button } from '@/components/ui/button'
import { createClient } from '@/app/supabase/client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { Trash2 } from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

interface RemoveMemberButtonProps {
  /** UUID identifier of the user to remove from organization */
  userId: string
  /** Human-readable display name of user for confirmation dialog */
  userName: string
  /** Numeric identifier of the organization for validation */
  organizationId: number
}

export function RemoveMemberButton({ userId, userName, organizationId }: RemoveMemberButtonProps) {
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const handleRemove = async () => {
    setLoading(true)
    
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          organisation: null
        })
        .eq('id', userId)

      if (error) {
        throw error
      }

      toast.success(`${userName} removed from organization`)
      router.refresh()
    } catch (error) {
      console.error('Error removing member:', error)
      toast.error('Failed to remove member')
    } finally {
      setLoading(false)
    }
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="ghost" size="sm" disabled={loading}>
          <Trash2 className="h-4 w-4" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remove Member</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to remove {userName} from this organization? 
            This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleRemove} disabled={loading}>
            {loading ? 'Removing...' : 'Remove'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
