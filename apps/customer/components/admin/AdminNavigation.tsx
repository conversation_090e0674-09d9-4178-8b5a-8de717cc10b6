/**
 * # Administrative Navigation Sidebar Component for ESG Analysis Platform
 *
 * This React client component provides a comprehensive administrative navigation sidebar for the
 * EkoIntelligence ESG (Environmental, Social, Governance) analysis platform. It serves as the primary
 * navigation interface for platform administrators, offering access to user management, organization
 * oversight, feature configuration, and system administration functions within a modern glass-morphism
 * design framework.
 *
 * ## Core Functionality
 * - **Administrative Navigation**: Centralized navigation hub for all administrative functions and system management
 * - **Role-Based Access**: Admin-only navigation interface with proper authentication and authorization controls
 * - **Active State Management**: Dynamic highlighting of current page using Next.js App Router pathname detection
 * - **Authentication Integration**: Seamless integration with Supabase authentication for secure session management
 * - **Glass-morphism Design**: Modern frosted glass visual design with backdrop blur effects and translucent surfaces
 * - **Responsive Layout**: Mobile-friendly responsive design that adapts to different screen sizes and orientations
 *
 * ## Administrative Features
 * **Core Admin Navigation** (7 primary sections):
 * - **Dashboard** (`/admin`): Administrative overview with system statistics and monitoring capabilities
 * - **Organizations** (`/admin/organizations`): Organization management for multi-tenant ESG analysis platform
 * - **Users** (`/admin/users`): User account management, permissions, and access control administration
 * - **Feature Flags** (`/admin/flags`): Dynamic feature toggle management for controlled feature rollouts
 * - **Virtual Entities** (`/admin/entities`): ESG entity management and virtual entity configuration
 * - **Messages** (`/admin/messages`): System messaging and notification management for user communications
 * - **Changelog** (`/admin/changelog`): Platform update logs and version history for transparency
 *
 * **Navigation Actions**:
 * - **Back to App**: Quick navigation return to customer-facing application (`/customer`)
 * - **Sign Out**: Secure authentication logout with automatic redirect to login page
 *
 * ## Visual Design System
 * **Glass-morphism Implementation**:
 * - **Frosted Glass Effect**: `bg-white/80 backdrop-blur-sm` creates translucent, modern appearance
 * - **Subtle Borders**: Consistent border styling with `border-gray-200` for visual hierarchy
 * - **Rounded Elements**: Modern rounded corners throughout navigation items for approachable design
 * - **Hover States**: Interactive hover effects with smooth transitions for enhanced user experience
 * - **Active States**: Primary color highlighting for current page with `bg-primary text-primary-foreground`
 *
 * **Icon Integration**:
 * - **Lucide React Icons**: Consistent iconography throughout navigation items for visual clarity
 * - **Semantic Icons**: Each navigation item uses contextually appropriate icons (Users, Building, Settings, etc.)
 * - **Size Consistency**: Uniform 16x16px (`h-4 w-4`) icon dimensions for visual harmony
 *
 * ## Technical Architecture
 * **Next.js 15 App Router Integration**:
 * - **Client Component**: Uses `'use client'` directive for interactive navigation functionality
 * - **Pathname Detection**: `usePathname()` hook provides accurate current route detection for active states
 * - **Programmatic Navigation**: `useRouter()` enables controlled navigation after authentication actions
 * - **Link Component**: Next.js `Link` components for optimized client-side navigation with prefetching
 *
 * **Supabase Authentication Integration**:
 * - **Client Instance**: Creates Supabase client for authentication operations and session management
 * - **Sign Out Handler**: Secure logout functionality with `supabase.auth.signOut()` and automatic redirect
 * - **Session Management**: Leverages Supabase's automatic session refresh and token management
 * - **Security**: Row Level Security (RLS) policies ensure admin access controls at database level
 *
 * **UI Component Architecture**:
 * - **ScrollArea**: Radix UI-based scrollable container for overflow handling in navigation lists
 * - **Button Components**: shadcn/ui Button components with consistent styling and accessibility features
 * - **Utility Classes**: `cn()` utility for conditional className merging with Tailwind CSS
 * - **Type Safety**: Full TypeScript integration with proper type checking for all component props
 *
 * ## System Context and Integration
 * **Administrative Platform Context**:
 * This navigation component serves as the entry point to the administrative layer of the EkoIntelligence platform:
 * - **User Management**: Connects to user administration interfaces for account oversight and permissions
 * - **Organization Management**: Links to multi-tenant organization management workflows and configurations
 * - **Feature Control**: Provides access to feature flag management for controlled platform feature rollouts
 * - **System Monitoring**: Gateway to system health monitoring, logging, and performance analytics
 * - **Content Management**: Access to system messaging, changelogs, and platform communication tools
 *
 * **Database Integration**:
 * The navigation connects to administrative database operations:
 * - **User Tables**: `profiles`, `acc_organisations` for user and organization management
 * - **Feature Flags**: Dynamic feature configuration stored in database with real-time updates
 * - **System Tables**: Administrative logs, messages, and configuration data
 * - **Analytics Integration**: Connects to analytics database for system monitoring and reporting
 *
 * ## Security and Access Control
 * **Administrative Security**:
 * - **Role-Based Access**: Component should only be rendered for users with administrative privileges
 * - **Route Protection**: All linked routes protected by middleware and server-side authentication checks
 * - **Session Security**: Automatic session validation and refresh through Supabase authentication
 * - **Audit Logging**: Administrative actions logged for security monitoring and compliance
 *
 * **Authentication Flow**:
 * - **Session Validation**: Component assumes valid admin session but handles logout gracefully
 * - **Secure Logout**: `handleSignOut()` ensures complete session termination and secure redirect
 * - **Token Management**: Leverages Supabase's automatic token refresh and security features
 *
 * ## Performance and Optimization
 * **Component Performance**:
 * - **Static Navigation**: Navigation array defined at module level for optimal performance
 * - **Efficient Re-renders**: Minimal re-rendering through optimized dependency arrays and state management
 * - **Lazy Loading**: ScrollArea component enables efficient rendering of large navigation lists
 * - **Memory Management**: Proper cleanup of event listeners and subscriptions
 *
 * **Navigation Optimization**:
 * - **Client-Side Routing**: Next.js Link components provide optimized navigation with prefetching
 * - **Conditional Rendering**: Efficient active state detection without unnecessary DOM updates
 * - **Icon Optimization**: Tree-shaken Lucide icons for minimal bundle size impact
 *
 * ## Related Components and Systems
 * **Component Relationships**:
 * - **Admin Layout**: Typically used within administrative layout components for consistent page structure
 * - **Authentication Guards**: Works with route protection components to ensure administrative access
 * - **Main Navigation**: Complements customer-facing navigation system in `/components/navigation/`
 * - **User Provider**: Integrates with user context providers for role-based rendering decisions
 *
 * **System Integration**:
 * - **Middleware**: Protected by Next.js middleware for route-level authentication validation
 * - **Database Policies**: Supabase RLS policies enforce server-side administrative access controls
 * - **Analytics**: Navigation usage tracked for administrative workflow optimization
 * - **Monitoring**: Component performance and usage monitored for system health insights
 *
 * ## Usage Examples and Patterns
 * **Basic Implementation**:
 * ```tsx
 * // Admin layout with navigation
 * import { AdminNavigation } from '@/components/admin/AdminNavigation'
 *
 * export default function AdminLayout({ children }: { children: React.ReactNode }) {
 *   return (
 *     <div className="flex h-screen bg-gray-50">
 *       <AdminNavigation />
 *       <main className="flex-1 overflow-auto">
 *         {children}
 *       </main>
 *     </div>
 *   )
 * }
 * ```
 *
 * **Route Protection Integration**:
 * ```tsx
 * // Protected admin route
 * import { redirect } from 'next/navigation'
 * import { createClient } from '@/app/supabase/server'
 *
 * export default async function AdminPage() {
 *   const supabase = createClient()
 *   const { data: { user } } = await supabase.auth.getUser()
 *
 *   if (!user?.user_metadata?.is_admin) {
 *     redirect('/customer')
 *   }
 *
 *   return <AdminNavigation />
 * }
 * ```
 *
 * **Custom Navigation Extension**:
 * ```tsx
 * // Extended navigation with additional items
 * const extendedNavigation = [
 *   ...navigation,
 *   { name: 'Reports', href: '/admin/reports', icon: BarChart3 },
 *   { name: 'Settings', href: '/admin/settings', icon: Cog }
 * ]
 * ```
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing Next.js App Router Documentation
 * @see https://nextjs.org/docs/app/api-reference/functions/use-pathname Next.js usePathname Hook
 * @see https://nextjs.org/docs/app/api-reference/functions/use-router Next.js useRouter Hook
 * @see https://supabase.com/docs/guides/auth/server-side/nextjs Supabase Next.js Authentication
 * @see https://supabase.com/docs/reference/javascript/auth-signout Supabase Sign Out Documentation
 * @see https://lucide.dev/guide/packages/lucide-react Lucide React Icon Documentation
 * @see https://ui.shadcn.com/docs/components/button shadcn/ui Button Component
 * @see https://ui.shadcn.com/docs/components/scroll-area shadcn/ui ScrollArea Component
 * @see {@link /app/supabase/client.ts} Supabase Browser Client Factory
 * @see {@link /components/ui/button.tsx} Button UI Component
 * @see {@link /components/ui/scroll-area.tsx} ScrollArea UI Component
 * @see {@link /lib/utils.ts} Utility Functions for Class Names
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Administrative navigation sidebar component with glass-morphism design, authentication integration, and comprehensive admin access management for ESG analysis platform
 * @example
 * ```tsx
 * // Basic usage in admin layout
 * import { AdminNavigation } from '@/components/admin/AdminNavigation'
 *
 * export default function AdminLayout({ children }: { children: React.ReactNode }) {
 *   return (
 *     <div className="flex h-screen">
 *       <AdminNavigation />
 *       <main className="flex-1 p-6">
 *         {children}  
 *       </main>
 *     </div>
 *   )
 * }
 *
 * // Usage with role-based rendering
 * function AdminDashboard() {
 *   const { user } = useAuth()
 *
 *   if (!user?.is_admin) {
 *     return <Redirect to="/customer" />
 *   }
 *
 *   return (
 *     <div className="admin-dashboard">
 *       <AdminNavigation />
 *       <AdminContent />
 *     </div>
 *   )
 * }
 *
 * // Integration with authentication guard
 * async function AdminProtectedPage() {
 *   const supabase = createClient()
 *   const { data } = await supabase.auth.getUser()
 *
 *   if (!data.user?.user_metadata?.admin_role) {
 *     redirect('/login')
 *   }
 *
 *   return <AdminNavigation />
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { ArrowLeft, Building, FileText, Home, LogOut, MessageSquare, Settings, Users } from 'lucide-react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { createClient } from '@/app/supabase/client'

const navigation = [
  { name: 'Dashboard', href: '/admin', icon: Home },
  { name: 'Organizations', href: '/admin/organizations', icon: Building },
  { name: 'Users', href: '/admin/users', icon: Users },
  { name: 'Feature Flags', href: '/admin/flags', icon: Settings },
  { name: 'Virtual Entities', href: '/admin/entities', icon: Settings },
  { name: 'Messages', href: '/admin/messages', icon: MessageSquare },
  { name: 'Changelog', href: '/admin/changelog', icon: FileText },
]

export function AdminNavigation() {
  const pathname = usePathname()
  const router = useRouter()
  const supabase = createClient()

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/login')
  }

  return (
    <div className="flex h-full w-64 flex-col bg-white/80 backdrop-blur-sm border-r border-gray-200">
      <div className="flex h-16 items-center px-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold">Admin Panel</h2>
      </div>
      
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                  isActive
                    ? 'bg-primary text-primary-foreground'
                    : 'text-gray-700 hover:bg-gray-100'
                )}
              >
                <item.icon className="mr-3 h-4 w-4" />
                {item.name}
              </Link>
            )
          })}
        </nav>
      </ScrollArea>
      
      <div className="border-t border-gray-200 p-4 space-y-2">
        <Button
          variant="ghost"
          size="sm"
          className="w-full justify-start"
          asChild
        >
          <Link href="/customer">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to App
          </Link>
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          className="w-full justify-start"
          onClick={handleSignOut}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Sign Out
        </Button>
      </div>
    </div>
  )
}
