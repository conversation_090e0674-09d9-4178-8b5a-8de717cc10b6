# Authentication Context Module

## Overview

The authentication context module provides comprehensive authentication and authorization management for the EkoIntelligence ESG analysis customer application. Built on Supabase authentication with PostgreSQL Row Level Security (RLS), it manages user sessions, profile data, admin status, and hierarchical feature flag evaluation while providing seamless integration with Next.js 15 App Router navigation and Sentry error tracking.

This module implements the foundational authentication layer that enables secure access to ESG analysis data, corporate sustainability reports, and platform features through a sophisticated multi-tier authorization system.

## Specification

### Authentication Flow Architecture

The authentication system implements a comprehensive session lifecycle with automatic state synchronization:

```mermaid
sequenceDiagram
    participant App as React App
    participant AuthCtx as AuthContext
    participant Supa<PERSON> as Supabase Auth
    participant DB as Customer DB
    participant Sentry as Sentry

    App->>AuthCtx: Mount AuthProvider
    AuthCtx->>Supabase: getUser()
    Supabase-->>AuthCtx: User session
    
    alt User exists
        AuthCtx->>DB: Fetch profile data
        DB-->>AuthCtx: Profile + admin status
        AuthCtx->>DB: Fetch org feature flags
        DB-->>AuthCtx: Organization flags
        AuthCtx->>Sentry: Set user context
        AuthCtx-->>App: Auth state ready
    else No user
        AuthCtx->>App: Redirect to login
    end

    AuthCtx->>Supabase: onAuthStateChange listener
    loop Auth state changes
        Supabase->>AuthCtx: Auth event
        AuthCtx->>DB: Refresh profile
        AuthCtx-->>App: Updated auth state
    end
```

### Feature Flag Hierarchy System

The module implements a three-tier hierarchical feature flag evaluation system:

```mermaid
flowchart TD
    A[Feature Flag Request] --> B{User Flags?}
    B -->|Yes| C[Return User Flag Result]
    B -->|No| D{Organization Flags?}
    D -->|Yes| E[Return Org Flag Result]
    D -->|No| F{Default Flags?}
    F -->|Yes| G[Return Default Flag Result]
    F -->|No| H[Return false]

    I[Flag Patterns] --> J[Exact: 'dashboard.analytics']
    I --> K[Wildcard: 'document.*']
    I --> L[Negation: '!debug.mode']
    I --> M[Wildcard Negation: '!document.editor.*']
```

**Priority Order (Highest to Lowest):**
1. **User Flags**: Individual user feature flags from `profiles.feature_flags[]`
2. **Organization Flags**: Organization-level flags from `acc_organisations.feature_flags[]`
3. **Default Flags**: System-wide default flags from `DEFAULT_FEATURE_FLAGS`

### Database Schema Integration

The authentication context integrates with the customer database schema:

**Profiles Table**: `profiles`
- `id`: Primary key matching Supabase auth user ID
- `email`: User email address
- `name`: Display name
- `full_name`: Complete user name
- `avatar_url`: Profile image reference in Supabase storage
- `is_admin`: Boolean admin status (database-controlled)
- `feature_flags[]`: Array of user-specific feature flags
- `organisation`: Foreign key to `acc_organisations`
- `welcome_message`: Customizable welcome text

**Organizations Table**: `acc_organisations`
- `id`: Primary key
- `name`: Organization name
- `email_domain`: Associated email domain
- `feature_flags[]`: Array of organization feature flags
- `entity_xid`: Associated ESG entity identifier

## Key Components

### AuthProvider (`auth-context.tsx`)

**Purpose**: Main context provider component that wraps the application and manages authentication state.

**Core Functionality**:
- Supabase session management with automatic synchronization
- User profile fetching with admin status resolution
- Organization feature flag loading for hierarchical permissions
- Avatar URL generation with signed storage URLs
- Automatic login redirect with return URL preservation
- Sentry user context integration for error tracking

**Key Methods**:
- `fetchProfile(user)`: Retrieves user profile and organization data
- `getUserProfile()`: Initial user authentication check
- `checkFeature(flagName)`: Hierarchical feature flag evaluation

### useAuth Hook (`auth-context.tsx`)

**Purpose**: Custom React hook for consuming authentication context.

**Returns**:
- `user`: Supabase User object or null
- `profile`: User profile data from database
- `admin`: Boolean admin status
- `hasFeature(flagName)`: Function for feature flag checking

**Usage Pattern**:
```typescript
const { user, profile, admin, hasFeature } = useAuth();

if (!user) return <LoginPrompt />;

return (
  <div>
    <h1>Welcome, {profile?.name}</h1>
    {admin && <AdminPanel />}
    {hasFeature('dashboard.analytics') && <Analytics />}
  </div>
);
```

### Feature Flags Helper (`feature-flags-helper.ts`)

**Purpose**: Environment-aware feature flag enhancement for testing scenarios.

**Core Functionality**:
- Automatic detection of development/test environments
- Enhanced feature flags for AI tools in test environments
- Browser detection for headless testing (Playwright)
- CI/CD environment recognition

**Enhanced Test Flags**:
- `document.editor.ai.tools`: AI-powered document editing
- `document.editor.ai.chat`: Interactive AI chat interface
- `document.editor.ai.edit`: AI content editing suggestions
- `navigation.help.docs`: Enhanced help documentation

### TestAuthProvider (`test-auth-provider.tsx`)

**Purpose**: Test-aware wrapper that ensures proper feature flag initialization in testing environments.

**Functionality**:
- Wraps standard AuthProvider with test enhancements
- Exports enhanced default feature flags for test utilities
- Provides clean interface for test component mounting

### Test Suites

**Authentication Context Tests** (`auth-context.test.tsx`):
- Complete provider lifecycle testing
- User authentication and profile loading
- Feature flag system validation
- Error handling and edge cases
- Auth state change management
- Mock Supabase client integration

**Feature Flags Helper Tests** (`feature-flags-helper.test.tsx`):
- Environment detection validation
- Test flag enhancement verification
- Server-side rendering compatibility
- CI environment recognition

## Dependencies

### Core Dependencies

**External Libraries**:
- `@supabase/supabase-js`: Supabase client for authentication and database
- `next/navigation`: Next.js App Router navigation hooks
- `@sentry/nextjs`: Error tracking and user context management
- `react`: React 18+ with Context API and hooks

**Internal Dependencies**:
- `/app/supabase/client`: Configured Supabase client instance
- `/types`: TypeScript definitions including `ProfileType`
- `/utils/feature-flags`: Core feature flag system and utilities

**Database Dependencies**:
- Customer database with `profiles` and `acc_organisations` tables
- Row Level Security (RLS) policies for secure data access
- Supabase Auth service for JWT token management
- Supabase Storage for avatar image management

### Integration Points

**Next.js Integration**:
- `useRouter()`: Navigation and login redirects
- `usePathname()`: Current route detection for redirect loops prevention
- App Router compatibility with server/client component patterns

**Supabase Integration**:
- `auth.getUser()`: Initial session verification
- `auth.onAuthStateChange()`: Real-time auth state monitoring
- `from('profiles')`: User profile data fetching
- `from('acc_organisations')`: Organization feature flags
- `storage.from('avatars')`: Avatar image signed URLs

## Usage Examples

### Basic Authentication Check

```typescript
import { useAuth } from '@/components/context/auth/auth-context';

function Dashboard() {
  const { user, profile, admin } = useAuth();

  if (!user) {
    return <div>Please log in to access the dashboard</div>;
  }

  return (
    <div>
      <header>
        <h1>Welcome back, {profile?.name || user.email}</h1>
        {admin && <AdminBadge />}
      </header>
      <DashboardContent />
    </div>
  );
}
```

### Feature Flag Conditional Rendering

```typescript
import { useAuth } from '@/components/context/auth/auth-context';

function DocumentEditor() {
  const { hasFeature } = useAuth();

  return (
    <div className="editor-container">
      <EditorToolbar />
      
      {/* AI features available in test/dev environments */}
      {hasFeature('document.editor.ai.tools') && (
        <AIToolsPanel />
      )}
      
      {/* Advanced features for specific users/orgs */}
      {hasFeature('document.editor.advanced.*') && (
        <AdvancedFeatures />
      )}
      
      {/* Explicitly disabled features */}
      {!hasFeature('!document.editor.markdown') && (
        <MarkdownSupport />
      )}
      
      <EditorContent />
    </div>
  );
}
```

### App-Level Provider Setup

```typescript
// app/layout.tsx
import { AuthProvider } from '@/components/context/auth/auth-context';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <AuthProvider>
          <div className="app">
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
```

### Testing Component with Auth Context

```typescript
// Using TestAuthProvider for enhanced test capabilities
import { TestAuthProvider } from '@/components/context/auth/test-auth-provider';

function TestComponent() {
  return (
    <TestAuthProvider>
      <MyComponentThatUsesAuth />
    </TestAuthProvider>
  );
}
```

## Architecture Notes

### State Management Pattern

The authentication context uses React's built-in Context API with `useState` for state management, providing:

- **Single Source of Truth**: Centralized authentication state across the application
- **Automatic Re-renders**: Components automatically update when auth state changes
- **Memory Efficiency**: Single context subscription prevents multiple auth listeners
- **Performance Optimization**: Memoized context values prevent unnecessary re-renders

### Security Architecture

The module implements multiple layers of security:

```mermaid
graph TD
    A[Client Request] --> B[JWT Authentication]
    B --> C[Row Level Security]
    C --> D[Feature Flag Authorization]
    D --> E[Admin Role Verification]
    E --> F[Data Access Granted]

    B --> G[Invalid JWT]
    G --> H[Redirect to Login]
    
    C --> I[RLS Policy Denied]
    I --> J[403 Forbidden]
    
    D --> K[Feature Disabled]
    K --> L[Feature Hidden/Disabled]
```

**Security Layers**:
1. **JWT Authentication**: Supabase handles secure token management
2. **Row Level Security**: PostgreSQL RLS policies filter data by user
3. **Feature Authorization**: Hierarchical feature flag evaluation
4. **Admin Protection**: Database-controlled admin flags
5. **Session Security**: Automatic cleanup and refresh token rotation

### Performance Optimizations

**Efficient Authentication Management**:
- Single `onAuthStateChange` subscription to prevent memory leaks
- Avatar URL caching with 1-hour signed URL expiration
- Profile data caching in React state to minimize database queries
- Organization data lazy loading only when user belongs to organization

**Context Value Memoization**:
```typescript
const contextValue = useMemo(() => ({
  user,
  profile,
  admin: isAdmin,
  hasFeature: checkFeature,
}), [user, profile, isAdmin, orgFeatureFlags]);
```

### Error Handling Strategy

The module implements graceful error handling:

- **Network Errors**: Continues with limited functionality when Supabase is unavailable
- **Profile Errors**: Continues without profile data rather than blocking app
- **Redirect Protection**: Prevents infinite redirect loops by checking current pathname
- **Sentry Integration**: Comprehensive error tracking with user context
- **State Consistency**: Ensures auth state remains consistent during error conditions

## Known Issues

### Current Limitations

1. **Avatar Processing**: Avatar URL generation requires additional API call to Supabase Storage for signed URLs, which could be optimized with caching strategies.

2. **Organization Loading**: Organization feature flags are fetched sequentially after profile load, which could be optimized with parallel loading.

3. **Error Boundary**: The context doesn't implement error boundaries, so authentication errors can potentially crash the entire app tree.

4. **Session Persistence**: Local session state is lost on page refresh until Supabase session is restored, causing brief loading states.

### Related Linear Issues

Based on git commit analysis, the following Linear tickets relate to authentication functionality:

- **EKO-269**: Changelog system behind `welcome.changelog` feature flag
- **EKO-280**: Welcome page customizable welcome message in profiles table
- **EKO-296/EKO-297**: Supabase client configuration errors requiring URL and key validation

## Future Work

### Planned Enhancements

1. **Enhanced Error Boundaries**: Implement authentication-specific error boundaries with retry mechanisms and graceful degradation.

2. **Session Persistence Optimization**: Add localStorage backup for session state to eliminate loading flickers on page refresh.

3. **Parallel Data Loading**: Optimize profile and organization data fetching with `Promise.all()` for faster initial load times.

4. **Advanced Feature Flags**: 
   - Time-based feature flags with activation/expiration dates
   - A/B testing support with percentage-based rollouts
   - Geographic feature flag restrictions

5. **Multi-tenancy Support**: Enhanced organization switching for users belonging to multiple organizations.

6. **Authentication Analytics**: Track authentication events, feature flag usage, and user engagement metrics.

### Technical Debt

1. **Type Safety**: Strengthen TypeScript types for feature flag patterns and validation
2. **Test Coverage**: Expand test coverage for edge cases and error scenarios
3. **Documentation**: Add inline JSDoc comments for better IDE support
4. **Performance Monitoring**: Add authentication performance metrics and monitoring

## Troubleshooting

### Common Issues

**Authentication Loop/Redirect Issues**:
```bash
# Symptom: Infinite redirect to login page
# Solution: Check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY
# Verify RLS policies allow profile access for authenticated users
```

**Feature Flags Not Working**:
```bash
# Symptom: hasFeature() always returns false
# Solution: Check feature flag format and hierarchy
# Verify user has proper profile.feature_flags or organization.feature_flags
# Check DEFAULT_FEATURE_FLAGS includes expected flags
```

**Profile Data Missing**:
```bash
# Symptom: profile is null despite authenticated user
# Solution: Verify profiles table has record for user.id
# Check RLS policies allow SELECT on profiles table
# Confirm foreign key constraints for organization table
```

**Avatar Images Not Loading**:
```bash
# Symptom: avatar_url is empty or gives 403 errors
# Solution: Check Supabase Storage bucket permissions
# Verify avatar files exist in 'avatars' storage bucket
# Confirm signed URL generation has proper permissions
```

### Development Debugging

**Enable Debug Logging**:
```typescript
// Add to auth-context.tsx for development debugging
console.log('Auth state:', { user, profile, isAdmin, orgFeatureFlags });
```

**Feature Flag Testing**:
```bash
# Test feature flags in browser console
localStorage.setItem('debug-feature-flags', 'true');
# This enables additional console logging for feature flag evaluation
```

**Supabase Connection Testing**:
```bash
# Test Supabase connection
curl -H "apikey: YOUR_ANON_KEY" \
     -H "Authorization: Bearer YOUR_JWT" \
     "YOUR_SUPABASE_URL/rest/v1/profiles?select=*"
```

## FAQ

### User-Centric Questions and Answers

**Q: Why do I see a loading screen when refreshing the page?**
A: The authentication context needs to restore your session from Supabase on page refresh. This typically takes 100-200ms. The loading screen ensures the app doesn't show unauthenticated content before your session is restored.

**Q: How do feature flags work and why can't I see certain features?**
A: Feature flags control which features are available to you based on three levels: your personal settings, your organization's settings, and system defaults. Features may be disabled for testing, limited to certain user groups, or behind paid tiers.

**Q: What happens to my data if I lose internet connection?**
A: Your authentication session is cached locally, so you can continue using the app offline. However, any data changes will queue until your internet connection is restored and will sync automatically.

**Q: Why do I sometimes need to log in again?**
A: Authentication tokens expire after 1 hour for security. The app automatically refreshes them, but if you're offline or there's a network issue during refresh, you may need to log in again.

**Q: Can I belong to multiple organizations?**
A: Currently, each user can belong to one organization at a time. Multi-organization support is planned for future releases.

**Q: How secure is my profile data?**
A: Your profile data is protected by multiple security layers: JWT authentication, PostgreSQL Row Level Security (RLS), and encrypted database storage. Only you and system administrators can access your profile information.

**Q: Why can't I access admin features even though I'm supposed to be an admin?**
A: Admin status is controlled at the database level and can only be modified by system administrators, not through the user interface. Contact your system administrator if you believe you should have admin access.

**Q: What information does the app collect about me?**
A: The app stores your email, name, profile picture, organization membership, feature flag preferences, and usage analytics. This data is used to provide personalized ESG analysis and improve the platform. All data collection follows privacy regulations.

## References

### Documentation Links

- [Supabase Authentication Documentation](https://supabase.com/docs/guides/auth) - Official Supabase auth guide
- [Next.js App Router Documentation](https://nextjs.org/docs/app/building-your-application/routing) - Next.js routing system
- [React Context API Documentation](https://react.dev/reference/react/createContext) - React Context pattern
- [Sentry Next.js Integration](https://docs.sentry.io/platforms/javascript/guides/nextjs/) - Error tracking setup

### Related Code Files

- [`/apps/customer/app/supabase/client.ts`](../../../app/supabase/client.ts) - Supabase client configuration
- [`/apps/customer/utils/feature-flags.ts`](../../../utils/feature-flags.ts) - Core feature flag system
- [`/apps/customer/types.ts`](../../../types.ts) - TypeScript definitions including ProfileType
- [`/apps/customer/database.types.ts`](../../../database.types.ts) - Generated Supabase database types

### Related README Files

- [`/apps/customer/README.md`](../../../README.md) - Customer application overview
- [`/apps/customer/tests/README.md`](../../../tests/README.md) - Testing strategies and patterns
- [`/README.md`](../../../../../README.md) - EkoIntelligence platform overview

### External Dependencies

- [Supabase JavaScript Client](https://github.com/supabase/supabase-js) - Official Supabase SDK
- [Sentry JavaScript SDK](https://github.com/getsentry/sentry-javascript) - Error tracking and monitoring
- [Next.js](https://github.com/vercel/next.js) - React framework with App Router
- [React](https://github.com/facebook/react) - JavaScript library for user interfaces

---

## Changelog

### 2025-07-31
- **Created**: Comprehensive README.md documentation for authentication context module
- **Added**: Architecture diagrams using Mermaid for authentication flow and feature flag hierarchy
- **Documented**: Complete API surface, usage patterns, and troubleshooting guide
- **Included**: FAQ section with user-centric questions and practical troubleshooting steps

(c) All rights reserved ekoIntelligence 2025