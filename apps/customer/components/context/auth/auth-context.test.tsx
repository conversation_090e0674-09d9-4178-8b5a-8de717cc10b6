import React from 'react'
import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor, cleanup, renderHook, act } from '@testing-library/react'
import { User } from '@supabase/supabase-js'
import { AuthProvider, useAuth } from './auth-context'
import { ProfileType } from '@/types'

// Mock Next.js navigation
const mockPush = vi.fn()
const mockPathname = '/dashboard'

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  usePathname: () => mockPathname,
}))

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    getUser: vi.fn(),
    onAuthStateChange: vi.fn(),
  },
  from: vi.fn(),
  storage: {
    from: vi.fn(),
  },
}

vi.mock('@/app/supabase/client', () => ({
  createClient: () => mockSupabaseClient,
}))

// Mock feature flags
vi.mock('@/utils/feature-flags', () => ({
  DEFAULT_FEATURE_FLAGS: ['test.flag'],
  hasFeature: vi.fn((flagName) => flagName === 'user.flag'),
  normalizeFlags: vi.fn((flags) => flags || []),
}))

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/dashboard',
    href: 'https://example.com/dashboard',
  },
  writable: true,
})

const mockUser: User = {
  id: 'user-123',
  email: '<EMAIL>',
  created_at: '2023-01-01T00:00:00Z',
  app_metadata: {},
  user_metadata: {},
  aud: 'authenticated',
  role: 'authenticated',
} as User

const mockProfile: ProfileType = {
  id: 'user-123',
  name: 'Test User',
  email: '<EMAIL>',
  avatar_url: null,
  is_admin: false,
  organisation: 123,
  feature_flags: ['user.flag'],
  full_name: 'Test User',
  updated_at: '2023-01-01T00:00:00Z',
  username: 'testuser',
  website: null,
  welcome_message: null,
}

afterEach(() => {
  cleanup()
  vi.clearAllMocks()
})

describe('AuthProvider', () => {
  beforeEach(() => {
    // Setup default mock implementations
    mockSupabaseClient.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    })

    const mockSubscription = {
      unsubscribe: vi.fn(),
    }

    mockSupabaseClient.auth.onAuthStateChange.mockReturnValue({
      data: { subscription: mockSubscription },
    })

    // Mock database queries
    mockSupabaseClient.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockProfile,
            error: null,
          }),
        }),
      }),
    })

    // Mock storage
    mockSupabaseClient.storage.from.mockReturnValue({
      createSignedUrl: vi.fn().mockResolvedValue({
        data: { signedUrl: 'https://example.com/avatar.jpg' },
        error: null,
      }),
    })
  })

  describe('Basic Rendering', () => {
    test('renders children without crashing', async () => {
      await act(async () => {
        render(
          <AuthProvider>
            <div data-testid="child">Test Child</div>
          </AuthProvider>
        )
      })

      expect(screen.getByTestId('child')).toBeInTheDocument()
    })

    test('provides auth context to children', async () => {
      const TestComponent = () => {
        const { user, profile, admin, hasFeature } = useAuth()
        return (
          <div>
            <div data-testid="user-id">{user?.id || 'no-user'}</div>
            <div data-testid="profile-name">{profile?.name || 'no-profile'}</div>
            <div data-testid="admin-status">{admin ? 'admin' : 'not-admin'}</div>
            <div data-testid="has-feature">{hasFeature('user.flag') ? 'has-feature' : 'no-feature'}</div>
          </div>
        )
      }

      await act(async () => {
        render(
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        )
      })

      // Check that hasFeature function is available and working
      expect(screen.getByTestId('has-feature')).toHaveTextContent('has-feature')

      // Wait for user/profile to load
      await waitFor(() => {
        expect(screen.getByTestId('user-id')).toHaveTextContent('user-123')
      })

      await waitFor(() => {
        expect(screen.getByTestId('profile-name')).toHaveTextContent('Test User')
      })

      await waitFor(() => {
        expect(screen.getByTestId('admin-status')).toHaveTextContent('not-admin')
      })
    })
  })

  describe('User Authentication', () => {
    test('loads user on mount', async () => {
      const TestComponent = () => {
        const { user } = useAuth()
        return <div data-testid="user-id">{user?.id || 'loading'}</div>
      }

      await act(async () => {
        render(
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        )
      })

      await waitFor(() => {
        expect(screen.getByTestId('user-id')).toHaveTextContent('user-123')
      })

      expect(mockSupabaseClient.auth.getUser).toHaveBeenCalled()
    })

    test('handles authentication error', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Authentication failed' },
      })

      const TestComponent = () => {
        const { user } = useAuth()
        return <div data-testid="user-id">{user?.id || 'no-user'}</div>
      }

      await act(async () => {
        render(
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        )
      })

      await waitFor(() => {
        expect(screen.getByTestId('user-id')).toHaveTextContent('no-user')
      })

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/login?next=https%3A%2F%2Fexample.com%2Fdashboard')
      })
    })

    test('does not redirect if already on login page', async () => {
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/login',
          href: 'https://example.com/login',
        },
        writable: true,
      })

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Authentication failed' },
      })

      await act(async () => {
        render(
          <AuthProvider>
            <div>Test</div>
          </AuthProvider>
        )
      })

      await waitFor(() => {
        expect(mockSupabaseClient.auth.getUser).toHaveBeenCalled()
      })

      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  describe('Profile Management', () => {
    test('loads user profile when user is available', async () => {
      const TestComponent = () => {
        const { profile } = useAuth()
        return <div data-testid="profile-name">{profile?.name || 'loading'}</div>
      }

      await act(async () => {
        render(
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        )
      })

      await waitFor(() => {
        expect(screen.getByTestId('profile-name')).toHaveTextContent('Test User')
      })

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('profiles')
    })

    test('handles profile fetch error gracefully', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Profile not found' },
            }),
          }),
        }),
      })

      const TestComponent = () => {
        const { profile, admin } = useAuth()
        return (
          <div>
            <div data-testid="profile-name">{profile?.name || 'no-profile'}</div>
            <div data-testid="admin-status">{admin ? 'admin' : 'not-admin'}</div>
          </div>
        )
      }

      await act(async () => {
        render(
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        )
      })

      await waitFor(() => {
        expect(screen.getByTestId('profile-name')).toHaveTextContent('no-profile')
      })

      expect(screen.getByTestId('admin-status')).toHaveTextContent('not-admin')
    })

    test('sets admin status from profile', async () => {
      const adminProfile = { ...mockProfile, is_admin: true }
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: adminProfile,
              error: null,
            }),
          }),
        }),
      })

      const TestComponent = () => {
        const { admin } = useAuth()
        return <div data-testid="admin-status">{admin ? 'admin' : 'not-admin'}</div>
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('admin-status')).toHaveTextContent('admin')
      })
    })

    test('handles avatar URL creation', async () => {
      const profileWithAvatar = { ...mockProfile, avatar_url: 'avatar.jpg' }
      
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: profileWithAvatar,
              error: null,
            }),
          }),
        }),
      })

      const TestComponent = () => {
        const { profile } = useAuth()
        return <div data-testid="avatar-url">{profile?.avatar_url || 'no-avatar'}</div>
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('avatar-url')).toHaveTextContent('https://example.com/avatar.jpg')
      })

      expect(mockSupabaseClient.storage.from).toHaveBeenCalledWith('avatars')
    })
  })

  describe('Organization Feature Flags', () => {
    test('fetches organization feature flags when user has organization', async () => {
      const orgFlagsResponse = {
        data: { feature_flags: ['org.flag1', 'org.flag2'] },
        error: null,
      }

      mockSupabaseClient.from
        .mockReturnValueOnce({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: mockProfile,
                error: null,
              }),
            }),
          }),
        })
        .mockReturnValueOnce({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue(orgFlagsResponse),
            }),
          }),
        })

      render(
        <AuthProvider>
          <div>Test</div>
        </AuthProvider>
      )

      await waitFor(() => {
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('acc_organisations')
      })
    })

    test('handles missing organization gracefully', async () => {
      const profileWithoutOrg = { ...mockProfile, organisation: null }
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: profileWithoutOrg,
              error: null,
            }),
          }),
        }),
      })

      render(
        <AuthProvider>
          <div>Test</div>
        </AuthProvider>
      )

      await waitFor(() => {
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('profiles')
      })

      // Should not call organization table
      expect(mockSupabaseClient.from).not.toHaveBeenCalledWith('acc_organisations')
    })
  })

  describe('Feature Flag System', () => {
    test('hasFeature function is available', async () => {
      const TestComponent = () => {
        const { hasFeature } = useAuth()
        return <div data-testid="has-feature">{typeof hasFeature}</div>
      }

      await act(async () => {
        render(
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        )
      })

      expect(screen.getByTestId('has-feature')).toHaveTextContent('function')
    })

    test('hasFeature calls feature flag utility with correct config', async () => {
      const { hasFeature: mockHasFeature } = await import('@/utils/feature-flags')
      
      const TestComponent = () => {
        const { hasFeature } = useAuth()
        React.useEffect(() => {
          hasFeature('test.flag')
        }, [hasFeature])
        return <div>Test</div>
      }

      await act(async () => {
        render(
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        )
      })

      await waitFor(() => {
        expect(mockHasFeature).toHaveBeenCalledWith('test.flag', expect.objectContaining({
          userFlags: expect.any(Array),
          orgFlags: expect.any(Array),
          defaultFlags: expect.any(Array),
        }))
      })
    })
  })

  describe('Auth State Changes', () => {
    test('sets up auth state change listener', async () => {
      await act(async () => {
        render(
          <AuthProvider>
            <div>Test</div>
          </AuthProvider>
        )
      })

      expect(mockSupabaseClient.auth.onAuthStateChange).toHaveBeenCalled()
    })

    test('unsubscribes from auth state changes on unmount', async () => {
      const mockUnsubscribe = vi.fn()
      mockSupabaseClient.auth.onAuthStateChange.mockReturnValue({
        data: { subscription: { unsubscribe: mockUnsubscribe } },
      })

      let unmount: () => void
      await act(async () => {
        const result = render(
          <AuthProvider>
            <div>Test</div>
          </AuthProvider>
        )
        unmount = result.unmount
      })

      unmount!()

      expect(mockUnsubscribe).toHaveBeenCalled()
    })

    test('handles auth state change events', async () => {
      let authStateCallback: (event: string, session: any) => void

      mockSupabaseClient.auth.onAuthStateChange.mockImplementation((callback) => {
        authStateCallback = callback
        return {
          data: { subscription: { unsubscribe: vi.fn() } },
        }
      })

      const TestComponent = () => {
        const { user } = useAuth()
        return <div data-testid="user-id">{user?.id || 'no-user'}</div>
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByTestId('user-id')).toHaveTextContent('user-123')
      })

      // Simulate auth state change
      const newUser = { ...mockUser, id: 'new-user-456' }
      act(() => {
        authStateCallback!('SIGNED_IN', { user: newUser })
      })

      await waitFor(() => {
        expect(screen.getByTestId('user-id')).toHaveTextContent('new-user-456')
      })
    })
  })

  describe('Error Handling', () => {
    test('handles Supabase client errors gracefully', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: null,
        error: { message: 'Network error' },
      })

      const TestComponent = () => {
        const { user } = useAuth()
        return <div data-testid="user-id">{user?.id || 'error'}</div>
      }

      await act(async () => {
        render(
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        )
      })

      await waitFor(() => {
        expect(screen.getByTestId('user-id')).toHaveTextContent('error')
      })
    })

    test('handles console.log calls without crashing', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      render(
        <AuthProvider>
          <div>Test</div>
        </AuthProvider>
      )

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalled()
      })

      consoleSpy.mockRestore()
    })
  })
})

describe('useAuth Hook', () => {
  test('throws error when used outside AuthProvider', () => {
    const TestComponent = () => {
      useAuth()
      return <div>Test</div>
    }

    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    expect(() => {
      render(<TestComponent />)
    }).toThrow('useAuth must be used within a UserProvider')

    consoleSpy.mockRestore()
  })

  test('returns auth context when used within AuthProvider', async () => {
    // Setup mock subscription properly for renderHook
    const mockUnsubscribe = vi.fn()
    mockSupabaseClient.auth.onAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: mockUnsubscribe } },
    })

    const { result } = renderHook(() => useAuth(), {
      wrapper: ({ children }) => <AuthProvider>{children}</AuthProvider>,
    })

    expect(result.current).toHaveProperty('user')
    expect(result.current).toHaveProperty('profile')
    expect(result.current).toHaveProperty('admin')
    expect(result.current).toHaveProperty('hasFeature')
    expect(typeof result.current.hasFeature).toBe('function')
  })
})