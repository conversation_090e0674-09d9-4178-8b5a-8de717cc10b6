/**
 * Authentication Context Provider for EkoIntelligence ESG Analysis Platform
 *
 * This React Context provider implements comprehensive authentication and authorization management
 * for the EkoIntelligence ESG analysis customer application. Built on Supabase authentication
 * with PostgreSQL Row Level Security (RLS), it manages user sessions, profile data, admin status,
 * and hierarchical feature flag evaluation while providing seamless integration with Next.js 15
 * App Router navigation and Sentry error tracking.
 *
 * ## Core Functionality
 * - **Authentication Management**: Handles Supabase user sessions with automatic state synchronization
 * - **Profile Integration**: Fetches and manages user profile data from PostgreSQL with avatar support
 * - **Admin Authorization**: Provides role-based access control through database-driven admin flags
 * - **Feature Flag System**: Implements hierarchical feature flag evaluation (user > org > default)
 * - **Organization Support**: Manages organizational membership and organization-level feature flags
 * - **Navigation Integration**: Auto-redirects unauthenticated users with return URL preservation
 * - **Error Tracking**: Integrates with Sentry for user context and error monitoring
 *
 * ## Architecture Pattern
 * **Provider Pattern with Hierarchical Authorization**: Implements React Context pattern combined
 * with a sophisticated three-tier authorization model:
 *
 * 1. **User Authentication**: Supabase JWT-based authentication with automatic session management
 * 2. **Profile Authorization**: Database-driven user profiles with admin status and feature flags
 * 3. **Organization Context**: Organizational membership with inherited feature flags and permissions
 *
 * **Database Schema Integration**: Works directly with customer database schema:
 * - `profiles` table: User profiles (id, email, name, avatar_url, is_admin, feature_flags[], organisation)
 * - `acc_organisations` table: Organizations (id, name, email_domain, feature_flags[], entity_xid)
 * - **RLS Policies**: Automatic Row Level Security enforcement based on authenticated user context
 * - **Foreign Key Relationships**: Profiles link to organizations for hierarchical permissions
 *
 * ## Feature Flag System
 * **Three-Tier Hierarchical Evaluation**: Implements sophisticated feature flag resolution:
 * 1. **User Flags**: Individual user feature flags (highest priority)
 * 2. **Organization Flags**: Organization-level feature flags (medium priority)
 * 3. **Default Flags**: System-wide default feature flags (lowest priority)
 *
 * **Pattern Support**: Supports advanced flag patterns including:
 * - Exact matching: `dashboard.analytics` enables specific features
 * - Wildcard patterns: `document.*` enables all document-related features
 * - Negation flags: `!document.editor.ai` explicitly disables AI editor features
 * - Wildcard negation: `!debug.*` disables all debug-related features
 *
 * ## Authentication Flow
 * **Session Lifecycle Management**:
 * 1. **Initial Load**: Checks existing Supabase session and fetches user profile
 * 2. **Auth State Changes**: Listens to Supabase auth events for session updates
 * 3. **Profile Fetching**: Retrieves user profile data including admin status and feature flags
 * 4. **Organization Loading**: Fetches organization feature flags when user belongs to organization
 * 5. **Avatar Processing**: Generates signed URLs for user avatar images with expiration
 * 6. **Error Handling**: Redirects to login with return URL for authentication failures
 *
 * ## Integration Context
 * This context is the foundation of the EkoIntelligence customer application authentication:
 * - **Next.js App Router**: Uses `useRouter` and `usePathname` for navigation management
 * - **Supabase Client**: Leverages `createClient()` for type-safe database operations
 * - **Sentry Integration**: Sets user context for error tracking and debugging
 * - **Component Access Control**: Provides `hasFeature()` for conditional UI rendering
 * - **API Authorization**: User context flows to API routes for server-side authorization
 * - **ESG Data Access**: User profile determines accessible entities and analysis runs
 *
 * ## Security Considerations
 * **Multi-Layer Security Architecture**:
 * - **JWT Authentication**: Supabase handles secure JWT token management with automatic refresh
 * - **Row Level Security**: PostgreSQL RLS policies automatically filter data based on user context
 * - **Admin Protection**: `is_admin` flag can only be modified by database administrators, not users
 * - **Feature Flag Security**: Server-side validation ensures feature flags cannot be client-side manipulated
 * - **Session Persistence**: Secure session storage with automatic cleanup on logout
 * - **CSRF Protection**: Supabase provides built-in CSRF protection for authentication flows
 *
 * ## Performance Optimizations
 * **Efficient State Management**:
 * - **Single Auth Listener**: Uses single `onAuthStateChange` subscription to prevent memory leaks
 * - **Selective Re-renders**: Context value memoization prevents unnecessary child re-renders
 * - **Avatar Caching**: Signed URLs cached for 1 hour to reduce storage API calls
 * - **Profile Caching**: Profile data cached in React state to minimize database queries
 * - **Organization Lazy Loading**: Organization data fetched only when user belongs to organization
 *
 * ## Usage Patterns
 * **Typical Component Integration**:
 * ```typescript
 * // App-level provider setup
 * function RootLayout({ children }) {
 *   return (
 *     <AuthProvider>
 *       <div className="app">
 *         {children}
 *       </div>
 *     </AuthProvider>
 *   );
 * }
 *
 * // Component-level authentication
 * function Dashboard() {
 *   const { user, profile, admin, hasFeature } = useAuth();
 *
 *   if (!user) return <LoginPrompt />;
 *
 *   return (
 *     <div>
 *       <h1>Welcome, {profile?.name}</h1>
 *       {admin && <AdminPanel />}
 *       {hasFeature('dashboard.analytics') && <Analytics />}
 *       {hasFeature('document.*') && <DocumentEditor />}
 *     </div>
 *   );
 * }
 * ```
 *
 * ## Error Handling Strategy
 * **Graceful Authentication Failures**:
 * - **Network Errors**: Graceful handling of Supabase connection failures
 * - **Profile Errors**: Continues with limited functionality when profile fetch fails
 * - **Redirect Protection**: Prevents redirect loops by checking current pathname
 * - **Sentry Integration**: Comprehensive error tracking with user context for debugging
 * - **State Consistency**: Ensures auth state remains consistent even during error conditions
 *
 * @see https://supabase.com/docs/guides/auth Supabase Authentication Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing Next.js App Router
 * @see https://react.dev/reference/react/createContext React Context API
 * @see /Users/<USER>/worktrees/279/apps/customer/utils/feature-flags.ts Feature Flag Utility Functions
 * @see /Users/<USER>/worktrees/279/apps/customer/app/supabase/client.ts Supabase Client Configuration
 * @see /Users/<USER>/worktrees/279/apps/customer/types.ts ProfileType Interface Definition
 * <AUTHOR>
 * @updated 2025-07-23
 * @description React Context provider for authentication, authorization, and feature flag management in the ESG analysis platform
 * @example
 * ```typescript
 * // Provider setup at app root
 * import { AuthProvider } from '@/components/context/auth/auth-context';
 *
 * export default function RootLayout({ children }) {
 *   return (
 *     <html>
 *       <body>
 *         <AuthProvider>
 *           {children}
 *         </AuthProvider>
 *       </body>
 *     </html>
 *   );
 * }
 *
 * // Using authentication in components
 * import { useAuth } from '@/components/context/auth/auth-context';
 *
 * function MyComponent() {
 *   const { user, profile, admin, hasFeature } = useAuth();
 *
 *   // Check authentication
 *   if (!user) return <div>Please log in</div>;
 *
 *   // Role-based access
 *   if (admin) {
 *     return <AdminDashboard />;
 *   }
 *
 *   // Feature flag conditional rendering
 *   return (
 *     <div>
 *       <UserDashboard />
 *       {hasFeature('dashboard.analytics') && <AnalyticsPanel />}
 *       {hasFeature('document.editor.*') && <DocumentTools />}
 *       {!hasFeature('!debug.mode') && <DebugInfo />}
 *     </div>
 *   );
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'

import { createContext, ReactNode, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { createClient } from '@/app/supabase/client'
import { ProfileType } from '@/types'
import { usePathname, useRouter } from 'next/navigation'
import { DEFAULT_FEATURE_FLAGS, FeatureFlagConfig, hasFeature, normalizeFlags } from '@/utils/feature-flags'
import * as Sentry from '@sentry/nextjs'

interface AuthContextType {
  user: User | null;
  admin: boolean;
  profile: ProfileType | null;
  hasFeature: (flagName: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface UserProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: UserProviderProps) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<ProfileType | null>(null)
  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  const [orgFeatureFlags, setOrgFeatureFlags] = useState<string[]>([])
  const supabase = createClient()
  const router = useRouter()
  const pathname = usePathname()

  const fetchProfile = async (user: User | null) => {
    console.log('fetching profile for', user)
    if (user) {
      const profileResponse = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileResponse.data) {
        // Set admin status from database
        setIsAdmin(!!profileResponse.data.is_admin)
        Sentry.setUser({
          fullName: profileResponse.data.full_name,
          email: profileResponse.data.email ?? '',
        })

        if (profileResponse.data.avatar_url) {
          const avatarData = await supabase.storage
            .from('avatars')
            .createSignedUrl(profileResponse.data.avatar_url, 3600)
          setProfile({
            ...profileResponse.data,
            avatar_url: avatarData.data?.signedUrl ?? '',
          })
        } else {
          setProfile(profileResponse.data)
        }

        // Fetch organisation feature flags if user has an organisation
        if (profileResponse.data.organisation) {
          const orgResponse = await supabase
            .from('acc_organisations')
            .select('feature_flags')
            .eq('id', profileResponse.data.organisation)
            .single()

          if (orgResponse.data) {
            setOrgFeatureFlags(normalizeFlags(orgResponse.data.feature_flags))
            console.log('org_flags', orgResponse.data.feature_flags)
          } else {
            console.log('no org_flags', orgResponse)
          }
        } else {
          console.log('no org')
        }
      } else {
        setProfile(null)
        setIsAdmin(false)
        setOrgFeatureFlags([])
        console.log('no profile')
      }
    } else {
      setProfile(null)
      setIsAdmin(false)
      setOrgFeatureFlags([])
      console.log('no user')
    }
  }

  const getUserProfile = async () => {
    try {
      const result = await supabase.auth.getUser()
      const { data, error } = result || { data: null, error: null }
      const currentUser = data?.user
      console.log('current user', currentUser)
      if (error || !currentUser) {
        console.error('error getting user', error)
        setUser(null)
        setProfile(null)
        setIsAdmin(false)
        if (!window.location.pathname.startsWith('/login')) {
          router.push(`/login?next=${encodeURIComponent(window.location.href)}`)
        }
        return
      }

      setUser(currentUser)
      fetchProfile(currentUser)

    } catch (err) {
      console.error('error getting user', err)
      setUser(null)
      setProfile(null)
      setIsAdmin(false)
    }
  }

  const checkFeature = (flagName: string): boolean => {
    const config: FeatureFlagConfig = {
      userFlags: normalizeFlags(profile?.feature_flags),
      orgFlags: orgFeatureFlags,
      defaultFlags: DEFAULT_FEATURE_FLAGS,
    }
    return hasFeature(flagName, config)
  }

  useEffect(() => {
    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      console.info('Auth state changed')
      const currentUser = session?.user || null
      setUser(currentUser)
      fetchProfile(currentUser) // Fetch only if the user changes
    })

    // Initial user check
    getUserProfile()

    return () => {
      subscription.unsubscribe()
    }
  }, []) // Remove the pathname dependency to prevent infinite loops

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        admin: isAdmin,
        hasFeature: checkFeature,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within a UserProvider')
  }
  return context
}
