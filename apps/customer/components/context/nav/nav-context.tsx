/**
 * Navigation Context Provider for Dynamic Title and Breadcrumb Management - EkoIntelligence Customer App
 *
 * This React Context system provides centralized navigation state management for the EkoIntelligence
 * ESG analysis platform's customer-facing application. It integrates with Next.js 15 App Router to
 * automatically manage page titles, navigation breadcrumbs, and routing state across the application,
 * ensuring consistent navigation experiences and proper SEO metadata throughout the user journey.
 *
 * ## Core Functionality
 * - **Dynamic Title Management**: Automatically extracts and manages page titles from URL paths
 * - **Breadcrumb Path Construction**: Builds hierarchical navigation paths for complex route structures
 * - **Route-Based Title Resolution**: Maps URL segments to human-readable titles using navigation configuration
 * - **Context State Synchronization**: Maintains navigation state across component tree with React Context
 * - **Next.js Router Integration**: Seamless integration with Next.js 15 App Router navigation primitives
 *
 * ## Navigation State Architecture
 * **Automatic Title Detection**: Uses reverse navigation mapping to resolve URL paths to meaningful titles:
 * - **URL Path Analysis**: Traverses URL segments from specific to general to find matching titles
 * - **Hierarchical Resolution**: Falls back through parent paths when specific routes lack title mappings
 * - **Dynamic Updates**: Automatically updates when pathname changes via Next.js navigation
 * - **SEO Optimization**: Provides structured title data for search engine optimization
 *
 * **Navigation Path Management**: Maintains breadcrumb-style navigation state:
 * - **Path Array**: Ordered array of `NavigationItem` objects representing current navigation hierarchy
 * - **Dynamic Modification**: Components can programmatically update navigation paths via `changeNavPath()`
 * - **Title Override**: Components can override automatic title detection via `changeTitle()`
 * - **Context Propagation**: Navigation state automatically propagated to all consuming components
 *
 * ## Integration with EkoIntelligence Platform
 * **ESG Application Navigation**: Specialized for complex ESG analysis workflows:
 * - **Dashboard Navigation**: Supports multi-level dashboard pages (dashboard, flags, claims, promises)
 * - **Document Management**: Handles document creation, editing, and viewing navigation flows
 * - **Analysis Workflows**: Manages navigation for company analysis, document analysis, and usage reporting
 * - **Model Integration**: Supports navigation for ESG models (Doughnut Economics, UN SDG, Plant Based Treaty)
 * - **Admin Functions**: Provides navigation context for administrative features and user management
 *
 * **Next.js 15 App Router Compatibility**:
 * - **Server Components**: Compatible with React Server Components architecture
 * - **Client-Side Navigation**: Optimized for client-side transitions with Next.js Link components
 * - **Route Segments**: Works with dynamic route segments and nested layouts
 * - **Middleware Integration**: Supports custom middleware and route protection patterns
 *
 * ## Database and System Integration
 * **Navigation Configuration**: Integrates with centralized navigation definition system:
 * - **Reverse Navigation Map**: Uses `reverseNavigationMap` from `/app/customer/navigation.tsx`
 * - **Feature Flag Support**: Navigation items can be conditionally shown based on feature flags
 * - **Permission Integration**: Supports admin-only and role-based navigation visibility
 * - **Configuration Driven**: Navigation structure defined declaratively for maintainability
 *
 * ## Component Architecture
 * **Provider Pattern**: Standard React Context provider pattern with enhanced navigation logic:
 * - **NavigationProvider**: Main provider component wrapping application sections requiring navigation
 * - **useNav Hook**: Custom hook for consuming navigation context with error boundary protection
 * - **Type Safety**: Full TypeScript integration with NavigationContextType interface
 * - **Performance Optimized**: Uses useCallback for stable function references and efficient re-renders
 *
 * ## Usage Patterns in EkoIntelligence
 * **Page Header Integration**: Primary integration point for displaying navigation state:
 * - **Title Display**: Automatic title extraction and display in page headers
 * - **Breadcrumb Rendering**: Navigation path array used for breadcrumb component construction
 * - **Router Access**: Provides Next.js router instance for programmatic navigation
 * - **Responsive Design**: Navigation state adapts to mobile and desktop viewing contexts
 *
 * **Dynamic Navigation Updates**: Components can modify navigation state for complex workflows:
 * - **Document Editor**: Updates navigation path during document editing sessions
 * - **Analysis Workflows**: Modifies breadcrumbs during multi-step analysis processes
 * - **Modal Interactions**: Manages navigation state during overlay and modal interactions
 * - **Form Wizards**: Supports multi-step form navigation with proper breadcrumb updates
 *
 * ## Technical Implementation
 * **State Management Strategy**:
 * - **Automatic Path Resolution**: useEffect hook monitors pathname changes for automatic title updates
 * - **Segment Traversal**: Iterative URL segment analysis from specific to general paths
 * - **Memory Optimization**: Efficient string manipulation and object creation patterns
 * - **Error Handling**: Graceful fallbacks when navigation mappings are unavailable
 *
 * **Performance Considerations**:
 * - **Callback Stabilization**: useCallback ensures stable function references across re-renders
 * - **Minimal Re-renders**: Strategic state updates to minimize unnecessary component re-renders
 * - **URL Parsing Optimization**: Efficient string manipulation for URL segment analysis
 * - **Context Optimization**: Proper context value construction to prevent cascading updates
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing Next.js App Router Documentation
 * @see https://react.dev/reference/react/useContext React Context API Documentation
 * @see https://nextjs.org/docs/app/api-reference/functions/use-pathname Next.js usePathname Hook
 * @see https://nextjs.org/docs/app/api-reference/functions/use-router Next.js useRouter Hook
 * @see {@link /app/customer/navigation.tsx} Application Navigation Configuration
 * @see {@link /components/page-header.tsx} Page Header Component Integration
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Navigation Context Provider for Dynamic Title and Breadcrumb Management in EkoIntelligence Customer App
 * @example
 * ```tsx
 * // Wrap your app section with NavigationProvider
 * <NavigationProvider>
 *   <YourAppContent />
 * </NavigationProvider>
 *
 * // Use navigation context in components
 * function MyComponent() {
 *   const { title, path, changeTitle, changeNavPath, router } = useNav();
 *
 *   // Access current title
 *   console.log('Current page title:', title);
 *
 *   // Update navigation programmatically
 *   changeTitle('Custom Page Title');
 *   changeNavPath([
 *     { label: 'Dashboard', href: '/customer/dashboard' },
 *     { label: 'Analysis', href: '/customer/analysis' }
 *   ]);
 *
 *   // Navigate programmatically
 *   router.push('/customer/documents');
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
// context/UserContext.tsx
'use client';

import React, { createContext, ReactNode, useCallback, useContext, useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { NavigationItem } from '@/components/page-header'
import { reverseNavigationMap } from '@/app/customer/navigation'

export interface NavigationContextType {
    readonly path: NavigationItem[],
    readonly title?: string,
    readonly router: ReturnType<typeof useRouter>,
    changeTitle: (title: string) => void,
    changeNavPath: (navPath: NavigationItem[]) => void,
}

const NavContext = createContext<NavigationContextType | undefined>(undefined);

interface NavigationProviderProps {
    children: ReactNode;
}

function removeLastPathSegment(path:string) {
    const segments = path.split('/');
    segments.pop(); // Remove the last segment
    return segments.join('/');
}


export const NavigationProvider = ({children}: NavigationProviderProps) => {
    const pathname = usePathname();
    const [navPath, setNavPath] = useState<NavigationItem[]>([]);
    const [title, setTitle] = useState<string | undefined>(undefined);
    const router = useRouter();


  useEffect(() => {

        let path = pathname;
        while (path && path.startsWith('/') && path.length > 1) {
            let titleFromPath = reverseNavigationMap.get(path)?.title;
            if (titleFromPath) {
                setTitle(titleFromPath);
                break;
            }
            path = removeLastPathSegment(path);
        }
    }, [pathname]);


  const changeTitle = useCallback((newTitle: string) => {
        setTitle(newTitle);
  }, []);

  const changeNavPath = useCallback((newPath: NavigationItem[]) => {
        setNavPath(newPath);
  }, []);

    return (
      <NavContext.Provider value={{ router, title, path: navPath, changeTitle, changeNavPath }}>
            {children}
        </NavContext.Provider>
    );
};

export const useNav = (): NavigationContextType => {
    const context = useContext(NavContext);
    if (context === undefined) {
        throw new Error('useNav must be used within a NavigationProvider');
    }
    return context;
};
