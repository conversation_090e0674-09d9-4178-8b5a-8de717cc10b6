# Entity Context Module

## Overview

The Entity Context Module serves as the central state management system for ESG (Environmental, Social, Governance) entity analysis within the EkoIntelligence customer dashboard application. This React Context-based module orchestrates the complex interaction between URL-based navigation state, real-time database queries, and comprehensive ESG analysis data loading, providing a unified interface for accessing corporate sustainability analysis across the entire application.

Built with Next.js 15 App Router architecture and Supabase real-time database integration, the module combines React Context with nuqs URL state synchronization to maintain entity selection, analysis run configuration, and model preferences across browser navigation and page refreshes. It coordinates multiple data streams simultaneously while ensuring consistent state across all consumer components.

## Specification

### Core Requirements

The Entity Context Module must provide:

1. **Centralized State Management**: Single source of truth for ESG entity analysis state across the application
2. **URL Synchronization**: Type-safe URL parameter management using `nuqs` for persistent state across navigation
3. **Multi-Stream Data Loading**: Coordination of simultaneous data fetching for comprehensive ESG analysis
4. **Real-time Database Integration**: Secure access to synchronized ESG analysis data via Supabase
5. **Request Management**: Intelligent request cancellation, deduplication, and lifecycle management
6. **Session Persistence**: Browser session storage for user preferences and entity selections
7. **Performance Optimization**: Efficient data loading with concurrent requests and smart caching

### Entity-Model-Run (EMR) Pattern

The module implements a sophisticated three-dimensional selection model:

- **Entity (E)**: ESG company/organization being analyzed
- **Model (M)**: AI analysis framework (SDG goals, doughnut economics, ekoIntelligence, etc.)
- **Run (R)**: Temporal analysis scope with start/end years and analysis type

### URL State Parameters

- `entity` (string): Selected ESG entity identifier for analysis scope
- `run` (string|'latest'): Analysis run ID or 'latest' for most recent data
- `model` (string): AI analysis model selection (defaults to 'sdg')
- `disclosures` (boolean): Flag for including disclosure documents in analysis

## Key Components

### Core Infrastructure

- **`entity-context.tsx`** - Primary React Context provider implementing centralized state management system for ESG entity analysis, combining URL synchronization, database integration, and comprehensive data loading
- **`emr-selector.tsx`** - Interactive navigation component for ESG entity-model-run selection with real-time data synchronization, responsive design, and glass-morphism UI patterns

### Data Management Layer

- **`data/`** - Complete ESG data fetching module providing specialized data fetchers for all analysis types:
  - Comprehensive README with architecture diagrams and usage examples
  - Base data fetcher with Template Method pattern for consistent request management
  - Specialized fetchers for flags, claims, promises, cherry-picked data, model sections, scoring, and vague language analysis
  - Full test coverage for all data fetchers and integration scenarios

### Test Coverage

- **`entity-context.test.tsx`** - Comprehensive unit tests for context provider functionality
- **`emr-selector.test.tsx`** - Component tests for entity-model-run selector UI and interactions
- **`data/*.test.tsx`** - Individual test suites for each specialized data fetcher

## Dependencies

### External Libraries

- **`nuqs`** (`/47ng/nuqs`) - Type-safe URL state management for React frameworks, providing `useQueryState` and `useQueryStates` hooks for synchronizing component state with URL query parameters
- **`@supabase/supabase-js`** - Database client for secure, authenticated access to customer database with automatic Row Level Security (RLS) policy application
- **`React`** - Hook-based state management with `createContext`, `useContext`, `useState`, `useEffect`, and `useRef` for component lifecycle management
- **`Next.js 15`** - App Router integration for server-side rendering and API route integration with optimized data loading patterns

### Internal Dependencies

- **Database Types** - Generated TypeScript interfaces from Supabase schema providing compile-time type safety for all database operations
- **Authentication Context** (`../auth/auth-context.tsx`) - User authentication and session management integration
- **Feature Flags** (`@/utils/feature-flags.ts`) - Configuration system for conditional data loading and experimental feature enablement
- **Entity Utilities** - Data conversion utilities for V1/V2 format compatibility and entity data transformation
- **UI Components** - Glass-morphism styled components from the design system for consistent user interface

### Database Schema Integration

The module integrates with synchronized `xfer_*` tables in the customer database:

- **`xfer_entities`** - Entity metadata synchronized from analytics database
- **`xfer_runs`** - Analysis run data with temporal scope and model configuration
- **`xfer_flags`** - ESG effect flags with impact analysis and severity ratings
- **`xfer_claims`** - Corporate claims with verification outcomes and confidence scores
- **`xfer_promises`** - Future commitments with tracking status and fulfillment metrics
- **`xfer_model_sections`** - DEMISE model analysis sections for comprehensive ESG evaluation
- **`view_my_companies`** - User-accessible companies with quota validation and RLS policies

## Usage Examples

### Basic Provider Setup

```typescript
import { EntityProvider } from '@/components/context/entity/entity-context';
import { EntityModelRunSelector } from '@/components/context/entity/emr-selector';

function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <EntityProvider>
      <EntityModelRunSelector navPath={[
        { label: "Dashboard", href: "/customer/dashboard" },
        { label: "Analysis" }
      ]} />
      {children}
    </EntityProvider>
  );
}
```

### Component Consumption

```typescript
import { useEntity } from '@/components/context/entity/entity-context';

function ESGAnalysisView() {
  const {
    entity,           // Current entity identifier
    entityData,       // Entity metadata and information
    run,             // Selected analysis run
    runObject,       // Complete run metadata
    model,           // Selected analysis model
    includeDisclosures, // Disclosure inclusion flag
    flagsData,       // ESG effect flags data
    claimsData,      // Corporate claims analysis
    promisesData,    // Promises and commitments data
    isLoading,       // Consolidated loading function
    changeParams     // Parameter change handler
  } = useEntity();

  // Handle entity selection change
  const handleEntityChange = (newEntityId: string) => {
    changeParams([{ key: 'entity', value: newEntityId }]);
  };

  // Handle model configuration change
  const handleModelChange = (newModel: string) => {
    changeParams([{ key: 'model', value: newModel }]);
  };

  if (isLoading()) {
    return <LoadingSpinner />;
  }

  return (
    <div className="esg-analysis">
      <ESGAnalysisDisplay
        entityData={entityData}
        flagsData={flagsData}
        claimsData={claimsData}
        promisesData={promisesData}
      />
    </div>
  );
}
```

### Custom Hook Integration

```typescript
import { useEntity } from '@/components/context/entity/entity-context';

function useEntityAnalysis() {
  const {
    entity,
    run,
    model,
    includeDisclosures,
    flagsData,
    claimsData,
    promisesData,
    isLoading
  } = useEntity();

  const analysisComplete = !isLoading() && 
    flagsData && claimsData && promisesData;

  const analysisHash = `${entity}:${run}:${model}:${includeDisclosures ? '1' : '0'}`;

  return {
    entity,
    run,
    model,
    includeDisclosures,
    flagsData,
    claimsData,
    promisesData,
    analysisComplete,
    analysisHash,
    isLoading: isLoading()
  };
}
```

## Architecture Notes

### State Management Architecture

```mermaid
graph TB
    A[URL Parameters] -->|nuqs| B[Entity Context Provider]
    B --> C[Session Storage]
    B --> D[Supabase Database]
    B --> E[Data Fetchers]
    E --> F[React Components]
    
    G[User Interactions] --> H[EMR Selector]
    H --> I[changeParams]
    I --> B
    
    J[Browser Navigation] --> A
    K[Page Refresh] --> C
    C --> B
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant EMR as EMR Selector
    participant EC as Entity Context
    participant DB as Supabase DB
    participant DF as Data Fetchers
    participant C as Components

    U->>EMR: Select Entity
    EMR->>EC: changeParams([{key: 'entity', value: 'new-entity'}])
    EC->>EC: Update URL with nuqs
    EC->>EC: Store in Session Storage
    EC->>DB: Query entity data
    DB-->>EC: Entity metadata
    EC->>DF: Trigger data fetching
    DF->>DB: Query analysis data
    DB-->>DF: Analysis results
    DF-->>EC: Processed data
    EC-->>C: Context value updates
    C-->>U: UI updates
```

### Request Lifecycle Management

```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> Validating: Parameters Change
    Validating --> Cancelled: Invalid Dependencies
    Validating --> Loading: Valid Dependencies
    Loading --> Cancelled: Abort Signal
    Loading --> Success: Data Received
    Loading --> Error: Request Failed
    Success --> Idle: Data Set
    Error --> Idle: Error Handled
    Cancelled --> Idle: Cleanup Complete
```

### Component Integration Architecture

```mermaid
graph LR
    A[EntityProvider] --> B[EMR Selector]
    A --> C[Dashboard Components]
    A --> D[Analysis Views]
    A --> E[Report Generation]
    
    F[Data Fetchers] --> G[Flags Fetcher]
    F --> H[Claims Fetcher]
    F --> I[Promises Fetcher]
    F --> J[Score Fetcher]
    F --> K[Other Fetchers]
    
    A --> F
    
    L[URL State] --> M[nuqs]
    M --> A
    
    N[Session Storage] --> A
    O[Supabase Client] --> A
```

## Known Issues

### Data Synchronization Issues

Based on Linear ticket analysis and code review:

- **EKO-298**: Broken promises expandable sections show "+8 more pieces of evidence" but no expansion functionality in promise tracking components
- **EKO-291**: Claims page navigation sidebar failures - entity context state not properly synchronized with routing on specific pages
- **EKO-227**: Company context not preserved when switching between dashboard and report generation - URL state synchronization gaps between different application sections

### User Interface Integration Issues

- **EKO-265**: Charts integration with entity context data - chart components may not properly subscribe to entity context updates
- Green Flags links not properly navigating to detail views when clicked, potentially due to incomplete entity context propagation
- False Claims section has inconsistent font rendering and potentially irrelevant content display

### Performance Considerations

- Request cancellation logic may need optimization for rapid entity switching scenarios - current AbortController implementation could be enhanced
- Database query performance could be improved with additional indexing on frequently accessed `xfer_*` table fields
- Memory usage monitoring needed for large result sets in entity scoring data, especially with multiple simultaneous data fetchers

### Session Storage Limitations

- Session storage mechanism may not properly handle concurrent browser tabs with different entity selections
- Browser refresh scenarios may not always restore complete entity context state consistently
- Cross-browser compatibility considerations for session storage implementation

## Future Work

### Planned Enhancements

Based on code analysis and Linear ticket requirements:

1. **Enhanced Request Management System**:
   - Implement request prioritization for critical data types
   - Add progressive loading capabilities for large datasets
   - Develop intelligent prefetching based on user navigation patterns

2. **Real-time Data Updates Integration**:
   - Integrate Supabase real-time subscriptions for live data updates
   - Implement optimistic updates for improved user experience
   - Add conflict resolution for concurrent data modifications

3. **Advanced URL State Management**:
   - Support for complex URL state scenarios including nested routing
   - Enhanced browser history management for better user navigation experience
   - URL state validation and error recovery mechanisms

### Technical Debt Resolution

1. **Legacy Format Support**: Remove V1 fallback logic once all entities migrated to V2 format across the database
2. **Type Safety Improvements**: Strengthen runtime type validation for database responses and API integration
3. **Error Recovery Enhancement**: Implement more sophisticated retry mechanisms with exponential backoff
4. **Caching Layer Addition**: Add client-side caching to reduce database load for frequently accessed data

### Performance Optimizations

1. **Query Optimization**: Implement database-level pagination for large result sets
2. **Bundle Splitting**: Consider lazy loading of specialized fetchers to reduce initial bundle size
3. **Memory Management**: Implement data cleanup strategies for long-running sessions
4. **Request Deduplication**: Enhanced deduplication logic for concurrent identical requests

### User Experience Enhancements

1. **Loading State Improvements**: More granular loading indicators for different data types
2. **Error Messaging**: Enhanced user-friendly error messages for different failure scenarios
3. **Accessibility**: Improved screen reader support and keyboard navigation
4. **Mobile Optimization**: Enhanced responsive design for mobile entity selection

## Troubleshooting

### Common Issues

**Q: Entity context returns null despite valid entity selection**  
A: Check browser network tab for failed requests. Verify user authentication status and RLS policies. Ensure entity exists in `xfer_entities` table with valid run data. Confirm `view_my_companies` includes the entity for the current user.

**Q: URL parameters not syncing with entity context state**  
A: Verify nuqs is properly configured and EntityProvider wraps the component tree. Check for conflicting URL parameter management in parent components. Ensure browser supports session storage.

**Q: Request cancellation not working properly during rapid entity switching**  
A: Verify AbortController cleanup in component unmount. Check that request IDs are unique and properly managed in concurrent scenarios. Review data fetcher implementation for proper abort signal handling.

**Q: Data fetchers return stale data after entity changes**  
A: Confirm request tracking refs are properly updated. Verify data fetcher request IDs include all relevant parameters. Check for memory leaks in abort controller management.

**Q: Session storage not persisting entity selections**  
A: Verify browser supports session storage and it's not disabled. Check for cross-origin issues. Ensure session storage keys are consistent across application.

### Debugging Procedures

1. **Enable Context Logging**: Add console logging to track context state changes and data fetcher requests
2. **Database Query Analysis**: Use Supabase dashboard to monitor query performance and execution
3. **Network Request Inspection**: Use browser dev tools to examine request/response patterns and timing
4. **State Flow Debugging**: Add React Developer Tools to visualize context state propagation
5. **AbortController Monitoring**: Track abort controller lifecycle for request cancellation debugging

### Error Recovery Patterns

```typescript
// Example error handling pattern for entity context consumers
function useEntityWithErrorHandling() {
  const entityContext = useEntity();
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    // Reset error when entity changes
    setError(null);
  }, [entityContext.entity]);
  
  // Monitor for context errors
  useEffect(() => {
    if (entityContext.entity && !entityContext.entityData && !entityContext.isLoading()) {
      setError('Failed to load entity data. Please refresh and try again.');
    }
  }, [entityContext.entity, entityContext.entityData, entityContext.isLoading]);
  
  return {
    ...entityContext,
    error,
    hasError: !!error
  };
}
```

### Performance Monitoring

```typescript
// Example performance monitoring for entity context
function useEntityPerformanceMonitoring() {
  const entityContext = useEntity();
  
  useEffect(() => {
    const startTime = performance.now();
    
    if (!entityContext.isLoading()) {
      const loadTime = performance.now() - startTime;
      console.log(`Entity context load time: ${loadTime}ms`);
      
      // Report slow loads
      if (loadTime > 5000) {
        console.warn('Slow entity context load detected');
      }
    }
  }, [entityContext.isLoading()]);
  
  return entityContext;
}
```

## FAQ

### User-Centric Questions

**Q: Why does the entity selector sometimes show "No companies available"?**  
A: This occurs when your user account doesn't have access to any entities, or when the database connection is temporarily unavailable. Contact your administrator to verify your company quota and entity access permissions.

**Q: How can I tell if my entity data is up-to-date?**  
A: Check the run information in the EMR selector - it shows the completion date and analysis scope. If data seems outdated, try selecting a more recent run or contact support for fresh analysis.

**Q: What happens when I change the analysis model?**  
A: Changing the model (SDG, Doughnut, etc.) will reload the analysis sections with different ESG frameworks while preserving your entity and run selections. Some data types may take longer to load depending on model complexity.

**Q: Why do some entities have more analysis data than others?**  
A: Data availability depends on the source documents analyzed and the entity's public disclosure practices. Larger entities with more comprehensive ESG reporting typically have richer analysis data.

**Q: How does the disclosure toggle affect my analysis?**  
A: When "Include disclosures" is enabled, the analysis includes routine regulatory filings and informational statements. Disabling it focuses on impact-oriented content and strategic ESG communications.

**Q: Can I share a specific entity analysis with colleagues?**  
A: Yes, the URL contains all necessary parameters. Simply copy the browser URL when viewing the desired entity, run, and model configuration - your colleagues can use that link to view the same analysis.

**Q: What should I do if the dashboard seems stuck loading?**  
A: Try refreshing the page first. If the issue persists, check your internet connection and try selecting a different entity or run. The system may be processing new data for your selected configuration.

**Q: How do I report missing or incorrect entity data?**  
A: Contact support with the specific entity name, run date, and description of the issue. Include the URL of the page where you noticed the problem for faster resolution.

## References

### Documentation Links

- [Nuqs URL State Management](https://nuqs.47ng.com/) - Type-safe search params state manager for React frameworks
- [React Context API Documentation](https://react.dev/reference/react/createContext) - Official React Context documentation
- [Supabase TypeScript Client](https://supabase.com/docs/reference/javascript/typescript-support) - Database client documentation
- [Next.js App Router Data Fetching](https://nextjs.org/docs/app/building-your-application/data-fetching) - Modern data fetching patterns
- [Supabase Row Level Security](https://supabase.com/docs/guides/database/postgres/row-level-security) - Database security implementation

### Related Code Files

- [`./entity-context.tsx`](./entity-context.tsx) - Primary React Context provider for entity state management
- [`./emr-selector.tsx`](./emr-selector.tsx) - Interactive entity-model-run navigation component
- [`./data/README.md`](./data/README.md) - Comprehensive data fetching module documentation
- [`./data/index.ts`](./data/index.ts) - Central export module for all data fetchers
- [`../auth/auth-context.tsx`](../auth/auth-context.tsx) - User authentication context integration
- [`../../types/index.ts`](../../types/index.ts) - ESG analysis type definitions
- [`../../utils/entity-converter.ts`](../../utils/entity-converter.ts) - Entity data format conversion utilities
- [`../../utils/run-utils.ts`](../../utils/run-utils.ts) - Analysis run data utilities

### External Resources

- [React useContext Hook](https://react.dev/reference/react/useContext) - Hook for consuming React Context
- [React useEffect Dependencies](https://react.dev/reference/react/useEffect#specifying-reactive-dependencies) - Managing effect dependencies
- [AbortController API](https://developer.mozilla.org/en-US/docs/Web/API/AbortController) - Request cancellation implementation
- [TypeScript Utility Types](https://www.typescriptlang.org/docs/handbook/utility-types.html) - Type system utilities
- [Browser Session Storage](https://developer.mozilla.org/en-US/docs/Web/API/Window/sessionStorage) - Client-side storage API

### Database Schema Documentation

- Customer Database Schema: [`@tmp/db/customer_schema.sql`](../../../../tmp/db/customer_schema.sql)
- Entity Transfer Tables: Analytics to customer database synchronization process
- View Definitions: [`view_my_companies`](../../../../tmp/db/customer/schemas/public/views/view_my_companies.sql)

### Linear Issue References

- [EKO-227: Company Context Preservation](https://linear.app/ekointelligence/issue/EKO-227) - URL state synchronization between dashboard and reports
- [EKO-291: Claims Page Navigation](https://linear.app/ekointelligence/issue/EKO-291) - Sidebar navigation failures on claims page
- [EKO-298: Broken Promises Expansion](https://linear.app/ekointelligence/issue/EKO-298) - Promise detail expansion functionality

---

## Changelog

### 2025-07-31
- Initial comprehensive README.md creation for entity context module
- Documented complete architecture including EMR pattern and data flow
- Added detailed usage examples and troubleshooting guide
- Included Mermaid diagrams for system architecture visualization
- Integrated Linear issue tracking for known issues and future work
- Added comprehensive FAQ section and external resource references
- Documented dependencies including nuqs URL state management and Supabase integration

---

**(c) All rights reserved ekoIntelligence 2025**