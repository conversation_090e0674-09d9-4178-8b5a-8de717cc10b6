/**
 * Entity Context Provider for ESG Corporate Analysis State Management - Customer Dashboard
 *
 * This React Context provider serves as the central state management system for ESG (Environmental,
 * Social, Governance) entity analysis within the EkoIntelligence customer dashboard application.
 * It orchestrates the complex interaction between URL-based navigation state, real-time database
 * queries, and comprehensive ESG analysis data loading, providing a unified interface for accessing
 * corporate sustainability analysis across the entire application.
 *
 * ## Core Architecture
 * **Centralized State Management**: Combines React Context with `nuqs` URL state synchronization
 * to maintain entity selection, analysis run configuration, and model preferences across browser
 * navigation and page refreshes. The provider manages multiple data streams simultaneously while
 * ensuring consistent state across all consumer components.
 *
 * **URL-Synchronized Parameters**: Leverages `nuqs` for type-safe URL parameter management:
 * - `entity` (string): Selected ESG entity identifier for analysis scope
 * - `run` (string|'latest'): Analysis run ID or 'latest' for most recent data
 * - `model` (string): AI analysis model selection (defaults to 'sdg')
 * - `disclosures` (boolean): Flag for including disclosure documents in analysis
 *
 * ## Data Management Strategy
 * **Multi-Stream Data Loading**: Coordinates simultaneous data fetching for comprehensive ESG analysis:
 * - **Effect Flags**: Environmental, social, and governance impact indicators with severity ratings
 * - **Claims Analysis**: Corporate ESG claims with verification status and confidence scoring
 * - **Promises Tracking**: Future-oriented commitments with fulfillment monitoring and timeline analysis
 * - **Cherry Data**: Selective disclosure analysis and cherry-picked statement identification
 * - **Model Sections**: DEMISE framework analysis sections for comprehensive ESG evaluation
 * - **Entity Scoring**: Overall ESG performance metrics with domain-specific breakdowns
 * - **Vague Language Analysis**: Detection and analysis of ambiguous statements in corporate communications
 *
 * ## Database Integration
 * **Supabase Customer Database**: Secure access to synchronized ESG analysis data:
 * - **Entity Data**: Retrieved from `xfer_entities` table with comprehensive entity metadata
 * - **Run Information**: Analysis run details from `xfer_runs` with timing and configuration data
 * - **Available Entities**: User-accessible entities from `view_my_companies` with quota-based filtering
 * - **Row Level Security**: Automatic application of RLS policies for secure, user-scoped data access
 * - **Real-time Updates**: Support for live data updates through Supabase real-time capabilities
 *
 * ## State Persistence & Session Management
 * **Browser Session Storage**: Maintains user preferences across browser sessions:
 * - **Entity Selection**: Remembers last selected entity for improved user experience
 * - **Analysis Configuration**: Persists model selection and disclosure preferences
 * - **Run Selection**: Maintains analysis run preferences for consistent reporting
 * - **SSR Compatibility**: Safe session storage access with server-side rendering support
 *
 * ## Request Management & Performance
 * **Intelligent Request Orchestration**: Advanced request lifecycle management:
 * - **Concurrent Requests**: Multiple data streams loaded simultaneously for optimal performance
 * - **Request Cancellation**: Automatic cancellation of stale requests when parameters change
 * - **Loading State Coordination**: Centralized loading state management across all data types
 * - **Abort Controllers**: Proper cleanup of network requests to prevent memory leaks
 * - **Request Deduplication**: Prevents duplicate requests through intelligent tracking
 *
 * ## Data Fetcher Integration
 * **Specialized Data Fetchers**: Utilizes dedicated fetcher classes for each data type:
 * - `FlagsDataFetcher`: ESG effect flags with impact analysis and filtering capabilities
 * - `ClaimsDataFetcher`: Corporate claims analysis with verification and confidence scoring
 * - `PromisesDataFetcher`: Future commitments tracking with fulfillment monitoring
 * - `CherryDataFetcher`: Selective disclosure and cherry-picking analysis
 * - `ModelSectionsDataFetcher`: DEMISE model sections for comprehensive ESG evaluation
 * - `ScoreDataFetcher`: Entity scoring data with performance metrics and domain breakdowns
 * - `VagueDataFetcher`: Vague language detection and ambiguity analysis
 *
 * ## Component Consumer Pattern
 * **Hook-Based Access**: Provides `useEntity()` hook for component consumption:
 * ```typescript
 * const {
 *   entity,           // Current entity identifier
 *   entityData,       // Entity metadata and information
 *   run,             // Selected analysis run
 *   runObject,       // Complete run metadata
 *   model,           // Selected analysis model
 *   includeDisclosures, // Disclosure inclusion flag
 *   flagsData,       // ESG effect flags data
 *   claimsData,      // Corporate claims analysis
 *   promisesData,    // Promises and commitments data
 *   isLoading,       // Consolidated loading function
 *   changeParams     // Parameter change handler
 * } = useEntity();
 * ```
 *
 * ## System Architecture Context
 * **ESG Analysis Pipeline Integration**:
 * - **Analytics Backend**: Python-based system processes raw ESG documents and generates analysis flags
 * - **Data Synchronization**: Transfer tables (`xfer_*`) sync processed analytics data to customer database
 * - **API Layer**: This context provides customer-facing access to synchronized ESG analysis data
 * - **Frontend Dashboard**: React components consume entity data through this centralized state provider
 * - **User Interface**: Supports entity selection, analysis configuration, and comprehensive data visualization
 *
 * ## Error Handling & Resilience
 * **Graceful Degradation**: Comprehensive error handling across all data operations:
 * - **Database Error Recovery**: Handles database connectivity issues without application crashes
 * - **Request Cancellation Handling**: Proper management of cancelled requests to prevent error noise
 * - **Loading State Management**: Maintains consistent loading indicators even during error conditions
 * - **User Experience**: Ensures application remains functional even when specific data streams fail
 *
 * ## Performance Optimizations
 * **Efficient Data Management**:
 * - **Selective Data Loading**: Loads only required fields for each data type to minimize bandwidth
 * - **Smart Caching**: Utilizes browser session storage for frequently accessed data
 * - **Concurrent Loading**: Parallel data fetching for multiple analysis streams
 * - **Memory Management**: Proper cleanup of event listeners and abort controllers
 * - **Render Optimization**: Minimizes re-renders through careful dependency management
 *
 * ## Security & Authorization
 * **Secure Data Access**:
 * - **User Authentication**: Requires valid Supabase user session for all data operations
 * - **Row Level Security**: Automatic application of database-level security policies
 * - **Entity Authorization**: Users can only access entities within their organizational quota
 * - **Session Management**: Secure handling of user session state and authentication tokens
 *
 * ## Future Extensibility
 * **Modular Architecture**: Designed for easy extension with additional data types:
 * - **Plugin Architecture**: New data fetchers can be easily integrated
 * - **State Extension**: Additional URL parameters and state management can be added
 * - **Analysis Expansion**: Support for new ESG analysis models and frameworks
 * - **Real-time Features**: Foundation for real-time collaborative analysis features
 *
 * @see https://nuqs.47ng.com/ Nuqs URL State Management Documentation
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://react.dev/reference/react/createContext React Context API Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching Next.js App Router Data Fetching
 * @see ./data/index.ts ESG Entity Data Fetchers Module
 * @see ../../types/index.ts ESG Analysis Type Definitions
 * @see ../auth/auth-context.tsx User Authentication Context Provider
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Centralized state management system for ESG entity analysis, combining URL synchronization, database integration, and comprehensive data loading for the customer dashboard
 * @example
 * ```typescript
 * // Provider setup in layout or app root
 * function AppLayout({ children }: { children: React.ReactNode }) {
 *   return (
 *     <EntityProvider>
 *       {children}
 *     </EntityProvider>
 *   );
 * }
 *
 * // Component consumption
 * function ESGDashboard() {
 *   const {
 *     entity,
 *     entityData,
 *     flagsData,
 *     claimsData,
 *     promisesData,
 *     isLoading,
 *     changeParams
 *   } = useEntity();
 *
 *   // Handle entity selection change
 *   const handleEntityChange = (newEntityId: string) => {
 *     changeParams([{ key: 'entity', value: newEntityId }]);
 *   };
 *
 *   // Handle model configuration change
 *   const handleModelChange = (newModel: string) => {
 *     changeParams([{ key: 'model', value: newModel }]);
 *   };
 *
 *   if (isLoading()) {
 *     return <LoadingSpinner />;
 *   }
 *
 *   return (
 *     <div className="esg-dashboard">
 *       <EntitySelector
 *         selectedEntity={entity}
 *         onEntityChange={handleEntityChange}
 *       />
 *       <ModelSelector
 *         selectedModel={model}
 *         onModelChange={handleModelChange}
 *       />
 *       <ESGAnalysisDisplay
 *         entityData={entityData}
 *         flagsData={flagsData}
 *         claimsData={claimsData}
 *         promisesData={promisesData}
 *       />
 *     </div>
 *   );
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
'use client'

import React, { createContext, ReactNode, useContext, useEffect, useRef, useState } from 'react'
import { createClient } from '@/app/supabase/client'
import { parseAsBoolean, parseAsString, useQueryStates } from 'nuqs'
import { CherryTypeV2, FlagTypeV2, ModelSectionType, PromiseTypeV2, RunType, ScoreTypeV2, VagueType } from '@/types'
import { ClaimTypeV2 } from '@/types/claim'
import { runAsync } from '@utils/react-utils'
import { convertEntityV2ToEntityV1 } from '@/utils/entity-converter'
import { convertRunV2ToRunV1 } from '@/utils/run-utils'
import {
  CherryDataFetcher,
  ClaimsDataFetcher,
  FlagsDataFetcher,
  ModelSectionsDataFetcher,
  PromisesDataFetcher,
  ScoreDataFetcher,
  VagueDataFetcher,
} from './data'

export interface EntityContextType {
  entity: string | null;
  entityData: any | null;
  run: string | 'latest';
  model: string | 'ekoIntelligence';
  includeDisclosures: boolean;
  changeParams: (newParams: { key: string, value: string | null }[]) => void;
  toggleDisclosures: (include: boolean) => void;
  queryString: string | null;
  hash: () => string;
  runObject: RunType | null;
  flagsData: FlagTypeV2[] | null;
  isLoadingFlags: boolean;
  promisesData: PromiseTypeV2[] | null;
  isLoadingPromises: boolean;
  cherryData: CherryTypeV2[] | null;
  isLoadingCherry: boolean;
  claimsData: ClaimTypeV2[] | null;
  isLoadingClaims: boolean;
  modelSectionsData: ModelSectionType[] | null;
  isLoadingModelSections: boolean;
  score: number;
  scoreData: ScoreTypeV2 | null;
  isLoadingScore: boolean;
  vagueData: VagueType | null;
  vagueDetailData: VagueType[] | null;
  isLoadingVague: boolean;
  isLoading: () => boolean;
}

const DEFAULT_MODEL = 'sdg'

const EntityContext = createContext<EntityContextType | undefined>(undefined)

interface EntityProviderProps {
  children: ReactNode;
}

export const EntityProvider = ({ children }: EntityProviderProps) => {
  const supabase = createClient()

  // Define parsers for entity-relevant parameters
  const entityParsers = {
    entity: parseAsString.withDefault(''),
    run: parseAsString.withDefault('latest'),
    model: parseAsString.withDefault(DEFAULT_MODEL),
    disclosures: parseAsBoolean.withDefault(true)
  }

  // Use nuqs to manage only entity-relevant URL parameters
  const [urlParams, setUrlParams] = useQueryStates(entityParsers)

  // State for available entities
  const [availableEntities, setAvailableEntities] = useState<Array<{entity_xid: string, name: string}>>([])
  const [isLoadingEntities, setIsLoadingEntities] = useState(true)

  const { entity, run, model, disclosures: includeDisclosures } = urlParams
  
  // Generate queryString containing only entity-relevant parameters
  const queryString = React.useMemo(() => {
    const params = new URLSearchParams()
    
    // Only include entity-relevant parameters
    if (entity) params.set('entity', entity)
    if (run !== 'latest') params.set('run', run)
    if (model !== DEFAULT_MODEL) params.set('model', model)
    if (includeDisclosures === false) params.set('disclosures', 'false')
    
    return params.toString()
  }, [entity, run, model, includeDisclosures])

  const [entityData, setEntityData] = useState<any | null>(null)
  const [runs, setRuns] = useState<RunType[]>([])
  const [runObject, setRunObject] = useState<null | RunType>(null)
  const [flagsData, setFlagsData] = useState<FlagTypeV2[] | null>(null)
  const [isLoadingFlags, setIsLoadingFlags] = useState<boolean>(false)
  const [promisesData, setPromisesData] = useState<PromiseTypeV2[] | null>(null)
  const [isLoadingPromises, setIsLoadingPromises] = useState<boolean>(false)
  const [cherryData, setCherryData] = useState<CherryTypeV2[] | null>(null)
  const [isLoadingCherry, setIsLoadingCherry] = useState<boolean>(false)
  const [claimsData, setClaimsData] = useState<ClaimTypeV2[] | null>(null)
  const [isLoadingClaims, setIsLoadingClaims] = useState<boolean>(false)
  const [modelSectionsData, setModelSectionsData] = useState<ModelSectionType[] | null>(null)
  const [isLoadingModelSections, setIsLoadingModelSections] = useState<boolean>(false)
  const [score, setScore] = useState<number>(0)
  const [scoreData, setScoreData] = useState<ScoreTypeV2 | null>(null)
  const [isLoadingScore, setIsLoadingScore] = useState<boolean>(false)
  const [vagueData, setVagueData] = useState<VagueType | null>(null)
  const [vagueDetailData, setVagueDetailData] = useState<VagueType[] | null>(null)
  const [isLoadingVague, setIsLoadingVague] = useState<boolean>(false)
  const [isBeforeLoading, setIsBeforeLoading] = useState<boolean>(false)

  // Add refs to track current request context and abort controllers per request type
  const currentRequestRefs = useRef<Map<string, string>>(new Map())
  const abortControllersRef = useRef<Map<string, AbortController>>(new Map())

  // Session storage utilities
  const getFromSessionStorage = (key: string): string | null => {
    if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
      return null
    }
    return sessionStorage.getItem(key)
  }

  const setToSessionStorage = (key: string, value: string): void => {
    if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
      return
    }
    sessionStorage.setItem(key, value)
  }

  // Initialize data fetchers
  const flagsDataFetcher = useRef(new FlagsDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const promisesDataFetcher = useRef(new PromisesDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const cherryDataFetcher = useRef(new CherryDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const claimsDataFetcher = useRef(new ClaimsDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const modelSectionsDataFetcher = useRef(new ModelSectionsDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const scoreDataFetcher = useRef(new ScoreDataFetcher(currentRequestRefs.current, abortControllersRef.current))
  const vagueDataFetcher = useRef(new VagueDataFetcher(currentRequestRefs.current, abortControllersRef.current))

  // Load available entities on mount
  useEffect(() => {
    let abort = false
    runAsync(async () => {
      try {
        setIsLoadingEntities(true)

        // Get current user from auth context
        const userResponse = await supabase.auth.getUser()
        const user = userResponse?.data?.user
        if (!user?.id || abort) return

        const { data: companies, error } = await supabase
          .from('view_my_companies')
          .select('entity_xid, name')
          .eq('profile_id', user.id)

        if (abort) return

        if (error) {
          console.error('Error loading available entities:', error)
          setAvailableEntities([])
          return
        }

        setAvailableEntities((companies || []).map(c => ({
          entity_xid: String(c.entity_xid),
          name: String(c.name)
        })))
      } catch (error) {
        if (!abort) {
          console.error('Error in available entities loading:', error)
          setAvailableEntities([])
        }
      } finally {
        if (!abort) {
          setIsLoadingEntities(false)
        }
      }
    })

    return () => {
      abort = true
    }
  }, [])

  // Handle default entity selection when no entity is in URL and entities are loaded
  const hasSetDefaultEntity = useRef(false)
  useEffect(() => {
    if (isLoadingEntities || availableEntities.length === 0) return

    // If no entity in URL, try to get from session storage or use first available
    // Use ref to prevent infinite loop - only set default entity once
    if (!entity && !hasSetDefaultEntity.current) {
      const sessionEntity = getFromSessionStorage('entity-context-entity')
      const sessionRun = getFromSessionStorage('entity-context-run')
      const sessionModel = getFromSessionStorage('entity-context-model')
      const sessionDisclosures = getFromSessionStorage('entity-context-disclosures')

      // Check if session entity is still available
      const isSessionEntityAvailable = sessionEntity &&
        availableEntities.some(e => e.entity_xid === sessionEntity)

      const defaultEntity = isSessionEntityAvailable ? sessionEntity : availableEntities[0]?.entity_xid

      if (defaultEntity) {
        hasSetDefaultEntity.current = true
        const updates: Partial<typeof urlParams> = {
          entity: defaultEntity,
          run: sessionRun || 'latest',
          model: sessionModel || DEFAULT_MODEL,
          disclosures: sessionDisclosures ? sessionDisclosures === 'true' : true
        }

        // Store in session storage
        setToSessionStorage('entity-context-entity', defaultEntity)
        setToSessionStorage('entity-context-run', updates.run!)
        setToSessionStorage('entity-context-model', updates.model!)
        setToSessionStorage('entity-context-disclosures', String(updates.disclosures))

        setUrlParams(updates)
      }
    }
    
    // Reset ref when entity is explicitly cleared
    if (entity) {
      hasSetDefaultEntity.current = false
    }
  }, [entity, availableEntities, isLoadingEntities])

  // Store current values in session storage whenever they change
  useEffect(() => {
    if (entity) {
      setToSessionStorage('entity-context-entity', entity)
    }
  }, [entity])

  useEffect(() => {
    if (run) {
      setToSessionStorage('entity-context-run', run)
    }
  }, [run])

  useEffect(() => {
    if (model) {
      setToSessionStorage('entity-context-model', model)
    }
  }, [model])

  useEffect(() => {
    setToSessionStorage('entity-context-disclosures', String(includeDisclosures))
  }, [includeDisclosures])

  function isLoading() {
    return isBeforeLoading || isLoadingFlags || isLoadingPromises || isLoadingCherry ||
      isLoadingClaims || isLoadingModelSections || isLoadingScore || isLoadingVague
  }

  function hash() {
    return entity + ':' + run + ':' + model + ':' + (includeDisclosures ? '1' : '0')
  }

  function toggleDisclosures(include: boolean) {
    if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
      return
    }
    setToSessionStorage('entity-context-disclosures', include ? 'true' : 'false')
    setUrlParams({ disclosures: include })
  }

  // Function to fetch and filter flags based on the current entity, run, and disclosure setting
  async function fetchAndFilterFlags() {
    await flagsDataFetcher.current.fetch(
      { entity, runObject, includeDisclosures },
      setFlagsData,
      setIsLoadingFlags,
    )
  }

  // Function to fetch and filter promises based on the current entity and run
  async function fetchAndFilterPromises() {
    await promisesDataFetcher.current.fetch(
      { entity, runObject },
      setPromisesData,
      setIsLoadingPromises,
    )
  }

  // Function to fetch cherry data
  async function fetchCherryData() {
    await cherryDataFetcher.current.fetch(
      { entity, runObject },
      setCherryData,
      setIsLoadingCherry,
    )
  }

  // Function to fetch claims data
  async function fetchClaimsData() {
    await claimsDataFetcher.current.fetch(
      { entity, runObject },
      setClaimsData,
      setIsLoadingClaims,
    )
  }

  // Function to fetch model sections data
  async function fetchModelSectionsData() {
    await modelSectionsDataFetcher.current.fetch(
      { entity, runObject, model },
      setModelSectionsData,
      setIsLoadingModelSections,
    )
  }

  // Function to fetch score data
  async function fetchScoreData() {
    await scoreDataFetcher.current.fetch(
      { entity, runObject },
      (data) => {
        if (data) {
          setScore(data.score)
          setScoreData(data.scoreData)
        } else {
          setScore(0)
          setScoreData(null)
        }
      },
      setIsLoadingScore,
    )
  }

  // Function to fetch vague data
  async function fetchVagueData() {
    await vagueDataFetcher.current.fetch(
      { entity, runObject },
      (data) => {
        if (data) {
          setVagueData(data.vagueData)
          setVagueDetailData(data.vagueDetailData)
        } else {
          setVagueData(null)
          setVagueDetailData(null)
        }
      },
      setIsLoadingVague,
    )
  }

  function changeParams(newParams: { key: string, value: string | null }[]) {
    if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
      return
    }
    setIsBeforeLoading(true)
    // Abort all ongoing data retrievals when parameters change
    console.log('Aborting all data retrievals due to parameter change')
    const requestTypes = ['flags', 'promises', 'cherry', 'claims', 'modelSections', 'score', 'vague']
    requestTypes.forEach(requestType => {
      const controller = abortControllersRef.current.get(requestType)
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete(requestType)
        console.log(`Aborted ${requestType} request`)
      }
    })

    // Build update object for nuqs
    const updates: Partial<typeof urlParams> = {}
    let resetRun = false

    newParams.forEach(({ key, value }) => {
      if (value !== undefined && value !== null) {
        // Store in session storage with entity-context prefix
        setToSessionStorage(`entity-context-${key}`, value)

        // Handle specific parameter updates
        if (key === 'entity') {
          updates.entity = value
          // Reset run to latest when entity changes
          if (value !== entity) {
            resetRun = true
          }
        } else if (key === 'run') {
          updates.run = value
        } else if (key === 'model') {
          updates.model = value
        } else if (key === 'disclosures') {
          updates.disclosures = value === 'true'
        }
      } else {
        // Set to empty string to remove from URL and session storage
        if (key === 'entity') {
          updates.entity = ''
          setToSessionStorage('entity-context-entity', '')
        } else if (key === 'run') {
          updates.run = 'latest'
          setToSessionStorage('entity-context-run', 'latest')
        } else if (key === 'model') {
          updates.model = DEFAULT_MODEL
          setToSessionStorage('entity-context-model', DEFAULT_MODEL)
        } else if (key === 'disclosures') {
          updates.disclosures = true
          setToSessionStorage('entity-context-disclosures', 'true')
        }
      }
    })

    if (resetRun) {
      updates.run = 'latest'
      setToSessionStorage('entity-context-run', 'latest')
    }

    // Update URL state with nuqs
    setUrlParams(updates)
  }

  // Load entity data when entity changes
  useEffect(() => {
    if (!entity) {
      setEntityData(null)
      return
    }

    let abort = false
    runAsync(async () => {
      try {
        const { data: entityV2Data, error: entityV2Error } = await supabase
          .from('xfer_entities')
          .select(`
            entity_xid, 
            run_id, 
            name, 
            type, 
            entity_description,
            entity_base_entities_json,
            model
          `)
          .eq('entity_xid', entity)
          .single()

        if (abort) return

        if (entityV2Error) {
          console.error('Error loading entity data:', entityV2Error)
          setEntityData(null)
          return
        }

        if (entityV2Data) {
          // Convert the new format to the old format for compatibility
          const entityV1 = convertEntityV2ToEntityV1(entityV2Data)
          setEntityData(entityV1)
        } else {
          setEntityData(null)
        }
      } catch (error) {
        if (!abort) {
          console.error('Error in entity data loading:', error)
          setEntityData(null)
        }
      }
    })

    return () => {
      abort = true
    }
  }, [entity])

  useEffect(() => {
    let abort = false
    runAsync(async () => {
      // Try to get runs from xfer_runs first
        const { data: specificRunsV2, error: specificRunsV2Error } = await supabase
          .from('xfer_runs')
          .select('*')
          .eq('scope', 'entity')
          .eq('target', entity!)

        const { data: generalRunsV2, error: generalRunsV2Error } = await supabase
          .from('xfer_runs')
          .select('*')
          .eq('scope', 'all')

        // Convert V2 runs to V1 format if available
        let specificRuns: RunType[] = []
        let generalRuns: RunType[] = []

        if (specificRunsV2 && specificRunsV2.length > 0) {
          specificRuns = specificRunsV2.map(runV2 => convertRunV2ToRunV1(runV2))
        }
        if (generalRunsV2 && generalRunsV2.length > 0) {
          generalRuns = generalRunsV2.map(runV2 => convertRunV2ToRunV1(runV2))
        }

        if (abort) return

        if (specificRuns.length > 0 && generalRuns.length > 0) {
          setRuns([...specificRuns, ...generalRuns])
        } else if (specificRuns.length > 0) {
          setRuns(specificRuns)
        } else if (generalRuns.length > 0) {
          setRuns(generalRuns)
        }
        console.log('Set runs', specificRuns, 'for entity', entity)
      },
    )

    return () => {
      abort = true
    }

  }, [entity])

  // Set run object when run or runs change
  useEffect(() => {
    if (!runs.length) return

    runAsync(async () => {
      let defaultRun = null

      if (run !== 'latest') {
        // Try to get run from xfer_runs first
        const {
          data: defaultRunV2Value,
          error: runV2Error,
        } = await supabase.from('xfer_runs').select('*').eq('id', +run!).single()

        if (!runV2Error && defaultRunV2Value) {
          // Convert the new format to the old format for compatibility
          defaultRun = convertRunV2ToRunV1(defaultRunV2Value)
        }
      } else {
        for (let r of runs) {
          if (r.run_type == 'full' || r.run_type == 'inc') {
            if (defaultRun == null || r.id! > (defaultRun.id || 0)) {
              defaultRun = r
            }
          }
        }
      }
      console.log('Set run object', defaultRun)
      // Make sure defaultRun is a proper RunType before setting it
      if (defaultRun) {
        const typedRun: RunType = {
          id: typeof defaultRun.id === 'number' ? defaultRun.id : 0,
          run_type: String(defaultRun.run_type || ''),
          scope: String(defaultRun.scope || ''),
          target: typeof defaultRun.target === 'string' ? defaultRun.target : null,
          model: defaultRun.model || {},
          completed_at: typeof defaultRun.completed_at === 'string' ? defaultRun.completed_at : null,
        }
        
        // Only update if the run object has actually changed to prevent infinite re-renders
        setRunObject(prevRunObject => {
          if (!prevRunObject || 
              prevRunObject.id !== typedRun.id ||
              prevRunObject.run_type !== typedRun.run_type ||
              prevRunObject.scope !== typedRun.scope ||
              prevRunObject.target !== typedRun.target ||
              prevRunObject.completed_at !== typedRun.completed_at ||
              JSON.stringify(prevRunObject.model) !== JSON.stringify(typedRun.model)) {
            return typedRun
          }
          return prevRunObject
        })
      } else {
        setRunObject(null)
      }
    })
  }, [run, runs])

  // Fetch flags when entity, run, or includeDisclosures changes
  useEffect(() => {
    if (entity && runObject) {
      // Set isBeforeLoading to false as we start the actual data fetching phase
      setIsBeforeLoading(false)
      fetchAndFilterFlags()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('flags')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('flags')
      }
    }
  }, [entity, runObject, includeDisclosures])

  // Fetch promises when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchAndFilterPromises()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('promises')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('promises')
      }
    }
  }, [entity, runObject])

  // Fetch cherry data when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchCherryData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('cherry')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('cherry')
      }
    }
  }, [entity, runObject])

  // Fetch claims data when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchClaimsData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('claims')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('claims')
      }
    }
  }, [entity, runObject])

  // Fetch model sections data when model changes
  useEffect(() => {
    if (model) {
      fetchModelSectionsData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('modelSections')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('modelSections')
      }
    }
  }, [model])

  // Fetch score data when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchScoreData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('score')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('score')
      }
    }
  }, [entity, runObject])

  // Fetch vague data when entity or run changes
  useEffect(() => {
    if (entity && runObject) {
      fetchVagueData()
    }

    // Cleanup function to cancel requests when dependencies change
    return () => {
      const controller = abortControllersRef.current.get('vague')
      if (controller) {
        controller.abort()
        abortControllersRef.current.delete('vague')
      }
    }
  }, [entity, runObject])


  return (
    <EntityContext.Provider value={{
      entity,
      run,
      model,
      includeDisclosures,
      changeParams,
      toggleDisclosures,
      hash,
      queryString,
      entityData,
      runObject,
      flagsData,
      isLoadingFlags,
      promisesData,
      isLoadingPromises,
      cherryData,
      isLoadingCherry,
      claimsData,
      isLoadingClaims,
      modelSectionsData,
      isLoadingModelSections,
      score,
      scoreData,
      isLoadingScore,
      vagueData,
      vagueDetailData,
      isLoadingVague,
      isLoading,
    }}>
      {children}
    </EntityContext.Provider>
  )
}

export const useEntity = (): EntityContextType => {
  const context = useContext(EntityContext)
  if (context === undefined) {
    throw new Error('useEntity must be used within a EntityProvider')
  }
  return context
}
