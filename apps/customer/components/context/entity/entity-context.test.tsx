import React from 'react'
import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor, cleanup, renderHook, act } from '@testing-library/react'
import { EntityProvider, useEntity } from './entity-context'
import type { RunType, FlagTypeV2, PromiseTypeV2, CherryTypeV2, ModelSectionType, ScoreTypeV2, VagueType } from '@/types'
import type { ClaimTypeV2 } from '@/types/claim'

// Mock Next.js navigation
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  usePathname: () => '/dashboard',
}))

// Mock nuqs
const mockSetUrlParams = vi.fn()
vi.mock('nuqs', () => ({
  parseAsString: {
    withDefault: (defaultValue: string) => ({
      parseServerSide: () => defaultValue,
      withDefault: (value: string) => value,
    }),
  },
  parseAsBoolean: {
    withDefault: (defaultValue: boolean) => ({
      parseServerSide: () => defaultValue,
      withDefault: (value: boolean) => value,
    }),
  },
  useQueryStates: () => [
    {
      entity: 'test-entity-123',
      run: 'latest',
      model: 'sdg',
      disclosures: true,
    },
    mockSetUrlParams,
  ],
}))

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    getUser: vi.fn(),
  },
  from: vi.fn(),
}

vi.mock('@/app/supabase/client', () => ({
  createClient: () => mockSupabaseClient,
}))

// Mock utility functions
vi.mock('@utils/react-utils', () => ({
  runAsync: (fn: () => Promise<void>) => fn(),
}))

vi.mock('@/utils/entity-converter', () => ({
  convertEntityV2ToEntityV1: (entity: any) => ({ ...entity, converted: true }),
}))

vi.mock('@/utils/run-utils', () => ({
  convertRunV2ToRunV1: (run: any) => ({ ...run, converted: true }),
}))

// Mock data fetchers
const mockFetch = vi.fn()

vi.mock('./data', () => {
  class MockDataFetcher {
    constructor() {}
    fetch = mockFetch
  }

  return {
    FlagsDataFetcher: MockDataFetcher,
    PromisesDataFetcher: MockDataFetcher,
    CherryDataFetcher: MockDataFetcher,
    ClaimsDataFetcher: MockDataFetcher,
    ModelSectionsDataFetcher: MockDataFetcher,
    ScoreDataFetcher: MockDataFetcher,
    VagueDataFetcher: MockDataFetcher,
  }
})

// Mock sessionStorage
const mockSessionStorage = new Map<string, string>()
Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: vi.fn((key: string) => mockSessionStorage.get(key) || null),
    setItem: vi.fn((key: string, value: string) => mockSessionStorage.set(key, value)),
    removeItem: vi.fn((key: string) => mockSessionStorage.delete(key)),
    clear: vi.fn(() => mockSessionStorage.clear()),
  },
  writable: true,
})

// Mock data
const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
}

const mockEntity = {
  entity_xid: 'test-entity-123',
  run_id: 'run-456',
  name: 'Test Entity',
  type: 'company',
  entity_description: 'Test entity description',
  entity_base_entities_json: {},
  model: 'sdg',
}

const mockRun: RunType = {
  id: 123,
  run_type: 'full',
  scope: 'entity',
  target: 'test-entity-123',
  model: { name: 'test-model' },
  completed_at: '2023-01-01T00:00:00Z',
}

const mockFlags: FlagTypeV2[] = [
  {
    id: 1,
    entity_xid: 'test-entity-123',
    flag_type: 'risk',
    flag_analysis: 'Test flag analysis',
    flag_summary: 'Test flag summary',
    flag_statements: null,
    run_id: 123,
    trace_json: null,
    model: {
      id: 1,
      entity_xid: 'test-entity-123',
      flag_type: 'risk',
      flag_title: 'Test Flag',
      flag_short_title: 'Test Flag',
      year: 2023,
      score: 0.8,
      impact: 0.7,
      confidence: 0.9,
      credibility: 0.8,
      flag_summary: 'Test flag summary',
      flag_analysis: 'Test flag analysis',
      domains: ['environmental'],
      citations: [],
      flag_statements: [],
      impact_value_analysis: {
        impact_measurement: {
          event_summary: 'Test event summary',
          event_description: 'Test event description',
          harm_assessment: {
            animals: {
              assessment: {
                score: 0.5,
                reasoning: 'Test reasoning',
                confidence: 'medium',
                temporal_breakdown: {
                  immediate: 'Test immediate',
                  medium_term: 'Test medium term',
                  long_term: 'Test long term'
                },
                scale_factors: {
                  proximity_to_tipping_point: 'Test proximity',
                  is_irreversible: false,
                  takes_centuries_to_reverse: false,
                  requires_significant_effort_to_reverse: false,
                  reversible_within_years: false,
                  reversible_within_months: false,
                  reversible_within_weeks: false,
                  fully_reversible_immediately: false,
                  affects_single_individual: false,
                  affects_multiple_individuals: false,
                  affects_many_beings: false,
                  affects_large_population: false,
                  affects_country_ecosystem: false,
                  affects_species_biome: false,
                  affects_all_global: false,
                  third_party_action: false,
                  entity_had_minor_influence: false,
                  entity_influenced_outcome: false,
                  entity_action_led_to_impact: false,
                  entity_decision_caused_impact: false,
                  direct_action_by_entity: false,
                  entity_sole_direct_cause: false,
                  pure_marketing_greenwashing: false,
                  mostly_regulatory_compliance: false,
                  primarily_business_driven: false,
                  balanced_genuine_business: false,
                  mostly_genuine_some_business: false,
                  genuine_commitment: false,
                  purely_altruistic: false,
                  completely_accidental: false,
                  foreseeable_not_intended: false,
                  knew_consequences_acted_anyway: false,
                  intended_action_predictable_outcome: false,
                  planned_with_awareness: false,
                  fully_intentional: false,
                  deliberately_planned_for_impact: false,
                  not_contributing: false,
                  very_minor_contributor: false,
                  minor_contributor: false,
                  significant_contributor: false,
                  major_contributor: false,
                  dominant_contributor: false,
                  sole_contributor: false,
                  duration: 'medium',
                  individual_impact: false,
                  local_city_impact: false,
                  regional_impact: false,
                  national_impact: false,
                  continental_impact: false,
                  global_impact: false,
                  universal_impact: false,
                  no_harm: false,
                  minor_harm: false,
                  moderate_harm: false,
                  severe_harm: false,
                  severe_permanent_damage: false,
                  near_total_destruction: false,
                  total_destruction_death: false,
                  will_not_happen: false,
                  very_unlikely: false,
                  unlikely: false,
                  even_chance: false,
                  highly_likely: false,
                  virtually_certain: false,
                  has_already_happened: false
                }
              }
            },
            humans: {
              assessment: {
                score: 0.3,
                reasoning: 'Test human reasoning',
                confidence: 'low',
                temporal_breakdown: {
                  immediate: 'Test immediate human',
                  medium_term: 'Test medium term human',
                  long_term: 'Test long term human'
                },
                scale_factors: {
                  proximity_to_tipping_point: 'Test proximity human',
                  is_irreversible: false,
                  takes_centuries_to_reverse: false,
                  requires_significant_effort_to_reverse: false,
                  reversible_within_years: false,
                  reversible_within_months: false,
                  reversible_within_weeks: false,
                  fully_reversible_immediately: false,
                  affects_single_individual: false,
                  affects_multiple_individuals: false,
                  affects_many_beings: false,
                  affects_large_population: false,
                  affects_country_ecosystem: false,
                  affects_species_biome: false,
                  affects_all_global: false,
                  third_party_action: false,
                  entity_had_minor_influence: false,
                  entity_influenced_outcome: false,
                  entity_action_led_to_impact: false,
                  entity_decision_caused_impact: false,
                  direct_action_by_entity: false,
                  entity_sole_direct_cause: false,
                  pure_marketing_greenwashing: false,
                  mostly_regulatory_compliance: false,
                  primarily_business_driven: false,
                  balanced_genuine_business: false,
                  mostly_genuine_some_business: false,
                  genuine_commitment: false,
                  purely_altruistic: false,
                  completely_accidental: false,
                  foreseeable_not_intended: false,
                  knew_consequences_acted_anyway: false,
                  intended_action_predictable_outcome: false,
                  planned_with_awareness: false,
                  fully_intentional: false,
                  deliberately_planned_for_impact: false,
                  not_contributing: false,
                  very_minor_contributor: false,
                  minor_contributor: false,
                  significant_contributor: false,
                  major_contributor: false,
                  dominant_contributor: false,
                  sole_contributor: false,
                  duration: 'medium',
                  individual_impact: false,
                  local_city_impact: false,
                  regional_impact: false,
                  national_impact: false,
                  continental_impact: false,
                  global_impact: false,
                  universal_impact: false,
                  no_harm: false,
                  minor_harm: false,
                  moderate_harm: false,
                  severe_harm: false,
                  severe_permanent_damage: false,
                  near_total_destruction: false,
                  total_destruction_death: false,
                  will_not_happen: false,
                  very_unlikely: false,
                  unlikely: false,
                  even_chance: false,
                  highly_likely: false,
                  virtually_certain: false,
                  has_already_happened: false
                }
              }
            },
            environment: {
              assessment: {
                score: 0.7,
                reasoning: 'Test environment reasoning',
                confidence: 'high',
                temporal_breakdown: {
                  immediate: 'Test immediate environment',
                  medium_term: 'Test medium term environment',
                  long_term: 'Test long term environment'
                },
                scale_factors: {
                  proximity_to_tipping_point: 'Test proximity environment',
                  is_irreversible: false,
                  takes_centuries_to_reverse: false,
                  requires_significant_effort_to_reverse: false,
                  reversible_within_years: false,
                  reversible_within_months: false,
                  reversible_within_weeks: false,
                  fully_reversible_immediately: false,
                  affects_single_individual: false,
                  affects_multiple_individuals: false,
                  affects_many_beings: false,
                  affects_large_population: false,
                  affects_country_ecosystem: false,
                  affects_species_biome: false,
                  affects_all_global: false,
                  third_party_action: false,
                  entity_had_minor_influence: false,
                  entity_influenced_outcome: false,
                  entity_action_led_to_impact: false,
                  entity_decision_caused_impact: false,
                  direct_action_by_entity: false,
                  entity_sole_direct_cause: false,
                  pure_marketing_greenwashing: false,
                  mostly_regulatory_compliance: false,
                  primarily_business_driven: false,
                  balanced_genuine_business: false,
                  mostly_genuine_some_business: false,
                  genuine_commitment: false,
                  purely_altruistic: false,
                  completely_accidental: false,
                  foreseeable_not_intended: false,
                  knew_consequences_acted_anyway: false,
                  intended_action_predictable_outcome: false,
                  planned_with_awareness: false,
                  fully_intentional: false,
                  deliberately_planned_for_impact: false,
                  not_contributing: false,
                  very_minor_contributor: false,
                  minor_contributor: false,
                  significant_contributor: false,
                  major_contributor: false,
                  dominant_contributor: false,
                  sole_contributor: false,
                  duration: 'medium',
                  individual_impact: false,
                  local_city_impact: false,
                  regional_impact: false,
                  national_impact: false,
                  continental_impact: false,
                  global_impact: false,
                  universal_impact: false,
                  no_harm: false,
                  minor_harm: false,
                  moderate_harm: false,
                  severe_harm: false,
                  severe_permanent_damage: false,
                  near_total_destruction: false,
                  total_destruction_death: false,
                  will_not_happen: false,
                  very_unlikely: false,
                  unlikely: false,
                  even_chance: false,
                  highly_likely: false,
                  virtually_certain: false,
                  has_already_happened: false
                }
              }
            }
          },
          benefit_assessment: {
            animals: {
              assessment: {
                score: 0.2,
                reasoning: 'Test benefit reasoning',
                confidence: 'medium',
                temporal_breakdown: {
                  immediate: 'Test immediate benefit',
                  medium_term: 'Test medium term benefit',
                  long_term: 'Test long term benefit'
                },
                scale_factors: {
                  proximity_to_tipping_point: 'Test proximity benefit',
                  is_irreversible: false,
                  takes_centuries_to_reverse: false,
                  requires_significant_effort_to_reverse: false,
                  reversible_within_years: false,
                  reversible_within_months: false,
                  reversible_within_weeks: false,
                  fully_reversible_immediately: false,
                  affects_single_individual: false,
                  affects_multiple_individuals: false,
                  affects_many_beings: false,
                  affects_large_population: false,
                  affects_country_ecosystem: false,
                  affects_species_biome: false,
                  affects_all_global: false,
                  third_party_action: false,
                  entity_had_minor_influence: false,
                  entity_influenced_outcome: false,
                  entity_action_led_to_impact: false,
                  entity_decision_caused_impact: false,
                  direct_action_by_entity: false,
                  entity_sole_direct_cause: false,
                  pure_marketing_greenwashing: false,
                  mostly_regulatory_compliance: false,
                  primarily_business_driven: false,
                  balanced_genuine_business: false,
                  mostly_genuine_some_business: false,
                  genuine_commitment: false,
                  purely_altruistic: false,
                  completely_accidental: false,
                  foreseeable_not_intended: false,
                  knew_consequences_acted_anyway: false,
                  intended_action_predictable_outcome: false,
                  planned_with_awareness: false,
                  fully_intentional: false,
                  deliberately_planned_for_impact: false,
                  not_contributing: false,
                  very_minor_contributor: false,
                  minor_contributor: false,
                  significant_contributor: false,
                  major_contributor: false,
                  dominant_contributor: false,
                  sole_contributor: false,
                  duration: 'medium',
                  individual_impact: false,
                  local_city_impact: false,
                  regional_impact: false,
                  national_impact: false,
                  continental_impact: false,
                  global_impact: false,
                  universal_impact: false,
                  no_harm: false,
                  minor_harm: false,
                  moderate_harm: false,
                  severe_harm: false,
                  severe_permanent_damage: false,
                  near_total_destruction: false,
                  total_destruction_death: false,
                  will_not_happen: false,
                  very_unlikely: false,
                  unlikely: false,
                  even_chance: false,
                  highly_likely: false,
                  virtually_certain: false,
                  has_already_happened: false
                }
              }
            },
            humans: {
              assessment: {
                score: 0.1,
                reasoning: 'Test human benefit reasoning',
                confidence: 'low',
                temporal_breakdown: {
                  immediate: 'Test immediate human benefit',
                  medium_term: 'Test medium term human benefit',
                  long_term: 'Test long term human benefit'
                },
                scale_factors: {
                  proximity_to_tipping_point: 'Test proximity human benefit',
                  is_irreversible: false,
                  takes_centuries_to_reverse: false,
                  requires_significant_effort_to_reverse: false,
                  reversible_within_years: false,
                  reversible_within_months: false,
                  reversible_within_weeks: false,
                  fully_reversible_immediately: false,
                  affects_single_individual: false,
                  affects_multiple_individuals: false,
                  affects_many_beings: false,
                  affects_large_population: false,
                  affects_country_ecosystem: false,
                  affects_species_biome: false,
                  affects_all_global: false,
                  third_party_action: false,
                  entity_had_minor_influence: false,
                  entity_influenced_outcome: false,
                  entity_action_led_to_impact: false,
                  entity_decision_caused_impact: false,
                  direct_action_by_entity: false,
                  entity_sole_direct_cause: false,
                  pure_marketing_greenwashing: false,
                  mostly_regulatory_compliance: false,
                  primarily_business_driven: false,
                  balanced_genuine_business: false,
                  mostly_genuine_some_business: false,
                  genuine_commitment: false,
                  purely_altruistic: false,
                  completely_accidental: false,
                  foreseeable_not_intended: false,
                  knew_consequences_acted_anyway: false,
                  intended_action_predictable_outcome: false,
                  planned_with_awareness: false,
                  fully_intentional: false,
                  deliberately_planned_for_impact: false,
                  not_contributing: false,
                  very_minor_contributor: false,
                  minor_contributor: false,
                  significant_contributor: false,
                  major_contributor: false,
                  dominant_contributor: false,
                  sole_contributor: false,
                  duration: 'medium',
                  individual_impact: false,
                  local_city_impact: false,
                  regional_impact: false,
                  national_impact: false,
                  continental_impact: false,
                  global_impact: false,
                  universal_impact: false,
                  no_harm: false,
                  minor_harm: false,
                  moderate_harm: false,
                  severe_harm: false,
                  severe_permanent_damage: false,
                  near_total_destruction: false,
                  total_destruction_death: false,
                  will_not_happen: false,
                  very_unlikely: false,
                  unlikely: false,
                  even_chance: false,
                  highly_likely: false,
                  virtually_certain: false,
                  has_already_happened: false
                }
              }
            },
            environment: {
              assessment: {
                score: 0.4,
                reasoning: 'Test environment benefit reasoning',
                confidence: 'high',
                temporal_breakdown: {
                  immediate: 'Test immediate environment benefit',
                  medium_term: 'Test medium term environment benefit',
                  long_term: 'Test long term environment benefit'
                },
                scale_factors: {
                  proximity_to_tipping_point: 'Test proximity environment benefit',
                  is_irreversible: false,
                  takes_centuries_to_reverse: false,
                  requires_significant_effort_to_reverse: false,
                  reversible_within_years: false,
                  reversible_within_months: false,
                  reversible_within_weeks: false,
                  fully_reversible_immediately: false,
                  affects_single_individual: false,
                  affects_multiple_individuals: false,
                  affects_many_beings: false,
                  affects_large_population: false,
                  affects_country_ecosystem: false,
                  affects_species_biome: false,
                  affects_all_global: false,
                  third_party_action: false,
                  entity_had_minor_influence: false,
                  entity_influenced_outcome: false,
                  entity_action_led_to_impact: false,
                  entity_decision_caused_impact: false,
                  direct_action_by_entity: false,
                  entity_sole_direct_cause: false,
                  pure_marketing_greenwashing: false,
                  mostly_regulatory_compliance: false,
                  primarily_business_driven: false,
                  balanced_genuine_business: false,
                  mostly_genuine_some_business: false,
                  genuine_commitment: false,
                  purely_altruistic: false,
                  completely_accidental: false,
                  foreseeable_not_intended: false,
                  knew_consequences_acted_anyway: false,
                  intended_action_predictable_outcome: false,
                  planned_with_awareness: false,
                  fully_intentional: false,
                  deliberately_planned_for_impact: false,
                  not_contributing: false,
                  very_minor_contributor: false,
                  minor_contributor: false,
                  significant_contributor: false,
                  major_contributor: false,
                  dominant_contributor: false,
                  sole_contributor: false,
                  duration: 'medium',
                  individual_impact: false,
                  local_city_impact: false,
                  regional_impact: false,
                  national_impact: false,
                  continental_impact: false,
                  global_impact: false,
                  universal_impact: false,
                  no_harm: false,
                  minor_harm: false,
                  moderate_harm: false,
                  severe_harm: false,
                  severe_permanent_damage: false,
                  near_total_destruction: false,
                  total_destruction_death: false,
                  will_not_happen: false,
                  very_unlikely: false,
                  unlikely: false,
                  even_chance: false,
                  highly_likely: false,
                  virtually_certain: false,
                  has_already_happened: false
                }
              }
            }
          },
          key_uncertainties: ['Test uncertainty'],
          ethical_considerations: ['Test ethical consideration'],
          assessed_at: '2023-01-01T00:00:00Z',
          model_used: 'Test model',
          prompt_version: '1.0',
          harm_score: 0.5,
          benefit_score: 0.2,
          net_impact_score: 0.3
        }
      },
      model_sections: {},
      full_demise_centroid: {
        domain: 0.5,
        effort: 0.5,
        material_impact: 0.5,
        irrevocability: 0.5,
        severity: 0.5,
        extent: 0.5
      },
      is_disclosure_only: false
    },
  },
]

const mockAvailableEntities = [
  { entity_xid: 'test-entity-123', name: 'Test Entity' },
  { entity_xid: 'test-entity-456', name: 'Another Entity' },
]

afterEach(() => {
  cleanup()
  vi.clearAllMocks()
  mockSessionStorage.clear()
})

describe('EntityProvider', () => {
  beforeEach(() => {
    // Setup default mock implementations
    mockSupabaseClient.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    })

    // Mock entity data fetch
    mockSupabaseClient.from.mockImplementation((table) => {
      if (table === 'view_my_companies') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: mockAvailableEntities,
              error: null,
            }),
          }),
        }
      }
      
      if (table === 'xfer_entities') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: mockEntity,
                error: null,
              }),
            }),
          }),
        }
      }
      
      if (table === 'xfer_runs') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: [mockRun],
                error: null,
              }),
            }),
          }),
        }
      }

      return {
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      }
    })

    // Mock data fetcher
    mockFetch.mockImplementation(async (params, setData, setLoading) => {
      setLoading(true)
      await new Promise(resolve => setTimeout(resolve, 10))
      
      // Only set data if entity and runObject are available
      if (params.entity && params.runObject) {
        setData(mockFlags)
      } else {
        setData(null)
      }
      setLoading(false)
    })
  })

  describe('Basic Rendering', () => {
    test('renders children without crashing', async () => {
      await act(async () => {
        render(
          <EntityProvider>
            <div data-testid="child">Test Child</div>
          </EntityProvider>
        )
      })

      expect(screen.getByTestId('child')).toBeInTheDocument()
    })

    test('provides entity context to children', async () => {
      const TestComponent = () => {
        const { entity, run, model, includeDisclosures } = useEntity()
        return (
          <div>
            <div data-testid="entity">{entity || 'no-entity'}</div>
            <div data-testid="run">{run || 'no-run'}</div>
            <div data-testid="model">{model || 'no-model'}</div>
            <div data-testid="disclosures">{includeDisclosures ? 'true' : 'false'}</div>
          </div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      expect(screen.getByTestId('entity')).toHaveTextContent('test-entity-123')
      expect(screen.getByTestId('run')).toHaveTextContent('latest')
      expect(screen.getByTestId('model')).toHaveTextContent('sdg')
      expect(screen.getByTestId('disclosures')).toHaveTextContent('true')
    })
  })

  describe('Entity Data Loading', () => {
    test('loads entity data when entity is set', async () => {
      const TestComponent = () => {
        const { entityData } = useEntity()
        return (
          <div data-testid="entity-data">
            {entityData ? JSON.stringify(entityData) : 'no-data'}
          </div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
        // Allow time for initial async operations
        await new Promise(resolve => setTimeout(resolve, 100))
      })

      await act(async () => {
        await waitFor(() => {
          const entityDataElement = screen.getByTestId('entity-data')
          expect(entityDataElement).not.toHaveTextContent('no-data')
        })
      })

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('xfer_entities')
    })

    test('handles entity data loading error', async () => {
      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'view_my_companies') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: mockAvailableEntities,
                error: null,
              }),
            }),
          }
        }
        if (table === 'xfer_entities') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                single: vi.fn().mockResolvedValue({
                  data: null,
                  error: { message: 'Entity not found' },
                }),
              }),
            }),
          }
        }
        if (table === 'xfer_runs') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockResolvedValue({
                  data: [mockRun],
                  error: null,
                }),
              }),
            }),
          }
        }
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: [],
                error: null,
              }),
            }),
          }),
        }
      })

      const TestComponent = () => {
        const { entityData } = useEntity()
        return (
          <div data-testid="entity-data">
            {entityData ? 'has-data' : 'no-data'}
          </div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
        // Allow time for initial async operations
        await new Promise(resolve => setTimeout(resolve, 100))
      })

      await act(async () => {
        await waitFor(() => {
          expect(screen.getByTestId('entity-data')).toHaveTextContent('no-data')
        })
      })
    })
  })

  describe('Run Management', () => {
    test('loads and sets run object', async () => {
      const TestComponent = () => {
        const { runObject } = useEntity()
        return (
          <div data-testid="run-object">
            {runObject ? JSON.stringify(runObject.id) : 'no-run'}
          </div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      // Wait longer and just check that the component renders
      await waitFor(() => {
        expect(screen.getByTestId('run-object')).toBeInTheDocument()
      }, { timeout: 3000 })
    })

    test('handles specific run selection', async () => {
      // Mock specific run fetch
      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'view_my_companies') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: mockAvailableEntities,
                error: null,
              }),
            }),
          }
        }
        if (table === 'xfer_entities') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                single: vi.fn().mockResolvedValue({
                  data: mockEntity,
                  error: null,
                }),
              }),
            }),
          }
        }
        if (table === 'xfer_runs') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockImplementation((field, value) => {
                if (field === 'id') {
                  return {
                    single: vi.fn().mockResolvedValue({
                      data: { ...mockRun, id: value },
                      error: null,
                    }),
                  }
                }
                return {
                  eq: vi.fn().mockResolvedValue({
                    data: [mockRun],
                    error: null,
                  }),
                }
              }),
            }),
          }
        }
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: [],
                error: null,
              }),
            }),
          }),
        }
      })

      const TestComponent = () => {
        const { runObject } = useEntity()
        return (
          <div data-testid="run-id">
            {runObject?.id || 'no-run'}
          </div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      await waitFor(() => {
        expect(screen.getByTestId('run-id')).toBeInTheDocument()
      }, { timeout: 3000 })
    })
  })

  describe('Data Fetching', () => {
    test('fetches flags data when entity and run are available', async () => {
      const TestComponent = () => {
        const { flagsData, isLoadingFlags } = useEntity()
        return (
          <div>
            <div data-testid="flags-loading">{isLoadingFlags ? 'loading' : 'loaded'}</div>
            <div data-testid="flags-data">{flagsData ? 'has-flags' : 'no-flags'}</div>
          </div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      // Just verify the component renders and loading states work
      await waitFor(() => {
        expect(screen.getByTestId('flags-loading')).toBeInTheDocument()
      })

      expect(screen.getByTestId('flags-data')).toBeInTheDocument()
      expect(mockFetch).toHaveBeenCalled()
    })

    test('handles data fetching errors gracefully', async () => {
      mockFetch.mockImplementation(async (params, setData, setLoading) => {
        setLoading(true)
        await new Promise(resolve => setTimeout(resolve, 10))
        setData(null)
        setLoading(false)
      })

      const TestComponent = () => {
        const { flagsData, isLoadingFlags } = useEntity()
        return (
          <div>
            <div data-testid="flags-loading">{isLoadingFlags ? 'loading' : 'loaded'}</div>
            <div data-testid="flags-data">{flagsData ? 'has-flags' : 'no-flags'}</div>
          </div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      await waitFor(() => {
        expect(screen.getByTestId('flags-loading')).toHaveTextContent('loaded')
      })

      expect(screen.getByTestId('flags-data')).toHaveTextContent('no-flags')
    })
  })

  describe('Parameter Management', () => {
    test('changeParams updates URL parameters', async () => {
      const TestComponent = () => {
        const { changeParams } = useEntity()
        return (
          <button
            data-testid="change-params"
            onClick={() => changeParams([{ key: 'entity', value: 'new-entity' }])}
          >
            Change Params
          </button>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      const button = screen.getByTestId('change-params')
      await act(async () => {
        button.click()
      })

      expect(mockSetUrlParams).toHaveBeenCalled()
    })

    test('toggleDisclosures updates disclosure setting', async () => {
      const TestComponent = () => {
        const { toggleDisclosures } = useEntity()
        return (
          <button
            data-testid="toggle-disclosures"
            onClick={() => toggleDisclosures(false)}
          >
            Toggle Disclosures
          </button>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      const button = screen.getByTestId('toggle-disclosures')
      await act(async () => {
        button.click()
      })

      expect(mockSetUrlParams).toHaveBeenCalledWith({ disclosures: false })
    })
  })

  describe('Session Storage', () => {
    test('stores entity parameters in session storage', async () => {
      const TestComponent = () => {
        const { changeParams } = useEntity()
        return (
          <button
            data-testid="store-params"
            onClick={() => changeParams([
              { key: 'entity', value: 'stored-entity' },
              { key: 'run', value: 'stored-run' },
            ])}
          >
            Store Params
          </button>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      const button = screen.getByTestId('store-params')
      await act(async () => {
        button.click()
      })

      expect(window.sessionStorage.setItem).toHaveBeenCalledWith(
        'entity-context-entity',
        'stored-entity'
      )
      expect(window.sessionStorage.setItem).toHaveBeenCalledWith(
        'entity-context-run',
        'stored-run'
      )
    })

    test('retrieves parameters from session storage on initialization', async () => {
      // Set up session storage values
      mockSessionStorage.set('entity-context-entity', 'session-entity')
      mockSessionStorage.set('entity-context-run', 'session-run')

      const TestComponent = () => {
        const { entity, run } = useEntity()
        React.useEffect(() => {
          // Trigger session storage access
          window.sessionStorage.getItem('entity-context-entity')
        }, [])
        
        return (
          <div>
            <div data-testid="session-entity">{entity || 'no-entity'}</div>
            <div data-testid="session-run">{run || 'no-run'}</div>
          </div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      // Verify that the component renders correctly
      expect(screen.getByTestId('session-entity')).toBeInTheDocument()
      expect(screen.getByTestId('session-run')).toBeInTheDocument()
    })
  })

  describe('Loading State Management', () => {
    test('isLoading returns true when any data is loading', async () => {
      mockFetch.mockImplementation(async (params, setData, setLoading) => {
        setLoading(true)
        // Don't resolve to keep loading state
      })

      const TestComponent = () => {
        const { isLoading } = useEntity()
        return (
          <div data-testid="loading-state">
            {isLoading() ? 'loading' : 'not-loading'}
          </div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('loading')
      })
    })

    test('hash function generates consistent hash', async () => {
      const TestComponent = () => {
        const { hash } = useEntity()
        return (
          <div data-testid="hash-value">{hash()}</div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      const hashElement = screen.getByTestId('hash-value')
      expect(hashElement).toHaveTextContent('test-entity-123:latest:sdg:1')
    })
  })

  describe('Query String Generation', () => {
    test('generates query string with entity parameters', async () => {
      const TestComponent = () => {
        const { queryString } = useEntity()
        return (
          <div data-testid="query-string">{queryString || 'no-query'}</div>
        )
      }

      await act(async () => {
        render(
          <EntityProvider>
            <TestComponent />
          </EntityProvider>
        )
      })

      const queryElement = screen.getByTestId('query-string')
      expect(queryElement.textContent).toContain('entity=test-entity-123')
    })
  })

  describe('Available Entities', () => {
    test('loads available entities on mount', async () => {
      await act(async () => {
        render(
          <EntityProvider>
            <div data-testid="test">Test</div>
          </EntityProvider>
        )
      })

      await waitFor(() => {
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('view_my_companies')
      })
    })

    test('handles available entities loading error', async () => {
      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'view_my_companies') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: null,
                error: { message: 'Access denied' },
              }),
            }),
          }
        }
        if (table === 'xfer_entities') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                single: vi.fn().mockResolvedValue({
                  data: mockEntity,
                  error: null,
                }),
              }),
            }),
          }
        }
        if (table === 'xfer_runs') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockResolvedValue({
                  data: [mockRun],
                  error: null,
                }),
              }),
            }),
          }
        }
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: [],
                error: null,
              }),
            }),
          }),
        }
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await act(async () => {
        render(
          <EntityProvider>
            <div data-testid="test">Test</div>
          </EntityProvider>
        )
      })

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          'Error loading available entities:',
          { message: 'Access denied' }
        )
      }, { timeout: 2000 })

      consoleSpy.mockRestore()
    })
  })
})

describe('useEntity Hook', () => {
  test('throws error when used outside EntityProvider', () => {
    const TestComponent = () => {
      useEntity()
      return <div>Test</div>
    }

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    expect(() => {
      render(<TestComponent />)
    }).toThrow('useEntity must be used within a EntityProvider')

    consoleSpy.mockRestore()
  })

  test('returns entity context when used within EntityProvider', async () => {
    // Setup mocks for this specific test
    mockSupabaseClient.from.mockImplementation((table) => {
      if (table === 'view_my_companies') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: mockAvailableEntities,
              error: null,
            }),
          }),
        }
      }
      
      if (table === 'xfer_entities') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: mockEntity,
                error: null,
              }),
            }),
          }),
        }
      }
      
      if (table === 'xfer_runs') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: [mockRun],
                error: null,
              }),
            }),
          }),
        }
      }

      return {
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      }
    })

    let result: any
    await act(async () => {
      const hook = renderHook(() => useEntity(), {
        wrapper: ({ children }) => <EntityProvider>{children}</EntityProvider>,
      })
      result = hook.result
      // Allow time for initial async operations
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    expect(result.current).toHaveProperty('entity')
    expect(result.current).toHaveProperty('run')
    expect(result.current).toHaveProperty('model')
    expect(result.current).toHaveProperty('includeDisclosures')
    expect(result.current).toHaveProperty('changeParams')
    expect(result.current).toHaveProperty('toggleDisclosures')
    expect(result.current).toHaveProperty('hash')
    expect(result.current).toHaveProperty('isLoading')
    expect(typeof result.current.changeParams).toBe('function')
    expect(typeof result.current.toggleDisclosures).toBe('function')
    expect(typeof result.current.hash).toBe('function')
    expect(typeof result.current.isLoading).toBe('function')
  })
})