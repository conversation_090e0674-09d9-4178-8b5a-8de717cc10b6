/**
 * Entity-Model-Run (EMR) Selector Component for ESG Analysis Navigation
 *
 * This React component provides a sophisticated navigation interface for the EkoIntelligence
 * ESG analysis platform, enabling users to select and switch between different ESG entities
 * (companies), analysis runs (time periods), AI models, and disclosure settings. Built with
 * Next.js 15 App Router architecture and Supabase real-time database integration, it serves
 * as the primary navigation control that drives the entire ESG dashboard experience.
 *
 * ## Core Functionality
 * - **Entity Selection**: Dynamic dropdown for accessible companies with quota-based filtering
 * - **Run Management**: Analysis run selection with temporal sorting and metadata display
 * - **Model Selection**: AI model switching (SDG, Doughnut, Plant Based Treaty, ekoIntelligence)
 * - **Disclosure Control**: Toggle switch for including/excluding disclosure documents in analysis
 * - **Real-time Updates**: Live data synchronization with Supabase database changes
 * - **Responsive Design**: Adaptive layout with glass-morphism styling for different screen sizes
 *
 * ## Architecture Pattern
 * **Context-Driven Navigation Controller**: Implements a sophisticated state management pattern
 * that coordinates with the EntityContext provider to maintain global application state while
 * providing local UI state management for dropdown interactions.
 *
 * **Data Flow Architecture**:
 * 1. **Context Integration**: Leverages `useEntity()` hook for global entity state management
 * 2. **Local State Management**: Maintains component-specific state for UI interactions and data loading
 * 3. **Database Synchronization**: Direct Supabase queries with abort controller for request cancellation
 * 4. **URL Parameter Sync**: Automatic synchronization of selections with browser URL parameters
 *
 * ## Database Schema Integration
 * **Multi-Table Data Coordination**: Works with synchronized customer database tables:
 * - **`view_my_companies`**: User-accessible companies with quota validation and RLS policies
 * - **`xfer_entities`**: ESG entity metadata synchronized from analytics database
 * - **`xfer_runs`**: Analysis run data with temporal scope and model configuration
 * - **`profiles`**: User profile data including avatar URLs and authentication context
 *
 * **Data Synchronization Flow**:
 * 1. **Analytics to Customer DB**: Python backend processes ESG data and syncs to `xfer_` tables
 * 2. **User Permission Layer**: `view_my_companies` applies Row Level Security for data access
 * 3. **Real-time Updates**: Supabase real-time features enable live updates to entity data
 * 4. **Version Compatibility**: Automatic conversion between V1 and V2 data formats for migration
 *
 * ## Entity-Model-Run (EMR) Pattern
 * **Three-Dimensional Selection Model**: Implements sophisticated selection logic:
 * - **Entity (E)**: ESG company/organization being analyzed
 * - **Model (M)**: AI analysis framework (SDG goals, doughnut economics, etc.)
 * - **Run (R)**: Temporal analysis scope with start/end years and analysis type
 *
 * **Selection Dependencies**:
 * - Entity selection resets run to 'latest' to ensure data consistency
 * - Model selection preserves entity and run but changes analysis framework
 * - Run selection maintains entity and model but changes temporal scope
 * - Disclosure toggle affects data filtering across all analysis types
 *
 * ## User Experience Design
 * **Glass-Morphism Navigation Interface**: Implements modern UI patterns:
 * - **Frosted Glass Effects**: Translucent backgrounds with backdrop blur for depth
 * - **Responsive Breakpoints**: Adaptive visibility (hidden on sm/md for some controls)
 * - **Loading States**: Disabled states during data fetching with visual feedback
 * - **Accessible Controls**: ARIA labels, keyboard navigation, and screen reader support
 * - **Test Integration**: Comprehensive `data-testid` attributes for automated testing
 *
 * ## Data Loading Strategy
 * **Optimized Data Fetching with Request Cancellation**:
 * 1. **Entity-Triggered Loading**: Data fetching triggered by entity context changes
 * 2. **Abort Controller Pattern**: Automatic cancellation of stale requests when entity changes
 * 3. **Error Boundary Handling**: Graceful error handling with abort error distinction
 * 4. **Avatar URL Generation**: Signed URL generation for user avatars with expiration
 * 5. **Data Transformation**: Automatic V2 to V1 format conversion for backward compatibility
 *
 * ## Performance Optimizations
 * **Efficient State Management**:
 * - **Request Cancellation**: AbortController prevents memory leaks from cancelled requests
 * - **Selective Re-renders**: useEffect dependencies minimize unnecessary re-renders
 * - **Data Sorting**: Client-side sorting of runs by completion date for performance
 * - **Conditional Rendering**: Responsive visibility controls reduce DOM complexity
 * - **Memoized Computations**: Efficient date formatting and data transformation
 *
 * ## Security Architecture
 * **Multi-Layer Security Implementation**:
 * - **Row Level Security**: Supabase RLS policies automatically filter accessible entities
 * - **Authentication Checks**: User authentication validation before data access
 * - **Quota Enforcement**: Server-side quota validation through view_my_companies
 * - **Request Validation**: Abort signal checking prevents processing cancelled requests
 * - **Session Management**: Secure session handling with automatic authentication refresh
 *
 * ## Integration Context
 * **ESG Dashboard Navigation Hub**: Serves as the central navigation component for:
 * - **Dashboard Pages**: Entity selection drives all dashboard content and analysis views
 * - **Report Generation**: Selected EMR parameters flow to report API endpoints
 * - **API Integration**: EMR state provides context for all backend API calls
 * - **URL Routing**: Navigation selections synchronize with Next.js App Router parameters
 * - **Component Communication**: EMR state flows to child components through entity context
 *
 * ## Usage Patterns
 * **Typical Implementation**:
 * ```typescript
 * // Page-level integration with navigation
 * function DashboardLayout() {
 *   return (
 *     <EntityProvider>
 *       <div className="dashboard">
 *         <EntityModelRunSelector navPath={[
 *           { label: "Dashboard", href: "/customer/dashboard" },
 *           { label: "Analysis" }
 *         ]} />
 *         <DashboardContent />
 *       </div>
 *     </EntityProvider>
 *   );
 * }
 *
 * // Component consumption of EMR state
 * function AnalysisView() {
 *   const { entity, run, model, includeDisclosures } = useEntity();
 *
 *   useEffect(() => {
 *     // Refetch analysis data when EMR parameters change
 *     if (entity && run && model) {
 *       loadAnalysisData(entity, run, model, includeDisclosures);
 *     }
 *   }, [entity, run, model, includeDisclosures]);
 * }
 * ```
 *
 * ## Responsive Design Strategy
 * **Multi-Breakpoint Visibility Control**:
 * - **Mobile (default)**: Shows only company selector for space optimization
 * - **Small (sm)**: Adds model selector and disclosure toggle for expanded functionality
 * - **Medium (md)**: Includes run selector for complete navigation control
 * - **Large (lg/xl)**: Full-width run selector with extended metadata display
 *
 * ## Testing Integration
 * **Comprehensive Test Coverage**: Extensive `data-testid` attributes enable:
 * - **Component Testing**: Unit tests for individual selector components
 * - **Integration Testing**: End-to-end workflow testing with Playwright
 * - **Accessibility Testing**: Screen reader and keyboard navigation validation
 * - **Performance Testing**: Load time and interaction responsiveness measurement
 *
 * @see https://supabase.com/docs/guides/database/postgres/row-level-security Supabase RLS Documentation
 * @see https://nextjs.org/docs/app/building-your-application/routing/parallel-routes Next.js Parallel Routes
 * @see https://react.dev/reference/react/useEffect React useEffect Hook
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/entity-context.tsx Entity Context Provider
 * @see /Users/<USER>/worktrees/279/apps/customer/components/page-header.tsx Page Header Component Integration
 * @see /Users/<USER>/worktrees/279/apps/customer/utils/entity-converter.ts Entity Data Format Conversion
 * @see /Users/<USER>/worktrees/279/apps/customer/utils/run-utils.ts Run Data Format Conversion
 * @see /Users/<USER>/worktrees/279/tmp/db/customer/schemas/public/views/view_my_companies.sql Company Access View Schema
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Interactive navigation component for ESG entity-model-run selection with real-time data synchronization
 * @example
 * ```typescript
 * // Basic navigation integration
 * function ESGDashboard() {
 *   return (
 *     <EntityProvider>
 *       <EntityModelRunSelector navPath={[
 *         { label: "Home", href: "/" },
 *         { label: "Dashboard" }
 *       ]} />
 *       <main>
 *         <AnalysisContent />
 *       </main>
 *     </EntityProvider>
 *   );
 * }
 *
 * // Advanced usage with custom navigation
 * function CustomAnalysisPage() {
 *   const navigationPath = [
 *     { label: "Analysis", href: "/customer/analysis" },
 *     { label: "Companies", href: "/customer/analysis/companies" },
 *     { label: "ESG Report" }
 *   ];
 *
 *   return (
 *     <div className="analysis-layout">
 *       <EntityModelRunSelector navPath={navigationPath} />
 *       <div className="analysis-content">
 *         {/* Analysis components automatically receive EMR context * /}
 *       </div>
 *     </div>
 *   );
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import React, { useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useEntity } from '@/components/context/entity/entity-context'
import { User } from '@supabase/supabase-js'
import { MyCompaniesType, ProfileType, RunType } from '@/types'
import { createClient } from '@/app/supabase/client'
import { runAsync } from '@utils/react-utils'
import { NavigationItem, PageHeader } from '@/components/page-header'
import { conciseDateTime } from '@utils/date-utils'
import { convertEntityV2ToEntityV1 } from '@/utils/entity-converter'
import { convertRunV2ToRunV1 } from '@/utils/run-utils'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'


export function EntityModelRunSelector({navPath}: { navPath: NavigationItem[] }) {
    const [runs, setRuns] = React.useState<RunType[]>([]);
    const [defaultRun, setDefaultRun] = React.useState<number | null>(null);
    const [user, setUser] = React.useState<User | null>(null);
    const [profile, setProfile] = React.useState<ProfileType | null>(null);
    const [entity, setEntity] = React.useState<any | null>(null);
    const [companies, setCompanies] = React.useState<MyCompaniesType[]>([]);
    const supabase = createClient();
    const entityData = useEntity();


  useEffect(() => {
    let abortController: AbortController | null = null;

    if (entityData.entity) {
      abortController = new AbortController();
      loadData(entityData.entity, abortController.signal);
    }

    // Cleanup function to cancel request when entity changes
    return () => {
      if (abortController) {
        abortController.abort();
      }
    };
  }, [entityData.entity])

  useEffect(() => {
    runAsync(async () => {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      
      // Only load companies if user is authenticated
      if (user?.id) {
        const response = await supabase.from('view_my_companies').select('*')
        setCompanies((response.data as MyCompaniesType[]) || [])
      } else {
        setCompanies([])
      }
    })
  }, [])


  async function loadData(entityId: string, abortSignal?: AbortSignal) {
        if (typeof window === 'undefined') {
            return;
        }

        try {
          // Try to get entity from xfer_entities first
            let entityQuery = supabase
              .from('xfer_entities')
                .select('*')
                .eq("entity_xid", entityId);

            if (abortSignal) {
                entityQuery = entityQuery.abortSignal(abortSignal);
            }

            const { data: entityV2Data, error: entityV2Error } = await entityQuery;

            // Check if request was cancelled
            if (abortSignal?.aborted) {
                return;
            }

            if (entityV2Data === null)
                return;

            // Convert the new format to the old format for compatibility
            const entityV1 = convertEntityV2ToEntityV1(entityV2Data[0]);
            setEntity(entityV1);

          // Try to get runs from xfer_runs first
            let runsQuery = supabase
              .from('xfer_runs')
                .select('*')
                .eq("scope", "entity")
                .eq("target", entityId);

            if (abortSignal) {
                runsQuery = runsQuery.abortSignal(abortSignal);
            }

            const { data: runsV2Data, error: runsV2Error } = await runsQuery;

            // Check if request was cancelled
            if (abortSignal?.aborted) {
                return;
            }

        let runsData: RunType[] = [];

        if (runsV2Data && runsV2Data.length > 0) {
            // Convert V2 runs to V1 format
            runsData = runsV2Data.map((i) => convertRunV2ToRunV1(i));
            // Sort by completed_at
            runsData.sort((a, b) => {
                if (!a.completed_at) return 1;
                if (!b.completed_at) return -1;
                return new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime();
            });
        }
        setRuns(runsData);
        let runId = entityData.run;

        if (runId === "latest") {
            setDefaultRun(runsData.length > 0 ? runsData[0]?.id : null);
        } else {
            setDefaultRun(runId ? +runId : null);
        }

        if (user) {
            const profileResponse = await supabase
                .from('profiles')
                .select('*')
                .eq('id', user.id)
                .single()
                
            let newProfile = profileResponse.data;
            if (newProfile && newProfile.avatar_url) {
                const avatarResponse = await supabase.storage.from('avatars').createSignedUrl(newProfile.avatar_url, 60)
                setProfile({...newProfile, avatar_url: avatarResponse.data?.signedUrl} as any);
            }
        }
        } catch (error) {
            if (error instanceof Error && error.name === 'AbortError') {
                return;
            }
            console.error('Error in loadData:', error);
        }
  }

    return (<PageHeader navPath={navPath}>
            <div className="flex flex-row gap-4 text-foreground pointer-events-auto  text-sm" data-testid="emr-selector">
                {entityData.entity &&
                    <div className="block">
                        <Select defaultValue={entityData.entity || "JN6ZWej7Rw"}
                                onValueChange={(value) => entityData.changeParams([{
                                    key: 'entity',
                                    value: value
                                }, { key: 'run', value: 'latest' }])}
                                disabled={Boolean(entityData.isLoading()) || !user?.id || companies.length === 0}
                        >
                            <SelectTrigger className="w-[180px]" data-testid="company-selector">
                                <SelectValue placeholder="Select Company"/>
                            </SelectTrigger>
                            <SelectContent className="bg-background" data-testid="company-dropdown">
                                {companies.length === 0 ? (
                                    <div className="px-2 py-1.5 text-sm text-muted-foreground" data-testid="no-companies-message">
                                        No companies available
                                    </div>
                                ) : (
                                    companies.map((company) => (
                                        <SelectItem key={company.entity_xid} value={company.entity_xid!} data-testid="company-option">
                                            <span data-testid="company-name">{company.name}</span>
                                        </SelectItem>
                                    ))
                                )}
                            </SelectContent>
                        </Select></div>}
                {entityData.run &&
                    <div className="hidden md:block text-xs">
                        <Select defaultValue={defaultRun ? "" + defaultRun : ""}
                                disabled={Boolean(entityData.isLoading()) || !user?.id}
                                onValueChange={(value) => entityData.changeParams([{key: 'run', value: value}])}>
                            <SelectTrigger className="w-40 lg:w-48 xl:w-[20rem]" data-testid="run-selector">
                                <SelectValue placeholder="Select Run"/>
                            </SelectTrigger>
                            <SelectContent className="bg-background" data-testid="run-dropdown">
                                {
                                    runs.map((run) => (
                                        <SelectItem key={run.id} value={"" + run.id} data-testid="run-option">
                                            <span data-testid="run-date">{run.completed_at ? conciseDateTime(new Date(run.completed_at), Date.now(), navigator.language) : 'No date'}</span> : {run.start_year} - {run.end_year || 'now'} ( <span data-testid="run-type">{run.id} {run.run_type}</span>)
                                        </SelectItem>))
                                }
                            </SelectContent>
                        </Select></div>}

                {entityData.model &&
                    <div className="hidden sm:block">
                      <Select disabled={Boolean(entityData.isLoading()) || !user?.id} defaultValue={entityData.model || 'ekoIntelligence'}
                                onValueChange={(value) => entityData.changeParams([{key: 'model', value}])}>
                            <SelectTrigger className="w-[140px]" data-testid="model-selector">
                                <SelectValue placeholder="Select Model"/>
                            </SelectTrigger>
                            <SelectContent className="bg-background" data-testid="model-dropdown">
                              <SelectItem value="eko" data-testid="model-option">ekoIntelligence</SelectItem>
                                <SelectItem value="sdg" data-testid="model-option">SDG</SelectItem>
                                <SelectItem value="doughnut" data-testid="model-option">Doughnut</SelectItem>
                                <SelectItem value="plant_based_treaty" data-testid="model-option">Plant Based Treaty</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                }

                {/* Disclosure toggle */}
                <div className="hidden sm:flex items-center space-x-2 ml-4">
                    <Switch
                        id="include-disclosures-nav"
                        data-testid="disclosure-toggle"
                        disabled={Boolean(entityData.isLoading()) || !user?.id}
                        checked={entityData.includeDisclosures}
                        onCheckedChange={entityData.toggleDisclosures}
                        className="data-[state=checked]:bg-brand"
                    />
                    <Label htmlFor="include-disclosures-nav" className="cursor-pointer text-slate-700 dark:text-slate-200 text-xs">
                        {entityData.includeDisclosures ? 'Include disclosures' : 'Exclude disclosures'}
                    </Label>
                </div>
            </div>
        </PageHeader>);

}
