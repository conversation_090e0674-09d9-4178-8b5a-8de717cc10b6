import React from 'react'
import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor, cleanup, fireEvent, act } from '@testing-library/react'
import { EntityModelRunSelector } from './emr-selector'
import type { RunType, MyCompaniesType } from '@/types'

// Mock Next.js navigation
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  usePathname: () => '/dashboard',
}))

// Mock entity context
const mockEntityData = {
  entity: 'test-entity-123' as string | null,
  run: 'latest' as string | 'latest',
  model: 'sdg' as string | 'ekoIntelligence',
  includeDisclosures: true,
  changeParams: vi.fn(),
  toggleDisclosures: vi.fn(),
  isLoading: vi.fn(() => false),
}

vi.mock('./entity-context', () => ({
  useEntity: () => mockEntityData,
}))

// Mock Supabase client
const mockSupabaseClient = {
  from: vi.fn(),
  auth: {
    getUser: vi.fn(),
  },
  storage: {
    from: vi.fn(),
  },
}

vi.mock('@/app/supabase/client', () => ({
  createClient: () => mockSupabaseClient,
}))

// Mock utility functions
vi.mock('@utils/react-utils', () => ({
  runAsync: (fn: () => Promise<void>) => {
    return Promise.resolve().then(() => fn())
  },
}))

vi.mock('@/utils/entity-converter', () => ({
  convertEntityV2ToEntityV1: (entity: any) => ({ ...entity, converted: true }),
}))

vi.mock('@/utils/run-utils', () => ({
  convertRunV2ToRunV1: (run: any) => ({ ...run, converted: true }),
}))

vi.mock('@utils/date-utils', () => ({
  conciseDateTime: (date: Date) => '2023-01-01 12:00',
}))

// Mock UI components
vi.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange, defaultValue, disabled }: any) => (
    <div data-testid="select" data-disabled={disabled} data-default-value={defaultValue} onClick={() => onValueChange?.('test-value')}>
      {children}
    </div>
  ),
  SelectContent: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  SelectItem: ({ children, value, ...props }: any) => (
    <div data-value={value} {...props}>{children}</div>
  ),
  SelectTrigger: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  SelectValue: ({ placeholder }: any) => <div data-testid="select-value">{placeholder}</div>,
}))

vi.mock('@/components/ui/switch', () => ({
  Switch: ({ checked, onCheckedChange, disabled, ...props }: any) => (
    <button
      data-checked={checked}
      data-disabled={disabled}
      onClick={() => onCheckedChange?.(!checked)}
      {...props}
    >
      {checked ? 'on' : 'off'}
    </button>
  ),
}))

vi.mock('@/components/ui/label', () => ({
  Label: ({ children, ...props }: any) => <label data-testid="label" {...props}>{children}</label>,
}))

vi.mock('@/components/page-header', () => ({
  PageHeader: ({ children, navPath }: any) => (
    <div data-testid="page-header" data-nav-path={JSON.stringify(navPath)}>
      {children}
    </div>
  ),
}))

// Mock data
const mockCompanies: MyCompaniesType[] = [
  {
    entity_xid: 'company-1',
    name: 'Test Company 1',
    id: 1,
    org_id: 1,
    profile_id: 'profile-1',
    quota: 100,
  },
  {
    entity_xid: 'company-2', 
    name: 'Test Company 2',
    id: 2,
    org_id: 1,
    profile_id: 'profile-1',
    quota: 100,
  },
]

const mockRuns: RunType[] = [
  {
    id: 123,
    run_type: 'full',
    scope: 'entity',
    target: 'test-entity-123',
    model: { name: 'test-model' },
    completed_at: '2023-01-01T00:00:00Z',
    start_year: 2022,
    end_year: 2023,
  },
  {
    id: 124,
    run_type: 'incremental',
    scope: 'entity',
    target: 'test-entity-123',
    model: { name: 'test-model' },
    completed_at: '2023-02-01T00:00:00Z',
    start_year: 2023,
    end_year: null,
  },
]

const mockNavPath = [
  { label: 'Dashboard', href: '/dashboard' },
  { label: 'EMR', href: '/emr' },
]

afterEach(() => {
  cleanup()
  vi.clearAllMocks()
})

describe('EntityModelRunSelector', () => {
  beforeEach(() => {
    // Setup default mock implementations
    mockSupabaseClient.from.mockImplementation((table) => {
      if (table === 'view_my_companies') {
        return {
          select: vi.fn().mockResolvedValue({ data: mockCompanies, error: null }),
        }
      }
      
      if (table === 'xfer_entities') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              abortSignal: vi.fn().mockResolvedValue({ data: [{ entity_xid: 'test-entity-123', name: 'Test Entity' }], error: null }),
            }),
          }),
        }
      }
      
      if (table === 'xfer_runs') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                abortSignal: vi.fn().mockResolvedValue({ data: mockRuns, error: null }),
              }),
            }),
          }),
        }
      }

      if (table === 'profiles') {
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({ data: { id: 'user-123', avatar_url: null }, error: null }),
            }),
          }),
        }
      }

      return {
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      }
    })

    mockSupabaseClient.storage.from.mockReturnValue({
      createSignedUrl: vi.fn().mockResolvedValue({
        data: { signedUrl: 'http://example.com/avatar.jpg' },
        error: null,
      }),
    })

    // Reset entity context mocks
    mockEntityData.changeParams.mockClear()
    mockEntityData.toggleDisclosures.mockClear()
    mockEntityData.isLoading.mockReturnValue(false)
    
    // Reset auth mock
    mockSupabaseClient.auth.getUser.mockResolvedValue({ 
      data: { user: { id: 'test-user-123' } },
      error: null
    })
  })

  describe('Basic Rendering', () => {
    test('renders without crashing', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      expect(screen.getByTestId('page-header')).toBeInTheDocument()
      expect(screen.getByTestId('emr-selector')).toBeInTheDocument()
    })

    test('passes nav path to PageHeader correctly', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      const pageHeader = screen.getByTestId('page-header')
      expect(pageHeader).toHaveAttribute('data-nav-path', JSON.stringify(mockNavPath))
    })
  })

  describe('Company Selector', () => {
    test('renders company selector when entity is available', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      expect(screen.getByTestId('company-selector')).toBeInTheDocument()
      expect(screen.getByTestId('company-dropdown')).toBeInTheDocument()
    })

    test('shows "No companies available" when no companies are loaded', async () => {
      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'view_my_companies') {
          return {
            select: vi.fn().mockResolvedValue({ data: [], error: null }),
          }
        }
        
        if (table === 'xfer_entities') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                abortSignal: vi.fn().mockResolvedValue({ data: [{ entity_xid: 'test-entity-123', name: 'Test Entity' }], error: null }),
              }),
            }),
          }
        }
        
        if (table === 'xfer_runs') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  abortSignal: vi.fn().mockResolvedValue({ data: mockRuns, error: null }),
                }),
              }),
            }),
          }
        }
        
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: [],
                error: null,
              }),
            }),
          }),
        }
      })

      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      await waitFor(() => {
        expect(screen.getByTestId('no-companies-message')).toBeInTheDocument()
        expect(screen.getByTestId('no-companies-message')).toHaveTextContent('No companies available')
      })
    })

    test('renders company options when companies are available', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      // Wait for companies to load
      await waitFor(() => {
        const companyDropdown = screen.getByTestId('company-dropdown')
        expect(companyDropdown).toBeInTheDocument()
        
        // If there are companies, check that company options are shown
        if (screen.queryAllByTestId('company-option').length > 0) {
          const companyOptions = screen.getAllByTestId('company-option')
          expect(companyOptions).toHaveLength(2)
          
          const companyNames = screen.getAllByTestId('company-name')
          expect(companyNames[0]).toHaveTextContent('Test Company 1')
          expect(companyNames[1]).toHaveTextContent('Test Company 2')
        } else {
          // If no companies, that should be shown in the message  
          expect(screen.getByTestId('no-companies-message')).toBeInTheDocument()
        }
      })
    })

    test('is disabled when loading', async () => {
      mockEntityData.isLoading.mockReturnValue(true)
      
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      const selects = screen.getAllByTestId('select')
      selects.forEach(select => {
        expect(select).toHaveAttribute('data-disabled', 'true')
      })
    })

    test('calls changeParams when company selection changes', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      // Find the Select element and trigger its onClick
      const selectElement = screen.getByTestId('company-selector').closest('[data-testid="select"]')
      expect(selectElement).toBeTruthy()
      fireEvent.click(selectElement!)
      
      expect(mockEntityData.changeParams).toHaveBeenCalledWith([
        { key: 'entity', value: 'test-value' },
        { key: 'run', value: 'latest' },
      ])
    })
  })

  describe('Run Selector', () => {
    test('renders run selector when run is available', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      expect(screen.getByTestId('run-selector')).toBeInTheDocument()
      expect(screen.getByTestId('run-dropdown')).toBeInTheDocument()
    })

    test('is hidden on mobile devices', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      // The run selector should be present - we'll test the responsive classes in an integration test
      const runSelector = screen.getByTestId('run-selector')
      expect(runSelector).toBeInTheDocument()
    })

    test('renders run options with correct format', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      // Wait for runs to be loaded and rendered
      await waitFor(() => {
        const runDropdown = screen.getByTestId('run-dropdown')
        expect(runDropdown).toBeInTheDocument()
        
        const runOptions = screen.getAllByTestId('run-option')
        expect(runOptions.length).toBeGreaterThan(0)
      })
    })
  })

  describe('Model Selector', () => {
    test('renders model selector when model is available', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      expect(screen.getByTestId('model-selector')).toBeInTheDocument()
      expect(screen.getByTestId('model-dropdown')).toBeInTheDocument()
    })

    test('is hidden on small screens', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      // The model selector should be present - we'll test the responsive classes in an integration test
      const modelSelector = screen.getByTestId('model-selector')
      expect(modelSelector).toBeInTheDocument()
    })

    test('has all model options available', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      const modelOptions = screen.getAllByTestId('model-option')
      expect(modelOptions).toHaveLength(4)
      
      const optionValues = modelOptions.map(option => option.getAttribute('data-value'))
      expect(optionValues).toContain('eko')
      expect(optionValues).toContain('sdg')
      expect(optionValues).toContain('doughnut')
      expect(optionValues).toContain('plant_based_treaty')
    })

    test('calls changeParams when model selection changes', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      // Find the Select element and trigger its onClick
      const selectElement = screen.getByTestId('model-selector').closest('[data-testid="select"]')
      expect(selectElement).toBeTruthy()
      fireEvent.click(selectElement!)
      
      expect(mockEntityData.changeParams).toHaveBeenCalledWith([
        { key: 'model', value: 'test-value' }
      ])
    })
  })

  describe('Disclosure Toggle', () => {
    test('renders disclosure toggle', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      expect(screen.getByTestId('disclosure-toggle')).toBeInTheDocument()
      expect(screen.getByTestId('label')).toBeInTheDocument()
    })

    test('is hidden on small screens', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      const disclosureContainer = screen.getByTestId('disclosure-toggle').closest('div')
      expect(disclosureContainer).toHaveClass('hidden', 'sm:flex')
    })

    test('shows correct label based on disclosure state', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      const label = screen.getByTestId('label')
      expect(label).toHaveTextContent('Include disclosures')
    })

    test('shows exclude label when disclosures are disabled', async () => {
      mockEntityData.includeDisclosures = false
      
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      const label = screen.getByTestId('label')
      expect(label).toHaveTextContent('Exclude disclosures')
    })

    test('calls toggleDisclosures when clicked', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      const toggle = screen.getByTestId('disclosure-toggle')
      await act(async () => {
        fireEvent.click(toggle)
      })
      
      expect(mockEntityData.toggleDisclosures).toHaveBeenCalledWith(true)
    })

    test('is disabled when loading', async () => {
      mockEntityData.isLoading.mockReturnValue(true)
      
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      const toggle = screen.getByTestId('disclosure-toggle')
      expect(toggle).toHaveAttribute('data-disabled', 'true')
    })
  })

  describe('Data Loading', () => {
    test('loads companies data on mount', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      await waitFor(() => {
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('view_my_companies')
      })
    })

    test('handles companies loading error gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'view_my_companies') {
          return {
            select: vi.fn().mockResolvedValue({ data: null, error: { message: 'Network error' } }),
          }
        }
        
        if (table === 'xfer_entities') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                abortSignal: vi.fn().mockResolvedValue({ data: [{ entity_xid: 'test-entity-123', name: 'Test Entity' }], error: null }),
              }),
            }),
          }
        }
        
        if (table === 'xfer_runs') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  abortSignal: vi.fn().mockResolvedValue({ data: mockRuns, error: null }),
                }),
              }),
            }),
          }
        }
        
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: [],
                error: null,
              }),
            }),
          }),
        }
      })

      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      await waitFor(() => {
        expect(screen.getByTestId('no-companies-message')).toBeInTheDocument()
      })

      consoleSpy.mockRestore()
    })

    test('loads entity and run data when entity changes', async () => {
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      await waitFor(() => {
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('xfer_entities')
      })
    })

    test('handles abort signal correctly', async () => {
      const abortController = new AbortController()
      
      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'xfer_entities') {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                abortSignal: vi.fn().mockImplementation((signal) => {
                  // Simulate abort - just return empty data
                  return Promise.resolve({ data: null, error: null })
                }),
              }),
            }),
          }
        }
        return {
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockResolvedValue({
                data: [],
                error: null,
              }),
            }),
          }),
        }
      })

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      await waitFor(() => {
        expect(screen.getByTestId('emr-selector')).toBeInTheDocument()
      })

      consoleSpy.mockRestore()
    })
  })

  describe('Conditional Rendering', () => {
    test('only renders company selector when entity is available', async () => {
      mockEntityData.entity = null
      
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      expect(screen.queryByTestId('company-selector')).not.toBeInTheDocument()
    })

    test('only renders run selector when run is available', async () => {
      (mockEntityData.run as any) = null
      
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      expect(screen.queryByTestId('run-selector')).not.toBeInTheDocument()
    })

    test('only renders model selector when model is available', async () => {
      (mockEntityData.model as any) = null
      
      await act(async () => {
        render(<EntityModelRunSelector navPath={mockNavPath} />)
      })
      
      expect(screen.queryByTestId('model-selector')).not.toBeInTheDocument()
    })
  })
})