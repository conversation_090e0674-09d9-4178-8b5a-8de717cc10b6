/**
 * Abstract Base Data Fetcher for ESG Entity Analysis Data Operations
 *
 * This abstract base class provides a robust foundation for implementing type-safe data fetching
 * operations within the EkoIntelligence ESG analysis platform. It establishes a standardized
 * pattern for managing concurrent data requests, handling request cancellation, and providing
 * consistent error handling across all entity-related data fetchers (claims, promises, flags, etc.).
 *
 * ## Core Functionality
 * - **Request Management**: Centralized tracking and cancellation of concurrent data requests
 * - **Abort Control**: Automatic cancellation of stale requests when newer ones are initiated
 * - **Type Safety**: Generic TypeScript implementation ensuring type-safe data operations
 * - **Error Handling**: Standardized error management with consistent logging patterns
 * - **Database Integration**: Supabase client integration for secure database access with RLS
 * - **Request Deduplication**: Prevents duplicate requests through intelligent request tracking
 *
 * ## Architecture Pattern
 * **Template Method Pattern**: Abstract class defines the structure while concrete implementations
 * provide specific data fetching logic for different ESG data types (claims, promises, flags).
 * Each subclass implements:
 * - `fetch()`: Specific data retrieval logic for the data type
 * - `validateDependencies()`: Custom validation for required parameters
 *
 * ## Request Lifecycle Management
 * **Concurrent Request Handling**:
 * 1. **Request Creation**: Generate unique request ID with timestamp and randomization
 * 2. **Stale Request Cancellation**: Abort existing requests of the same type automatically
 * 3. **Fresh Request Execution**: Execute new request with abort controller for cancellation
 * 4. **Staleness Detection**: Check if request is still current before processing results
 * 5. **State Management**: Update loading states and data only for current requests
 *
 * ## Data Fetcher Dependencies
 * **Required Parameters**: All data fetchers require consistent dependency structure:
 * - `entity` (string): ESG entity identifier for data scoping
 * - `runObject` (RunType): Analysis run metadata with timing and configuration
 * - `model` (string, optional): AI model identifier for analysis context
 * - `includeDisclosures` (boolean, optional): Flag for including disclosure documents
 *
 * ## Supabase Integration
 * **Database Client**: Uses `createClient()` from `@/app/supabase/client` for:
 * - **Authenticated Access**: Leverages user session for Row Level Security (RLS)
 * - **Type Safety**: Typed database client with generated TypeScript interfaces
 * - **Real-time Capabilities**: Support for real-time subscriptions and live updates
 * - **Secure Queries**: Automatic application of security policies for data access
 *
 * ## Error Management Strategy
 * **Graceful Error Handling**:
 * - **AbortError Recognition**: Distinguishes cancelled requests from genuine errors
 * - **Contextual Logging**: Provides detailed error context for debugging and monitoring
 * - **User Experience**: Prevents error noise from cancelled requests while preserving genuine error reporting
 * - **Recovery Patterns**: Consistent error recovery across all data fetcher implementations
 *
 * ## System Architecture Context
 * **ESG Data Platform Integration**:
 * - **Analytics Backend**: Python system generates ESG analysis data stored in analytics database
 * - **Data Synchronization**: `xfer_` tables sync processed data between analytics and customer databases
 * - **API Layer**: This fetcher system provides customer-facing access to synchronized ESG data
 * - **Frontend Components**: React components consume data through these fetchers for dashboard display
 * - **Real-time Updates**: Supabase real-time features enable live data updates in customer interface
 *
 * ## Usage Patterns
 * **Typical Implementation Flow**:
 * ```typescript
 * // Concrete fetcher extends BaseDataFetcher
 * class ClaimsDataFetcher extends BaseDataFetcher<ClaimTypeV2[]> {
 *   validateDependencies(deps) { return !!(deps.entity && deps.runObject) }
 *   async fetch(deps, setData, setLoading) {
 *     const { requestId, abortController, isStale } = this.setupRequestCancellation(deps);
 *     // Perform Supabase query with abort signal
 *     // Check staleness before updating state
 *   }
 * }
 * ```
 *
 * ## Security Considerations
 * - **Row Level Security**: All database queries automatically apply RLS policies based on user session
 * - **Data Isolation**: Users can only access data for entities they have permissions for
 * - **Request Validation**: Dependencies are validated before processing to prevent unauthorized access
 * - **Session Management**: Supabase client handles authentication and session persistence
 *
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching Next.js Data Fetching
 * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortController AbortController API
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/data/claims-data-fetcher.ts Claims Data Fetcher Implementation
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/data/promises-data-fetcher.ts Promises Data Fetcher Implementation
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/data/flags-data-fetcher.ts Flags Data Fetcher Implementation
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Abstract base class for implementing type-safe, concurrent-aware data fetching operations in the ESG analysis platform
 * @example
 * ```typescript
 * // Implementing a concrete data fetcher
 * class MyDataFetcher extends BaseDataFetcher<MyDataType[]> {
 *   constructor(requestRefs, abortControllers) {
 *     super('mydata', requestRefs, abortControllers);
 *   }
 *   
 *   validateDependencies(deps) {
 *     return !!(deps.entity && deps.runObject);
 *   }
 *   
 *   async fetch(deps, setData, setLoading) {
 *     const { requestId, abortController, isStale } = this.setupRequestCancellation(deps);
 *     setLoading(true);
 *     
 *     try {
 *       const { data, error } = await this.supabase
 *         .from('my_table')
 *         .select('*')
 *         .eq('entity_id', deps.entity)
 *         .abortSignal(abortController.signal);
 *         
 *       if (isStale()) return;
 *       
 *       if (error) throw error;
 *       setData(data);
 *     } catch (error) {
 *       if (!this.handleAbortError(error)) {
 *         this.logError(error, 'MyDataFetcher.fetch');
 *       }
 *     } finally {
 *       if (!isStale()) setLoading(false);
 *     }
 *   }
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import { createClient } from '@/app/supabase/client';
import { RunType } from '@/types';

export interface RequestCancellation {
    requestId: string;
    abortController: AbortController;
    isStale: () => boolean;
}

export interface DataFetcherDependencies {
    entity: string | null;
    runObject: RunType | null;
    model?: string;
    includeDisclosures?: boolean;
}

export abstract class BaseDataFetcher<T> {
    protected supabase = createClient();
    protected requestType: string;
    protected currentRequestRefs: Map<string, string>;
    protected abortControllersRef: Map<string, AbortController>;

    constructor(
        requestType: string,
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        this.requestType = requestType;
        this.currentRequestRefs = currentRequestRefs;
        this.abortControllersRef = abortControllersRef;
    }

    protected createRequestId(dependencies: DataFetcherDependencies): string {
        const { entity, runObject } = dependencies;
        return `${this.requestType}-${entity}-${runObject?.id || 'no-run'}-${Date.now()}-${Math.random()}`;
    }

    protected setupRequestCancellation(dependencies: DataFetcherDependencies): RequestCancellation {
        const requestId = this.createRequestId(dependencies);
        this.currentRequestRefs.set(this.requestType, requestId);

        // Cancel any existing request for this type
        const existingController = this.abortControllersRef.get(this.requestType);
        if (existingController) {
            existingController.abort();
        }

        // Create new abort controller
        const abortController = new AbortController();
        this.abortControllersRef.set(this.requestType, abortController);

        // Function to check if this request is still current for this specific request type
        const isStale = () => this.currentRequestRefs.get(this.requestType) !== requestId;

        return { requestId, abortController, isStale };
    }

    protected handleAbortError(error: unknown): boolean {
        if (error instanceof Error && error.name === 'AbortError') {
            console.log(`${this.requestType} request was cancelled`);
            return true;
        }
        return false;
    }

    protected logError(error: unknown, context: string): void {
        console.error(`Error in ${context}:`, error);
    }

    protected logSuccess(message: string, count?: number): void {
        const countText = count !== undefined ? `: ${count}` : '';
        console.log(`${message}${countText}`);
    }

    protected logCancellation(): void {
        console.log(`${this.requestType} request cancelled - newer request in progress`);
    }

    abstract fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: T | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void>;

    abstract validateDependencies(dependencies: DataFetcherDependencies): boolean;
}
