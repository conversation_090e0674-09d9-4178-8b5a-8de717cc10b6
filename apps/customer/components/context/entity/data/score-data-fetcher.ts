/**
 * ESG Entity Score Data Fetcher for Corporate Sustainability Scoring
 *
 * This specialized data fetcher implements optimized retrieval and processing of ESG (Environmental, Social,
 * Governance) entity scores from the EkoIntelligence analytics platform. Entity scores represent comprehensive
 * sustainability assessments that quantify corporate ESG performance based on claims, promises, flags, and
 * other analysis components. The fetcher extends BaseDataFetcher to provide reliable score data access with
 * intelligent error handling and request cancellation capabilities.
 *
 * ## Core Functionality
 * - **Score Retrieval**: Fetches comprehensive ESG entity scores with full model data
 * - **Fallback Handling**: Provides graceful degradation with default scores when data is unavailable
 * - **Performance Optimization**: Single-row query design for minimal database overhead
 * - **Type Safety**: Full TypeScript integration with generated database types
 * - **Request Management**: Intelligent request cancellation and staleness detection
 * - **Model Integration**: Seamless integration with score utility functions for data processing
 *
 * ## ESG Score System Overview
 * **Entity Scores** represent comprehensive sustainability assessments calculated by the analytics backend.
 * Each score is derived from analysis of corporate claims, promises, effect flags, and other ESG indicators.
 * Scores range from 0-100 and include detailed breakdowns of positive and negative factors contributing
 * to the overall assessment.
 *
 * **Score Components**:
 * - **Overall Score**: Composite ESG performance score (0-100)
 * - **Rating Text**: Human-readable rating classification (e.g., 'Excellent', 'Good', 'Poor')
 * - **Severity Assessment**: Impact classification (e.g., 'Minor', 'Major', 'Very Serious')
 * - **Flag Analysis**: Red and green flag counts with average scores
 * - **Statistical Metrics**: Median values and distribution analysis
 *
 * ## Data Source & Database Schema
 * **Primary Table**: `xfer_score` (Customer Database)
 * 
 * **Table Structure**:
 * ```sql
 * CREATE TABLE xfer_score (
 *   entity_xid text NOT NULL,           -- ESG entity identifier (primary key)
 *   run_id integer NOT NULL,            -- Analysis run identifier (primary key)
 *   score numeric NOT NULL,             -- Overall ESG score (0-100)
 *   model jsonb NOT NULL                -- Complete XferScoreModel data structure
 * );
 * ```
 *
 * **XferScoreModel JSON Structure**:
 * ```typescript
 * {
 *   entity_xid: string;                 -- Entity identifier
 *   score: number;                      -- Numeric score (0-100)
 *   rating_text: string;                -- Rating classification
 *   minor_major_text: string;           -- Severity assessment
 *   red_flags_count: number;            -- Count of negative indicators
 *   green_flags_count: number;          -- Count of positive indicators
 *   red_flags_score: number;            -- Average negative impact score
 *   green_flags_score: number;          -- Average positive impact score
 *   average_red: number;                -- Mean red flag intensity
 *   average_green: number;              -- Mean green flag intensity
 *   median_red: number;                 -- Median red flag value
 *   median_green: number;               -- Median green flag value
 *   created_at?: string;                -- ISO timestamp of score generation
 * }
 * ```
 *
 * ## Query Strategy & Performance
 * **Database Access Pattern**:
 * 1. **Single Record Query**: Uses `.single()` for exact entity-run combination
 * 2. **Primary Key Access**: Leverages composite primary key (entity_xid, run_id) for optimal performance
 * 3. **Full Model Retrieval**: Fetches complete score model for comprehensive analysis
 * 4. **Error Resilience**: Handles missing data gracefully with default fallback values
 *
 * **Performance Optimizations**:
 * - **Index Utilization**: Queries use optimized B-tree indexes on entity_xid and run_id
 * - **Minimal Data Transfer**: Single row retrieval reduces network overhead
 * - **JSON Processing**: Client-side model parsing for flexible data access
 * - **Request Cancellation**: Prevents unnecessary database load from stale requests
 *
 * ## Integration with Score Utilities
 * **Score Processing Pipeline**:
 * 1. **Data Retrieval**: Fetches raw ScoreTypeV2 data from xfer_score table
 * 2. **Utility Processing**: Passes data to `getScoreValue()` for score extraction
 * 3. **State Management**: Updates component state with both numeric score and full model
 * 4. **UI Integration**: Provides data structure for score display components
 *
 * **Utility Functions Integration** (from `/utils/score-utils.ts`):
 * - `getScoreValue()`: Extracts numeric score from ScoreTypeV2 structure
 * - `getRatingText()`: Gets human-readable rating classification
 * - `getSeverityText()`: Retrieves impact severity assessment
 * - `getScoreColorClass()`: Determines CSS color class based on score ranges
 *
 * ## Request Lifecycle Management
 * **Dependency Validation**:
 * - Verifies `entity` (string) is provided for database query targeting
 * - Confirms `runObject` exists with valid `id` for run-specific data retrieval
 * - Returns default values immediately if dependencies are missing
 *
 * **Error Handling Strategy**:
 * 1. **Missing Data**: Returns `{ score: 0, scoreData: null }` when no records found
 * 2. **Database Errors**: Logs error context and provides fallback values
 * 3. **Request Cancellation**: Handles AbortError gracefully without logging noise
 * 4. **Staleness Protection**: Prevents state updates from cancelled requests
 *
 * ## System Architecture Context
 * **ESG Analytics Pipeline Integration**:
 * - **Analytics Backend**: Python system calculates comprehensive ESG scores from multiple data sources
 * - **Data Synchronization**: `xfer_score` table receives processed scores from analytics database
 * - **API Layer**: This fetcher provides customer-facing access to synchronized score data
 * - **Frontend Integration**: React components consume score data for dashboard and report display
 * - **Real-time Capabilities**: Supabase infrastructure supports live score updates when available
 *
 * ## Usage Patterns
 * **Typical Implementation Flow**:
 * ```typescript
 * const scoreDataFetcher = new ScoreDataFetcher(requestRefs, abortControllers);
 * 
 * const dependencies = {
 *   entity: 'entity-123',
 *   runObject: { id: 456, ...otherRunData }
 * };
 * 
 * await scoreDataFetcher.fetch(
 *   dependencies,
 *   (data) => setScoreData(data),
 *   (loading) => setLoadingState(loading)
 * );
 * ```
 *
 * **Data Structure Returned**:
 * ```typescript
 * interface ScoreData {
 *   score: number;                      // 0-100 ESG performance score
 *   scoreData: ScoreTypeV2 | null;      // Complete score model or null if unavailable
 * }
 * ```
 *
 * ## Security & Access Control
 * - **Row Level Security**: Database queries automatically apply RLS policies based on user session
 * - **Entity Access Control**: Users can only access scores for entities they have permissions for
 * - **Data Isolation**: Supabase RLS ensures proper data boundaries between organizations
 * - **Request Validation**: Dependencies validated before database access to prevent unauthorized queries
 *
 * ## Error Recovery & Resilience
 * **Graceful Degradation**:
 * - Missing entity or run data results in default score (0) rather than application errors
 * - Database connection issues are logged but don't crash the user interface
 * - Request cancellation is handled transparently without user-visible errors
 * - Component state remains stable during error conditions
 *
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching Next.js Data Fetching Patterns
 * @see https://www.typescriptlang.org/docs/handbook/generics.html TypeScript Generics Documentation
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/data/base-data-fetcher.ts Base Data Fetcher Implementation
 * @see /Users/<USER>/worktrees/279/apps/customer/utils/score-utils.ts Score Utility Functions
 * @see /Users/<USER>/worktrees/279/apps/customer/types/score.ts Score Type Definitions
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Specialized data fetcher for retrieving and processing ESG entity scores with comprehensive model data and utility integration
 * @example
 * ```typescript
 * // Initialize score data fetcher
 * const scoreDataFetcher = new ScoreDataFetcher(
 *   new Map(), // request references
 *   new Map()  // abort controllers
 * );
 * 
 * // Define dependencies
 * const dependencies = {
 *   entity: 'apple-inc',
 *   runObject: { id: 123, created_at: '2024-01-01', status: 'completed' }
 * };
 * 
 * // Fetch score data
 * await scoreDataFetcher.fetch(
 *   dependencies,
 *   (data) => {
 *     if (data?.scoreData) {
 *       console.log(`Entity Score: ${data.score}`);
 *       console.log(`Rating: ${getRatingText(data.scoreData)}`);
 *       console.log(`Red Flags: ${getRedFlagsCount(data.scoreData)}`);
 *     }
 *   },
 *   (loading) => setLoadingState(loading)
 * );
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import { ScoreTypeV2 } from '@/types'
import { getScoreValue } from '@/utils/score-utils'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export interface ScoreData {
    score: number;
    scoreData: ScoreTypeV2 | null;
}

export class ScoreDataFetcher extends BaseDataFetcher<ScoreData> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('score', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: ScoreData | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData({ score: 0, scoreData: null });
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            const { data: scoreV2Data, error: scoreV2Error } = await this.supabase
              .from('xfer_score')
                .select('*')
                .eq('run_id', runObject!.id)
                .eq('entity_xid', entity!)
                .abortSignal(abortController.signal)
                .single();

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (scoreV2Data) {
                // Get score from V2 format
                const scoreV2DataTyped = scoreV2Data as unknown as ScoreTypeV2;
                const scoreValue = getScoreValue(scoreV2DataTyped);
                if (!isStale()) {
                    setData({ score: scoreValue, scoreData: scoreV2DataTyped });
                    this.logSuccess("Loaded score data", scoreValue);
                }
            } else {
                if (scoreV2Error) {
                    this.logError(scoreV2Error, 'fetchScoreData');
                }
                // Set default score if no data found
                if (!isStale()) {
                    setData({ score: 0, scoreData: null });
                }
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchScoreData');
            if (!isStale()) {
                setData({ score: 0, scoreData: null });
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }
}
