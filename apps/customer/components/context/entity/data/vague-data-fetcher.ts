/**
 * Vague Terms Data Fetcher for ESG Language Ambiguity Analysis
 *
 * This specialized data fetcher implements efficient retrieval and processing of vague language analysis
 * from the EkoIntelligence ESG analytics platform. It extends the BaseDataFetcher abstract class to provide
 * optimized access to vague terminology analysis data, handling both aggregated summary reports and detailed
 * individual term breakdowns for comprehensive language ambiguity assessment in corporate ESG communications.
 *
 * ## Core Functionality
 * - **Vague Terms Analysis**: Fetches AI-powered analysis of ambiguous language in ESG documents
 * - **Dual Data Retrieval**: Simultaneously retrieves summary and detailed vague term data
 * - **Fallback Architecture**: Implements graceful degradation from V2 to V1 data sources when needed
 * - **Performance Optimization**: Uses targeted queries with efficient indexing for fast data access
 * - **Request Management**: Provides intelligent request cancellation and staleness detection
 * - **Data Validation**: Ensures data integrity through comprehensive dependency validation
 *
 * ## Data Source & Structure
 * **Primary Table**: `_deprecated_xfer_gw_vague_v2` (Version 2 format)
 * - **id**: integer NOT NULL - Primary key identifier for each vague term record
 * - **entity_xid**: text NOT NULL - ESG entity identifier linking analysis to specific organizations
 * - **run_id**: integer NOT NULL - Analysis run identifier for data versioning and temporal tracking
 * - **phrase**: text - The vague term or special identifier ("__summary__" for aggregated data)
 * - **model**: jsonb NOT NULL - Complete vague term analysis data in flexible JSON format
 *
 * ## Database Optimization Features
 * **High-Performance Indexing**:
 * - **Composite Index**: `(entity_xid, run_id)` for efficient entity-run combinations
 * - **Entity Index**: B-tree index on `entity_xid` for entity-specific queries
 * - **GIN Indexes**: Multiple JSON indexes on `model` field for fast JSONB queries
 * - **Phrase Index**: B-tree index on `phrase` for summary/detail data separation
 * - **Run Index**: B-tree index on `run_id` for temporal data access
 *
 * ## Vague Terms Analysis Model
 * **XferVagueModel Structure** (stored in JSONB `model` field):
 * - **id**: Unique identifier for the vague term analysis
 * - **entity_xid**: Entity identifier for cross-referencing
 * - **phrase**: The specific vague term or language pattern identified
 * - **score**: Numeric ambiguity score indicating the severity of vagueness
 * - **explanation**: AI-generated explanation of why the term is considered vague
 * - **analysis**: Detailed linguistic analysis of the term's ambiguity impact
 * - **summary**: Consolidated assessment of the vague language usage
 * - **citations**: Array of source document citations supporting the analysis
 * - **rank**: Optional ranking for term importance or frequency
 * - **created_at**: ISO timestamp for temporal analysis tracking
 *
 * ## Query Architecture
 * **Two-Phase Data Retrieval**:
 * 1. **Summary Data Query**: 
 *    - Filters by `phrase = "__summary__"` to retrieve aggregated vague language assessment
 *    - Uses `ORDER BY id DESC LIMIT 1` to get the most recent summary analysis
 *    - Provides high-level overview of entity's vague language usage patterns
 * 
 * 2. **Detail Data Query**:
 *    - Filters by `phrase != "__summary__"` to retrieve individual vague term analyses
 *    - Returns comprehensive list of specific ambiguous terms and phrases
 *    - Enables detailed examination of language precision issues
 *
 * ## Data Processing Flow
 * **Retrieval Strategy**:
 * 1. **Dependency Validation**: Ensures entity identifier and run object are available
 * 2. **Request Setup**: Creates unique request ID and abort controller for cancellation management
 * 3. **Summary Fetch**: Retrieves aggregated vague language analysis from V2 table
 * 4. **Detail Fetch**: Retrieves individual vague term breakdowns from V2 table
 * 5. **Fallback Handling**: Attempts V1 table access if V2 data is unavailable (currently disabled)
 * 6. **Data Consolidation**: Combines summary and detail data into unified response structure
 * 7. **State Management**: Updates loading states and data only for non-stale requests
 *
 * ## Legacy System Integration
 * **Version 2 Architecture**: This fetcher primarily uses the V2 table format which consolidates
 * previously fragmented vague term data into a single JSONB-based structure for improved performance.
 * **Deprecated V1 Support**: Contains placeholder methods for V1 fallback, but V1 tables are not
 * available in the customer database, making this a V2-only implementation.
 *
 * ## Error Handling & Resilience
 * **Comprehensive Error Management**:
 * - **Abort Error Recognition**: Distinguishes cancelled requests from genuine database errors
 * - **Graceful Degradation**: Provides fallback mechanisms when primary data sources are unavailable
 * - **Staleness Protection**: Prevents processing of outdated requests through intelligent timing checks
 * - **User Experience**: Maintains smooth UX by avoiding error noise from cancelled operations
 * - **Logging Strategy**: Provides detailed context for debugging while preserving clean user interface
 *
 * ## System Architecture Context
 * **ESG Language Analysis Pipeline**:
 * - **Analytics Backend**: Python NLP system processes corporate documents to identify vague language
 * - **Data Synchronization**: Processed analysis results are synchronized to customer database via `xfer_` tables
 * - **API Layer**: This fetcher provides customer-facing access to vague language analysis data
 * - **Frontend Integration**: React components consume this data for language precision dashboards
 * - **Real-time Updates**: Supabase integration enables live updates when new analysis results are available
 *
 * ## Security & Access Control
 * **Row Level Security (RLS) Policies**:
 * - **Authenticated Access**: Requires user authentication for all data access operations
 * - **Admin Controls**: Special delete permissions reserved for administrative users
 * - **Data Isolation**: Users can only access vague term analysis for entities they have permissions for
 * - **Session Management**: Leverages Supabase authentication for secure, session-based access control
 *
 * ## Performance Characteristics
 * **Optimization Features**:
 * - **Targeted Queries**: Fetches only essential fields to minimize data transfer overhead
 * - **Index Utilization**: Leverages multiple database indexes for sub-millisecond query response
 * - **Request Deduplication**: Prevents redundant database calls through intelligent request tracking
 * - **Memory Efficiency**: Uses streaming data patterns to minimize client-side memory usage
 * - **Connection Pooling**: Benefits from Supabase connection pooling for database efficiency
 *
 * ## Usage Patterns
 * **Typical Implementation Flow**:
 * ```typescript
 * const vagueDataFetcher = new VagueDataFetcher(requestRefs, abortControllers);
 * 
 * // Fetch vague language analysis for an entity
 * await vagueDataFetcher.fetch(
 *   { entity: 'ENTITY_123', runObject: runData },
 *   (data) => setVagueData(data),
 *   (loading) => setLoading(loading)
 * );
 * 
 * // Access both summary and detailed analysis
 * const { vagueData, vagueDetailData } = data;
 * ```
 *
 * ## Data Quality Assurance
 * **Validation Mechanisms**:
 * - **Required Dependencies**: Validates presence of entity identifier and run object before processing
 * - **Data Integrity**: Ensures JSONB model data conforms to expected XferVagueModel interface structure
 * - **Temporal Consistency**: Maintains consistency between summary and detail data from same analysis run
 * - **Citation Validation**: Ensures all vague term citations reference valid source documents
 *
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://www.postgresql.org/docs/current/datatype-json.html PostgreSQL JSONB Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching Next.js Data Fetching Patterns
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/data/base-data-fetcher.ts BaseDataFetcher Abstract Class
 * @see /Users/<USER>/worktrees/279/apps/customer/types/vague.ts VagueType Interface Definitions
 * @see /Users/<USER>/worktrees/279/apps/customer/components/citation CitationType Interface
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Data fetcher for ESG vague language analysis, providing both summary and detailed ambiguous term identification from corporate sustainability communications
 * @example
 * ```typescript
 * // Initialize the vague data fetcher
 * const requestRefs = new Map<string, string>();
 * const abortControllers = new Map<string, AbortController>();
 * const vagueDataFetcher = new VagueDataFetcher(requestRefs, abortControllers);
 * 
 * // Set up state management
 * const [vagueData, setVagueData] = useState<VagueData | null>(null);
 * const [loading, setLoading] = useState(false);
 * 
 * // Fetch vague language analysis
 * const dependencies = {
 *   entity: 'ENTITY_XID_123',
 *   runObject: { id: 456, created_at: '2025-01-15' }
 * };
 * 
 * await vagueDataFetcher.fetch(dependencies, setVagueData, setLoading);
 * 
 * // Process results
 * if (vagueData) {
 *   const summary = vagueData.vagueData; // Aggregated vague language assessment
 *   const details = vagueData.vagueDetailData; // Individual vague terms array
 *   
 *   // Display summary analysis
 *   console.log('Vague Language Score:', summary?.model.score);
 *   console.log('Analysis:', summary?.model.analysis);
 *   
 *   // Process individual vague terms
 *   details?.forEach(term => {
 *     console.log('Vague Term:', term.phrase);
 *     console.log('Explanation:', term.model.explanation);
 *     console.log('Citations:', term.model.citations);
 *   });
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import { VagueType } from '@/types'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export interface VagueData {
    vagueData: VagueType | null;
    vagueDetailData: VagueType[] | null;
}

export class VagueDataFetcher extends BaseDataFetcher<VagueData> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('vague', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: VagueData | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData({ vagueData: null, vagueDetailData: null });
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            // Get vague terms summary from _deprecated_xfer_gw_vague_v2
            const summaryData = await this.fetchSummaryData(dependencies, abortController, isStale);
            if (isStale()) return;

            // Get vague terms details from _deprecated_xfer_gw_vague_v2
            const detailData = await this.fetchDetailData(dependencies, abortController, isStale);
            if (isStale()) return;

            if (!isStale()) {
                setData({
                    vagueData: summaryData,
                    vagueDetailData: detailData
                });
                this.logSuccess("Loaded vague data");
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchVagueData');
            if (!isStale()) {
                setData({ vagueData: null, vagueDetailData: [] });
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }

    private async fetchSummaryData(
        dependencies: DataFetcherDependencies,
        abortController: AbortController,
        isStale: () => boolean
    ): Promise<VagueType | null> {
        const { entity, runObject } = dependencies;

        const { data: vagueV2Data, error: vagueV2Error } = await this.supabase
          .from('_deprecated_xfer_gw_vague_v2')
            .select("*")
            .eq("entity_xid", entity!)
            .eq("phrase", "__summary__")
            .eq("run_id", runObject!.id)
            .order("id", { ascending: false })
            .limit(1)
            .abortSignal(abortController.signal);

        if (isStale()) return null;

        if (vagueV2Data && vagueV2Data.length > 0) {
            return vagueV2Data[0] as unknown as VagueType;
        } else {
            // Fallback to old table if no vague term found in v2
            return await this.fetchSummaryFromV1(dependencies, abortController, isStale);
        }
    }

    private async fetchSummaryFromV1(
        dependencies: DataFetcherDependencies,
        abortController: AbortController,
        isStale: () => boolean
    ): Promise<VagueType | null> {
        // V1 table is not available in customer database
        if (isStale()) return null;
        console.warn("No vague summary found - V1 table not available in customer database");
        return null;
    }

    private async fetchDetailData(
        dependencies: DataFetcherDependencies,
        abortController: AbortController,
        isStale: () => boolean
    ): Promise<VagueType[] | null> {
        const { entity, runObject } = dependencies;

        const { data: detailV2Data, error: detailV2Error } = await this.supabase
          .from('_deprecated_xfer_gw_vague_v2')
            .select("*")
            .eq("entity_xid", entity!)
            .eq("run_id", runObject!.id)
            .neq("phrase", "__summary__")
            .abortSignal(abortController.signal);

        if (isStale()) return null;

        if (detailV2Data && detailV2Data.length > 0) {
            return detailV2Data as unknown as VagueType[];
        } else {
            // Fallback to old table if no vague terms found in v2
            return await this.fetchDetailFromV1(dependencies, abortController, isStale);
        }
    }

    private async fetchDetailFromV1(
        dependencies: DataFetcherDependencies,
        abortController: AbortController,
        isStale: () => boolean
    ): Promise<VagueType[] | null> {
        // V1 table is not available in customer database
        if (isStale()) return null;
        console.warn("No vague details found - V1 table not available in customer database");
        return [];
    }
}
