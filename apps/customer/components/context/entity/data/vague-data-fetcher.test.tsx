import { describe, test, expect, vi, beforeEach } from 'vitest'
import { VagueDataFetcher, VagueData } from './vague-data-fetcher'
import { DataFetcherDependencies } from './base-data-fetcher'
import { VagueType, RunType } from '@/types'

// Mock the Supabase client factory
const mockSupabaseClient = {
  from: vi.fn()
}

vi.mock('@/app/supabase/client', () => ({
  createClient: () => mockSupabaseClient
}))

// Mock console methods to prevent test output noise
const consoleSpy = {
  log: vi.spyOn(console, 'log'),
  error: vi.spyOn(console, 'error'),
  warn: vi.spyOn(console, 'warn')
}

describe('VagueDataFetcher', () => {
  let vagueDataFetcher: VagueDataFetcher
  let mockFrom: ReturnType<typeof vi.fn>
  let mockSelect: ReturnType<typeof vi.fn>
  let mockEq: ReturnType<typeof vi.fn>
  let mockNeq: ReturnType<typeof vi.fn>
  let mockOrder: ReturnType<typeof vi.fn>
  let mockLimit: ReturnType<typeof vi.fn>
  let mockAbortSignal: ReturnType<typeof vi.fn>
  let mockSetData: ReturnType<typeof vi.fn>
  let mockSetLoading: ReturnType<typeof vi.fn>
  let currentRequestRefs: Map<string, string>
  let abortControllersRef: Map<string, AbortController>

  const mockVagueData: VagueType = {
    id: 1,
    entity_xid: 'test-entity',
    phrase: '__summary__',
    run_id: 123,
    model: {
      id: 1,
      entity_xid: 'test-entity',
      phrase: '__summary__',
      score: 0.9,
      explanation: 'Test explanation',
      analysis: 'Test analysis',
      summary: 'Test summary',
      citations: []
    }
  }

  const mockVagueDetailData: VagueType[] = [
    {
      id: 2,
      entity_xid: 'test-entity',
      phrase: 'detail1',
      run_id: 123,
      model: {
        id: 2,
        entity_xid: 'test-entity',
        phrase: 'detail1',
        score: 0.8,
        explanation: 'Test explanation 1',
        analysis: 'Test analysis 1',
        summary: 'Test summary 1',
        citations: []
      }
    },
    {
      id: 3,
      entity_xid: 'test-entity',
      phrase: 'detail2',
      run_id: 123,
      model: {
        id: 3,
        entity_xid: 'test-entity',
        phrase: 'detail2',
        score: 0.7,
        explanation: 'Test explanation 2',
        analysis: 'Test analysis 2',
        summary: 'Test summary 2',
        citations: []
      }
    }
  ]

  const mockRunObject: RunType = {
    id: 123,
    completed_at: '2023-01-01T00:00:00Z',
    run_type: 'test',
    scope: 'test'
  }

  const mockDependencies: DataFetcherDependencies = {
    entity: 'test-entity',
    runObject: mockRunObject
  }

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    
    // Reset console spies
    Object.values(consoleSpy).forEach(spy => spy.mockClear())
    
    // Setup mock chain
    mockAbortSignal = vi.fn().mockReturnThis()
    mockLimit = vi.fn().mockReturnThis()
    mockOrder = vi.fn().mockReturnThis()
    mockNeq = vi.fn().mockReturnThis()
    mockEq = vi.fn().mockReturnThis()
    mockSelect = vi.fn().mockReturnThis()
    mockFrom = vi.fn().mockReturnValue({
      select: mockSelect
    })
    
    // Set up the mock chain
    mockSelect.mockImplementation(() => ({
      eq: mockEq,
      neq: mockNeq
    }))
    mockEq.mockImplementation(() => ({
      eq: mockEq,
      neq: mockNeq,
      order: mockOrder,
      limit: mockLimit,
      abortSignal: mockAbortSignal
    }))
    mockNeq.mockImplementation(() => ({
      abortSignal: mockAbortSignal
    }))
    mockOrder.mockImplementation(() => ({
      limit: mockLimit
    }))
    mockLimit.mockImplementation(() => ({
      abortSignal: mockAbortSignal
    }))
    
    // Setup the Supabase client mock
    mockSupabaseClient.from = mockFrom
    
    // Create fresh refs for each test
    currentRequestRefs = new Map()
    abortControllersRef = new Map()
    
    // Create fresh mocks for setData and setLoading
    mockSetData = vi.fn()
    mockSetLoading = vi.fn()
    
    vagueDataFetcher = new VagueDataFetcher(currentRequestRefs, abortControllersRef)
  })

  describe('constructor', () => {
    test('should initialize with correct request type', () => {
      expect(vagueDataFetcher).toBeInstanceOf(VagueDataFetcher)
    })
  })

  describe('validateDependencies', () => {
    test('should return true for valid dependencies', () => {
      const result = vagueDataFetcher.validateDependencies(mockDependencies)
      expect(result).toBe(true)
    })

    test('should return false when entity is null', () => {
      const invalidDeps = { ...mockDependencies, entity: null }
      const result = vagueDataFetcher.validateDependencies(invalidDeps)
      expect(result).toBe(false)
    })

    test('should return false when runObject is null', () => {
      const invalidDeps = { ...mockDependencies, runObject: null }
      const result = vagueDataFetcher.validateDependencies(invalidDeps)
      expect(result).toBe(false)
    })

    test('should return false when runObject has no id', () => {
      const invalidDeps = { 
        ...mockDependencies, 
        runObject: { ...mockRunObject, id: undefined } as any
      }
      const result = vagueDataFetcher.validateDependencies(invalidDeps)
      expect(result).toBe(false)
    })
  })

  describe('fetch', () => {
    test('should fetch and set vague data successfully', async () => {
      // Mock successful summary data fetch
      mockAbortSignal.mockResolvedValueOnce({ 
        data: [mockVagueData], 
        error: null 
      })
      
      // Mock successful detail data fetch
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockVagueDetailData, 
        error: null 
      })

      await vagueDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).toHaveBeenCalledWith({
        vagueData: mockVagueData,
        vagueDetailData: mockVagueDetailData
      })
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should set null data for invalid dependencies', async () => {
      const invalidDeps = { ...mockDependencies, entity: null }

      await vagueDataFetcher.fetch(invalidDeps, mockSetData, mockSetLoading)

      expect(mockSetData).toHaveBeenCalledWith({
        vagueData: null,
        vagueDetailData: null
      })
      expect(mockSetLoading).not.toHaveBeenCalled()
    })

    test('should handle summary data fetch failure and fallback to V1', async () => {
      // Mock failed summary data fetch (no data found)
      mockAbortSignal.mockResolvedValueOnce({ 
        data: [], 
        error: null 
      })
      
      // Mock successful detail data fetch
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockVagueDetailData, 
        error: null 
      })

      await vagueDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetData).toHaveBeenCalledWith({
        vagueData: null,
        vagueDetailData: mockVagueDetailData
      })
    })

    test('should handle detail data fetch failure and fallback to V1', async () => {
      // Mock successful summary data fetch
      mockAbortSignal.mockResolvedValueOnce({ 
        data: [mockVagueData], 
        error: null 
      })
      
      // Mock failed detail data fetch (no data found)
      mockAbortSignal.mockResolvedValueOnce({ 
        data: [], 
        error: null 
      })

      await vagueDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetData).toHaveBeenCalledWith({
        vagueData: mockVagueData,
        vagueDetailData: []
      })
    })

    test('should handle database errors gracefully', async () => {
      const mockError = new Error('Database connection failed')
      mockAbortSignal.mockRejectedValueOnce(mockError)

      await vagueDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).toHaveBeenCalledWith({
        vagueData: null,
        vagueDetailData: []
      })
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should handle AbortError properly', async () => {
      const abortError = new Error('AbortError')
      abortError.name = 'AbortError'
      mockAbortSignal.mockRejectedValueOnce(abortError)

      await vagueDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).not.toHaveBeenCalled()
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should not set data or loading when request becomes stale during summary fetch', async () => {
      // Simulate stale request by changing the current request ID
      setTimeout(() => {
        currentRequestRefs.set('vague', 'different-request-id')
      }, 10)

      mockAbortSignal.mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({ data: [mockVagueData], error: null })
          }, 20)
        })
      })

      await vagueDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).not.toHaveBeenCalled()
      expect(mockSetLoading).not.toHaveBeenCalledWith(false)
    })

    test('should make correct Supabase queries for summary data', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: [mockVagueData], 
        error: null 
      })
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockVagueDetailData, 
        error: null 
      })

      await vagueDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockFrom).toHaveBeenCalledWith('_deprecated_xfer_gw_vague_v2')
      expect(mockSelect).toHaveBeenCalledWith('*')
      expect(mockEq).toHaveBeenCalledWith('entity_xid', 'test-entity')
      expect(mockEq).toHaveBeenCalledWith('phrase', '__summary__')
      expect(mockEq).toHaveBeenCalledWith('run_id', 123)
      expect(mockOrder).toHaveBeenCalledWith('id', { ascending: false })
      expect(mockLimit).toHaveBeenCalledWith(1)
    })

    test('should make correct Supabase queries for detail data', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: [mockVagueData], 
        error: null 
      })
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockVagueDetailData, 
        error: null 
      })

      await vagueDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockFrom).toHaveBeenCalledWith('_deprecated_xfer_gw_vague_v2')
      expect(mockNeq).toHaveBeenCalledWith('phrase', '__summary__')
    })
  })
})