import { describe, test, expect, vi, beforeEach } from 'vitest'
import { FlagsDataFetcher } from './flags-data-fetcher'
import { DataFetcherDependencies } from './base-data-fetcher'
import { FlagTypeV2 } from '@/types'
import { RunType } from '@/types'

// Mock the Supabase client factory
const mockSupabaseClient = {
  from: vi.fn()
}

vi.mock('@/app/supabase/client', () => ({
  createClient: () => mockSupabaseClient
}))

// Mock the processFlags utility
vi.mock('@/utils/flag-converter', () => ({
  processFlags: vi.fn((flags) => flags) // By default, pass through unchanged
}))

// Mock console methods to prevent test output noise
const consoleSpy = {
  log: vi.spyOn(console, 'log'),
  error: vi.spyOn(console, 'error'),
  warn: vi.spyOn(console, 'warn')
}

describe('FlagsDataFetcher', () => {
  let flagsDataFetcher: FlagsDataFetcher
  let mockFrom: ReturnType<typeof vi.fn>
  let mockSelect: ReturnType<typeof vi.fn>
  let mockEq: ReturnType<typeof vi.fn>
  let mockOrder: ReturnType<typeof vi.fn>
  let mockAbortSignal: ReturnType<typeof vi.fn>
  let mockSingle: ReturnType<typeof vi.fn>
  let mockSetData: ReturnType<typeof vi.fn>
  let mockSetLoading: ReturnType<typeof vi.fn>
  let currentRequestRefs: Map<string, string>
  let abortControllersRef: Map<string, AbortController>

  const mockFlagsV2Data: FlagTypeV2[] = [
    {
      id: 1,
      run_id: 123,
      entity_xid: 'test-entity',
      flag_type: 'red',
      flag_summary: 'Test red flag',
      flag_analysis: 'Red flag analysis',
      flag_statements: [{ statement_text: 'Statement 1', metadata: { statement_category: 'test' } }],
      trace_json: null,
      model: {
        id: 1,
        entity_xid: 'test-entity',
        flag_type: 'red',
        flag_title: 'Test Red Flag',
        flag_short_title: 'Red Flag',
        year: 2023,
        score: 0.8,
        impact: 0.7,
        confidence: 0.9,
        credibility: 0.8,
        flag_summary: 'Test red flag',
        flag_analysis: 'Red flag analysis',
        domains: ['test'],
        citations: [],
        flag_statements: [{ statement_text: 'Statement 1', metadata: { statement_category: 'test' } }],
        impact_value_analysis: {},
        model_sections: {},
        full_demise_centroid: {},
        is_disclosure_only: false
      }
    } as FlagTypeV2,
    {
      id: 2,
      run_id: 123,
      entity_xid: 'test-entity',
      flag_type: 'green',
      flag_summary: 'Test green flag (disclosure only)',
      flag_analysis: 'Green flag analysis',
      flag_statements: [{ statement_text: 'Statement 2', metadata: { statement_category: 'test' } }],
      trace_json: null,
      model: {
        id: 2,
        entity_xid: 'test-entity',
        flag_type: 'green',
        flag_title: 'Test Green Flag',
        flag_short_title: 'Green Flag',
        year: 2023,
        score: 0.6,
        impact: 0.5,
        confidence: 0.7,
        credibility: 0.6,
        flag_summary: 'Test green flag (disclosure only)',
        flag_analysis: 'Green flag analysis',
        domains: ['test'],
        citations: [],
        flag_statements: [{ statement_text: 'Statement 2', metadata: { statement_category: 'test' } }],
        impact_value_analysis: {},
        model_sections: {},
        full_demise_centroid: {},
        is_disclosure_only: true
      }
    } as FlagTypeV2,
    {
      id: 3,
      run_id: 123,
      entity_xid: 'test-entity',
      flag_type: 'green',
      flag_summary: 'Test green flag (not disclosure only)',
      flag_analysis: 'Green flag analysis',
      flag_statements: [{ statement_text: 'Statement 3', metadata: { statement_category: 'test' } }],
      trace_json: null,
      model: {
        id: 3,
        entity_xid: 'test-entity',
        flag_type: 'green',
        flag_title: 'Test Green Flag 2',
        flag_short_title: 'Green Flag 2',
        year: 2023,
        score: 0.7,
        impact: 0.6,
        confidence: 0.8,
        credibility: 0.7,
        flag_summary: 'Test green flag (not disclosure only)',
        flag_analysis: 'Green flag analysis',
        domains: ['test'],
        citations: [],
        flag_statements: [{ statement_text: 'Statement 3', metadata: { statement_category: 'test' } }],
        impact_value_analysis: {},
        model_sections: {},
        full_demise_centroid: {},
        is_disclosure_only: false
      }
    } as FlagTypeV2
  ]

  const mockRunObject: RunType = {
    id: 123,
    completed_at: '2023-01-01T00:00:00Z',
    run_type: 'test',
    scope: 'test'
  }

  const mockDependencies: DataFetcherDependencies = {
    entity: 'test-entity',
    runObject: mockRunObject
  }

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    
    // Reset console spies
    Object.values(consoleSpy).forEach(spy => spy.mockClear())
    
    // Setup mock chain
    mockAbortSignal = vi.fn().mockReturnThis()
    mockOrder = vi.fn().mockReturnThis()
    mockEq = vi.fn().mockReturnThis()
    mockSelect = vi.fn().mockReturnThis()
    mockSingle = vi.fn().mockReturnThis()
    mockFrom = vi.fn().mockReturnValue({
      select: mockSelect
    })
    
    // Set up the mock chain
    mockSelect.mockImplementation(() => ({
      eq: mockEq
    }))
    mockEq.mockImplementation(() => ({
      eq: mockEq,
      order: mockOrder,
      single: mockSingle,
      abortSignal: mockAbortSignal
    }))
    mockOrder.mockImplementation(() => ({
      abortSignal: mockAbortSignal
    }))
    mockSingle.mockImplementation(() => ({
      abortSignal: mockAbortSignal
    }))
    
    // Setup the Supabase client mock
    mockSupabaseClient.from = mockFrom
    
    // Create fresh refs for each test
    currentRequestRefs = new Map()
    abortControllersRef = new Map()
    
    // Create fresh mocks for setData and setLoading
    mockSetData = vi.fn()
    mockSetLoading = vi.fn()
    
    flagsDataFetcher = new FlagsDataFetcher(currentRequestRefs, abortControllersRef)
  })

  describe('constructor', () => {
    test('should initialize with correct request type', () => {
      expect(flagsDataFetcher).toBeInstanceOf(FlagsDataFetcher)
    })
  })

  describe('validateDependencies', () => {
    test('should return true for valid dependencies', () => {
      const result = flagsDataFetcher.validateDependencies(mockDependencies)
      expect(result).toBe(true)
    })

    test('should return false when entity is null', () => {
      const invalidDeps = { ...mockDependencies, entity: null }
      const result = flagsDataFetcher.validateDependencies(invalidDeps)
      expect(result).toBe(false)
    })

    test('should return false when runObject is null', () => {
      const invalidDeps = { ...mockDependencies, runObject: null }
      const result = flagsDataFetcher.validateDependencies(invalidDeps)
      expect(result).toBe(false)
    })

    test('should return false when runObject has no id', () => {
      const invalidDeps = { 
        ...mockDependencies, 
        runObject: { ...mockRunObject, id: undefined } as any
      }
      const result = flagsDataFetcher.validateDependencies(invalidDeps)
      expect(result).toBe(false)
    })
  })

  describe('fetch', () => {
    test('should fetch and set flags data successfully', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockFlagsV2Data, 
        error: null 
      })

      await flagsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      
      // By default, includeDisclosures is false, so disclosure-only green flags are filtered
      const expectedFlags = mockFlagsV2Data.filter(flag => 
        flag.model.flag_type === 'red' || !flag.model.is_disclosure_only
      )
      expect(mockSetData).toHaveBeenCalledWith(expectedFlags)
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should filter out disclosure-only green flags when includeDisclosures is false', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockFlagsV2Data, 
        error: null 
      })

      const dependenciesWithoutDisclosures = {
        ...mockDependencies,
        includeDisclosures: false
      }

      await flagsDataFetcher.fetch(dependenciesWithoutDisclosures, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      
      // Should filter out the disclosure-only green flag (id: 2)
      // Should keep red flag (id: 1) and non-disclosure green flag (id: 3)
      const filteredFlags = mockFlagsV2Data.filter(flag => 
        flag.model.flag_type === 'red' || !flag.model.is_disclosure_only
      )
      expect(mockSetData).toHaveBeenCalledWith(filteredFlags)
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should include all flags when includeDisclosures is true', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockFlagsV2Data, 
        error: null 
      })

      const dependenciesWithDisclosures = {
        ...mockDependencies,
        includeDisclosures: true
      }

      await flagsDataFetcher.fetch(dependenciesWithDisclosures, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).toHaveBeenCalledWith(mockFlagsV2Data)
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should set null data for invalid dependencies', async () => {
      const invalidDeps = { ...mockDependencies, entity: null }

      await flagsDataFetcher.fetch(invalidDeps, mockSetData, mockSetLoading)

      expect(mockSetData).toHaveBeenCalledWith(null)
      expect(mockSetLoading).not.toHaveBeenCalled()
    })

    test('should handle empty flags data', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: [], 
        error: null 
      })

      await flagsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).toHaveBeenCalledWith([])
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should handle database errors gracefully', async () => {
      const mockError = new Error('Database connection failed')
      mockAbortSignal.mockResolvedValueOnce({ 
        data: null, 
        error: mockError 
      })

      await flagsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).toHaveBeenCalledWith([])
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should handle AbortError properly', async () => {
      const abortError = new Error('AbortError')
      abortError.name = 'AbortError'
      mockAbortSignal.mockRejectedValueOnce(abortError)

      await flagsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).not.toHaveBeenCalled()
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should not set data or loading when request becomes stale', async () => {
      // Simulate stale request by changing the current request ID
      setTimeout(() => {
        currentRequestRefs.set('flags', 'different-request-id')
      }, 10)

      mockAbortSignal.mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({ data: mockFlagsV2Data, error: null })
          }, 20)
        })
      })

      await flagsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).not.toHaveBeenCalled()
      expect(mockSetLoading).not.toHaveBeenCalledWith(false)
    })

    test('should make correct Supabase queries', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockFlagsV2Data, 
        error: null 
      })

      await flagsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockFrom).toHaveBeenCalledWith('xfer_flags')
      expect(mockSelect).toHaveBeenCalledWith(`
                 id,
    run_id,
    entity_xid,
    flag_type,
    flag_summary,
    flag_analysis,
    flag_statements,
    model
                `)
      expect(mockEq).toHaveBeenCalledWith('run_id', 123)
      expect(mockEq).toHaveBeenCalledWith('entity_xid', 'test-entity')
      expect(mockOrder).toHaveBeenCalledWith('id', { ascending: false })
    })

    test('should handle string model data by parsing it', async () => {
      const flagsWithStringModel = [
        {
          ...mockFlagsV2Data[0],
          model: JSON.stringify(mockFlagsV2Data[0].model)
        }
      ]

      mockAbortSignal.mockResolvedValueOnce({ 
        data: flagsWithStringModel, 
        error: null 
      })

      const dependenciesWithoutDisclosures = {
        ...mockDependencies,
        includeDisclosures: false
      }

      await flagsDataFetcher.fetch(dependenciesWithoutDisclosures, mockSetData, mockSetLoading)

      expect(mockSetData).toHaveBeenCalled()
      // The actual filtering logic would be tested by the real processFlags function
    })
  })

  describe('fetchDetailed', () => {
    test('should fetch detailed flags data successfully', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockFlagsV2Data, 
        error: null 
      })

      await flagsDataFetcher.fetchDetailed(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      
      // By default, includeDisclosures is false, so disclosure-only green flags are filtered
      const expectedFlags = mockFlagsV2Data.filter(flag => 
        flag.model.flag_type === 'red' || !flag.model.is_disclosure_only
      )
      expect(mockSetData).toHaveBeenCalledWith(expectedFlags)
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should filter out disclosure-only green flags in detailed fetch when includeDisclosures is false', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockFlagsV2Data, 
        error: null 
      })

      const dependenciesWithoutDisclosures = {
        ...mockDependencies,
        includeDisclosures: false
      }

      await flagsDataFetcher.fetchDetailed(dependenciesWithoutDisclosures, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      
      // Should filter out the disclosure-only green flag (id: 2)
      const filteredFlags = mockFlagsV2Data.filter(flag => 
        flag.model.flag_type === 'red' || !flag.model.is_disclosure_only
      )
      expect(mockSetData).toHaveBeenCalledWith(filteredFlags)
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should set null data for invalid dependencies in detailed fetch', async () => {
      const invalidDeps = { ...mockDependencies, entity: null }

      await flagsDataFetcher.fetchDetailed(invalidDeps, mockSetData, mockSetLoading)

      expect(mockSetData).toHaveBeenCalledWith(null)
      expect(mockSetLoading).not.toHaveBeenCalled()
    })

    test('should make correct Supabase query for detailed fetch', async () => {
      mockAbortSignal.mockResolvedValueOnce({ 
        data: mockFlagsV2Data, 
        error: null 
      })

      await flagsDataFetcher.fetchDetailed(mockDependencies, mockSetData, mockSetLoading)

      expect(mockFrom).toHaveBeenCalledWith('xfer_flags')
      expect(mockSelect).toHaveBeenCalledWith(`
                    id, 
                    run_id, 
                    entity_xid, 
                    flag_type, 
                    flag_summary, 
                    flag_analysis,
                    flag_statements,
                    model
                `)
      expect(mockEq).toHaveBeenCalledWith('run_id', 123)
      expect(mockEq).toHaveBeenCalledWith('entity_xid', 'test-entity')
      expect(mockOrder).toHaveBeenCalledWith('id', { ascending: false })
    })
  })

  describe('fetchTrace', () => {
    test('should fetch trace data successfully', async () => {
      const mockTraceData = { trace_json: { debug: 'trace info' } }
      
      // Reset the mock chain specifically for single() method
      const mockSingleResult = vi.fn().mockResolvedValueOnce({ 
        data: mockTraceData, 
        error: null 
      })
      
      mockEq.mockImplementationOnce(() => ({
        single: mockSingleResult
      }))

      const result = await flagsDataFetcher.fetchTrace(1)

      expect(mockFrom).toHaveBeenCalledWith('xfer_flags')
      expect(mockSelect).toHaveBeenCalledWith('trace_json')
      expect(mockEq).toHaveBeenCalledWith('id', 1)
      expect(mockSingleResult).toHaveBeenCalled()
      expect(result).toEqual({ debug: 'trace info' })
    })

    test('should handle trace fetch errors gracefully', async () => {
      const mockError = new Error('Database error')
      
      const mockSingleResult = vi.fn().mockResolvedValueOnce({ 
        data: null, 
        error: mockError 
      })
      
      mockEq.mockImplementationOnce(() => ({
        single: mockSingleResult
      }))

      const result = await flagsDataFetcher.fetchTrace(1)

      expect(result).toBe(null)
    })

    test('should return undefined when trace data is missing', async () => {
      const mockSingleResult = vi.fn().mockResolvedValueOnce({ 
        data: null, 
        error: null 
      })
      
      mockEq.mockImplementationOnce(() => ({
        single: mockSingleResult
      }))

      const result = await flagsDataFetcher.fetchTrace(1)

      expect(result).toBe(undefined)
    })

    test('should return undefined when data exists but trace_json is missing', async () => {
      const mockSingleResult = vi.fn().mockResolvedValueOnce({ 
        data: {}, // Empty object, no trace_json field
        error: null 
      })
      
      mockEq.mockImplementationOnce(() => ({
        single: mockSingleResult
      }))

      const result = await flagsDataFetcher.fetchTrace(1)

      expect(result).toBe(undefined)
    })
  })
})