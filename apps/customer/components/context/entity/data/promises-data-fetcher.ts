/**
 * Promises Data Fetcher for ESG Corporate Promise Tracking and Analysis
 *
 * This specialized data fetcher implements efficient retrieval and processing of corporate ESG promises
 * from the EkoIntelligence analytics platform. It extends the BaseDataFetcher abstract class to provide
 * optimized access to promise tracking data, analyzing corporate commitments against actual performance
 * while maintaining high performance through intelligent filtering and request management.
 *
 * ## Core Functionality
 * - **Promise Tracking**: Fetches corporate ESG promises with fulfillment analysis and confidence scoring
 * - **Commitment Analysis**: Evaluates promises against evidence of actual performance and delivery
 * - **Confidence Filtering**: Returns only high-confidence promises (>75%) for reliable analysis
 * - **Performance Optimization**: Uses direct column access for faster queries on frequently-used fields
 * - **Legacy Support**: Provides fallback to V1 promise format when V2 data is unavailable
 * - **Request Cancellation**: Implements intelligent request cancellation to prevent stale data
 *
 * ## Data Source & Structure
 * **Primary Table**: `xfer_promises` (V2 format)
 * - **id**: Unique promise identifier (composite key with run_id)
 * - **entity_xid**: ESG entity identifier for promise attribution
 * - **run_id**: Analysis run identifier for data versioning
 * - **statement_id**: Link to source statement that generated the promise
 * - **kept**: Boolean flag indicating whether the promise was fulfilled
 * - **summary**: Direct column extraction of promise summary for performance
 * - **conclusion**: Direct column extraction of promise conclusion for performance
 * - **statement_text**: Direct column extraction of original statement text for performance
 * - **model**: JSONB field containing complete XferPromiseModel data with evidence and citations
 *
 * ## Promise Data Model
 * **XferPromiseModel Structure**:
 * - **confidence**: Numeric confidence score (0-100) in promise analysis accuracy
 * - **greenwashing**: Boolean flag identifying potential greenwashing in promises
 * - **verdict**: AI-generated assessment of promise fulfillment
 * - **evidence**: Dictionary containing supporting evidence for promise evaluation
 * - **citations**: Array of supporting document citations with internal reference IDs
 * - **promise_doc**: Document reference containing the original promise
 * - **promise_doc_year**: Year of the document containing the promise
 * - **company**: Corporate entity name for display purposes
 * - **esg_promise**: Boolean flag identifying ESG-specific promises
 *
 * ## Query Optimization Strategy
 * **Performance Optimizations**:
 * 1. **Essential Fields Selection**: Queries only required fields to minimize data transfer
 * 2. **Direct Column Access**: Uses extracted columns (summary, conclusion, statement_text) for faster filtering
 * 3. **Confidence Filtering**: Server-side filtering for promises with >75% confidence reduces processing overhead
 * 4. **Confidence Ordering**: Results sorted by confidence score (descending) for relevance ranking
 * 5. **Run-Specific Queries**: Efficient filtering by run_id and entity_xid for targeted data retrieval
 *
 * ## Promise Analysis Context
 * **Corporate Promise Tracking**:
 * The promise analysis system evaluates corporate commitments by:
 * - Analyzing stated commitments and targets in corporate communications
 * - Tracking evidence of actual performance against stated promises
 * - Identifying gaps between promises and delivery
 * - Detecting potential greenwashing through unfulfilled or misleading commitments
 * - Providing confidence scores based on evidence quality and promise specificity
 * - Enabling stakeholders to assess corporate credibility and track accountability
 *
 * ## Request Lifecycle Management
 * **Concurrent Request Handling**:
 * 1. **Dependency Validation**: Ensures entity and run_id are available before processing
 * 2. **Request Setup**: Creates unique request ID and abort controller for cancellation
 * 3. **Data Retrieval**: Executes optimized Supabase query with confidence filtering
 * 4. **Staleness Check**: Verifies request is still current before processing results
 * 5. **Data Processing**: Applies client-side confidence filtering and sorting
 * 6. **State Management**: Updates loading states and data only for current requests
 *
 * ## Database Integration
 * **Supabase Client Features**:
 * - **Row-Level Security**: Automatic application of RLS policies for data access control
 * - **Type Safety**: Strongly-typed database operations with generated TypeScript interfaces
 * - **Real-time Capabilities**: Support for live data updates via Supabase subscriptions
 * - **AbortSignal Support**: Request cancellation for improved performance and user experience
 * - **Optimized Queries**: Efficient column selection and server-side filtering
 *
 * ## System Architecture Context
 * **ESG Analysis Pipeline Integration**:
 * - **Analytics Backend**: Python system generates promise analysis data in analytics database
 * - **Data Synchronization**: `xfer_promises` table syncs processed data between analytics and customer databases
 * - **API Layer**: This fetcher provides customer-facing access to synchronized ESG promise data
 * - **Frontend Components**: React components consume promise data for dashboard and report display
 * - **Real-time Updates**: Supabase enables live updates when new promise analyses are available
 *
 * ## Security & Performance
 * - **Access Control**: Database queries automatically apply Row-Level Security policies
 * - **Data Isolation**: Users can only access promises for entities they have permissions for
 * - **Request Optimization**: Intelligent request deduplication and cancellation prevent resource waste
 * - **Error Handling**: Graceful handling of network errors, timeouts, and cancelled requests
 * - **Fallback Mechanisms**: V1 table fallback ensures system resilience during data transitions
 *
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching Next.js Data Fetching
 * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortController AbortController API
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/data/base-data-fetcher.ts Base Data Fetcher Abstract Class
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/data/claims-data-fetcher.ts Claims Data Fetcher Implementation
 * @see /Users/<USER>/worktrees/279/apps/customer/types/promise.ts Promise Type Definitions
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Specialized data fetcher for retrieving and managing ESG corporate promise tracking data with confidence filtering and performance optimization
 * @example
 * ```typescript
 * // Initialize promises data fetcher
 * const requestRefs = new Map<string, string>();
 * const abortControllers = new Map<string, AbortController>();
 * const promisesFetcher = new PromisesDataFetcher(requestRefs, abortControllers);
 * 
 * // Set up state management
 * const [promisesData, setPromisesData] = useState<PromiseTypeV2[] | null>(null);
 * const [loading, setLoading] = useState<boolean>(false);
 * 
 * // Fetch promises data
 * const dependencies = {
 *   entity: 'AAPL',
 *   runObject: { id: 123, run_type: 'entity_analysis' },
 *   model: 'ekoIntelligence'
 * };
 * 
 * await promisesFetcher.fetch(dependencies, setPromisesData, setLoading);
 * 
 * // Results will be filtered for confidence >75% and sorted by confidence
 * // promisesData will contain array of PromiseTypeV2 objects with high-confidence promise analyses
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import { PromiseTypeV2 } from '@/types'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export class PromisesDataFetcher extends BaseDataFetcher<PromiseTypeV2[]> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('promises', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: PromiseTypeV2[] | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData(null);
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            // Try to get promises from xfer_promises with optimized column selection
            const { data: promisesV2Data, error: promisesV2Error } = await this.supabase
              .from('xfer_promises')
              .select(`
                    id, 
                    run_id, 
                    statement_id, 
                    entity_xid, 
                    kept, 
                    summary,
                    conclusion,
                    statement_text,
                    model
                `)
                .eq('run_id', runObject!.id)
                .eq('entity_xid', entity!)
              .order('id', { ascending: false })
              .abortSignal(abortController.signal);

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (promisesV2Error) {
                this.logError(promisesV2Error, 'fetchAndFilterPromises - v2');
                if (!isStale()) {
                    setData([]);
                }
                return;
            }

            if (promisesV2Data && promisesV2Data.length > 0) {
                // Use V2 promises directly without conversion
                const filteredPromises = promisesV2Data
                    .filter(promise => {
                        // Safely access confidence with type checking
                        const model = promise.model as any;
                        return (model && typeof model === 'object' && 'confidence' in model ? model.confidence : 0) > 75;
                    })
                    .map(promise => promise as unknown as PromiseTypeV2);

                // Sort by confidence
                filteredPromises.sort((a, b) => {
                    // Safely access confidence with type checking
                    const modelA = a.model as any;
                    const modelB = b.model as any;
                    const confA = modelA && typeof modelA === 'object' && 'confidence' in modelA ? modelA.confidence : 0;
                    const confB = modelB && typeof modelB === 'object' && 'confidence' in modelB ? modelB.confidence : 0;
                    return confB - confA;
                });

                // Final check before setting data
                if (!isStale()) {
                    setData(filteredPromises);
                    this.logSuccess("Loaded promises", filteredPromises.length);
                }
            } else {
                // Fallback to old table if no promises found in v2
                await this.fetchFromV1Table(dependencies, setData, isStale, abortController);
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchAndFilterPromises');
            if (!isStale()) {
                setData([]);
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }

    private async fetchFromV1Table(
        dependencies: DataFetcherDependencies,
        setData: (data: PromiseTypeV2[] | null) => void,
        isStale: () => boolean,
        abortController: AbortController
    ): Promise<void> {
        // V1 table is not available in customer database, set empty data
        if (!isStale()) {
            console.warn("No promises found - V1 table not available in customer database");
            setData([]);
        }
    }
}
