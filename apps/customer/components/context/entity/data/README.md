# Entity Data Fetchers Module

## Overview

The Entity Data Fetchers module serves as the centralized data access layer for ESG (Environmental, Social, Governance) entity analysis within the EkoIntelligence customer application. This module provides a comprehensive suite of specialized data fetchers that retrieve, process, and manage various types of corporate sustainability analysis data from the synchronized customer database. Each fetcher implements a consistent, type-safe interface while specializing in specific aspects of ESG analysis including claims verification, promise tracking, impact flags, and risk assessment.

## Specification

### Architecture Requirements

The module must provide:

1. **Unified Data Access**: Single import location for all ESG data fetching capabilities
2. **Type Safety**: Full TypeScript integration with generated database types
3. **Request Management**: Intelligent request cancellation and deduplication
4. **Performance Optimization**: Efficient database queries with selective field loading
5. **Error Handling**: Consistent error management with graceful degradation
6. **Security**: Row Level Security (RLS) compliance through Supabase client integration

### Data Fetcher Interface

All data fetchers must implement:

```typescript
interface DataFetcher<T> {
  validateDependencies(deps: DataFetcherDependencies): boolean;
  fetch(
    dependencies: DataFetcherDependencies,
    setData: (data: T | null) => void,
    setLoading: (loading: boolean) => void
  ): Promise<void>;
}
```

### Data Source Requirements

- **Primary Database**: Customer database accessed via Supabase client
- **Table Prefix**: All data sourced from `xfer_*` tables synchronized from analytics database
- **Security**: User authentication required with RLS policies applied
- **Performance**: Optimized queries with selective field loading and database-level filtering

## Key Components

### Core Infrastructure

- **`index.ts`** - Central export module providing unified access to all data fetchers and shared types
- **`base-data-fetcher.ts`** - Abstract base class implementing the Template Method pattern for consistent request management, type safety, and error handling across all specialized fetchers

### Specialized Data Fetchers

#### ESG Analysis Data Types
- **`flags-data-fetcher.ts`** - Retrieves ESG effect flags representing identified impacts, risks, and opportunities with intelligent disclosure filtering and performance optimization
- **`claims-data-fetcher.ts`** - Fetches corporate ESG claims with verification status, confidence scoring, and greenwashing detection capabilities
- **`promises-data-fetcher.ts`** - Manages future-oriented commitments with tracking, fulfillment analysis, and delivery assessment
- **`cherry-data-fetcher.ts`** - Handles cherry-picked statements and selective disclosure analysis for bias detection
- **`model-sections-data-fetcher.ts`** - Provides DEMISE model sections for comprehensive ESG framework analysis and scoring
- **`score-data-fetcher.ts`** - Retrieves entity scoring data including overall ESG performance metrics and domain-specific ratings
- **`vague-data-fetcher.ts`** - Analyzes vague language and ambiguous statements in corporate communications for transparency assessment

#### Test Coverage
Each data fetcher includes comprehensive unit tests (`.test.tsx`) covering:
- Dependency validation logic
- Request cancellation scenarios
- Error handling and recovery
- Data transformation and normalization
- Type safety and interface compliance

## Dependencies

### External Libraries

- **`@supabase/supabase-js`** - Database client for secure, authenticated access to customer database with automatic RLS policy application
- **`React`** - Hook-based state management integration for component lifecycle management
- **`Next.js`** - Server-side rendering and API route integration for optimized data loading patterns

### Internal Dependencies

- **Database Types** - Generated TypeScript interfaces from Supabase schema providing compile-time type safety
- **Entity Context** - Parent context providing entity selection and run management capabilities
- **Feature Flags** - Configuration system for conditional data loading and experimental feature enablement
- **Utility Functions** - Shared data processing and normalization functions for consistent data transformation

### Database Schema

The module depends on synchronized `xfer_*` tables in the customer database:

- `xfer_flags` - ESG effect flags with impact analysis and severity ratings
- `xfer_claims` - Corporate claims with verification outcomes and confidence scores  
- `xfer_promises` - Future commitments with tracking status and fulfillment metrics
- `xfer_entities` - Entity metadata including names, identifiers, and classification
- `xfer_model_sections` - DEMISE model analysis sections for comprehensive ESG evaluation
- `xfer_score` - Entity scoring data with domain-specific ESG performance metrics

## Usage Examples

### Basic Component Integration

```typescript
import { 
  FlagsDataFetcher, 
  ClaimsDataFetcher,
  type DataFetcherDependencies 
} from '@/components/context/entity/data';

function ESGDashboard({ entity, runObject }: Props) {
  const [flagsData, setFlagsData] = useState(null);
  const [claimsData, setClaimsData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const requestRefs = useRef(new Map());
  const abortControllers = useRef(new Map());
  
  const dependencies: DataFetcherDependencies = {
    entity,
    runObject,
    model: 'ekoIntelligence',
    includeDisclosures: false
  };
  
  useEffect(() => {
    const flagsFetcher = new FlagsDataFetcher(requestRefs.current, abortControllers.current);
    const claimsFetcher = new ClaimsDataFetcher(requestRefs.current, abortControllers.current);
    
    if (flagsFetcher.validateDependencies(dependencies)) {
      flagsFetcher.fetch(dependencies, setFlagsData, setLoading);
    }
    
    if (claimsFetcher.validateDependencies(dependencies)) {
      claimsFetcher.fetch(dependencies, setClaimsData, setLoading);
    }
  }, [entity, runObject]);
  
  return (
    <div>
      {loading && <LoadingSpinner />}
      {flagsData && <FlagsDisplay data={flagsData} />}
      {claimsData && <ClaimsDisplay data={claimsData} />}
    </div>
  );
}
```

### Custom Hook Integration

```typescript
import { useEffect, useState, useRef } from 'react';
import { ScoreDataFetcher, type ScoreData } from '@/components/context/entity/data';

function useEntityScore(entity: string, runObject: RunType) {
  const [scoreData, setScoreData] = useState<ScoreData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const requestRefs = useRef(new Map());
  const abortControllers = useRef(new Map());
  
  useEffect(() => {
    const scoreFetcher = new ScoreDataFetcher(requestRefs.current, abortControllers.current);
    
    const dependencies = {
      entity,
      runObject,
      model: 'ekoIntelligence',
      includeDisclosures: false
    };
    
    if (scoreFetcher.validateDependencies(dependencies)) {
      scoreFetcher.fetch(dependencies, setScoreData, setLoading)
        .catch(err => setError(err.message));
    }
  }, [entity, runObject]);
  
  return { scoreData, loading, error };
}
```

## Architecture Notes

### Request Lifecycle Management

```mermaid
sequenceDiagram
    participant Component
    participant DataFetcher
    participant Supabase
    participant Database
    
    Component->>DataFetcher: fetch(dependencies, setData, setLoading)
    DataFetcher->>DataFetcher: validateDependencies()
    DataFetcher->>DataFetcher: generateRequestId()
    DataFetcher->>DataFetcher: cancelStaleRequests()
    DataFetcher->>Component: setLoading(true)
    DataFetcher->>Supabase: query with RLS
    Supabase->>Database: SELECT with filters
    Database-->>Supabase: rows
    Supabase-->>DataFetcher: typed results
    DataFetcher->>DataFetcher: processData()
    DataFetcher->>Component: setData(processedData)
    DataFetcher->>Component: setLoading(false)
```

### Data Flow Architecture

```mermaid
flowchart TD
    A[Analytics Database] -->|Data Sync| B[xfer_* Tables]
    B --> C[Supabase Client]
    C --> D[Data Fetchers]
    D --> E[React Components]
    
    F[Base Data Fetcher] --> G[Flags Fetcher]
    F --> H[Claims Fetcher]
    F --> I[Promises Fetcher]
    F --> J[Score Fetcher]
    F --> K[Other Fetchers]
    
    L[Request Management] --> M[Cancellation]
    L --> N[Deduplication]
    L --> O[Error Handling]
    
    P[Type Safety] --> Q[Database Types]
    P --> R[Interface Validation]
    P --> S[Compile-time Checks]
```

### Class Hierarchy

```mermaid
classDiagram
    class BaseDataFetcher {
        <<abstract>>
        +validateDependencies(deps)
        +generateRequestId(deps)
        +cancelStaleRequests(type)
        +isRequestStale(requestId)
        #abstract fetch(deps, setData, setLoading)
    }
    
    class FlagsDataFetcher {
        +fetch(deps, setData, setLoading)
        +validateDependencies(deps)
        -processFlags(data)
    }
    
    class ClaimsDataFetcher {
        +fetch(deps, setData, setLoading)
        +validateDependencies(deps)
        -processClaims(data)
    }
    
    class PromisesDataFetcher {
        +fetch(deps, setData, setLoading)
        +validateDependencies(deps)
        -processPromises(data)
    }
    
    BaseDataFetcher <|-- FlagsDataFetcher
    BaseDataFetcher <|-- ClaimsDataFetcher
    BaseDataFetcher <|-- PromisesDataFetcher
```

## Known Issues

### Data Synchronization
- **EKO-264**: Review Analysis Data - Overall task for analysis rendering and content fixes affecting data display consistency
- **EKO-261**: Cherry Picking Analysis displays "No Summary Available" for some entities, indicating potential data sync issues

### User Interface Integration
- **EKO-298**: Broken promises expandable sections show "+8 more pieces of evidence" but no expansion functionality
- **EKO-290**: Green Flags links not properly navigating to detail views when clicked
- **EKO-303**: False Claims section has inconsistent font rendering and potentially irrelevant content

### Performance Considerations
- Request cancellation logic may need optimization for rapid entity switching scenarios
- Database query performance could be improved with additional indexing on frequently accessed fields
- Memory usage monitoring needed for large result sets in entity scoring data

## Future Work

### Planned Enhancements (Based on Linear Tickets)

1. **EKO-210**: Implement "Not Applicable" status for sections with no relevant impact data
   - Add logic to detect empty result sets and display appropriate messaging
   - Enhance report template to handle missing data scenarios gracefully

2. **Enhanced Request Management**: 
   - Implement request prioritization for critical data types
   - Add progressive loading capabilities for large datasets
   - Develop intelligent prefetching based on user navigation patterns

3. **Real-time Data Updates**:
   - Integrate Supabase real-time subscriptions for live data updates
   - Implement optimistic updates for improved user experience
   - Add conflict resolution for concurrent data modifications

### Technical Debt

1. **Legacy Format Support**: Remove V1 fallback logic once all entities migrated to V2 format
2. **Type Safety**: Strengthen runtime type validation for database responses
3. **Error Recovery**: Implement more sophisticated retry mechanisms with exponential backoff
4. **Caching Layer**: Add client-side caching to reduce database load for frequently accessed data

### Performance Optimizations

1. **Query Optimization**: Implement database-level pagination for large result sets
2. **Bundle Splitting**: Consider lazy loading of specialized fetchers to reduce initial bundle size
3. **Memory Management**: Implement data cleanup strategies for long-running sessions

## Troubleshooting

### Common Issues

**Q: Data fetcher returns null despite valid dependencies**
A: Check browser network tab for failed requests. Verify user authentication status and RLS policies. Ensure entity exists in database with valid run data.

**Q: Request cancellation not working properly**
A: Verify AbortController cleanup in component unmount. Check that request IDs are unique and properly managed in concurrent scenarios.

**Q: Type errors with database responses**
A: Regenerate database types using `supabase gen types typescript`. Ensure schema changes are reflected in type definitions.

**Q: Performance issues with large datasets**
A: Enable database query logging to identify slow queries. Consider implementing pagination or result limiting. Check for missing database indexes.

### Debugging Procedures

1. **Enable Request Logging**: Add console logging to track request lifecycle
2. **Database Query Analysis**: Use Supabase dashboard to monitor query performance
3. **Network Inspection**: Use browser dev tools to examine request/response patterns
4. **Type Validation**: Enable strict TypeScript checking to catch type mismatches

### Error Recovery

```typescript
// Example error handling pattern
try {
  await fetcher.fetch(dependencies, setData, setLoading);
} catch (error) {
  if (error.name === 'AbortError') {
    // Request was cancelled, safe to ignore
    return;
  }
  
  console.error('Data fetch failed:', error);
  setError('Failed to load data. Please try again.');
  setLoading(false);
}
```

## FAQ

### User-Centric Questions

**Q: Why does my dashboard sometimes show "No data available" for entities that should have data?**
A: This typically occurs when the entity hasn't been analyzed in the current run, or when data synchronization from the analytics database is in progress. Try refreshing the page or selecting a different analysis run.

**Q: How can I tell if data is still loading versus actually empty?**
A: Each data fetcher manages loading states independently. Look for loading indicators or spinners. If loading is false and data is null, the entity likely has no data for that analysis type.

**Q: What does "includeDisclosures" mean and when should I use it?**
A: Disclosures are routine regulatory or informational statements without significant ESG impact. Set `includeDisclosures: true` for comprehensive analysis, or `false` for focused impact analysis.

**Q: Why do some entities have more data types available than others?**
A: Data availability depends on the source documents analyzed and the entity's public disclosure practices. Newer entities or those with limited public information may have fewer data points.

**Q: How recent is the data I'm viewing?**
A: Data freshness depends on the selected analysis run. Check the run metadata for the last update timestamp. Data is typically refreshed when new documents are analyzed or periodically for comprehensive updates.

## References

### Documentation Links
- [Supabase TypeScript Client Documentation](https://supabase.com/docs/reference/javascript/typescript-support)
- [Next.js Data Fetching Patterns](https://nextjs.org/docs/app/building-your-application/data-fetching)
- [React Hooks API Reference](https://react.dev/reference/react/hooks)

### Related Code Files
- [`./base-data-fetcher.ts`](./base-data-fetcher.ts) - Abstract base class for all data fetchers
- [`./flags-data-fetcher.ts`](./flags-data-fetcher.ts) - ESG effect flags data fetcher implementation
- [`./claims-data-fetcher.ts`](./claims-data-fetcher.ts) - Corporate claims verification data fetcher
- [`./promises-data-fetcher.ts`](./promises-data-fetcher.ts) - Corporate promises tracking data fetcher
- [`../entity-context.tsx`](../entity-context.tsx) - Parent context providing entity and run management

### External Resources
- [Supabase Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [TypeScript Utility Types](https://www.typescriptlang.org/docs/handbook/utility-types.html)
- [React AbortController Integration](https://developer.mozilla.org/en-US/docs/Web/API/AbortController)

### Database Schema Documentation
- Customer Database Schema: [`@tmp/db/customer_schema.sql`](../../../../tmp/db/customer_schema.sql)
- Transfer Tables Documentation: See analytics database sync process documentation

---

## Changelog

### 2025-07-30
- Initial comprehensive README.md creation
- Documented all data fetchers and their specialized functionality
- Added architecture diagrams and usage examples
- Included troubleshooting guide and FAQ section
- Integrated Linear issue tracking for known issues and future work

---

**(c) All rights reserved ekoIntelligence 2025**