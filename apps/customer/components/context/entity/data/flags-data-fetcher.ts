/**
 * ESG Effect Flags Data Fetcher for Corporate Impact Analysis
 *
 * This specialized data fetcher implements optimized retrieval and processing of ESG (Environmental, Social,
 * Governance) effect flags from the EkoIntelligence analytics platform. Effect flags represent AI-generated
 * analysis results that identify significant ESG impacts, risks, and opportunities within corporate documents
 * and statements. The fetcher extends BaseDataFetcher to provide intelligent flag filtering, disclosure
 * management, and performance-optimized data access.
 *
 * ## Core Functionality
 * - **Effect Flag Retrieval**: Fetches ESG analysis flags with intelligent filtering and disclosure management
 * - **Performance Optimization**: Provides separate detailed and lightweight fetch methods for different UI contexts
 * - **Disclosure Filtering**: Smart filtering of disclosure-only flags while preserving critical red flags
 * - **Trace Data Access**: Administrative debugging capabilities for analysis pipeline troubleshooting
 * - **Flag Processing**: Automatic JSON parsing and data normalization using utility functions
 * - **Request Cancellation**: Intelligent request lifecycle management to prevent stale data issues
 *
 * ## ESG Effect Flags Overview
 * **Effect Flags** are the result of AI-powered analysis of corporate ESG statements and documents.
 * They represent identified impacts, risks, or opportunities that could affect stakeholders, the environment,
 * or governance practices. Each flag contains structured analysis including impact assessments,
 * supporting evidence, and model-generated classifications.
 *
 * **Flag Types**:
 * - **Red Flags**: Critical issues, risks, or negative impacts requiring attention
 * - **Green Flags**: Positive impacts, opportunities, or beneficial practices
 * - **Disclosure Flags**: Routine disclosures without significant impact implications
 *
 * ## Data Source & Database Schema
 * **Primary Table**: `xfer_flags` (Customer Database)
 * 
 * **Table Structure**:
 * ```sql
 * CREATE TABLE xfer_flags (
 *   id integer NOT NULL,                    -- Unique flag identifier
 *   run_id integer NOT NULL,                -- Analysis run identifier
 *   entity_xid text NOT NULL,               -- ESG entity identifier
 *   flag_type text,                         -- Flag category (extracted for performance)
 *   flag_summary text,                      -- Summary text (extracted from model JSON)
 *   flag_analysis text,                     -- Analysis text (extracted from model JSON)
 *   flag_statements jsonb,                  -- Supporting statements array
 *   model jsonb NOT NULL,                   -- Complete FlagModelData structure
 *   trace_json jsonb                        -- Pipeline trace data for debugging
 * );
 * ```
 *
 * **Performance Indexes**:
 * - `btree (entity_xid, run_id)`: Primary query pattern optimization
 * - `gin (model)`: JSONB field search optimization
 * - `gin (flag_summary, flag_analysis)`: Full-text search support
 * - `gin (trace_json)`: Administrative debugging support
 *
 * ## Flag Model Structure
 * **FlagModelData** (stored in `model` JSONB column):
 * - **flag_type**: 'red' | 'green' (criticality classification)
 * - **is_disclosure_only**: Boolean flag for disclosure identification
 * - **impact_analysis**: Structured impact assessment data
 * - **supporting_statements**: Array of statement IDs providing evidence
 * - **model_sections**: Categorized analysis sections (social, environmental, governance)
 * - **citations**: Document references supporting the analysis
 * - **confidence_score**: AI model confidence in analysis accuracy
 *
 * ## Fetch Method Optimization
 * **Two-Tier Fetch Strategy**:
 * 
 * ### 1. Standard `fetch()` Method (List Views)
 * - **Use Case**: Dashboard listings, summary views, flag browsing
 * - **Optimized Fields**: Only essential fields for list display performance
 * - **Data Volume**: Lightweight for fast rendering and scrolling
 * - **Processing**: Basic flag processing with disclosure filtering
 *
 * ### 2. Detailed `fetchDetailed()` Method (Detail Views)  
 * - **Use Case**: Flag detail modals, expanded views, administrative interfaces
 * - **Complete Fields**: All available data including heavy analysis fields
 * - **Data Volume**: Full flag model data for comprehensive display
 * - **Processing**: Complete flag processing with all metadata
 *
 * ## Disclosure Management Strategy
 * **Intelligent Disclosure Filtering**:
 * The fetcher implements sophisticated disclosure management to balance completeness with relevance:
 *
 * **Default Behavior** (`includeDisclosures: false`):
 * - **Red Flags**: Always included regardless of disclosure status (critical issues)
 * - **Green Flags**: Filtered to exclude disclosure-only items (focus on significant impacts)
 * - **Performance**: Reduces data volume and improves user focus
 *
 * **Complete View** (`includeDisclosures: true`):
 * - **All Flags**: Includes complete flag set including routine disclosures
 * - **Use Case**: Comprehensive analysis, regulatory compliance, complete reporting
 * - **Administrative**: Full data access for debugging and analysis
 *
 * ## Query Performance Optimization
 * **Performance Strategies**:
 * 1. **Selective Field Loading**: Queries only necessary columns for each use case
 * 2. **Extracted Columns**: Uses pre-extracted `flag_summary` and `flag_analysis` columns
 * 3. **Index Utilization**: Leverages composite index on `(entity_xid, run_id)`
 * 4. **Result Ordering**: Consistent ordering by `id DESC` for stable pagination
 * 5. **Request Batching**: Minimizes database round trips through efficient queries
 *
 * ## System Architecture Integration
 * **ESG Analysis Pipeline Context**:
 * - **Analytics Backend**: Python system processes corporate documents and generates effect flags
 * - **DEMISE Model**: Flags are categorized using Domain, Effect, Momentum, Impact, Scope, Ethics analysis
 * - **Data Synchronization**: Flags sync from analytics database to customer database via `xfer_flags`
 * - **API Layer**: This fetcher provides customer-facing access to processed flag data
 * - **Frontend Components**: React components consume flag data for dashboard and detail views
 * - **Real-time Updates**: Supabase real-time features enable live updates when new flags are generated
 *
 * ## Administrative Debugging
 * **Trace Data Access**: `fetchTrace()` method provides access to pipeline execution trace data:
 * - **Pipeline Steps**: Detailed logs of analysis pipeline execution
 * - **Model Decisions**: AI model reasoning and decision points
 * - **Performance Metrics**: Execution timing and resource utilization
 * - **Error Diagnostics**: Detailed error information for troubleshooting
 * - **Access Control**: Available only for administrative debugging purposes
 *
 * ## Security & Access Control
 * **Row Level Security (RLS)**:
 * - **User Authentication**: Requires authenticated user session
 * - **Data Isolation**: Users access only flags for entities they have permissions for
 * - **Admin Functions**: Enhanced access for administrative trace data retrieval
 * - **Query Security**: All database queries automatically apply security policies
 *
 * ## Flag Processing Pipeline
 * **Data Transformation Flow**:
 * 1. **Raw Data Retrieval**: Fetch flag records from `xfer_flags` table
 * 2. **JSON Parsing**: Parse `model` JSONB column using `processFlags()` utility
 * 3. **Disclosure Filtering**: Apply disclosure inclusion/exclusion logic
 * 4. **Type Validation**: Ensure proper TypeScript typing for frontend consumption
 * 5. **State Management**: Update React component state with processed flag data
 *
 * ## Error Handling & Resilience
 * **Robust Error Management**:
 * - **Network Failures**: Graceful handling of database connectivity issues
 * - **Data Validation**: Validates flag structure before state updates
 * - **Request Cancellation**: Proper handling of aborted requests without error noise
 * - **Fallback Behavior**: Returns empty arrays rather than null to prevent UI crashes
 * - **Logging Strategy**: Comprehensive logging for debugging while avoiding user-facing errors
 *
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching Next.js Data Fetching Patterns
 * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortController AbortController API Reference
 * @see /Users/<USER>/worktrees/279/apps/customer/utils/flag-converter.ts Flag Processing Utilities
 * @see /Users/<USER>/worktrees/279/apps/customer/types.ts FlagTypeV2 Type Definitions
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/data/base-data-fetcher.ts Base Data Fetcher Implementation
 * <AUTHOR>
 * @updated 2025-07-23
 * @description ESG Effect Flags data fetcher with optimized disclosure filtering, performance optimization, and comprehensive flag processing
 * @example
 * ```typescript
 * // Initialize flags data fetcher
 * const flagsFetcher = new FlagsDataFetcher(requestRefs, abortControllers);
 * 
 * // Standard fetch for dashboard listing
 * await flagsFetcher.fetch(
 *   { entity: 'ENTITY123', runObject: run, includeDisclosures: false },
 *   setFlags,
 *   setLoading
 * );
 * 
 * // Detailed fetch for flag detail modal
 * await flagsFetcher.fetchDetailed(
 *   { entity: 'ENTITY123', runObject: run, includeDisclosures: true },
 *   setDetailedFlags,
 *   setLoading
 * );
 * 
 * // Administrative trace data access
 * const traceData = await flagsFetcher.fetchTrace(flagId);
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import { FlagTypeV2 } from '@/types'
import { processFlags } from '@/utils/flag-converter'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export class FlagsDataFetcher extends BaseDataFetcher<FlagTypeV2[]> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('flags', currentRequestRefs, abortControllersRef);
    }

    /**
     * Fetch detailed flag data including heavy fields like impact_value_analysis
     * Use this for detail views, modals, and expanded flag displays
     */
    async fetchDetailed(
      dependencies: DataFetcherDependencies,
      setData: (data: FlagTypeV2[] | null) => void,
      setLoading: (loading: boolean) => void,
    ): Promise<void> {
        const { entity, runObject, includeDisclosures = false } = dependencies

        if (!this.validateDependencies(dependencies)) {
            setData(null)
            return
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies)
        setLoading(true)

        try {
            // Full query for detail views - includes heavy model data
            const { data: flagsV2Data, error: flagsV2Error } = await this.supabase
              .from('xfer_flags')
              .select(`
                    id, 
                    run_id, 
                    entity_xid, 
                    flag_type, 
                    flag_summary, 
                    flag_analysis,
                    flag_statements,
                    model
                `)
              .eq('run_id', runObject!.id)
              .eq('entity_xid', entity!)
              .order('id', { ascending: false })
              .abortSignal(abortController.signal)

            if (isStale()) {
                this.logCancellation()
                return
            }

            if (flagsV2Error) {
                this.logError(flagsV2Error, 'fetchDetailedFlags')
                setData([])
                return
            }

            if (flagsV2Data && flagsV2Data.length > 0) {
                let processedFlags = processFlags(flagsV2Data as unknown as FlagTypeV2[])

                if (!includeDisclosures) {
                    processedFlags = processedFlags.filter(flag => {
                        const parsedFlag = typeof flag.model === 'string' ?
                          { ...flag, model: JSON.parse(flag.model) } : flag
                        return parsedFlag.model.flag_type === 'red' || !parsedFlag.model.is_disclosure_only
                    })
                }

                if (!isStale()) {
                    setData(processedFlags)
                    this.logSuccess('Loaded detailed flags', processedFlags.length)
                }
            } else {
                if (!isStale()) {
                    setData([])
                }
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return
            }
            this.logError(error, 'fetchDetailedFlags')
            if (!isStale()) {
                setData([])
            }
        } finally {
            if (!isStale()) {
                setLoading(false)
            }
        }
    }

    /**
     * Fetch trace data for admin debugging
     * Only loads when specifically needed
     */
    async fetchTrace(flagId: number): Promise<any> {
        const { data, error } = await this.supabase
          .from('xfer_flags')
          .select('trace_json')
          .eq('id', flagId)
          .single()

        if (error) {
            this.logError(error, 'fetchTrace')
            return null
        }

        return data?.trace_json
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: FlagTypeV2[] | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject, includeDisclosures = false } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData(null);
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            // Optimized query for list views - only fetch commonly accessed fields
            const { data: flagsV2Data, error: flagsV2Error } = await this.supabase
              .from('xfer_flags')
              .select(`
                 id,
    run_id,
    entity_xid,
    flag_type,
    flag_summary,
    flag_analysis,
    flag_statements,
    model
                `)
                .eq('run_id', runObject!.id)
                .eq('entity_xid', entity!)
              .order('id', { ascending: false })
                .abortSignal(abortController.signal);

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (flagsV2Error) {
                this.logError(flagsV2Error, 'fetchAndFilterFlags');
                setData([]);
                return;
            }

            if (flagsV2Data && flagsV2Data.length > 0) {
                // Process flags to ensure models are properly parsed
                let processedFlags = processFlags(flagsV2Data as unknown as FlagTypeV2[]);

                // Filter out disclosure-only flags if includeDisclosures is false
                // Always keep red flags regardless of disclosure setting
                if (!includeDisclosures) {
                    processedFlags = processedFlags.filter(flag => {
                        // Ensure model is parsed
                        const parsedFlag = typeof flag.model === 'string' ?
                            { ...flag, model: JSON.parse(flag.model) } : flag;

                        // Keep all red flags and non-disclosure-only green flags
                        return parsedFlag.model.flag_type === 'red' || !parsedFlag.model.is_disclosure_only;
                    });
                    console.log(`Filtered flags: ${processedFlags.length} (excluded disclosure-only green flags)`);
                }

                // Final check before setting data
                if (!isStale()) {
                    setData(processedFlags);
                    this.logSuccess("Loaded flags", processedFlags.length);
                }
            } else {
                if (!isStale()) {
                    console.warn("No flags found for this entity and run");
                    setData([]);
                }
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchAndFilterFlags');
            if (!isStale()) {
                setData([]);
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }
}
