import { describe, test, expect, vi, beforeEach } from 'vitest'
import { ModelSectionsDataFetcher } from './model-sections-data-fetcher'
import { DataFetcherDependencies } from './base-data-fetcher'
import { ModelSectionType, RunType } from '@/types'

// Mock the Supabase client factory
const mockSupabaseClient = {
  from: vi.fn()
}

vi.mock('@/app/supabase/client', () => ({
  createClient: () => mockSupabaseClient
}))

// Mock console methods to prevent test output noise
const consoleSpy = {
  log: vi.spyOn(console, 'log').mockImplementation(() => {}),
  error: vi.spyOn(console, 'error').mockImplementation(() => {}),
  warn: vi.spyOn(console, 'warn').mockImplementation(() => {})
}

describe('ModelSectionsDataFetcher', () => {
  let modelSectionsDataFetcher: ModelSectionsDataFetcher
  let mockSetData: ReturnType<typeof vi.fn>
  let mockSetLoading: ReturnType<typeof vi.fn>
  let currentRequestRefs: Map<string, string>
  let abortControllersRef: Map<string, AbortController>

  const mockRunObject: RunType = {
    id: 123,
    completed_at: '2023-01-01T00:00:00Z',
    run_type: 'test',
    scope: 'test'
  }

  const mockModelSectionsData: ModelSectionType[] = [
    {
      id: 1,
      model: 'test-model',
      section: 'section-1',
      title: 'Section 1',
      description: 'First section',
      level: 'high',
      icon: 'icon1',
      status: 'active'
    },
    {
      id: 2,
      model: 'test-model',
      section: 'section-2',
      title: 'Section 2',
      description: 'Second section',
      level: 'medium',
      icon: 'icon2',
      status: 'active'
    }
  ]

  const mockDependencies: DataFetcherDependencies = {
    entity: 'test-entity',
    runObject: mockRunObject,
    model: 'test-model'
  }

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    
    // Reset console spies but keep them as mocks
    consoleSpy.log.mockClear()
    consoleSpy.error.mockClear()
    consoleSpy.warn.mockClear()
    
    // Create fresh refs for each test
    currentRequestRefs = new Map()
    abortControllersRef = new Map()
    
    // Create fresh mocks for setData and setLoading
    mockSetData = vi.fn()
    mockSetLoading = vi.fn()
    
    modelSectionsDataFetcher = new ModelSectionsDataFetcher(currentRequestRefs, abortControllersRef)
  })

  describe('constructor', () => {
    test('should initialize with correct properties', () => {
      expect(modelSectionsDataFetcher).toBeInstanceOf(ModelSectionsDataFetcher)
    })

    test('should initialize supabase client', () => {
      expect(mockSupabaseClient).toBeDefined()
    })
  })

  describe('validateDependencies', () => {
    test('should return true when model is provided', () => {
      const result = modelSectionsDataFetcher.validateDependencies(mockDependencies)
      expect(result).toBe(true)
    })

    test('should return false when model is null', () => {
      const invalidDeps = { ...mockDependencies, model: undefined }
      const result = modelSectionsDataFetcher.validateDependencies(invalidDeps)
      expect(result).toBe(false)
    })

    test('should return false when model is undefined', () => {
      const invalidDeps = { ...mockDependencies, model: undefined }
      const result = modelSectionsDataFetcher.validateDependencies(invalidDeps)
      expect(result).toBe(false)
    })

    test('should return false when model is empty string', () => {
      const invalidDeps = { ...mockDependencies, model: '' }
      const result = modelSectionsDataFetcher.validateDependencies(invalidDeps)
      expect(result).toBe(false)
    })
  })

  describe('fetch', () => {
    let mockQueryBuilder: any

    beforeEach(() => {
      mockQueryBuilder = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        abortSignal: vi.fn().mockResolvedValue({ data: mockModelSectionsData, error: null })
      }
      mockSupabaseClient.from.mockReturnValue(mockQueryBuilder)
    })

    test('should fetch model sections data successfully', async () => {
      await modelSectionsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('xfer_model_sections')
      expect(mockQueryBuilder.select).toHaveBeenCalledWith('*')
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('model', 'test-model')
      expect(mockQueryBuilder.order).toHaveBeenCalledWith('id', { ascending: false })
      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).toHaveBeenCalledWith(mockModelSectionsData)
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should set null data for invalid dependencies', async () => {
      const invalidDeps = { ...mockDependencies, model: undefined }

      await modelSectionsDataFetcher.fetch(invalidDeps, mockSetData, mockSetLoading)

      expect(mockSetData).toHaveBeenCalledWith(null)
      expect(mockSetLoading).not.toHaveBeenCalled()
      expect(mockSupabaseClient.from).not.toHaveBeenCalled()
    })

    test('should handle supabase error', async () => {
      const mockError = new Error('Database error')
      mockQueryBuilder.abortSignal.mockResolvedValue({ data: null, error: mockError })

      await modelSectionsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).toHaveBeenCalledWith([])
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should handle empty data response', async () => {
      mockQueryBuilder.abortSignal.mockResolvedValue({ data: [], error: null })

      await modelSectionsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetData).toHaveBeenCalledWith([])
    })

    test('should handle null data response', async () => {
      mockQueryBuilder.abortSignal.mockResolvedValue({ data: null, error: null })

      await modelSectionsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetData).toHaveBeenCalledWith([])
    })

    test('should handle abort error', async () => {
      const abortError = new Error('Request aborted')
      abortError.name = 'AbortError'
      mockQueryBuilder.abortSignal.mockRejectedValue(abortError)

      await modelSectionsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).not.toHaveBeenCalled()
      // AbortError is handled and returns early, finally block runs if not stale
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should handle network error', async () => {
      const networkError = new Error('Network error')
      mockQueryBuilder.abortSignal.mockRejectedValue(networkError)

      await modelSectionsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).toHaveBeenCalledWith([])
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })

    test('should not set data when request becomes stale', async () => {
      // Mock a delayed response
      mockQueryBuilder.abortSignal.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ data: mockModelSectionsData, error: null }), 50))
      )

      // Start the fetch
      const fetchPromise = modelSectionsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)
      
      // Immediately make the request stale by creating a new one
      modelSectionsDataFetcher['setupRequestCancellation'](mockDependencies)
      
      await fetchPromise

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).not.toHaveBeenCalled()
      expect(mockSetLoading).not.toHaveBeenCalledWith(false)
    })

    test('should not set data when stale after error', async () => {
      const mockError = new Error('Database error')
      mockQueryBuilder.abortSignal.mockImplementation(() => 
        new Promise((_, reject) => setTimeout(() => reject(mockError), 50))
      )

      // Start the fetch
      const fetchPromise = modelSectionsDataFetcher.fetch(mockDependencies, mockSetData, mockSetLoading)
      
      // Immediately make the request stale by creating a new one
      modelSectionsDataFetcher['setupRequestCancellation'](mockDependencies)
      
      await fetchPromise

      expect(mockSetLoading).toHaveBeenCalledWith(true)
      expect(mockSetData).not.toHaveBeenCalled()
      expect(mockSetLoading).not.toHaveBeenCalledWith(false)
    })
  })
})