/**
 * ESG Entity Data Fetchers - Centralized Module for Corporate Sustainability Analysis
 *
 * This module serves as the unified entry point for all ESG (Environmental, Social, Governance) 
 * entity data fetching operations within the EkoIntelligence customer application. It provides
 * a comprehensive suite of specialized data fetchers that retrieve, process, and manage various
 * types of corporate sustainability analysis data from the synchronized customer database.
 *
 * ## Core Purpose
 * **Centralized Data Access**: Provides a single import location for all ESG data fetching capabilities,
 * enabling React components and hooks to access corporate sustainability data through a consistent,
 * type-safe interface. Each fetcher specializes in a specific aspect of ESG analysis while sharing
 * common patterns for request management, caching, and error handling.
 *
 * ## Data Fetcher Architecture
 * **Template Method Pattern**: All fetchers extend the abstract `BaseDataFetcher` class, which provides:
 * - **Request Lifecycle Management**: Intelligent request cancellation and deduplication
 * - **Type Safety**: Generic TypeScript implementation ensuring compile-time type checking
 * - **Error Handling**: Standardized error management with proper logging and user feedback
 * - **Supabase Integration**: Secure database access with Row Level Security (RLS) policies
 * - **Performance Optimization**: Automatic request cancellation for stale requests
 *
 * ## Available Data Fetchers
 * **Corporate Analysis Data Types**:
 * - **`FlagsDataFetcher`**: ESG effect flags representing identified impacts, risks, and opportunities
 * - **`ClaimsDataFetcher`**: Corporate ESG claims with verification status and confidence scoring
 * - **`PromisesDataFetcher`**: Future-oriented commitments with tracking and fulfillment analysis
 * - **`CherryDataFetcher`**: Cherry-picked statements and selective disclosure analysis
 * - **`ModelSectionsDataFetcher`**: DEMISE model sections for comprehensive ESG framework analysis
 * - **`ScoreDataFetcher`**: Entity scoring data including overall ESG performance metrics
 * - **`VagueDataFetcher`**: Analysis of vague language and ambiguous statements in corporate communications
 *
 * ## System Integration Context
 * **ESG Data Platform Architecture**:
 * - **Analytics Backend**: Python-based system processes raw ESG documents and generates analysis
 * - **Data Synchronization**: `xfer_` tables sync processed analytics data to customer database
 * - **API Layer**: These fetchers provide customer-facing access to synchronized ESG data
 * - **Frontend Components**: React dashboard components consume data through these fetchers
 * - **Real-time Updates**: Supabase real-time capabilities enable live data updates
 * - **Security Layer**: RLS policies ensure users only access data for authorized entities
 *
 * ## Database Schema Overview
 * **Transfer Tables (`xfer_*`)**: Synchronized data from analytics database:
 * - `xfer_flags`: ESG effect flags with impact analysis and severity ratings
 * - `xfer_claims`: Corporate claims with verification outcomes and confidence scores
 * - `xfer_promises`: Future commitments with tracking status and fulfillment metrics
 * - `xfer_entities`: Entity metadata including names, identifiers, and classification
 * - `xfer_model_sections`: DEMISE model analysis sections for comprehensive ESG evaluation
 * - `xfer_score`: Entity scoring data with domain-specific ESG performance metrics
 *
 * ## Usage Patterns
 * **Typical Component Integration**:
 * ```typescript
 * import { FlagsDataFetcher, ClaimsDataFetcher } from '@/components/context/entity/data';
 * 
 * // Inside React component or custom hook
 * const flagsFetcher = new FlagsDataFetcher(requestRefs, abortControllers);
 * const claimsFetcher = new ClaimsDataFetcher(requestRefs, abortControllers);
 * 
 * // Fetch data with proper dependency validation
 * await flagsFetcher.fetch(dependencies, setFlagsData, setFlagsLoading);
 * await claimsFetcher.fetch(dependencies, setClaimsData, setClaimsLoading);
 * ```
 *
 * ## Request Management
 * **Concurrent Request Handling**: Each fetcher manages its own request lifecycle:
 * - **Request Identification**: Unique request IDs with entity, run, and timestamp data
 * - **Automatic Cancellation**: Newer requests automatically cancel older ones
 * - **Staleness Detection**: Prevents stale data from updating UI state
 * - **Resource Cleanup**: Proper abort controller cleanup to prevent memory leaks
 *
 * ## Performance Considerations
 * **Optimized Data Loading**:
 * - **Selective Field Loading**: Fetchers load only required fields for performance
 * - **Smart Filtering**: Database-level filtering reduces data transfer and processing
 * - **Request Deduplication**: Prevents duplicate requests for identical data
 * - **Abort Controller Usage**: Cancels unnecessary network requests to improve performance
 *
 * ## Error Handling Strategy
 * **Graceful Degradation**: All fetchers implement consistent error handling:
 * - **AbortError Recognition**: Distinguishes cancelled requests from genuine errors
 * - **Contextual Logging**: Detailed error context for debugging and monitoring
 * - **User Experience**: Maintains application functionality during data loading failures
 * - **Recovery Patterns**: Consistent error recovery across all data types
 *
 * ## Security & Authorization
 * **Data Access Control**:
 * - **Row Level Security**: Automatic application of Supabase RLS policies
 * - **Session-based Access**: User authentication required for all data operations
 * - **Entity Permissions**: Users can only access data for authorized entities
 * - **Audit Logging**: Database-level logging of data access patterns
 *
 * ## Type Safety & Development Experience
 * **TypeScript Integration**:
 * - **Generated Types**: Database types automatically generated from Supabase schema
 * - **Compile-time Validation**: Catch data structure mismatches during development
 * - **IntelliSense Support**: Full IDE support with type hints and autocompletion
 * - **Interface Consistency**: Standardized interfaces across all data fetcher implementations
 *
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching Next.js Data Fetching Patterns
 * @see https://react.dev/reference/react/hooks React Hooks Documentation
 * @see ./base-data-fetcher.ts Base Data Fetcher Abstract Class
 * @see ./flags-data-fetcher.ts ESG Effect Flags Data Fetcher
 * @see ./claims-data-fetcher.ts Corporate Claims Data Fetcher
 * @see ./promises-data-fetcher.ts Corporate Promises Data Fetcher
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Centralized module providing unified access to all ESG entity data fetching operations for the customer application
 * @example
 * ```typescript
 * // Import specific data fetchers for component use
 * import { 
 *   FlagsDataFetcher, 
 *   ClaimsDataFetcher, 
 *   BaseDataFetcher,
 *   type DataFetcherDependencies 
 * } from '@/components/context/entity/data';
 * 
 * // Example usage in a React hook
 * function useEntityData(entity: string, runObject: RunType) {
 *   const [flagsData, setFlagsData] = useState(null);
 *   const [claimsData, setClaimsData] = useState(null);
 *   const [loading, setLoading] = useState(false);
 *   
 *   const requestRefs = useRef(new Map());
 *   const abortControllers = useRef(new Map());
 *   
 *   const dependencies: DataFetcherDependencies = {
 *     entity,
 *     runObject,
 *     model: 'ekoIntelligence',
 *     includeDisclosures: false
 *   };
 *   
 *   useEffect(() => {
 *     const flagsFetcher = new FlagsDataFetcher(requestRefs.current, abortControllers.current);
 *     const claimsFetcher = new ClaimsDataFetcher(requestRefs.current, abortControllers.current);
 *     
 *     if (flagsFetcher.validateDependencies(dependencies)) {
 *       flagsFetcher.fetch(dependencies, setFlagsData, setLoading);
 *     }
 *     
 *     if (claimsFetcher.validateDependencies(dependencies)) {
 *       claimsFetcher.fetch(dependencies, setClaimsData, setLoading);
 *     }
 *   }, [entity, runObject]);
 *   
 *   return { flagsData, claimsData, loading };
 * }
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
export { BaseDataFetcher, type DataFetcherDependencies, type RequestCancellation } from './base-data-fetcher';
export { FlagsDataFetcher } from './flags-data-fetcher';
export { PromisesDataFetcher } from './promises-data-fetcher';
export { CherryDataFetcher } from './cherry-data-fetcher';
export { ClaimsDataFetcher } from './claims-data-fetcher';
export { ModelSectionsDataFetcher } from './model-sections-data-fetcher';
export { ScoreDataFetcher, type ScoreData } from './score-data-fetcher';
export { VagueDataFetcher, type VagueData } from './vague-data-fetcher';
