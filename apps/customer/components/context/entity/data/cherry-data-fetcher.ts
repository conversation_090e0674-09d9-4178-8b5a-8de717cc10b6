/**
 * Cherry Picking Data Fetcher for ESG Selective Highlighting Analysis
 *
 * This specialized data fetcher retrieves and manages "cherry picking" analysis data, which identifies
 * instances where ESG entities engage in selective highlighting - presenting favorable information
 * while omitting negative context or data points. The fetcher provides real-time access to ESG
 * selective disclosure patterns and potential greenwashing indicators through systematic analysis
 * of corporate communications and sustainability reports.
 *
 * ## Core Functionality
 * - **Selective Highlighting Detection**: Retrieves analysis of cherry-picked positive statements versus omitted negative context
 * - **Greenwashing Identification**: Accesses pattern recognition data for selective ESG disclosure practices
 * - **Bias Analysis**: Provides data on potential presentation bias and incomplete disclosure patterns
 * - **Statement Classification**: Retrieves categorized positive/negative statement pairs with selective emphasis analysis
 * - **Real-time Data Access**: Leverages Supabase client for live updates on cherry picking analysis results
 * - **Request Management**: Implements concurrent request handling with automatic stale request cancellation
 *
 * ## Cherry Picking Analysis Context
 * **Selective Highlighting Detection**:
 * The cherry picking analysis identifies cases where entities:
 * - Emphasize positive environmental or social achievements while downplaying negative impacts
 * - Present incomplete data sets that favor their ESG performance narrative
 * - Use selective timeframes, metrics, or geographic scopes to enhance appearance
 * - Omit relevant context that would provide a balanced view of ESG performance
 * - Highlight improvements without disclosing absolute performance levels or industry comparisons
 *
 * ## Database Integration
 * **xfer_selective Table Structure**:
 * - `id` (integer): Unique identifier for each cherry picking analysis record
 * - `entity_xid` (text): ESG entity identifier for data scoping and authorization
 * - `run_id` (integer): Analysis run identifier linking to specific analytical sessions
 * - `label` (text, nullable): Human-readable label describing the cherry picking pattern
 * - `model` (jsonb): Complex JSON structure containing detailed analysis data and statement pairs
 * - `analysis` (text, nullable): Extracted analysis text explaining the selective highlighting pattern
 * - `explanation` (text, nullable): Detailed explanation of why this constitutes cherry picking
 * - **reason** (text, nullable): Reasoning behind the cherry picking classification and severity
 *
 * **Performance Optimizations**:
 * - Column extraction from JSON model field to text columns for improved query performance
 * - GIN indexing on analysis text for full-text search capabilities
 * - Composite indexing on entity_xid and run_id for efficient data retrieval
 * - Row Level Security (RLS) policies ensuring proper data access authorization
 *
 * ## Data Model Structure
 * **CherryTypeV2 Interface**:
 * The fetched data conforms to the CherryTypeV2 interface which includes:
 * - Statement pairs: Positive statements emphasized vs negative statements omitted
 * - Analysis metadata: Entity information, run identifiers, and analytical context
 * - Classification data: Cherry picking severity levels and pattern categorization
 * - Temporal context: Analysis timestamps and report period coverage
 * - Evidence links: Citations and source document references for verification
 *
 * ## System Architecture Integration
 * **ESG Analysis Pipeline**:
 * - **Analytics Backend**: Python system analyzes corporate communications for selective highlighting patterns
 * - **Data Processing**: Machine learning algorithms identify statement pairs and disclosure bias
 * - **Data Sync Layer**: `xfer_selective` table synchronizes cherry picking analysis between databases
 * - **API Layer**: This fetcher provides customer-facing access to cherry picking analysis results
 * - **Frontend Display**: Customer dashboard components consume this data for greenwashing assessment visualization
 * - **Real-time Updates**: Supabase real-time features enable live updates as new analyses complete
 *
 * ## Security and Access Control
 * **Row Level Security (RLS)**:
 * - Authenticated users can view cherry picking analysis through "Users can view cherry pick items" policy
 * - Admin users have delete permissions through "Admins can delete cherry pick items" policy
 * - Entity-based access control ensures users only see data for entities they're authorized to access
 * - Supabase authentication handles session management and user context for secure data retrieval
 *
 * ## Usage in Greenwashing Analysis
 * **Dashboard Integration**:
 * This fetcher specifically supports the `/customer/dashboard/gw/cherry/` route for cherry picking analysis display:
 * - Selective highlighting visualization components consume this data
 * - Timeline views showing evolution of selective disclosure patterns over time
 * - Comparison tools highlighting omitted context versus emphasized achievements
 * - Risk assessment indicators based on cherry picking severity and frequency
 * - Citation systems linking back to source documents for verification and transparency
 *
 * ## Request Lifecycle
 * **Dependency Validation**: Requires valid entity identifier and run object with ID
 * **Query Execution**: Retrieves cherry picking data ordered by ID (most recent first)
 * **Error Handling**: Manages Supabase query errors and abort signals for cancelled requests
 * **State Management**: Updates loading states and data only for current, non-stale requests
 * **Performance Logging**: Tracks successful data loads with record counts for monitoring
 *
 * @see https://supabase.com/docs/guides/database/postgres Supabase PostgreSQL Documentation
 * @see https://nextjs.org/docs/app/building-your-application/data-fetching Next.js Data Fetching Patterns
 * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortController AbortController API for Request Cancellation
 * @see /Users/<USER>/worktrees/279/apps/customer/types/cherry.ts CherryTypeV2 Interface Definition
 * @see /Users/<USER>/worktrees/279/apps/customer/app/customer/dashboard/gw/cherry/page.tsx Cherry Picking Dashboard Component
 * @see /Users/<USER>/worktrees/279/apps/customer/components/context/entity/data/base-data-fetcher.ts Base Data Fetcher Implementation
 * <AUTHOR>
 * @updated 2025-07-23
 * @description Specialized data fetcher for cherry picking analysis in ESG selective highlighting detection and greenwashing identification
 * @example
 * ```typescript
 * // Initialize cherry data fetcher
 * const cherryFetcher = new CherryDataFetcher(currentRequestRefs, abortControllersRef);
 * 
 * // Fetch cherry picking analysis data
 * await cherryFetcher.fetch(
 *   { entity: 'ENTITY_123', runObject: { id: 456 } },
 *   (data) => setCherryData(data),
 *   (loading) => setLoading(loading)
 * );
 * 
 * // Process cherry picking results
 * cherryData?.forEach(cherry => {
 *   console.log(`Cherry picking pattern: ${cherry.label}`);
 *   console.log(`Analysis: ${cherry.analysis}`);
 *   console.log(`Reason: ${cherry.reason}`);
 * });
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */
import { CherryTypeV2 } from '@/types'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export class CherryDataFetcher extends BaseDataFetcher<CherryTypeV2[]> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('cherry', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: CherryTypeV2[] | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData(null);
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            const { data: cherryV2Data, error: cherryV2Error } = await this.supabase
              .from('xfer_selective')
              .select(`
                    id, 
                    run_id, 
                    entity_xid, 
                    label,
                    analysis,
                    explanation,
                    reason,
                    model
                `)
                .eq('entity_xid', entity!)
                .eq('run_id', runObject!.id)
              .order('id', { ascending: false })
                .abortSignal(abortController.signal);

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (cherryV2Error) {
                this.logError(cherryV2Error, 'fetchCherryData');
                if (!isStale()) {
                    setData([]);
                }
                return;
            }

            if (!isStale()) {
                setData((cherryV2Data as unknown as CherryTypeV2[]) || []);
                this.logSuccess("Loaded cherry data", cherryV2Data?.length || 0);
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchCherryData');
            if (!isStale()) {
                setData([]);
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }
}
