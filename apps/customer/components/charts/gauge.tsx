/**
 * # ESG Analytics Gauge Component - Animated Circular Progress Visualization
 *
 * This React component provides a sophisticated animated circular gauge visualization for displaying
 * ESG (Environmental, Social, Governance) metrics, scores, and data visualizations throughout the
 * customer application. Built with Framer Motion for smooth animations and SVG-based rendering,
 * the component offers responsive sizing, customizable styling, and accessibility features for
 * presenting quantitative data in an engaging visual format.
 *
 * ## Core Functionality
 * - **Animated Progress Display**: Smooth circular progress animation using SVG stroke techniques
 * - **Interactive Value Counter**: Incremental number counting animation that synchronizes with circle progress
 * - **Viewport-Based Activation**: Animation triggers only when component enters viewport using `useInView`
 * - **Responsive Sizing**: Four predefined size variants (sm, md, lg, xl) for different UI contexts
 * - **Custom Styling**: Configurable colors, labels, and percentage display options
 * - **Accessibility Ready**: Comprehensive test ID support for Playwright end-to-end testing
 *
 * ## ESG Platform Integration
 * **Data Visualization Context**: Designed specifically for ESG analytics dashboard integration:
 * - **Score Visualization**: Perfect for displaying entity ESG scores, risk assessments, and performance metrics
 * - **Progress Indicators**: Ideal for showing completion rates, analysis progress, and data quality metrics
 * - **Dashboard Cards**: Optimized for use within glass-morphism cards and metric display components
 * - **Real-Time Updates**: Handles dynamic value updates from Supabase real-time subscriptions
 * - **Responsive Design**: Seamlessly adapts to various dashboard layouts and screen sizes
 *
 * ## Component Architecture
 * **Props Interface**: Comprehensive configuration for flexible usage across the platform:
 * - **value**: number | null - The numeric value to display (0-100 scale)
 * - **size**: "sm" | "md" | "lg" | "xl" - Predefined size variants with consistent scaling
 * - **showValue**: boolean - Controls visibility of the numeric value and label display
 * - **color**: string - CSS class for the progress arc color (defaults to green theme)
 * - **bgcolor**: string - CSS class for the background circle color with dark mode support
 * - **label**: string - Optional text label displayed below the numeric value
 * - **percentage**: boolean - Adds percentage symbol to displayed value when enabled
 * - **className**: string - Additional CSS classes for custom styling and positioning
 * - **data-testid**: string - Test identifier for Playwright automated testing
 * - **data-value-testid**: string - Specific test identifier for the value display element
 *
 * ## Animation System & Performance
 * **Framer Motion Integration**: Leverages advanced animation capabilities for smooth user experience:
 * - **Viewport Detection**: Uses `useInView` hook with 50% threshold for optimal animation timing
 * - **Synchronized Animations**: Coordinated circle progress and value counter animations
 * - **Performance Optimization**: Animations only activate when component is visible on screen
 * - **Smooth Transitions**: Configurable easing curves and animation durations for professional feel
 * - **Memory Efficient**: Proper cleanup and animation lifecycle management
 *
 * ## Technical Implementation Details
 * **SVG-Based Rendering**: High-quality scalable graphics with precise mathematical calculations:
 * - **Circle Mathematics**: Uses 2πr formula for circumference calculation (radius = 40)
 * - **Stroke Animation**: `strokeDasharray` and `strokeDashoffset` technique for progress visualization
 * - **Responsive Scaling**: ViewBox-based scaling maintains crisp graphics at all sizes
 * - **Color System**: CSS class-based theming with platform color palette integration
 * - **Counter Animation**: Custom interval-based number animation with configurable speed
 *
 * ## Size Variants & Responsive Design
 * **Predefined Size System**: Four carefully designed size options for different UI contexts:
 * - **sm**: 36x36px - Compact size for dense layouts, small cards, and inline metrics
 * - **md**: 72x72px - Standard size for dashboard cards and general metric displays
 * - **lg**: 180x180px - Large size for prominent feature displays and detailed views
 * - **xl**: 220x220px - Extra large for hero sections and primary data showcases
 * - **Typography Scaling**: Automatic text size adjustment for optimal readability at each size
 * - **Label Sizing**: Proportional label text sizing to maintain visual hierarchy
 *
 * ## Accessibility & Testing Integration
 * **Comprehensive Testing Support**: Built with automated testing and accessibility in mind:
 * - **Test ID Architecture**: Dual test ID system for component and value testing
 * - **Screen Reader Support**: Semantic HTML structure with proper ARIA considerations
 * - **Keyboard Navigation**: Focusable elements and proper tab order support
 * - **Visual Indicators**: High contrast color options and clear visual feedback
 * - **Playwright Integration**: Optimized for reliable end-to-end test automation
 *
 * ## Usage Patterns & Integration
 * **Common Implementation Scenarios**: Versatile component suitable for various ESG data displays:
 * - **Entity Scores**: Display ESG risk scores, sustainability ratings, and performance metrics
 * - **Analysis Progress**: Show document processing, analysis completion, and data quality indicators
 * - **Dashboard Metrics**: Integrate with dashboard cards, summary panels, and metric grids
 * - **Report Components**: Embed in generated reports, executive summaries, and detailed analysis
 * - **Real-Time Data**: Connect with Supabase subscriptions for live updating metrics
 *
 * ## System Architecture Integration
 * This component fits within the broader ESG analysis system:
 * - **Frontend Layer**: React/Next.js 15 customer application for data visualization
 * - **Data Layer**: Supabase customer database integration for real-time metric updates
 * - **Analytics Backend**: Python analytics system generates scores synchronized via xfer_ tables
 * - **UI Design System**: Glass-morphism design language with consistent theming and animations
 * - **Testing Infrastructure**: Playwright end-to-end testing with comprehensive selector support
 *
 * @see https://www.framer.com/motion/ Framer Motion Animation Library Documentation
 * @see https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/stroke-dasharray SVG Stroke Dash Array Documentation
 * @see https://tailwindcss.com/docs/responsive-design Tailwind CSS Responsive Design
 * @see {@link ../../../lib/utils.ts} Utility Functions for Class Management
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Animated circular gauge component for ESG metrics visualization with responsive sizing and Framer Motion animations
 * @example ```tsx
 * // Basic ESG score display
 * <Gauge 
 *   value={75} 
 *   size="md" 
 *   label="ESG Score" 
 *   percentage={true}
 *   data-testid="entity-score-gauge"
 * />
 *
 * // Large dashboard metric
 * <Gauge
 *   value={92}
 *   size="lg"
 *   color="text-blue-500"
 *   bgcolor="text-gray-200"
 *   label="Analysis Complete"
 *   showValue={true}
 *   data-testid="analysis-progress"
 * />
 *
 * // Compact inline metric
 * <Gauge
 *   value={68}
 *   size="sm"
 *   showValue={false}
 *   color="text-red-400"
 *   className="inline-block"
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

"use client";
import React, { useEffect, useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { cn } from '@utils/lib/utils'

export const Gauge = ({
                          value,
                          size = "sm",
                          showValue = true,
                          color = "text-[#69b386]",
                          bgcolor = "text-[#fff] dark:text-[#fff]",
                          label = "",
                          percentage = false,
                          className,
                          'data-testid': dataTestId,
                          'data-value-testid': valueTestId
                      }: {
    value: number | null;
    size: "sm" | "md" | "lg" | "xl";
    showValue: boolean;
    color?: String;
    bgcolor?: String;
    label?: String;
    percentage?: boolean;
    className?: string;
    'data-testid'?: string;
    'data-value-testid'?: string;
}) => {
    const circumference = 2 * Math.PI * 40;
    const valueInCircumference = value ? (value / 100) * circumference : 0;
    const strokeDasharray = `${circumference} ${circumference}`;
    const initialOffset = circumference;
    const strokeDashoffset = initialOffset - valueInCircumference;
    const [displayVal, setDisplayVal] = React.useState(0);
    const ref = useRef(null);
    const isInView = useInView(ref, {once: true, amount: 0.5});

    const sizes = {
        sm: {
            width: "36",
            height: "36",
            textSize: "text-sm",
            labelSize: "text-[10px]",
        },
        md: {
            width: "72",
            height: "72",
            textSize: "text-lg",
            labelSize: "text-xs",
        },
        lg: {
            width: "180",
            height: "180",
            textSize: "text-xl",
            labelSize: "text-sm",
        },
        xl: {
            width: "220",
            height: "220",
            textSize: "text-4xl",
            labelSize: "text-md",
        },
    };

    useEffect(() => {
        let timer;
        if (isInView && value !== null) {
            let i = 0;
            let timer: any;

            const startCounter = () => {
                timer = setInterval(() => {
                    if (i < value) {
                        i += 1;
                        setDisplayVal(i);
                    } else {
                        clearInterval(timer);
                    }
                }, 1200 / value);
            };

            startCounter();

            return () => clearInterval(timer);
        }
    }, [isInView, value]);

    return (
        <div className={cn("flex flex-col items-center justify-center relative", className)} ref={ref} data-testid={dataTestId}>
            <motion.svg
                fill="none"
                shapeRendering="crispEdges"
                height={sizes[size].height}
                width={sizes[size].width}
                viewBox="0 0 120 120"
                strokeWidth="2"
                className="transform overflow-visible "
                initial={{opacity: 0, scale: 0.8}}
                animate={isInView ? {opacity: 1, scale: 1} : {}}
                transition={{duration: 0.5, ease: "easeOut"}}
            >
                <circle
                    className={`${bgcolor} drop-shadow-md`}
                    strokeWidth="20"
                    stroke="currentColor"
                    fill="transparent"
                    shapeRendering="geometricPrecision"
                    r="40"
                    cx="60"
                    cy="60"
                />
                <motion.circle
                    className={cn(color,'opacity-60')}
                    strokeWidth="16"
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={initialOffset}
                    shapeRendering="geometricPrecision"
                    strokeLinecap="round"
                    stroke="currentColor"
                    fill="transparent"
                    transform="rotate(-90, 60, 60)"

                    r="40"
                    cx="60"
                    cy="60"
                    initial={{strokeDashoffset: initialOffset}}
                    animate={isInView ? {strokeDashoffset} : {}}
                    transition={{duration: 1.2, ease: "easeIn"}}
                />
            </motion.svg>
            {showValue ? (
                <motion.div
                    className="absolute flex opacity-0"
                    animate={isInView ? {opacity: 1} : {}}
                    transition={{duration: 0.8, ease: "easeOut"}}
                >
                    <div className="text-center">
                        <p className={`opacity-90 ${sizes[size].textSize}`} data-testid={valueTestId || (dataTestId ? `${dataTestId}-value` : undefined)}>
                            {displayVal}
                            {percentage ? "%" : ""}
                        </p>
                        <p className={`opacity-80 ${sizes[size].labelSize}`}>{label}</p>
                    </div>
                </motion.div>
            ) : null}
        </div>
    );
};
