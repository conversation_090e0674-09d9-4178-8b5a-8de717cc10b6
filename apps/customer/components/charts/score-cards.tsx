/**
 * # ESG Analytics Score Card Components - Interactive Metric Display & Visualization Suite
 *
 * This module provides sophisticated score card components for visualizing ESG (Environmental, Social,
 * Governance) metrics, risk assessments, and performance indicators throughout the customer application.
 * Built with Next.js 15 and React, these components feature glass-morphism design, interactive tooltips,
 * responsive layouts, and dynamic color theming based on score values and metric types for comprehensive
 * ESG data presentation.
 *
 * ## Core Functionality
 * - **Interactive Score Cards**: Clickable cards with hover animations and navigation links
 * - **Gauge Visualization**: Advanced circular gauge displays with responsive sizing
 * - **Dynamic Color Theming**: Smart color assignment based on score values and inverse metrics
 * - **Glass-Morphism Design**: Translucent frosted glass effects with backdrop blur styling
 * - **Responsive Layout**: Adaptive sizing and typography for various screen sizes and devices
 * - **Accessibility Support**: Comprehensive tooltip system and test ID integration for automation
 *
 * ## ESG Platform Integration
 * **Score Data Context**: Designed for ESG analytics dashboard integration with data from:
 * - **xfer_score Table**: Entity scores synchronized from analytics database with numeric values and JSON models
 * - **Real-Time Updates**: Supabase-powered live score updates with automatic UI refreshing
 * - **Multi-Metric Support**: Environmental, Social, Governance, and composite risk score displays
 * - **Entity Navigation**: Deep-linking to detailed entity analysis pages and metric breakdowns
 * - **Dashboard Cards**: Optimized for dashboard grid layouts and summary metric displays
 *
 * ## Component Architecture
 * **ScoreCard Props Interface**: Comprehensive configuration for flexible metric display:
 * - **score**: number - Primary numeric value to display (0-100 scale typical)
 * - **label**: string - Descriptive text label for the metric category
 * - **icon**: LucideIcon - Visual icon component for metric type identification
 * - **subtext**: string (optional) - Additional context text displayed below main score
 * - **inverse**: boolean - Inverts color logic for metrics where lower values are better
 * - **color**: boolean - Enables dynamic color theming based on score values
 * - **scoreForColor**: number | null - Alternative score value for color determination logic
 * - **scoreTooltip**: string (optional) - Tooltip text for score value hover information
 * - **labelTooltip**: string (optional) - Tooltip text for label hover explanations
 * - **subTextTooltip**: string (optional) - Tooltip text for subtext additional details
 * - **href**: string - Navigation URL for click-through functionality
 * - **percentage**: boolean (optional) - Adds percentage symbol to displayed value
 * - **data-testid**: string (optional) - Test identifier for Playwright automation
 *
 * **GuageCard Props Interface**: Extended configuration for gauge-style metric visualization:
 * - **score**: number | null - Numeric value for gauge progress display
 * - **label**: string - Primary label for gauge metric identification
 * - **icon**: LucideIcon (optional) - Visual icon for metric type representation
 * - **subtext**: string (optional) - Secondary descriptive text below gauge
 * - **inverse**: boolean - Inverts gauge color logic for reverse metrics
 * - **color**: boolean - Enables dynamic color theming for gauge visualization
 * - **scoreForColor**: number | null (optional) - Alternative score for color calculation
 * - **percentage**: boolean (optional) - Displays score as percentage value
 * - **scoreTooltip**: string (optional) - Tooltip information for gauge value
 * - **labelTooltip**: string (optional) - Tooltip explanation for gauge label
 * - **subTextTooltip**: string (optional) - Tooltip details for subtext information
 *
 * ## Glass-Morphism Design System
 * **Dynamic Color Classes**: Advanced theming system with score-based color assignment:
 * - **glass-effect-brand-strong-lit**: Green tint for excellent scores (≥60 normal, <30 inverse)
 * - **glass-effect-brand-alt-strong-lit**: Yellow tint for moderate scores (30-59 normal, 30-59 inverse)
 * - **glass-effect-brand-compliment-strong-lit**: Red tint for poor scores (<30 normal, ≥60 inverse)
 * - **glass-effect-neutral-strong-lit**: Neutral gray tint for null scores or 45-55 range
 * - **Gradient Backgrounds**: Matching gradient classes for consistent visual hierarchy
 * - **Hover Animations**: Smooth lift effects with shadow transitions and transform animations
 *
 * ## Interactive Features & Navigation
 * **Enhanced User Experience**: Comprehensive interaction and feedback systems:
 * - **Click Navigation**: Next.js Link integration for seamless page navigation
 * - **Hover Effects**: Subtle lift animations with shadow depth changes for visual feedback
 * - **Tooltip System**: Radix UI tooltip components with glass-morphism styling
 * - **Responsive Behavior**: Adaptive layouts and text sizing for mobile and desktop
 * - **Loading States**: Graceful handling of null scores and loading data states
 *
 * ## Responsive Design & Layout
 * **Window Size Adaptation**: Dynamic sizing based on screen dimensions:
 * - **Mobile Optimization**: Compact layouts with reduced text sizes for small screens
 * - **Desktop Enhancement**: Larger typography and expanded visual elements
 * - **Gauge Sizing**: Responsive gauge dimensions using useWindowSize hook
 * - **Grid Integration**: Optimized for CSS Grid and Flexbox dashboard layouts
 * - **Typography Scaling**: Automatic text size adjustment across breakpoints
 *
 * ## Color Logic & Score Interpretation
 * **Smart Color Assignment**: Sophisticated scoring system with dual logic modes:
 * - **Normal Metrics** (higher is better): ESG scores, sustainability ratings, performance metrics
 * - **Inverse Metrics** (lower is better): Risk scores, violation counts, negative impact measures
 * - **Threshold System**: 60+ (excellent), 30-59 (moderate), <30 (poor) with inverse logic support
 * - **Neutral Range**: 45-55 scores display neutral gray for balanced/uncertain metrics
 * - **Null Handling**: Graceful display of unavailable or loading score data
 *
 * ## Utility Functions Integration
 * **getRatingColor Function**: Converts numeric scores to appropriate CSS color classes:
 * - **Input**: score (number | null), inverse (boolean) for metric type indication
 * - **Output**: CSS class string for text color assignment
 * - **Logic**: Threshold-based color assignment with inverse metric support
 *
 * **getRatingText Function**: Provides human-readable score interpretations:
 * - **Input**: score (number | null), inverse (boolean) for metric interpretation
 * - **Output**: Descriptive text ("Excellent", "Good", "Average", "Poor", "Very Poor", "Terrible", "N/A")
 * - **Inverse Logic**: Automatically inverts score calculation for reverse metrics
 *
 * ## System Architecture Integration
 * This component suite integrates with the broader ESG analysis system:
 * - **Frontend Layer**: React/Next.js 15 customer application with App Router navigation
 * - **Data Layer**: Supabase customer database integration with real-time score updates
 * - **Analytics Backend**: Python analytics system generating scores via xfer_score table synchronization
 * - **UI Framework**: Tailwind CSS with custom glass-morphism classes and responsive utilities
 * - **Testing Infrastructure**: Playwright end-to-end testing with comprehensive data-testid support
 *
 * @see https://nextjs.org/docs/app/api-reference/components/link Next.js Link Component Documentation
 * @see https://lucide.dev/ Lucide React Icons Library
 * @see https://www.radix-ui.com/primitives/docs/components/tooltip Radix UI Tooltip Documentation
 * @see https://tailwindcss.com/docs/backdrop-blur Tailwind CSS Backdrop Blur Effects
 * @see {@link ./gauge.tsx} Gauge Component for Advanced Circular Visualizations
 * @see {@link ../ui/card.tsx} Card UI Components with Glass-Morphism Styling
 * @see {@link ../../lib/utils.ts} Utility Functions for Class Name Management
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Interactive ESG score card components with glass-morphism design and responsive gauge visualizations
 * @example ```tsx
 * // Basic ESG score card with navigation
 * <ScoreCard
 *   score={75}
 *   label="ESG Score"
 *   icon={TrendingUp}
 *   subtext="Last updated today"
 *   inverse={false}
 *   color={true}
 *   scoreForColor={75}
 *   href="/entity/eko-123/overview"
 *   percentage={true}
 *   data-testid="esg-score-card"
 * />
 *
 * // Gauge-style risk metric display
 * <GuageCard
 *   score={42}
 *   label="Risk Assessment"
 *   icon={AlertTriangle}
 *   subtext="Medium risk level"
 *   inverse={true}
 *   color={true}
 *   percentage={true}
 *   scoreTooltip="Overall risk score based on ESG factors"
 *   labelTooltip="Comprehensive risk assessment methodology"
 * />
 *
 * // Compact score card without gauge
 * <ScoreCard
 *   score={88}
 *   label="Environmental"
 *   icon={Leaf}
 *   inverse={false}
 *   color={true}
 *   scoreForColor={88}
 *   href="/entity/eko-123/environmental"
 *   data-testid="env-score"
 * />
 * ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import { LucideIcon } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Gauge } from './gauge'
import React from 'react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import Link from 'next/link'
import { useWindowSize } from '@react-hookz/web'
import { cn } from '@utils/lib/utils'


export function getRatingColor(rating: number | null, inverse: boolean): string {
    let color = "";
    if (rating === null) return "white";
    if (!inverse) {
        if (rating >= 60) color = "text-brand";
        else if (rating >= 30) color = "text-brand-accent";
        else color = "text-brand-accent";
    } else {
        if (rating >= 60) color = "text-brand-compliment";
        else if (rating >= 30) color = "text-brand-accent";
        else color = "text-brand";
    }
    return color;
}

export function ScoreCard({
                              score,
                              label,
                              icon: Icon,
                              subtext,
                              inverse,
                              color,
                              scoreForColor,
                              scoreTooltip,
                              labelTooltip,
                              subTextTooltip,
                              href,
                              percentage,
                              'data-testid': dataTestId
                          }: {
    score: number,
    label: string,
    icon: LucideIcon,
    subtext?: string,
    inverse: boolean,
    color: boolean,
    scoreForColor: number | null,
    scoreTooltip?: string,
    labelTooltip?: string,
    subTextTooltip?: string,
    href: string,
    percentage?: boolean,
    'data-testid'?: string
}) {
    // Determine glass effect class based on score and inverse
    const getGlassEffectClass = () => {
        if (!color || scoreForColor === null) {
            return "glass-effect-neutral-strong-lit";
        }

        const value = scoreForColor || score;

        // Handle neutral scores (around 50%)
        if (value >= 45 && value <= 55) {
            return "glass-effect-neutral-strong-lit";
        }

        if (inverse) {
            // For inverse metrics (lower is better)
            if (value < 30) {
                return "glass-effect-brand-strong-lit"; // Good (green)
            }
            if (value < 60) {
                return "glass-effect-brand-alt-strong-lit"; // Medium (yellow)
            }
            return "glass-effect-brand-compliment-strong-lit"; // Bad (red)
        } else {
            // For normal metrics (higher is better)
            if (value >= 60) {
                return "glass-effect-brand-strong-lit"; // Good (green)
            }
            if (value >= 30) {
                return "glass-effect-brand-alt-strong-lit"; // Medium (yellow)
            }
            return "glass-effect-brand-compliment-strong-lit"; // Bad (red)
        }
    };

    // Get text color based on glass effect class
    const getTextColor = () => {
        return "text-white";
    };

    const glassEffectClass = getGlassEffectClass();
    const textColor = getTextColor();

    return (
        <Link href={href}>
            <Card className={cn(
                glassEffectClass,
                "hover:shadow-medium transition-all duration-300 hover:-translate-y-1 shadow-soft rounded-2xl overflow-visible"
            )} data-testid={dataTestId}>
                <CardContent className="pt-6 relative h-full min-h-24  overflow-visible">
                    <div className="absolute top-2 left-2 p-2 bg-white/20 rounded-full backdrop-blur-sm">
                        <Icon className="w-3 h-3 text-white"/>
                    </div>
                    <div className="flex flex-col items-center absolute top-4 left-12 right-12">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <div className="text-[10px] w-full lg:text-[12px] xl:text-xs font-medium text-white mb-2">
                                        {label}
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent className={glassEffectClass}>
                                    {labelTooltip || label}
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <div className={cn(
                                        "text-2xl text-center w-full font-bold",
                                        textColor
                                    )} data-testid={dataTestId ? (dataTestId.endsWith('-card') ? dataTestId.replace('-card', '-value') : `${dataTestId}-value`) : undefined}>
                                        {typeof score === 'number' ? Math.round(score) : score}{percentage && "%"}
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent className={glassEffectClass}>
                                    {scoreTooltip || 'The score'}
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    {subtext && (
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <p className="text-xs italic text-white/90 absolute bottom-2 text-center right-4 hidden md:block">
                                        {subtext}
                                    </p>
                                </TooltipTrigger>
                                <TooltipContent className="glass-effect-subtle">
                                    {subTextTooltip || subtext}
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    )}
                </CardContent>
            </Card>
        </Link>
    );
}


export function GuageCard({
                              score,
                              label,
                              icon: Icon,
                              subtext,
                              inverse,
                              color,
                              scoreForColor,
                              scoreTooltip,
                              labelTooltip,
                              subTextTooltip,
                              percentage = false
                          }: {
    score: number | null,
    label: string,
    icon?: LucideIcon,
    subtext?: string,
    inverse: boolean,
    color: boolean,
    scoreForColor?: number | null,
    percentage?: boolean,
    scoreTooltip?: string,
    labelTooltip?: string,
    subTextTooltip?: string
}) {
    const windowSize = useWindowSize();

    // Determine glass effect class based on score and inverse
    const getGlassEffectClass = () => {
        if (!color || score === null) {
            return "glass-effect-neutral-strong-lit";
        }

        const value = scoreForColor || score;

        // Handle neutral scores (around 50%)
        if (value >= 45 && value <= 55) {
            return "glass-effect-neutral-strong-lit";
        }

        if (inverse) {
            // For inverse metrics (lower is better)
            if (value < 30) {
                return "glass-effect-brand-strong-lit"; // Good (green)
            }
            if (value < 60) {
                return "glass-effect-brand-alt-strong-lit"; // Medium (yellow)
            }
            return "glass-effect-brand-compliment-strong-lit"; // Bad (red)
        } else {
            // For normal metrics (higher is better)
            if (value >= 60) {
                return "glass-effect-brand-strong-lit"; // Good (green)
            }
            if (value >= 30) {
                return "glass-effect-brand-alt-strong-lit"; // Medium (yellow)
            }
            return "glass-effect-brand-compliment-strong-lit"; // Bad (red)
        }
    };

    // Determine gradient color for the top border
    const getGradientColor = () => {
        if (!color || score === null) return "bg-brand-gradient";

        const value = scoreForColor || score;

        // Handle neutral scores (around 50%)
        if (value >= 45 && value <= 55) return "bg-gradient-to-r from-slate-600 to-slate-700";

        if (inverse) {
            // For inverse metrics (lower is better)
            if (value < 30) return "bg-brand-gradient"; // Good (green)
            if (value < 60) return "bg-brand-gradient-accent"; // Medium (yellow)
            return "bg-brand-gradient-compliment"; // Bad (red)
        } else {
            // For normal metrics (higher is better)
            if (value >= 60) return "bg-brand-gradient"; // Good (green)
            if (value >= 30) return "bg-brand-gradient-accent"; // Medium (yellow)
            return "bg-brand-gradient-compliment"; // Bad (red)
        }
    };

    const gradientColor = getGradientColor();
    const glassEffectClass = getGlassEffectClass();

    return (
        <Card className={cn(
            glassEffectClass,
            "rounded-2xl shadow-medium overflow-hidden"
        )}>
            <CardContent className="pt-6 relative min-h-[320px]">
                <div className={cn(
                    "absolute top-0 left-0 right-0 h-1",
                    gradientColor
                )}></div>

                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <div className="text-sm font-medium text-center  absolute top-5 left-2 right-2 heading-5">
                                {label}
                            </div>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-72 glass-effect-subtle">
                            {labelTooltip || label}
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>

                {subtext && (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div className="text-sm text-center absolute top-14 left-2 right-2">
                                    {subtext}
                                </div>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-72 glass-effect-subtle">
                                {subTextTooltip ? subTextTooltip : subtext}
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                )}
                {Icon && (
                    <div className="p-2 bg-primary/10 rounded-full absolute top-2 right-2 md:hidden lg:block">
                        <Icon className="w-6 h-6 text-primary"/>
                    </div>
                )}

                <div className="absolute top-[80px] left-2 right-2">
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div className="my-4">
                                    <Gauge
                                        value={score}
                                        size={(windowSize.width > 1024 || windowSize.width < 768) ? "xl" : "lg"}
                                        showValue={true}
                                        percentage={percentage}
                                        color={getRatingColor(scoreForColor || score, inverse)}
                                        label={getRatingText(scoreForColor || score, inverse)}
                                    />
                                </div>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-72 glass-effect-subtle">
                                {scoreTooltip || 'The score'}
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </CardContent>
        </Card>
    );
}


export function getRatingText(rating: number | null, inverse = false): string {
    if (rating === null) return "N/A";
    if (inverse) rating = 100 - rating;

    if (rating >= 80) return "Excellent";
    if (rating >= 60) return "Good";
    if (rating >= 40) return "Average";
    if (rating >= 30) return "Poor";
    if (rating >= 20) return "Very Poor";
    return "Terrible";
}
