# Charts Components - ESG Data Visualization Suite

A comprehensive collection of specialized React components for visualizing ESG (Environmental, Social, Governance) metrics, scores, and data throughout the ekoIntelligence customer application. Built with modern web technologies including React, TypeScript, Framer Motion, and Chart.js integration, these components provide elegant animated visualizations optimized for the platform's glass-morphism design system.

## Overview

The charts directory contains sophisticated visualization components designed specifically for ESG analytics dashboards, featuring animated gauges, interactive score cards, and responsive data displays. These components serve as the primary visual interface for presenting complex ESG metrics in an intuitive, accessible format that supports decision-making for investment professionals and ESG analysts.

The components integrate seamlessly with the broader ekoIntelligence system, consuming data from Supabase real-time subscriptions and presenting it through carefully crafted animations and responsive layouts that work across desktop and mobile devices.

## Specification

### Component Architecture

**Gauge Component (`gauge.tsx`)**
- **Purpose**: Animated circular progress visualization for ESG scores and metrics
- **Animation System**: Framer Motion-powered viewport detection and synchronized animations
- **Size Variants**: Four predefined sizes (sm: 36px, md: 72px, lg: 180px, xl: 220px)
- **Customization**: Color theming, percentage display, label support, accessibility features
- **Performance**: Viewport-triggered animations with proper cleanup and memory management

**Score Cards Components (`score-cards.tsx`)**
- **ScoreCard**: Interactive clickable cards with navigation links and glass-morphism styling
- **GuageCard**: Gauge-integrated cards with responsive sizing and comprehensive tooltip system
- **Color Logic**: Dynamic theming based on score values with inverse metric support
- **Glass Effects**: Six distinct glass-morphism classes for different score ranges and contexts

**Utility Functions**
- **getRatingColor**: Converts numeric scores to CSS color classes with inverse logic support
- **getRatingText**: Provides human-readable score interpretations ("Excellent", "Good", etc.)

### Data Integration Requirements

**Score Data Structure**
```typescript
interface ScoreData {
  score: number | null;          // Primary numeric value (0-100 scale)
  inverse: boolean;              // Inverts color logic for risk metrics
  scoreForColor?: number | null; // Alternative score for color determination
}
```

**Navigation Integration**
```typescript
interface NavigationProps {
  href: string;                  // Next.js Link target URL
  'data-testid'?: string;       // Playwright automation support
}
```

## Key Components

### Gauge Component
**Location**: `gauge.tsx`
**Purpose**: Animated circular progress visualization
**Key Features**:
- SVG-based rendering with mathematical precision (2πr circumference calculation)
- Synchronized circle progress and value counter animations
- Four responsive size variants with automatic typography scaling
- Viewport detection for performance-optimized animations
- Comprehensive accessibility support with test ID architecture

**Props Interface**:
- `value`: number | null - The numeric value to display
- `size`: "sm" | "md" | "lg" | "xl" - Predefined size variants
- `showValue`: boolean - Controls numeric value visibility
- `color/bgcolor`: string - CSS classes for theming
- `label`: string - Optional descriptive text
- `percentage`: boolean - Adds percentage symbol
- `data-testid`: string - Testing automation support

### ScoreCard Component
**Location**: `score-cards.tsx` (ScoreCard function)
**Purpose**: Interactive score display with navigation and glass-morphism styling
**Key Features**:
- Glass-morphism design with dynamic color theming
- Next.js Link integration for navigation
- Comprehensive tooltip system with Radix UI
- Responsive typography and layout adaptation
- Icon integration with Lucide React

### GuageCard Component
**Location**: `score-cards.tsx` (GuageCard function) 
**Purpose**: Gauge-integrated cards with enhanced visualization
**Key Features**:
- Embedded Gauge component with responsive sizing
- Window size-aware gauge dimensions
- Gradient color system for visual hierarchy
- Comprehensive tooltip coverage for all elements
- Responsive behavior with useWindowSize hook

### Test Suites
**Location**: `gauge.test.tsx`, `score-cards.test.tsx`
**Purpose**: Comprehensive test coverage with Vitest and React Testing Library
**Coverage Areas**:
- Component rendering with various prop combinations
- Animation system verification with mocked Framer Motion
- Size variant validation and SVG property testing
- Color theming and glass effect class application
- Accessibility structure and test ID functionality
- Edge case handling (null values, extreme scores)
- Integration testing between components

## Dependencies

### Core React Ecosystem
- **React 18+**: Modern hooks API (useEffect, useRef, useState)
- **TypeScript**: Full type safety with comprehensive interface definitions
- **Next.js 15**: App Router integration for navigation and Link components

### Animation & Interaction
- **Framer Motion**: Advanced animations with useInView and motion components
  - Viewport detection for performance optimization
  - Synchronized circle progress and counter animations
  - Smooth transitions with configurable easing curves

### UI Framework
- **Tailwind CSS**: Responsive design system with glass-morphism utilities
- **Radix UI Primitives**: Accessible tooltip system with TooltipProvider
- **Lucide React**: Icon system for visual indicators and navigation cues

### Platform Integration
- **@react-hookz/web**: Window size detection for responsive gauge sizing
- **@utils/lib/utils**: Utility functions including cn() for class name management
- **Supabase**: Real-time data updates (indirect dependency through parent components)

### Testing Infrastructure
- **Vitest**: Modern test runner with comprehensive mocking capabilities
- **React Testing Library**: Component testing with accessibility-focused queries
- **Playwright**: End-to-end testing automation with data-testid support

## Usage Examples

### Basic ESG Score Display
```tsx
<Gauge 
  value={75} 
  size="md" 
  label="ESG Score" 
  percentage={true}
  data-testid="entity-score-gauge"
/>
```

### Interactive Dashboard Card
```tsx
<ScoreCard
  score={85}
  label="Environmental Score"
  icon={Leaf}
  subtext="Last updated today"
  inverse={false}
  color={true}
  scoreForColor={85}
  href="/entity/eko-123/environmental"
  percentage={true}
  data-testid="env-score-card"
/>
```

### Advanced Gauge Card with Tooltips
```tsx
<GuageCard
  score={42}
  label="Risk Assessment"
  icon={AlertTriangle}
  subtext="Medium risk level"
  inverse={true}
  color={true}
  percentage={true}
  scoreTooltip="Overall risk score based on ESG factors"
  labelTooltip="Comprehensive risk assessment methodology"
/>
```

## Architecture Notes

### Animation Performance Strategy
The components implement a sophisticated animation system designed for optimal performance:

```mermaid
graph TB
    A[Component Mount] --> B[useInView Hook]
    B --> C{Element in Viewport?}
    C -->|No| D[No Animation]
    C -->|Yes| E[Trigger Animations]
    E --> F[SVG Circle Animation]
    E --> G[Counter Animation]
    F --> H[Stroke Dash Offset]
    G --> I[Interval-based Counting]
    H --> J[Animation Complete]
    I --> J
    J --> K[Cleanup Timers]
```

### Color System Logic
Dynamic color assignment based on score values and metric types:

```mermaid
flowchart TD
    A[Score Input] --> B{Color Enabled?}
    B -->|No| C[Neutral Glass Effect]
    B -->|Yes| D{Inverse Metric?}
    D -->|No| E[Normal Logic<br/>Higher = Better]
    D -->|Yes| F[Inverse Logic<br/>Lower = Better]
    E --> G{Score >= 60?}
    F --> H{Score < 30?}
    G -->|Yes| I[Brand Green]
    G -->|No| J{Score >= 30?}
    H -->|Yes| I
    H -->|No| K{Score < 60?}
    J -->|Yes| L[Brand Accent Yellow]
    J -->|No| M[Brand Compliment Red]
    K -->|Yes| L
    K -->|No| M
```

### Data Flow Architecture
Integration with the broader ESG analysis system:

```mermaid
graph LR
    A[Analytics Backend<br/>Python] --> B[xfer_score Table<br/>PostgreSQL]
    B --> C[Supabase Real-time<br/>Subscriptions]
    C --> D[Dashboard Page<br/>React]
    D --> E[Score Cards<br/>Components]
    D --> F[Gauge Components<br/>Visualization]
    E --> G[Glass-morphism<br/>Display]
    F --> G
    G --> H[User Interaction<br/>Navigation]
```

## Known Issues

Based on Linear issue analysis and code review:

### Historical Chart Issues (Resolved)
- **EKO-265**: "Charts are Broken and not very good" - Major charts overhaul completed
- **EKO-299**: "Switch off Charts until Fixed" - Charts were temporarily disabled
- **EKO-293**: "Chart not loading" - Specific loading issues resolved
- **EKO-300**: "Charts need reworking" - Current implementation represents the rework

### Current State
✅ **Components are functional** - Both Gauge and ScoreCard components have comprehensive test suites passing
✅ **Animation system working** - Framer Motion integration properly implemented
✅ **Responsive design complete** - Multiple size variants and window-aware sizing
✅ **Glass-morphism integration** - Full design system compliance
✅ **Accessibility support** - Comprehensive test ID architecture for automation

### Potential Improvements
- **Chart.js Integration**: Current components are custom SVG-based; future Chart.js integration planned
- **Advanced Chart Types**: Bar charts, line charts, and complex visualizations not yet implemented
- **Real-time Updates**: Direct Supabase subscription integration could be added to components
- **Animation Customization**: More granular animation control options could be beneficial

## Future Work

### Near-term Enhancements (Based on Linear Issues)
1. **Chart.js Integration (EKO-300)**: Implement Chart.js-based charts for complex data visualization
2. **Advanced Chart Types**: Bar charts, line charts, area charts for trend visualization
3. **Real-time Data Updates**: Direct Supabase integration for live updating charts
4. **Export Functionality**: PDF/image export capabilities for chart visualizations

### Long-term Strategic Goals
1. **Interactive Dashboard Builder**: Drag-and-drop chart creation for custom dashboards
2. **Advanced Analytics Visualization**: Time series analysis, correlation charts, benchmarking
3. **Mobile-first Chart Experience**: Enhanced mobile interactions and touch gestures
4. **Accessibility Enhancement**: Screen reader optimization and keyboard navigation
5. **Performance Optimization**: Virtual scrolling for large datasets and chart collections

### Technical Debt Considerations
- **Chart.js Migration**: Current custom SVG implementation vs. Chart.js ecosystem
- **Animation Performance**: Optimize for low-end devices and complex datasets
- **Component Splitting**: Consider splitting large components for better maintainability
- **CSS-in-JS Migration**: Evaluate styled-components or emotion for component-scoped styling

## Troubleshooting

### Common Issues

**Gauge Not Animating**
- Verify Framer Motion is properly installed and configured
- Check that `useInView` hook is detecting element visibility
- Ensure component is not rendered with `value={null}`

**Score Cards Not Displaying Colors**
- Confirm `color={true}` prop is set on component
- Verify score values are in expected 0-100 range
- Check that glass-morphism CSS classes are available

**Test Failures**
- Mock Framer Motion components in test environment
- Use `vi.useFakeTimers()` for animation testing
- Verify test IDs are consistently applied

**Responsive Issues**
- Check window size detection with useWindowSize hook
- Verify Tailwind CSS breakpoint classes are correct
- Test viewport meta tag configuration

### Performance Debugging

**Animation Performance**
```typescript
// Enable animation debugging
const ref = useRef(null);
const isInView = useInView(ref, { 
  once: true, 
  amount: 0.5,
  debug: true // Add for debugging
});
```

**Memory Leaks**
- Verify timer cleanup in useEffect return functions
- Check for proper component unmounting
- Monitor browser DevTools Performance tab

## FAQ

### User-Centric Questions

**Q: Why do some gauges show different colors for the same score?**
A: The color system supports both normal metrics (higher is better) and inverse metrics (lower is better). Risk scores use inverse coloring where lower values appear green (good) and higher values appear red (concerning).

**Q: Can I customize the gauge sizes?**
A: Yes, four predefined sizes are available: "sm" (36px), "md" (72px), "lg" (180px), and "xl" (220px). The components automatically adjust typography and spacing for each size.

**Q: Why don't animations play on every page load?**
A: Animations are viewport-triggered for performance optimization. They only play when the component enters the user's view, and by default, only play once per session using the `once: true` setting.

**Q: How do tooltips work on mobile devices?**
A: Tooltips use Radix UI primitives which automatically adapt to touch interactions, showing on tap and hiding when tapping elsewhere. The glass-morphism styling ensures readability across devices.

**Q: Can I export or screenshot the charts?**
A: The components render as DOM elements and can be captured using standard screenshot tools. Future versions may include built-in export functionality.

## References

### Documentation Links
- [Framer Motion Animation Library](https://www.framer.com/motion/) - Animation system powering chart transitions
- [SVG Stroke Dash Array](https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/stroke-dasharray) - Technical reference for circular progress
- [Tailwind CSS Responsive Design](https://tailwindcss.com/docs/responsive-design) - Responsive design principles
- [Radix UI Tooltip Documentation](https://www.radix-ui.com/primitives/docs/components/tooltip) - Tooltip system implementation
- [Chart.js Documentation](https://www.chartjs.org/docs/latest/) - Future integration reference

### Related Code Files
- [Utility Functions](../../lib/utils.ts) - Class name management and utility functions
- [Card UI Components](../ui/card.tsx) - Base card components with glass-morphism styling
- [Chart UI Components](../ui/chart.tsx) - Recharts-based chart system for complex visualizations
- [Glass Card Components](../ui/glass-card.tsx) - Advanced glass-morphism card implementations
- [Dashboard Page](../../app/customer/dashboard/page.tsx) - Primary usage context and integration
- [Score Converter Utilities](../../utils/score-converter.ts) - Score processing and conversion functions

### Related URLs
- [Next.js Link Component](https://nextjs.org/docs/app/api-reference/components/link) - Navigation system integration
- [Lucide React Icons](https://lucide.dev/) - Icon system used throughout components
- [React Hookz Web](https://github.com/react-hookz/web) - Hook library for window size detection
- [Supabase Real-time](https://supabase.com/docs/guides/realtime) - Data synchronization system

### README Files
- [Customer App README](../../README.md) - Application overview and development setup
- [UI Components README](../ui/README.md) - Base UI component documentation
- [Graph Components README](../graph/README.md) - Additional visualization components

---

## Changelog

### 2025-07-31 - Initial Documentation Creation
- Created comprehensive README.md for charts component directory
- Documented Gauge and ScoreCard component specifications
- Added architecture diagrams for animation and color systems
- Included troubleshooting guide and FAQ section
- Established future work roadmap based on Linear issue analysis
- Documented dependencies and integration patterns

---

(c) All rights reserved ekoIntelligence 2025