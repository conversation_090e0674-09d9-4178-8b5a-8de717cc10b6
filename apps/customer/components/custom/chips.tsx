/**
 * # Interactive Chips Input Component for Tag Selection and Management
 *
 * This React component provides a sophisticated chips/tags input interface for selecting and managing
 * multiple values within forms and data entry contexts in the EkoIntelligence ESG (Environmental, Social,
 * Governance) analysis platform. The component combines real-time search functionality, autocomplete suggestions,
 * and visual chip management to create an intuitive user experience for selecting entities, keywords,
 * categories, or any multi-value input scenarios throughout the customer application.
 *
 * ## Core Functionality
 * - **Multi-Value Input Management**: Add, remove, and manage multiple selected values as visual chips/tags
 * - **Real-Time Search & Filtering**: Type-ahead search with case-insensitive filtering of available options
 * - **Autocomplete Suggestions**: Dynamic dropdown display of filtered options with click-to-select functionality
 * - **Duplicate Prevention**: Automatic prevention of duplicate chip selection with user-friendly handling
 * - **Maximum Limit Enforcement**: Optional maximum chip count with toast notifications for limit violations
 * - **Flexible Options Mode**: Support for both predefined option lists and free-form text input modes
 * - **Keyboard Navigation**: Full keyboard support with Enter key for chip addition and intuitive interaction
 * - **Visual Chip Management**: Interactive chips with remove buttons and modern glass-morphism styling
 *
 * ## ESG Platform Integration
 * **Form Data Management**: Designed for ESG analysis and reporting form integration:
 * - **Entity Selection**: Perfect for selecting multiple companies, organizations, or ESG entities for analysis
 * - **Keyword Management**: Ideal for managing ESG-related keywords, topics, and classification tags
 * - **Category Filtering**: Excellent for selecting multiple analysis categories, sectors, or report classifications
 * - **Filter Management**: Seamlessly integrates with dashboard filters and search functionality
 * - **Report Configuration**: Enables multi-selection for report parameters and analysis scope definition
 * - **Data Entry Forms**: Enhances user experience in collaborative document editing and report generation
 *
 * ## Props Interface
 * **Comprehensive Configuration**: Flexible props for various use cases across the platform:
 * - **options**: string[] | null - Available options for selection (null enables free-form input)
 * - **initial**: string[] - Initially selected chips/values to display on component mount
 * - **onChange**: (values: string[]) => any - Callback function triggered when chip selection changes
 * - **max**: number (optional) - Maximum number of chips allowed (triggers toast warning when exceeded)
 *
 * ## Component Architecture
 * **State Management**: Uses React hooks for comprehensive state management:
 * - **selectedChips**: Manages currently selected chip values with React.useState
 * - **inputValue**: Tracks current input field text for search and filtering functionality
 * - **suggestions**: Dynamic filtered options list based on current input and available options
 * - **Real-Time Updates**: Immediate UI updates and parent component notification via onChange callback
 *
 * ## User Interface Design
 * **Modern Glass-Morphism Styling**: Consistent with EkoIntelligence design system:
 * - **Chip Design**: Rounded zinc-colored chips with hover states and remove button interactions
 * - **Input Integration**: Seamless text input within chip container with flexible growth behavior
 * - **Dropdown Styling**: Positioned absolute dropdown with shadow, rounded corners, and hover states
 * - **Dark Mode Support**: Full dark mode compatibility with appropriate color schemes
 * - **Responsive Layout**: Flexible layout that adapts to various container sizes and screen dimensions
 * - **Accessibility**: Proper focus management, keyboard navigation, and screen reader compatibility
 *
 * ## Input Modes & Validation
 * **Dual Input Mode Support**: Handles both constrained and free-form input scenarios:
 *
 * ### Predefined Options Mode (options: string[])
 * - **Validation**: Only allows selection of values present in the options array
 * - **Autocomplete**: Shows filtered suggestions based on user input with case-insensitive matching
 * - **Validation Feedback**: Silently rejects invalid selections without error messages
 *
 * ### Free-Form Input Mode (options: null)
 * - **Unrestricted Input**: Allows any text input as chip values for maximum flexibility
 * - **Custom Values**: Perfect for user-generated tags, custom categories, or open-ended selections
 * - **Input Sanitization**: Automatically trims whitespace and prevents empty value submissions
 *
 * ## User Interaction Patterns
 * **Intuitive Selection Workflow**: Multiple interaction methods for optimal user experience:
 * - **Keyboard Entry**: Type and press Enter to add chips with real-time input validation
 * - **Click Selection**: Click on suggestion dropdown items for quick selection and addition
 * - **Chip Removal**: Click × button on individual chips to remove specific selections
 * - **Input Filtering**: Dynamic filtering as user types with immediate visual feedback
 * - **Limit Handling**: Toast notifications when maximum chip limit is reached
 *
 * ## Integration with Sonner Toast System
 * **User Feedback**: Leverages Sonner toast library for user notifications:
 * - **Limit Warnings**: Toast.warning displays when maximum chip limit is exceeded
 * - **User Guidance**: Provides clear feedback about interaction limits and constraints
 * - **Non-Intrusive**: Toast notifications don't interrupt workflow or require dismissal
 *
 * ## Technical Implementation
 * **React Hooks Integration**: Modern React patterns with functional component architecture:
 * - **useState**: Multiple state variables for input, chips, and suggestions management
 * - **Event Handling**: Comprehensive keyboard and mouse event handling for all interactions
 * - **Effect Management**: Real-time suggestion filtering and dropdown visibility management
 * - **Callback Integration**: Immediate parent component notification via onChange prop
 * - **Performance Optimization**: Efficient re-rendering with proper dependency management
 *
 * ## Styling & Theme Integration
 * **Tailwind CSS Integration**: Consistent with platform design system:
 * - **Color Scheme**: Zinc color palette with proper contrast and accessibility
 * - **Border Radius**: Heavily rounded design (rounded-full chips) matching platform aesthetics
 * - **Spacing**: Consistent padding and margin using Tailwind spacing scale
 * - **Typography**: Appropriate text sizing and font weights for readability
 * - **Interactive States**: Hover, focus, and active states for all interactive elements
 * - **Z-Index Management**: Proper layering for dropdown positioning and visibility
 *
 * ## Error Handling & Edge Cases
 * **Robust Input Validation**: Comprehensive handling of edge cases and error scenarios:
 * - **Empty Input**: Prevents submission of empty or whitespace-only values
 * - **Duplicate Prevention**: Automatic detection and prevention of duplicate chip selection
 * - **Option Validation**: When options array provided, validates all additions against allowed values
 * - **Limit Enforcement**: Graceful handling of maximum chip limit with user notification
 * - **Null Options**: Safe handling of null options array for free-form input scenarios
 * - **Input Sanitization**: Automatic trimming of whitespace from input values
 *
 * ## Testing & Quality Assurance
 * **Comprehensive Test Coverage**: Extensive test suite covering all functionality:
 * - **Unit Tests**: Complete test coverage with Vitest and React Testing Library
 * - **Interaction Testing**: User event simulation for keyboard and mouse interactions
 * - **Edge Case Testing**: Validation of error scenarios, limits, and boundary conditions
 * - **Mock Integration**: Proper mocking of Sonner toast notifications for isolated testing
 * - **Accessibility Testing**: Screen reader compatibility and keyboard navigation validation
 *
 * ## Use Cases in ESG Platform
 * **Common Implementation Scenarios**: Typical usage patterns throughout the application:
 * - **Entity Analysis Forms**: Select multiple companies for comparative ESG analysis
 * - **Report Filter Configuration**: Choose analysis categories, sectors, or time periods
 * - **Document Tagging**: Add keywords and categories to collaborative documents
 * - **Search Interface**: Multi-value search filters for dashboard and report interfaces
 * - **User Preference Settings**: Manage notification preferences, interests, or focus areas
 * - **Admin Configuration**: Set up analysis parameters, user roles, or system settings
 *
 * @see https://sonner.emilkowal.ski/ Sonner Toast Library Documentation
 * @see https://react.dev/reference/react/useState React useState Hook Reference
 * @see https://tailwindcss.com/docs Tailwind CSS Documentation
 * @see {@link ../../ui/input.tsx} Base Input Component
 * <AUTHOR>
 * @updated 2025-07-24
 * @description Interactive chips input component for multi-value selection with autocomplete, validation, and modern UI design
 * @example ```tsx
 // Basic usage with predefined options
 <ChipsInput
 options={['Environmental', 'Social', 'Governance', 'Sustainability']}
 initial={['Environmental']}
 onChange={(values) => console.log('Selected:', values)}
 />

 // Free-form input mode with maximum limit
 <ChipsInput
 options={null}
 initial={[]}
 onChange={(values) => setSelectedTags(values)}
 max={5}
 />

 // Entity selection for ESG analysis
 <ChipsInput
 options={entityNames}
 initial={selectedEntities}
 onChange={(entities) => updateAnalysisScope(entities)}
 max={10}
 />
 ```
 * @docgen doc-by-claude
 * @copyright (c) All rights reserved ekoIntelligence 2025
 */

import React, { useState } from 'react'
import { toast } from 'sonner'
// import PropTypes from "prop-types";

const ChipsInput = (props: {
    options: string[] | null,
    initial: string[],
    onChange: (values: string[]) => any,
    max?: number
}) => {
    const [selectedChips, setSelectedChips] = useState<string[]>(props.initial);
    const [inputValue, setInputValue] = useState<string>("");
    const [suggestions, setSuggestions] = useState<string[] | null>([]);

    console.log("Selected chips", selectedChips);
    console.log("Input value", inputValue);
    console.log("Suggestions", suggestions);
    console.log("Options", props.options);
    console.log("Initial", props.initial);

    const handleInputChange = (e: any) => {
        const value = e.target.value;
        setInputValue(value);

        if (value) {
            const filteredSuggestions = props.options?.filter(
                (option) => option.toLowerCase().indexOf(value.toLowerCase()) !== -1
            ) || null;
            setSuggestions(filteredSuggestions);
        } else {
            setSuggestions(props.options);
        }

    };

    const handleKeyDown = (e: any) => {
        if (e.key === "Enter") {
            const value = inputValue.trim();
            if (value && (props.options === null || props.options.includes(value)) && !selectedChips.includes(value)) {
                if (props.max && (selectedChips.length + 1) >= props.max) {
                    toast.warning("Maximum number of options reached");
                    return;
                }
                const newSelectedChips = [...selectedChips, value];
                setSelectedChips(newSelectedChips);
                setInputValue("");
                setSuggestions([]);
                props.onChange(newSelectedChips);
            }
        }
    };

    const handleSuggestionClick = (suggestion: string) => {
        if (!selectedChips.includes(suggestion)) {
            if (props.max && (selectedChips.length + 1) >= props.max) {
                toast.warning("Maximum number of options reached");
                return;
            }
            const newSelectedChips = [...selectedChips, suggestion];
            setSelectedChips(newSelectedChips);
            setInputValue("");
            setSuggestions([]);
            props.onChange(newSelectedChips);
        }
    };

    const removeChip = (chip: string) => {
        const newSelectedChips = selectedChips.filter((c) => c !== chip);
        setSelectedChips(newSelectedChips);
        props.onChange(newSelectedChips);
    };

    return (
        <div className="w-full">
            <div className="flex flex-wrap border rounded p-2 overflow-clip">
                {selectedChips.map((chip, index) => (
                    <div
                        key={index}
                        className="flex items-center m-1 bg-zinc-200 rounded-full px-3 py-1 text-sm font-medium text-zinc-700"
                    >
                        {chip}
                        <button
                            type="button"
                            className="ml-2 text-zinc-500 hover:text-zinc-700"
                            onClick={() => removeChip(chip)}
                        >
                            &times;
                        </button>
                    </div>
                ))}
                <input
                    type="text"
                    className="flex-grow p-1 outline-none dark:bg-background dark:text-foreground text-sm"
                    placeholder="Add ..."
                    value={inputValue}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                />
            </div>
            {suggestions !== null && suggestions.length > 0 && (
                <div
                    className="border mt-2 rounded p-2 dark:bg-background dark:text-foreground shadow-md absolute z-10">
                    {suggestions.map((suggestion, index) => (
                        <div
                            key={index}
                            className="p-2 cursor-pointer hover:bg-zinc-100"
                            onClick={() => handleSuggestionClick(suggestion)}
                        >
                            {suggestion}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

// ChipsInput.propTypes = {
//     options: PropTypes.arrayOf(PropTypes.string).isRequired,
//     onChange: PropTypes.func.isRequired,
// };

export default ChipsInput;
