name: Supabase Branch Database Sync

permissions:
  contents: read
  pull-requests: write

on:
  pull_request:
    types: [ opened, synchronize, reopened, closed ]
    paths:
      - 'apps/customer/**'
      - 'packages/**'
      - 'apps/customer/supabase/**'  # Trigger on Supabase files
      - '.github/workflows/supabase-branch-sync.yml'

env:
  # Database connection (from secrets)
  CUSTOMER_DB_HOST: ${{ secrets.CUSTOMER_DB_HOST }}
  CUSTOMER_DB_USER: ${{ secrets.CUSTOMER_DB_USER }}
  CUSTOMER_DB_PASSWORD: ${{ secrets.CUSTOMER_DB_PASSWORD }}
  CUSTOMER_DB_DB: ${{ secrets.CUSTOMER_DB_DB }}
  CUSTOMER_DB_PORT: ${{ secrets.CUSTOMER_DB_PORT }}
  
  # Supabase configuration
  SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
  SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}
  
  # Live customer Supabase credentials for data sync
  LIVE_CUSTOMER_SUPABASE_URL: ${{ secrets.CUSTOMER_SUPABASE_URL }}
  LIVE_CUSTOMER_SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.CUSTOMER_SUPABASE_SERVICE_ROLE_KEY }}
  
  # GitHub environment - Use the actual PR branch name
  GITHUB_REF_NAME: "${{ github.event.pull_request.head.ref }}"

jobs:
  # Wait for Supabase's automatic branch creation, then sync data
  sync-database:
    name: Wait for Supabase Branch & Sync Data
    runs-on: ubuntu-latest
    if: github.event.action != 'closed'
    timeout-minutes: 45
    
    outputs:
      branch-id: ${{ steps.sync.outputs.branch-id }}
      branch-url: ${{ steps.sync.outputs.branch-url }}
      branch-anon-key: ${{ steps.sync.outputs.branch-anon-key }}
      studio-url: ${{ steps.sync.outputs.studio-url }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Checkout eko-tools repository
        uses: actions/checkout@v4
        with:
          repository: ekointelligence/eko-tools
          path: eko-tools
          token: ${{ secrets.EKO_TOOLS_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install Node.js dependencies for data sync
        run: |
          cd eko-tools/db-branch
          npm install pg @supabase/supabase-js

      - name: Setup PostgreSQL client
        run: |
          # Install PostgreSQL 17 client to match server version
          sudo apt-get update
          sudo apt-get install -y wget ca-certificates
          
          # Add PostgreSQL official APT repository
          wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
          echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list
          
          # Update package list and install PostgreSQL 17 client
          sudo apt-get update
          sudo apt-get install -y postgresql-client-17
          
          # Verify version
          psql --version
          pg_dump --version

      - name: Install Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Authenticate with Supabase
        run: |
          echo "Authenticating with Supabase..."
          supabase login --token "$SUPABASE_ACCESS_TOKEN"

      # Wait for Supabase's GitHub integration to create the preview branch
      - name: Wait for Supabase Preview Branch
        uses: fountainhead/action-wait-for-check@v1.2.0
        id: wait-for-branch
        with:
          checkName: Supabase Preview
          ref: ${{ github.event.pull_request.head.sha || github.sha }}
          token: ${{ secrets.EKO_TOOLS_TOKEN }}
          timeoutSeconds: 1800  # 30 minutes max wait

      - name: Debug eko-tools script
        if: steps.wait-for-branch.outputs.conclusion == 'success'
        run: |
          echo "Checking eko-tools script..."
          ls -la eko-tools/bin/supabase-branch.sh
          echo "Script exists, proceeding with sync"

      - name: Sync production data to branch
        id: sync
        if: steps.wait-for-branch.outputs.conclusion == 'success'
        run: |
          set -euxo pipefail
          echo "Supabase branch is ready, starting data sync for PR #${{ github.event.number }}"
          
          # Override GITHUB_REF_NAME for the script to use the correct branch name
          GITHUB_REF_NAME="${{ github.event.pull_request.head.ref }}" ./eko-tools/bin/supabase-branch.sh --action --debug 

          # Extract them for job outputs
          if [[ -f "$GITHUB_ENV" ]]; then
            branch_id=$(grep "SUPABASE_BRANCH_ID=" "$GITHUB_ENV" | cut -d'=' -f2)
            branch_url=$(grep "BRANCH_SUPABASE_URL=" "$GITHUB_ENV" | cut -d'=' -f2)
            branch_anon_key=$(grep "BRANCH_SUPABASE_ANON_KEY=" "$GITHUB_ENV" | cut -d'=' -f2)
          
            echo "branch-id=$branch_id" >> $GITHUB_OUTPUT
            echo "branch-url=$branch_url" >> $GITHUB_OUTPUT  
            echo "branch-anon-key=$branch_anon_key" >> $GITHUB_OUTPUT
          fi
        
        env:
          # Additional environment variables for the sync script
          PR_NUMBER: ${{ github.event.number }}
          PR_TITLE: ${{ github.event.pull_request.title }}

      - name: Comment on PR with branch info
        uses: actions/github-script@v7
        if: success()
        with:
          script: |
            const branchId = '${{ steps.sync.outputs.branch-id }}';
            const branchUrl = '${{ steps.sync.outputs.branch-url }}';
            const studioUrl = '${{ steps.sync.outputs.studio-url }}';
            const syncOutput = `${{ steps.sync.outputs.SYNC_OUTPUT }}`;
            
            const comment = `## 🚀 Database Branch Created
            
            Your PR now has a dedicated Supabase branch with a subset of production data for E2E testing.
            
            **Branch Details:**
            - **Branch ID:** \`${branchId}\`
            - **Database URL:** \`${branchUrl}\`
            - **Status:** Ready for testing
            
            **What's included:**
            - ✅ Anonymized user profiles and organizations
            - ✅ Document management data
            - ✅ Analysis results and flags
            - ✅ System configuration data
            
            **Next Steps:**
            1. Your E2E tests will automatically use this branch
            2. The branch will be cleaned up when the PR is closed
            3. Any new commits will update the branch data
            
            _Note: This is only @example.com and @ekointelligence.com users to avoid privacy issues._
            
            Comment by supabase-branch-sync.yml
            `;
            
            // Find existing comment with the same header
            const { data: comments } = await github.rest.issues.listComments({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
            });
            
            const existingComment = comments.find(comment => 
              comment.body.includes('Comment by supabase-branch-sync.yml')
            );
            
            if (existingComment) {
              // Update existing comment
              await github.rest.issues.updateComment({
                comment_id: existingComment.id,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            } else {
              // Create new comment
              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }

      - name: Comment on PR if sync failed
        uses: actions/github-script@v7
        if: failure()
        with:
          script: |
            const comment = `## ❌ Database Branch Sync Failed
            
            There was an error creating the database branch for this PR.
            
            **Possible causes:**
            - Supabase branch creation timeout
            - Database connection issues
            - Data migration errors
            - Anonymization process failure
            
            **Next Steps:**
            - Check the [workflow logs](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}) for details
            - Re-run the workflow if it was a temporary issue
            - Contact the development team if the issue persists
            
            E2E tests may run against an empty database until this is resolved.
            
            Comment by supabase-branch-sync.yml
            `;
            
            // Find existing comment with the same identifier
            const { data: comments } = await github.rest.issues.listComments({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
            });
            
            const existingComment = comments.find(comment => 
              comment.body.includes('Comment by supabase-branch-sync.yml')
            );
            
            if (existingComment) {
              // Update existing comment
              await github.rest.issues.updateComment({
                comment_id: existingComment.id,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            } else {
              // Create new comment
              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }

  #  # Branch cleanup job (runs when PR is closed)
  #  cleanup-database:
  #    name: Cleanup Branch Database
  #    runs-on: ubuntu-latest
  #    if: github.event.action == 'closed'
  #    timeout-minutes: 10
  #
  #    steps:
  #      - name: Checkout code
  #        uses: actions/checkout@v4
  #
  #      - name: Install Supabase CLI
  #        uses: supabase/setup-cli@v1
  #        with:
  #          version: latest
  #
  #      - name: Authenticate with Supabase
  #        run: |
  #          echo "$SUPABASE_ACCESS_TOKEN" | supabase login --token -
  #
  #      - name: Find and delete branch
  #        run: |
  #          echo "Cleaning up database branch for closed PR #${{ github.event.number }}"
  #
  #          # Create branch name that would have been used
  #          branch_name="${{ github.head_ref }}"
  #
  #          # Find branch by name
  #          branch_id=$(supabase --experimental branches list --project-ref "$SUPABASE_PROJECT_ID" | \
  #                     grep "$branch_name" | awk -F'|' '{print $1}' | tr -d ' ' || echo "")
  #
  #          if [[ -n "$branch_id" ]]; then
  #            echo "Found branch to cleanup: $branch_id"
  #            supabase --experimental branches delete "$branch_id" --project-ref "$SUPABASE_PROJECT_ID"
  #            echo "Branch cleanup completed"
  #          else
  #            echo "No branch found for cleanup (may have been already deleted)"
  #          fi
  #
  #      - name: Comment on PR about cleanup
  #        uses: actions/github-script@v7
  #        if: success()
  #        with:
  #          script: |
  #            const comment = `## 🧹 Database Branch Cleaned Up
  #
  #            The Supabase branch for this PR has been successfully deleted.
  #
  #            **Cleanup Summary:**
  #            - ✅ Branch database deleted
  #            - ✅ Resources freed up
  #            - ✅ No data retention concerns
  #
  #            Thank you for contributing! 🎉`;
  #
  #            github.rest.issues.createComment({
  #              issue_number: context.issue.number,
  #              owner: context.repo.owner,
  #              repo: context.repo.repo,
  #              body: comment
  #            });

  # Optional: Update existing customer app workflow to use the branch
  update-test-env:
    name: Update Test Environment
    runs-on: ubuntu-latest
    needs: sync-database
    if: success() && github.event.action != 'closed'
    
    steps:
      - name: Set branch environment for subsequent jobs
        run: |
          echo "NEXT_PUBLIC_SUPABASE_URL=${{ needs.sync-database.outputs.branch-url }}" >> $GITHUB_ENV
          echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ needs.sync-database.outputs.branch-anon-key }}" >> $GITHUB_ENV
          echo "Using branch database for E2E tests"
          
          # These environment variables can be used by the customer-app.yml workflow
          # if it's configured to depend on this workflow
