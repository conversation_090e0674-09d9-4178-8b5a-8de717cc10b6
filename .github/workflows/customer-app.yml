name: Customer App CI
permissions:
  checks: write
  actions: write
  contents: read

on:
  pull_request:
    paths:
      - 'apps/customer/**'
      - 'packages/**'
      - 'pnpm-lock.yaml'

jobs:
  build:
    name: Build Customer App
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: --max-old-space-size=4096
      # Fix: Use the correct Next.js environment variable names
      NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.CUSTOMER_SUPABASE_URL }}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.CUSTOMER_SUPABASE_ANON_KEY }}
      SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.CUSTOMER_SUPABASE_SERVICE_ROLE_KEY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: '9.0.0'

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: TypeScript check
        working-directory: apps/customer
        run: npx tsc --noEmit

      # Remove this step since env vars are now set at job level
      # - name: Create environment file
      #   working-directory: apps/customer
      #   run: |
      #     echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.CUSTOMER_SUPABASE_URL }}" > .env.development.local
      #     echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.CUSTOMER_SUPABASE_ANON_KEY }}" >> .env.development.local

      - name: Build customer app
        working-directory: apps/customer
        run: pnpm build

  vitest-tests:
    name: Vitest Unit Tests
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: --max-old-space-size=16384
      NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.CUSTOMER_SUPABASE_URL }}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.CUSTOMER_SUPABASE_ANON_KEY }}
      SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.CUSTOMER_SUPABASE_SERVICE_ROLE_KEY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: '9.0.0'

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Run Vitest unit tests
        working-directory: apps/customer
        run: pnpm run unit-test --reporter=verbose --reporter=junit --outputFile=test-results/junit.xml

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: vitest-results
          path: apps/customer/test-results/
          retention-days: 30

      - name: Publish Test Results
        uses: dorny/test-reporter@v1
        if: always()
        with:
          name: Vitest Results
          path: apps/customer/test-results/junit.xml
          reporter: java-junit
          fail-on-error: true

  playwright-tests:
    name: Playwright Tests
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'pull_request'
    timeout-minutes: 15
    env:
      NODE_OPTIONS: --max-old-space-size=4096
      # Use branch database if available from supabase-branch-sync workflow, fallback to production
      NEXT_PUBLIC_SUPABASE_URL: ${{ vars.BRANCH_SUPABASE_URL || secrets.CUSTOMER_SUPABASE_URL }}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ vars.BRANCH_SUPABASE_ANON_KEY || secrets.CUSTOMER_SUPABASE_ANON_KEY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: '9.0.0'  # Match the version from build job

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Install Playwright Browsers
        working-directory: apps/customer
        run: npx playwright install --with-deps

      - name: Run Playwright tests
        working-directory: apps/customer
        run: npx playwright test --workers=2 --reporter=line
        env:
          CI: true

      - name: Upload Playwright Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: apps/customer/playwright-report/
          retention-days: 30

      - name: Upload Test Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-test-results
          path: apps/customer/test-results/
          retention-days: 30
