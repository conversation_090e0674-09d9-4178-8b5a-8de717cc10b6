name: CMS App CI

#on:
#  push:
#    paths:
#      - 'apps/cms/**'
#      - 'packages/**'
#      - 'pnpm-lock.yaml'
#  pull_request:
#    paths:
#      - 'apps/cms/**'
#      - 'packages/**'
#      - 'pnpm-lock.yaml'

jobs:
  build:
    name: Build CMS App
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 'latest'

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: TypeScript check
        working-directory: apps/cms
        run: npx tsc --noEmit

      - name: Create environment file
        working-directory: apps/cms
        run: |
          echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.CMS_SUPABASE_URL }}" > .env.development.local
          echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.CMS_SUPABASE_ANON_KEY }}" >> .env.development.local

      - name: Build CMS app
        working-directory: apps/cms
        run: pnpm build
        env:
          NODE_ENV: production
          # Optional environment variables for CMS build
          PAYLOAD_SECRET: ${{ secrets.PAYLOAD_SECRET || 'dummy-secret-for-ci' }}
          DATABASE_URI: ${{ secrets.DATABASE_URI || 'postgresql://dummy:dummy@localhost:5432/dummy' }}
          S3_SECRET_ACCESS_KEY: ${{ secrets.S3_SECRET_ACCESS_KEY || 'dummy' }}
          S3_ACCESS_KEY_ID: ${{ secrets.S3_ACCESS_KEY_ID || 'dummy' }}
          S3_ENDPOINT: ${{ secrets.S3_ENDPOINT || 'dummy' }}
          S3_REGION: ${{ secrets.S3_REGION || 'us-east-1' }}
          S3_BUCKET: ${{ secrets.S3_BUCKET || 'dummy' }}
