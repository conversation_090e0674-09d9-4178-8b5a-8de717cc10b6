<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="Old Customer Database" read-only="true" uuid="9a106f55-9c29-45b7-8061-cd545532443d">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>*******************************************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Supabase Local (Customer DB)" uuid="6b26bf32-8f1c-42a3-86cb-************">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>******************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Live Back Office" uuid="fc96f573-4add-4df6-afa5-2f08c0340538">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <remarks>Live Backoffice DB</remarks>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>*********************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Live Customer DB" uuid="e7212b61-8cbc-4bc7-aa31-b4f814b3c561">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>*******************************************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Live Back Office RO" uuid="dca01198-d96b-4b54-9942-d2034c136416">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <remarks>Live Backoffice DB</remarks>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>*********************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>