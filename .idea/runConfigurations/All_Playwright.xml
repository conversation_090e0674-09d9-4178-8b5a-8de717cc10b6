<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="All Playwright" type="JavaScriptTestRunnerPlaywright" editBeforeRun="true">
    <config value="$PROJECT_DIR$/apps/customer/playwright.config.ts" />
    <node-interpreter value="project" />
    <playwright-package value="$PROJECT_DIR$/apps/customer/node_modules/@playwright/test" />
    <working-dir value="$PROJECT_DIR$/apps/customer" />
    <cli-options value="--ui" />
    <envs>
      <env name="PW_PORT" value="9999" />
    </envs>
    <scope-kind value="DIRECTORY" />
    <test-directory value="$PROJECT_DIR$/apps/customer/tests" />
    <method v="2" />
  </configuration>
</component>