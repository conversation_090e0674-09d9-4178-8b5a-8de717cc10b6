<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="All CustomerTests" type="JavaScriptTestRunnerVitest" editBeforeRun="true">
    <config value="$PROJECT_DIR$/apps/customer/vitest.config.mjs" />
    <node-interpreter value="project" />
    <node-options value="--max-old-space-size=6144" />
    <vitest-package value="$PROJECT_DIR$/apps/customer/node_modules/vitest" />
    <working-dir value="$PROJECT_DIR$/apps/customer" />
    <vitest-options value="--run" />
    <envs>
      <env name="NODE_OPTIONS" value="--max-old-space-size=6144" />
    </envs>
    <scope-kind value="DIRECTORY" />
    <test-directory value="$PROJECT_DIR$/apps/customer" />
    <method v="2" />
  </configuration>
</component>