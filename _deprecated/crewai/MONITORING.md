# CrewAI Monitoring System

This document describes the comprehensive monitoring system for CrewAI multi-agent workflows, including database schema,
OpenTelemetry integration, and dashboard visualization.

## Overview

The monitoring system captures detailed metrics about CrewAI agent activities, tool usage, LLM calls, and performance
data. It consists of:

1. **Database Layer**: PostgreSQL tables storing monitoring data
2. **OpenTelemetry Integration**: Span processors capturing real-time agent activities
3. **Dashboard**: Next.js SSR dashboard for visualization
4. **Agent Integration**: Automatic monitoring setup in CrewAI workflows

## Architecture

### Data Flow

```
CrewAI Agent → OpenTelemetry Spans → DatabaseSpanProcessor → PostgreSQL → Dashboard
```

### Components

- **AgentMonitoringService**: Core monitoring service for database logging
- **DatabaseSpanProcessor**: Custom OpenTelemetry span processor
- **CrewAI Dashboard**: Next.js application for monitoring visualization
- **Database Tables**: PostgreSQL schema for storing monitoring data

## Database Schema

### Agent Sessions (`agent_sessions`)

Tracks individual agent execution sessions.

```sql
CREATE TABLE agent_sessions
(
    session_id   VARCHAR(255) PRIMARY KEY,
    company_name VARCHAR(255),
    created_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Agent Execution Events (`agent_execution_events`)

Logs significant agent events and decision points.

```sql
CREATE TABLE agent_execution_events
(
    id         SERIAL PRIMARY KEY,
    session_id VARCHAR(255) REFERENCES agent_sessions (session_id),
    agent_name VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB,
    task_name  VARCHAR(255),
    tool_name  VARCHAR(255),
    run_id     INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Event Types:**

- `task_start` - Task execution begins
- `task_complete` - Task execution completes
- `decision_point` - Agent decision or reasoning
- `span_complete` - OpenTelemetry span completion
- `error` - Error or exception

### Agent Tool Usage (`agent_tool_usage`)

Captures detailed tool execution metrics.

```sql
CREATE TABLE agent_tool_usage
(
    id            SERIAL PRIMARY KEY,
    session_id    VARCHAR(255) REFERENCES agent_sessions (session_id),
    agent_name    VARCHAR(255) NOT NULL,
    tool_name     VARCHAR(255) NOT NULL,
    tool_input    JSONB,
    tool_output   JSONB,
    success       BOOLEAN      NOT NULL,
    error_message TEXT,
    duration_ms   INTEGER      NOT NULL,
    created_at    TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Captured Data:**

- Tool execution time (milliseconds)
- Input parameters and output results
- Success/failure status
- Error messages for failed executions

### Agent LLM Calls (`agent_llm_calls`)

Tracks LLM API calls with token usage and costs.

```sql
CREATE TABLE agent_llm_calls
(
    id                SERIAL PRIMARY KEY,
    session_id        VARCHAR(255) REFERENCES agent_sessions (session_id),
    agent_name        VARCHAR(255) NOT NULL,
    model_name        VARCHAR(255) NOT NULL,
    prompt_tokens     INTEGER                  DEFAULT 0,
    completion_tokens INTEGER                  DEFAULT 0,
    total_tokens      INTEGER                  DEFAULT 0,
    cost              DECIMAL(10, 6)           DEFAULT 0.00,
    duration_ms       INTEGER      NOT NULL,
    created_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Token Tracking:**

- Prompt tokens (input)
- Completion tokens (output)
- Total tokens
- Cost calculation based on model pricing

## OpenTelemetry Integration

### Setup Process

The monitoring system integrates with CrewAI's existing OpenTelemetry instrumentation:

```python
def setup_opentelemetry_monitoring(session_id: str) -> AgentMonitoringService:
    """Set up OpenTelemetry monitoring for CrewAI."""

    # Create monitoring service
    monitoring_service = AgentMonitoringService(session_id)

    # Create custom span processor
    database_processor = DatabaseSpanProcessor(monitoring_service)

    # Add to existing tracer provider (avoid "TracerProvider override" error)
    existing_tracer_provider = trace_api.get_tracer_provider()
    existing_tracer_provider.add_span_processor(database_processor)

    # Enable CrewAI instrumentation
    CrewAIInstrumentor().instrument(skip_dep_check=True)

    return monitoring_service
```

### DatabaseSpanProcessor

Custom span processor that captures CrewAI activities:

```python
class DatabaseSpanProcessor:
    def on_start(self, span, parent_context=None):
        """Called when a span starts."""
        # Log span initiation

    def on_end(self, span):
        """Called when a span ends."""
        # Extract span data
        span_name = span.name
        attributes = dict(span.attributes) if span.attributes else {}
        duration_ms = int((span.end_time - span.start_time) / 1_000_000)

        # Detect tool usage
        if tool_name := self._extract_tool_name(span_name, attributes):
            self.monitoring_service.log_tool_usage(...)

        # Detect LLM calls  
        if self._is_llm_call(span_name):
            self.monitoring_service.log_llm_call(...)

        # Log general events
        self.monitoring_service.log_event(...)
```

### Span Detection Logic

**Tool Usage Detection:**

- Attribute keys: `tool.name`, `tool`, `crewai.tool.name`
- Span name patterns: Contains "tool"
- Captures: tool name, input/output, duration, success status

**LLM Call Detection:**

- Span name patterns: "llm", "chat", "openai"
- Attributes: `llm.model`, `llm.usage.prompt_tokens`, etc.
- Captures: model, token usage, duration, cost

**Agent Identification:**

- Attribute keys: `agent.name`, `agent`, `crewai.agent.name`
- Falls back to "Unknown" if not found

## Agent Integration

### Automatic Setup

CrewAI agents automatically initialize monitoring:

```python
class PersistentWebCrawlCrew:
    def __init__(self, company_name: str, session_id: Optional[str] = None):
        self.session_id = session_id or f"crawl_{int(time.time())}"

        # Initialize OpenTelemetry monitoring
        self.monitoring_service = setup_opentelemetry_monitoring(self.session_id)

        # Create agents with standard tools (monitoring automatic)
        self.research_agent = ResearchAgent(web_tools)
        self.journalist_agent = JournalistAgent(web_tools)
```

### Session Management

Sessions are created automatically with unique IDs:

- Format: `crawl_{timestamp}`
- Stored in `agent_sessions` table
- All related data linked via `session_id`

## Dashboard Implementation

### Technology Stack

- **Framework**: Next.js 14 with Server-Side Rendering (SSR)
- **Database**: PostgreSQL connection with pooling
- **Styling**: Tailwind CSS with glass-morphism design
- **Components**: React components for metrics visualization

### Dashboard Pages

#### Main Dashboard (`/`)

- **Active Sessions**: Sessions with recent activity
- **Total Statistics**: Aggregate metrics across all sessions
- **Recent Activity**: Latest tool usage and events
- **Navigation**: Links to detailed analysis pages

#### Agent Performance (`/agents`)

- **Agent Efficiency**: Tool success rates and execution times
- **Activity Timeline**: Chronological view of agent actions
- **Performance Metrics**: Average duration, success rates
- **Tool Usage Breakdown**: Most frequently used tools

#### LLM Cost Analysis (`/costs`)

- **Cost Breakdown**: Spending by model and time period
- **Token Usage**: Prompt vs completion token metrics
- **Model Performance**: Cost per token and efficiency
- **Cost Trends**: Historical cost analysis

#### Session Details (`/session/[id]`)

- **Session Timeline**: Detailed execution flow
- **Tool Usage**: Complete tool execution log
- **LLM Calls**: Token usage and costs for session
- **Event Log**: All agent events and decisions

### Database Queries

**Active Sessions:**

```sql
SELECT COUNT(*) as count
FROM agent_sessions
WHERE updated_at > NOW() - INTERVAL '5 minutes'
```

**Tool Usage Stats:**

```sql
SELECT tool_name,
       COUNT(*)                                                   as usage_count,
       AVG(duration_ms)                                           as avg_duration,
       SUM(CASE WHEN success THEN 1 ELSE 0 END)::float / COUNT(*) as success_rate
FROM agent_tool_usage
WHERE session_id = $1
GROUP BY tool_name
```

**LLM Cost Analysis:**

```sql
SELECT model_name,
       SUM(total_tokens) as total_tokens,
       SUM(cost)         as total_cost,
       COUNT(*)          as call_count
FROM agent_llm_calls
GROUP BY model_name
```

## Monitoring Service API

### AgentMonitoringService

Core service for database logging:

```python
class AgentMonitoringService:
    def __init__(self, session_id: str):
        self.session_id = session_id

    def log_event(
            self,
            agent_name: str,
            event_type: str,
            event_data: Dict[str, Any],
            task_name: Optional[str] = None,
            tool_name: Optional[str] = None
    ):
        """Log agent execution event."""

    def log_tool_usage(
            self,
            agent_name: str,
            tool_name: str,
            tool_input: Dict[str, Any],
            tool_output: Dict[str, Any],
            success: bool,
            duration_ms: int,
            error_message: Optional[str] = None
    ):
        """Log tool usage with performance metrics."""

    def log_llm_call(
            self,
            agent_name: str,
            model_name: str,
            prompt_tokens: int,
            completion_tokens: int,
            total_tokens: int,
            cost: float,
            duration_ms: int
    ):
        """Log LLM API call with token usage."""
```

## Configuration

### Environment Variables

**Backend (Python):**

```bash
# Database connection
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=backoffice
POSTGRES_USER=username
POSTGRES_PASSWORD=password

# OpenTelemetry (optional)
OTEL_SERVICE_NAME=crewai-agent
OTEL_RESOURCE_ATTRIBUTES=service.version=1.0.0
```

**Frontend (Next.js):**

```bash
# Database connection
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=backoffice
POSTGRES_USER=username
POSTGRES_PASSWORD=password
```

### Package Dependencies

**Backend:**

```bash
pip install opentelemetry-api opentelemetry-sdk openinference-instrumentation-crewai
```

**Frontend:**

```bash
pnpm add pg @types/pg
```

## Usage Examples

### Starting Monitored Agent

```python
from eko.agent.crewai.crawl.crawler import PersistentWebCrawlCrew

# Create monitored crew
crew = PersistentWebCrawlCrew(
    company_name="Minderoo Foundation",
    max_iterations=5
)

# Run with automatic monitoring
result = crew.kickoff()
```

### Viewing Monitoring Data

1. **Start Dashboard**: `cd apps/agent-dashboard && pnpm dev`
2. **Access URL**: `http://localhost:3001`
3. **View Sessions**: Navigate to active sessions
4. **Analyze Performance**: Check agent efficiency and costs

### Querying Database Directly

```bash
# From project root
./bin/run_in_db.sh "
SELECT 
    s.session_id,
    s.company_name,
    COUNT(t.id) as tool_uses,
    COUNT(l.id) as llm_calls
FROM agent_sessions s
LEFT JOIN agent_tool_usage t ON s.session_id = t.session_id  
LEFT JOIN agent_llm_calls l ON s.session_id = l.session_id
GROUP BY s.session_id, s.company_name
ORDER BY s.created_at DESC;
"
```

## Troubleshooting

### Common Issues

**1. "Overriding of current TracerProvider is not allowed"**

- **Cause**: Attempting to create new TracerProvider when one exists
- **Solution**: Use `trace_api.get_tracer_provider()` and add span processor to existing provider

**2. No tool usage captured**

- **Cause**: Span processor not attached or wrong span detection logic
- **Solution**: Verify `existing_tracer_provider.add_span_processor()` called after CrewAI initialization

**3. Agent names showing as "Unknown"**

- **Cause**: Agent name not in expected span attributes
- **Solution**: Check span attributes for agent identification patterns

**4. Dashboard connection errors**

- **Cause**: Database connection misconfiguration
- **Solution**: Verify `POSTGRES_*` environment variables match between backend and frontend

### Debugging

**Enable Debug Logging:**

```python
import logging

logging.basicConfig(level=logging.DEBUG)

# Monitor span processor calls
logger.info(f"OpenTelemetry span ended: '{span_name}' with attributes: {attributes}")
```

**Check Database Tables:**

```sql
-- Verify session creation
SELECT *
FROM agent_sessions
ORDER BY created_at DESC
LIMIT 5;

-- Check tool usage capture
SELECT tool_name, COUNT(*)
FROM agent_tool_usage
WHERE session_id = 'crawl_xxx'
GROUP BY tool_name;

-- Verify LLM calls
SELECT model_name, SUM(total_tokens)
FROM agent_llm_calls
WHERE session_id = 'crawl_xxx'
GROUP BY model_name;
```

## Performance Considerations

### Database Optimization

**Indexes:**

```sql
CREATE INDEX idx_agent_sessions_created_at ON agent_sessions (created_at);
CREATE INDEX idx_agent_tool_usage_session_id ON agent_tool_usage (session_id);
CREATE INDEX idx_agent_llm_calls_session_id ON agent_llm_calls (session_id);
```

**Retention Policy:**

```sql
-- Clean up old monitoring data (example: 30 days)
DELETE
FROM agent_execution_events
WHERE created_at < NOW() - INTERVAL '30 days';
DELETE
FROM agent_tool_usage
WHERE created_at < NOW() - INTERVAL '30 days';
DELETE
FROM agent_llm_calls
WHERE created_at < NOW() - INTERVAL '30 days';
```

### Memory Usage

- Span processor operations are lightweight
- JSONB attributes stored efficiently
- Dashboard queries use pagination for large datasets

## Future Enhancements

### Planned Features

1. **Real-time Updates**: WebSocket connections for live dashboard updates
2. **Alerting**: Notifications for failed tasks or high costs
3. **Cost Budgets**: Spending limits and warnings
4. **Agent Comparison**: Performance benchmarking across agents
5. **Export Capabilities**: CSV/JSON export of monitoring data

### Integration Opportunities

1. **Prometheus/Grafana**: Export metrics to time-series databases
2. **Slack/Discord**: Notifications for agent completions
3. **CI/CD Pipelines**: Performance regression detection
4. **Cost Management**: Integration with cloud billing APIs

## Security Considerations

### Data Protection

- Sensitive tool inputs/outputs should be sanitized before storage
- Database connections use environment variables (not hardcoded)
- Dashboard requires proper authentication in production
- JSONB data should not contain API keys or secrets

### Access Control

- Monitor database access logs
- Implement role-based access for dashboard
- Regular security audits of monitoring data

## Support

### Documentation

- **Main Documentation**: This file (`MONITORING.md`)
- **Database Schema**: See `CREATE TABLE` statements above
- **API Reference**: See `AgentMonitoringService` class documentation

### Contact

- **Issues**: Report monitoring issues via project issue tracker
- **Development**: See `CLAUDE.md` for development guidelines
- **Database**: See `bin/run_in_db.sh` for database operations
