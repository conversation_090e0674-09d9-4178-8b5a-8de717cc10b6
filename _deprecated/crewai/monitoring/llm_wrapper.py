"""
CrewAI LLM Monitoring Wrapper for EkoIntelligence Agent Performance Analytics

This module provides transparent LLM interaction monitoring within EkoIntelligence's CrewAI-powered
multi-agent ESG research platform. The MonitoredLLM class serves as a seamless wrapper around
CrewAI's LLM instances, automatically capturing and logging comprehensive metrics for all Large
Language Model interactions without disrupting agent workflows or affecting core functionality.

## Core Purpose

The MonitoredLLM is a **transparent instrumentation layer** that enables comprehensive observability
of LLM usage across EkoIntelligence's distributed agent workflows. It captures token consumption,
cost tracking, performance metrics, and interaction patterns to support financial monitoring, 
performance optimization, and operational analytics for AI-powered ESG research operations analyzing
corporate sustainability practices, greenwashing detection, and compliance assessment.

## Architecture & Integration

### Seamless LLM Wrapping Architecture
The monitoring wrapper implements a **proxy pattern** that preserves complete LLM functionality:
- **Attribute Delegation**: All LLM properties and methods transparently delegated to wrapped instance
- **Call Interception**: LLM interactions intercepted for monitoring while preserving original behavior  
- **Performance Preservation**: Minimal monitoring overhead with microsecond-precision timing measurement
- **Error Propagation**: Complete exception transparency ensures agent error handling remains intact

### Database Integration Layer
The wrapper integrates with EkoIntelligence's monitoring infrastructure through structured event logging:
- **Event Logger Integration**: Uses `AgentEventLogger` for consistent database persistence
- **Session-Based Tracking**: All LLM calls associated with agent session identifiers for traceability
- **Agent Attribution**: Each LLM interaction linked to specific agent names for usage pattern analysis
- **Transaction Safety**: Database logging wrapped in proper transaction boundaries with error resilience

### Core Database Schema Integration

#### agent_llm_calls Table Integration
**Purpose**: Comprehensive LLM interaction logging with detailed cost and performance analytics
**Data Flow**: MonitoredLLM → AgentEventLogger → agent_llm_calls table → Analytics Dashboard

**Key Metrics Captured**:
- **Token Usage**: Prompt, completion, and total token counts for cost calculation and efficiency analysis
- **Cost Tracking**: USD cost calculation for budget management and financial optimization
- **Performance Metrics**: Response time measurement for model performance benchmarking
- **Model Attribution**: LLM model identification for provider-specific usage analysis
- **Content Analysis**: Request/response data for interaction pattern analysis (truncated for performance)

**Performance Optimization Features**:
- **B-tree indexes** on session_id, agent_name for fast agent-specific queries
- **GIN indexes** on request_data, response_data JSONB for content analysis
- **Descending timestamp index** for efficient time-series dashboard queries

### Multi-Agent Session Management
The wrapper supports comprehensive session-based tracking across distributed agent workflows:
- **Session Isolation**: Each research session (e.g., "nike_esg_analysis_2025") tracked independently
- **Agent Attribution**: LLM calls linked to specific agents (ResearchAgent, AnalysisAgent, ReportingAgent)
- **Workflow Correlation**: Session-based grouping enables cross-agent interaction analysis
- **Performance Aggregation**: Session-level metrics support comprehensive workflow optimization

## Core Components

### MonitoredLLM Class
**Purpose**: Transparent LLM wrapper providing comprehensive monitoring without behavioral changes

**Initialization Parameters**:
- `llm` (LLM): CrewAI LLM instance to wrap with monitoring capabilities
- `event_logger` (AgentEventLogger): Logger for structured database persistence
- `agent_name` (str): Agent identifier for attribution and usage pattern analysis

**Architecture Features**:
- **Complete Attribute Delegation**: All original LLM properties accessible through `__getattr__` and `__setattr__`
- **Method Preservation**: Original LLM methods remain fully functional and accessible
- **Transparent Integration**: Agents can use MonitoredLLM as drop-in replacement for standard LLM
- **Memory Efficiency**: Minimal memory overhead through efficient attribute copying and delegation

#### call() Method - Core LLM Interaction Monitoring
**Purpose**: Primary LLM interaction wrapper with comprehensive metrics capture and error handling

**Parameters**:
- `messages` (Any): LLM input messages (prompts, conversation history, system instructions)
- `**kwargs` (Any): Additional LLM parameters (temperature, max_tokens, etc.)

**Monitoring Capabilities**:
- **Precise Timing**: Microsecond-level timing using `TimedContext` for performance analysis
- **Token Extraction**: Automatic extraction of usage metrics from LLM response objects
- **Cost Calculation**: USD cost computation for financial tracking and budget management
- **Error Capture**: Comprehensive error logging with context preservation and transparent propagation
- **Content Logging**: Request/response data capture with configurable truncation for storage efficiency

**Advanced Features**:
- **Usage Metrics Extraction**: Automatic parsing of token usage from various LLM response formats
- **Error Resilience**: Monitoring failures don't disrupt agent operations - core functionality preserved
- **Content Truncation**: Automatic truncation to 500 characters prevents database storage issues
- **Exception Transparency**: All LLM exceptions propagated unchanged to maintain agent error handling

### Attribute Management System
**Purpose**: Seamless integration with existing CrewAI agent code through transparent attribute handling

#### __getattr__() Method - Transparent Attribute Access
**Purpose**: Delegate all attribute access to wrapped LLM instance
**Implementation**: Direct delegation ensures complete API compatibility
**Error Handling**: Attribute errors bubble up naturally for proper exception handling

#### __setattr__() Method - Intelligent Attribute Management  
**Purpose**: Route attribute assignments to appropriate targets (wrapper vs wrapped LLM)
**Logic**: 
- **Wrapper Attributes**: `llm`, `event_logger`, `agent_name` stored on wrapper instance
- **LLM Attributes**: All other attributes delegated to wrapped LLM for behavioral preservation
- **Initialization Safety**: Proper handling during object construction phase

### Utility Functions

#### wrap_llm_with_monitoring() Factory Function
**Purpose**: Convenient factory function for creating monitored LLM instances

**Parameters**:
- `llm` (LLM): CrewAI LLM instance to wrap
- `event_logger` (AgentEventLogger): Logger for database persistence  
- `agent_name` (str): Agent identifier for monitoring attribution

**Returns**: Configured MonitoredLLM instance ready for immediate use

## System Architecture Context

### Integration with EkoIntelligence Platform
The LLM monitoring system operates as critical infrastructure within the broader ESG analysis ecosystem:

#### Upstream Integrations
- **CrewAI Agent Framework**: Seamless integration with agent LLM configuration and usage patterns
- **Multi-Agent Orchestration**: Supports monitoring across research, analysis, reporting, and validation agents
- **ESG Workflow Processing**: Captures LLM usage during document analysis, claim verification, and insight extraction
- **Model Provider Management**: Monitors interactions across OpenAI, Anthropic, Google, and other LLM providers

#### Downstream Integrations
- **Analytics Dashboard**: Real-time LLM usage data powers `/backoffice/src/eko/dash/` monitoring interfaces
- **Cost Management**: Financial data enables budget tracking, cost optimization, and provider comparison
- **Performance Optimization**: Usage patterns support agent workflow optimization and model selection
- **Quality Assurance**: Interaction logs support agent effectiveness measurement and improvement initiatives

### Multi-Provider LLM Support
The monitoring wrapper supports comprehensive tracking across all major LLM providers:

#### OpenAI Integration
- **Model Support**: GPT-4, GPT-4-turbo, GPT-3.5-turbo, GPT-4o, GPT-4o-mini
- **Token Tracking**: Accurate prompt_tokens, completion_tokens extraction from response.usage
- **Cost Calculation**: Real-time USD cost computation using current OpenAI pricing
- **Feature Monitoring**: Function calling, tool usage, streaming response tracking

#### Anthropic Integration  
- **Model Support**: Claude-3-opus, Claude-3-sonnet, Claude-3-haiku, Claude-3.5-sonnet
- **Usage Metrics**: Token consumption tracking through Anthropic's usage response format
- **Cost Tracking**: Financial monitoring with Anthropic's pricing structure
- **Capability Monitoring**: Long context usage, reasoning performance, tool interaction tracking

#### Google Integration
- **Model Support**: Gemini-pro, Gemini-pro-vision, Gemini-2.0-flash, Gemini-2.5-pro
- **Metrics Extraction**: Token usage parsing from Google's response format
- **Cost Monitoring**: Google AI pricing integration for accurate financial tracking
- **Multimodal Tracking**: Vision, text, and code generation usage pattern analysis

#### Local/Ollama Integration
- **Model Support**: All Ollama-served models (Llama, Mistral, CodeLlama, etc.)
- **Performance Focus**: Response time and throughput monitoring for local deployments
- **Resource Tracking**: Memory and compute usage patterns for capacity planning
- **Cost Optimization**: Zero-cost local model usage tracking for provider comparison

## Advanced Features & Capabilities

### Fail-Fast Error Handling
Following EkoIntelligence's fail-fast philosophy:
- **Exception Transparency**: All LLM errors propagate unchanged to maintain agent error handling logic
- **Monitoring Resilience**: Database logging failures don't disrupt core agent functionality
- **Comprehensive Logging**: All monitoring errors logged via Loguru with full context and stack traces
- **Transaction Integrity**: Database operations maintain ACID properties with proper rollback on failures

### Performance Optimization Features
- **Minimal Overhead**: Monitoring operations optimized for microsecond-level performance impact
- **Efficient Serialization**: JSON serialization optimized for database storage and query performance
- **Memory Management**: Automatic content truncation prevents memory exhaustion from large responses
- **Connection Pooling**: Efficient database connection reuse through centralized connection management

### Scalability & Reliability
- **Concurrent Safety**: Thread-safe operations support concurrent agent execution across multiple sessions
- **Session Isolation**: Independent monitoring per research session prevents resource contention
- **Resource Management**: Automatic cleanup of database connections and monitoring resources
- **High Throughput**: Designed to handle high-frequency LLM calls during intensive ESG analysis workflows

### Security & Privacy Features
- **Data Protection**: Automatic truncation of sensitive content in logged request/response data
- **Access Control**: Database logging subject to EkoIntelligence access control policies
- **Audit Trail**: Complete monitoring data provides security audit capabilities
- **Privacy Compliance**: Data collection aligned with GDPR and privacy regulations

## Usage Patterns & Examples

### Basic LLM Monitoring Setup
```python
from crewai import LLM, Agent
from eko.agent.crewai.monitoring.llm_wrapper import wrap_llm_with_monitoring
from eko.agent.crewai.monitoring.event_logger import create_event_logger

# Create monitored LLM for ESG research agent
event_logger = create_event_logger("nike_sustainability_analysis_2025")
base_llm = LLM(model="gpt-4o", temperature=0.1)
monitored_llm = wrap_llm_with_monitoring(base_llm, event_logger, "ResearchAgent")

# Use in agent creation - transparent integration
research_agent = Agent(
    role="ESG Research Specialist", 
    goal="Analyze corporate sustainability claims for accuracy and completeness",
    backstory="Expert in corporate ESG analysis with focus on greenwashing detection",
    llm=monitored_llm,
    tools=[document_scraper, claim_analyzer, evidence_verifier]
)
```

### Multi-Agent Monitoring Configuration
```python
# Configure monitoring for different agents with different models
models = {
    "ResearchAgent": LLM(model="gpt-4o"),  # High-accuracy model for research
    "AnalysisAgent": LLM(model="claude-3-sonnet"),  # Strong reasoning for analysis  
    "ReportingAgent": LLM(model="gpt-4o-mini")  # Cost-effective for report generation
}

agents = {}
for agent_name, llm in models.items():
    monitored_llm = wrap_llm_with_monitoring(llm, event_logger, agent_name)
    agents[agent_name] = Agent(
        role=f"{agent_name} Specialist",
        llm=monitored_llm,
        tools=get_tools_for_agent(agent_name)
    )
```

### Error Handling and Resilience
```python
# Monitoring errors don't affect agent operation
try:
    result = monitored_llm.call("Analyze sustainability claims in this report...")
    # LLM call succeeds regardless of monitoring status
except LLMAPIError as e:
    # Original LLM errors propagate unchanged
    logger.error(f"LLM API failed: {e}")
    raise
except MonitoringError as e:
    # Monitoring errors logged but don't disrupt workflow
    logger.warning(f"Monitoring failed: {e}")  
    # Agent continues with core functionality intact
```

## Dependencies & Requirements

### Core Dependencies
- **CrewAI**: Primary framework for LLM and Agent classes (`from crewai import LLM`)
- **Loguru**: Advanced Python logging for error handling and debug information
- **AgentEventLogger**: Database persistence layer for monitoring data (`from .event_logger import AgentEventLogger, TimedContext`)
- **Python Typing**: Type annotations for robust code structure and IDE support

### Database Dependencies
- **PostgreSQL**: Primary database for monitoring data persistence via `agent_llm_calls` table
- **Psycopg2**: PostgreSQL adapter for database connectivity through `eko.db.get_bo_conn()`
- **JSONB Support**: PostgreSQL JSON binary format for efficient event data storage and querying

### System Requirements
- **Python 3.8+**: Modern Python with typing support, context managers, and advanced error handling
- **Memory**: Configurable based on monitoring volume and data retention requirements
- **Database Storage**: Variable based on LLM usage patterns and retention policies

## Security & Compliance Considerations

### Data Protection Measures
- **Content Truncation**: Automatic limiting of logged content to prevent exposure of excessive sensitive data
- **Database Security**: Leverages application-level database access controls and encryption
- **Audit Trail**: Complete monitoring data provides security audit and compliance capabilities
- **Privacy Controls**: Data collection aligned with GDPR, CCPA, and enterprise privacy policies

### Monitoring Data Governance
- **Retention Policies**: Event data subject to EkoIntelligence data retention and deletion policies
- **Access Control**: Monitoring data access controlled through database connection permissions
- **Encryption**: Database connections and stored data protected through enterprise encryption standards
- **Compliance Reporting**: Monitoring data supports regulatory compliance and audit requirements

## Integration with Related Components

### Monitoring System Components
- **AgentEventLogger** (`event_logger.py`): Core logging infrastructure used by this wrapper
- **AgentMonitoringService** (`agent_monitoring.py`): Higher-level service consuming wrapper-generated events
- **TimedContext** (`event_logger.py`): Precision timing utility for performance measurement

### CrewAI Framework Integration
- **LLM Class**: Core CrewAI LLM class wrapped by this monitoring system
- **Agent Class**: CrewAI agents using MonitoredLLM instances for transparent monitoring
- **Crew Orchestration**: Multi-agent workflows with comprehensive LLM usage tracking

### Database and Analytics Integration
- **Database Schema**: Integration with `agent_llm_calls` table structure and indexing strategy
- **Analytics Dashboard**: Real-time monitoring data consumption by `/backoffice/src/eko/dash/` components
- **Cost Management**: Financial tracking and optimization recommendations based on usage data

## See Also

@see https://docs.crewai.com/core-concepts/llms CrewAI LLM Documentation and Configuration Guide
@see https://loguru.readthedocs.io/en/stable/ Loguru Python Logging Library Documentation
@see https://www.postgresql.org/docs/current/datatype-json.html PostgreSQL JSONB Data Type Documentation
@see https://docs.python.org/3/reference/datamodel.html#object.__getattr__ Python Object Attribute Access Documentation
@see ./event_logger.py Core event logging infrastructure used by this wrapper
@see ./agent_monitoring.py Higher-level agent monitoring service using this wrapper
@see ../../../db/__init__.py Database connection management and pooling infrastructure
@see ../../../../tmp/db/backoffice/schemas/public/tables/agent_llm_calls.sql Database schema for LLM call monitoring

<AUTHOR>
@updated 2025-07-23
@description CrewAI LLM monitoring wrapper providing transparent instrumentation and comprehensive metrics capture for Large Language Model interactions within EkoIntelligence's multi-agent ESG research platform
@example
```python
# Complete LLM monitoring setup for ESG research workflow
from crewai import LLM, Agent
from eko.agent.crewai.monitoring.llm_wrapper import wrap_llm_with_monitoring
from eko.agent.crewai.monitoring.event_logger import create_event_logger

# Initialize session-scoped monitoring
event_logger = create_event_logger("corporate_sustainability_analysis_2025")

# Create monitored LLM with transparent integration
base_llm = LLM(model="gpt-4o", temperature=0.1)
monitored_llm = wrap_llm_with_monitoring(base_llm, event_logger, "ResearchAgent")

# Use in agent creation - no code changes required
research_agent = Agent(
    role="ESG Research Specialist",
    llm=monitored_llm,  # Drop-in replacement with monitoring
    tools=[document_analyzer, claim_verifier]
)

# All LLM interactions automatically logged with comprehensive metrics:
# - Token usage and cost tracking
# - Performance timing and optimization data
# - Error patterns and reliability metrics
# - Usage attribution and session correlation
```
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import time
from typing import Any, Dict, Optional

from crewai import LLM
from loguru import logger

from .event_logger import AgentEventLogger, TimedContext


class MonitoredLLM:
    """
    Wrapper around CrewAI LLM that logs all calls for monitoring.
    
    This class wraps LLM calls to automatically capture metrics such as
    token usage, cost, and response times for dashboard visualization.
    """
    
    def __init__(self, llm: LLM, event_logger: AgentEventLogger, agent_name: str):
        """
        Initialize the monitored LLM wrapper.
        
        Args:
            llm: The CrewAI LLM instance to wrap
            event_logger: Event logger for capturing metrics
            agent_name: Name of the agent using this LLM
        """
        self.llm = llm
        self.event_logger = event_logger
        self.agent_name = agent_name
        
        # Copy attributes from the original LLM
        for attr in dir(llm):
            if not attr.startswith('_') and not callable(getattr(llm, attr)):
                setattr(self, attr, getattr(llm, attr))
    
    def call(self, messages: Any, **kwargs) -> Any:
        """
        Wrapper around LLM call with monitoring.
        
        Args:
            messages: Messages to send to the LLM
            **kwargs: Additional arguments
            
        Returns:
            LLM response
        """
        with TimedContext() as timer:
            try:
                # Prepare request data for logging
                request_data = {
                    "messages": str(messages)[:500],  # Truncate for storage
                    "model": getattr(self.llm, 'model', 'unknown'),
                    "temperature": getattr(self.llm, 'temperature', None),
                    "kwargs": {k: str(v)[:100] for k, v in kwargs.items()}
                }
                
                # Make the actual LLM call
                response = self.llm.call(messages, **kwargs)
                
                # Extract metrics if available
                prompt_tokens = None
                completion_tokens = None
                total_tokens = None
                cost_usd = None
                
                # Try to extract token information from response
                if hasattr(response, 'usage'):
                    usage = response.usage
                    prompt_tokens = getattr(usage, 'prompt_tokens', None)
                    completion_tokens = getattr(usage, 'completion_tokens', None)
                    total_tokens = getattr(usage, 'total_tokens', None)
                
                # Prepare response data for logging
                response_data = {
                    "content": str(response)[:500],  # Truncate for storage
                    "success": True
                }
                
                # Log the LLM call
                self.event_logger.log_llm_call(
                    agent_name=self.agent_name,
                    model_name=getattr(self.llm, 'model', 'unknown'),
                    request_data=request_data,
                    response_data=response_data,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=total_tokens,
                    cost_usd=cost_usd,
                    duration_ms=timer.duration_ms
                )
                
                return response
                
            except Exception as e:
                # Log failed LLM call
                error_data = {
                    "content": f"Error: {str(e)}",
                    "success": False,
                    "error": str(e)
                }
                
                self.event_logger.log_llm_call(
                    agent_name=self.agent_name,
                    model_name=getattr(self.llm, 'model', 'unknown'),
                    request_data=request_data,
                    response_data=error_data,
                    duration_ms=timer.duration_ms
                )
                
                raise
    
    def __getattr__(self, name: str) -> Any:
        """Delegate attribute access to the wrapped LLM."""
        return getattr(self.llm, name)
    
    def __setattr__(self, name: str, value: Any) -> None:
        """Set attributes on the wrapper or delegate to the wrapped LLM."""
        if name in ['llm', 'event_logger', 'agent_name']:
            super().__setattr__(name, value)
        else:
            if hasattr(self, 'llm'):
                setattr(self.llm, name, value)
            else:
                super().__setattr__(name, value)


def wrap_llm_with_monitoring(llm: LLM, event_logger: AgentEventLogger, agent_name: str) -> MonitoredLLM:
    """
    Wrap an LLM with monitoring capabilities.
    
    Args:
        llm: The LLM to wrap
        event_logger: Event logger for capturing metrics
        agent_name: Name of the agent using this LLM
        
    Returns:
        MonitoredLLM instance
    """
    return MonitoredLLM(llm, event_logger, agent_name)