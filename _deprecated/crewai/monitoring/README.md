# CrewAI Multi-Agent Monitoring System

## Overview

The CrewAI Multi-Agent Monitoring System provides comprehensive observability capabilities for CrewAI-powered multi-agent ESG research workflows within the EkoIntelligence platform. This monitoring package captures real-time agent activities, LLM interactions, tool usage, and performance metrics, enabling detailed analysis and optimization of AI-powered ESG research workflows through centralized logging and dashboard visualization.

The system enables **complete observability** of CrewAI agent operations during ESG research tasks, corporate document analysis, and sustainability claim verification. It provides real-time insights into agent behavior, performance bottlenecks, cost tracking, and operational effectiveness for the platform's AI-powered ESG analysis workflows.

## Specification

The monitoring system shall:

1. **Event Logging**: Capture all agent execution events including task lifecycle, decisions, and errors
2. **LLM Monitoring**: Track token usage, costs, response times, and model performance across all providers
3. **Tool Instrumentation**: Monitor success/failure rates, execution times, and I/O patterns for all agent tools
4. **Session Management**: Associate activities with session IDs for trace analysis and workflow reconstruction
5. **Database Integration**: Store monitoring data in structured PostgreSQL tables with optimized indexing
6. **OpenTelemetry Support**: Provide industry-standard distributed tracing capabilities
7. **Error Resilience**: Maintain core agent functionality even if monitoring fails
8. **Performance Optimization**: Minimize monitoring overhead on agent operations

## Key Components

### Core Files

- **`__init__.py`**: Package initialization with comprehensive documentation and exports
- **`event_logger.py`**: Core event logging infrastructure with `AgentEventLogger` class
- **`llm_wrapper.py`**: LLM monitoring wrapper (`MonitoredLLM`) for transparent instrumentation
- **`agent_monitoring.py`**: High-level monitoring service and OpenTelemetry integration

### AgentEventLogger (`event_logger.py`)
**Purpose**: Centralized event logging for all agent activities with database persistence

**Core Capabilities**:
- **Event Tracking**: Logs task start/complete, decisions, errors with contextual data
- **LLM Call Monitoring**: Captures token usage, costs, response times, and model performance
- **Tool Usage Logging**: Tracks success/failure rates, execution times, and I/O data
- **Session Management**: Associates all activities with session IDs for trace analysis
- **Error Capture**: Comprehensive error logging with context for debugging

**Database Tables**:
- `agent_execution_events`: General agent events with JSONB event data
- `agent_llm_calls`: LLM-specific metrics with token counts and USD costs
- `agent_tool_usage`: Tool execution logs with detailed success/failure tracking

### MonitoredLLM (`llm_wrapper.py`)
**Purpose**: Transparent LLM wrapper that automatically captures performance metrics

**Monitoring Features**:
- **Automatic Metrics Collection**: Token counts, response times, cost calculation
- **Request/Response Logging**: Captures prompts and responses (truncated for storage)
- **Error Handling**: Logs failed LLM calls with detailed error information
- **Performance Analysis**: Duration tracking for response time optimization
- **Cost Tracking**: USD cost calculation for budget management and optimization

### AgentMonitoringService (`agent_monitoring.py`)
**Purpose**: High-level monitoring service for agent orchestration with OpenTelemetry integration

**Service Features**:
- **Multi-Agent Coordination**: Tracks interactions between multiple agents
- **Session-Based Logging**: Groups related activities under session identifiers
- **Real-Time Monitoring**: Immediate logging of agent activities for live dashboard updates
- **Performance Metrics**: Comprehensive timing and success rate tracking
- **OpenTelemetry Integration**: Industry-standard observability with custom CrewAI instrumentation

## Dependencies

### Core Dependencies
- **CrewAI Framework**: Multi-agent orchestration and tool management
- **OpenTelemetry**: Industry-standard observability and distributed tracing
- **PostgreSQL**: Database persistence via `eko.db.get_bo_conn()`
- **Loguru**: Advanced Python logging with structured output
- **Pydantic**: Type-safe data validation for monitoring models

### Optional Dependencies
- **OpenInference CrewAI**: Specialized CrewAI instrumentation (`openinference-instrumentation-crewai`)
- **OpenTelemetry SDK**: Advanced span processing and custom instrumentation
- **OpenTelemetry Exporters**: Console and custom span export capabilities

### Installation
```bash
# Core monitoring dependencies (required)
pip install crewai pydantic loguru psycopg2-binary

# OpenTelemetry integration (optional but recommended)
pip install opentelemetry-api opentelemetry-sdk
pip install openinference-instrumentation-crewai
```

## Usage Examples

### Basic Agent Monitoring Setup
```python
from eko.agent.crewai.monitoring import create_event_logger, wrap_llm_with_monitoring
from crewai import LLM, Agent

# Create event logger for session
logger = create_event_logger("nike_esg_analysis_2025")

# Wrap LLM with monitoring
base_llm = LLM(model="gpt-4o", temperature=0.1)
monitored_llm = wrap_llm_with_monitoring(base_llm, logger, "ResearchAgent")

# Create agent with monitored LLM
research_agent = Agent(
    role="ESG Research Specialist",
    goal="Analyze corporate sustainability claims for accuracy",
    backstory="Expert in corporate ESG analysis with focus on greenwashing detection",
    llm=monitored_llm,
    tools=[document_scraper, claim_analyzer]
)
```

### Tool Monitoring Integration
```python
from eko.agent.crewai.monitoring import AgentMonitoringService, wrap_tools_with_monitoring

# Initialize monitoring service
monitoring_service = AgentMonitoringService("comprehensive_esg_analysis")

# Wrap existing tools with monitoring
original_tools = [web_search_tool, pdf_analyzer_tool, insight_extractor_tool]
monitored_tools = wrap_tools_with_monitoring(original_tools, monitoring_service, "ResearchAgent")

# Create agent with monitored tools
agent = Agent(
    role="ESG Researcher",
    tools=monitored_tools,
    llm=monitored_llm
)
```

### OpenTelemetry Enterprise Setup
```python
from eko.agent.crewai.monitoring import setup_opentelemetry_monitoring

# Enable comprehensive monitoring with industry-standard observability
monitoring_service = setup_opentelemetry_monitoring("comprehensive_esg_analysis_session")

# All CrewAI agents automatically instrumented
# Complete visibility into agent workflows, tool usage, and LLM interactions
```

### Custom Event Logging
```python
# Log agent decisions with context
monitoring_service.log_decision(
    agent_name="AnalysisAgent",
    decision="flag_greenwashing_detected",
    reasoning="Claims lack supporting evidence and contradict historical data",
    context={"confidence": 0.87, "evidence_sources": 3, "contradictions": 2}
)

# Log critical errors with full context
try:
    result = analyze_esg_document(document)
except Exception as e:
    monitoring_service.log_error("AnalysisAgent", e, {
        "document_id": document.id,
        "analysis_stage": "greenwashing_detection",
        "previous_successful_analyses": 45
    })
    raise  # Maintain fail-fast behavior
```

## Architecture Notes

### System Integration
The monitoring system operates as critical infrastructure within the broader ESG analysis ecosystem:

```mermaid
graph TD
    A[CrewAI Agents] --> B[Monitoring System]
    B --> C[PostgreSQL Database]
    C --> D[Analytics Dashboard]
    
    B --> E[Event Logger]
    B --> F[LLM Wrapper]
    B --> G[Tool Monitor]
    
    E --> H[agent_execution_events]
    F --> I[agent_llm_calls]
    G --> J[agent_tool_usage]
    
    K[OpenTelemetry] --> B
    L[Cost Tracking] --> B
```

### Database Schema Architecture
```mermaid
erDiagram
    agent_execution_events {
        text session_id PK
        text agent_name
        text event_type
        jsonb event_data
        text task_name
        text tool_name
        integer run_id
        timestamp event_timestamp
        timestamp created_at
    }
    
    agent_llm_calls {
        text session_id PK
        text agent_name
        text model_name
        integer prompt_tokens
        integer completion_tokens
        integer total_tokens
        numeric cost_usd
        jsonb request_data
        jsonb response_data
        integer duration_ms
        integer run_id
        timestamp call_timestamp
        timestamp created_at
    }
    
    agent_tool_usage {
        text session_id PK
        text agent_name
        text tool_name
        jsonb tool_input
        jsonb tool_output
        boolean success
        text error_message
        integer duration_ms
        integer run_id
        timestamp call_timestamp
        timestamp created_at
    }
```

### Data Flow Architecture
```mermaid
sequenceDiagram
    participant Agent
    participant MonitoredLLM
    participant EventLogger
    participant Database
    participant Dashboard
    
    Agent->>MonitoredLLM: call(messages)
    MonitoredLLM->>MonitoredLLM: Time execution
    MonitoredLLM->>Agent: response
    MonitoredLLM->>EventLogger: log_llm_call()
    EventLogger->>Database: INSERT INTO agent_llm_calls
    Database->>Dashboard: Real-time metrics
    
    Agent->>MonitoredTool: execute()
    MonitoredTool->>MonitoredTool: Time execution
    MonitoredTool->>Agent: result
    MonitoredTool->>EventLogger: log_tool_usage()
    EventLogger->>Database: INSERT INTO agent_tool_usage
```

## Known Issues

Based on TODO comments and code analysis:

1. **Cost Calculation**: USD cost calculation for LLM calls is currently set to `None` in some cases - needs integration with provider-specific pricing APIs
2. **Content Truncation**: Response data is truncated to 500 characters for storage efficiency - may need configurable limits
3. **OpenTelemetry Dependency**: Optional OpenTelemetry packages may not be available in all environments - graceful degradation implemented
4. **Performance Overhead**: Monitoring operations add minimal latency - needs continuous performance profiling

## Future Work

### Planned Enhancements
1. **Advanced Analytics Integration**
   - Machine Learning integration for agent performance prediction
   - Anomaly detection for unusual agent behavior patterns
   - Cost optimization recommendations based on usage patterns

2. **Extended Monitoring Capabilities**
   - Real-time alerting for critical agent failures
   - Custom dashboards for different stakeholder needs
   - Enhanced multi-agent workflow visualization

3. **Integration Improvements**
   - REST/GraphQL APIs for external monitoring system integration
   - Enhanced OpenTelemetry span attributes for better observability
   - Advanced cost tracking with provider-specific pricing integration

4. **Performance Optimizations**
   - Asynchronous logging to reduce agent operation latency
   - Configurable sampling rates for high-throughput scenarios
   - Enhanced database query optimization for analytics workloads

### Related Linear Tickets
- Monitor Linear project for ESG analysis monitoring improvements
- Check for tickets related to agent performance optimization
- Look for cost tracking and budget management requirements

## Troubleshooting

### Common Issues

**Monitoring Data Not Appearing in Dashboard**
- Verify database connection via `get_bo_conn()`
- Check that agent session IDs are consistent
- Ensure PostgreSQL tables exist and have proper permissions

**OpenTelemetry Integration Failing**
- Install required packages: `pip install opentelemetry-api opentelemetry-sdk openinference-instrumentation-crewai`
- Check that OpenTelemetry is properly initialized before agent creation
- Review console logs for instrumentation errors

**High Memory Usage**
- Review event data truncation settings (default 500 chars)
- Monitor JSONB field sizes in database
- Consider implementing data retention policies

**Performance Impact on Agents**
- Monitor database connection pool usage
- Check for slow database queries in monitoring tables
- Verify efficient JSON serialization

### Debug Configuration
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable verbose monitoring logging
logger = create_event_logger("debug_session", verbose=True)
```

## FAQ

**Q: Does monitoring affect agent performance?**
A: The monitoring system is designed with minimal overhead. Database operations are optimized and monitoring failures don't interrupt agent execution.

**Q: Can I disable monitoring for specific agents?**
A: Yes, simply don't wrap the LLM or tools with monitoring wrappers. The agents will function normally without monitoring.

**Q: How is cost tracking calculated?**
A: Cost tracking uses token counts and model-specific pricing. Currently requires manual configuration for accurate USD calculations.

**Q: Is monitoring data encrypted?**
A: Monitoring data follows standard EkoIntelligence database encryption policies. Sensitive content is automatically truncated.

**Q: Can I export monitoring data?**
A: Yes, all monitoring data is stored in PostgreSQL tables and can be queried directly or exported through standard database tools.

**Q: Does the system support multi-tenant monitoring?**
A: Yes, through session-based isolation. Each research session maintains independent monitoring data.

## References

### Documentation Links
- [CrewAI Framework Documentation](https://docs.crewai.com/) - Official CrewAI multi-agent framework
- [OpenTelemetry Python Documentation](https://opentelemetry.io/docs/languages/python/) - Industry-standard observability
- [Loguru Documentation](https://loguru.readthedocs.io/en/stable/) - Advanced Python logging
- [PostgreSQL JSONB Documentation](https://www.postgresql.org/docs/current/datatype-json.html) - JSONB data type usage

### Related Code Files
- [AgentEventLogger](./event_logger.py) - Core event logging infrastructure
- [MonitoredLLM](./llm_wrapper.py) - LLM monitoring wrapper
- [AgentMonitoringService](./agent_monitoring.py) - High-level monitoring service
- [Database Connection Management](../../../db/__init__.py) - PostgreSQL connection pooling
- [Analytics Dashboard](../../../dash/) - Monitoring visualization components

### External Dependencies
- CrewAI Multi-Agent Framework
- OpenTelemetry Observability Platform
- PostgreSQL Database System
- Loguru Python Logging Library

### Database Schema
- [Database Tables](../../../../tmp/db/backoffice/schemas/public/tables/) - Complete schema definitions
- Agent monitoring tables: `agent_execution_events`, `agent_llm_calls`, `agent_tool_usage`

---

## Changelog

### 2025-07-30
- Initial comprehensive README.md creation
- Documented complete system architecture and usage patterns
- Added troubleshooting guide and FAQ section
- Included Mermaid diagrams for system visualization
- Comprehensive dependency and installation documentation

(c) All rights reserved ekoIntelligence 2025