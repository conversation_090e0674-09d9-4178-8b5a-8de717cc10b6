"""
Advanced Agent Monitoring Service for CrewAI Multi-Agent ESG Research Platform

This module provides comprehensive monitoring, instrumentation, and observability capabilities
for CrewAI-powered multi-agent systems within the EkoIntelligence ESG analysis platform.
It implements sophisticated event logging, performance tracking, cost analysis, and OpenTelemetry
integration to enable complete visibility into AI agent operations during corporate sustainability
research workflows and ESG document analysis processes.

## Core Purpose
The Agent Monitoring Service serves as the **observability backbone** for EkoIntelligence's
CrewAI-powered ESG research system, providing real-time insights into agent behavior,
performance bottlenecks, cost optimization opportunities, and operational effectiveness.
It enables comprehensive tracking of multi-agent workflows that discover, analyze, and
report on corporate sustainability practices, greenwashing detection, and ESG compliance.

## Architecture & Components

### AgentMonitoringService - Central Monitoring Orchestrator
**Purpose**: Primary monitoring service for session-based agent activity tracking  
**Database Integration**: Direct connection to analytics database via `get_bo_conn()`

**Core Capabilities**:
- **Event Logging**: Captures task lifecycle events (start/complete), decisions, and errors
- **LLM Call Monitoring**: Tracks token usage, costs, response times, and model performance
- **Tool Usage Analytics**: Records success/failure rates, execution times, and I/O patterns
- **Session Management**: Associates all activities with session IDs for trace analysis
- **Multi-Agent Coordination**: Monitors interactions and workflows across agent teams

**Database Storage Strategy**:
- `agent_execution_events`: General agent events with JSONB event data
- `agent_llm_calls`: LLM-specific metrics with token counts and USD costs
- `agent_tool_usage`: Tool execution logs with detailed success/failure tracking

### MonitoredTool - Intelligent Tool Wrapper
**Purpose**: Transparent monitoring wrapper for CrewAI tools with automatic instrumentation  
**Integration**: Seamlessly wraps existing BaseTool instances without code changes

**Monitoring Features**:
- **Automatic Instrumentation**: Zero-code-change monitoring for all tool interactions
- **Performance Metrics**: Precise timing measurements and execution duration tracking
- **Error Handling**: Comprehensive exception capture with context preservation
- **I/O Logging**: Input/output data capture with configurable truncation limits
- **Multi-Interface Support**: Handles various CrewAI tool invocation patterns (_run, run, __call__, invoke)

### OpenTelemetry Integration - Enterprise Observability
**Purpose**: Industry-standard distributed tracing with custom CrewAI instrumentation  
**Implementation**: `setup_opentelemetry_monitoring()` function with custom span processors

**Advanced Features**:
- **CrewAI Instrumentation**: Automatic capturing of CrewAI-specific agent and tool events
- **Custom Span Processing**: Converts OpenTelemetry spans to structured database records
- **Real-Time Analytics**: Live monitoring with immediate database persistence
- **Distributed Tracing**: Tracks request flows across multiple agents and external systems
- **Dashboard Integration**: Metrics forwarded to analytics dashboard for visualization

## Database Schema Integration

The monitoring system integrates deeply with the EkoIntelligence analytics database architecture:

### Primary Monitoring Tables

#### agent_execution_events
**Purpose**: Comprehensive agent activity logging with flexible event data storage  
**Schema Structure**:
- `session_id`: Groups related activities across agent sessions
- `agent_name`: Identifies the specific agent performing actions
- `event_type`: Categorizes events (task_start, task_complete, decision, error, span_complete)
- `event_data`: JSONB field for flexible event-specific metadata storage
- `task_name`: Optional task identifier for workflow tracking
- `tool_name`: Optional tool identifier for operation-specific logging

**Indexing Strategy**: Optimized indexes on session_id, agent_name, event_type, and timestamp fields

#### agent_llm_calls
**Purpose**: Detailed LLM interaction monitoring with cost and performance analytics  
**Schema Structure**:
- `model_name`: LLM model identifier (GPT-4, Claude, Gemini, etc.)
- `prompt_tokens`: Input token count for cost calculation
- `completion_tokens`: Output token count for response analysis
- `total_tokens`: Combined token usage for budget tracking
- `cost_usd`: Calculated USD cost for financial monitoring
- `request_data`: JSONB storage for prompt and parameters (truncated)
- `response_data`: JSONB storage for model responses (truncated)
- `duration_ms`: Response time measurement for performance optimization

#### agent_tool_usage
**Purpose**: Tool execution monitoring with success/failure analysis and performance metrics  
**Schema Structure**:
- `tool_name`: Identifies the specific tool being executed
- `tool_input`: JSONB storage for tool parameters and input data
- `tool_output`: JSONB storage for tool results and return values
- `success`: Boolean flag indicating execution success/failure status
- `error_message`: Detailed error information when tool execution fails
- `duration_ms`: Execution time measurement for performance analysis

### Performance Optimization Features
- **GIN Indexes**: Fast JSONB queries on event_data, request_data, response_data, tool_input, tool_output
- **Composite Indexing**: Multi-column indexes for common query patterns
- **Time-Series Optimization**: Timestamp-based indexing for dashboard analytics
- **Connection Pooling**: Efficient database connection management through centralized connection system

## System Architecture Context

### Integration with EkoIntelligence Platform
The monitoring system operates as a critical infrastructure component within the broader ESG analysis pipeline:

#### Upstream Integrations
- **CrewAI Orchestration**: Monitors multi-agent research workflows and task delegation
- **ESG Web Crawling**: Tracks document discovery, analysis, and insight extraction
- **LLM Provider Management**: Monitors calls across OpenAI, Anthropic, Google, and other providers
- **Tool Ecosystem**: Instruments web scraping, document processing, and analysis tools

#### Downstream Integrations
- **Analytics Dashboard**: Real-time metrics visualization in `/backoffice/src/eko/dash/`
- **Customer Reporting**: Performance data integration with customer-facing dashboards
- **Cost Management**: Budget tracking and optimization recommendations
- **Quality Assurance**: Agent performance analysis and workflow optimization

#### Data Flow Architecture
```
Agent Activities → MonitoringService → Database Storage
       ↓                   ↓               ↓
Tool Interactions → Event Logging → Performance Metrics
       ↓                   ↓               ↓
LLM API Calls → Cost Tracking → Dashboard Visualization
```

## Advanced Monitoring Capabilities

### Intelligent Event Classification
- **Task Lifecycle Tracking**: Complete visibility into task start, progress, and completion
- **Decision Point Logging**: Captures agent reasoning and decision-making processes
- **Error Context Preservation**: Comprehensive error logging with full context and stack traces
- **Performance Correlation**: Links events across agents for workflow performance analysis

### Cost Management & Optimization
- **Real-Time Cost Tracking**: Immediate USD cost calculation for all LLM interactions
- **Token Usage Analytics**: Detailed analysis of prompt efficiency and response patterns
- **Budget Alerting**: Cost threshold monitoring and optimization recommendations
- **Provider Comparison**: Performance and cost comparison across different LLM providers

### Advanced Analytics Support
- **Session-Based Analysis**: Complete workflow reconstruction from session data
- **Cross-Agent Coordination**: Monitors communication and data sharing between agents
- **Tool Performance Benchmarking**: Success rates, execution times, and reliability metrics
- **Scalability Monitoring**: Resource usage patterns and performance bottleneck identification

## Error Handling & Resilience

### Fail-Fast Architecture
Following EkoIntelligence's fail-fast philosophy:
- **Exception Propagation**: All monitoring errors bubble up immediately without silent failures
- **Comprehensive Logging**: Database failures logged via Loguru with full context
- **Graceful Degradation**: Core agent functionality continues even if monitoring fails
- **Connection Recovery**: Automatic database reconnection and retry mechanisms

### Monitoring Reliability
- **Transaction Safety**: Database operations wrapped in proper transaction boundaries
- **Atomic Operations**: Individual log entries succeed or fail atomically
- **Resource Cleanup**: Automatic cleanup of database connections and resources
- **Performance Impact**: Minimal overhead on core agent operations

## Usage Patterns & Examples

### Basic Session Monitoring Setup
```python
from eko.agent.crewai.monitoring.agent_monitoring import AgentMonitoringService

# Initialize monitoring for research session
monitoring_service = AgentMonitoringService("esg_research_session_001")

# Log agent task lifecycle
monitoring_service.log_task_start("ResearchAgent", "corporate_analysis", "Analyze Nike ESG practices")
# ... agent work happens ...
monitoring_service.log_task_complete("ResearchAgent", "corporate_analysis", analysis_results, duration_ms=45000)
```

### Tool Monitoring Integration
```python
from eko.agent.crewai.monitoring.agent_monitoring import wrap_tools_with_monitoring

# Wrap existing tools with monitoring capabilities
original_tools = [web_search_tool, pdf_analyzer_tool, insight_extractor_tool]
monitored_tools = wrap_tools_with_monitoring(original_tools, monitoring_service, "ResearchAgent")

# Tools now automatically log all usage, performance, and errors
agent = Agent(role="ESG Researcher", tools=monitored_tools, llm=llm)
```

### OpenTelemetry Enterprise Setup
```python
from eko.agent.crewai.monitoring.agent_monitoring import setup_opentelemetry_monitoring

# Enable comprehensive monitoring with industry-standard observability
monitoring_service = setup_opentelemetry_monitoring("comprehensive_esg_analysis_session")

# All CrewAI agents automatically instrumented
# Complete visibility into agent workflows, tool usage, and LLM interactions
```

### Custom Event Logging
```python
# Log agent decisions with context
monitoring_service.log_decision(
    agent_name="AnalysisAgent",
    decision="flag_greenwashing_detected",
    reasoning="Claims lack supporting evidence and contradict historical data",
    context={"confidence": 0.87, "evidence_sources": 3, "contradictions": 2}
)

# Log critical errors with full context
try:
    result = analyze_esg_document(document)
except Exception as e:
    monitoring_service.log_error("AnalysisAgent", e, {
        "document_id": document.id,
        "analysis_stage": "greenwashing_detection",
        "previous_successful_analyses": 45
    })
    raise  # Maintain fail-fast behavior
```

## Performance Characteristics

### Scalability Features
- **Session Isolation**: Independent monitoring per research session prevents resource contention
- **Batched Operations**: Efficient bulk logging for high-throughput agent workflows
- **Connection Pooling**: Database connections shared across monitoring operations
- **Memory Efficiency**: Configurable truncation limits prevent memory exhaustion

### Latency Optimization
- **Asynchronous Logging**: Non-blocking database operations preserve agent performance
- **Connection Reuse**: Persistent database connections minimize connection overhead
- **Efficient Serialization**: Optimized JSON serialization for JSONB storage
- **Index-Optimized Queries**: Database queries designed for fast retrieval and analysis

## Dependencies & Requirements

### Core Dependencies
- **CrewAI Framework**: Multi-agent orchestration and tool management (`crewai.tools.BaseTool`)
- **OpenTelemetry**: Industry-standard observability and distributed tracing
- **Pydantic**: Type-safe data validation and model definition
- **Loguru**: Structured logging with exception capture and performance monitoring
- **PostgreSQL**: Primary database persistence via `eko.db.get_bo_conn()`

### Optional Dependencies
- **OpenInference CrewAI**: Specialized CrewAI instrumentation for enhanced monitoring
- **OpenTelemetry SDK**: Advanced span processing and custom instrumentation
- **OpenTelemetry Exporters**: Console and custom span export capabilities

### Installation Requirements
```bash
# Core monitoring dependencies (required)
pip install crewai pydantic loguru psycopg2-binary

# OpenTelemetry integration (optional but recommended)
pip install opentelemetry-api opentelemetry-sdk
pip install openinference-instrumentation-crewai
```

## Security Considerations

### Data Protection
- **Sensitive Information**: Tool inputs/outputs may contain confidential corporate data
- **Truncation Limits**: Automatic truncation prevents accidental logging of large sensitive datasets
- **Database Security**: Leverages application-level database security and RLS policies
- **Access Control**: Monitoring data access controlled through database connection permissions

### Privacy Compliance
- **Data Minimization**: Only necessary data collected for monitoring and optimization
- **Retention Policies**: Monitoring data subject to standard EkoIntelligence retention policies
- **Audit Trail**: Complete operation logging provides security audit capabilities
- **Encryption**: Database connections and storage encrypted according to platform standards

## Integration with CrewAI Ecosystem

### Agent Lifecycle Integration
The monitoring system seamlessly integrates with CrewAI's agent lifecycle:
- **Agent Initialization**: Automatic monitoring setup during agent creation
- **Task Execution**: Real-time logging of task progress and completion
- **Tool Invocation**: Transparent monitoring of all tool interactions
- **LLM Communication**: Comprehensive tracking of language model interactions

### Multi-Agent Coordination
- **Session Grouping**: Related agents grouped under common session identifiers
- **Cross-Agent Communication**: Monitoring of data sharing and coordination between agents
- **Workflow Visualization**: Complete visibility into multi-agent workflow execution
- **Performance Correlation**: Analysis of agent interdependencies and bottlenecks

## Future Enhancement Opportunities

### Advanced Analytics
- **Machine Learning Integration**: Agent performance prediction and optimization
- **Anomaly Detection**: Automatic identification of unusual agent behavior patterns
- **Cost Optimization**: Intelligent recommendations for LLM usage optimization
- **Quality Metrics**: Advanced agent effectiveness and accuracy measurements

### Extended Integrations
- **Real-Time Alerting**: Immediate notifications for critical agent failures or performance issues
- **Custom Dashboards**: User-configurable monitoring dashboards for different stakeholder needs
- **API Integration**: REST/GraphQL APIs for external monitoring system integration
- **Export Capabilities**: Data export for external analysis and reporting tools

@see https://docs.crewai.com/ CrewAI Multi-Agent Framework Documentation
@see https://opentelemetry.io/docs/languages/python/ OpenTelemetry Python Instrumentation Documentation
@see https://loguru.readthedocs.io/en/stable/ Loguru Python Logging Documentation
@see https://pydantic-docs.helpmanual.io/ Pydantic Data Validation Documentation
@see ./event_logger.py AgentEventLogger for session-based event tracking
@see ./llm_wrapper.py MonitoredLLM for LLM call monitoring and cost tracking
@see ../crawl/ ESG research agent implementations using this monitoring system
@see ../../../dash/ Analytics dashboard components consuming monitoring data
@see ../../../db/data/ Database access objects and connection management
<AUTHOR>
@updated 2025-07-23
@example
```python
# Complete monitoring setup for ESG research session
from eko.agent.crewai.monitoring.agent_monitoring import setup_opentelemetry_monitoring, wrap_tools_with_monitoring

# Initialize comprehensive monitoring
monitoring_service = setup_opentelemetry_monitoring("nike_esg_analysis_2025")

# Create monitored tools
monitored_research_tools = wrap_tools_with_monitoring(research_tools, monitoring_service, "ResearchAgent")

# Agents automatically instrumented with complete observability
research_crew = create_esg_research_crew(monitored_research_tools)
results = research_crew.kickoff()  # All activities monitored and logged
```
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from loguru import logger

from crewai.tools import BaseTool
from pydantic import Field
from eko.db import get_bo_conn

# OpenTelemetry imports for proper CrewAI monitoring
try:
    from opentelemetry import trace as trace_api
    from opentelemetry.sdk import trace as trace_sdk
    from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
    from opentelemetry.sdk.resources import Resource
    OPENTELEMETRY_AVAILABLE = True
except ImportError:
    logger.warning("OpenTelemetry not available. Install opentelemetry-api, opentelemetry-sdk")
    OPENTELEMETRY_AVAILABLE = False

try:
    from openinference.instrumentation.crewai import CrewAIInstrumentor
    CREWAI_INSTRUMENTATION_AVAILABLE = True
except ImportError:
    logger.warning("CrewAI instrumentation not available. Install openinference-instrumentation-crewai")
    CREWAI_INSTRUMENTATION_AVAILABLE = False


class AgentMonitoringService:
    """
    Service for monitoring agent activities and logging events to the database.
    
    This service tracks:
    - Agent execution events (task start/complete, decisions, errors)
    - LLM calls with cost and performance metrics
    - Tool usage with success/failure tracking
    """
    
    def __init__(self, session_id: str):
        """
        Initialize the monitoring service for a specific session.
        
        Args:
            session_id: The unique session ID for this agent run
        """
        self.session_id = session_id
        
    def log_event(
        self, 
        agent_name: str,
        event_type: str,
        event_data: Dict[str, Any],
        task_name: Optional[str] = None,
        tool_name: Optional[str] = None,
        run_id: Optional[int] = None
    ):
        """
        Log an agent execution event.
        
        Args:
            agent_name: Name of the agent performing the action
            event_type: Type of event (task_start, task_complete, tool_call, llm_call, decision, error)
            event_data: Additional data about the event
            task_name: Name of the task if applicable
            tool_name: Name of the tool if applicable
            run_id: Run ID if part of a larger analysis run
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_execution_events 
                        (session_id, agent_name, event_type, event_data, task_name, tool_name, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            event_type,
                            json.dumps(event_data),
                            task_name,
                            tool_name,
                            run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged event: {event_type} for agent {agent_name}")
        except Exception as e:
            logger.error(f"Failed to log event: {e}")
    
    def log_llm_call(
        self,
        agent_name: str,
        model_name: Optional[str] = None,
        prompt_tokens: Optional[int] = None,
        completion_tokens: Optional[int] = None,
        total_tokens: Optional[int] = None,
        cost_usd: Optional[float] = None,
        request_data: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[int] = None,
        run_id: Optional[int] = None
    ):
        """
        Log an LLM call with cost and performance metrics.
        
        Args:
            agent_name: Name of the agent making the call
            model_name: Name of the LLM model used
            prompt_tokens: Number of tokens in the prompt
            completion_tokens: Number of tokens in the completion
            total_tokens: Total tokens used
            cost_usd: Cost in USD for this call
            request_data: Request data sent to the LLM
            response_data: Response data from the LLM
            duration_ms: Duration of the call in milliseconds
            run_id: Run ID if part of a larger analysis run
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_llm_calls 
                        (session_id, agent_name, model_name, prompt_tokens, completion_tokens, 
                         total_tokens, cost_usd, request_data, response_data, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            model_name,
                            prompt_tokens,
                            completion_tokens,
                            total_tokens,
                            cost_usd,
                            json.dumps(request_data) if request_data else None,
                            json.dumps(response_data) if response_data else None,
                            duration_ms,
                            run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged LLM call: {model_name} for agent {agent_name}")
        except Exception as e:
            logger.error(f"Failed to log LLM call: {e}")
    
    def log_tool_usage(
        self,
        agent_name: str,
        tool_name: str,
        tool_input: Dict[str, Any],
        tool_output: Dict[str, Any],
        success: bool,
        error_message: Optional[str] = None,
        duration_ms: Optional[int] = None,
        run_id: Optional[int] = None
    ):
        """
        Log tool usage with success/failure tracking.
        
        Args:
            agent_name: Name of the agent using the tool
            tool_name: Name of the tool being used
            tool_input: Input data passed to the tool
            tool_output: Output data from the tool
            success: Whether the tool usage was successful
            error_message: Error message if the tool failed
            duration_ms: Duration of the tool usage in milliseconds
            run_id: Run ID if part of a larger analysis run
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_tool_usage 
                        (session_id, agent_name, tool_name, tool_input, tool_output, 
                         success, error_message, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            tool_name,
                            json.dumps(tool_input),
                            json.dumps(tool_output),
                            success,
                            error_message,
                            duration_ms,
                            run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged tool usage: {tool_name} for agent {agent_name}")
        except Exception as e:
            logger.error(f"Failed to log tool usage: {e}")
            
    def log_task_start(self, agent_name: str, task_name: str, task_description: str):
        """Log the start of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type="task_start",
            event_data={
                "task_description": task_description,
                "started_at": datetime.now().isoformat()
            },
            task_name=task_name
        )
    
    def log_task_complete(self, agent_name: str, task_name: str, result: Any, duration_ms: Optional[int] = None):
        """Log the completion of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type="task_complete",
            event_data={
                "result": str(result)[:1000],  # Truncate long results
                "completed_at": datetime.now().isoformat(),
                "duration_ms": duration_ms
            },
            task_name=task_name
        )
    
    def log_decision(self, agent_name: str, decision: str, reasoning: str, context: Dict[str, Any]):
        """Log an agent decision."""
        self.log_event(
            agent_name=agent_name,
            event_type="decision",
            event_data={
                "decision": decision,
                "reasoning": reasoning,
                "context": context,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_error(self, agent_name: str, error: Exception, context: Dict[str, Any]):
        """Log an error that occurred during agent execution."""
        self.log_event(
            agent_name=agent_name,
            event_type="error",
            event_data={
                "error_type": type(error).__name__,
                "error_message": str(error),
                "context": context,
                "timestamp": datetime.now().isoformat()
            }
        )


class MonitoredTool(BaseTool):
    """
    Wrapper class for tools to add monitoring capabilities.
    
    This wraps existing tools and automatically logs their usage.
    """
    
    # Define model fields to avoid Pydantic validation errors
    monitoring_service: Optional[AgentMonitoringService] = Field(default=None, exclude=True)
    agent_name: str = Field(default="", exclude=True) 
    wrapped_tool: Optional[BaseTool] = Field(default=None, exclude=True)
    
    def __init__(self, tool: BaseTool, monitoring_service: AgentMonitoringService, agent_name: str):
        """
        Initialize the monitored tool wrapper.
        
        Args:
            tool: The original tool to wrap
            monitoring_service: The monitoring service to log to
            agent_name: Name of the agent using this tool
        """
        # Initialize parent with the original tool's attributes
        # Handle cases where tool doesn't have args_schema or it's None
        args_schema = getattr(tool, "args_schema", None)

        # Create initialization args, excluding None args_schema to use BaseTool's default
        init_kwargs = {
            "name": tool.name,
            "description": tool.description,
            "monitoring_service": monitoring_service,
            "agent_name": agent_name,
            "wrapped_tool": tool,
        }

        # Only include args_schema if it's not None to let BaseTool handle the default
        if args_schema is not None:
            init_kwargs["args_schema"] = args_schema

        super().__init__(**init_kwargs)
        
    def _run(self, *args, **kwargs):
        """Execute the tool with monitoring."""
        logger.info(f"MonitoredTool._run called for {self.name} by {self.agent_name}")
        start_time = time.time()
        
        try:
            # Log tool input
            tool_input = {
                "args": args,
                "kwargs": kwargs
            }
            
            # Execute the original tool's _run method
            result = self.wrapped_tool._run(*args, **kwargs)
            
            # Calculate duration
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log successful tool usage
            self.monitoring_service.log_tool_usage(
                agent_name=self.agent_name,
                tool_name=self.name,
                tool_input=tool_input,
                tool_output={"result": str(result)[:1000]},  # Truncate long outputs
                success=True,
                duration_ms=duration_ms
            )
            
            return result
            
        except Exception as e:
            # Calculate duration even for errors
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log failed tool usage
            self.monitoring_service.log_tool_usage(
                agent_name=self.agent_name,
                tool_name=self.name,
                tool_input={"args": args, "kwargs": kwargs},
                tool_output={"error": str(e)},
                success=False,
                error_message=str(e),
                duration_ms=duration_ms
            )
            
            # Re-raise the exception
            raise
    
    def run(self, *args, **kwargs):
        """Execute the tool with monitoring (CrewAI might call this instead of _run)."""
        logger.info(f"MonitoredTool.run called for {self.name} by {self.agent_name}")
        return self._run(*args, **kwargs)
    
    def __call__(self, *args, **kwargs):
        """Execute the tool with monitoring (CrewAI might call the tool directly)."""
        logger.info(f"MonitoredTool.__call__ called for {self.name} by {self.agent_name}")
        return self._run(*args, **kwargs)
    
    def invoke(self, *args, **kwargs):
        """Execute the tool with monitoring (CrewAI might use invoke)."""
        logger.info(f"MonitoredTool.invoke called for {self.name} by {self.agent_name}")
        return self._run(*args, **kwargs)


def wrap_tools_with_monitoring(tools: List, monitoring_service: AgentMonitoringService, agent_name: str) -> List:
    """
    Wrap a list of tools with monitoring capabilities.
    
    Args:
        tools: List of tools to wrap
        monitoring_service: The monitoring service to use
        agent_name: Name of the agent using these tools
        
    Returns:
        List of wrapped tools with monitoring
    """
    logger.info(f"Wrapping {len(tools)} tools with monitoring for agent {agent_name}")
    wrapped_tools = [MonitoredTool(tool, monitoring_service, agent_name) for tool in tools]
    logger.info(f"Created {len(wrapped_tools)} monitored tools for agent {agent_name}")
    return wrapped_tools


def setup_opentelemetry_monitoring(session_id: str) -> AgentMonitoringService:
    """
    Set up OpenTelemetry monitoring for CrewAI with custom span processing.
    
    This function configures OpenTelemetry to capture CrewAI agent activities
    and forwards the data to our custom monitoring service for database storage.
    
    Args:
        session_id: The session ID for this agent run
        
    Returns:
        AgentMonitoringService instance
        
    Raises:
        ImportError: If required OpenTelemetry packages are not installed
        Exception: If monitoring setup fails
    """
    if not OPENTELEMETRY_AVAILABLE:
        raise ImportError("OpenTelemetry not available. Install: pip install opentelemetry-api opentelemetry-sdk")
    
    if not CREWAI_INSTRUMENTATION_AVAILABLE:
        raise ImportError("CrewAI instrumentation not available. Install: pip install openinference-instrumentation-crewai")
    
    # Create a monitoring service for database logging
    monitoring_service = AgentMonitoringService(session_id)
    
    # Create custom span processor that logs to our database
    class DatabaseSpanProcessor:
        def __init__(self, monitoring_service: AgentMonitoringService):
            self.monitoring_service = monitoring_service
            
        def on_start(self, span, parent_context=None):
            """Called when a span starts."""
            span_name = span.name
            attributes = dict(span.attributes) if span.attributes else {}

            # Log all spans to understand what we're getting
            logger.info(f"OpenTelemetry span started: '{span_name}' with attributes: {attributes}")
                
        def on_end(self, span):
            """Called when a span ends."""
            span_name = span.name
            attributes = dict(span.attributes) if span.attributes else {}
            
            # Log all spans to understand what we're getting
            logger.info(f"OpenTelemetry span ended: '{span_name}' with attributes: {attributes}")
            
            # Calculate duration
            duration_ms = int((span.end_time - span.start_time) / 1_000_000)  # Convert from nanoseconds
            
            # Check if this is a tool-related span
            tool_name = attributes.get("tool.name") or attributes.get("tool") or attributes.get("crewai.tool.name")
            agent_name = attributes.get("agent.name", "Unknown") or attributes.get("agent") or attributes.get("crewai.agent.name", "Unknown")
            
            # Also check if the span name itself indicates tool usage
            if not tool_name and "tool" in span_name.lower():
                tool_name = span_name
            
            # Check for LLM calls
            if "llm" in span_name.lower() or "chat" in span_name.lower() or "openai" in span_name.lower():
                # This might be an LLM call
                self.monitoring_service.log_llm_call(
                    agent_name=agent_name,
                    model_name=attributes.get("llm.model", "unknown"),
                    prompt_tokens=attributes.get("llm.usage.prompt_tokens", 0),
                    completion_tokens=attributes.get("llm.usage.completion_tokens", 0),
                    total_tokens=attributes.get("llm.usage.total_tokens", 0),
                    cost=0.0,  # Will calculate later if needed
                    duration_ms=duration_ms
                )
                logger.info(f"OpenTelemetry captured LLM call: {span_name} by {agent_name} ({duration_ms}ms)")
            
            if tool_name:
                # Log tool usage
                self.monitoring_service.log_tool_usage(
                    agent_name=agent_name,
                    tool_name=tool_name,
                    tool_input=attributes.get("tool.input", {}) or attributes.get("input", {}),
                    tool_output=attributes.get("tool.output", {}) or attributes.get("output", {}),
                    success=span.status.status_code.name != "ERROR",
                    error_message=span.status.description if span.status.status_code.name == "ERROR" else None,
                    duration_ms=duration_ms
                )
                logger.info(f"OpenTelemetry captured tool usage: {tool_name} by {agent_name} ({duration_ms}ms)")
            
            # Also log general agent events
            if span_name and agent_name != "Unknown":
                self.monitoring_service.log_event(
                    agent_name=agent_name,
                    event_type="span_complete",
                    event_data={
                        "span_name": span_name,
                        "duration_ms": duration_ms,
                        "attributes": attributes
                    }
                )
                logger.info(f"OpenTelemetry captured agent event: {span_name} by {agent_name} ({duration_ms}ms)")
            
        def shutdown(self):
            """Called when the processor is shut down."""
            pass
            
        def force_flush(self, timeout_millis=None):
            """Force flush any pending spans."""
            pass
    
    # Get the existing tracer provider and add our custom processor
    existing_tracer_provider = trace_api.get_tracer_provider()
    database_processor = DatabaseSpanProcessor(monitoring_service)
    
    # Add our custom processor to the existing tracer provider
    existing_tracer_provider.add_span_processor(database_processor)
    
    # Also add console logging for debugging
    console_processor = BatchSpanProcessor(ConsoleSpanExporter())
    existing_tracer_provider.add_span_processor(console_processor)
    
    # Instrument CrewAI
    CrewAIInstrumentor().instrument(skip_dep_check=True)
    logger.info("CrewAI OpenTelemetry instrumentation enabled")
    
    logger.info(f"OpenTelemetry monitoring setup complete for session {session_id}")
    return monitoring_service
