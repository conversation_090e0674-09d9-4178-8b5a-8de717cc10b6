"""
CrewAI Multi-Agent Monitoring System for EkoIntelligence ESG Analysis Platform

This package provides comprehensive monitoring and observability capabilities for CrewAI agents
within the EkoIntelligence ESG analysis platform. It captures real-time agent activities,
LLM interactions, tool usage, and performance metrics, enabling detailed analysis and
optimization of AI-powered ESG research workflows through centralized logging and dashboard
visualization.

## Core Purpose
The monitoring system enables **complete observability** of CrewAI agent operations during
ESG research tasks, corporate document analysis, and sustainability claim verification.
It provides real-time insights into agent behavior, performance bottlenecks, cost tracking,
and operational effectiveness for the platform's AI-powered ESG analysis workflows.

## System Architecture Integration
This monitoring package fits into the broader EkoIntelligence system architecture:

- **Analytics Backend**: Python system for ESG document processing and agent orchestration
- **Agent Monitoring Layer**: This package - captures and logs all agent activities to analytics database
- **Dashboard Visualization**: Agent performance metrics displayed in analytics dashboard (`/backoffice/src/eko/dash/`)
- **Database Storage**: Metrics stored in `agent_*` tables in the analytics database (PostgreSQL)
- **Cost Management**: LLM usage and cost tracking for budget optimization and analysis

## Key Monitoring Components

### AgentEventLogger
**Purpose**: Centralized event logging for all agent activities  
**Database Tables**: `agent_execution_events`, `agent_llm_calls`, `agent_tool_usage`

**Core Capabilities**:
- **Event Tracking**: Logs task start/complete, decisions, errors with contextual data
- **LLM Call Monitoring**: Captures token usage, costs, response times, and model performance
- **Tool Usage Logging**: Tracks success/failure rates, execution times, and I/O data
- **Session Management**: Associates all activities with session IDs for trace analysis
- **Error Capture**: Comprehensive error logging with context for debugging

### MonitoredLLM
**Purpose**: LLM wrapper that automatically captures performance metrics  
**Integration**: Transparent wrapper around CrewAI LLM instances

**Monitoring Features**:
- **Automatic Metrics Collection**: Token counts, response times, cost calculation
- **Request/Response Logging**: Captures prompts and responses (truncated for storage)
- **Error Handling**: Logs failed LLM calls with detailed error information
- **Performance Analysis**: Duration tracking for response time optimization
- **Cost Tracking**: USD cost calculation for budget management and optimization

### AgentMonitoringService
**Purpose**: High-level monitoring service for agent orchestration  
**Database Integration**: Direct connection to analytics database via `get_bo_conn()`

**Service Features**:
- **Multi-Agent Coordination**: Tracks interactions between multiple agents
- **Session-Based Logging**: Groups related activities under session identifiers  
- **Real-Time Monitoring**: Immediate logging of agent activities for live dashboard updates
- **Performance Metrics**: Comprehensive timing and success rate tracking
- **Scalable Architecture**: Handles multiple concurrent agent sessions

### OpenTelemetry Integration
**Purpose**: Industry-standard observability with custom CrewAI instrumentation  
**Implementation**: `setup_opentelemetry_monitoring()` function

**Advanced Features**:
- **Distributed Tracing**: Tracks request flows across multiple agents and tools
- **Custom Span Processing**: Converts OpenTelemetry spans to database records
- **CrewAI Instrumentation**: Automatic capturing of CrewAI-specific events
- **Dashboard Integration**: Spans forwarded to analytics dashboard for visualization
- **Production Monitoring**: Enterprise-grade observability for production deployments

## Database Schema Integration
The monitoring system integrates with the EkoIntelligence analytics database schema:

**Table Structure**:
- `agent_execution_events`: General agent events with JSONB event data
- `agent_llm_calls`: LLM-specific metrics with token counts and costs  
- `agent_tool_usage`: Tool execution logs with success/failure tracking
- `agent_sessions`: High-level session metadata for grouping activities

**Performance Optimization**:
- Indexed on `session_id`, `agent_name`, `event_type`, and `event_timestamp`
- GIN indexes on JSONB columns for fast event data queries
- Optimized for time-series analysis and dashboard visualization

## Usage Patterns

### Basic Agent Monitoring
```python
from eko.agent.crewai.monitoring import create_event_logger, wrap_llm_with_monitoring

# Create event logger for session
logger = create_event_logger(session_id="research_session_001")

# Wrap LLM with monitoring
monitored_llm = wrap_llm_with_monitoring(llm, logger, "ResearchAgent")

# Agents automatically log activities
logger.log_task_start("ResearchAgent", "esg_research", "Research Nike ESG practices")
```

### Advanced OpenTelemetry Setup  
```python
from eko.agent.crewai.monitoring import setup_opentelemetry_monitoring

# Enable comprehensive monitoring with OpenTelemetry
monitoring_service = setup_opentelemetry_monitoring("crawl_session_123")

# CrewAI agents automatically instrumented
# All activities captured and forwarded to database
```

## Related Documentation
- **CrewAI Documentation**: https://docs.crewai.com/ - Official CrewAI framework documentation
- **OpenTelemetry Python**: https://opentelemetry.io/docs/languages/python/ - Observability framework
- **EkoIntelligence Agent System**: `/backoffice/src/eko/agent/` - Parent agent orchestration system
- **Analytics Dashboard**: `/backoffice/src/eko/dash/` - Monitoring visualization components

## Key Exports
- **AgentEventLogger**: Core event logging class for manual instrumentation
- **TimedContext**: Context manager for precise operation timing
- **create_event_logger**: Factory function for creating configured event loggers
- **MonitoredLLM**: LLM wrapper with automatic performance monitoring
- **wrap_llm_with_monitoring**: Factory function for LLM monitoring setup
- **AgentMonitoringService**: High-level monitoring service for multi-agent coordination
- **wrap_tools_with_monitoring**: Tool wrapper factory for comprehensive tool monitoring
- **setup_opentelemetry_monitoring**: OpenTelemetry configuration for production monitoring

@see https://docs.crewai.com/concepts/agents CrewAI Agent Documentation
@see https://opentelemetry.io/docs/languages/python/ OpenTelemetry Python Documentation  
@see /backoffice/src/eko/agent/crewai/crawl/ ESG Research Agent Implementation
@see /backoffice/src/eko/dash/ Analytics Dashboard Components
<AUTHOR>
@updated 2025-07-23
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from .event_logger import AgentEventLogger, TimedContext, create_event_logger
from .llm_wrapper import MonitoredLLM, wrap_llm_with_monitoring
from .agent_monitoring import AgentMonitoringService, wrap_tools_with_monitoring, setup_opentelemetry_monitoring

__all__ = ['AgentEventLogger', 'TimedContext', 'create_event_logger', 'MonitoredLLM', 'wrap_llm_with_monitoring', 'AgentMonitoringService', 'wrap_tools_with_monitoring', 'setup_opentelemetry_monitoring']