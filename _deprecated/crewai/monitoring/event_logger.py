"""
Centralized Event Logging System for CrewAI Agent Monitoring and Analytics

This module provides the core event logging infrastructure for EkoIntelligence's CrewAI-powered
multi-agent ESG research platform. The AgentEventLogger class serves as the foundational logging
service that captures, structures, and persists comprehensive agent activity data to enable
real-time monitoring, performance analytics, cost tracking, and operational insights across
distributed AI agent workflows analyzing corporate sustainability practices and ESG compliance.

## Core Purpose

The AgentEventLogger is the **primary data collection layer** for EkoIntelligence's agent
monitoring infrastructure, responsible for systematic capture of all agent activities into
structured database records. It enables comprehensive observability of multi-agent ESG research
workflows by logging execution events, LLM interactions, tool usage patterns, and operational
metrics that power the platform's analytics dashboard and operational intelligence systems.

## Architecture & Integration

### Database-Centric Event Persistence
The event logger integrates directly with EkoIntelligence's analytics database infrastructure:
- **Database Connection**: Uses `get_bo_conn()` for reliable PostgreSQL connectivity
- **Session-Based Tracking**: Groups related activities under unique session identifiers
- **Structured Storage**: Leverages JSONB fields for flexible, queryable event metadata
- **Transaction Safety**: Database operations wrapped in proper transaction boundaries

### Multi-Dimensional Event Classification
Events are systematically categorized across multiple dimensions:
- **Agent Identity**: Associates events with specific agent names for attribution
- **Event Types**: Structured taxonomy including task lifecycle, tool usage, LLM calls, decisions, errors
- **Temporal Tracking**: Automatic timestamping with millisecond precision for performance analysis
- **Contextual Metadata**: Flexible JSONB storage for event-specific data and metadata

### Core Database Schema Integration

#### agent_execution_events Table
**Purpose**: General-purpose agent activity logging with flexible event data storage
**Schema Integration**:
- `session_id` (text, NOT NULL): Groups related activities across multi-agent sessions
- `agent_name` (text, NOT NULL): Identifies specific agent performing activities
- `event_type` (text, NOT NULL): Categorizes events (task_start, task_complete, decision, error)
- `event_data` (jsonb): Flexible storage for event-specific metadata and context
- `task_name` (text, nullable): Optional task identifier for workflow tracking
- `tool_name` (text, nullable): Optional tool identifier for operation-specific logging
- `run_id` (integer, nullable): Links events to broader analysis runs
- `event_timestamp` (timestamp, default NOW()): Precise event timing
- `created_at` (timestamp, default NOW()): Record creation timestamp

**Performance Optimization**:
- **B-tree indexes** on session_id, agent_name, event_type for fast filtering
- **GIN index** on event_data JSONB field for complex metadata queries
- **Descending timestamp index** for efficient time-series analytics

#### agent_llm_calls Table  
**Purpose**: Detailed LLM interaction monitoring with comprehensive cost and performance analytics
**Schema Integration**:
- `session_id` (text, NOT NULL): Session association for trace correlation
- `agent_name` (text, NOT NULL): Agent attribution for LLM usage patterns
- `model_name` (text): LLM model identifier (GPT-4, Claude, Gemini, etc.)
- `prompt_tokens` (integer): Input token count for cost calculation and efficiency analysis
- `completion_tokens` (integer): Output token count for response pattern analysis
- `total_tokens` (integer): Combined token usage for comprehensive cost tracking
- `cost_usd` (numeric(10,4)): Calculated USD cost for financial monitoring and budget management
- `request_data` (jsonb): Structured storage for prompt content and parameters (truncated for performance)
- `response_data` (jsonb): Structured storage for model responses and metadata (truncated for performance)
- `duration_ms` (integer): Response time measurement for performance optimization
- `run_id` (integer, nullable): Association with broader analysis workflows
- `call_timestamp` (timestamp, default NOW()): Precise LLM interaction timing
- `created_at` (timestamp, default NOW()): Record creation timestamp

**Performance Optimization**:
- **B-tree indexes** on session_id, agent_name for efficient agent-specific queries
- **GIN indexes** on request_data and response_data JSONB fields for content analysis
- **Descending timestamp index** optimized for real-time dashboard queries

#### agent_tool_usage Table
**Purpose**: Comprehensive tool execution monitoring with detailed success/failure analysis
**Schema Integration**:
- `session_id` (text, NOT NULL): Session-based grouping for workflow reconstruction
- `agent_name` (text, NOT NULL): Agent identification for tool usage pattern analysis
- `tool_name` (text, NOT NULL): Specific tool identifier for performance benchmarking
- `tool_input` (jsonb): Structured storage for tool parameters and input data
- `tool_output` (jsonb): Structured storage for tool results and return values  
- `success` (boolean): Success/failure flag for reliability analysis
- `error_message` (text, nullable): Detailed error information for debugging and improvement
- `duration_ms` (integer, nullable): Execution time measurement for performance optimization
- `run_id` (integer, nullable): Association with broader analysis workflows
- `call_timestamp` (timestamp, default NOW()): Precise tool execution timing
- `created_at` (timestamp, default NOW()): Record creation timestamp

**Performance Optimization**:
- **B-tree indexes** on session_id, agent_name, tool_name for multi-dimensional filtering
- **GIN indexes** on tool_input and tool_output JSONB fields for parameter analysis
- **Descending timestamp index** for time-series performance analysis

## Core Components

### AgentEventLogger Class
**Purpose**: Session-scoped event logger providing structured database persistence for agent activities

**Initialization**:
- `session_id` (str): Unique identifier linking related agent activities
- `run_id` (Optional[int]): Optional association with broader analysis runs

**Core Logging Methods**:

#### log_event() - General Event Logging
**Purpose**: Flexible logging method for all agent execution events
**Parameters**:
- `agent_name` (str): Identifying the specific agent performing the action
- `event_type` (str): Event category (task_start, task_complete, tool_call, llm_call, decision, error)
- `event_data` (Dict[str, Any]): Flexible metadata storage for event-specific information
- `task_name` (Optional[str]): Task identifier for workflow tracking
- `tool_name` (Optional[str]): Tool identifier for operation-specific logging

**Implementation Features**:
- **Error Resilience**: Database failures logged via Loguru without disrupting agent operation
- **JSON Serialization**: Automatic conversion of event_data to JSONB format
- **Transaction Safety**: Database operations within proper transaction boundaries
- **Performance Optimization**: Efficient database connection reuse through connection pooling

#### log_llm_call() - LLM Interaction Logging
**Purpose**: Comprehensive logging of language model interactions with cost and performance metrics
**Parameters**:
- `agent_name` (str): Agent making the LLM call
- `model_name` (str): LLM model identifier
- `request_data` (Dict[str, Any]): Prompt content and parameters
- `response_data` (Dict[str, Any]): Model response and metadata
- `prompt_tokens` (Optional[int]): Input token count for cost calculation
- `completion_tokens` (Optional[int]): Output token count for usage analysis
- `total_tokens` (Optional[int]): Combined token usage
- `cost_usd` (Optional[float]): Calculated USD cost for budget tracking
- `duration_ms` (Optional[int]): Response time for performance analysis

**Advanced Features**:
- **Cost Tracking**: Immediate USD cost calculation and budget monitoring
- **Performance Analytics**: Millisecond-precision timing for optimization
- **Content Analysis**: Structured prompt and response storage for pattern analysis
- **Token Efficiency**: Detailed token usage tracking for model optimization

#### log_tool_usage() - Tool Execution Logging
**Purpose**: Detailed monitoring of tool interactions with success/failure tracking and performance metrics
**Parameters**:
- `agent_name` (str): Agent using the tool
- `tool_name` (str): Specific tool identifier
- `tool_input` (Dict[str, Any]): Tool parameters and input data
- `tool_output` (Dict[str, Any]): Tool results and return values
- `success` (bool): Success/failure indicator for reliability analysis
- `error_message` (Optional[str]): Detailed error information when failures occur
- `duration_ms` (Optional[int]): Execution time for performance benchmarking

**Reliability Features**:
- **Success Tracking**: Boolean success indicators for tool reliability analysis
- **Error Context**: Comprehensive error message capture for debugging and improvement
- **Performance Monitoring**: Precise execution time measurement for optimization
- **I/O Analysis**: Complete input/output data capture for tool behavior analysis

### Convenience Logging Methods

#### Specialized Event Loggers
- **log_task_start()**: Captures task initiation with description and timestamp
- **log_task_complete()**: Records task completion with results and duration
- **log_error()**: Structured error logging with exception details and context
- **log_decision()**: Documents agent decision-making with reasoning and context

### Utility Components

#### TimedContext Class
**Purpose**: Context manager for precise operation timing measurement
**Features**:
- **High-Precision Timing**: Microsecond-level timing accuracy using time.time()
- **Context Management**: Automatic start/stop timing with __enter__/__exit__ methods
- **Duration Calculation**: Automatic millisecond conversion for database storage
- **Exception Safety**: Timing calculations preserved even when exceptions occur

#### create_event_logger() Factory Function
**Purpose**: Simplified factory function for AgentEventLogger instantiation
**Parameters**:
- `session_id` (str): Session identifier for the agent run
- `run_id` (Optional[int]): Optional run identifier for workflow tracking
**Returns**: Configured AgentEventLogger instance ready for immediate use

## System Architecture Context

### Integration with EkoIntelligence Platform
The event logging system operates as critical infrastructure within the broader ESG analysis ecosystem:

#### Upstream Integrations
- **CrewAI Agent Framework**: Captures all agent lifecycle events and interactions
- **Multi-Agent Orchestration**: Logs coordination between research, analysis, and reporting agents
- **ESG Research Workflows**: Documents document discovery, analysis, and insight extraction processes
- **LLM Provider Management**: Monitors interactions across OpenAI, Anthropic, Google, and other providers

#### Downstream Integrations  
- **Analytics Dashboard**: Provides real-time data for `/backoffice/src/eko/dash/` monitoring interfaces
- **Performance Optimization**: Data supports agent workflow optimization and bottleneck identification
- **Cost Management**: Financial data enables budget tracking and cost optimization recommendations
- **Quality Assurance**: Event logs support agent effectiveness measurement and improvement

#### Data Flow Architecture
```
Agent Operations → EventLogger → Database Storage → Analytics Dashboard
       ↓               ↓              ↓                    ↓
Tool Interactions → Structured Events → Performance Metrics → Optimization
       ↓               ↓              ↓                    ↓
LLM API Calls → Cost Tracking → Budget Management → Financial Controls
```

## Advanced Features & Capabilities

### Fail-Fast Error Handling
Following EkoIntelligence's fail-fast philosophy:
- **Exception Propagation**: Database logging failures bubble up immediately without silent degradation
- **Comprehensive Logging**: All database failures logged via Loguru with full context and stack traces
- **Graceful Degradation**: Core agent functionality continues even if event logging fails
- **Transaction Integrity**: Database operations maintain ACID properties with proper rollback on failures

### Performance Optimization
- **Connection Pooling**: Efficient database connection reuse through centralized connection management
- **Batch Operations**: Support for efficient bulk logging during high-throughput agent operations
- **JSON Optimization**: Efficient serialization/deserialization for JSONB database storage
- **Index-Optimized Design**: Database schema designed for fast queries and analytics

### Scalability Features
- **Session Isolation**: Independent logging per research session prevents resource contention
- **Memory Efficiency**: Configurable data truncation prevents memory exhaustion from large payloads
- **Concurrent Safety**: Thread-safe operations support concurrent agent execution
- **Resource Management**: Automatic cleanup of database connections and resources

## Usage Patterns & Examples

### Basic Event Logging Setup
```python
from eko.agent.crewai.monitoring.event_logger import create_event_logger

# Initialize event logger for ESG research session
logger = create_event_logger("nike_esg_analysis_2025_q1")

# Log agent task lifecycle
logger.log_task_start("ResearchAgent", "corporate_document_analysis", "Analyze Nike 2024 sustainability report")
# ... agent work happens ...
logger.log_task_complete("ResearchAgent", "corporate_document_analysis", analysis_results)
```

### LLM Call Monitoring
```python
# Log detailed LLM interaction with cost tracking
logger.log_llm_call(
    agent_name="AnalysisAgent",
    model_name="gpt-4-turbo",
    request_data={"prompt": "Analyze greenwashing indicators...", "temperature": 0.1},
    response_data={"analysis": "High greenwashing risk detected...", "confidence": 0.87},
    prompt_tokens=1250,
    completion_tokens=890,
    total_tokens=2140,
    cost_usd=0.0428,
    duration_ms=3450
)
```

### Tool Usage Tracking
```python
# Monitor tool execution with success/failure tracking
try:
    result = web_scraping_tool.execute(url="https://nike.com/sustainability")
    logger.log_tool_usage(
        agent_name="ResearchAgent",
        tool_name="WebScrapingTool",
        tool_input={"url": "https://nike.com/sustainability", "max_pages": 5},
        tool_output={"pages_scraped": 5, "documents_found": 12},
        success=True,
        duration_ms=8900
    )
except Exception as e:
    logger.log_tool_usage(
        agent_name="ResearchAgent", 
        tool_name="WebScrapingTool",
        tool_input={"url": "https://nike.com/sustainability"},
        tool_output={"error": str(e)},
        success=False,
        error_message=str(e),
        duration_ms=2100
    )
```

### Precision Timing with Context Managers
```python
from eko.agent.crewai.monitoring.event_logger import TimedContext

# Precise timing measurement
with TimedContext() as timer:
    analysis_result = perform_complex_esg_analysis(document)

# Automatic duration calculation available
logger.log_task_complete("AnalysisAgent", "esg_analysis", analysis_result, duration_ms=timer.duration_ms)
```

### Error Logging with Context
```python
# Comprehensive error logging with contextual information
try:
    result = analyze_corporate_claims(sustainability_report)
except Exception as e:
    logger.log_error("AnalysisAgent", e, {
        "document_id": sustainability_report.id,
        "analysis_stage": "claim_verification",
        "previous_successful_analyses": 127,
        "memory_usage_mb": 450,
        "processing_time_s": 15.7
    })
    raise  # Maintain fail-fast behavior
```

## Dependencies & Requirements

### Core Dependencies
- **Loguru**: Advanced Python logging with structured output and exception handling
- **PostgreSQL**: Primary database persistence via `eko.db.get_bo_conn()` connection management
- **Python JSON**: Native JSON serialization for JSONB database storage
- **Python Datetime**: Timestamp generation and ISO format conversion
- **Python Time**: High-precision timing measurements for performance analysis

### Optional Dependencies
- **Psycopg2**: PostgreSQL database adapter (via connection pool)
- **Connection Pooling**: Database connection management via `eko.db` module

### System Requirements
- **Python 3.8+**: Modern Python with typing support and context managers
- **PostgreSQL 12+**: Database with JSONB support and GIN indexing
- **Memory**: Configurable based on event volume and retention requirements

## Security & Privacy Considerations

### Data Protection
- **Sensitive Information**: Event data may contain confidential corporate ESG information
- **Truncation Safeguards**: Automatic data truncation prevents accidental logging of excessive sensitive data
- **Database Security**: Leverages application-level database security and access controls
- **Connection Security**: Encrypted database connections following platform security standards

### Compliance & Audit Trail
- **Complete Audit Trail**: Comprehensive logging provides security audit capabilities
- **Data Retention**: Event data subject to EkoIntelligence data retention policies
- **Access Control**: Event data access controlled through database connection permissions
- **GDPR Compliance**: Data collection aligned with privacy regulations and consent frameworks

## Integration with Related Components

### Monitoring System Components
- **AgentMonitoringService** (`agent_monitoring.py`): Higher-level service consuming this event logger
- **MonitoredTool Wrapper** (`agent_monitoring.py`): Tool instrumentation using this logging infrastructure
- **OpenTelemetry Integration** (`agent_monitoring.py`): Enterprise observability consuming event data

### Database Layer Integration
- **Connection Management** (`eko.db`): Database connection pooling and transaction management
- **Schema Management**: Integration with analytics database schema and migration system
- **Data Access Objects**: DAO pattern implementations accessing logged event data

### Analytics & Dashboard Integration
- **Dashboard Components** (`/backoffice/src/eko/dash/`): Real-time analytics consuming event data
- **Performance Metrics**: Agent performance calculations based on logged events
- **Cost Analytics**: LLM cost tracking and optimization recommendations
- **Quality Metrics**: Agent effectiveness measurements derived from event logs

## See Also

@see https://loguru.readthedocs.io/en/stable/ Loguru Python Logging Library Documentation
@see https://www.postgresql.org/docs/current/datatype-json.html PostgreSQL JSONB Documentation  
@see https://docs.python.org/3/library/contextlib.html Python Context Managers Documentation
@see https://docs.python.org/3/library/time.html Python Time Module Documentation
@see ./agent_monitoring.py Higher-level agent monitoring service using this event logger
@see ../../../db/__init__.py Database connection management and pooling
@see ../../../dash/ Analytics dashboard components consuming logged events
@see ../../../../tmp/db/backoffice/schemas/public/tables/ Database schema definitions for monitoring tables

<AUTHOR>
@updated 2025-07-23
@description Centralized event logging system for CrewAI agent monitoring providing structured database persistence for comprehensive agent activity tracking, LLM interaction monitoring, and tool usage analytics within EkoIntelligence's ESG research platform
@example
```python
# Complete event logging workflow for ESG research session
from eko.agent.crewai.monitoring.event_logger import create_event_logger, TimedContext

# Initialize session-scoped event logger
logger = create_event_logger("corporate_sustainability_analysis_2025")

# Log agent task with precise timing
with TimedContext() as timer:
    analysis_results = research_agent.analyze_sustainability_report(nike_report)

logger.log_task_complete("ResearchAgent", "sustainability_analysis", analysis_results, duration_ms=timer.duration_ms)

# Log LLM interaction with cost tracking  
logger.log_llm_call(
    agent_name="AnalysisAgent",
    model_name="claude-3-sonnet",
    prompt_tokens=2100,
    completion_tokens=1450,
    cost_usd=0.0567,
    duration_ms=4200
)
```
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, Optional

from loguru import logger

from eko.db import get_bo_conn


class AgentEventLogger:
    """
    Central event logger for CrewAI agent monitoring.
    
    This class provides methods to log various types of agent events to the monitoring
    database tables for visualization in the dashboard.
    """
    
    def __init__(self, session_id: str, run_id: Optional[int] = None):
        """
        Initialize the event logger.
        
        Args:
            session_id: The session ID for the agent run
            run_id: Optional run ID for tracking across multiple sessions
        """
        self.session_id = session_id
        self.run_id = run_id
        
    def log_event(
        self, 
        agent_name: str, 
        event_type: str, 
        event_data: Dict[str, Any],
        task_name: Optional[str] = None,
        tool_name: Optional[str] = None
    ):
        """
        Log a general agent event.
        
        Args:
            agent_name: Name of the agent
            event_type: Type of event ('task_start', 'task_complete', 'tool_call', 'llm_call', 'decision', 'error')
            event_data: Additional event data
            task_name: Optional task name
            tool_name: Optional tool name
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_execution_events 
                        (session_id, agent_name, event_type, event_data, task_name, tool_name, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            event_type,
                            json.dumps(event_data),
                            task_name,
                            tool_name,
                            self.run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged event: {agent_name} - {event_type}")
        except Exception as e:
            logger.error(f"Failed to log event: {e}")
    
    def log_llm_call(
        self,
        agent_name: str,
        model_name: str,
        request_data: Dict[str, Any],
        response_data: Dict[str, Any],
        prompt_tokens: Optional[int] = None,
        completion_tokens: Optional[int] = None,
        total_tokens: Optional[int] = None,
        cost_usd: Optional[float] = None,
        duration_ms: Optional[int] = None
    ):
        """
        Log an LLM call with detailed metrics.
        
        Args:
            agent_name: Name of the agent making the call
            model_name: Name of the LLM model
            request_data: Request data (prompt, parameters)
            response_data: Response data
            prompt_tokens: Number of prompt tokens
            completion_tokens: Number of completion tokens
            total_tokens: Total tokens used
            cost_usd: Cost in USD
            duration_ms: Duration in milliseconds
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_llm_calls 
                        (session_id, agent_name, model_name, prompt_tokens, completion_tokens, 
                         total_tokens, cost_usd, request_data, response_data, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            model_name,
                            prompt_tokens,
                            completion_tokens,
                            total_tokens,
                            cost_usd,
                            json.dumps(request_data),
                            json.dumps(response_data),
                            duration_ms,
                            self.run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged LLM call: {agent_name} - {model_name}")
        except Exception as e:
            logger.error(f"Failed to log LLM call: {e}")
    
    def log_tool_usage(
        self,
        agent_name: str,
        tool_name: str,
        tool_input: Dict[str, Any],
        tool_output: Dict[str, Any],
        success: bool,
        error_message: Optional[str] = None,
        duration_ms: Optional[int] = None
    ):
        """
        Log tool usage with input/output data.
        
        Args:
            agent_name: Name of the agent using the tool
            tool_name: Name of the tool
            tool_input: Tool input data
            tool_output: Tool output data
            success: Whether the tool call was successful
            error_message: Error message if failed
            duration_ms: Duration in milliseconds
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_tool_usage 
                        (session_id, agent_name, tool_name, tool_input, tool_output, 
                         success, error_message, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            tool_name,
                            json.dumps(tool_input),
                            json.dumps(tool_output),
                            success,
                            error_message,
                            duration_ms,
                            self.run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged tool usage: {agent_name} - {tool_name}")
        except Exception as e:
            logger.error(f"Failed to log tool usage: {e}")
    
    def log_task_start(self, agent_name: str, task_name: str, task_description: str):
        """Log the start of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type='task_start',
            event_data={'task_description': task_description},
            task_name=task_name
        )
    
    def log_task_complete(self, agent_name: str, task_name: str, result: Any):
        """Log the completion of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type='task_complete',
            event_data={'result': str(result)[:1000]},  # Truncate large results
            task_name=task_name
        )
    
    def log_error(self, agent_name: str, error_message: str, error_details: Dict[str, Any]):
        """Log an error event."""
        self.log_event(
            agent_name=agent_name,
            event_type='error',
            event_data={
                'error_message': error_message,
                'error_details': error_details
            }
        )
    
    def log_decision(self, agent_name: str, decision_type: str, decision_data: Dict[str, Any]):
        """Log a decision made by an agent."""
        self.log_event(
            agent_name=agent_name,
            event_type='decision',
            event_data={
                'decision_type': decision_type,
                'decision_data': decision_data
            }
        )


class TimedContext:
    """Context manager for timing operations."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.duration_ms = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        self.duration_ms = int((self.end_time - self.start_time) * 1000)


def create_event_logger(session_id: str, run_id: Optional[int] = None) -> AgentEventLogger:
    """
    Factory function to create an event logger.
    
    Args:
        session_id: The session ID for the agent run
        run_id: Optional run ID for tracking
        
    Returns:
        AgentEventLogger instance
    """
    return AgentEventLogger(session_id, run_id)