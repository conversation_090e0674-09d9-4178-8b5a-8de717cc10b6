"""
doc-by-claude

Comprehensive Monitoring Service for CrewAI Multi-Agent Orchestration System

This module provides the core monitoring infrastructure for EkoIntelligence's CrewAI-powered 
multi-agent system, delivering comprehensive real-time observability, database-backed event logging, 
and enterprise-grade performance analytics for autonomous ESG research operations. The service acts 
as the primary instrumentation layer that captures, persists, and tracks agent activities, LLM 
interactions, and tool usage patterns across complex multi-agent workflows orchestrated through 
the EkoIntelligence platform's sophisticated agent-based research automation.

## Core Purpose

The monitoring service establishes **enterprise-grade observability** for CrewAI agent operations 
within the EkoIntelligence ESG analysis platform, providing comprehensive tracking of multi-agent 
coordination, LLM API consumption analytics, tool execution monitoring, and detailed session 
management capabilities. This system enables sophisticated operational insights, cost optimization, 
debugging support, and performance analytics for autonomous research workflows that can span hours 
or days of continuous operation across distributed agent teams.

## Key Features

### Comprehensive Agent Activity Monitoring
- **Session-Based Tracking**: Complete session lifecycle management with unique session identifiers and persistence
- **Event-Driven Logging**: Structured logging of agent decisions, task transitions, and workflow state changes
- **Cross-Agent Coordination**: Monitoring of collaborative agent interactions and shared context management
- **Execution Flow Analysis**: Detailed tracking of task progression and agent workflow orchestration

### LLM API Integration Monitoring
- **Token Usage Analytics**: Comprehensive tracking of prompt/completion tokens across all LLM providers
- **Cost Management**: Real-time cost calculation and budget tracking for LLM API consumption
- **Performance Metrics**: Response time monitoring, throughput analysis, and efficiency optimization
- **Model Usage Distribution**: Analytics on model selection patterns and provider utilization

### Tool Execution Observability
- **Success/Failure Tracking**: Comprehensive monitoring of tool execution outcomes with detailed error logging
- **Performance Profiling**: Execution time tracking, throughput analysis, and bottleneck identification
- **Tool Usage Analytics**: Frequency analysis, success rates, and performance optimization insights
- **Input/Output Monitoring**: Structured logging of tool parameters and results for debugging and analysis

### Database-Backed Persistence
- **PostgreSQL Integration**: Enterprise-grade data persistence through the EkoIntelligence analytics database
- **JSONB Storage**: Flexible structured data storage for complex event data and metadata
- **Audit Trail Management**: Complete traceability of agent activities for compliance and debugging
- **Query Optimization**: Indexed storage design for efficient dashboard queries and analytics operations

## System Architecture Integration

### EkoIntelligence Platform Integration
The monitoring service operates as a core infrastructure component within the broader EkoIntelligence 
system architecture, providing observability for the autonomous multi-agent research platform:

#### CrewAI Orchestration Framework
- **Seamless Integration**: Native integration with CrewAI's agent orchestration without workflow disruption
- **Tool Wrapper System**: Automatic monitoring injection through intelligent tool wrapping mechanisms
- **OpenTelemetry Compatibility**: Full compatibility with existing OpenTelemetry instrumentation infrastructure
- **Session Management**: Coordinated session tracking across multiple agent teams and research workflows

#### Analytics Database Layer
- **Connection Pool Integration**: Optimized database connectivity through EkoIntelligence's connection pooling infrastructure
- **Schema Alignment**: Full integration with the platform's PostgreSQL schema design and table relationships
- **Transaction Management**: Robust transaction handling with connection management and error recovery
- **Performance Optimization**: Database query optimization and indexing strategies for high-throughput monitoring

#### Dashboard and Visualization Layer
- **Real-Time Data Pipeline**: Structured data provision for the Next.js monitoring dashboard application
- **Analytics Support**: Comprehensive metrics calculation for agent performance analytics and operational insights  
- **Alerting Infrastructure**: Foundation for operational alerting and threshold-based notifications
- **Reporting Integration**: Data structure optimization for management reporting and operational analysis

### Database Schema Architecture

The monitoring service interfaces with four primary database tables designed for comprehensive agent observability:

#### Agent Sessions (`agent_sessions`)
Session-level tracking for multi-agent workflow coordination:
- **Session Management**: Unique session identifiers with company context and lifecycle tracking
- **Memory Integration**: Session state persistence and memory manager coordination
- **Time-Based Tracking**: Creation and update timestamps for session lifecycle analysis

#### Agent Execution Events (`agent_execution_events`)
Comprehensive event tracking for agent workflow monitoring:
- **Event Categories**: Task transitions, decision points, workflow state changes, and error conditions
- **Structured Data**: JSONB storage for complex event metadata and contextual information
- **Cross-Session Linking**: Session-based event aggregation and workflow reconstruction capabilities

#### LLM Call Analytics (`agent_llm_calls`)
Detailed LLM API interaction monitoring and cost analysis:
- **Token Analytics**: Comprehensive tracking of input/output token consumption patterns
- **Cost Calculation**: Real-time cost computation with model-specific pricing integration
- **Performance Monitoring**: Response time tracking and throughput analysis across LLM providers
- **Request/Response Logging**: Structured storage of API interactions for debugging and optimization

#### Tool Usage Monitoring (`agent_tool_usage`)
Comprehensive tool execution tracking and performance analysis:
- **Execution Metrics**: Duration tracking, success rates, and performance optimization insights
- **Input/Output Logging**: Structured parameter and result storage for debugging and analysis
- **Error Tracking**: Detailed error logging with context preservation for troubleshooting
- **Usage Analytics**: Tool frequency analysis and efficiency optimization support

## Monitoring Components Architecture

### AgentMonitoringService Class
The core monitoring orchestration service providing comprehensive agent observability:

```python
monitoring_service = AgentMonitoringService(session_id="crawl_1234567")

# Comprehensive event logging with structured data
monitoring_service.log_event(
    agent_name="ResearchAgent",
    event_type="task_start", 
    event_data={"task": "web_crawl", "target": "sustainability_report"},
    task_name="document_discovery"
)

# LLM interaction monitoring with cost tracking
monitoring_service.log_llm_call(
    agent_name="AnalysisAgent",
    model_name="gpt-4-turbo",
    prompt_tokens=1500,
    completion_tokens=800,
    cost_usd=0.05,
    duration_ms=2340
)

# Tool execution monitoring with success tracking
monitoring_service.log_tool_usage(
    agent_name="JournalistAgent", 
    tool_name="web_scraper",
    tool_input={"url": "https://company.com/report"},
    tool_output={"content_length": 45000, "status": "success"},
    success=True,
    duration_ms=1200
)
```

### MonitoredTool Wrapper System
Intelligent tool wrapping for automatic monitoring injection:

```python
# Automatic tool monitoring with transparent integration
monitored_tools = wrap_tools_with_monitoring(
    tools=original_tools,
    monitoring_service=monitoring_service,
    agent_name="ResearchAgent"
)

# Tools automatically log execution metrics, success/failure, and performance data
# without requiring modification to existing tool implementations or agent logic
```

## Integration with EkoIntelligence ESG Analysis Platform

### Multi-Agent Research Workflow Monitoring
- **PersistentWebCrawlCrew Integration**: Comprehensive monitoring of the platform's primary research orchestration system
- **Cross-Agent Coordination**: Tracking of collaborative research workflows between ResearchAgent, JournalistAgent, AnalysisAgent, and SummaryAgent
- **Memory Manager Integration**: Session persistence coordination with CrewMemoryManager for long-running research operations
- **Task Lifecycle Tracking**: Complete monitoring of research task progression from initiation to completion

### ESG Research Operation Analytics
- **Research Session Tracking**: Comprehensive monitoring of multi-day ESG research operations with session persistence
- **Document Discovery Monitoring**: Tracking of web crawling operations, PDF processing, and content extraction workflows
- **Analysis Pipeline Observability**: Monitoring of ESG insight extraction, greenwashing detection, and corporate analysis operations
- **Performance Optimization**: Data-driven insights for research workflow optimization and efficiency improvements

### Cost Management and Resource Optimization
- **LLM Cost Analytics**: Real-time cost tracking across multiple LLM providers for budget management and optimization
- **Token Usage Optimization**: Analytics-driven insights for prompt optimization and token consumption reduction
- **Resource Utilization Monitoring**: Tool usage analytics for workflow optimization and resource allocation
- **Performance Benchmarking**: Comparative analysis of agent performance and workflow efficiency metrics

### Operational Excellence and Reliability
- **Error Detection and Recovery**: Comprehensive error tracking with contextual information for rapid issue resolution
- **Performance Monitoring**: Real-time performance analytics for proactive bottleneck identification and resolution
- **Quality Assurance**: Monitoring data for research quality assessment and workflow validation
- **Compliance Support**: Audit trail generation for regulatory compliance and operational transparency requirements

## Technical Implementation Details

### Database Connection Management
- **Connection Pool Integration**: Utilizes EkoIntelligence's optimized PostgreSQL connection pooling infrastructure
- **Transaction Safety**: Robust transaction management with automatic rollback on error conditions
- **Error Recovery**: Comprehensive error handling with fallback mechanisms and detailed logging
- **Performance Optimization**: Batch processing capabilities and query optimization for high-throughput scenarios

### JSON Data Serialization
- **Structured Data Storage**: JSONB-based storage for complex event data with efficient query capabilities
- **Schema Flexibility**: Dynamic data structure support for evolving monitoring requirements
- **Query Performance**: GIN indexing for efficient JSON queries and analytics operations
- **Data Integrity**: Validation and sanitization of monitored data before database persistence

### Integration Patterns
- **OpenTelemetry Compatibility**: Full compatibility with existing observability infrastructure
- **Dashboard Integration**: Optimized data structures for efficient dashboard queries and visualization
- **API Integration**: RESTful data access patterns for external monitoring and analytics systems
- **Event-Driven Architecture**: Asynchronous event processing for minimal performance impact on agent operations

---

**Author**: <EMAIL>  
**Updated**: 2025-07-24  
**Copyright**: EkoIntelligence Limited  
**Module**: backoffice/src/eko/agent/crewai/monitoring.py  
**Purpose**: Enterprise-grade monitoring infrastructure for CrewAI multi-agent ESG research automation
"""

import json
import time
from datetime import datetime
from loguru import logger
from typing import Dict, Any, Optional, List

from eko.db import get_bo_conn


class AgentMonitoringService:
    """
    Service for monitoring agent activities and logging events to the database.
    
    This service tracks:
    - Agent execution events (task start/complete, decisions, errors)
    - LLM calls with cost and performance metrics
    - Tool usage with success/failure tracking
    """
    
    def __init__(self, session_id: str):
        """
        Initialize the monitoring service for a specific session.
        
        Args:
            session_id: The unique session ID for this agent run
        """
        self.session_id = session_id
        
    def log_event(
        self, 
        agent_name: str,
        event_type: str,
        event_data: Dict[str, Any],
        task_name: Optional[str] = None,
        tool_name: Optional[str] = None,
        run_id: Optional[int] = None
    ):
        """
        Log an agent execution event.
        
        Args:
            agent_name: Name of the agent performing the action
            event_type: Type of event (task_start, task_complete, tool_call, llm_call, decision, error)
            event_data: Additional data about the event
            task_name: Name of the task if applicable
            tool_name: Name of the tool if applicable
            run_id: Run ID if part of a larger analysis run
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_execution_events 
                        (session_id, agent_name, event_type, event_data, task_name, tool_name, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            event_type,
                            json.dumps(event_data),
                            task_name,
                            tool_name,
                            run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged event: {event_type} for agent {agent_name}")
        except Exception as e:
            logger.error(f"Failed to log event: {e}")
    
    def log_llm_call(
        self,
        agent_name: str,
        model_name: Optional[str] = None,
        prompt_tokens: Optional[int] = None,
        completion_tokens: Optional[int] = None,
        total_tokens: Optional[int] = None,
        cost_usd: Optional[float] = None,
        request_data: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[int] = None,
        run_id: Optional[int] = None
    ):
        """
        Log an LLM call with cost and performance metrics.
        
        Args:
            agent_name: Name of the agent making the call
            model_name: Name of the LLM model used
            prompt_tokens: Number of tokens in the prompt
            completion_tokens: Number of tokens in the completion
            total_tokens: Total tokens used
            cost_usd: Cost in USD for this call
            request_data: Request data sent to the LLM
            response_data: Response data from the LLM
            duration_ms: Duration of the call in milliseconds
            run_id: Run ID if part of a larger analysis run
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_llm_calls 
                        (session_id, agent_name, model_name, prompt_tokens, completion_tokens, 
                         total_tokens, cost_usd, request_data, response_data, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            model_name,
                            prompt_tokens,
                            completion_tokens,
                            total_tokens,
                            cost_usd,
                            json.dumps(request_data) if request_data else None,
                            json.dumps(response_data) if response_data else None,
                            duration_ms,
                            run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged LLM call: {model_name} for agent {agent_name}")
        except Exception as e:
            logger.error(f"Failed to log LLM call: {e}")
    
    def log_tool_usage(
        self,
        agent_name: str,
        tool_name: str,
        tool_input: Dict[str, Any],
        tool_output: Dict[str, Any],
        success: bool,
        error_message: Optional[str] = None,
        duration_ms: Optional[int] = None,
        run_id: Optional[int] = None
    ):
        """
        Log tool usage with success/failure tracking.
        
        Args:
            agent_name: Name of the agent using the tool
            tool_name: Name of the tool being used
            tool_input: Input data passed to the tool
            tool_output: Output data from the tool
            success: Whether the tool usage was successful
            error_message: Error message if the tool failed
            duration_ms: Duration of the tool usage in milliseconds
            run_id: Run ID if part of a larger analysis run
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_tool_usage 
                        (session_id, agent_name, tool_name, tool_input, tool_output, 
                         success, error_message, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            tool_name,
                            json.dumps(tool_input),
                            json.dumps(tool_output),
                            success,
                            error_message,
                            duration_ms,
                            run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged tool usage: {tool_name} for agent {agent_name}")
        except Exception as e:
            logger.error(f"Failed to log tool usage: {e}")
            
    def log_task_start(self, agent_name: str, task_name: str, task_description: str):
        """Log the start of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type="task_start",
            event_data={
                "task_description": task_description,
                "started_at": datetime.now().isoformat()
            },
            task_name=task_name
        )
    
    def log_task_complete(self, agent_name: str, task_name: str, result: Any, duration_ms: Optional[int] = None):
        """Log the completion of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type="task_complete",
            event_data={
                "result": str(result)[:1000],  # Truncate long results
                "completed_at": datetime.now().isoformat(),
                "duration_ms": duration_ms
            },
            task_name=task_name
        )
    
    def log_decision(self, agent_name: str, decision: str, reasoning: str, context: Dict[str, Any]):
        """Log an agent decision."""
        self.log_event(
            agent_name=agent_name,
            event_type="decision",
            event_data={
                "decision": decision,
                "reasoning": reasoning,
                "context": context,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_error(self, agent_name: str, error: Exception, context: Dict[str, Any]):
        """Log an error that occurred during agent execution."""
        self.log_event(
            agent_name=agent_name,
            event_type="error",
            event_data={
                "error_type": type(error).__name__,
                "error_message": str(error),
                "context": context,
                "timestamp": datetime.now().isoformat()
            }
        )


class MonitoredTool:
    """
    Wrapper class for tools to add monitoring capabilities.
    
    This wraps existing tools and automatically logs their usage.
    """
    
    def __init__(self, tool, monitoring_service: AgentMonitoringService, agent_name: str):
        """
        Initialize the monitored tool wrapper.
        
        Args:
            tool: The original tool to wrap
            monitoring_service: The monitoring service to log to
            agent_name: Name of the agent using this tool
        """
        self.tool = tool
        self.monitoring_service = monitoring_service
        self.agent_name = agent_name
        
        # Copy tool attributes
        self.name = getattr(tool, 'name', 'unknown_tool')
        self.description = getattr(tool, 'description', '')
        
    def __call__(self, *args, **kwargs):
        """Execute the tool with monitoring."""
        start_time = time.time()
        
        try:
            # Log tool input
            tool_input = {
                "args": args,
                "kwargs": kwargs
            }
            
            # Execute the tool
            result = self.tool(*args, **kwargs)
            
            # Calculate duration
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log successful tool usage
            self.monitoring_service.log_tool_usage(
                agent_name=self.agent_name,
                tool_name=self.name,
                tool_input=tool_input,
                tool_output={"result": str(result)[:1000]},  # Truncate long outputs
                success=True,
                duration_ms=duration_ms
            )
            
            return result
            
        except Exception as e:
            # Calculate duration even for errors
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log failed tool usage
            self.monitoring_service.log_tool_usage(
                agent_name=self.agent_name,
                tool_name=self.name,
                tool_input={"args": args, "kwargs": kwargs},
                tool_output={"error": str(e)},
                success=False,
                error_message=str(e),
                duration_ms=duration_ms
            )
            
            # Re-raise the exception
            raise


def wrap_tools_with_monitoring(tools: List, monitoring_service: AgentMonitoringService, agent_name: str) -> List:
    """
    Wrap a list of tools with monitoring capabilities.
    
    Args:
        tools: List of tools to wrap
        monitoring_service: The monitoring service to use
        agent_name: Name of the agent using these tools
        
    Returns:
        List of wrapped tools with monitoring
    """
    return [MonitoredTool(tool, monitoring_service, agent_name) for tool in tools]
