"""
doc-by-claude

# CrewAI Telemetry Collection System for EkoIntelligence Multi-Agent Platform

This module provides a lightweight, database-integrated telemetry collection system specifically designed 
for CrewAI multi-agent workflows within the EkoIntelligence ESG (Environmental, Social, Governance) 
analysis platform. The system leverages CrewAI's native OpenTelemetry instrumentation capabilities 
while providing seamless integration with the EkoIntelligence analytics database for comprehensive 
agent monitoring, performance tracking, and operational insights.

## Core Purpose

The telemetry collector serves as a **bridge between CrewAI's built-in observability system** and the 
EkoIntelligence platform's database-backed monitoring infrastructure. Rather than creating custom wrappers 
that might interfere with CrewAI's internal operations, this system intelligently captures telemetry data 
from CrewAI's native OpenTelemetry spans and persists it in structured format for dashboard visualization, 
cost analysis, and operational monitoring.

## Key Features

### Native CrewAI Integration
- **Zero Interference Design**: Works with CrewAI's built-in OpenTelemetry instrumentation without custom wrappers
- **Automatic Detection**: Monitors standard CrewAI telemetry environment variables (`CREWAI_DISABLE_TELEMETRY`, `OTEL_SDK_DISABLED`)
- **Session-Based Tracking**: Provides unique session identifiers for tracking multi-agent workflow executions
- **Non-Intrusive Monitoring**: Captures data without affecting CrewAI's internal agent orchestration

### Database-Backed Persistence
- **PostgreSQL Integration**: Stores telemetry data in the `agent_execution_events` table within the analytics database
- **JSONB Storage**: Flexible structured data storage for complex event metadata and execution details
- **Connection Pool Integration**: Uses EkoIntelligence's database connection pooling system (`get_bo_conn()`)
- **Error-Resilient Logging**: Graceful handling of database connection issues without impacting agent execution

### Comprehensive Event Capture
- **Crew Execution Tracking**: High-level crew workflow monitoring with duration and result summarization
- **Individual Task Monitoring**: Granular task-level execution tracking with agent assignment and performance metrics
- **Execution Timeline**: Complete chronological record of agent activities for debugging and analysis
- **Metadata Preservation**: Captures execution context, timing information, and workflow state

## System Architecture Integration

### EkoIntelligence Platform Layer
This telemetry collector integrates into the broader EkoIntelligence monitoring ecosystem:

- **Analytics Database**: Stores telemetry data in the backoffice PostgreSQL database
- **Dashboard Integration**: Provides data for the Next.js-based agent monitoring dashboard
- **Cost Management**: Enables LLM cost tracking and budget analysis across agent workflows
- **Audit Trail**: Creates compliance-ready audit logs for ESG research operations

### CrewAI Integration Points
The collector interfaces with CrewAI's telemetry system at several key points:

- **Environment Monitoring**: Checks for telemetry disable flags and warns about limited functionality
- **Session Management**: Creates and tracks unique session identifiers for crew executions
- **Result Capture**: Logs crew execution outcomes and performance metrics
- **Task Tracking**: Monitors individual task completions and agent assignments

## Database Schema Integration

The collector writes to the `agent_execution_events` table with the following structure:

```sql
CREATE TABLE agent_execution_events (
    id SERIAL PRIMARY KEY,
    session_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    event_type TEXT NOT NULL,
    event_data JSONB,
    task_name TEXT,
    tool_name TEXT,
    run_id INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Event Types
- **`crew_execution`**: High-level crew workflow completion events
- **`task_execution`**: Individual agent task completion tracking  
- **`session_start`**: New crew session initialization
- **`error`**: Exception and error condition logging

## OpenTelemetry Enhancement Path

The module includes provisions for future OpenTelemetry integration enhancement:

- **Custom Exporter Support**: Framework for implementing PostgreSQL OpenTelemetry exporters
- **Span Processor Integration**: Infrastructure for custom span processing and filtering
- **Metrics Collection**: Foundation for future metrics-based monitoring capabilities
- **Distributed Tracing**: Support for multi-service trace correlation

## Usage Patterns

### Session-Based Monitoring
```python
# Initialize collector for a crew session
collector = CrewAITelemetryCollector("crew_session_12345")

# Track crew execution
start_time = datetime.now()
crew_result = crew.kickoff()
end_time = datetime.now()

collector.capture_crew_execution(
    crew_result=crew_result,
    company_name="Example Corporation",
    start_time=start_time,
    end_time=end_time
)
```

### Task-Level Tracking
```python
# Monitor individual task execution
collector.capture_task_execution(
    task_name="research_financial_data",
    agent_name="Financial Analyst",
    result=task_result,
    duration_ms=execution_time_ms
)
```

## Integration with EkoIntelligence Workflows

### ESG Research Operations
The telemetry collector is specifically designed for EkoIntelligence's ESG research workflows:

- **Company Analysis Sessions**: Tracks multi-agent research crews analyzing corporate ESG performance
- **Document Processing**: Monitors agent-based document ingestion and analysis workflows
- **Claims Verification**: Captures telemetry from automated fact-checking and verification processes
- **Report Generation**: Tracks the automated generation of ESG analysis reports

### Performance Optimization
- **Agent Efficiency Analysis**: Identifies high-performing vs. struggling agent configurations
- **Resource Utilization**: Tracks computational resource consumption patterns
- **Bottleneck Identification**: Helps identify workflow optimization opportunities
- **Cost Analysis**: Enables LLM usage cost optimization across agent teams

## Error Handling and Resilience

### Graceful Degradation
- **Database Failures**: Continues agent execution even if telemetry logging fails
- **Connection Issues**: Logs warnings without interrupting crew workflows
- **Malformed Data**: Safely handles unexpected data formats and structures
- **Resource Constraints**: Operates with minimal memory and computational overhead

### Logging Integration
- **Loguru Integration**: Uses EkoIntelligence's standard logging framework
- **Structured Logging**: Provides consistent log formatting across the platform
- **Debug Support**: Includes detailed logging for troubleshooting and development
- **Warning System**: Alerts operators to telemetry configuration issues

## Security and Privacy Considerations

### Data Protection
- **Result Summarization**: Truncates large result objects to prevent sensitive data leakage
- **Connection Security**: Uses secure database connections through the platform's connection pool
- **Access Control**: Integrates with EkoIntelligence's database access control systems
- **Audit Compliance**: Maintains tamper-evident audit trails for regulatory compliance

## Related Components

This telemetry collector works in conjunction with several other EkoIntelligence platform components:

- **Agent Monitoring Dashboard**: Next.js dashboard that visualizes telemetry data
- **Database Connection Pool**: Shared connection management system
- **CrewAI Agent Orchestration**: The primary multi-agent workflow system being monitored
- **Cost Management System**: LLM usage tracking and budget management
- **Audit and Compliance**: Regulatory compliance and audit trail management

## Future Enhancement Opportunities

### Advanced OpenTelemetry Integration
- **Custom Span Processors**: Direct integration with CrewAI's span generation
- **Metrics Collection**: Prometheus-compatible metrics export
- **Distributed Tracing**: Cross-service trace correlation and analysis
- **Real-time Streaming**: WebSocket-based real-time telemetry streaming

### Enhanced Analytics
- **Predictive Analytics**: Machine learning-based performance prediction
- **Anomaly Detection**: Automated detection of unusual agent behavior patterns
- **Resource Optimization**: AI-driven resource allocation recommendations
- **Workflow Optimization**: Automated identification of workflow improvement opportunities

## Dependencies and Requirements

### Core Dependencies
- **CrewAI Framework**: Multi-agent orchestration system with built-in OpenTelemetry support
- **OpenTelemetry SDK**: Industry-standard observability framework for distributed systems
- **PostgreSQL**: Enterprise-grade database for telemetry data persistence
- **Python 3.8+**: Modern Python runtime with async/await support

### Platform Integration
- **EkoIntelligence Database**: Analytics database connection through `eko.db.get_bo_conn()`
- **Loguru Logging**: Structured logging framework for enterprise applications
- **JSONB Support**: PostgreSQL JSON document storage for flexible event data

@see https://docs.crewai.com/en/observability/overview CrewAI Observability Documentation
@see https://opentelemetry.io/docs/python/ OpenTelemetry Python Documentation  
@see https://www.postgresql.org/docs/current/datatype-json.html PostgreSQL JSONB Documentation
@see /Users/<USER>/worktrees/279/backoffice/src/eko/agent/crewai/MONITORING.md EkoIntelligence CrewAI Monitoring System
@see /Users/<USER>/worktrees/279/backoffice/src/eko/db/__init__.py EkoIntelligence Database Connection Management
<AUTHOR>
@updated 2025-07-24
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import json
import os
from datetime import datetime
from loguru import logger
from typing import Dict, Any, Optional

from eko.db import get_bo_conn


class CrewAITelemetryCollector:
    """
    Collector that integrates with CrewAI's native telemetry system.
    
    This leverages CrewAI's built-in observability instead of custom wrappers.
    """
    
    def __init__(self, session_id: str):
        """Initialize the telemetry collector."""
        self.session_id = session_id
        
        # Enable CrewAI telemetry (it's enabled by default unless explicitly disabled)
        # We just ensure it's not disabled
        if os.getenv('CREWAI_DISABLE_TELEMETRY') == 'true':
            logger.warning("CrewAI telemetry is disabled. Dashboard data will be limited.")
            
        if os.getenv('OTEL_SDK_DISABLED') == 'true':
            logger.warning("OpenTelemetry is disabled globally. Dashboard data will be limited.")
    
    def capture_crew_execution(self, crew_result: Any, company_name: str, start_time: datetime, end_time: datetime):
        """
        Capture high-level crew execution data.
        
        This is called after crew execution to store summary data.
        """
        try:
            duration_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Store crew execution summary
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_execution_events 
                        (session_id, agent_name, event_type, event_data, task_name)
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            "CrewAI",
                            "crew_execution",
                            json.dumps({
                                "company_name": company_name,
                                "result_summary": str(crew_result)[:500],
                                "duration_ms": duration_ms,
                                "start_time": start_time.isoformat(),
                                "end_time": end_time.isoformat()
                            }),
                            "crew_execution"
                        )
                    )
                    conn.commit()
                    logger.debug(f"Captured crew execution for session {self.session_id}")
        except Exception as e:
            logger.error(f"Failed to capture crew execution: {e}")
    
    def capture_task_execution(self, task_name: str, agent_name: str, result: Any, duration_ms: Optional[int] = None):
        """
        Capture individual task execution data.
        
        This can be called from task callbacks.
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_execution_events 
                        (session_id, agent_name, event_type, event_data, task_name)
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            "task_execution",
                            json.dumps({
                                "result_summary": str(result)[:500],
                                "duration_ms": duration_ms,
                                "timestamp": datetime.now().isoformat()
                            }),
                            task_name
                        )
                    )
                    conn.commit()
                    logger.debug(f"Captured task execution: {task_name} by {agent_name}")
        except Exception as e:
            logger.error(f"Failed to capture task execution: {e}")
    
    @staticmethod
    def setup_opentelemetry_export():
        """
        Set up OpenTelemetry to export to our database.
        
        This would be the ideal approach - configuring CrewAI's built-in
        telemetry to export to our database instead of creating wrappers.
        """
        # This would require setting up an OpenTelemetry exporter
        # that writes to our PostgreSQL database tables
        # For now, we'll use the simpler callback approach
        pass


def create_telemetry_collector(session_id: str) -> CrewAITelemetryCollector:
    """Create a telemetry collector for the given session."""
    return CrewAITelemetryCollector(session_id)
