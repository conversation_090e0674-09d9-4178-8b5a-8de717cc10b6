# CrewAI AgentOps Observability Integration

## Overview

The `observability` module provides seamless AgentOps integration for CrewAI agents within the EkoIntelligence ESG analysis platform. This module offers industry-standard observability capabilities through AgentOps, the recommended monitoring solution for CrewAI workflows, enabling comprehensive tracking of AI agent performance, cost optimization, and session analytics for production ESG research operations.

AgentOps provides professional-grade monitoring specifically designed for multi-agent systems, offering zero-configuration instrumentation, advanced analytics, session replay capabilities, and detailed cost tracking that complements EkoIntelligence's custom monitoring infrastructure.

The module serves as a bridge between EkoIntelligence's CrewAI-powered ESG research agents and the AgentOps cloud monitoring platform, providing stakeholders with real-time visibility into agent performance, research quality, and operational costs across all sustainability analysis workflows.

## Specification

The observability module shall provide:

### Core Requirements

1. **Zero-Configuration Integration**: Automatic AgentOps instrumentation for all CrewAI agents without code changes
2. **Session Management**: Complete lifecycle management of AgentOps monitoring sessions with proper initialization and termination  
3. **Error Resilience**: Graceful degradation when AgentOps services are unavailable, ensuring core agent functionality continues
4. **Environment Integration**: Secure API key management through environment variables with fallback handling
5. **Tag Management**: Session categorization and filtering capabilities for organized monitoring across research projects
6. **Dashboard Access**: Direct URL generation for AgentOps web dashboard session analysis and debugging

### AgentOps Integration Features

1. **Automatic Instrumentation**: CrewAI agents, tasks, and tools automatically monitored without manual configuration
2. **Real-Time Analytics**: Live performance metrics, token usage, and cost tracking during research sessions
3. **Session Replay**: Complete conversation and decision flow playback for debugging and quality assurance
4. **Multi-Agent Coordination**: Visibility into agent interactions and collaborative research workflows
5. **Professional Dashboard**: Web-based monitoring interface with advanced filtering and analysis capabilities
6. **Industry Standards**: OpenTelemetry-compatible observability following CrewAI team recommendations

### Complementary Architecture

The module works alongside EkoIntelligence's existing monitoring infrastructure:

- **AgentOps Benefits**: Cloud-based professional dashboard, automatic instrumentation, session replay, cost optimization
- **Custom Monitoring Benefits**: Local data control, ESG-specific metrics, offline operation, full customization
- **Dual Coverage**: Comprehensive observability through both cloud-based and local monitoring systems

## Key Components

### AgentOpsManager Class (`agentops_setup.py`)

**Purpose**: Central management class for AgentOps session lifecycle with comprehensive error handling and dashboard integration.

**Core Capabilities**:
- **Session Initialization**: Secure API key handling with environment variable detection and validation
- **Tag Management**: Dynamic session tagging for categorization and advanced filtering in AgentOps dashboard
- **Error Handling**: Graceful degradation when AgentOps is unavailable or misconfigured
- **Dashboard Integration**: Automatic session URL generation for direct access to monitoring interface
- **State Management**: Complete session lifecycle tracking with proper initialization and termination
- **Action Recording**: Custom action logging capabilities for specialized ESG research events

**Key Methods**:
- `initialize(api_key, tags)`: Initialize AgentOps with optional custom configuration
- `tag_session(tags)`: Add dynamic tags during research for enhanced categorization
- `end_session(state)`: Proper session termination with success/failure status
- `get_session_url()`: Direct dashboard URL for session analysis and debugging
- `record_action(type, params)`: Log custom research actions and milestones

### setup_agentops() Function (`agentops_setup.py`)

**Purpose**: Streamlined initialization function for immediate AgentOps integration across all CrewAI agents.

**Functionality**:
- **One-Function Setup**: Complete AgentOps integration with single function call
- **Environment Detection**: Automatic API key discovery from environment variables  
- **Error Recovery**: Continues operation even if AgentOps initialization fails
- **Manager Return**: Returns configured AgentOpsManager instance for advanced usage
- **Default Configuration**: Sensible defaults for immediate production deployment

**Usage Pattern**:
```python
from eko.agent.crewai.observability import setup_agentops

# Enable monitoring for all CrewAI agents
manager = setup_agentops(tags=["esg-analysis", "nike-research"])
# All subsequent CrewAI operations automatically monitored
```

### Global Manager Instance

**Purpose**: Application-wide singleton AgentOpsManager instance (`agentops_manager`) for consistent access across the EkoIntelligence platform.

**Benefits**:
- **Consistent Access**: Single reference point for AgentOps functionality across modules
- **Session Continuity**: Maintained state across different components and research phases
- **Simplified Integration**: No need to pass manager instances between components
- **Memory Efficiency**: Single instance reduces memory overhead in multi-agent scenarios

## Dependencies

### Core Dependencies

- **AgentOps SDK**: Official AgentOps Python library for CrewAI integration
  - Installation: `pip install 'crewai[agentops]'` (includes both CrewAI and AgentOps)
  - Purpose: Direct integration with AgentOps cloud monitoring platform
  - Features: Automatic instrumentation, session management, dashboard integration

- **CrewAI Framework**: Multi-agent orchestration framework with native AgentOps support
  - Integration: Built-in AgentOps detection and automatic instrumentation
  - Compatibility: Requires CrewAI v0.28.0+ for optimal AgentOps integration
  - Purpose: Host framework for ESG research agents with embedded monitoring hooks

- **Loguru**: Advanced Python logging for error tracking and debug information
  - Purpose: Comprehensive logging of AgentOps integration status and errors
  - Configuration: Structured logging with integration success/failure tracking
  - Debug Support: Detailed error messages for troubleshooting integration issues

### Environment Requirements

- **Internet Connectivity**: Required for AgentOps cloud service communication
- **API Key**: Valid AgentOps account and API key for service authentication
- **Environment Variables**: `AGENTOPS_API_KEY` for authentication, optional `AGENTOPS_DEFAULT_TAGS`

### EkoIntelligence Platform Integration

- **Multi-Agent System**: Seamless integration with existing CrewAI agent architecture
- **Research Workflow**: Compatible with comprehensive ESG research pipeline
- **Custom Monitoring**: Works alongside existing local monitoring infrastructure  
- **Database Independence**: No local database requirements - all data handled by AgentOps cloud

## Usage Examples

### Basic Integration (Recommended)

```python
from eko.agent.crewai.observability import setup_agentops
from crewai import Agent, Task, Crew

# Enable AgentOps monitoring for all agents
manager = setup_agentops(tags=["esg-analysis", "corporate-research"])

# Create research agents - automatically monitored
research_agent = Agent(
    role="ESG Research Specialist", 
    goal="Investigate corporate sustainability practices",
    backstory="Expert in ESG analysis and greenwashing detection",
    tools=[web_scraper, document_analyzer]
)

# Execute research - fully monitored by AgentOps
crew = Crew(agents=[research_agent], tasks=[research_task])
result = crew.kickoff()  # Comprehensive monitoring active

print(f"Monitor session: {manager.get_session_url()}")
```

### Advanced Session Management

```python
from eko.agent.crewai.observability import agentops_manager

# Custom initialization with production settings
success = agentops_manager.initialize(
    api_key="production-agentops-key",
    tags=["production", "esg-analysis", "high-priority"]
)

if success:
    # Dynamic tagging during research workflow
    agentops_manager.tag_session(["nike-analysis", "sustainability-claims"])
    
    # Log custom research milestones
    agentops_manager.record_action(
        "research_milestone", 
        {"phase": "document_discovery", "documents_found": 127}
    )
    
    # Execute CrewAI research (automatically monitored)
    crew_result = execute_esg_research("Nike Inc")
    
    # End session with appropriate status
    agentops_manager.end_session("Success" if crew_result.success else "Fail")
    
    # Dashboard access for detailed analysis
    print(f"Detailed analysis: {agentops_manager.get_session_url()}")
```

### ESG Research Workflow Integration

```python
from eko.agent.crewai.observability import setup_agentops
from eko.agent.crewai.crawl.crawler import PersistentWebCrawlCrew

# Initialize monitoring for comprehensive ESG research
agentops_manager = setup_agentops(tags=[
    "comprehensive-esg", 
    "greenwashing-detection", 
    "regulatory-compliance"
])

# Execute monitored ESG research workflow
crawler = PersistentWebCrawlCrew("Tesla Inc", "sustainability_investigation_2024")

# All agent activities automatically tracked in AgentOps
research_results = crawler.run_comprehensive_research()

# Log research completion metrics
agentops_manager.record_action("research_completed", {
    "documents_analyzed": len(research_results.document_inventory),
    "insights_extracted": len(research_results.insights),
    "potential_greenwashing_flags": research_results.greenwashing_count
})

agentops_manager.end_session("Success")
```

### Error Handling and Fallback

```python
from eko.agent.crewai.observability import setup_agentops

# Attempt AgentOps integration with graceful fallback
manager = setup_agentops(tags=["production-research"])

if manager.is_available():
    print("AgentOps monitoring active - full observability enabled")
    # Research with comprehensive monitoring
else:
    print("AgentOps unavailable - research continues with local monitoring only")
    # Research continues normally with custom monitoring

# Research execution unaffected by AgentOps availability
execute_esg_research()
```

## Architecture Notes

### Integration Architecture

The observability module operates within EkoIntelligence's multi-layered monitoring ecosystem:

```mermaid
graph TB
    subgraph "EkoIntelligence Platform"
        A[CrewAI ESG Agents]
        B[Custom Monitoring System]  
        C[Analytics Database]
        D[Customer Dashboard]
    end
    
    subgraph "AgentOps Integration"
        E[AgentOpsManager]
        F[Session Management]
        G[Tag Management]
    end
    
    subgraph "AgentOps Cloud"
        H[Professional Dashboard]
        I[Session Replay]
        J[Cost Analytics]
        K[Performance Metrics]
    end
    
    A --> E
    A --> B
    E --> F
    E --> G
    F --> H
    G --> I
    B --> C
    C --> D
    H --> J
    H --> K
```

### Dual Monitoring Architecture

EkoIntelligence employs both AgentOps and custom monitoring for comprehensive coverage:

```mermaid
graph LR
    subgraph "CrewAI Agents"
        A[Research Agent]
        B[Analysis Agent] 
        C[Report Agent]
    end
    
    subgraph "Monitoring Layer"
        D[AgentOps Integration]
        E[Custom Monitoring]
    end
    
    subgraph "Data Destinations"
        F[AgentOps Cloud Dashboard]
        G[EkoIntelligence Analytics DB]
        H[Customer Dashboard]
    end
    
    A --> D
    A --> E
    B --> D
    B --> E
    C --> D
    C --> E
    
    D --> F
    E --> G
    G --> H
    
    F -.-> I[Professional Analytics]
    H -.-> J[Customer Reports]
```

### Session Lifecycle Management

```mermaid
sequenceDiagram
    participant User as Research System
    participant Manager as AgentOpsManager  
    participant AgentOps as AgentOps Cloud
    participant CrewAI as CrewAI Agents
    
    User->>Manager: setup_agentops(tags)
    Manager->>AgentOps: Initialize session
    AgentOps-->>Manager: Session ID & URL
    Manager-->>User: Manager instance
    
    User->>CrewAI: Execute research workflow
    CrewAI->>AgentOps: Automatic instrumentation
    AgentOps->>AgentOps: Track all agent activities
    
    User->>Manager: tag_session(new_tags)
    Manager->>AgentOps: Add session tags
    
    User->>Manager: record_action(milestone)
    Manager->>AgentOps: Log custom action
    
    User->>Manager: end_session("Success")
    Manager->>AgentOps: Terminate session
    AgentOps-->>Manager: Session analytics URL
```

## Known Issues

### Current Limitations

1. **Network Dependency**: AgentOps requires internet connectivity - research sessions cannot be monitored offline
2. **API Key Management**: Failed API key validation silently disables monitoring rather than raising explicit errors
3. **Cost Tracking**: No built-in budget alerts or cost thresholds in the integration layer
4. **Tag Limitations**: Session tags cannot be removed once added, only additional tags can be appended

### Integration Considerations

1. **Custom Action Limits**: Heavy usage of `record_action()` may impact AgentOps rate limits
2. **Session Persistence**: AgentOps sessions are tied to application lifecycle - process restarts create new sessions
3. **Memory Overhead**: AgentOps instrumentation adds minimal but measurable memory usage to agent operations
4. **Dashboard Access**: Session URLs are only valid during active session lifetime

### Compatibility Notes

1. **CrewAI Version**: Optimal integration requires CrewAI v0.28.0+, earlier versions may have limited instrumentation
2. **Python Environment**: AgentOps requires Python 3.8+ with specific dependency versions
3. **Concurrent Sessions**: Multiple AgentOpsManager instances may conflict - use global singleton pattern

## Future Work

### Enhanced Integration Features

Based on EKO-238 "Agentic Scraper" project requirements and platform evolution:

1. **Advanced Cost Management**
   - Budget threshold monitoring with automatic alerts
   - Cost allocation across different research projects and entities
   - LLM usage optimization recommendations based on AgentOps analytics
   - Integration with EkoIntelligence billing and resource management

2. **Research-Specific Analytics**
   - ESG research quality metrics integration with AgentOps custom attributes
   - Greenwashing detection confidence scoring in session analytics
   - Document discovery success rate tracking and optimization
   - Research completeness metrics and gap analysis integration

3. **Enhanced Session Management**
   - Persistent session continuation across application restarts
   - Session archiving and historical analysis capabilities
   - Research project grouping with hierarchical session organization
   - Automated session cleanup and retention policy management

### Dashboard Integration Enhancements

1. **EkoIntelligence Dashboard Integration**
   - AgentOps metrics displayed within customer dashboard
   - Real-time research progress indicators powered by AgentOps data
   - Combined local and cloud monitoring views for comprehensive observability
   - Executive reporting with AgentOps cost and performance summaries

2. **Admin Monitoring Capabilities**
   - Multi-tenant session monitoring for different customer organizations
   - Resource usage analytics across all research activities
   - Agent performance benchmarking and optimization recommendations
   - Automated incident detection and alerting for failed research sessions

### Advanced AI Agent Monitoring

1. **Multi-Agent Workflow Analytics**
   - Agent collaboration efficiency metrics and optimization insights
   - Task completion success rates across different agent types
   - Inter-agent communication pattern analysis and workflow optimization
   - Research quality correlation with agent performance metrics

2. **Predictive Analytics Integration**
   - Research time estimation based on historical AgentOps performance data
   - Failure prediction and preventive recommendations
   - Cost forecasting for large-scale ESG research initiatives
   - Agent performance trend analysis and capacity planning

## Troubleshooting

### Common Integration Issues

#### AgentOps Not Initializing

**Symptoms**: No monitoring data appearing in AgentOps dashboard, silent initialization failures

**Diagnosis**:
```python
from eko.agent.crewai.observability import agentops_manager
import os

# Check API key availability
print(f"API Key present: {'AGENTOPS_API_KEY' in os.environ}")
print(f"API Key value: {os.getenv('AGENTOPS_API_KEY', 'NOT_SET')[:10]}...")

# Test manager initialization
success = agentops_manager.initialize()
print(f"Initialization successful: {success}")
print(f"Manager available: {agentops_manager.is_available()}")
```

**Solutions**:
- Verify `AGENTOPS_API_KEY` environment variable is set correctly
- Check internet connectivity to AgentOps cloud services
- Ensure CrewAI version compatibility (v0.28.0+)
- Install AgentOps with correct dependencies: `pip install 'crewai[agentops]'`

#### Missing Agent Activity Data

**Symptoms**: Session appears in dashboard but shows no agent activity or tool usage

**Diagnosis**:
```python
# Verify AgentOps is initialized before CrewAI usage
from eko.agent.crewai.observability import setup_agentops
from crewai import Agent

# IMPORTANT: Initialize AgentOps BEFORE creating agents
manager = setup_agentops()
print(f"Monitoring active: {manager.is_available()}")

# Create agents after AgentOps initialization
agent = Agent(role="Test", goal="Test", backstory="Test")
```

**Solutions**:
- Initialize AgentOps before creating any CrewAI agents or crews
- Ensure agents are using compatible LLM providers that support AgentOps instrumentation
- Verify CrewAI agents are executing tasks (not just being created)
- Check that tasks include actual LLM calls and tool usage

#### Session URL Access Issues

**Symptoms**: `get_session_url()` returns None or invalid URLs

**Diagnosis**:
```python
from eko.agent.crewai.observability import agentops_manager

print(f"Manager initialized: {agentops_manager.initialized}")
print(f"Session ID: {agentops_manager.session_id}")
print(f"Session URL: {agentops_manager.get_session_url()}")
```

**Solutions**:
- Ensure AgentOps session is successfully initialized
- Session URLs are only valid during active session lifetime
- End sessions properly to ensure data is committed to AgentOps
- Check that session ID is properly set during initialization

#### High Memory Usage

**Symptoms**: Increased memory consumption when AgentOps is enabled

**Diagnosis**:
```python
import psutil
import os
from eko.agent.crewai.observability import setup_agentops

# Monitor memory before and after AgentOps initialization
before = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024
setup_agentops()
after = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024

print(f"Memory overhead: {after - before:.2f} MB")
```

**Solutions**:
- AgentOps instrumentation adds ~10-20MB baseline memory usage
- Memory usage scales with session activity and instrumentation depth
- Consider session archiving for long-running research processes
- Monitor memory growth patterns and implement session rotation if needed

### Debug Configuration

```python
import logging
import os
from eko.agent.crewai.observability import setup_agentops

# Enable comprehensive debug logging
logging.basicConfig(level=logging.DEBUG)

# Set debug environment variables
os.environ["AGENTOPS_DEBUG"] = "true"

# Initialize with maximum verbosity
manager = setup_agentops(tags=["debug-session"])

# Monitor all AgentOps interactions
if manager.is_available():
    print("AgentOps debug mode active")
    print(f"Dashboard URL: {manager.get_session_url()}")
else:
    print("AgentOps initialization failed - check debug logs")
```

### Performance Monitoring Commands

```bash
# Monitor AgentOps integration performance
python -c "
import time
from eko.agent.crewai.observability import setup_agentops
from crewai import Agent, Task, Crew

# Time AgentOps initialization
start = time.time()
manager = setup_agentops()
init_time = time.time() - start
print(f'AgentOps initialization: {init_time:.3f}s')

# Time agent creation with monitoring
start = time.time()
agent = Agent(role='Test', goal='Test', backstory='Test')
agent_time = time.time() - start
print(f'Monitored agent creation: {agent_time:.3f}s')
"

# Check AgentOps connectivity
curl -I https://api.agentops.ai/v1/health

# Validate environment configuration
python -c "
import os
from eko.agent.crewai.observability import agentops_manager

required_vars = ['AGENTOPS_API_KEY']
for var in required_vars:
    status = 'SET' if os.getenv(var) else 'MISSING'
    print(f'{var}: {status}')

print(f'AgentOps available: {agentops_manager.is_available()}')
"
```

## FAQ

### User-Centric Questions and Answers

**Q: Do I need to modify my existing CrewAI agents to use AgentOps monitoring?**
A: No code changes required. Simply call `setup_agentops()` before creating your agents, and all CrewAI activities are automatically monitored. The integration is completely transparent to your existing agent code.

**Q: What's the difference between AgentOps and EkoIntelligence's custom monitoring?**
A: AgentOps provides professional cloud-based analytics, session replay, and cost optimization with zero configuration. Custom monitoring offers local data control, ESG-specific metrics, and offline operation. Both systems work together for comprehensive coverage.

**Q: How much does AgentOps monitoring cost and how do I track usage?**
A: AgentOps pricing depends on your subscription plan. The module automatically tracks token usage and costs in the dashboard. Use `record_action()` to log research milestones for detailed cost allocation across projects.

**Q: Can I use AgentOps monitoring offline or in air-gapped environments?**
A: No, AgentOps requires internet connectivity to function. For offline environments, the system gracefully falls back to EkoIntelligence's custom monitoring system, ensuring research continues without interruption.

**Q: How do I organize monitoring data across different ESG research projects?**
A: Use session tags during initialization: `setup_agentops(tags=["project-name", "client-name", "research-type"])`. Tags enable filtering and organization in the AgentOps dashboard for project-specific analytics.

**Q: What happens if AgentOps is unavailable during critical research?**
A: The system is designed for graceful degradation. If AgentOps fails, research continues normally with local monitoring only. No research functionality is lost due to monitoring issues.

**Q: How do I access detailed analytics for a specific research session?**
A: Use `manager.get_session_url()` to get direct dashboard access, or log into AgentOps and filter by session tags. The dashboard provides complete session replay, cost analysis, and performance metrics.

**Q: Can multiple team members monitor the same research session?**  
A: Yes, AgentOps sessions are accessible to all team members with dashboard access. Share session URLs or use consistent tagging strategies for team visibility into research progress.

**Q: How long is monitoring data retained in AgentOps?**
A: Retention depends on your AgentOps subscription plan. Data is typically retained for 30-90 days. For long-term analytics, consider exporting key metrics to EkoIntelligence's analytics database.

**Q: Does AgentOps monitoring affect research performance or speed?**
A: AgentOps adds minimal overhead (~10-20ms per LLM call). The instrumentation is optimized for production use and doesn't significantly impact research performance while providing valuable insights.

## References

### Documentation Links

- [AgentOps Official Documentation](https://docs.agentops.ai/) - Complete integration guide and API reference
- [CrewAI Monitoring Documentation](https://docs.crewai.com/concepts/monitoring) - CrewAI framework observability concepts
- [AgentOps GitHub Repository](https://github.com/agentops-ai/agentops) - Open source AgentOps SDK implementation
- [CrewAI AgentOps Integration Guide](https://docs.crewai.com/how-to/agentops-observability) - Official CrewAI integration documentation

### Related Code Files

- [`agentops_setup.py`](./agentops_setup.py) - Core AgentOpsManager implementation and setup functions
- [`__init__.py`](./__init__.py) - Module exports and comprehensive integration documentation
- [Custom Monitoring System](../monitoring/README.md) - EkoIntelligence local monitoring infrastructure
- [CrewAI Crawler System](../crawl/README.md) - Multi-agent ESG research system using observability

### EkoIntelligence Platform Integration

- [CrewAI ESG Research Agents](../crawl/agents.py) - Specialized agents monitored through AgentOps
- [Research Task Pipeline](../crawl/tasks.py) - Research workflows with automatic monitoring
- [Analytics Database Schema](../../../db/__init__.py) - Integration with platform analytics
- [Customer Dashboard](../../../../../apps/customer/) - Frontend integration and monitoring display

### External Dependencies

- [AgentOps Cloud Platform](https://www.agentops.ai/) - Professional AI agent monitoring service
- [CrewAI Framework](https://crewai.com/) - Multi-agent AI framework with native AgentOps support
- [Google Gemini API](https://ai.google.dev/gemini-api/docs) - LLM provider with AgentOps instrumentation
- [Loguru Logging](https://loguru.readthedocs.io/en/stable/) - Python logging framework for error tracking

### Linear Project Integration

- [EKO-238: Agentic Scraper](https://linear.app/ekointelligence/issue/EKO-238/agentic-scraper) - Current project including observability improvements
- [CrewAI Monitoring Project](../PLAN.md) - Comprehensive monitoring dashboard development plan
- [ESG Research Pipeline](../../analysis_v2/) - Integration with broader ESG analysis platform

---

## Changelog

### 2025-07-30

- **Created comprehensive README.md** for AgentOps observability integration module
- **Documented complete AgentOps integration architecture** with CrewAI framework compatibility
- **Added detailed AgentOpsManager class documentation** including all methods and capabilities
- **Included comprehensive usage examples** covering basic setup, advanced management, and ESG workflow integration
- **Provided extensive troubleshooting section** with common issues, debug commands, and performance monitoring
- **Added detailed FAQ section** addressing user concerns and implementation questions
- **Documented dual monitoring architecture** showing integration with existing custom monitoring system
- **Included Mermaid diagrams** for system architecture and session lifecycle visualization
- **Provided complete dependency documentation** with installation and environment requirements
- **Added future work section** aligned with EKO-238 project requirements and platform evolution
- **Comprehensive reference documentation** linking to AgentOps, CrewAI, and platform resources

---

(c) All rights reserved ekoIntelligence 2025