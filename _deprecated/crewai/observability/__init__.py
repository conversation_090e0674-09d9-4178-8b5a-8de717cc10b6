"""
CrewAI AgentOps Observability Integration for EkoIntelligence ESG Analysis Platform

This package provides streamlined observability capabilities for CrewAI agents within the
EkoIntelligence ESG analysis platform using the recommended AgentOps integration. AgentOps
is the industry-standard observability solution for AI agents, offering comprehensive
monitoring, analytics, and debugging capabilities specifically designed for CrewAI workflows.

## Core Purpose
The observability package enables **production-grade monitoring** of CrewAI agents during
ESG research tasks, corporate document analysis, and sustainability verification workflows.
Unlike custom monitoring solutions, AgentOps provides:

- **Zero-configuration Integration**: Automatic instrumentation of CrewAI agents and tools
- **Industry-standard Metrics**: Token usage, costs, performance timing, error rates
- **Advanced Analytics**: Session replay, conversation flow analysis, agent behavior insights
- **Production Dashboard**: Real-time monitoring with alerting and performance optimization
- **Cost Optimization**: Detailed LLM usage tracking for budget management

## System Architecture Integration
This observability package integrates with the broader EkoIntelligence system architecture:

- **Analytics Backend**: Python system for ESG document processing and agent orchestration
- **AgentOps Integration**: This package - provides seamless connection to AgentOps monitoring
- **CrewAI Agent Layer**: ESG research agents (`/backoffice/src/eko/agent/crewai/crawl/`) automatically monitored
- **Dashboard Access**: AgentOps web dashboard for real-time monitoring and analytics
- **Database Independence**: No local database storage - all data handled by AgentOps cloud service

## AgentOps vs Custom Monitoring
The EkoIntelligence platform also includes a custom monitoring system (`/backoffice/src/eko/agent/crewai/monitoring/`)
for local analytics. This AgentOps package provides complementary benefits:

**AgentOps Advantages**:
- **Zero Configuration**: Automatic instrumentation without code changes
- **Industry Expertise**: Purpose-built for AI agent monitoring by domain experts  
- **Advanced Analytics**: Conversation flow analysis, agent behavior insights
- **Cloud Dashboard**: Professional web interface with real-time updates
- **Cost Optimization**: Built-in LLM cost analysis and optimization recommendations

**Custom Monitoring Advantages**:
- **Local Control**: Data stored in local analytics database  
- **Custom Metrics**: Integration with existing EkoIntelligence dashboard system
- **Offline Operation**: No dependency on external services
- **Full Customization**: Tailored metrics for specific ESG analysis workflows

## Key Components

### AgentOpsManager
**Purpose**: Central manager for AgentOps session lifecycle and configuration
**Location**: `agentops_setup.py`

**Core Capabilities**:
- **Session Management**: Initialize, configure, and terminate AgentOps monitoring sessions
- **API Key Handling**: Secure API key management with environment variable support
- **Tag Management**: Session tagging for categorization and filtering
- **Error Handling**: Graceful degradation when AgentOps is unavailable
- **Dashboard Integration**: Direct links to AgentOps dashboard for session analysis

### setup_agentops() Function
**Purpose**: Streamlined setup function for quick AgentOps integration
**Usage**: Single function call to enable monitoring across all CrewAI agents

**Features**:
- **Auto-initialization**: Detects environment configuration and initializes appropriately
- **Session Tagging**: Automatic tagging with platform and workflow identifiers
- **Error Recovery**: Continues operation even if AgentOps setup fails
- **Return Management**: Returns configured AgentOpsManager for advanced usage

## Integration with CrewAI Framework
AgentOps is the **officially recommended** observability solution for CrewAI, providing:

- **Native Integration**: Built into CrewAI framework for seamless operation
- **Automatic Discovery**: Detects CrewAI agents, tasks, and tools without configuration
- **Event Capture**: Comprehensive logging of agent decisions, tool usage, and LLM calls
- **Performance Metrics**: Real-time monitoring of agent execution times and success rates
- **Session Replay**: Complete playback of agent conversations and decision flows

## Environment Configuration
AgentOps requires minimal configuration for operation:

```bash
# Required environment variable
export AGENTOPS_API_KEY="your-agentops-api-key"

# Optional: Custom configuration
export AGENTOPS_TAGS="esg-analysis,production"
```

## Usage Patterns

### Basic Setup (Recommended)
```python
from eko.agent.crewai.observability import setup_agentops

# Enable monitoring for all CrewAI agents
manager = setup_agentops(tags=["esg-analysis", "document-research"])

# CrewAI agents automatically monitored
# No additional code changes required
```

### Advanced Management
```python  
from eko.agent.crewai.observability import agentops_manager

# Initialize with custom configuration
agentops_manager.initialize(api_key="custom-key", tags=["production"])

# Add session tags during operation
agentops_manager.tag_session(["high-priority", "nike-analysis"])

# Get dashboard URL for session analysis
dashboard_url = agentops_manager.get_session_url()

# End session with status
agentops_manager.end_session("Success")
```

## Benefits for ESG Analysis Workflows
AgentOps provides specific advantages for EkoIntelligence's ESG analysis use cases:

- **Research Workflow Monitoring**: Track agent performance during document analysis and fact-checking
- **Cost Management**: Monitor LLM usage across multiple ESG research sessions  
- **Quality Assurance**: Identify agents making poor decisions or generating unreliable outputs
- **Performance Optimization**: Analyze agent execution times and optimize for faster analysis
- **Error Detection**: Rapid identification and debugging of agent failures during critical research
- **Compliance Tracking**: Session logging for audit trails and quality control processes

## Related Documentation
- **AgentOps Documentation**: https://docs.agentops.ai/ - Official AgentOps integration guide
- **CrewAI Observability**: https://docs.crewai.com/concepts/monitoring - CrewAI monitoring concepts
- **EkoIntelligence Agent System**: `/backoffice/src/eko/agent/crewai/crawl/` - ESG research agent implementation
- **Custom Monitoring**: `/backoffice/src/eko/agent/crewai/monitoring/` - Local monitoring system

## Key Exports
- **AgentOpsManager**: Core management class for AgentOps session lifecycle
- **setup_agentops**: Streamlined setup function for quick integration
- **agentops_manager**: Global singleton instance for application-wide use

@see https://docs.agentops.ai/ AgentOps Official Documentation
@see https://docs.crewai.com/concepts/monitoring CrewAI Monitoring Documentation
@see /backoffice/src/eko/agent/crewai/crawl/ ESG Research Agent Implementation
@see /backoffice/src/eko/agent/crewai/monitoring/ Custom Monitoring System
<AUTHOR>
@updated 2025-07-23
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from .agentops_setup import AgentOpsManager, setup_agentops, agentops_manager

__all__ = ['AgentOpsManager', 'setup_agentops', 'agentops_manager']