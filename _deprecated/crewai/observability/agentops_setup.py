"""
AgentOps Integration Manager for CrewAI Observability in EkoIntelligence ESG Analysis Platform

This module provides comprehensive setup and configuration management for AgentOps monitoring
of CrewAI agents within the EkoIntelligence ESG analysis platform. AgentOps is the recommended
industry-standard observability solution for AI agents, offering automated instrumentation,
performance analytics, cost tracking, and session replay capabilities specifically optimized
for CrewAI workflows and multi-agent systems.

## Core Functionality
- **Zero-Configuration Setup**: Automatic detection and initialization of AgentOps for CrewAI agents
- **Session Management**: Complete lifecycle management including initialization, tagging, and termination
- **Error Resilience**: Graceful degradation when AgentOps is unavailable or misconfigured
- **API Key Management**: Secure handling of API keys through environment variables
- **Dashboard Integration**: Direct session URL generation for AgentOps web dashboard access
- **Tag Management**: Session categorization and filtering through custom tags

## Integration Architecture
This module serves as the primary integration point between the EkoIntelligence platform
and AgentOps cloud services within the broader system architecture:

- **Analytics Backend**: Python system (`/backoffice/src/eko/`) processes ESG documents and orchestrates agents
- **CrewAI Agent Layer**: ESG research agents (`/crawl/agents.py`) automatically monitored via this integration
- **AgentOps Cloud**: External monitoring service providing real-time analytics and session replay
- **Dashboard Access**: Web-based monitoring interface for debugging and performance optimization
- **Database Independence**: No local storage required - all telemetry handled by AgentOps service

## AgentOps vs Custom Monitoring
The EkoIntelligence platform includes both AgentOps integration (this module) and custom
monitoring (`/monitoring/`) to provide comprehensive observability coverage:

**AgentOps Benefits**:
- **Professional Dashboard**: Industry-standard web interface with advanced analytics
- **Automatic Instrumentation**: Zero-code-change integration with CrewAI framework
- **Session Replay**: Complete conversation and decision flow playback capabilities
- **Cost Optimization**: Built-in LLM usage analysis and budget management tools
- **Community Support**: Backed by CrewAI team with extensive documentation and support

**Custom Monitoring Benefits**:
- **Local Control**: Data stored in EkoIntelligence analytics database for offline access
- **ESG-Specific Metrics**: Tailored analytics for sustainability analysis workflows
- **Integration Depth**: Deep integration with existing EkoIntelligence dashboard systems
- **Customization**: Specialized tracking for entity analysis, claims verification, and greenwashing detection

## CrewAI Framework Integration
AgentOps provides native integration with the CrewAI multi-agent framework through:

- **Automatic Discovery**: Detects and instruments all CrewAI agents, tasks, and tools without configuration
- **Event Capture**: Comprehensive logging of agent decisions, inter-agent communication, and tool usage
- **Performance Metrics**: Real-time monitoring of task execution times, success rates, and resource usage
- **LLM Tracking**: Detailed token usage, costs, and model performance across all agent interactions
- **Error Analysis**: Automatic capture and categorization of agent failures and recovery patterns

## ESG Analysis Use Cases
This observability integration provides specific advantages for EkoIntelligence's ESG analysis workflows:

- **Research Quality Assurance**: Monitor agent performance during corporate document analysis
- **Cost Management**: Track LLM usage across ESG research sessions for budget optimization
- **Greenwashing Detection**: Analyze agent decision patterns in sustainability claim verification
- **Performance Optimization**: Identify bottlenecks in multi-agent ESG research workflows
- **Compliance Logging**: Maintain audit trails for regulatory compliance and quality control
- **Error Recovery**: Rapid identification and debugging of agent failures during critical analysis

## Key Components

### AgentOpsManager Class
Central management class providing complete AgentOps session lifecycle control with error handling,
API key management, and dashboard integration capabilities for production ESG analysis workflows.

### setup_agentops() Function  
Streamlined initialization function enabling AgentOps monitoring across all CrewAI agents with
a single function call, including automatic environment detection and graceful error handling.

### Global Manager Instance
Application-wide singleton instance (`agentops_manager`) providing consistent access to AgentOps
functionality across the entire EkoIntelligence platform architecture.

## Environment Configuration
Minimal configuration required through environment variables:

```bash
# Required: AgentOps API key for authentication
export AGENTOPS_API_KEY="your-agentops-api-key"

# Optional: Default session tags for categorization
export AGENTOPS_DEFAULT_TAGS="esg-analysis,production"
```

## Usage Patterns

### Basic Integration (Recommended)
```python
from eko.agent.crewai.observability import setup_agentops

# Enable monitoring for all CrewAI agents
manager = setup_agentops(tags=["entity-analysis", "nike-research"])

# CrewAI agents automatically monitored - no code changes required
crew = Crew(agents=[research_agent, analysis_agent], tasks=[research_task])
result = crew.kickoff()  # Fully monitored by AgentOps
```

### Advanced Session Management
```python
from eko.agent.crewai.observability import agentops_manager

# Custom initialization with specific configuration
agentops_manager.initialize(api_key="production-key", tags=["high-priority"])

# Dynamic session tagging during research
agentops_manager.tag_session(["greenwashing-analysis", "coca-cola"])

# Monitor session in dashboard
print(f"Monitor at: {agentops_manager.get_session_url()}")

# End session with result status
agentops_manager.end_session("Success" if analysis_completed else "Fail")
```

## System Requirements
- **Python Dependencies**: `agentops` library with optional CrewAI integration (`pip install 'crewai[agentops]'`)
- **Network Access**: Internet connectivity for AgentOps cloud service communication
- **API Key**: Valid AgentOps account and API key for service authentication
- **CrewAI Framework**: Compatible with CrewAI v0.28.0+ for optimal integration

## Related Documentation
- **AgentOps Official Documentation**: https://docs.agentops.ai/ - Complete integration guide and API reference
- **CrewAI Monitoring Guide**: https://docs.crewai.com/concepts/monitoring - CrewAI observability concepts
- **EkoIntelligence Agent Architecture**: `/backoffice/src/eko/agent/crewai/crawl/` - ESG research agent implementation
- **Custom Monitoring System**: `/backoffice/src/eko/agent/crewai/monitoring/` - Local analytics and tracking

## Key Classes and Functions
- **AgentOpsManager**: Complete session lifecycle management with error handling and dashboard integration
- **setup_agentops()**: One-function setup for immediate AgentOps integration across all agents
- **agentops_manager**: Global singleton instance for application-wide AgentOps access

@see https://docs.agentops.ai/ AgentOps Official Documentation and API Reference
@see https://docs.crewai.com/concepts/monitoring CrewAI Framework Monitoring Documentation  
@see https://github.com/agentops-ai/agentops AgentOps Open Source Repository
@see /backoffice/src/eko/agent/crewai/crawl/agents.py ESG Research Agent Implementation
@see /backoffice/src/eko/agent/crewai/monitoring/ Custom EkoIntelligence Monitoring System
<AUTHOR>
@updated 2025-07-23
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import os
from typing import Optional

from loguru import logger

try:
    import agentops
    AGENTOPS_AVAILABLE = True
except ImportError:
    AGENTOPS_AVAILABLE = False
    logger.warning("AgentOps not installed. Run: pip install 'crewai[agentops]' for monitoring")


class AgentOpsManager:
    """
    Manager for AgentOps observability integration.
    
    This class handles the initialization and management of AgentOps
    for monitoring CrewAI agent activities.
    """
    
    def __init__(self):
        self.session_id = None
        self.initialized = False
    
    def initialize(self, api_key: Optional[str] = None, tags: Optional[list] = None) -> bool:
        """
        Initialize AgentOps monitoring.
        
        Args:
            api_key: Optional API key (will use env var if not provided)
            tags: Optional tags to add to the session
            
        Returns:
            bool: True if initialization was successful
        """
        if not AGENTOPS_AVAILABLE:
            logger.warning("AgentOps not available - monitoring disabled")
            return False
        
        try:
            # Use provided API key or environment variable
            if api_key:
                os.environ['AGENTOPS_API_KEY'] = api_key
            
            # Check if API key is available
            if not os.getenv('AGENTOPS_API_KEY'):
                logger.warning("AGENTOPS_API_KEY not found - monitoring disabled")
                return False
            
            # Initialize AgentOps
            session = agentops.init(tags=tags or [])
            
            if session:
                self.session_id = session.session_id
                self.initialized = True
                logger.info(f"AgentOps initialized successfully - Session ID: {self.session_id}")
                return True
            else:
                logger.error("Failed to initialize AgentOps session")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing AgentOps: {e}")
            return False
    
    def tag_session(self, tags: list):
        """
        Add tags to the current AgentOps session.
        
        Args:
            tags: List of tags to add
        """
        if not self.initialized:
            logger.warning("AgentOps not initialized - cannot add tags")
            return
        
        try:
            agentops.add_tags(tags)
            logger.debug(f"Added tags to session: {tags}")
        except Exception as e:
            logger.error(f"Error adding tags to AgentOps session: {e}")
    
    def end_session(self, end_state: str = "Success"):
        """
        End the current AgentOps session.
        
        Args:
            end_state: The end state of the session (Success, Fail, Indeterminate)
        """
        if not self.initialized:
            return
        
        try:
            agentops.end_session(end_state)
            logger.info(f"AgentOps session ended with state: {end_state}")
            self.initialized = False
            self.session_id = None
        except Exception as e:
            logger.error(f"Error ending AgentOps session: {e}")
    
    def record_action(self, action_type: str, params: dict = None):
        """
        Record a custom action in AgentOps.
        
        Args:
            action_type: Type of action being recorded
            params: Optional parameters for the action
        """
        if not self.initialized:
            return
        
        try:
            agentops.record_action(action_type, params or {})
            logger.debug(f"Recorded action in AgentOps: {action_type}")
        except Exception as e:
            logger.error(f"Error recording action in AgentOps: {e}")
    
    def get_session_url(self) -> Optional[str]:
        """
        Get the URL for viewing the current session in AgentOps dashboard.
        
        Returns:
            Optional[str]: URL to the session or None if not available
        """
        if not self.initialized or not self.session_id:
            return None
        
        return f"https://app.agentops.ai/drilldown?session_id={self.session_id}"
    
    def is_available(self) -> bool:
        """Check if AgentOps is available and initialized."""
        return AGENTOPS_AVAILABLE and self.initialized


# Global instance for easy access
agentops_manager = AgentOpsManager()


def setup_agentops(api_key: Optional[str] = None, tags: Optional[list] = None) -> AgentOpsManager:
    """
    Setup AgentOps observability for CrewAI.
    
    Args:
        api_key: Optional API key (will use env var if not provided)
        tags: Optional tags to add to the session
        
    Returns:
        AgentOpsManager instance
    """
    agentops_manager.initialize(api_key, tags)
    return agentops_manager