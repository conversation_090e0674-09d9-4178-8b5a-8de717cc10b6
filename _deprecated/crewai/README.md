# EkoIntelligence CrewAI Multi-Agent System

## Overview

The EkoIntelligence CrewAI Multi-Agent System is a sophisticated AI-powered research and analysis platform designed specifically for comprehensive ESG (Environmental, Social, Governance) intelligence gathering. Built on the CrewAI framework, this system orchestrates specialized AI agents to conduct exhaustive corporate sustainability research, automated greenwashing detection, and regulatory compliance monitoring across multiple data sources and regulatory databases.

This system represents EkoIntelligence's core AI research infrastructure, enabling automated corporate ESG analysis at scale through intelligent multi-agent workflows that combine web crawling, document analysis, regulatory data mining, and AI-powered insights generation. The platform serves as the foundational technology for EkoIntelligence's ESG research and corporate sustainability analysis services.

**Key Capabilities:**
- **Multi-Agent ESG Research**: Orchestrated teams of specialized AI agents for comprehensive corporate sustainability investigation
- **Intelligent Web Crawling**: Advanced document discovery and analysis across corporate websites, regulatory databases, and third-party sources
- **Greenwashing Detection**: AI-powered analysis to identify misleading sustainability claims and corporate misconduct
- **Regulatory Integration**: Direct access to SEC, Companies House, GLEIF, and other authoritative regulatory data sources
- **Comprehensive Monitoring**: Real-time agent performance tracking, cost management, and operational observability
- **Enterprise Observability**: Integration with AgentOps and custom monitoring for production-grade insights

## Specification

### Core System Requirements

The CrewAI Multi-Agent System is designed as EkoIntelligence's primary AI research platform with the following architectural specifications:

#### Multi-Agent Framework Architecture
- **CrewAI Foundation**: Built on CrewAI's enterprise-grade multi-agent orchestration framework
- **Specialized Agent Teams**: Distinct agent roles (Research, Journalism, Analysis, Summary, Report) with role-specific capabilities
- **Intelligent Task Coordination**: Advanced workflow orchestration with dependency management and parallel execution
- **Persistent Memory System**: Dual-storage architecture (JSON + PostgreSQL) for session continuity and knowledge retention
- **Fault-Tolerant Design**: Resilient operation with graceful degradation and automatic recovery mechanisms

#### ESG Research Methodology
- **Systematic Discovery**: 11-phase research workflow covering corporate, regulatory, and third-party intelligence sources
- **Dynamic Intelligence Gathering**: AI-driven search query evolution and gap analysis based on discovered content
- **Comprehensive Source Coverage**: Integration with corporate websites, regulatory filings, news sources, and NGO databases
- **Quality Assurance**: Automated verification of research completeness and accuracy validation
- **Professional Reporting**: Stakeholder-ready ESG reports with complete audit trails and methodology documentation

#### Database Integration Requirements
- **Analytics Database**: Primary PostgreSQL storage for agent activities, insights, and research data
- **Customer Database**: Synchronized data delivery through `xfer_` table architecture for customer-facing applications  
- **Session Management**: Complete tracking of multi-agent workflows with unique session identifiers
- **Audit Trail**: Comprehensive logging of all agent actions for compliance and debugging purposes

### Technology Integration Stack

```mermaid
graph TB
    subgraph "CrewAI Multi-Agent System"
        A[Agent Orchestration]
        B[Task Management]
        C[Tool Integration]
        D[Memory System]
        E[Monitoring & Observability]
    end

    subgraph "EkoIntelligence Platform"
        F[Analytics Database]
        G[Customer Database]
        H[ESG Analysis Pipeline]
        I[Customer Dashboard]
        J[Audit & Compliance]
    end

    subgraph "External Services"
        K[Google Gemini LLM]
        L[Regulatory APIs]
        M[News & Media APIs]
        N[AgentOps Monitoring]
        O[OpenTelemetry]
    end

    A --> F
    B --> G
    C --> L
    C --> M
    D --> F
    E --> N
    E --> O
    F --> H
    G --> I
    H --> J
    A --> K
```

## Key Components

### Core System Architecture

| Component | Purpose | Key Functionality |
|-----------|---------|-------------------|
| **[`crawl/`](./crawl/)** | Multi-agent ESG research system | Comprehensive corporate sustainability research with specialized agents |
| **[`monitoring/`](./monitoring/)** | Agent activity monitoring | Real-time performance tracking, cost analysis, and operational insights |
| **[`observability/`](./observability/)** | AgentOps integration | Professional-grade observability with industry-standard monitoring |
| **[`telemetry_collector.py`](./telemetry_collector.py)** | Telemetry data collection | Database-integrated telemetry for agent performance analysis |
| **[`monitoring.py`](./monitoring.py)** | Legacy monitoring utilities | Backward compatibility and transition support |

### Multi-Agent Research System (`crawl/`)

The heart of the EkoIntelligence platform's AI research capabilities:

**Agent Specializations:**
- **ResearchAgent**: Primary web crawling and document discovery specialist
- **JournalistAgent**: Investigative journalism and media research expert  
- **AnalysisAgent**: Strategic content analysis and pattern recognition
- **SummaryAgent**: Content organization and preliminary reporting
- **ReportAgent**: Professional stakeholder-ready report generation

**Research Workflow:**
- **Phase 1**: Systematic discovery across corporate and regulatory sources
- **Phase 2**: Media analysis and investigative journalism research
- **Phase 3**: Intelligent query evolution and gap analysis
- **Phase 4**: Content analysis and comprehensive insight extraction

**Tool Ecosystem:**
- **17+ Specialized Tools**: Web crawling, document analysis, regulatory database access
- **Memory Integration**: Persistent state sharing across all agents
- **Deduplication System**: Automatic prevention of duplicate work and document processing

### Monitoring Infrastructure (`monitoring/`, `observability/`)

**Dual Monitoring Architecture:**

```mermaid
graph LR
    subgraph "Agent Operations"
        A[CrewAI Agents]
        B[Multi-Agent Workflows]
        C[ESG Research Tasks]
    end
    
    subgraph "Monitoring Layer"
        D[Custom Monitoring System]
        E[AgentOps Integration]
        F[OpenTelemetry Instrumentation]
    end
    
    subgraph "Data Destinations"
        G[EkoIntelligence Analytics DB]
        H[AgentOps Cloud Dashboard]
        I[Customer Reporting Dashboard]
    end
    
    A --> D
    A --> E
    B --> F
    
    D --> G
    E --> H
    F --> G
    
    G --> I
    H -.-> J[Professional Analytics]
```

**Comprehensive Monitoring Features:**
- **Real-Time Performance Tracking**: Agent execution times, success rates, and resource utilization
- **Cost Management**: LLM usage tracking, budget analysis, and cost optimization recommendations
- **Session Analytics**: Complete workflow tracking with detailed execution timelines
- **Error Monitoring**: Comprehensive error capture and root cause analysis
- **Professional Dashboard**: AgentOps cloud-based monitoring with session replay capabilities

## Dependencies

### Core Framework Dependencies

- **CrewAI Framework v0.28.0+**: Multi-agent orchestration with native observability support
- **OpenTelemetry SDK**: Industry-standard distributed tracing and observability
- **PostgreSQL 13+**: Enterprise database for analytics and customer data persistence
- **Python 3.8+**: Modern Python runtime with comprehensive async/await support

### AI/LLM Integration

- **Google Gemini Models**: Primary LLM provider for content analysis and decision making
- **AgentOps Cloud Service**: Professional monitoring and analytics platform
- **Custom AI Tools**: Specialized ESG analysis and greenwashing detection algorithms

### External API Integration

- **Google Custom Search API**: Web search functionality for comprehensive research
- **Companies House API**: UK corporate registry and regulatory filing access  
- **GLEIF API**: Global Legal Entity Identifier database for corporate verification
- **SEC EDGAR API**: US securities filings and enforcement action data
- **NewsAPI**: Real-time news monitoring and media analysis
- **Wikipedia MediaWiki API**: Corporate background research and controversy identification

### EkoIntelligence Platform Integration

- **Database Layer**: Connection pooling and transaction management via `eko.db.get_bo_conn()`
- **ESG Analysis Pipeline**: Integration with `eko.analysis_v2` for downstream corporate analysis
- **Data Synchronization**: Results delivery through `xfer_` table architecture
- **Customer Interface**: Dashboard integration for research visualization and reporting

### Supporting Libraries

- **Pydantic**: Type-safe data validation and serialization for agent communications
- **Loguru**: Advanced structured logging with enterprise-grade monitoring integration
- **Requests**: HTTP client library for API integrations and web content fetching
- **BeautifulSoup**: HTML parsing and content extraction from corporate websites
- **JSONB**: PostgreSQL JSON document storage for flexible research data persistence

## Usage Examples

### Comprehensive ESG Research Session

```python
from eko.agent.crewai.crawl.crawler import PersistentWebCrawlCrew
from eko.agent.crewai.observability import setup_agentops

# Initialize professional monitoring for research session
agentops_manager = setup_agentops(tags=[
    "esg-analysis", 
    "corporate-research", 
    "greenwashing-detection"
])

# Execute comprehensive multi-agent ESG research
crawler = PersistentWebCrawlCrew(
    company_name="Tesla Inc", 
    session_id="tesla_sustainability_audit_2024_q3"
)

# Run complete research workflow with all specialized agents
research_results = crawler.run_comprehensive_research()

# Access detailed research outcomes
print(f"Research Summary: {research_results.summary}")
print(f"Documents Discovered: {len(research_results.document_inventory)}")
print(f"ESG Insights Generated: {len(research_results.insights)}")
print(f"Potential Greenwashing Flags: {research_results.greenwashing_count}")

# Monitor session performance and costs
print(f"Session Analytics: {agentops_manager.get_session_url()}")
agentops_manager.end_session("Success")
```

### Custom Multi-Agent Workflow

```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.agents import ResearchAgent, AnalysisAgent, ReportAgent
from eko.agent.crewai.crawl.tasks import create_comprehensive_research_tasks
from eko.agent.crewai.crawl.tools import get_web_tools, get_analyst_tools
from eko.agent.crewai.monitoring import create_event_logger
from crewai import Crew, Process

# Initialize persistent memory for coordinated research
memory_manager = CrewMemoryManager(
    company_name="Nike Inc", 
    session_id="nike_supply_chain_analysis_2024"
)

# Create monitoring infrastructure
event_logger = create_event_logger("nike_supply_chain_analysis_2024")

# Create specialized agents with role-specific tool collections
research_agent = ResearchAgent(get_web_tools(memory_manager))
analysis_agent = AnalysisAgent(get_analyst_tools(memory_manager))
report_agent = ReportAgent(get_analyst_tools(memory_manager))

# Generate comprehensive research workflow
research_tasks = create_comprehensive_research_tasks(
    memory_manager, 
    "Nike Inc",
    focus_areas=["supply_chain", "labor_practices", "environmental_impact"]
)

# Execute coordinated multi-agent research with monitoring
crew = Crew(
    agents=[research_agent, analysis_agent, report_agent],
    tasks=research_tasks,
    process=Process.sequential,
    memory=True,
    verbose=True
)

results = crew.kickoff()

# Generate executive summary report
final_report = report_agent.generate_executive_summary(results)
print(f"Executive Report Generated: {len(final_report)} pages")
```

### Performance Monitoring and Cost Analysis

```python
from eko.agent.crewai.monitoring import AgentMonitoringService
from eko.agent.crewai.observability import agentops_manager

# Initialize comprehensive monitoring service
monitoring_service = AgentMonitoringService("quarterly_esg_analysis_2024_q3")

# Track custom research milestones
monitoring_service.log_event(
    agent_name="ResearchAgent",
    event_type="milestone_reached",
    event_data={
        "milestone": "regulatory_filings_complete",
        "documents_processed": 247,
        "insights_extracted": 89,
        "processing_time_minutes": 45
    }
)

# Monitor LLM costs and usage patterns
llm_costs = monitoring_service.get_session_costs()
print(f"Total LLM Costs: ${llm_costs.total_usd:.2f}")
print(f"Average Cost per Document: ${llm_costs.per_document:.4f}")
print(f"Most Expensive Operation: {llm_costs.max_cost_operation}")

# Generate performance analytics report
performance_report = monitoring_service.generate_performance_report()
agentops_manager.record_action("performance_analysis", performance_report)
```

## Architecture Notes

### Multi-Agent Coordination Architecture

```mermaid
sequenceDiagram
    participant User as Research System
    participant Controller as Crew Controller
    participant RA as Research Agent
    participant JA as Journalist Agent
    participant AA as Analysis Agent
    participant MM as Memory Manager
    participant DB as Analytics Database
    participant AO as AgentOps Cloud

    User->>Controller: Initialize ESG research session
    Controller->>MM: Create persistent session memory
    Controller->>AO: Initialize monitoring session
    
    Controller->>RA: Assign corporate discovery tasks
    RA->>MM: Track URLs and documents
    RA->>DB: Store research data
    RA->>AO: Report agent activities
    
    Controller->>JA: Assign investigative journalism tasks
    JA->>MM: Track news and media insights
    JA->>DB: Store journalism findings
    JA->>AO: Report research progress
    
    Controller->>AA: Assign content analysis tasks
    AA->>MM: Load all discovered content
    AA->>DB: Store structured insights
    AA->>AO: Report analysis completion
    
    Controller->>User: Return comprehensive results
    
    Note over MM,DB: Continuous synchronization ensures data persistence
    Note over AO: Professional monitoring and session replay available
```

### Database Integration Architecture

```mermaid
erDiagram
    agent_sessions {
        text session_id PK
        text company_name
        jsonb memory_data
        timestamptz created_at
        timestamptz updated_at
    }

    agent_execution_events {
        bigint id PK
        text session_id FK
        text agent_name
        text event_type
        jsonb event_data
        text task_name
        text tool_name
        integer run_id
        timestamptz created_at
    }

    agent_insights {
        bigint id PK
        text session_id FK
        text company_name
        text category
        text behavior_type
        text description
        text source_url
        text source_title
        double_precision confidence
        jsonb metadata
        timestamptz extracted_at
        timestamptz created_at
    }

    agent_llm_calls {
        bigint id PK
        text session_id FK
        text agent_name
        text model_name
        integer prompt_tokens
        integer completion_tokens
        integer total_tokens
        numeric cost_usd
        jsonb request_data
        jsonb response_data
        integer duration_ms
        timestamptz call_timestamp
        timestamptz created_at
    }

    agent_tool_usage {
        bigint id PK
        text session_id FK
        text agent_name
        text tool_name
        jsonb tool_input
        jsonb tool_output
        boolean success
        text error_message
        integer duration_ms
        timestamptz call_timestamp
        timestamptz created_at
    }

    agent_reports {
        bigint id PK
        text session_id FK
        text company_name
        text summary
        integer insights_count
        integer visited_urls_count
        integer downloaded_files_count
        jsonb report_data
        timestamptz created_at
    }

    agent_sessions ||--o{ agent_execution_events: "generates"
    agent_sessions ||--o{ agent_insights: "produces"
    agent_sessions ||--o{ agent_llm_calls: "tracks"
    agent_sessions ||--o{ agent_tool_usage: "monitors"
    agent_sessions ||--o{ agent_reports: "creates"
```

### Monitoring System Integration

The CrewAI system employs a comprehensive dual-monitoring architecture:

```mermaid
graph TB
    subgraph "Agent Execution Layer"
        A[CrewAI Agents]
        B[ESG Research Tasks]
        C[Tool Execution]
    end
    
    subgraph "Monitoring Infrastructure"
        D[Custom Event Logger]
        E[AgentOps Integration]
        F[OpenTelemetry Spans]
        G[Telemetry Collector]
    end
    
    subgraph "Data Storage & Analytics"
        H[Analytics PostgreSQL]
        I[AgentOps Cloud]
        J[Customer Dashboard]
        K[Admin Monitoring]
    end
    
    A --> D
    A --> E
    B --> F
    C --> G
    
    D --> H
    E --> I
    F --> H
    G --> H
    
    H --> J
    H --> K
    I --> K
    
    J -.-> L[Customer Reports]
    K -.-> M[Operational Analytics]
```

## Known Issues

Based on Linear ticket analysis (EKO-238: Agentic Scraper) and code inspection:

### Current System Limitations

1. **Memory File Performance**: Large JSON memory files (>10MB) can impact performance during concurrent agent access
2. **API Rate Limiting**: External APIs (Google Search, SEC, etc.) may throttle during intensive research sessions
3. **Concurrent Session Management**: Potential race conditions when multiple agents update shared memory simultaneously
4. **Large Document Processing**: PDFs over 50MB may cause memory issues during content extraction

### Integration Challenges

1. **Session Lifecycle Management**: No automatic cleanup mechanism for completed or abandoned research sessions
2. **Cost Tracking Integration**: Limited built-in cost optimization recommendations based on usage patterns
3. **Real-Time Dashboard Updates**: WebSocket integration needed for live session monitoring
4. **Error Recovery**: Limited automated recovery mechanisms for corrupted memory files or failed API responses

### Monitoring System Gaps

1. **Performance Metrics Collection**: Need more comprehensive metrics for tool execution optimization
2. **Predictive Analytics**: Missing ML-based performance prediction and anomaly detection
3. **Resource Optimization**: Limited automated resource allocation recommendations
4. **Compliance Reporting**: Enhanced audit trail requirements for regulatory compliance

## Future Work

### EKO-238 Agentic Scraper Project Enhancements

Based on the current Linear project scope and platform evolution requirements:

#### Admin Dashboard and Monitoring (Priority: High)
- **Real-Time Session Management**: Live monitoring interface for active CrewAI research sessions
- **Performance Analytics Dashboard**: Comprehensive metrics visualization for agent efficiency and cost analysis
- **Cost Management System**: Advanced LLM usage tracking with budget alerts and optimization recommendations
- **Session Lifecycle Management**: Automated cleanup, archiving, and retention policy management
- **Multi-Tenant Monitoring**: Role-based access control for different customer organizations

#### Advanced AI Capabilities (Priority: Medium)
- **Enhanced ESG Classification**: Machine learning models for more nuanced ESG insight categorization
- **Greenwashing Detection Algorithms**: Specialized AI models for identifying misleading sustainability claims
- **Multi-Language Support**: Enhanced processing capabilities for non-English ESG documents and regulatory filings
- **Trend Analysis Integration**: Time-series analysis of corporate ESG performance patterns and disclosure evolution
- **Risk Scoring Integration**: Direct integration with EkoIntelligence's corporate risk assessment algorithms

#### Scalability and Performance Improvements (Priority: Medium)
- **Distributed Memory Architecture**: Support for distributed memory across multiple compute instances
- **Advanced Caching Layer**: Redis integration for frequently accessed memory data and API response caching
- **Batch Processing Optimization**: Enhanced database synchronization with intelligent batched memory updates
- **Load Balancing Support**: Multi-instance deployment capabilities with shared database coordination
- **Archive System**: Automatic archiving of completed research sessions to cold storage

#### Research Workflow Enhancements (Priority: Low)
- **Dynamic Task Generation**: AI-powered task creation based on discovered content and identified research gaps
- **Quality Assurance Automation**: Automated verification of research completeness and accuracy validation
- **Collaborative Research Support**: Human-AI collaborative research workflows with expert review integration
- **Industry Specialization**: Industry-specific research templates and specialized knowledge bases
- **Regulatory Compliance Enhancement**: Advanced integration with regulatory requirement tracking and compliance monitoring

## Troubleshooting

### Common System Issues

#### Agent Session Initialization Problems

**Symptoms**: Agents failing to start, memory initialization errors, database connection issues

**Diagnosis**:
```bash
# Check database connectivity
cd backoffice && ./bin/run_in_db.sh "SELECT 1"

# Verify agent session table status
cd backoffice && ./bin/run_in_db.sh "
SELECT session_id, company_name, created_at, updated_at 
FROM agent_sessions 
WHERE created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;"

# Test memory manager initialization
python -c "
from eko.agent.crewai.crawl.memory import CrewMemoryManager
mm = CrewMemoryManager('Test Company', 'debug_session_001')
print(f'Memory Manager Status: {mm.is_initialized()}')
"
```

**Solutions**:
- Verify PostgreSQL database connectivity and permissions
- Check that required database tables exist and have proper indexes
- Ensure memory file directory permissions allow read/write access
- Validate environment variables for external API access

#### AgentOps Integration Issues

**Symptoms**: Missing monitoring data in AgentOps dashboard, silent initialization failures

**Diagnosis**:
```python
from eko.agent.crewai.observability import agentops_manager
import os

# Check API key configuration
print(f"API Key present: {'AGENTOPS_API_KEY' in os.environ}")
print(f"API Key value: {os.getenv('AGENTOPS_API_KEY', 'NOT_SET')[:10]}...")

# Test manager initialization
success = agentops_manager.initialize()
print(f"Initialization successful: {success}")
print(f"Manager available: {agentops_manager.is_available()}")
```

**Solutions**:
- Verify `AGENTOPS_API_KEY` environment variable is correctly configured
- Ensure internet connectivity to AgentOps cloud services
- Install AgentOps with correct dependencies: `pip install 'crewai[agentops]'`
- Initialize AgentOps before creating any CrewAI agents or crews

#### High Memory Usage and Performance Issues

**Symptoms**: Slow agent execution, high memory consumption, database connection timeouts

**Diagnosis**:
```bash
# Monitor memory usage patterns
find var/crawl_memory/ -name "*.json" -exec ls -lh {} \; | sort -k5 -hr

# Check database memory usage
cd backoffice && ./bin/run_in_db.sh "
SELECT session_id, company_name,
       pg_size_pretty(length(memory_data::text)) as memory_size,
       updated_at
FROM agent_sessions 
ORDER BY length(memory_data::text) DESC 
LIMIT 10;"

# Monitor tool performance
cd backoffice && ./bin/run_in_db.sh "
SELECT tool_name, 
       AVG(duration_ms) as avg_duration_ms,
       MAX(duration_ms) as max_duration_ms,
       COUNT(*) as execution_count,
       AVG(CASE WHEN success THEN 1 ELSE 0 END) * 100 as success_rate
FROM agent_tool_usage 
WHERE call_timestamp > NOW() - INTERVAL '24 hours'
GROUP BY tool_name 
ORDER BY avg_duration_ms DESC;"
```

**Solutions**:
- Implement session archiving for completed research sessions
- Configure memory compression for active long-running sessions
- Optimize database connection pooling settings
- Add circuit breaker patterns for unreliable external APIs

### Debug Commands and System Validation

```bash
# Comprehensive system health check
python -c "
from eko.agent.crewai.crawl.crawler import PersistentWebCrawlCrew
from eko.agent.crewai.crawl.tools import get_web_tools, get_analyst_tools
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.observability import setup_agentops

# Test system components
mm = CrewMemoryManager('Debug Company', 'debug_session_001')
web_tools = get_web_tools(mm)
analyst_tools = get_analyst_tools(mm)
agentops_manager = setup_agentops(tags=['debug-session'])

print(f'System Status:')
print(f'  Memory Manager: {'OK' if mm.is_initialized() else 'FAILED'}')
print(f'  Web Tools Count: {len(web_tools)}')
print(f'  Analyst Tools Count: {len(analyst_tools)}')
print(f'  AgentOps Available: {agentops_manager.is_available()}')
print(f'  Database Connection: {'OK' if mm.test_database_connection() else 'FAILED'}')
"

# Monitor real-time agent activity
tail -f var/logs/crewai.log | grep -E "Agent|Task|Tool|Memory|Error"

# Validate database schema integrity
cd backoffice && ./bin/run_in_db.sh "
SELECT table_name, column_count, row_count 
FROM (
    SELECT 
        schemaname||'.'||tablename as table_name,
        COUNT(*) as column_count,
        MAX(n_tup_ins) as row_count
    FROM pg_stats 
    WHERE tablename LIKE 'agent_%'
    GROUP BY schemaname, tablename
) t
ORDER BY table_name;
"
```

## FAQ

### User-Centric Questions and Answers

**Q: How do I start a comprehensive ESG research session for a new company?**
A: Use the `PersistentWebCrawlCrew` class which handles complete initialization including memory management, agent coordination, and monitoring setup:

```python
from eko.agent.crewai.crawl.crawler import PersistentWebCrawlCrew
crawler = PersistentWebCrawlCrew("Company Name", "unique_session_id")
results = crawler.run_comprehensive_research()
```

**Q: Can multiple agents work on the same company research simultaneously without conflicts?**
A: Yes, the system uses a sophisticated persistent memory architecture that enables true collaborative research. All agents share session state through the `CrewMemoryManager`, preventing duplicate work while enabling coordinated intelligence gathering.

**Q: How does the system prevent downloading and analyzing the same documents multiple times?**
A: The memory system includes comprehensive deduplication through `TrackFileTool` and `TrackURLTool`. All processed URLs and documents are tracked in session memory, with automatic duplicate detection before processing new content.

**Q: What happens if a research session is interrupted or the system crashes?**
A: The dual-storage memory system (JSON files + PostgreSQL) ensures complete persistence. When agents restart, they automatically resume from the exact interruption point using stored session state. No research progress is ever lost.

**Q: How do I monitor costs and performance for long-running research sessions?**
A: The system provides dual monitoring through both custom tracking and AgentOps integration:

```python
# Custom monitoring for detailed control
from eko.agent.crewai.monitoring import AgentMonitoringService
monitoring = AgentMonitoringService("session_id")
cost_analysis = monitoring.get_session_costs()

# AgentOps for professional analytics
from eko.agent.crewai.observability import agentops_manager
dashboard_url = agentops_manager.get_session_url()
```

**Q: Can I customize the ESG research focus areas and analysis categories?**
A: Yes, the system supports focus area customization through research task configuration. ESG categories (Environmental, Social, Governance) and behavior types (Positive, Negative, Neutral) can be customized by modifying the `ExtractESGInsightsTool` configuration.

**Q: How does the system integrate with the broader EkoIntelligence platform?**
A: The CrewAI system integrates seamlessly through multiple layers:
- **Analytics Database**: Direct integration via `agent_` tables storing all research data
- **Customer Database**: Synchronized delivery through `xfer_` table architecture
- **ESG Analysis Pipeline**: Results feed into `eko.analysis_v2` for advanced corporate analysis
- **Dashboard Integration**: Customer-facing visualization through the web application

**Q: What types of sources and documents does the system automatically discover?**
A: The system systematically discovers and analyzes: corporate websites and documentation, annual reports and investor presentations, sustainability and ESG disclosure reports, regulatory filings (SEC, Companies House, GLEIF), news articles and press releases, third-party ESG analyses, NGO reports and activist research, industry reports and benchmarking studies.

**Q: How do I handle API rate limits and optimize research performance?**
A: The system includes built-in rate limiting and optimization strategies:
- Tools implement exponential backoff and retry logic
- Session-based coordination prevents API abuse across agents
- Memory deduplication reduces unnecessary API calls
- Consider using multiple API keys for high-volume research
- Distribute intensive research across multiple sessions

**Q: Can the system operate offline or in air-gapped environments?**
A: The core research functionality requires internet access for API integrations and LLM services. However, the system gracefully handles connectivity issues:
- AgentOps monitoring fails gracefully if unavailable
- Local monitoring continues to function offline
- Research sessions can be resumed when connectivity is restored
- Memory persistence ensures no data loss during outages

## References

### Framework and Technology Documentation

- [CrewAI Framework Documentation](https://docs.crewai.com/) - Official multi-agent orchestration framework
- [CrewAI Observability Guide](https://docs.crewai.com/concepts/monitoring) - Built-in monitoring and observability
- [AgentOps Documentation](https://docs.agentops.ai/) - Professional AI agent monitoring platform
- [OpenTelemetry Python Documentation](https://opentelemetry.io/docs/languages/python/) - Industry-standard observability
- [PostgreSQL JSONB Documentation](https://www.postgresql.org/docs/current/datatype-json.html) - Document storage capabilities

### External API Documentation

- [Google Gemini API Documentation](https://ai.google.dev/gemini-api/docs) - Primary LLM provider integration
- [Google Custom Search API](https://developers.google.com/custom-search/v1/overview) - Web search functionality
- [Companies House API](https://developer.company-information.service.gov.uk/) - UK corporate registry access
- [GLEIF API Documentation](https://www.gleif.org/en/lei-data/gleif-api) - Global entity identifier database
- [SEC EDGAR API](https://www.sec.gov/edgar/sec-api-documentation) - US securities filings access
- [NewsAPI Documentation](https://newsapi.org/docs) - News and media content access
- [Wikipedia MediaWiki API](https://www.mediawiki.org/wiki/API:Main_page) - Wikipedia content access

### EkoIntelligence Platform Integration

- [Multi-Agent ESG Research System](./crawl/README.md) - Comprehensive crawling and analysis system
- [Agent Monitoring Infrastructure](./monitoring/README.md) - Performance tracking and cost analysis
- [AgentOps Observability Integration](./observability/README.md) - Professional monitoring setup
- [Database Connection Management](../../db/__init__.py) - Platform database integration
- [ESG Analysis Pipeline](../../analysis_v2/) - Downstream analysis and insight generation
- [Customer Dashboard Application](../../../../apps/customer/) - Web interface and reporting

### Development and Configuration

- [CrewAI Telemetry Collection](./telemetry_collector.py) - Custom telemetry implementation
- [System Monitoring Documentation](./MONITORING.md) - Detailed monitoring system specification
- [Admin Dashboard Planning](./PLAN.md) - Future dashboard development roadmap
- [Platform Development Guidelines](../../CLAUDE.md) - EkoIntelligence development standards

### Linear Project Integration

- [EKO-238: Agentic Scraper](https://linear.app/ekointelligence/issue/EKO-238/agentic-scraper) - Current development project
- [Agentic Scraper Project](https://linear.app/ekointelligence/project/9caf71c1-8bce-4516-a32f-40dbd3b63114) - Complete project roadmap
- [Platform Issues and Enhancements](https://linear.app/ekointelligence/team/EKO/all) - All related platform development

### Supporting Libraries and Dependencies

- [Pydantic Documentation](https://pydantic-docs.helpmanual.io/) - Type-safe data validation
- [Loguru Logging Framework](https://loguru.readthedocs.io/en/stable/) - Advanced Python logging
- [Requests HTTP Library](https://docs.python-requests.org/en/latest/) - HTTP client for API integration
- [BeautifulSoup Documentation](https://www.crummy.com/software/BeautifulSoup/bs4/doc/) - HTML parsing and extraction

---

## Changelog

### 2025-07-31

- **Created comprehensive README.md** for EkoIntelligence CrewAI Multi-Agent System
- **Documented complete system architecture** including multi-agent coordination, database integration, and monitoring infrastructure
- **Added detailed component documentation** covering crawl system, monitoring, observability, and telemetry collection
- **Included comprehensive usage examples** for ESG research sessions, custom workflows, and performance monitoring
- **Provided extensive troubleshooting section** with common issues, diagnosis commands, and resolution strategies
- **Added detailed FAQ section** addressing user concerns, implementation guidance, and best practices
- **Documented integration architecture** with EkoIntelligence platform, external APIs, and monitoring systems
- **Included Mermaid diagrams** for system architecture, database schema, and workflow visualization
- **Provided complete dependency documentation** with installation requirements and integration specifications
- **Added future work section** aligned with EKO-238 project requirements and platform evolution goals
- **Comprehensive reference documentation** linking to CrewAI, AgentOps, platform components, and Linear project
- **Included database schema documentation** with complete table structures and relationship mappings

---

(c) All rights reserved ekoIntelligence 2025