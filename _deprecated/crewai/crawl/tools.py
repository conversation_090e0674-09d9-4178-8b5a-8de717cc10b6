"""
CrewAI Web Crawler Tool Module - Central Import Gateway for ESG Research Tools

This module serves as the primary import gateway and tool collection interface for the 
EkoIntelligence ESG analysis platform's CrewAI-based web crawling system. It provides 
a streamlined access point to the comprehensive suite of specialized AI tools used by 
multi-agent web crawling crews for automated ESG (Environmental, Social, Governance) 
research, corporate misconduct investigation, and sustainability claim analysis.

## Core Purpose
The module acts as a **simplified facade** over the complex tool ecosystem in the `tools_new` 
package, providing easy access to pre-configured tool collections for different agent types 
in ESG research workflows. It abstracts away the complexity of individual tool initialization 
and memory management while maintaining full access to the underlying CrewAI BaseTool framework.

## Key Functionality
This module primarily serves as an import proxy to the `tools_new.all_tools` module, which 
contains the actual tool factory functions:

### Available Tool Collections
- **`get_web_tools(memory_manager)`**: Comprehensive toolkit for web crawling agents performing 
  extensive ESG document discovery, corporate website analysis, and regulatory filing research
- **`get_analyst_tools(memory_manager)`**: Focused toolkit for analyst agents performing targeted 
  content analysis, insight extraction, and relevance scoring

### Tool Categories Provided
**Search and Discovery Tools:**
- Web search capabilities for ESG-related content and corporate controversies
- Regulatory database integration (SEC, Companies House, GLEIF)
- Domain analysis and website structure mapping
- Wikipedia research for corporate background and controversy identification

**Content Processing Tools:**
- Advanced webpage fetching with deduplication and content cleaning
- PDF and document download with persistent session tracking
- URL extraction and relationship mapping from discovered webpages
- Content relevance analysis and ESG insight extraction

**AI-Powered Analysis Tools:**
- Intelligent search query generation for targeted ESG research
- Text analysis and relevance scoring for discovered content
- ESG-specific content classification and insight extraction
- Automated content summarization for research insights

**Memory Management Integration:**
- Session-based crawling state persistence across agent executions
- URL deduplication to prevent redundant research activities
- Progress tracking and cross-agent coordination capabilities
- Research insight accumulation and structured data storage

## CrewAI Framework Integration
The module integrates with the CrewAI multi-agent framework to support:
- **Agent Tool Assignment**: Tools are directly assignable to CrewAI Agent instances
- **Memory-Enabled Workflows**: Persistent session management across agent executions
- **Multi-Agent Coordination**: Shared memory enables collaborative research workflows
- **Workflow Orchestration**: Tools support complex ESG research pipelines and task delegation

## ESG Analysis System Architecture
This module is a critical component of the broader EkoIntelligence ESG analysis system:

```
┌─────────────────────────────────────────────────────────────────┐
│ EkoIntelligence ESG Analysis System Architecture               │
├─────────────────────────────────────────────────────────────────┤
│ Frontend Layer: Customer Dashboard (apps/customer)             │
│ ├── React/TypeScript UI components                             │
│ └── Real-time ESG insights and risk scoring displays           │
├─────────────────────────────────────────────────────────────────┤
│ API Layer: Customer Database (xfer_ tables)                    │
│ ├── Data synchronization between analytics and customer DBs    │
│ └── API endpoints for frontend ESG data consumption            │
├─────────────────────────────────────────────────────────────────┤
│ Analytics Backend: Python Backend (backoffice/)               │
│ ├── >> THIS MODULE: CrewAI Web Crawler Tools <<                │
│ ├── ESG Analysis Pipeline (eko.analysis_v2)                    │
│ ├── Effect Flags Generation and DEMISE Model Processing        │
│ └── Claims/Promises Analysis and Greenwashing Detection        │
├─────────────────────────────────────────────────────────────────┤
│ Data Layer: Analytics Database (PostgreSQL)                    │
│ ├── Knowledge Graph (kg_ tables)                               │
│ ├── Analysis Results (ana_ tables)                             │
│ └── Agent Session Management (agent_ tables)                   │
└─────────────────────────────────────────────────────────────────┘
```

## Database Integration
The tools interface with several key analytics database components:
- **agent_sessions**: Persistent crawling session metadata and JSON memory storage
- **agent_insights**: Structured ESG insight storage with confidence scoring and categorization
- **agent_reports**: Aggregated research reports with statistical summaries and findings
- **agent_tool_usage**: Tool usage analytics for performance monitoring and optimization
- **kg_search_queries**: Search query tracking for research pattern analysis and improvement

## Memory Architecture
Tools leverage a sophisticated dual-persistence memory system:
- **File-Based Storage**: JSON session files in `var/crawl_memory/` for rapid access and debugging
- **Database Integration**: PostgreSQL storage for structured research data and cross-session analysis
- **Session Management**: Company-specific research sessions with progress tracking and resumption
- **Cross-Agent Coordination**: Shared memory enables multiple agents to collaborate on complex research

## Usage Pattern
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools import get_web_tools, get_analyst_tools
from crewai import Agent, Task, Crew

# Initialize memory manager for persistent ESG research session
memory_manager = CrewMemoryManager("Apple Inc", "sustainability_research_2024")

# Create comprehensive web crawling tools for discovery agent
web_tools = get_web_tools(memory_manager)

# Create focused analyst tools for insight extraction agent  
analyst_tools = get_analyst_tools(memory_manager)

# Configure CrewAI agents with specialized tool collections
esg_researcher = Agent(
    role="ESG Web Research Specialist",
    goal="Discover corporate ESG documents, controversies, and regulatory filings",
    backstory="Expert in corporate sustainability research and ESG data discovery",
    tools=web_tools,
    memory=True
)

content_analyst = Agent(
    role="ESG Content Analysis Specialist", 
    goal="Extract insights from discovered ESG content and identify potential issues",
    backstory="Expert in ESG content analysis and greenwashing detection",
    tools=analyst_tools,
    memory=True
)

# Create multi-agent crew for comprehensive ESG research
esg_crew = Crew(agents=[esg_researcher, content_analyst], memory=True)
```

## Performance and Reliability Features
- **Lazy Tool Loading**: Tools instantiated on-demand to optimize memory usage during agent creation
- **Connection Pooling**: Database operations use optimized connection management via `eko.db.get_bo_conn()`
- **Error Recovery**: Network failures and API rate limits handled gracefully with retry mechanisms
- **Duplicate Prevention**: Memory system prevents redundant crawling of previously visited URLs
- **Session Persistence**: Research workflows can resume seamlessly from interruption points

## Technology Stack Dependencies
- **CrewAI Framework**: Multi-agent orchestration and tool management (crewai>=0.55.0)
- **PostgreSQL**: Persistent storage for research data and session management
- **Pydantic**: Type-safe data validation and serialization for tool inputs and outputs
- **Loguru**: Structured logging for debugging and monitoring agent research activities
- **EkoIntelligence Platform**: Proprietary ESG analysis pipeline and database integration

## Related Components
- **CrewAI Agent Definitions** (`agents.py`): Agent role definitions and tool assignments
- **Research Task Orchestration** (`tasks.py`): ESG research task definitions and workflows  
- **Memory Management System** (`memory.py`): CrewMemoryManager for session persistence
- **Individual Tool Implementations** (`tools_new/`): Specialized BaseTool subclasses
- **ESG Analysis Pipeline** (`../../analysis_v2/`): Downstream processing of research results

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/agents CrewAI Agent Management System
@see https://docs.crewai.com/concepts/memory CrewAI Memory and Persistence Framework
@see https://github.com/crewaiinc/crewai CrewAI Framework GitHub Repository
@see tools_new/all_tools.py Tool Factory and Registry Implementation
@see memory.py CrewMemoryManager Implementation
@see ../../../analysis_v2/ ESG Analysis Pipeline
<AUTHOR>
@updated 2025-07-23
@description Central import gateway for CrewAI ESG research tools used in automated corporate sustainability analysis
@example
```python
# Basic tool collection usage
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools import get_web_tools

memory_manager = CrewMemoryManager("Tesla Inc", "esg_research_session")
tools = get_web_tools(memory_manager)
print(f"Loaded {len(tools)} tools for ESG research")
```
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

# Import the get_web_tools function from the tools_new package
from .tools_new.all_tools import get_web_tools, get_analyst_tools
