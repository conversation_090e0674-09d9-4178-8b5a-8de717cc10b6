"""
CrewAI Web Crawler Memory Tools Module

This module serves as the main entry point for memory-related tools used by the CrewAI web crawling system
within the EkoIntelligence ESG analysis platform. It provides persistent memory capabilities for AI agents
performing automated web research, enabling them to track progress, store insights, and maintain state
across complex multi-step crawling workflows.

## Core Functionality
- **Tool Factory**: Provides centralized access to memory-related tools via `get_memory_tools()` function
- **Persistent Memory Management**: Integrates with `CrewMemoryManager` for dual file/database persistence
- **Crawling Session Tracking**: Maintains crawling progress across multiple agent executions and sessions
- **Insight Accumulation**: Enables agents to store and recall discovered ESG insights and business intelligence
- **URL and File Tracking**: Provides agents with visibility into previously visited URLs and downloaded content

## Memory Tool Ecosystem
The module exposes five specialized memory tools through the factory pattern:
- **URL Tracking**: `TrackURLTool` - Records visited URLs to prevent duplicate crawling
- **File Management**: `TrackFileTool` - Tracks downloaded documents and their source locations
- **Insight Storage**: `TrackInsightTool` - Stores discovered ESG insights with metadata and confidence scores
- **Search Query Logging**: `TrackSearchQueryTool` - Maintains history of search queries for research optimization
- **Progress Monitoring**: `GetProgressTool` - Provides comprehensive crawling progress summaries for agent coordination

## Integration with CrewAI Framework
This module integrates with the CrewAI multi-agent framework to provide:
- **Agent Memory Persistence**: Tools maintain state between agent executions and crew runs
- **Collaborative Intelligence**: Multiple agents can access shared memory for coordinated research
- **Progress Coordination**: Agents can understand crawling progress and avoid redundant work
- **Result Aggregation**: Final research results are compiled from distributed agent activities

## Data Persistence Architecture
Memory data is persisted using a dual-storage approach:
- **File System**: JSON files in `var/crawl_memory/` for quick access and debugging
- **PostgreSQL Database**: Structured storage in `agent_sessions`, `agent_insights`, and `agent_reports` tables
- **Automatic Synchronization**: Changes are synced between file system and database for reliability

## Database Schema Integration
The module interacts with several database tables in the analytics database:
- **agent_sessions**: Core session tracking with JSON memory data storage
- **agent_insights**: Structured storage of discovered ESG insights with categorization
- **agent_reports**: Final research reports with summary statistics and aggregated findings
- **agent_llm_calls**: LLM interaction tracking for cost monitoring and performance analysis
- **agent_tool_usage**: Tool usage analytics for workflow optimization

## Key Dependencies
- **CrewAI Framework**: Multi-agent orchestration and tool management system
- **PostgreSQL**: Persistent storage for session data, insights, and research results
- **EkoIntelligence Database Layer**: `eko.db.get_bo_conn()` for database connectivity
- **Loguru**: Structured logging for debugging and monitoring agent activities
- **JSON Persistence**: File-based memory storage for rapid access and development debugging

## Usage Pattern
The module follows a factory pattern for tool initialization:
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem import get_memory_tools

memory_manager = CrewMemoryManager("CompanyName", "session_123")
tools = get_memory_tools(memory_manager)

# Tools are then used by CrewAI agents
agent = Agent(
    role="Research Analyst",
    tools=tools,
    memory=True
)
```

## System Architecture Context
This module fits into the broader EkoIntelligence ESG analysis system:
- **Analytics Backend**: Python system orchestrates web crawling for ESG data collection
- **Data Pipeline**: CrewAI agents crawl corporate websites and extract sustainability information
- **Memory Layer**: This module provides persistence for multi-session research workflows
- **Database Integration**: Research findings are stored for customer-facing dashboard consumption
- **Frontend Integration**: Results are synced to customer database via `xfer_` tables for web access

## Security and Error Handling
- **Database Connection Pooling**: Uses `eko.db.get_bo_conn()` for proper connection management
- **Exception Handling**: Tools gracefully handle database failures and file system errors
- **Logging Integration**: Comprehensive logging via Loguru for debugging and monitoring
- **Session Isolation**: Memory data is isolated by company name and session ID

## Performance Considerations
- **Lazy Loading**: Tools are created on-demand with factory pattern
- **Batch Operations**: Memory updates are batched for database efficiency
- **File System Caching**: Local JSON files provide fast access for agent queries
- **Database Indexing**: Tables include appropriate indexes for session and company queries

@see https://docs.crewai.com/concepts/memory CrewAI Memory System Documentation  
@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see backoffice/src/eko/agent/crewai/crawl/memory.py CrewMemoryManager Implementation
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/all_tools.py Memory Tools Factory
<AUTHOR>
@updated 2025-07-22
@description CrewAI memory tools module providing persistent memory capabilities for AI-powered web crawling agents
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from eko.agent.crewai.crawl.tools_mem.all_tools import get_memory_tools