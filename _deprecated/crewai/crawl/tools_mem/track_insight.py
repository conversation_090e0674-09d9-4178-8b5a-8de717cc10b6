"""
CrewAI Insight Tracking Tool for ESG Research Intelligence Management

This module provides the `TrackInsightTool` class, a specialized CrewAI tool for capturing, storing, and
managing discovered insights during automated ESG (Environmental, Social, Governance) research workflows
within the EkoIntelligence analysis platform. The tool serves as an intelligent knowledge repository for
AI agents, enabling them to systematically record ESG insights, behavioral observations, and sustainability
findings with comprehensive categorization, confidence scoring, and source attribution for enhanced
research intelligence and corporate sustainability analysis.

## Core Functionality
- **Structured Insight Capture**: Records ESG insights with standardized categorization (Environmental, Social, Governance)
- **Behavioral Analysis**: Classifies corporate behavior types (Positive, Negative, Neutral) for sentiment tracking
- **Confidence Scoring**: Assigns numerical confidence levels (0.0-1.0) for insight reliability assessment
- **Source Attribution**: Maintains complete source URL and document title linkage for research provenance
- **Duplicate Prevention**: Advanced deduplication using description-based matching to prevent redundant insights
- **Multi-Format Input**: Supports both individual parameter input and JSON batch processing for flexible data ingestion

## ESG Research Integration

### Corporate Sustainability Analysis
The tool is specifically designed to support comprehensive ESG analysis workflows:
- **Environmental Impact Tracking**: Records climate, pollution, resource usage, and sustainability initiative insights
- **Social Responsibility Analysis**: Captures diversity, labor practices, community engagement, and human rights observations
- **Governance Assessment**: Documents corporate governance, ethics, transparency, and regulatory compliance findings
- **Greenwashing Detection**: Enables systematic collection of evidence for identifying misleading sustainability claims

### Behavioral Pattern Recognition
Advanced categorization system for corporate behavior analysis:
- **Positive Behaviors**: Genuine sustainability improvements, transparent reporting, measurable impact
- **Negative Behaviors**: Environmental damage, social harm, governance failures, regulatory violations
- **Neutral Behaviors**: Standard practices, compliance activities, neutral announcements

## CrewAI Framework Integration

### Tool Architecture Design
The `TrackInsightTool` follows CrewAI best practices by inheriting from `BaseTool`:
- **Pydantic Input Validation**: Uses `TrackInsightSchema` for type-safe parameter validation and documentation
- **Standardized Tool Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **Flexible Input Handling**: Supports both structured parameters and JSON string input for batch processing
- **Dual Execution Modes**: Provides both synchronous (`_run`) and asynchronous (`_arun`) execution patterns
- **Comprehensive Error Handling**: Graceful exception handling with detailed logging via Loguru

### Multi-Agent Research Workflows
In collaborative research environments, the insight tracking tool enables:
- **Shared Intelligence Repository**: Multiple agents contribute to a unified insight database
- **Research Coordination**: Prevents duplicate insight collection through systematic deduplication
- **Knowledge Synthesis**: Aggregates findings from multiple sources and agents for comprehensive analysis
- **Progress Tracking**: Maintains cumulative insight collection metrics for research progress assessment

## Data Persistence and Memory Management

### Dual-Storage Architecture
The tool implements a robust dual-storage strategy for reliability and performance:

#### File System Persistence
- **JSON Memory Files**: Local storage in `var/crawl_memory/{session_id}.json` for rapid access
- **Development Support**: Human-readable format facilitates debugging and manual data inspection  
- **Performance Optimization**: Eliminates database latency for frequent memory access operations
- **Backup Strategy**: Serves as fallback storage during database connectivity issues

#### PostgreSQL Database Integration
Structured storage across multiple analytics database tables:
- **agent_insights**: Primary table for structured insight storage with categorization and metadata
- **agent_sessions**: Session management with JSONB memory data snapshots for state persistence
- **agent_reports**: Aggregated research reports combining insights from multiple collection sessions
- **agent_tool_usage**: Usage analytics for monitoring tool performance and research workflow optimization

### Memory Synchronization Strategy
- **Bidirectional Sync**: Maintains consistency between file system and database storage layers
- **Atomic Operations**: Ensures data integrity during concurrent agent access to shared memory
- **Conflict Resolution**: Implements duplicate detection and prevention during memory synchronization
- **Recovery Mechanisms**: Graceful degradation to file-based storage during database failures

## Database Schema Integration

### Agent Insights Table Structure
The tool populates the `agent_insights` table with comprehensive ESG research data:
- **Primary Key**: `id` (auto-incrementing integer for unique insight identification)
- **Session Tracking**: `session_id` (text) linking insights to specific research sessions
- **Entity Focus**: `company_name` (text) identifying the target company or organization
- **ESG Categorization**: `category` (text) for Environmental, Social, or Governance classification
- **Behavioral Assessment**: `behavior_type` (text) for Positive, Negative, or Neutral behavior classification
- **Content Storage**: `description` (text) containing detailed insight descriptions and analysis
- **Source Attribution**: `source_url` (text) and `source_title` (text) for research provenance
- **Quality Metrics**: `confidence` (double precision) for insight reliability scoring (0.0-1.0 scale)
- **Metadata Storage**: `metadata` (JSONB) for extensible custom data and analytical annotations
- **Temporal Tracking**: `extracted_at` and `created_at` (timestamptz) for temporal analysis

### Database Indexing Strategy
Optimized indexing for efficient insight retrieval and analysis:
- **Session-Based Queries**: `idx_agent_insights_session_id` enables fast session-specific insight retrieval
- **Company-Focused Analysis**: `idx_agent_insights_company_name` supports entity-specific research queries
- **Foreign Key Integrity**: Maintains referential integrity with `agent_sessions` table via session_id

## Advanced Input Processing

### Parameter Validation Schema
The tool uses Pydantic `TrackInsightSchema` for comprehensive input validation:
```python
class TrackInsightSchema(BaseModel):
    category: str = Field(description="ESG category (Environmental, Social, Governance)")
    behavior_type: str = Field(description="Behavior classification (Positive, Negative, Neutral)")
    description: str = Field(description="Detailed insight description and analysis")
    source_url: str = Field(description="Source URL where insight was discovered")
    source_title: str = Field(description="Title of source document or webpage")
    confidence: float = Field(description="Confidence level (0.0-1.0)")
```

### JSON Batch Processing
Advanced JSON input handling for flexible data ingestion:
- **Single Object Processing**: Handles individual insight objects from JSON strings
- **Array Processing**: Supports batch processing of multiple insights from JSON arrays
- **Error Resilience**: Graceful handling of malformed JSON with fallback to parameter processing
- **Validation Pipeline**: Comprehensive validation ensures data quality before persistence

### Flexible Parameter Handling  
The tool adapts to various input scenarios:
- **Structured Parameters**: Individual parameters for direct agent tool calls
- **JSON String Input**: Supports complex data structures from external systems
- **Missing Field Validation**: Comprehensive validation with clear error messages
- **Type Conversion**: Automatic confidence score conversion from string to float

## System Architecture Context

### EkoIntelligence Platform Integration
The insight tracking tool operates within the broader ESG analysis ecosystem:
- **Research Pipeline**: Feeds discovered insights into corporate sustainability analysis workflows
- **Data Synchronization**: Insights flow through `xfer_` tables to customer-facing dashboard systems
- **Analytics Backend**: Python-based CrewAI orchestration drives automated corporate research
- **Intelligence Processing**: Collected insights feed into NLP analysis and greenwashing detection algorithms
- **Customer Dashboard**: Research results accessible through web-based ESG analysis and reporting interfaces

### Corporate Research Workflow
1. **Target Company Selection**: Research sessions focus on specific companies for comprehensive ESG analysis
2. **Multi-Agent Deployment**: CrewAI agents use insight tracking tools for coordinated web crawling and analysis
3. **Content Analysis**: Agents analyze corporate websites, sustainability reports, and regulatory filings
4. **Insight Extraction**: Advanced AI techniques identify and categorize ESG-relevant information
5. **Knowledge Synthesis**: Insight tracking tool accumulates findings across multiple agents and sources
6. **Research Reporting**: Final reports combine insights with confidence metrics and source attribution
7. **Database Integration**: Results feed into broader EkoIntelligence scoring and analysis systems

## Performance Optimization and Scalability

### Memory Management Strategy
- **Efficient Deduplication**: Description-based matching prevents redundant insight storage
- **Lazy Loading**: Memory data loaded on-demand to minimize resource utilization
- **Session Isolation**: Memory data properly scoped by company name and session ID
- **Batch Updates**: Database operations optimized through batching for improved performance

### Database Performance Optimization
- **Connection Pooling**: Uses EkoIntelligence standard connection pooling via `get_bo_conn()`
- **Transaction Management**: Proper transaction handling ensures data consistency and rollback capabilities
- **Index Utilization**: Leverages database indexes for efficient session and company-based queries
- **JSONB Optimization**: PostgreSQL JSONB storage enables flexible metadata querying and storage

### Error Handling and Reliability
- **Exception Safety**: Comprehensive exception handling with detailed error logging via Loguru
- **Graceful Degradation**: Continues operation during database failures using file-based storage
- **Data Validation**: Multi-layer validation prevents data corruption and ensures consistency
- **Recovery Procedures**: Automatic recovery mechanisms for failed database operations

## Security and Access Control

### Data Security Measures  
- **Session-Based Access Control**: Insights scoped to specific company and session combinations
- **Database Security**: Utilizes EkoIntelligence standard authentication and connection security
- **Input Sanitization**: Comprehensive input validation prevents injection attacks and data corruption
- **Audit Trail**: Complete logging of all insight collection activities for security monitoring

### Monitoring and Observability
- **Comprehensive Logging**: All tool operations logged via Loguru for debugging and audit purposes
- **Usage Analytics**: Detailed tool usage statistics stored in `agent_tool_usage` table
- **Performance Monitoring**: Real-time monitoring of insight collection rates and success metrics
- **Research Progress Tracking**: Integration with progress monitoring tools for workflow visibility

## Key Dependencies and Technologies

### CrewAI Framework Components
- **CrewAI BaseTool**: Foundation class providing standardized tool interface and agent integration
- **Pydantic Models**: Type-safe data validation using `BaseModel` and `Field` for input schemas
- **Tool Registration**: Automatic tool registration and discovery within CrewAI agent configurations

### Database and Persistence Technologies
- **PostgreSQL**: Analytics database providing ACID compliance and advanced JSON support
- **psycopg**: Python PostgreSQL adapter for efficient database connectivity and operations  
- **JSONB Storage**: PostgreSQL JSON Binary format for flexible metadata storage and querying

### Supporting Infrastructure
- **Loguru**: Advanced logging framework providing structured logs with exception tracing
- **Python JSON**: Native JSON processing for memory file persistence and data serialization
- **DateTime Handling**: ISO format timestamps for precise temporal tracking and analysis

## Usage Examples and Integration Patterns

### Basic Agent Configuration
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem.track_insight import TrackInsightTool
from crewai import Agent

# Initialize memory management
memory_manager = CrewMemoryManager("Tesla Inc", "esg_research_2024")
insight_tool = TrackInsightTool(
    memory_manager.company_name,
    memory_manager.session_id, 
    memory_manager.memory_file
)

# Configure ESG research agent
esg_analyst = Agent(
    role="ESG Research Specialist",
    goal="Identify and analyze corporate sustainability practices",
    tools=[insight_tool],
    memory=True,
    verbose=True
)
```

### Multi-Agent Research Scenario
```python
# Coordinated multi-agent ESG research
env_agent = Agent(role="Environmental Analyst", tools=[insight_tool])
social_agent = Agent(role="Social Impact Analyst", tools=[insight_tool])
governance_agent = Agent(role="Corporate Governance Analyst", tools=[insight_tool])

# All agents contribute to shared insight repository
crew = Crew(agents=[env_agent, social_agent, governance_agent])
results = crew.kickoff()
```

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/memory CrewAI Memory System Architecture
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see https://www.postgresql.org/docs/current/datatype-json.html PostgreSQL JSONB Documentation
@see backoffice/src/eko/agent/crewai/crawl/memory.py CrewMemoryManager Implementation
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/all_tools.py Memory Tools Factory
@see backoffice/src/eko/db/get_bo_conn Database Connection Management
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for capturing and managing ESG insights during automated corporate sustainability research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import json
from datetime import datetime
from typing import Dict, Any

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.db import get_bo_conn


class TrackInsightSchema(BaseModel):
    """Input schema for the track insight tool."""
    category: str = Field(description="The category of the insight (e.g., 'Environmental', 'Social', 'Governance')")
    behavior_type: str = Field(description="The type of behavior (e.g., 'Positive', 'Negative', 'Neutral')")
    description: str = Field(description="A detailed description of the insight")
    source_url: str = Field(description="The URL where the insight was found")
    source_title: str = Field(description="The title of the source")
    confidence: float = Field(description="The confidence level (0.0 to 1.0)")


class TrackInsightTool(BaseTool):
    """
    Tool for tracking discovered insights.
    
    This tool records insights found during the research process to build
    a comprehensive record of findings.
    """
    
    company_name: str = None  # Added as a field
    session_id: str = None    # Added as a field
    memory_file: str = None   # Added as a field
    
    def __init__(self, company_name: str, session_id: str, memory_file: str):
        """
        Initialize the tool with company and session information.
        
        Args:
            company_name: The name of the target company
            session_id: The session ID for persistence
            memory_file: Path to the memory file for persistence
        """
        # Define name and description directly here
        name = "track_insight"
        description = """
        Track an insight that was discovered during the research.
        Requires these fields:
        - category: The category of the insight (e.g., 'Environmental', 'Social', 'Governance')
        - behavior_type: The type of behavior (e.g., 'Positive', 'Negative', 'Neutral')
        - description: A detailed description of the insight
        - source_url: The URL where the insight was found
        - source_title: The title of the source
        - confidence: The confidence level (0.0 to 1.0)
        """
        
        # Initialize the BaseTool with the name and description
        super().__init__(
            name=name, 
            description=description,
            args_schema=TrackInsightSchema
        )
        
        # Store the company, session, and memory file information
        self.company_name = company_name
        self.session_id = session_id
        self.memory_file = memory_file
    
    def _load_memory(self) -> Dict[str, Any]:
        """
        Load the memory from the file system.
        
        Returns:
            Dict[str, Any]: The loaded memory
        """
        try:
            with open(self.memory_file, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading memory file: {e}")
            return {}
    
    def _save_memory(self, memory: Dict[str, Any]):
        """
        Save the memory to the file system.
        
        Args:
            memory: The memory to save
        """
        with open(self.memory_file, "w") as f:
            json.dump(memory, f, indent=2)
    
    def _sync_memory_to_db(self, memory: Dict[str, Any]):
        """
        Sync memory from file system to database.
        
        Args:
            memory: The memory to sync
        """
        try:
            memory["updated_at"] = datetime.now().isoformat()
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE agent_sessions 
                        SET memory_data = %s, updated_at = %s
                        WHERE session_id = %s
                        """,
                        (json.dumps(memory), datetime.now(), self.session_id)
                    )
                    conn.commit()
                    logger.debug(f"Synced memory to database for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error syncing memory to database: {e}")
    
    def _save_insight_to_db(self, insight: Dict[str, Any]):
        """
        Save an insight to the agent_insights table.
        
        Args:
            insight: The insight to save
        """
        try:
            category = insight.get("category", "unknown")
            behavior_type = insight.get("behavior_type", "unknown")
            description = insight.get("description", "")
            source_url = insight.get("source_url", "")
            source_title = insight.get("source_title", "")
            confidence = insight.get("confidence", 0.0)
            metadata = insight.get("metadata", {})
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_insights 
                        (session_id, company_name, category, behavior_type, description, 
                         source_url, source_title, confidence, metadata, extracted_at, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id, 
                            self.company_name,
                            category,
                            behavior_type,
                            description,
                            source_url,
                            source_title,
                            confidence,
                            json.dumps(metadata),
                            datetime.now(),
                            datetime.now()
                        )
                    )
                    conn.commit()
        except Exception as e:
            logger.error(f"Error saving insight to database: {e}")
    
    def _track_insight(self, insight: Dict[str, Any]) -> bool:
        """
        Track an insight in memory and database.
        
        Args:
            insight: The insight to track
            
        Returns:
            bool: True if the insight was newly tracked, False if it was already tracked
        """
        memory = self._load_memory()
        
        # Check if this insight already exists (by description)
        if "insights" not in memory:
            memory["insights"] = []
            
        existing_descriptions = [i.get("description") for i in memory["insights"]]
        if insight.get("description") in existing_descriptions:
            return False
            
        # Add timestamp to insight
        insight["discovered_at"] = datetime.now().isoformat()
        
        # Add to memory
        memory["insights"].append(insight)
        
        # Save updates
        self._save_memory(memory)
        self._sync_memory_to_db(memory)
        
        # Also save to agent_insights table
        self._save_insight_to_db(insight)
        
        logger.debug(f"Tracked insight: {insight.get('description', '')[:50]}...")
        return True
    
    def _run(
        self,
        category: str,
        behavior_type: str,
        description: str,
        source_url: str,
        source_title: str,
        confidence: float = 0.8
    ) -> str:
        """
        Run the tool to track a discovered insight.
        
        Args:
            category: The category of the insight
            behavior_type: The type of behavior
            description: A detailed description of the insight
            source_url: The URL where the insight was found
            source_title: The title of the source
            confidence: The confidence level (0.0 to 1.0)
            
        Returns:
            str: Confirmation message
        """
        try:
            
            # If only one parameter is provided and it's a string, it might be a JSON string
            if category and not any([behavior_type, description, source_url, source_title]):
                try:
                    # Try to parse it as JSON
                    if isinstance(category, str) and (category.startswith("{") or category.startswith("[")):
                        parsed_input = json.loads(category)
                        
                        # Handle array of objects
                        if isinstance(parsed_input, list) and len(parsed_input) > 0:
                            results = []
                            for item in parsed_input:
                                if isinstance(item, dict):
                                    success = self._track_insight(item)
                                    results.append(f"{'Successfully tracked' if success else 'Already tracked'} insight: {item.get('description', '')[:50]}...")
                            if results:
                                return "\n".join(results)
                                
                        # Handle single object
                        if isinstance(parsed_input, dict):
                            success = self._track_insight(parsed_input)
                            return f"{'Successfully tracked' if success else 'Already tracked'} insight: {parsed_input.get('description', '')[:50]}..."
                except Exception as e:
                    # Not valid JSON, continue with normal processing
                    logger.warning(f"Error parsing JSON input: {e}")
            
            # Ensure we have all required parameters
            required_fields = {
                "category": category,
                "behavior_type": behavior_type,
                "description": description,
                "source_url": source_url,
                "source_title": source_title
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                return f"Error: Missing required fields: {', '.join(missing_fields)}"
            
            # Default confidence to 0.8 if not provided
            if confidence is None:
                confidence = 0.8
            else:
                try:
                    confidence = float(confidence)
                except ValueError:
                    return f"Error: confidence must be a number between 0.0 and 1.0, got {confidence}"
            
            # Track the insight
            insight = {
                "category": category,
                "behavior_type": behavior_type,
                "description": description,
                "source_url": source_url,
                "source_title": source_title,
                "confidence": confidence,
                "metadata": {}
            }
            
            success = self._track_insight(insight)
            return f"{'Successfully tracked' if success else 'Already tracked'} insight about {behavior_type} {category} behavior from {source_title}"
        except Exception as e:
            logger.exception(f"Error tracking insight: {e}")
            return f"Error tracking insight: {str(e)}"
    
    async def _arun(
        self,
        category: str,
        behavior_type: str,
        description: str,
        source_url: str,
        source_title: str,
        confidence: float = 0.8
    ) -> str:
        """Async implementation of _run."""
        return self._run(
            category=category,
            behavior_type=behavior_type,
            description=description,
            source_url=source_url,
            source_title=source_title,
            confidence=confidence
        )
