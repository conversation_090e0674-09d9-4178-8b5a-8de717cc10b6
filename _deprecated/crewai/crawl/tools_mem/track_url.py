"""
CrewAI URL Tracking Tool for Web Crawling Memory Management

This module provides the `TrackURLTool` class, a specialized CrewAI tool for tracking and managing
visited URLs during automated web crawling research sessions within the EkoIntelligence ESG
(Environmental, Social, Governance) analysis platform. The tool serves as a comprehensive URL
deduplication system for AI agents, enabling them to maintain crawling state, avoid redundant
web requests, and coordinate multi-agent research workflows across complex corporate website analysis.

## Core Functionality
- **URL Visit Tracking**: Records visited URLs with timestamps to prevent duplicate web requests during research
- **Crawl State Management**: Maintains comprehensive crawling progress and position tracking across sessions
- **Deduplication Engine**: Prevents redundant URL visits to optimize bandwidth and improve crawling efficiency  
- **Multi-Agent Coordination**: Enables multiple CrewAI agents to share crawling progress and avoid overlapping work
- **Memory Integration**: Interfaces with CrewAI memory system for persistent URL tracking across research sessions

## URL Management Capabilities

### Visit Recording and Deduplication
The tool provides sophisticated URL tracking for web crawling optimization:
- **URL Registry**: Maintains complete list of visited URLs to prevent duplicate requests
- **State Persistence**: Records crawling position and progress for session continuity and recovery
- **Duplicate Detection**: Efficiently checks if URLs have been previously visited before making requests
- **Request Optimization**: Reduces unnecessary web requests through intelligent URL tracking and caching

### Crawling Progress Management
Advanced progress tracking enables coordinated multi-agent research:
- **Position Tracking**: Records current crawling position within the URL sequence for resumable sessions
- **Session Continuity**: Enables agents to resume crawling from last known position after interruptions
- **Progress Coordination**: Multiple agents can coordinate their crawling activities without conflicts
- **Timeline Maintenance**: Preserves chronological order of URL visits for analysis and debugging workflows

## CrewAI Framework Integration

### Tool Architecture
The `TrackURLTool` inherits from CrewAI's `BaseTool` class, providing:
- **Standardized Interface**: Follows CrewAI tool conventions for consistent agent interaction patterns
- **Flexible Input Handling**: Supports string URLs, dictionary objects, and arrays for versatile usage patterns
- **Parameter Validation**: Uses Pydantic `TrackURLSchema` for input validation and type safety
- **Synchronous and Asynchronous**: Supports both sync (`_run`) and async (`_arun`) execution modes

### Agent Workflow Integration
In multi-agent ESG research scenarios, the URL tracking tool enables:
- **Shared Crawling State**: All agents access the same URL tracking information for research coordination
- **Work Distribution**: Prevents multiple agents from crawling the same URLs simultaneously
- **Progress Visibility**: Agents can understand overall crawling progress and identify remaining work
- **Research Efficiency**: Streamlines web data collection across distributed agent research activities

## Memory System Integration

### File-Based Memory Storage
The tool interfaces with JSON-based memory files for rapid URL tracking:
- **Memory File Location**: `var/crawl_memory/{session_id}.json` for session-specific URL storage
- **Direct File Access**: Updates memory directly in file system for immediate URL deduplication
- **Error Resilience**: Gracefully handles missing or corrupted memory files with proper fallback mechanisms
- **Development Debugging**: Human-readable JSON format facilitates debugging and crawling workflow analysis

### Database Synchronization
While primarily operating on memory files, the tool integrates with database persistence:
- **Dual Persistence**: Memory changes are synchronized to PostgreSQL database for reliability and audit trails
- **Transaction Safety**: Database updates use proper transaction management for data consistency
- **Session Isolation**: URL tracking data is properly scoped by company name and session ID for security
- **Audit Trail**: Database storage provides permanent audit trail for URL visit activities and research workflows

## Input Parameter Flexibility

### Multi-Format URL Input Support
The tool supports flexible input formats for different usage patterns:
- **String URLs**: Simple string URLs for direct single-URL tracking operations
- **Dictionary Objects**: URL objects with additional metadata for enhanced tracking capabilities
- **Array Processing**: Can process arrays of URLs for bulk URL tracking operations and batch processing
- **Error Handling**: Gracefully handles malformed input with comprehensive error reporting and recovery

### Parameter Schema
```python
class TrackURLSchema(BaseModel):
    url: str = Field(description="The URL that was visited")
```

## ESG Research Workflow Integration

### Corporate Website Crawling
The URL tracking tool supports ESG research workflows by:
- **Sustainability Page Tracking**: Records visits to corporate sustainability and ESG reporting pages
- **Regulatory Compliance Monitoring**: Tracks crawling of regulatory filing websites and compliance portals
- **Third-Party ESG Analysis**: Manages URL visits across ESG rating agencies and research organizations
- **Multi-Domain Research**: Coordinates URL tracking across corporate websites and external ESG data sources

### Research Coordination Support
The tool provides infrastructure for coordinated ESG research:
- **Website Section Mapping**: Tracks visits to specific sections of corporate websites for comprehensive analysis
- **Content Discovery**: Enables agents to identify previously crawled pages to avoid redundant content processing
- **Research Timeline**: Maintains chronological order of website visits for ESG analysis workflows and reporting
- **Cross-Reference Tracking**: Supports tracking of inter-company relationships and ESG data cross-references

## Database Integration Context

### Analytics Database Tables
The underlying memory data synchronizes with PostgreSQL tables in the EkoIntelligence analytics database:

#### agent_sessions Table
- **Primary Storage**: Session metadata with JSON memory data containing URL tracking information
- **Schema**: `session_id` (TEXT), `company_name` (TEXT), `memory_data` (JSONB), timestamps
- **Indexing**: Optimized for company name queries to support multi-company research workflows and performance
- **Purpose**: Session tracking for AI agent interactions and workflows with persistent state management

#### URL Tracking Data Structure
Within the `memory_data` JSONB column, URL tracking information is stored as:
```json
{
  "visited_urls": [
    "https://tesla.com/impact/sustainability",
    "https://tesla.com/impact/environment",
    "https://tesla.com/impact/supply-chain"
  ],
  "last_position": {
    "url_index": 2
  },
  "updated_at": "2025-07-22T10:30:00Z"
}
```

### Data Persistence Strategy
- **File-First Approach**: URL tracking tool prioritizes file-based memory for performance and immediate access
- **Database Synchronization**: Memory managers handle synchronization between files and database for reliability
- **Atomic Updates**: URL tracking updates are atomic to prevent corruption during concurrent agent access
- **Recovery Mechanisms**: Database failures gracefully fall back to file-based memory persistence without data loss

## System Architecture Context

### EkoIntelligence Platform Integration
This tool fits within the broader EkoIntelligence ESG analysis system:
- **Analytics Backend**: Python-based CrewAI system orchestrates web crawling for comprehensive ESG data collection
- **Content Pipeline**: Tracked URLs feed into document processing, NLP analysis, and ESG scoring systems
- **URL Management Layer**: This tool provides URL tracking for document-based ESG research workflows and coordination
- **Customer Integration**: Crawled content analysis feeds customer-facing ESG dashboards, reports, and analytics

### Performance and Scalability Considerations
The tool is designed for high-performance URL management:
- **Minimal Latency**: File-based memory access provides immediate URL deduplication and tracking capabilities
- **Memory Efficiency**: Processes URL information without loading entire session state into memory
- **Concurrent Access**: Supports multiple agents tracking URLs simultaneously without conflicts or race conditions
- **Network Optimization**: Prevents duplicate web requests to minimize bandwidth usage and improve crawling speed

## Key Dependencies and Technologies

### CrewAI Framework Components
- **BaseTool Architecture**: Inherits from CrewAI's standardized tool interface for consistency across tool ecosystem
- **Pydantic Integration**: Uses Pydantic models for robust input validation and type safety across all operations
- **Agent System**: Designed for seamless integration with CrewAI multi-agent configurations and workflow orchestration

### Database and File System Technologies  
- **PostgreSQL**: Backend database storage for persistent URL tracking and session metadata with JSONB capabilities
- **JSON File System**: High-performance file-based memory storage for rapid URL tracking operations and caching
- **EkoIntelligence DB Layer**: `eko.db.get_bo_conn()` for database connectivity and transaction management

### Supporting Infrastructure
- **Loguru**: Comprehensive logging framework for debugging, monitoring, and audit trails of URL tracking activities
- **Python Type System**: Full type safety with appropriate type hints for maintainable and reliable code
- **Exception Handling**: Robust error handling ensures tool reliability in production crawling environments

## Usage Patterns and Examples

### Basic URL Tracking
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem.track_url import TrackURLTool

# Initialize URL tracking tool
memory_manager = CrewMemoryManager("Tesla Inc", "research_session_2024_07")
url_tool = TrackURLTool(
    memory_manager.company_name,
    memory_manager.session_id,
    memory_manager.memory_file
)

# Track a visited URL
result = url_tool._run("https://tesla.com/impact/sustainability-report-2023")
```

### Bulk URL Processing
```python
# Track multiple URLs at once
urls_to_track = [
    "https://tesla.com/impact/sustainability",
    "https://tesla.com/impact/environment", 
    "https://tesla.com/impact/supply-chain"
]
result = url_tool._run(urls_to_track)
```

### Agent Integration
```python
from crewai import Agent
from eko.agent.crewai.crawl.tools_mem import get_memory_tools

# Create agent with URL tracking capabilities
research_agent = Agent(
    role="ESG Website Research Analyst",
    goal="Systematically crawl corporate sustainability pages avoiding duplicates",
    tools=get_memory_tools(memory_manager),
    verbose=True
)
```

## Error Handling and Reliability

### Exception Safety
The tool implements comprehensive error handling:
- **File System Errors**: Gracefully handles missing or corrupted memory files with automatic recovery
- **URL Parsing Errors**: Recovers from malformed URL input with detailed error messages and continued operation
- **Parameter Validation**: Validates input parameters to ensure data integrity and prevent system failures
- **Database Failures**: Continues operation with file-based memory when database is temporarily unavailable

### Fault Tolerance
- **Graceful Degradation**: Tool continues to operate even when some components fail temporarily
- **Data Integrity**: Ensures URL tracking data remains consistent across all failure modes and recovery scenarios
- **Recovery Mechanisms**: Can recover from transient file system or database issues without data loss
- **Audit Logging**: All operations and errors are logged via Loguru for debugging, monitoring, and compliance

## Security and Access Control

### Session-Based Security
- **Company Isolation**: URL tracking data is isolated by company name to prevent cross-contamination of research
- **Session Scoping**: Each research session maintains separate URL tracking to ensure clean separation of workflows
- **URL Validation**: Validates URLs to ensure they meet security requirements and prevent malicious requests
- **Access Controls**: Integrates with EkoIntelligence access control systems for proper authorization

### Data Protection
- **Memory File Security**: File-based memory is stored in controlled application directories with appropriate permissions
- **Database Security**: Uses EkoIntelligence standard database authentication and access controls for data protection
- **Logging Security**: Sensitive URL information is properly sanitized in log outputs for security compliance
- **Request Security**: URL tracking includes validation to prevent unauthorized or malicious web requests

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/memory CrewAI Memory System Architecture  
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see https://loguru.readthedocs.io/en/stable/ Loguru Logging Documentation
@see backoffice/src/eko/agent/crewai/crawl/memory.py CrewMemoryManager Implementation
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/all_tools.py Memory Tools Factory
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/ Related Memory Tools Directory
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for tracking visited URLs during automated web crawling and ESG research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import json
from datetime import datetime
from typing import Dict, Any

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.db import get_bo_conn


class TrackURLSchema(BaseModel):
    """Input schema for the track URL tool."""
    url: str = Field(description="The URL that was visited")


class TrackURLTool(BaseTool):
    """
    Tool for tracking visited URLs.
    
    This tool records URLs that have been visited during the research process
    to avoid duplicate visits and to build a comprehensive record of the crawl.
    """
    
    company_name: str = None  # Added as a field
    session_id: str = None    # Added as a field
    memory_file: str = None   # Added as a field
    
    def __init__(self, company_name: str, session_id: str, memory_file: str):
        """
        Initialize the tool with company and session information.
        
        Args:
            company_name: The name of the target company
            session_id: The session ID for persistence
            memory_file: Path to the memory file for persistence
        """
        # Define name and description directly here
        name = "track_visited_url"
        description = """
        Track a URL that was visited during the research. Call this tool whenever you visit a new URL.
        This tool expects a single string URL, not a list or dictionary.
        Example: "https://example.com/page"
        """
        
        # Initialize the BaseTool with the name and description
        super().__init__(
            name=name, 
            description=description,
            args_schema=TrackURLSchema
        )
        
        # Store the company, session, and memory file information
        self.company_name = company_name
        self.session_id = session_id
        self.memory_file = memory_file
    
    def _load_memory(self) -> Dict[str, Any]:
        """
        Load the memory from the file system.
        
        Returns:
            Dict[str, Any]: The loaded memory
        """
        try:
            with open(self.memory_file, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading memory file: {e}")
            return {}
    
    def _save_memory(self, memory: Dict[str, Any]):
        """
        Save the memory to the file system.
        
        Args:
            memory: The memory to save
        """
        with open(self.memory_file, "w") as f:
            json.dump(memory, f, indent=2)
    
    def _sync_memory_to_db(self, memory: Dict[str, Any]):
        """
        Sync memory from file system to database.
        
        Args:
            memory: The memory to sync
        """
        try:
            memory["updated_at"] = datetime.now().isoformat()
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE agent_sessions 
                        SET memory_data = %s, updated_at = %s
                        WHERE session_id = %s
                        """,
                        (json.dumps(memory), datetime.now(), self.session_id)
                    )
                    conn.commit()
                    logger.debug(f"Synced memory to database for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error syncing memory to database: {e}")
    
    def _track_visited_url(self, url: str) -> bool:
        """
        Track a visited URL in memory and database.
        
        Args:
            url: The URL that was visited
            
        Returns:
            bool: True if the URL was newly tracked, False if it was already tracked
        """
        memory = self._load_memory()
        
        # Check if this URL is already tracked
        if url in memory.get("visited_urls", []):
            return False
            
        # Add to memory
        if "visited_urls" not in memory:
            memory["visited_urls"] = []
            
        memory["visited_urls"].append(url)
        
        # Update url index in last position
        if "last_position" not in memory:
            memory["last_position"] = {}
            
        memory["last_position"]["url_index"] = len(memory["visited_urls"]) - 1
        
        # Save updates
        self._save_memory(memory)
        self._sync_memory_to_db(memory)
        
        logger.debug(f"Tracked visited URL: {url}")
        return True
    
    def _run(self, url: str) -> str:
        """
        Run the tool to track a visited URL.
        
        Args:
            url: The URL that was visited
            
        Returns:
            str: Confirmation message
        """
        try:
            # Handle possible dict input
            if isinstance(url, dict) and "url" in url:
                url = url["url"]
                
            # Handle possible array input
            if isinstance(url, list):
                results = []
                for single_url in url:
                    if isinstance(single_url, dict) and "url" in single_url:
                        single_url = single_url["url"]
                    success = self._track_visited_url(str(single_url).strip())
                    results.append(f"{'Successfully tracked' if success else 'Already tracked'} URL: {single_url}")
                return "\n".join(results)
                
            # Ensure the URL is a string
            url = str(url).strip()
            
            # Track the URL
            success = self._track_visited_url(url)
            return f"{'Successfully tracked' if success else 'Already tracked'} URL: {url}"
        except Exception as e:
            logger.exception(f"Error tracking URL: {e}")
            return f"Error tracking URL: {str(e)}"
    
    async def _arun(self, url: str) -> str:
        """Async implementation of _run."""
        return self._run(url)
