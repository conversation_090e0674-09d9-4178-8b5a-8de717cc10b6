"""
CrewAI Web Crawling Progress Monitoring Tool

This module provides the `GetProgressTool` class, a specialized CrewAI tool for monitoring and reporting
the real-time progress of automated web crawling research sessions within the EkoIntelligence ESG
(Environmental, Social, Governance) analysis platform. The tool serves as a comprehensive progress
dashboard for AI agents, enabling them to understand current crawling status, coordinate activities,
and make informed decisions about research strategy and task prioritization.

## Core Functionality
- **Progress Summarization**: Provides comprehensive summaries of crawling progress across multiple dimensions
- **Real-time Metrics**: Reports current statistics on visited URLs, downloaded files, insights, and completed tasks
- **Coordination Support**: Enables multiple agents to coordinate research activities and avoid redundant work
- **Session State Visibility**: Offers transparency into current session progress and position tracking
- **Memory Integration**: Interfaces with the CrewAI memory system for persistent progress tracking

## Progress Monitoring Capabilities

### Quantitative Metrics
The tool provides detailed counts and statistics for key crawling activities:
- **URL Coverage**: Total visited URLs with recent activity highlighting (last 5 URLs)
- **Content Acquisition**: Downloaded files count with source URL tracking for content provenance
- **Intelligence Gathering**: Discovered insights count with category-based classification
- **Task Completion**: Completed tasks tracking for workflow progress measurement
- **Position Tracking**: Current task and URL index positions for resuming interrupted sessions

### Qualitative Analysis
Beyond raw metrics, the tool provides analytical insights:
- **Recent Activity Analysis**: Focuses on the last 5 URLs visited to understand current research direction
- **Search Strategy Review**: Displays recent search queries to show research methodology evolution
- **Insight Categorization**: Groups discovered insights by category to highlight research themes
- **Progress Patterns**: Identifies trending categories and research focus areas for strategic guidance

## CrewAI Framework Integration

### Tool Architecture
The `GetProgressTool` inherits from CrewAI's `BaseTool` class, providing:
- **Standardized Interface**: Follows CrewAI tool conventions for consistent agent interaction
- **Parameter-less Operation**: Uses empty schema as progress queries require no input parameters
- **Synchronous and Asynchronous**: Supports both sync (`_run`) and async (`_arun`) execution modes
- **Error Handling**: Graceful failure handling with comprehensive logging via Loguru

### Agent Coordination Model
In multi-agent research scenarios, the progress tool enables:
- **Shared State Awareness**: All agents access the same progress information for coordination
- **Work Distribution**: Agents can identify gaps and avoid duplicating research efforts
- **Progress Checkpoints**: Provides natural synchronization points for complex multi-step workflows
- **Research Strategy Adaptation**: Agents can adjust tactics based on collective progress insights

## Memory System Integration

### File-Based Memory Access
The tool interfaces with JSON-based memory files stored in the application's variable directory:
- **Memory File Location**: `var/crawl_memory/{session_id}.json` for session-specific storage
- **Direct File Access**: Reads memory directly from file system for rapid progress queries
- **Error Resilience**: Gracefully handles missing or corrupted memory files with empty state fallback
- **Development Debugging**: Human-readable JSON format facilitates debugging and development

### Memory Data Structure
The tool expects and interprets specific memory data structures:
- **visited_urls**: Array of URL strings representing crawled web pages
- **downloaded_files**: Array of file objects with source URL mappings
- **insights**: Array of insight objects with categories, confidence scores, and content
- **tasks_completed**: Array of completed task identifiers for workflow tracking
- **last_position**: Object containing current task_index, url_index, and active search queries

## Database Integration Context

While the progress tool primarily operates on memory files for performance, it integrates with
the broader EkoIntelligence database architecture:

### Analytics Database Tables
The underlying memory data synchronizes with several PostgreSQL tables:
- **agent_sessions**: Session metadata and memory snapshots with JSON storage
- **agent_insights**: Structured insight storage with ESG categorization and confidence scoring
- **agent_reports**: Final research reports with progress metrics and summary statistics
- **agent_tool_usage**: Tool usage analytics for performance monitoring and optimization

### Data Synchronization Strategy
- **File-First Approach**: Progress tool prioritizes file-based memory for speed
- **Database Synchronization**: Memory managers handle synchronization between files and database
- **Dual Persistence**: Ensures data reliability through redundant storage mechanisms
- **Performance Optimization**: File system queries provide immediate response without database latency

## ESG Research Workflow Integration

### Corporate Research Sessions
The progress tool supports ESG research workflows by:
- **Company-Specific Tracking**: Progress is isolated by company name for focused research sessions
- **Sustainability Focus**: Categorizes insights by ESG themes including environmental impact and governance
- **Regulatory Compliance**: Tracks progress on regulatory filing analysis and compliance document research
- **Multi-Source Integration**: Monitors progress across corporate websites, reports, and third-party sources

### Research Quality Assurance
The tool provides visibility into research quality through:
- **Coverage Analysis**: Identifies potential gaps in URL coverage and content acquisition
- **Source Diversity**: Highlights the variety of sources being analyzed for comprehensive coverage
- **Insight Quality**: Provides visibility into the types and confidence levels of discovered insights
- **Research Depth**: Tracks progress through different levels of analysis and investigation

## System Architecture Context

### EkoIntelligence Platform Integration
This tool fits within the broader EkoIntelligence ESG analysis system:
- **Analytics Backend**: Python-based system orchestrates CrewAI agents for automated research
- **Memory Layer**: This tool provides visibility into the persistent memory system used by agents
- **Data Pipeline**: Research progress feeds into downstream NLP analysis and greenwashing detection
- **Customer Integration**: Progress insights inform customer-facing dashboards and research reports

### Performance and Scalability
The tool is designed for high-performance operation:
- **Minimal Latency**: File-based memory access provides immediate progress information
- **Memory Efficiency**: Processes memory data without loading entire session state into memory
- **Concurrent Access**: Supports multiple agents querying progress simultaneously
- **Session Isolation**: Progress data is properly isolated by company and session identifiers

## Key Dependencies and Technologies

### CrewAI Framework Components
- **BaseTool Architecture**: Inherits from CrewAI's standardized tool interface
- **Pydantic Integration**: Uses Pydantic BaseModel for input schema validation
- **Agent System**: Designed for seamless integration with CrewAI agent configurations

### Database and Persistence Technologies
- **PostgreSQL**: Backend database storage for session metadata and structured insights
- **JSON File System**: High-performance file-based memory storage for rapid access
- **EkoIntelligence DB Layer**: `eko.db.get_bo_conn()` for database connectivity and transaction management

### Supporting Infrastructure
- **Loguru**: Comprehensive logging framework for debugging, monitoring, and error tracking
- **Python Type System**: Full type safety with appropriate type hints for maintainable code
- **Exception Handling**: Robust error handling ensures tool reliability in production environments

## Usage Patterns and Examples

### Basic Progress Query
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem.get_progress import GetProgressTool

# Initialize progress tool
memory_manager = CrewMemoryManager("Tesla Inc", "research_session_2024_07")
progress_tool = GetProgressTool(
    memory_manager.company_name,
    memory_manager.session_id, 
    memory_manager.memory_file
)

# Query current progress
progress_summary = progress_tool._run()
print(progress_summary)
```

### Agent Integration
```python
from crewai import Agent
from eko.agent.crewai.crawl.tools_mem import get_memory_tools

# Create agent with progress monitoring
research_agent = Agent(
    role="ESG Research Coordinator",
    goal="Monitor and coordinate multi-agent research activities",
    tools=get_memory_tools(memory_manager),
    verbose=True
)
```

## Error Handling and Reliability

### Exception Safety
The tool implements comprehensive error handling:
- **File System Errors**: Gracefully handles missing or corrupted memory files
- **JSON Parsing Errors**: Returns empty state when memory files contain invalid JSON
- **Memory Structure Errors**: Safely handles missing or malformed memory data structures
- **Logging Integration**: All errors are logged via Loguru for debugging and monitoring

### Fault Tolerance
- **Graceful Degradation**: Tool continues to operate even when memory data is incomplete
- **Empty State Handling**: Provides meaningful progress reports even with empty memory
- **Recovery Mechanisms**: Can recover from transient file system or memory issues
- **Consistent Interface**: Maintains consistent return format regardless of internal errors

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/memory CrewAI Memory System Architecture
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see https://loguru.readthedocs.io/en/stable/ Loguru Logging Documentation
@see backoffice/src/eko/agent/crewai/crawl/memory.py CrewMemoryManager Implementation
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/all_tools.py Memory Tools Factory
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/ Related Memory Tools
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for monitoring and reporting web crawling progress in ESG research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import json
from typing import Dict, Any

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel


class GetProgressSchema(BaseModel):
    """Empty schema for the get progress tool as it takes no parameters."""
    pass


class GetProgressTool(BaseTool):
    """
    Tool for getting crawl progress summary.
    
    This tool provides a summary of the current crawl progress,
    including visited URLs, insights, and task completion status.
    """
    
    company_name: str = None  # Added as a field
    session_id: str = None    # Added as a field
    memory_file: str = None   # Added as a field
    
    def __init__(self, company_name: str, session_id: str, memory_file: str):
        """
        Initialize the tool with company and session information.
        
        Args:
            company_name: The name of the target company
            session_id: The session ID for persistence
            memory_file: Path to the memory file for persistence
        """
        # Define name and description directly here
        name = "get_crawl_progress"
        description = """
        Get a summary of the current crawl progress.
        This tool returns information about visited URLs, downloaded files, 
        discovered insights, and tasks completed so far.
        """
        
        # Initialize the BaseTool with the name and description
        super().__init__(
            name=name, 
            description=description,
            args_schema=GetProgressSchema
        )
        
        # Store the company, session, and memory file information
        self.company_name = company_name
        self.session_id = session_id
        self.memory_file = memory_file
    
    def _load_memory(self) -> Dict[str, Any]:
        """
        Load the memory from the file system.
        
        Returns:
            Dict[str, Any]: The loaded memory
        """
        try:
            with open(self.memory_file, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading memory file: {e}")
            return {}
    
    def _get_crawl_progress_summary(self) -> str:
        """
        Get a summary of the crawl progress.
        
        Returns:
            str: A summary of the crawl progress
        """
        memory = self._load_memory()
        
        visited_urls_count = len(memory.get("visited_urls", []))
        downloaded_files_count = len(memory.get("downloaded_files", []))
        insights_count = len(memory.get("insights", []))
        tasks_completed_count = len(memory.get("tasks_completed", []))
        
        # Last positions
        current_task_index = memory.get("last_position", {}).get("task_index", 0)
        current_url_index = memory.get("last_position", {}).get("url_index", 0)
        search_queries = memory.get("last_position", {}).get("search_queries", [])
        
        # Recent URLs (last 5)
        recent_urls = memory.get("visited_urls", [])[-5:] if visited_urls_count > 0 else []
        
        # Top categories of insights
        categories = {}
        for insight in memory.get("insights", []):
            category = insight.get("category", "unknown")
            categories[category] = categories.get(category, 0) + 1
        
        top_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)[:3]
        
        # Format the summary
        summary = f"Crawl Progress for {self.company_name} (Session: {self.session_id})\n\n"
        summary += f"Progress Summary:\n"
        summary += f"- Visited URLs: {visited_urls_count}\n"
        summary += f"- Downloaded files: {downloaded_files_count}\n"
        summary += f"- Insights discovered: {insights_count}\n"
        summary += f"- Tasks completed: {tasks_completed_count}\n\n"
        
        summary += f"Current Position:\n"
        summary += f"- Current task index: {current_task_index}\n"
        summary += f"- Current URL index: {current_url_index}\n"
        
        if recent_urls:
            summary += f"\nRecent URLs visited:\n"
            for i, url in enumerate(recent_urls):
                summary += f"- {url}\n"
        
        if search_queries:
            summary += f"\nSearch queries used:\n"
            for i, query in enumerate(search_queries[-5:]):  # Show last 5 queries
                summary += f"- {query}\n"
        
        if top_categories:
            summary += f"\nTop insight categories:\n"
            for category, count in top_categories:
                summary += f"- {category}: {count} insights\n"
        
        return summary
    
    def _run(self) -> str:
        """
        Run the tool to get crawl progress summary.
        
        Returns:
            str: Crawl progress summary
        """
        try:
            return self._get_crawl_progress_summary()
        except Exception as e:
            logger.exception(f"Error getting crawl progress: {e}")
            return f"Error getting crawl progress: {str(e)}"
    
    async def _arun(self) -> str:
        """Async implementation of _run."""
        return self._run()