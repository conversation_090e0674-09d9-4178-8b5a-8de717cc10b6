# CrewAI Web Crawler Memory Tools Module

## Overview

The `tools_mem` module provides comprehensive memory management capabilities for CrewAI-powered web crawling agents
within the EkoIntelligence ESG (Environmental, Social, Governance) analysis platform. This module serves as the
persistent memory layer for automated AI agents performing corporate sustainability research, enabling them to track
progress, store insights, and maintain state across complex multi-session crawling workflows.

The module implements a factory pattern to provide five specialized memory tools that work together to create a robust,
coordinated research system where multiple AI agents can collaborate on ESG data collection while avoiding redundant
work and maintaining comprehensive audit trails.

## Specification

### Core Requirements

- **Persistent Memory Management**: Dual storage architecture using both file system (JSON) and PostgreSQL database
- **Multi-Agent Coordination**: Enable multiple CrewAI agents to share memory state and coordinate research activities
- **ESG Research Focus**: Specialized for corporate sustainability research with categorized insight storage
- **Session Isolation**: Complete separation of memory data by company name and session ID
- **Progress Tracking**: Real-time monitoring and reporting of crawling progress across agents
- **Deduplication**: Intelligent prevention of redundant URLs, files, insights, and search queries

### Memory Architecture

```mermaid
graph TB
    subgraph "Memory Storage"
        FS[File System JSON]
        DB[(PostgreSQL Database)]
        FS <--> DB
    end
    
    subgraph "Memory Tools"
        TU[TrackURLTool]
        TF[TrackFileTool] 
        TI[TrackInsightTool]
        TSQ[TrackSearchQueryTool]
        GP[GetProgressTool]
    end
    
    subgraph "CrewAI Framework"
        A1[Agent 1]
        A2[Agent 2]
        A3[Agent N]
    end
    
    Factory[get_memory_tools] --> TU
    Factory --> TF
    Factory --> TI
    Factory --> TSQ
    Factory --> GP
    
    A1 --> Factory
    A2 --> Factory
    A3 --> Factory
    
    TU --> FS
    TF --> FS
    TI --> FS
    TSQ --> FS
    GP --> FS
```

## Key Components

### Core Files

| File                        | Purpose                              | Key Functionality                                                 |
|-----------------------------|--------------------------------------|-------------------------------------------------------------------|
| **`__init__.py`**           | Module entry point and documentation | Factory pattern implementation for tool access                    |
| **`all_tools.py`**          | Memory tools factory                 | Creates and configures all memory tools with shared session data  |
| **`get_progress.py`**       | Progress monitoring tool             | Real-time crawling progress summaries and coordination            |
| **`track_file.py`**         | File tracking tool                   | Downloaded document management with source attribution            |
| **`track_insight.py`**      | ESG insights tool                    | Structured storage of sustainability insights with categorization |
| **`track_search_query.py`** | Search query tracker                 | Query history and research audit trail management                 |
| **`track_url.py`**          | URL tracking tool                    | Visited URL deduplication and crawling state management           |

### Factory Pattern Implementation

The module uses a factory pattern (`get_memory_tools()`) to ensure consistent configuration across all tools:

```python
def get_memory_tools(memory_manager: CrewMemoryManager) -> List[Any]:
    company_name = memory_manager.company_name
    session_id = memory_manager.session_id
    memory_file = memory_manager.memory_file
    
    return [
        TrackURLTool(company_name, session_id, memory_file),
        TrackFileTool(company_name, session_id, memory_file),
        TrackInsightTool(company_name, session_id, memory_file),
        TrackSearchQueryTool(company_name, session_id, memory_file),
        GetProgressTool(company_name, session_id, memory_file)
    ]
```

### Tool Specifications

#### TrackURLTool

- **Purpose**: Prevent duplicate URL visits during web crawling
- **Input**: URL strings, dictionaries, or arrays
- **Storage**: Maintains ordered list of visited URLs with position tracking
- **Integration**: Updates `visited_urls` array and `last_position.url_index`

#### TrackFileTool

- **Purpose**: Manage downloaded documents with source attribution
- **Input**: File path and source URL pairs
- **Storage**: File metadata with download timestamps and source mapping
- **Features**: Duplicate prevention, batch processing, JSON input support

#### TrackInsightTool

- **Purpose**: Store categorized ESG insights from research
- **Input**: Category, behavior type, description, source, confidence score
- **Categories**: Environmental, Social, Governance
- **Behavior Types**: Positive, Negative, Neutral
- **Database**: Synchronized to `agent_insights` table for structured analysis

#### TrackSearchQueryTool

- **Purpose**: Maintain search query history for audit and optimization
- **Input**: Search query strings
- **Storage**: Chronological query list in `last_position.search_queries`
- **Benefits**: Prevents duplicate searches, enables strategy analysis

#### GetProgressTool

- **Purpose**: Provide comprehensive progress summaries for agent coordination
- **Output**: Formatted progress reports with metrics and recent activity
- **Metrics**: URL count, file count, insight count, task completion, recent activity

## Dependencies

### External Frameworks

- **CrewAI Framework**: Multi-agent orchestration and tool management
    - `crewai.tools.BaseTool`: Base class for all memory tools
    - Agent integration and memory system compatibility
    - Pydantic schema validation for input parameters

### Database Technologies

- **PostgreSQL**: Persistent storage for session data and insights
    - `agent_sessions`: Session metadata with JSONB memory data
    - `agent_insights`: Structured ESG insights with categorization
    - `agent_reports`: Final research reports and aggregated findings
    - Connection management via `eko.db.get_bo_conn()`

### Supporting Libraries

- **Loguru**: Comprehensive logging for debugging and monitoring
- **Pydantic**: Type-safe input validation and schema definitions
- **JSON**: File-based memory storage for rapid access and debugging
- **Python Type Hints**: Full type safety throughout the module

### EkoIntelligence Integration

- **Memory Manager**: `eko.agent.crewai.crawl.memory.CrewMemoryManager`
- **Database Layer**: `eko.db.get_bo_conn()` for connection pooling
- **Session Management**: Company and session-based data isolation

## Usage Examples

### Basic Memory Tools Initialization

```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem import get_memory_tools
from crewai import Agent

# Initialize memory manager for company research session
memory_manager = CrewMemoryManager("Tesla Inc", "research_session_2024_07")

# Get all memory tools configured for the session
tools = get_memory_tools(memory_manager)

# Create agent with memory capabilities
research_agent = Agent(
    role="ESG Research Analyst",
    goal="Analyze corporate sustainability practices comprehensively",
    backstory="Expert analyst specializing in ESG data collection and analysis",
    tools=tools,
    memory=True,
    verbose=True
)
```

### Multi-Agent Coordination Example

```python
from crewai import Crew, Process

# Initialize shared memory manager
memory_manager = CrewMemoryManager("Microsoft Corp", "esg_analysis_2024")
shared_tools = get_memory_tools(memory_manager)

# Create specialized agents with shared memory
environmental_agent = Agent(
    role="Environmental Impact Analyst",
    goal="Research environmental sustainability practices",
    tools=shared_tools,
    memory=True
)

social_agent = Agent(
    role="Social Responsibility Analyst", 
    goal="Analyze social impact and labor practices",
    tools=shared_tools,
    memory=True
)

governance_agent = Agent(
    role="Corporate Governance Analyst",
    goal="Evaluate governance structures and ethics",
    tools=shared_tools,
    memory=True
)

# Create coordinated research crew
esg_crew = Crew(
    agents=[environmental_agent, social_agent, governance_agent],
    process=Process.sequential,
    memory=True,
    verbose=True
)
```

### Individual Tool Usage

```python
# Track a visited URL
url_result = agent.tools[0]._run("https://tesla.com/sustainability-report-2023")

# Track downloaded file
file_result = agent.tools[1]._run(
    file_path="reports/tesla_sustainability_2023.pdf",
    source_url="https://tesla.com/sustainability-report-2023"
)

# Store ESG insight
insight_result = agent.tools[2]._run(
    category="Environmental",
    behavior_type="Positive", 
    description="Tesla reports 100% renewable energy usage in manufacturing",
    source_url="https://tesla.com/sustainability-report-2023",
    source_title="Tesla Sustainability Report 2023",
    confidence=0.95
)

# Get current progress
progress = agent.tools[4]._run()
print(progress)
```

## Architecture Notes

### Memory Data Flow

```mermaid
sequenceDiagram
    participant Agent
    participant Tool
    participant FileSystem
    participant Database
    
    Agent->>Tool: Execute tool with data
    Tool->>FileSystem: Load current memory
    Tool->>Tool: Process and validate input
    Tool->>Tool: Check for duplicates
    Tool->>FileSystem: Update memory JSON
    Tool->>Database: Sync to PostgreSQL
    Tool->>Agent: Return confirmation
```

### Database Schema Integration

```mermaid
erDiagram
    agent_sessions {
        text session_id PK
        text company_name
        jsonb memory_data
        timestamptz created_at
        timestamptz updated_at
    }
    
    agent_insights {
        bigint id PK
        text session_id FK
        text company_name
        text category
        text behavior_type
        text description
        text source_url
        text source_title
        double_precision confidence
        jsonb metadata
        timestamptz extracted_at
        timestamptz created_at
    }
    
    agent_reports {
        bigint id PK
        text session_id FK
        text company_name
        jsonb report_data
        timestamptz created_at
    }
    
    agent_sessions ||--o{ agent_insights : "contains"
    agent_sessions ||--o{ agent_reports : "generates"
```

### Error Handling Strategy

```mermaid
flowchart TD
    A[Tool Execution] --> B{File System Available?}
    B -->|Yes| C[Load Memory from File]
    B -->|No| D[Initialize Empty Memory]
    
    C --> E{Valid JSON?}
    E -->|Yes| F[Process Tool Logic]
    E -->|No| G[Log Error, Use Empty State]
    
    D --> F
    G --> F
    
    F --> H{Update Successful?}
    H -->|Yes| I[Save to File System]
    H -->|No| J[Log Error, Continue]
    
    I --> K{Database Available?}
    K -->|Yes| L[Sync to Database]
    K -->|No| M[Log Warning, File Only]
    
    J --> N[Return Error Message]
    L --> O[Return Success Message]
    M --> O
```

## Known Issues

Based on Linear ticket analysis and code review:

### Current Issues

- **Performance Optimization Needed**: Large memory files may impact performance during concurrent agent access
- **Memory File Size Growth**: Long research sessions can create large JSON files that slow down operations
- **Database Synchronization Lag**: High-frequency updates may cause temporary inconsistencies between file and database
  storage

### Technical Debt

- **Error Recovery**: Limited recovery mechanisms for corrupted memory files
- **Concurrent Access**: Race conditions possible with multiple agents updating memory simultaneously
- **Memory Cleanup**: No automatic cleanup of old or abandoned session data

### Monitoring Gaps

- **Tool Usage Analytics**: Limited metrics on tool performance and usage patterns
- **Memory Growth Tracking**: No built-in monitoring of memory file size growth
- **Session Lifecycle**: No automatic session timeout or cleanup mechanisms

## Future Work

### Planned Enhancements (Based on EKO-279 Admin Pages Project)

- **Admin Interface Integration**: Tools will integrate with the new admin pages for session monitoring
- **Quota Management**: Memory usage quotas per organization/individual
- **Feature Flag Support**: Tool behavior customization through feature flags
- **User Management**: Session access control and user permission management

### Performance Improvements

- **Memory Compression**: Implement compression for large memory files
- **Database Optimization**: Add indexes and query optimization for better performance
- **Caching Layer**: Redis integration for frequently accessed memory data
- **Batch Operations**: Optimize database synchronization with batched updates

### Monitoring and Analytics

- **OpenTelemetry Integration**: Add comprehensive metrics and tracing
- **Dashboard Integration**: Real-time monitoring through Next.js admin dashboard
- **Cost Tracking**: LLM usage and API cost tracking per session
- **Performance Metrics**: Tool execution time and success rate monitoring

### Scalability Enhancements

- **Distributed Memory**: Support for distributed memory across multiple instances
- **Session Partitioning**: Horizontal scaling through session-based data partitioning
- **Load Balancing**: Multi-instance support with shared database coordination
- **Archive System**: Automatic archiving of completed research sessions

## Troubleshooting

### Common Issues

#### Memory File Corruption

**Symptoms**: JSON parsing errors, tools returning empty results
**Solution**:

```bash
# Check memory file validity
python -m json.tool var/crawl_memory/session_id.json

# Reset memory file if corrupted
rm var/crawl_memory/session_id.json
# Tools will reinitialize with empty state
```

#### Database Connection Failures

**Symptoms**: Memory synchronization errors in logs
**Solution**:

```bash
# Check database connectivity
cd backoffice && ./bin/run_in_db.sh "SELECT 1"

# Verify agent_sessions table exists
cd backoffice && ./bin/run_in_db.sh "SELECT COUNT(*) FROM agent_sessions"
```

#### Tool Performance Issues

**Symptoms**: Slow tool execution, timeout errors
**Solution**:

```bash
# Check memory file sizes
ls -la var/crawl_memory/

# Clean up large memory files
find var/crawl_memory/ -size +10M -name "*.json"

# Monitor database performance
cd backoffice && ./bin/run_in_db.sh "SELECT session_id, LENGTH(memory_data::text) FROM agent_sessions ORDER BY LENGTH(memory_data::text) DESC LIMIT 10"
```

### Debug Commands

```bash
# Monitor tool usage in real-time
tail -f var/logs/crewai.log | grep "TrackFileTool\|TrackURLTool\|TrackInsightTool"

# Check session status
cd backoffice && ./bin/run_in_db.sh "SELECT session_id, company_name, updated_at FROM agent_sessions WHERE updated_at > NOW() - INTERVAL '1 hour'"

# Validate memory synchronization
cd backoffice && ./bin/run_in_db.sh "SELECT session_id, CASE WHEN memory_data IS NOT NULL THEN 'synced' ELSE 'not_synced' END FROM agent_sessions"
```

## FAQ

### User-Centric Questions and Answers

**Q: How do I create a new research session for a company?**
A: Initialize a `CrewMemoryManager` with the company name and unique session ID, then use `get_memory_tools()` to create
tools for your agents:

```python
memory_manager = CrewMemoryManager("Company Name", "unique_session_id")
tools = get_memory_tools(memory_manager)
```

**Q: Can multiple agents work on the same research session simultaneously?**
A: Yes, all agents using the same `memory_manager` will share memory state and coordinate their activities automatically
through the memory tools.

**Q: How do I prevent agents from downloading the same files multiple times?**
A: The `TrackFileTool` automatically prevents duplicate downloads by checking if a file path has already been recorded
in the session memory.

**Q: What happens if an agent crashes during research?**
A: Memory is persistently stored in both files and database. When agents restart, they can resume from the last known
position using the stored memory state.

**Q: How do I monitor the progress of a multi-agent research session?**
A: Use the `GetProgressTool` which provides comprehensive summaries including visited URLs, downloaded files, discovered
insights, and recent activity.

**Q: Can I customize the ESG insight categories?**
A: Currently, the system supports Environmental, Social, and Governance categories with Positive, Negative, and Neutral
behavior types. Custom categories require code modifications.

**Q: How do I handle memory files that become too large?**
A: Monitor file sizes in `var/crawl_memory/` and consider splitting long research sessions into multiple smaller
sessions, or contact administrators for memory optimization.

**Q: What should I do if database synchronization fails?**
A: Tools will continue operating with file-based memory only. Check database connectivity and logs for specific error
messages. Memory will sync when database access is restored.

## References

### Documentation Links

- [CrewAI Framework Documentation](https://docs.crewai.com/)
- [CrewAI Tools Framework](https://docs.crewai.com/concepts/tools)
- [CrewAI Memory System](https://docs.crewai.com/concepts/memory)
- [Pydantic Data Validation](https://pydantic-docs.helpmanual.io/)
- [Loguru Logging Framework](https://loguru.readthedocs.io/en/stable/)
- [PostgreSQL JSONB Documentation](https://www.postgresql.org/docs/current/datatype-json.html)

### Code Files

- [`memory.py`](../memory.py) - CrewMemoryManager Implementation
- [`all_tools.py`](./all_tools.py) - Memory Tools Factory
- [`get_progress.py`](./get_progress.py) - Progress Monitoring Tool
- [`track_file.py`](./track_file.py) - File Tracking Tool
- [`track_insight.py`](./track_insight.py) - ESG Insights Tool
- [`track_search_query.py`](./track_search_query.py) - Search Query Tracker
- [`track_url.py`](./track_url.py) - URL Tracking Tool

### Database References

- [`get_bo_conn()`](../../../db/__init__.py) - Database Connection Management
- Schema: `agent_sessions`, `agent_insights`, `agent_reports`, `agent_tool_usage`

### Third-Party Dependencies

- [CrewAI GitHub Repository](https://github.com/crewaiinc/crewai)
- [CrewAI Tools Repository](https://github.com/crewaiinc/crewai-tools)
- [PostgreSQL Official Documentation](https://www.postgresql.org/docs/)
- [Python Type Hints Documentation](https://docs.python.org/3/library/typing.html)

---

## Changelog

### 2025-07-27

- **Created comprehensive README.md** with full module documentation
- **Added architecture diagrams** using Mermaid for memory flow and database schema
- **Documented all five memory tools** with specifications and usage examples
- **Included troubleshooting section** with common issues and debug commands
- **Added FAQ section** with user-centric questions and answers
- **Provided complete reference links** to related documentation and code files

---

(c) All rights reserved ekoIntelligence 2025
