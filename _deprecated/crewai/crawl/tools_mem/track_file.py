"""
CrewAI File Tracking Tool for Web Crawling Memory Management

This module provides the `TrackFileTool` class, a specialized CrewAI tool for tracking and managing
downloaded files during automated web crawling research sessions within the EkoIntelligence ESG
(Environmental, Social, Governance) analysis platform. The tool serves as a comprehensive file
management system for AI agents, enabling them to record downloaded documents, maintain source
attribution, and prevent duplicate downloads across multi-agent research workflows.

## Core Functionality
- **File Download Tracking**: Records downloaded files with complete source URL attribution for provenance
- **Duplicate Prevention**: Maintains comprehensive file registry to avoid redundant downloads
- **Source Attribution**: Links every downloaded file back to its originating web page URL for context
- **Multi-Format Support**: Handles various document formats including PDFs, reports, and web content
- **Memory Integration**: Interfaces with CrewAI memory system for persistent file tracking across sessions

## File Management Capabilities

### Download Recording
The tool provides detailed tracking for file download activities:
- **File Path Storage**: Records complete file paths relative to the crawling session directory
- **Source URL Mapping**: Maintains bidirectional mapping between downloaded files and source URLs
- **Download Timestamps**: Records when each file was downloaded for session timeline tracking
- **Metadata Preservation**: Stores additional file metadata for content categorization and analysis

### Duplicate Detection and Prevention
Advanced duplicate handling ensures efficient resource utilization:
- **Path-Based Deduplication**: Prevents re-downloading files to the same file path location
- **Content Verification**: Checks existing files to determine if downloads are already complete
- **Resource Optimization**: Reduces bandwidth usage and storage requirements through smart deduplication
- **Agent Coordination**: Enables multiple agents to share file download information efficiently

## CrewAI Framework Integration

### Tool Architecture
The `TrackFileTool` inherits from CrewAI's `BaseTool` class, providing:
- **Standardized Interface**: Follows CrewAI tool conventions for consistent agent interaction patterns
- **Flexible Input Handling**: Supports both JSON string parameters and individual arguments for versatility
- **Parameter Validation**: Uses Pydantic `TrackFileSchema` for input validation and type safety
- **Synchronous and Asynchronous**: Supports both sync (`_run`) and async (`_arun`) execution modes

### Agent Workflow Integration
In multi-agent research scenarios, the file tracking tool enables:
- **Shared File Registry**: All agents access the same file tracking information for coordination
- **Download Coordination**: Prevents multiple agents from downloading the same files simultaneously
- **Content Discovery**: Agents can identify previously downloaded content for analysis workflows
- **Research Efficiency**: Streamlines document collection across distributed agent activities

## Memory System Integration

### File-Based Memory Storage
The tool interfaces with JSON-based memory files for rapid file tracking:
- **Memory File Location**: `var/crawl_memory/{session_id}.json` for session-specific file storage
- **Direct File Access**: Updates memory directly in file system for immediate file tracking
- **Error Resilience**: Gracefully handles missing or corrupted memory files with proper fallback
- **Development Debugging**: Human-readable JSON format facilitates debugging and development

### Database Synchronization
While primarily operating on memory files, the tool integrates with database persistence:
- **Dual Persistence**: Memory changes are synchronized to PostgreSQL database for reliability
- **Transaction Safety**: Database updates use proper transaction management for data consistency
- **Session Isolation**: File tracking data is properly scoped by company name and session ID
- **Audit Trail**: Database storage provides permanent audit trail for file download activities

## Input Parameter Flexibility

### JSON String Input Support
The tool supports flexible input formats for different usage patterns:
- **Single JSON Parameter**: Accepts complete file information as a JSON string for batch operations
- **Individual Parameters**: Supports separate `file_path` and `source_url` parameters for direct calls
- **Array Processing**: Can process arrays of file objects for bulk file tracking operations
- **Error Handling**: Gracefully handles malformed JSON input with comprehensive error reporting

### Parameter Schema
```python
class TrackFileSchema(BaseModel):
    file_path: str = Field(description="The path to the downloaded file")
    source_url: str = Field(description="The URL the file was downloaded from")
```

## ESG Research Workflow Integration

### Corporate Document Management
The file tracking tool supports ESG research workflows by:
- **Sustainability Report Tracking**: Records downloads of corporate sustainability and ESG reports
- **Regulatory Filing Management**: Tracks SEC filings, regulatory documents, and compliance reports
- **Third-Party Analysis**: Manages downloads from ESG rating agencies and research organizations
- **Multi-Source Integration**: Coordinates file downloads across corporate websites and external sources

### Content Categorization Support
The tool provides infrastructure for document categorization:
- **File Type Recognition**: Identifies document types based on file extensions and source URLs
- **Content Source Attribution**: Links files to specific sections of corporate websites for context
- **Report Classification**: Supports classification of annual reports, sustainability reports, and regulatory filings
- **Research Timeline**: Maintains chronological order of document downloads for analysis workflows

## Database Integration Context

### Analytics Database Tables
The underlying memory data synchronizes with PostgreSQL tables in the EkoIntelligence analytics database:

#### agent_sessions Table
- **Primary Storage**: Session metadata with JSON memory data containing file tracking information
- **Schema**: `session_id` (TEXT), `company_name` (TEXT), `memory_data` (JSONB), timestamps
- **Indexing**: Optimized for company name queries to support multi-company research workflows

#### File Tracking Data Structure
Within the `memory_data` JSONB column, file tracking information is stored as:
```json
{
  "downloaded_files": [
    {
      "path": "reports/tesla_sustainability_2023.pdf",
      "source_url": "https://tesla.com/impact/sustainability-report-2023",
      "downloaded_at": "2024-07-22T10:30:00Z"
    }
  ]
}
```

### Data Persistence Strategy
- **File-First Approach**: File tracking tool prioritizes file-based memory for performance
- **Database Synchronization**: Memory managers handle synchronization between files and database
- **Atomic Updates**: File tracking updates are atomic to prevent corruption during concurrent access
- **Recovery Mechanisms**: Database failures gracefully fall back to file-based memory persistence

## System Architecture Context

### EkoIntelligence Platform Integration
This tool fits within the broader EkoIntelligence ESG analysis system:
- **Analytics Backend**: Python-based CrewAI system orchestrates document collection for ESG analysis
- **Content Pipeline**: Downloaded files feed into NLP analysis, greenwashing detection, and scoring systems
- **File Management Layer**: This tool provides file tracking for document-based ESG research workflows
- **Customer Integration**: Downloaded content analysis feeds customer-facing ESG dashboards and reports

### Performance and Scalability Considerations
The tool is designed for high-performance file management:
- **Minimal Latency**: File-based memory access provides immediate file tracking capabilities
- **Memory Efficiency**: Processes file information without loading entire session state
- **Concurrent Access**: Supports multiple agents tracking files simultaneously without conflicts
- **Storage Optimization**: Prevents duplicate downloads to minimize storage requirements

## Key Dependencies and Technologies

### CrewAI Framework Components
- **BaseTool Architecture**: Inherits from CrewAI's standardized tool interface for consistency
- **Pydantic Integration**: Uses Pydantic models for robust input validation and type safety
- **Agent System**: Designed for seamless integration with CrewAI multi-agent configurations

### File System and Database Technologies  
- **PostgreSQL**: Backend database storage for persistent file tracking and session metadata
- **JSON File System**: High-performance file-based memory storage for rapid file tracking operations
- **EkoIntelligence DB Layer**: `eko.db.get_bo_conn()` for database connectivity and transaction management

### Supporting Infrastructure
- **Loguru**: Comprehensive logging framework for debugging, monitoring, and audit trails
- **Python Type System**: Full type safety with appropriate type hints for maintainable code
- **Exception Handling**: Robust error handling ensures tool reliability in production environments

## Usage Patterns and Examples

### Basic File Tracking
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem.track_file import TrackFileTool

# Initialize file tracking tool
memory_manager = CrewMemoryManager("Tesla Inc", "research_session_2024_07")
file_tool = TrackFileTool(
    memory_manager.company_name,
    memory_manager.session_id,
    memory_manager.memory_file
)

# Track a downloaded file
result = file_tool._run(
    file_path="reports/tesla_sustainability_2023.pdf",
    source_url="https://tesla.com/sustainability-report"
)
```

### JSON Input Format
```python
# Track file using JSON string input
json_input = {
    "file_path": "reports/annual_report_2023.pdf",
    "source_url": "https://example.com/annual_report_2023.pdf"
}
result = file_tool._run(json.dumps(json_input))
```

### Agent Integration
```python
from crewai import Agent
from eko.agent.crewai.crawl.tools_mem import get_memory_tools

# Create agent with file tracking capabilities
research_agent = Agent(
    role="Document Collection Specialist",
    goal="Download and track corporate ESG documents",
    tools=get_memory_tools(memory_manager),
    verbose=True
)
```

## Error Handling and Reliability

### Exception Safety
The tool implements comprehensive error handling:
- **File System Errors**: Gracefully handles missing or corrupted memory files
- **JSON Parsing Errors**: Recovers from malformed JSON input with detailed error messages
- **Parameter Validation**: Validates input parameters to ensure data integrity
- **Database Failures**: Continues operation with file-based memory when database is unavailable

### Fault Tolerance
- **Graceful Degradation**: Tool continues to operate even when some components fail
- **Data Integrity**: Ensures file tracking data remains consistent across all failure modes
- **Recovery Mechanisms**: Can recover from transient file system or database issues
- **Audit Logging**: All operations and errors are logged via Loguru for debugging and compliance

## Security and Access Control

### Session-Based Security
- **Company Isolation**: File tracking data is isolated by company name to prevent cross-contamination
- **Session Scoping**: Each research session maintains separate file tracking to ensure clean separation
- **File Path Validation**: Validates file paths to prevent directory traversal and unauthorized access
- **Source URL Verification**: Validates source URLs to ensure they meet security requirements

### Data Protection
- **Memory File Security**: File-based memory is stored in controlled application directories
- **Database Security**: Uses EkoIntelligence standard database authentication and access controls
- **Logging Security**: Sensitive information is properly sanitized in log outputs for security compliance

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/memory CrewAI Memory System Architecture  
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see https://loguru.readthedocs.io/en/stable/ Loguru Logging Documentation
@see backoffice/src/eko/agent/crewai/crawl/memory.py CrewMemoryManager Implementation
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/all_tools.py Memory Tools Factory
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/ Related Memory Tools
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for tracking downloaded files during automated web crawling and ESG research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import json
from datetime import datetime
from typing import Dict, Any

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.db import get_bo_conn


class TrackFileSchema(BaseModel):
    """Input schema for the track file tool."""

    file_path: str = Field(min_length=1, description="The path to the downloaded file")
    source_url: str = Field(min_length=1, description="The URL the file was downloaded from")


class TrackFileTool(BaseTool):
    """
    Tool for tracking downloaded files.
    
    This tool records files that have been downloaded during the research process
    to maintain a comprehensive record of the crawl and avoid duplicate downloads.
    """
    
    company_name: str  # Added as a field
    session_id: str    # Added as a field
    memory_file: str   # Added as a field
    
    def __init__(self, company_name: str, session_id: str, memory_file: str):
        """
        Initialize the tool with company and session information.
        
        Args:
            company_name: The name of the target company
            session_id: The session ID for persistence
            memory_file: Path to the memory file for persistence
        """
        # Define name and description directly here
        name = "track_downloaded_file"
        description = """
        Track a file that was downloaded during the research.
        This tool can handle either:
        1. A JSON string with file_path and source_url keys
        2. Individual file_path and source_url parameters
        
        Example 1: "{"file_path": "reports/annual_report_2023.pdf", "source_url": "https://example.com/annual_report_2023.pdf"}"
        Example 2: Simply provide the file_path and source_url as separate parameters
        """
        
        # Initialize the BaseTool with the name and description
        super().__init__(
            name=name, 
            description=description,
            args_schema=TrackFileSchema
        )
        
        # Store the company, session, and memory file information
        self.company_name = company_name
        self.session_id = session_id
        self.memory_file = memory_file
    
    def _load_memory(self) -> Dict[str, Any]:
        """
        Load the memory from the file system.
        
        Returns:
            Dict[str, Any]: The loaded memory
        """
        try:
            with open(self.memory_file, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading memory file: {e}")
            return {}
    
    def _save_memory(self, memory: Dict[str, Any]):
        """
        Save the memory to the file system.
        
        Args:
            memory: The memory to save
        """
        with open(self.memory_file, "w") as f:
            json.dump(memory, f, indent=2)
    
    def _sync_memory_to_db(self, memory: Dict[str, Any]):
        """
        Sync memory from file system to database.
        
        Args:
            memory: The memory to sync
        """
        try:
            memory["updated_at"] = datetime.now().isoformat()
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE agent_sessions 
                        SET memory_data = %s, updated_at = %s
                        WHERE session_id = %s
                        """,
                        (json.dumps(memory), datetime.now(), self.session_id)
                    )
                    conn.commit()
                    logger.debug(f"Synced memory to database for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error syncing memory to database: {e}")
    
    def _track_downloaded_file(self, file_path: str, source_url: str) -> bool:
        """
        Track a downloaded file in memory and database.
        
        Args:
            file_path: The path to the downloaded file
            source_url: The URL the file was downloaded from
            
        Returns:
            bool: True if the file was newly tracked, False if it was already tracked
        """
        memory = self._load_memory()
        
        # Prepare file info
        file_info = {
            "path": file_path, 
            "source_url": source_url, 
            "downloaded_at": datetime.now().isoformat()
        }
        
        # Check if this file is already tracked (by path)
        existing_paths = [item.get("path") for item in memory.get("downloaded_files", [])]
        if file_path in existing_paths:
            return False
            
        # Add to memory
        if "downloaded_files" not in memory:
            memory["downloaded_files"] = []
            
        memory["downloaded_files"].append(file_info)
        
        # Save updates
        self._save_memory(memory)
        self._sync_memory_to_db(memory)
        
        logger.debug(f"Tracked downloaded file: {file_path} from {source_url}")
        return True
    
    def _run(self, file_path: str, source_url: str) -> str:
        """
        Run the tool to track a downloaded file.
        
        Args:
            file_path: The path to the downloaded file
            source_url: The URL the file was downloaded from
            
        Returns:
            str: Confirmation message
        """
        try:
            
            # If only one parameter is provided, it might be a JSON string
            if file_path and not source_url:
                try:
                    # Try to parse it as JSON
                    if isinstance(file_path, str) and (file_path.startswith("{") or file_path.startswith("[")):
                        parsed_input = json.loads(file_path)
                        
                        # Handle array of objects
                        if isinstance(parsed_input, list) and len(parsed_input) > 0:
                            results = []
                            for item in parsed_input:
                                if isinstance(item, dict):
                                    item_path = item.get("file_path")
                                    item_url = item.get("source_url")
                                    if item_path and item_url:
                                        success = self._track_downloaded_file(item_path, item_url)
                                        results.append(f"{'Successfully tracked' if success else 'Already tracked'} file: {item_path} from {item_url}")
                            if results:
                                return "\n".join(results)
                        
                        # Handle single object
                        if isinstance(parsed_input, dict):
                            file_path = parsed_input.get("file_path")
                            source_url = parsed_input.get("source_url")
                            
                            # If we found valid parameters, use them
                            if file_path and source_url:
                                success = self._track_downloaded_file(file_path, source_url)
                                return f"{'Successfully tracked' if success else 'Already tracked'} file: {file_path} from {source_url}"
                except Exception as e:
                    # Not valid JSON, continue with normal processing
                    logger.warning(f"Error parsing JSON input: {e}")
            
            # Ensure we have both required parameters
            if not file_path or not source_url:
                return "Error: Both file_path and source_url are required. Please provide both."
            
            # Track the file
            success = self._track_downloaded_file(file_path, source_url)
            return f"{'Successfully tracked' if success else 'Already tracked'} file: {file_path} from {source_url}"
        except Exception as e:
            logger.exception(f"Error tracking file: {e}")
            return f"Error tracking file: {str(e)}"
    
    async def _arun(self, file_path: str, source_url: str) -> str:
        """Async implementation of _run."""
        return self._run(file_path, source_url)
