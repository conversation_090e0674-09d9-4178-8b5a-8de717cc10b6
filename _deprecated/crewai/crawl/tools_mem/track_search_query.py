"""
CrewAI Web Crawling Search Query Tracking Tool

This module provides the `TrackSearchQueryTool` class, a specialized CrewAI tool for tracking and managing
search queries used during automated web research sessions within the EkoIntelligence ESG (Environmental,
Social, Governance) analysis platform. The tool serves as a comprehensive search audit system for AI agents,
enabling them to maintain a complete record of search activities, prevent duplicate queries, and optimize
research coverage across complex multi-session crawling workflows.

## Core Functionality
- **Search Query Tracking**: Records and persists search queries used during automated research sessions
- **Duplicate Prevention**: Enables agents to avoid redundant searches by checking previous query history
- **Research Coverage Analysis**: Provides visibility into search methodologies and coverage patterns
- **Session Continuity**: Maintains search query history across agent restarts and session resumptions
- **Research Optimization**: Enables agents to refine search strategies based on historical query performance

## Search Query Management Capabilities

### Query History Maintenance
The tool provides comprehensive tracking of search activities:
- **Complete Query Log**: Maintains ordered list of all search queries used during research sessions
- **Temporal Tracking**: Records query execution order with indexed positions for session continuity
- **Deduplication Logic**: Prevents storage of identical queries while maintaining search attempt records
- **Session Persistence**: Ensures search history survives agent restarts and workflow interruptions

### Research Strategy Support
Beyond simple tracking, the tool supports strategic research planning:
- **Coverage Analysis**: Enables agents to understand search breadth and identify potential coverage gaps
- **Query Evolution**: Tracks how search strategies evolve and adapt during research progression
- **Research Audit Trail**: Provides complete transparency into search methodologies for quality assurance
- **Strategy Optimization**: Historical data enables improvement of search query generation strategies

## CrewAI Framework Integration

### Tool Architecture
The `TrackSearchQueryTool` inherits from CrewAI's `BaseTool` class, providing:
- **Standardized Interface**: Follows CrewAI tool conventions for consistent agent interaction
- **Pydantic Schema Validation**: Uses `SearchQuerySchema` for input validation and type safety
- **Dual Execution Modes**: Supports both synchronous (`_run`) and asynchronous (`_arun`) operation
- **Error Handling**: Comprehensive exception handling with Loguru logging integration

### Multi-Agent Coordination
In collaborative research environments, the search tracking tool enables:
- **Shared Query History**: All agents access the same search query log for coordinated research
- **Work Distribution**: Agents can identify previously searched terms to avoid redundant efforts
- **Research Completeness**: Comprehensive search logs help ensure thorough coverage of research topics
- **Quality Assurance**: Search audit trails enable evaluation of research thoroughness and methodology

## Memory System Integration

### Dual Persistence Architecture
The tool employs a sophisticated dual-storage approach for reliability and performance:

#### File System Persistence
- **JSON Memory Files**: Stored in `var/crawl_memory/{session_id}.json` for rapid access
- **Development Debugging**: Human-readable format facilitates debugging and development
- **Immediate Access**: File-based queries provide instant search history without database latency
- **Session Recovery**: Local files enable quick recovery from database connectivity issues

#### PostgreSQL Database Integration
The tool synchronizes with the EkoIntelligence analytics database:
- **agent_sessions Table**: Primary storage with `session_id`, `company_name`, and `memory_data` JSONB field
- **Structured Storage**: Database normalization enables efficient queries and analytics
- **Transaction Safety**: Updates use proper transaction management for data consistency
- **Scalable Architecture**: Database storage supports enterprise-scale research operations

### Memory Data Structure
The tool manages specific memory structures within the JSON memory data:
- **search_queries Array**: Ordered list of search query strings used during the session
- **search_query_index**: Current position index in the search queries array for session continuity
- **last_position Object**: Container for positional metadata and session state information
- **Automatic Initialization**: Creates memory structures on first use with appropriate defaults

## ESG Research Workflow Integration

### Corporate Sustainability Research
The search query tracking supports ESG research workflows by:
- **Company-Specific Tracking**: Search histories are isolated by company name for focused analysis
- **ESG Domain Coverage**: Tracks queries across environmental, social, and governance research areas
- **Regulatory Research**: Maintains search records for compliance documentation and regulatory filings
- **Multi-Source Integration**: Coordinates searches across corporate websites, reports, and third-party sources

### Research Quality and Compliance
The tool contributes to research quality through:
- **Methodology Transparency**: Complete search logs provide transparency into research approaches
- **Coverage Verification**: Search history enables verification of comprehensive topic coverage
- **Research Reproducibility**: Detailed search logs enable reproduction and validation of research findings
- **Audit Trail Compliance**: Maintains complete audit trails for regulatory and client reporting requirements

## Database Architecture Context

### Analytics Database Integration
The tool integrates with the EkoIntelligence database schema:
- **Primary Storage**: `agent_sessions` table with `session_id` primary key and company name indexing
- **JSONB Efficiency**: Memory data stored as JSONB for flexible schema evolution and efficient querying
- **Connection Management**: Uses `eko.db.get_bo_conn()` for connection pooling and transaction management
- **Index Optimization**: Database includes appropriate indexes on company name for efficient session queries

### Data Synchronization Strategy
- **Memory-First Approach**: Tools prioritize file-based memory for performance during active sessions
- **Database Persistence**: Regular synchronization ensures data durability and cross-session availability
- **Conflict Resolution**: Synchronization handles concurrent updates from multiple agent instances
- **Backup and Recovery**: Database storage provides backup and disaster recovery capabilities

## System Architecture Context

### EkoIntelligence Platform Integration
This tool is a critical component of the broader ESG analysis system:
- **Analytics Backend**: Python-based CrewAI orchestration drives automated corporate research workflows
- **Data Collection Pipeline**: Search queries guide web crawling and document acquisition strategies
- **Intelligence Processing**: Search patterns inform NLP analysis and insight extraction algorithms
- **Customer Reporting**: Search methodologies contribute to research transparency in client reports

### Performance and Scalability Architecture
The tool is designed for enterprise-scale operation:
- **High-Performance Access**: File-based memory provides sub-millisecond query response times
- **Concurrent Session Support**: Supports multiple simultaneous research sessions with proper isolation
- **Memory Efficiency**: Processes search data without loading entire session state into memory
- **Horizontal Scaling**: Database architecture supports distributed research across multiple agents

## Input/Output Specifications

### Input Schema
The tool accepts search queries through the `SearchQuerySchema`:
- **query (str)**: The search query string used during research (required field)
- **Validation**: Pydantic schema ensures input type safety and validation
- **Flexible Input**: Handles both string inputs and dictionary inputs with 'query' keys
- **Array Processing**: Supports batch processing of multiple queries in a single call

### Output Format
The tool returns standardized confirmation messages:
- **Success Tracking**: "Successfully tracked search query: {query}" for new queries
- **Duplicate Detection**: "Already tracked search query: {query}" for previously recorded queries
- **Batch Results**: Multi-line responses for batch query processing operations
- **Error Reporting**: Detailed error messages with exception information for debugging

## Usage Patterns and Integration Examples

### Basic Search Query Tracking
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem.track_search_query import TrackSearchQueryTool

# Initialize search tracking for company research session
memory_manager = CrewMemoryManager("Tesla Inc", "research_session_2024_07")
search_tool = TrackSearchQueryTool(
    memory_manager.company_name,
    memory_manager.session_id,
    memory_manager.memory_file
)

# Track individual search queries
result = search_tool._run("Tesla sustainability report 2023")
print(result)  # "Successfully tracked search query: Tesla sustainability report 2023"
```

### CrewAI Agent Integration
```python
from crewai import Agent
from eko.agent.crewai.crawl.tools_mem import get_memory_tools

# Create agent with comprehensive memory tools including search tracking
research_agent = Agent(
    role="ESG Research Analyst",
    goal="Conduct comprehensive sustainability research with complete audit trails",
    tools=get_memory_tools(memory_manager),
    verbose=True,
    memory=True
)
```

### Batch Query Processing
```python
# Track multiple search queries simultaneously
batch_queries = [
    "Tesla environmental impact 2023",
    "Tesla social responsibility initiatives",
    "Tesla governance policies board composition"
]
result = search_tool._run(batch_queries)
print(result)  # Multi-line response with tracking status for each query
```

## Key Dependencies and Technologies

### CrewAI Framework Components
- **BaseTool Architecture**: Inherits from CrewAI's standardized tool interface for consistent behavior
- **Pydantic Integration**: Uses `BaseModel` and `Field` for robust input validation and type safety
- **Agent System**: Designed for seamless integration with CrewAI agent configurations and workflows
- **Memory Compatibility**: Integrates with CrewAI's memory system for persistent agent state

### Database and Persistence Technologies
- **PostgreSQL**: Enterprise-grade database storage with JSONB support for flexible memory structures
- **psycopg**: High-performance PostgreSQL adapter for Python with connection pooling support
- **JSON File System**: High-speed local file storage for development and rapid memory access
- **EkoIntelligence DB Layer**: Custom database abstraction with `eko.db.get_bo_conn()` connection management

### Supporting Infrastructure
- **Loguru**: Advanced logging framework with structured logging, exception tracking, and debugging support
- **Python Type System**: Full type safety with comprehensive type hints for maintainable code
- **Exception Handling**: Robust error handling ensures reliable operation in production environments
- **Session Management**: Proper session isolation and state management for concurrent operations

## Error Handling and Reliability

### Exception Safety Architecture
The tool implements comprehensive error handling strategies:
- **File System Resilience**: Graceful handling of missing, corrupted, or inaccessible memory files
- **JSON Parsing Safety**: Robust error handling for malformed JSON data with fallback to empty state
- **Database Error Recovery**: Continues operation during database connectivity issues using file-based fallback
- **Input Validation**: Comprehensive input validation prevents errors from malformed or unexpected data

### Fault Tolerance and Recovery
- **Graceful Degradation**: Tool continues operating even when persistence layers experience issues
- **Automatic Recovery**: Can recover from transient file system, database, or network issues
- **Data Integrity**: Ensures data consistency through proper transaction management and atomic updates
- **Comprehensive Logging**: All errors, warnings, and operations are logged via Loguru for debugging

### Production Reliability Features
- **Memory Structure Validation**: Validates and initializes memory structures to prevent corruption
- **Concurrent Access Safety**: Handles concurrent access from multiple agent instances safely
- **Resource Management**: Proper cleanup and resource management prevents memory leaks
- **Performance Monitoring**: Built-in performance metrics enable monitoring and optimization

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/memory CrewAI Memory System Architecture
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see https://pydantic-docs.helpmanual.io/ Pydantic Data Validation Documentation
@see https://loguru.readthedocs.io/en/stable/ Loguru Logging Framework Documentation
@see backoffice/src/eko/agent/crewai/crawl/memory.py CrewMemoryManager Implementation
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/all_tools.py Memory Tools Factory
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/ Related Memory Management Tools
@see backoffice/src/eko/db/ Database Connection and Management Layer
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for tracking search queries in automated ESG research and web crawling workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import json
from datetime import datetime
from typing import Dict, Any

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.db import get_bo_conn


class SearchQuerySchema(BaseModel):
    """Input schema for the track search query tool."""
    query: str = Field(description="The search query that was used")


class TrackSearchQueryTool(BaseTool):
    """
    Tool for tracking search queries.
    
    This tool records search queries used during the research process
    to avoid duplicate searches and to build a comprehensive record of the crawl.
    """
    
    company_name: str = None  # Added as a field
    session_id: str = None    # Added as a field
    memory_file: str = None   # Added as a field
    
    def __init__(self, company_name: str, session_id: str, memory_file: str):
        """
        Initialize the tool with company and session information.
        
        Args:
            company_name: The name of the target company
            session_id: The session ID for persistence
            memory_file: Path to the memory file for persistence
        """
        # Define name and description directly here
        name = "track_search_query"
        description = """
        Track a search query that was used during research.
        Call this tool whenever you perform a search to help track research progress.
        """
        
        # Initialize the BaseTool with the name and description
        super().__init__(
            name=name, 
            description=description,
            args_schema=SearchQuerySchema
        )
        
        # Store the company, session, and memory file information
        self.company_name = company_name
        self.session_id = session_id
        self.memory_file = memory_file
    
    def _load_memory(self) -> Dict[str, Any]:
        """
        Load the memory from the file system.
        
        Returns:
            Dict[str, Any]: The loaded memory
        """
        try:
            with open(self.memory_file, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading memory file: {e}")
            return {}
    
    def _save_memory(self, memory: Dict[str, Any]):
        """
        Save the memory to the file system.
        
        Args:
            memory: The memory to save
        """
        with open(self.memory_file, "w") as f:
            json.dump(memory, f, indent=2)
    
    def _sync_memory_to_db(self, memory: Dict[str, Any]):
        """
        Sync memory from file system to database.
        
        Args:
            memory: The memory to sync
        """
        try:
            memory["updated_at"] = datetime.now().isoformat()
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE agent_sessions 
                        SET memory_data = %s, updated_at = %s
                        WHERE session_id = %s
                        """,
                        (json.dumps(memory), datetime.now(), self.session_id)
                    )
                    conn.commit()
                    logger.debug(f"Synced memory to database for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error syncing memory to database: {e}")
    
    def _track_search_query(self, query: str) -> bool:
        """
        Track a search query in memory and database.
        
        Args:
            query: The search query to track
            
        Returns:
            bool: True if the query was newly tracked, False if it was already tracked
        """
        memory = self._load_memory()
        
        # Initialize last_position if not present
        if "last_position" not in memory:
            memory["last_position"] = {}
            
        # Initialize search_queries if not present
        if "search_queries" not in memory["last_position"]:
            memory["last_position"]["search_queries"] = []
            
        # Check if query is already tracked
        if query in memory["last_position"]["search_queries"]:
            return False
            
        # Add query to search_queries
        memory["last_position"]["search_queries"].append(query)
        memory["last_position"]["search_query_index"] = len(memory["last_position"]["search_queries"]) - 1
        
        # Save updates
        self._save_memory(memory)
        self._sync_memory_to_db(memory)
        
        logger.debug(f"Tracked search query: {query}")
        return True
    
    def _run(self, query: str) -> str:
        """
        Run the tool to track a search query.
        
        Args:
            query: The search query that was used
            
        Returns:
            str: Confirmation message
        """
        try:
                
            # Handle possible dict input
            if isinstance(query, dict) and "query" in query:
                query = query["query"]
                
            # Handle array input
            if isinstance(query, list):
                results = []
                for single_query in query:
                    if isinstance(single_query, dict) and "query" in single_query:
                        single_query = single_query["query"]
                    success = self._track_search_query(str(single_query).strip())
                    results.append(f"{'Successfully tracked' if success else 'Already tracked'} search query: {single_query}")
                return "\n".join(results)
                
            # Ensure the query is a string
            query = str(query).strip()
            
            # Track the search query
            success = self._track_search_query(query)
            return f"{'Successfully tracked' if success else 'Already tracked'} search query: {query}"
        except Exception as e:
            logger.exception(f"Error tracking search query: {e}")
            return f"Error tracking search query: {str(e)}"
    
    async def _arun(self, query: str) -> str:
        """Async implementation of _run."""
        return self._run(query)
