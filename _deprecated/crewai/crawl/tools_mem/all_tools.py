"""
CrewAI Web Crawler Memory Tools Factory

This module provides a factory function for creating memory-related tools used by CrewAI web crawling agents
within the EkoIntelligence ESG (Environmental, Social, Governance) analysis platform. The factory pattern
enables consistent initialization of memory tools that provide persistent state management, progress tracking,
and insight accumulation across multi-agent crawling sessions focused on corporate sustainability research.

## Core Functionality  
- **Tool Factory Pattern**: Centralizes creation of memory-related tools with shared configuration
- **Session-Based Memory**: All tools share company name, session ID, and memory file for coordinated state
- **Dual Persistence**: Tools utilize both file system (JSON) and PostgreSQL database for reliable memory storage
- **Agent Coordination**: Enables multiple CrewAI agents to collaborate using shared memory state
- **Research Progress Tracking**: Tools maintain visibility into crawling progress, visited URLs, and discovered insights

## Tool Ecosystem
The factory creates five specialized memory tools for comprehensive crawling state management:

### URL Management
- **TrackURLTool**: Records visited URLs to prevent duplicate crawling and track research coverage
- **Prevents Redundancy**: Agents can check previous URL visits before initiating new crawl requests
- **Progress Mapping**: Maintains ordered list of URLs for understanding crawl coverage and depth

### File and Content Management  
- **TrackFileTool**: Manages downloaded documents with source URL mapping for content provenance
- **Document Library**: Builds comprehensive library of crawled documents with metadata
- **Source Attribution**: Links downloaded files back to their originating web pages for context

### Intelligence Gathering
- **TrackInsightTool**: Stores discovered ESG insights with categorization, confidence scoring, and metadata
- **Knowledge Accumulation**: Builds structured knowledge base from distributed agent research
- **Insight Classification**: Categorizes findings by ESG domains, behavior types, and confidence levels

### Search and Discovery
- **TrackSearchQueryTool**: Maintains search query history for research optimization and coverage analysis
- **Query Optimization**: Prevents duplicate searches and enables search strategy refinement
- **Research Audit Trail**: Provides transparency into search methodologies and coverage gaps

### Progress Monitoring
- **GetProgressTool**: Generates comprehensive progress summaries for agent coordination and reporting
- **Real-time Status**: Provides current crawling status, metrics, and completion estimates
- **Multi-Agent Sync**: Enables agents to understand overall research progress and avoid conflicts

## CrewAI Framework Integration
This factory integrates seamlessly with the CrewAI multi-agent system architecture:

### Agent Configuration
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem.all_tools import get_memory_tools

# Initialize memory manager for company research session
memory_manager = CrewMemoryManager("Tesla Inc", "research_session_2024_07")
tools = get_memory_tools(memory_manager)

# Configure CrewAI agent with memory tools
research_agent = Agent(
    role="ESG Research Analyst", 
    goal="Analyze corporate sustainability practices",
    tools=tools,
    memory=True,
    verbose=True
)
```

### Multi-Agent Coordination
- **Shared Memory State**: All agents access the same memory manager for coordinated research
- **Tool Consistency**: Factory ensures all tools use identical configuration (company, session, memory file)
- **Progress Synchronization**: Agents can query progress and adjust strategies based on collective findings
- **Result Aggregation**: Final research results combine insights from all participating agents

## Data Persistence Architecture
The factory-created tools utilize a robust dual-persistence strategy:

### File System Persistence
- **JSON Memory Files**: Located in `var/crawl_memory/{session_id}.json` for rapid access
- **Development Debugging**: Human-readable JSON format facilitates debugging and development
- **Performance Optimization**: Local file access provides immediate memory queries without database latency

### PostgreSQL Database Integration
The tools interact with several database tables in the EkoIntelligence analytics database:
- **agent_sessions**: Session metadata and JSON memory snapshots with timestamp tracking
- **agent_insights**: Structured ESG insights with categorization, confidence scores, and source attribution
- **agent_reports**: Final research reports with summary statistics and aggregated findings
- **agent_tool_usage**: Tool usage analytics for performance monitoring and workflow optimization

### Synchronization Strategy
- **Bidirectional Sync**: Changes flow between file system and database for redundancy
- **Atomic Updates**: Memory updates are atomic to prevent corruption during concurrent access
- **Error Recovery**: Database failures gracefully fall back to file-based memory persistence

## System Architecture Context
This module serves as a critical component in the broader EkoIntelligence ESG analysis pipeline:

### Research Pipeline Integration
- **Analytics Backend**: Python-based CrewAI orchestration drives automated corporate research
- **Data Collection**: Agents crawl corporate websites, sustainability reports, and regulatory filings
- **Memory Layer**: This factory provides persistent state for multi-session research workflows
- **Intelligence Processing**: Discovered insights feed into NLP analysis and greenwashing detection systems
- **Customer Dashboard**: Research results sync to customer database via `xfer_` tables for web dashboard access

### ESG Analysis Workflow
1. **Target Identification**: Research sessions focus on specific companies for ESG analysis
2. **Agent Deployment**: Multiple CrewAI agents use memory tools for coordinated web crawling
3. **Content Discovery**: Agents track URLs, download documents, and extract ESG-related insights
4. **Knowledge Synthesis**: Memory tools accumulate findings across agents for comprehensive analysis
5. **Reporting Generation**: Final reports combine multi-agent findings with progress metrics
6. **Database Integration**: Results feed into broader EkoIntelligence analysis and scoring systems

## Key Dependencies and Technologies

### CrewAI Framework Components
- **CrewAI BaseTool**: All created tools inherit from CrewAI's tool architecture for consistent behavior
- **Agent Integration**: Tools are designed for seamless integration with CrewAI agent configurations
- **Multi-Agent Orchestration**: Factory supports tools for coordinated multi-agent research workflows

### Database and Persistence
- **PostgreSQL**: Analytics database provides structured storage for session data and insights
- **EkoIntelligence DB Layer**: `eko.db.get_bo_conn()` provides connection pooling and transaction management
- **JSON File System**: Local file storage enables rapid memory access and development debugging

### Supporting Infrastructure  
- **Loguru**: Comprehensive logging framework for debugging, monitoring, and audit trails
- **Pydantic**: Type-safe data models ensure consistent memory data structures
- **Python Type Hints**: Full type safety with `typing.List` and `typing.Any` for tool collections

## Performance and Scalability Considerations

### Memory Management
- **Lazy Initialization**: Tools are created on-demand to minimize memory footprint
- **Shared Configuration**: Factory pattern prevents duplicate configuration data across tools
- **Session Isolation**: Memory data is properly isolated by company name and session ID combinations

### Database Optimization  
- **Connection Pooling**: Database connections are managed through EkoIntelligence connection pool
- **Batch Operations**: Memory synchronization batches updates for improved database performance
- **Index Optimization**: Database tables include proper indexes for session and company-based queries

### Error Handling and Reliability
- **Graceful Degradation**: Database failures don't prevent tool operation via file-based fallback
- **Exception Safety**: All tools handle errors gracefully with comprehensive logging
- **Transaction Safety**: Database updates use proper transaction management for data consistency

## Security and Monitoring

### Access Control
- **Session-Based Security**: Tools are scoped to specific company and session combinations
- **Database Security**: Uses EkoIntelligence standard database connection and authentication
- **File System Security**: Memory files are stored in controlled application directories

### Monitoring and Observability
- **Comprehensive Logging**: All tool operations are logged via Loguru for debugging and audit
- **Usage Analytics**: Tool usage statistics are stored for performance monitoring and optimization
- **Progress Tracking**: Real-time progress monitoring enables research workflow management

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/memory CrewAI Memory System Architecture  
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see backoffice/src/eko/agent/crewai/crawl/memory.py CrewMemoryManager Implementation
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/ Individual Memory Tool Implementations
<AUTHOR>
@updated 2025-07-22
@description Factory function for creating CrewAI memory tools used in automated ESG research and web crawling workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from typing import List, Any

from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem.get_progress import GetProgressTool
from eko.agent.crewai.crawl.tools_mem.track_file import TrackFileTool
from eko.agent.crewai.crawl.tools_mem.track_insight import TrackInsightTool
from eko.agent.crewai.crawl.tools_mem.track_search_query import TrackSearchQueryTool
from eko.agent.crewai.crawl.tools_mem.track_url import TrackURLTool


def get_memory_tools(memory_manager: CrewMemoryManager) -> List[Any]:
    """
    Create a list of memory-related tools.
    
    This function creates tools for tracking visited URLs, downloaded files,
    insights, search queries, and getting crawl progress.
    
    Args:
        memory_manager: The memory manager to use for tracking
        
    Returns:
        List[Any]: A list of memory-related tools
    """
    # Create a shared memory file for all tools
    company_name = memory_manager.company_name
    session_id = memory_manager.session_id
    memory_file = memory_manager.memory_file
    
    # Create tools with direct access to company, session and memory file
    tools = [
        TrackURLTool(company_name, session_id, memory_file),
        TrackFileTool(company_name, session_id, memory_file),
        TrackInsightTool(company_name, session_id, memory_file),
        TrackSearchQueryTool(company_name, session_id, memory_file),
        GetProgressTool(company_name, session_id, memory_file)
    ]
    
    return tools