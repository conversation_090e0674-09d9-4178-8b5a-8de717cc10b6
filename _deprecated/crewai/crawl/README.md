# CrewAI Multi-Agent ESG Web Crawler System

## Overview

The `crawl` module is EkoIntelligence's sophisticated AI-powered web crawling and research system built on the CrewAI framework. This module orchestrates multi-agent teams of specialized AI researchers to conduct comprehensive ESG (Environmental, Social, Governance) investigations of corporate entities, systematically discovering, analyzing, and documenting sustainability practices, regulatory compliance, and potential greenwashing activities through exhaustive web research and intelligent document analysis.

The system combines automated web crawling, regulatory database integration, investigative journalism techniques, and AI-powered content analysis to produce thorough ESG assessments that serve as the foundation for EkoIntelligence's corporate sustainability analysis platform.

**Key Capabilities:**

- **Multi-Agent Orchestration**: Specialized AI agents (Research, Journalism, Analysis, Summary, Report) collaborate on complex ESG investigations
- **Persistent Memory Management**: Dual-storage architecture (JSON + PostgreSQL) enables session continuity across interruptions
- **Comprehensive Tool Ecosystem**: 17+ specialized tools for web crawling, document discovery, and content analysis
- **Intelligent Research Workflows**: 11-task research pipeline with dynamic query generation and gap analysis
- **Regulatory Integration**: Direct access to SEC, Companies House, GLEIF, and other authoritative data sources
- **Greenwashing Detection**: AI-powered analysis identifies misleading sustainability claims and corporate misconduct

## Specification

### Architecture Requirements

The crawl system is designed as a sophisticated multi-agent research platform with the following core specifications:

#### Multi-Agent Framework Integration
- **CrewAI Foundation**: Built on CrewAI's multi-agent orchestration framework for collaborative AI workflows
- **Agent Specialization**: Five distinct agent types with specialized roles and tool collections
- **Task Coordination**: Sequential and parallel task execution with dependency management
- **Memory Sharing**: Persistent session state shared across all agents for coordinated research

#### Persistent Memory System
- **Dual Storage**: File-based JSON storage for fast access + PostgreSQL for structured querying
- **Session Isolation**: Complete separation of research data by company and session ID
- **Progress Tracking**: Multi-dimensional progress indicators for resumable workflows
- **Insight Management**: Categorized storage of AI-generated insights with confidence scoring

#### Research Methodology
- **Systematic Discovery**: 11-phase research workflow covering corporate, regulatory, and third-party sources
- **Dynamic Intelligence**: AI-driven search query evolution based on discovered content
- **Quality Assurance**: Comprehensive coverage verification and gap analysis
- **Professional Reporting**: Stakeholder-ready ESG reports with complete audit trails

### Technology Stack

```mermaid
graph TB
    subgraph "CrewAI Framework"
        A[Multi-Agent Orchestration]
        B[Task Management]
        C[Tool Integration]
        D[Memory System]
    end

    subgraph "EkoIntelligence Platform"
        E[Analytics Database]
        F[Memory Manager]
        G[ESG Analysis Pipeline]
        H[Customer Dashboard]
    end

    subgraph "External APIs"
        I[Google Custom Search]
        J[Companies House API]
        K[GLEIF API]
        L[SEC EDGAR API]
        M[NewsAPI]
        N[Wikipedia MediaWiki]
    end

    subgraph "AI/LLM Services"
        O[Google Gemini Models]
        P[Content Analysis]
        Q[Query Generation]
    end

    A --> E
    B --> F
    C --> I
    C --> J
    C --> K
    C --> L
    C --> M
    C --> N
    D --> G
    G --> H
    A --> O
    P --> O
    Q --> O
```

## Key Components

### Core System Files

| File | Purpose | Key Functionality |
|------|---------|-------------------|
| **[`crawler.py`](./crawler.py)** | Main orchestration system | `PersistentWebCrawlCrew` class for managing multi-agent research sessions |
| **[`agents.py`](./agents.py)** | Agent definitions | 5 specialized CrewAI agents with distinct roles and capabilities |
| **[`tasks.py`](./tasks.py)** | Task factory system | 11-task research pipeline with intelligent workflow orchestration |
| **[`memory.py`](./memory.py)** | Memory management | `CrewMemoryManager` with dual-storage persistence and session coordination |
| **[`tools.py`](./tools.py)** | Tool import gateway | Central access point to comprehensive tool collections |

### Specialized Agent System

The system employs five specialized AI agents, each optimized for specific research activities:

#### ResearchAgent
- **Role**: Primary web crawling and document discovery
- **Specialization**: Corporate website exploration, regulatory filing discovery, systematic document collection
- **Tools**: Full web tools collection (13 tools + memory integration)
- **Tasks**: Initial discovery, corporate analysis, final documentation hunts

#### JournalistAgent  
- **Role**: Investigative journalism and media research
- **Specialization**: News coverage analysis, third-party investigations, critical source discovery
- **Tools**: Web tools with journalist-focused configurations
- **Tasks**: Media analysis, investigative research, search query evolution

#### AnalysisAgent
- **Role**: Strategic content analysis and pattern recognition
- **Specialization**: Document analysis, research gap identification, insight extraction
- **Tools**: Analyst tools collection (12 tools focused on content processing)
- **Tasks**: Multi-database searches, pattern analysis, document inventory

#### SummaryAgent
- **Role**: Content organization and preliminary reporting
- **Specialization**: Document categorization, inventory management, gap analysis
- **Tools**: Analyst tools for content processing and organization
- **Tasks**: Comprehensive document inventory creation

#### ReportAgent
- **Role**: Professional report generation
- **Specialization**: Stakeholder-ready ESG reports with complete methodology documentation
- **Tools**: Analyst tools for final report compilation
- **Tasks**: Final comprehensive reporting with executive summaries

### Tool Ecosystem Architecture

```mermaid
graph TB
    subgraph "Tool Collections"
        A[get_web_tools<br/>13 tools + memory]
        B[get_analyst_tools<br/>12 tools + memory]
        C[get_memory_tools<br/>5 memory tools]
    end

    subgraph "Tool Categories"
        D[Search & Discovery<br/>6 tools]
        E[Content Processing<br/>4 tools]
        F[AI Analysis<br/>4 tools]
        G[Memory Management<br/>5 tools]
    end

    subgraph "External Integrations"
        H[Google Custom Search]
        I[Companies House API]
        J[GLEIF Database]
        K[SEC EDGAR]
        L[NewsAPI]
        M[Wikipedia]
    end

    A --> D
    A --> E
    A --> F
    B --> E
    B --> F
    C --> G
    D --> H
    D --> I
    D --> J
    D --> K
    D --> L
    D --> M
```

### Research Workflow Pipeline

The system implements a sophisticated 11-task research workflow designed for exhaustive ESG investigation:

#### Phase 1: Systematic Discovery (Tasks 1-5)
1. **Third-Party ESG Research**: Activist groups, NGOs, independent analysis
2. **Multi-Database Systematic Search**: Companies House, SEC, GLEIF, Wikipedia integration
3. **Corporate Website Deep Exploration**: Comprehensive website document discovery
4. **Regulatory & Financial Documentation**: Annual reports, investor presentations, SEC filings
5. **Sustainability Report Collection**: ESG reports, CSR publications, environmental data

#### Phase 2: Media & Investigation (Tasks 6-7)
6. **Media Coverage Analysis**: News articles, press releases, journalist investigations
7. **Investigative Third-Party Research**: Critical analysis and corporate misconduct research

#### Phase 3: Intelligent Evolution (Tasks 8-10)
8. **Dynamic Search Evolution**: AI-generated search queries based on discoveries
9. **Gap Analysis & Completion**: Systematic research gap identification and filling
10. **Final Exhaustive Documentation Hunt**: Comprehensive final search phase

#### Phase 4: Analysis & Reporting (Task 11)
11. **Content Analysis & Insight Extraction**: Document analysis for ESG patterns and insights

## Dependencies

### CrewAI Framework
- **Core Framework**: `crewai` - Multi-agent orchestration and workflow management
- **Agent System**: CrewAI Agent class for specialized AI researcher roles
- **Task Management**: CrewAI Task system for complex workflow orchestration
- **Tool Integration**: CrewAI BaseTool framework for custom tool development
- **Memory System**: CrewAI memory capabilities enhanced with custom persistence

### Database Technologies
- **PostgreSQL**: Primary persistence layer for analytics database
  - `agent_sessions`: Session metadata with JSONB memory data storage
  - `agent_insights`: Structured ESG insights with categorization and confidence scoring
  - `agent_reports`: Aggregated research findings and comprehensive reporting
  - `agent_tool_usage`: Tool execution analytics for performance monitoring
  - Connection management via `eko.db.get_bo_conn()`

### External APIs and Services
- **Google Custom Search API**: Primary web search functionality with ESG-focused queries
- **Companies House API**: UK corporate registry data and regulatory filing access
- **GLEIF API**: Global Legal Entity Identifier database for corporate verification
- **SEC EDGAR API**: US securities filings and enforcement action data
- **NewsAPI**: Real-time news monitoring for ESG-related developments and controversies
- **Wikipedia MediaWiki API**: Corporate background research and controversy identification

### AI/LLM Integration
- **Google Gemini Models**: Primary LLM provider for content analysis and intelligent decision making
- **Content Analysis**: AI-powered relevance scoring, insight extraction, and pattern recognition
- **Query Generation**: Dynamic search strategy development based on discovered content
- **Greenwashing Detection**: Specialized AI analysis for identifying misleading sustainability claims

### Supporting Libraries
- **Pydantic**: Type-safe data validation and serialization for all tool inputs/outputs
- **Loguru**: Comprehensive logging with structured output for debugging and monitoring
- **Requests**: HTTP client library for API integrations and webpage fetching
- **BeautifulSoup**: HTML parsing and content extraction from web pages
- **JSON**: File-based memory storage for rapid access and development debugging

### EkoIntelligence Platform Integration
- **Database Layer**: `eko.db.get_bo_conn()` for connection pooling and transaction management
- **ESG Analysis Pipeline**: Integration with `eko.analysis_v2` for downstream corporate analysis
- **Data Synchronization**: Results flow to customer database via `xfer_` table synchronization
- **Customer Interface**: Final reports accessible through customer dashboard web application

## Usage Examples

### Basic ESG Research Session

```python
from eko.agent.crewai.crawl.crawler import PersistentWebCrawlCrew

# Initialize comprehensive ESG research session
crawler = PersistentWebCrawlCrew("Tesla Inc", "esg_investigation_2024_q3")

# Execute full research workflow with all agents
results = crawler.run_comprehensive_research()

# Access final ESG report and document inventory
print(f"Research Summary: {results.summary}")
print(f"Documents Discovered: {len(results.document_inventory)}")
print(f"ESG Insights: {len(results.insights)}")
```

### Custom Multi-Agent Workflow

```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.agents import ResearchAgent, AnalysisAgent
from eko.agent.crewai.crawl.tasks import create_research_tasks, create_analysis_tasks
from eko.agent.crewai.crawl.tools import get_web_tools, get_analyst_tools
from crewai import Crew, Process

# Initialize persistent memory for coordinated research
memory_manager = CrewMemoryManager("Microsoft Corp", "sustainability_audit_2024")

# Create specialized agents with appropriate tool collections
research_agent = ResearchAgent(get_web_tools(memory_manager))
analysis_agent = AnalysisAgent(get_analyst_tools(memory_manager))

# Generate comprehensive task workflows
research_tasks = create_research_tasks(memory_manager, "Microsoft Corp")
analysis_tasks = create_analysis_tasks(memory_manager, "Microsoft Corp")

# Execute coordinated multi-agent research
crew = Crew(
    agents=[research_agent, analysis_agent],
    tasks=research_tasks + analysis_tasks,
    process=Process.sequential,
    memory=True,
    verbose=True
)

results = crew.kickoff()
```

### Progress Monitoring and Session Management

```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_mem import get_memory_tools

# Initialize memory manager for existing session
memory_manager = CrewMemoryManager("ACME Corporation", "ongoing_research_session")

# Get current research progress
progress_summary = memory_manager.get_crawl_progress_summary()
print(f"Research Progress:\n{progress_summary}")

# Access memory tools for detailed monitoring
memory_tools = get_memory_tools(memory_manager)
progress_tool = memory_tools[4]  # GetProgressTool

# Get detailed progress report
detailed_progress = progress_tool._run()
print(f"Detailed Progress:\n{detailed_progress}")
```

## Architecture Notes

### Multi-Agent Coordination Flow

```mermaid
sequenceDiagram
    participant U as User/System
    participant C as Crawler Controller
    participant RA as Research Agent
    participant JA as Journalist Agent
    participant AA as Analysis Agent
    participant MM as Memory Manager
    participant DB as Analytics Database

    U->>C: Initialize research session
    C->>MM: Create session memory
    C->>RA: Assign discovery tasks
    RA->>MM: Track URLs and documents
    RA->>DB: Store research data
    C->>JA: Assign media research tasks
    JA->>MM: Track news and insights
    JA->>DB: Store journalism findings
    C->>AA: Assign analysis tasks
    AA->>MM: Load all discoveries
    AA->>DB: Store structured insights
    C->>U: Return comprehensive results
    
    Note over MM,DB: Continuous synchronization ensures data persistence
```

### Memory Management Architecture

```mermaid
graph TB
    subgraph "Memory Storage Layer"
        A[JSON Files<br/>Fast Access]
        B[PostgreSQL<br/>Structured Queries]
        A <--> B
    end
    
    subgraph "Session Management"
        C[Session Initialization]
        D[Progress Tracking]
        E[Insight Accumulation]
        F[Report Generation]
    end
    
    subgraph "Multi-Agent Access"
        G[Research Agent]
        H[Journalist Agent]
        I[Analysis Agent]
        J[Summary Agent]
        K[Report Agent]
    end
    
    C --> A
    D --> A
    E --> B
    F --> B
    G --> C
    H --> D
    I --> E
    J --> E
    K --> F
```

### Database Schema Integration

```mermaid
erDiagram
    agent_sessions {
        text session_id PK
        text company_name
        jsonb memory_data
        timestamptz created_at
        timestamptz updated_at
    }

    agent_insights {
        bigint id PK
        text session_id FK
        text company_name
        text category
        text behavior_type
        text description
        text source_url
        text source_title
        double_precision confidence
        jsonb metadata
        timestamptz extracted_at
        timestamptz created_at
    }

    agent_reports {
        bigint id PK
        text session_id FK
        text company_name
        text summary
        integer insights_count
        integer visited_urls_count
        integer downloaded_files_count
        jsonb report_data
        timestamptz created_at
    }

    agent_tool_usage {
        bigint id PK
        text session_id FK
        text tool_name
        jsonb input_data
        jsonb output_data
        integer duration_ms
        boolean success
        timestamptz executed_at
    }

    agent_sessions ||--o{ agent_insights: "generates"
    agent_sessions ||--o{ agent_reports: "produces"
    agent_sessions ||--o{ agent_tool_usage: "tracks"
```

## Known Issues

Based on code analysis and Linear project integration:

### Current Technical Limitations

- **Memory File Performance**: Large JSON memory files (>10MB) may impact performance during concurrent agent access
- **Database Synchronization**: High-frequency updates can cause temporary lag between file and database storage
- **API Rate Limiting**: External APIs (Google Search, SEC, etc.) may throttle during intensive crawling sessions
- **Concurrent Session Access**: Potential race conditions when multiple agents update shared memory simultaneously

### Integration Challenges

- **Session Lifecycle Management**: No automatic cleanup mechanism for completed or abandoned research sessions
- **Tool Performance Monitoring**: Limited built-in analytics for individual tool execution times and success rates
- **Large Document Processing**: PDFs over 50MB may cause memory issues during content extraction
- **Dynamic Content Limitations**: JavaScript-heavy websites may not be fully crawled by current webpage fetching

### Monitoring and Observability Gaps

- **Real-time Monitoring**: Limited dashboard integration for monitoring active research sessions
- **Cost Tracking**: No built-in LLM usage cost monitoring per research session
- **Error Recovery**: Limited automated recovery mechanisms for corrupted memory files or failed API responses
- **Performance Metrics**: Insufficient metrics collection for tool execution optimization

## Future Work

### Admin Dashboard Integration (EKO-279)

Based on the "Agentic Scraper" Linear project, planned enhancements include:

- **Session Management Dashboard**: Real-time monitoring interface for active CrewAI research sessions
- **Performance Analytics**: Comprehensive metrics dashboard for tool usage, success rates, and execution times
- **Cost Management**: LLM usage tracking and cost analysis per research session and organization
- **Memory Optimization**: Admin controls for session cleanup, memory compression, and storage management
- **User Access Control**: Role-based permissions for session access and research data viewing

### Scalability and Performance Improvements

- **Distributed Memory**: Support for distributed memory across multiple compute instances
- **Caching Layer**: Redis integration for frequently accessed memory data and API response caching
- **Batch Processing**: Optimized database synchronization with batched memory updates
- **Load Balancing**: Multi-instance deployment support with shared database coordination
- **Archive System**: Automatic archiving of completed research sessions to cold storage

### Enhanced AI Capabilities

- **Advanced ESG Classification**: Machine learning models for more nuanced ESG insight categorization
- **Greenwashing Detection Algorithms**: Specialized AI models for identifying misleading sustainability claims
- **Multi-Language Support**: Enhanced processing capabilities for non-English ESG documents
- **Trend Analysis**: Time-series analysis of corporate ESG performance patterns and disclosure evolution
- **Risk Scoring Integration**: Direct integration with EkoIntelligence's corporate risk assessment algorithms

### Research Workflow Enhancements

- **Dynamic Task Generation**: AI-powered task creation based on discovered content and research gaps
- **Quality Assurance Automation**: Automated verification of research completeness and accuracy
- **Collaborative Research**: Support for human-AI collaborative research workflows
- **Industry Specialization**: Industry-specific research templates and specialized knowledge bases
- **Regulatory Compliance**: Enhanced integration with regulatory requirement tracking and compliance monitoring

## Troubleshooting

### Common Session Issues

#### Memory File Corruption

**Symptoms**: JSON parsing errors, agents returning empty results, session initialization failures

**Diagnosis**:
```bash
# Validate memory file structure
python -m json.tool var/crawl_memory/session_id.json

# Check file permissions and sizes
ls -la var/crawl_memory/
find var/crawl_memory/ -size +10M -name "*.json"
```

**Solution**:
```bash
# Backup corrupted file
mv var/crawl_memory/session_id.json var/crawl_memory/session_id.json.backup

# Memory manager will reinitialize with empty state
# Previous insights remain in database and will be recovered
```

#### Database Connection Failures

**Symptoms**: Memory synchronization errors, database timeout messages, agent persistence issues

**Diagnosis**:
```bash
# Test database connectivity
cd backoffice && ./bin/run_in_db.sh "SELECT 1"

# Check agent session table status
cd backoffice && ./bin/run_in_db.sh "
SELECT session_id, company_name, updated_at 
FROM agent_sessions 
WHERE updated_at > NOW() - INTERVAL '24 hours'
ORDER BY updated_at DESC;"
```

**Solution**:
- Memory manager automatically falls back to file-based storage
- Database synchronization resumes when connectivity restored
- Check PostgreSQL connection pool settings and network connectivity

#### API Rate Limiting

**Symptoms**: HTTP 429 errors, failed search operations, incomplete document discovery

**Diagnosis**:
```bash
# Check recent API usage patterns
cd backoffice && ./bin/run_in_db.sh "
SELECT tool_name, COUNT(*) as usage_count,
       MIN(executed_at) as first_call,
       MAX(executed_at) as last_call
FROM agent_tool_usage 
WHERE executed_at > NOW() - INTERVAL '1 hour'
  AND success = false
GROUP BY tool_name
ORDER BY usage_count DESC;"
```

**Solution**:
- Implement exponential backoff in tool retry logic
- Distribute API calls across multiple research sessions
- Configure tool-specific rate limiting in memory manager
- Consider using multiple API keys for high-volume research

### Performance Optimization

#### Large Memory Files

**Symptoms**: Slow agent initialization, high memory usage, file I/O bottlenecks

**Diagnosis**:
```bash
# Identify large memory files
find var/crawl_memory/ -name "*.json" -exec ls -lh {} \; | sort -k5 -hr

# Check database memory usage
cd backoffice && ./bin/run_in_db.sh "
SELECT session_id, company_name,
       pg_size_pretty(length(memory_data::text)) as memory_size,
       updated_at
FROM agent_sessions 
ORDER BY length(memory_data::text) DESC 
LIMIT 10;"
```

**Solution**:
```bash
# Archive completed sessions
cd backoffice && python main.py archive-agent-sessions --older-than-days 30

# Split long research sessions into focused sub-sessions
# Implement memory compression for active sessions
```

#### Tool Execution Timeouts

**Symptoms**: CrewAI agent timeouts, incomplete task execution, tool hanging

**Diagnosis**:
```bash
# Monitor tool performance metrics
cd backoffice && ./bin/run_in_db.sh "
SELECT tool_name, 
       AVG(duration_ms) as avg_duration_ms,
       MAX(duration_ms) as max_duration_ms,
       COUNT(*) as execution_count,
       AVG(CASE WHEN success THEN 1 ELSE 0 END) * 100 as success_rate
FROM agent_tool_usage 
WHERE executed_at > NOW() - INTERVAL '24 hours'
GROUP BY tool_name 
ORDER BY avg_duration_ms DESC;"
```

**Solution**:
- Increase CrewAI agent timeout settings for complex research tasks
- Implement async tool execution where possible
- Add circuit breaker pattern for unreliable external APIs
- Configure tool-specific timeout values based on expected execution times

### Debug Commands

```bash
# Monitor real-time agent activity
tail -f var/logs/crewai.log | grep "Agent\|Task\|Tool\|Memory"

# Check session health and progress
cd backoffice && ./bin/run_in_db.sh "
SELECT s.session_id, s.company_name, s.updated_at,
       COUNT(DISTINCT a.id) as insight_count,
       COUNT(DISTINCT t.tool_name) as unique_tools_used,
       MAX(t.executed_at) as last_tool_execution
FROM agent_sessions s
LEFT JOIN agent_insights a ON s.session_id = a.session_id
LEFT JOIN agent_tool_usage t ON s.session_id = t.session_id
WHERE s.updated_at > NOW() - INTERVAL '7 days'
GROUP BY s.session_id, s.company_name, s.updated_at
ORDER BY s.updated_at DESC;"

# Validate system integration
python -c "
from eko.agent.crewai.crawl.crawler import PersistentWebCrawlCrew
from eko.agent.crewai.crawl.tools import get_web_tools, get_analyst_tools
from eko.agent.crewai.crawl.memory import CrewMemoryManager

# Test system initialization
mm = CrewMemoryManager('Test Company', 'debug_session_001')
web_tools = get_web_tools(mm)
analyst_tools = get_analyst_tools(mm)
print(f'System Status: Memory Manager OK, Web Tools: {len(web_tools)}, Analyst Tools: {len(analyst_tools)}')
"
```

## FAQ

### User-Centric Questions and Answers

**Q: How do I start a new ESG research session for a company?**
A: Use the `PersistentWebCrawlCrew` class which handles all initialization:

```python
from eko.agent.crewai.crawl.crawler import PersistentWebCrawlCrew
crawler = PersistentWebCrawlCrew("Company Name", "unique_session_id")
results = crawler.run_comprehensive_research()
```

**Q: Can multiple agents work on the same company research simultaneously?**
A: Yes, all agents using the same memory manager instance share session state and coordinate automatically through the persistent memory system. This prevents duplicate work and enables true collaborative research.

**Q: How does the system prevent downloading the same documents multiple times?**
A: The memory system maintains comprehensive deduplication through the `TrackFileTool` and `TrackURLTool`, checking against all previously processed URLs and files in the session memory before attempting new downloads.

**Q: What happens if the research process is interrupted or crashes?**
A: The dual-storage memory system (JSON + PostgreSQL) ensures complete persistence. When agents restart, they automatically resume from the exact interruption point using stored memory state. No research progress is lost.

**Q: How do I monitor the progress of a long-running research session?**
A: Use the memory manager's progress tracking capabilities:

```python
memory_manager = CrewMemoryManager("Company", "session_id")
progress = memory_manager.get_crawl_progress_summary()
print(progress)
```

**Q: Can I customize the ESG categories and research focus areas?**
A: The system uses predefined ESG categories (Environmental, Social, Governance) with behavior types (Positive, Negative, Neutral). Custom categories require modifications to the `ExtractESGInsightsTool` and keyword definitions in the tools package.

**Q: How does the system handle API rate limits from external services?**
A: Tools implement basic retry logic and exponential backoff. For high-volume research, consider distributing work across multiple sessions, using different API keys, or implementing custom rate limiting in the memory manager.

**Q: What types of documents and sources does the system automatically discover?**
A: The system systematically discovers: annual reports, sustainability reports, ESG disclosures, regulatory filings (SEC, Companies House), press releases, news articles, investor presentations, CSR publications, third-party analyses, and NGO reports.

**Q: How do I integrate this system with the broader EkoIntelligence platform?**
A: The system automatically integrates through the analytics database (`agent_` tables) and feeds into the ESG analysis pipeline (`eko.analysis_v2`). Final results sync to customer database via `xfer_` tables for dashboard display.

## References

### Framework Documentation

- [CrewAI Framework Documentation](https://docs.crewai.com/)
- [CrewAI Multi-Agent Workflows](https://docs.crewai.com/concepts/agents)
- [CrewAI Task Management](https://docs.crewai.com/concepts/tasks)
- [CrewAI Tools Framework](https://docs.crewai.com/concepts/tools)
- [CrewAI Memory System](https://docs.crewai.com/concepts/memory)

### External API Documentation

- [Google Custom Search API](https://developers.google.com/custom-search/v1/overview)
- [Companies House API](https://developer.company-information.service.gov.uk/)
- [GLEIF API Documentation](https://www.gleif.org/en/lei-data/gleif-api)
- [SEC EDGAR API](https://www.sec.gov/edgar/sec-api-documentation)
- [NewsAPI Documentation](https://newsapi.org/docs)
- [Wikipedia MediaWiki API](https://www.mediawiki.org/wiki/API:Main_page)
- [Google Gemini API Documentation](https://ai.google.dev/gemini-api/docs)

### Related Code Files

- [`agents.py`](./agents.py) - Specialized CrewAI agent definitions for ESG research
- [`tasks.py`](./tasks.py) - Comprehensive task factory for research workflows
- [`memory.py`](./memory.py) - CrewMemoryManager persistent session system
- [`tools.py`](./tools.py) - Central tool import gateway and collections
- [`tools_new/`](./tools_new/) - Complete tool ecosystem implementation
- [`tools_mem/`](./tools_mem/) - Memory management tool collection
- [`crawler.py`](./crawler.py) - Main orchestration system

### Database Schema References

- [`eko.db`](../../db/__init__.py) - Database connection management
- Analytics Database Schema: `agent_sessions`, `agent_insights`, `agent_reports`, `agent_tool_usage`
- Customer Database Integration: `xfer_` tables for data synchronization

### EkoIntelligence Platform Integration

- [`eko.analysis_v2`](../../analysis_v2/) - ESG Analysis Pipeline
- [`eko.db.sync`](../../db/sync.py) - Database synchronization layer
- [Customer Dashboard](../../../../../apps/customer/) - Web application frontend
- [Admin Dashboard Project](../PLAN.md) - CrewAI monitoring dashboard specification

### Third-Party Dependencies

- [Pydantic Documentation](https://pydantic-docs.helpmanual.io/)
- [Loguru Logging Framework](https://loguru.readthedocs.io/en/stable/)
- [PostgreSQL JSONB](https://www.postgresql.org/docs/current/datatype-json.html)
- [Requests HTTP Library](https://docs.python-requests.org/en/latest/)
- [BeautifulSoup HTML Parsing](https://www.crummy.com/software/BeautifulSoup/bs4/doc/)

---

## Changelog

### 2025-07-30

- **Created comprehensive README.md** with complete module documentation and system architecture
- **Added detailed architecture diagrams** using Mermaid for multi-agent coordination, memory management, and database integration
- **Documented all 5 specialized agents** with roles, capabilities, and task assignments
- **Included 11-task research workflow** with phase-by-phase breakdown and intelligent coordination
- **Added comprehensive tool ecosystem documentation** with 17+ specialized tools and memory integration
- **Provided extensive usage examples** covering basic research, custom workflows, and progress monitoring
- **Included detailed troubleshooting section** with common issues, debug commands, and performance optimization
- **Added comprehensive FAQ section** addressing user-centric questions and implementation guidance
- **Provided complete reference documentation** linking to framework docs, APIs, and platform integration
- **Documented future work alignment** with EKO-279 admin dashboard and scalability improvements
- **Included database schema integration** showing analytics and customer database synchronization

---

(c) All rights reserved ekoIntelligence 2025