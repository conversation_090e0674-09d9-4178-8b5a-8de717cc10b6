"""
CrewAI Task Factory for Multi-Agent ESG Web Research and Document Discovery

This module provides a comprehensive task definition system for EkoIntelligence's CrewAI-powered 
ESG (Environmental, Social, Governance) research platform. It orchestrates sophisticated multi-agent 
research workflows through specialized task configurations that systematically discover, analyze, 
and document corporate ESG practices, sustainability claims, and potential policy violations through 
exhaustive web crawling and intelligent document analysis.

## Core Purpose
The module serves as the **task orchestration layer** for the CrewAI multi-agent system, defining 
complex, interconnected research workflows that enable AI agents to conduct comprehensive ESG 
investigations. It implements a systematic approach to corporate sustainability research that combines 
automated web crawling, regulatory database queries, investigative journalism techniques, and 
AI-powered content analysis to produce thorough ESG assessments and identify potential greenwashing.

## Task Architecture

### Multi-Phase Research Workflow
The module implements a sophisticated **11-task research pipeline** designed for exhaustive corporate 
ESG investigation:

#### Phase 1: Systematic Document Discovery (Tasks 1-5)
- **Regulatory & Third-Party Research**: Discovers ESG reports from activist groups, NGOs, and regulatory bodies
- **Comprehensive Web Research**: Multi-database searches across Companies House, SEC, GLEIF, and Wikipedia
- **Corporate Website Analysis**: Deep exploration of company websites for ESG documentation
- **Financial & Investor Content**: Systematic collection of annual reports, investor presentations, and regulatory filings
- **Sustainability Documentation**: Focused discovery of ESG reports, CSR publications, and environmental disclosures

#### Phase 2: Media & Investigation Research (Tasks 6-7)
- **Journalistic Coverage**: News articles, press releases, and media analysis of ESG activities
- **Investigative Research**: Third-party critical analysis from pressure groups and NGOs for balanced assessment

#### Phase 3: Intelligent Search Evolution (Tasks 8-10)
- **Dynamic Query Generation**: AI-powered search query evolution based on discovered content
- **Gap Analysis Research**: Systematic identification and filling of research gaps
- **Final Exhaustive Search**: Comprehensive final sweep to ensure complete document coverage

#### Phase 4: Analysis & Reporting (Tasks 11-12)
- **Content Analysis**: Systematic analysis of discovered documents for ESG insights and patterns
- **Comprehensive Reporting**: Professional ESG report generation with complete document inventory

### Task Complexity & Intelligence
Each task incorporates sophisticated AI reasoning:
- **Multi-Iteration Capability**: Tasks support up to 50 iterations for thorough investigation
- **Intelligent Query Evolution**: Search strategies adapt based on discovered content
- **Source Diversification**: Systematic coverage of corporate, regulatory, and third-party sources
- **Quality Assurance**: Built-in mechanisms for comprehensive coverage verification

## Key Functions

### `create_research_tasks(memory_manager: CrewMemoryManager, company_name: str, max_iterations: int = 50) -> List[Task]`
Creates the complete **11-task research workflow** for comprehensive ESG investigation:

**Research Tasks Overview:**
1. **Third-Party ESG Research**: Activist groups, NGOs, and independent analysis
2. **Multi-Database Systematic Search**: Companies House, SEC, GLEIF, Wikipedia integration
3. **Corporate Website Deep Exploration**: Comprehensive website document discovery
4. **Regulatory & Financial Documentation**: Annual reports, investor presentations, SEC filings
5. **Sustainability Report Collection**: ESG reports, CSR publications, environmental data
6. **Media Coverage Analysis**: News articles, press releases, journalist investigations
7. **Investigative Third-Party Research**: Critical analysis and corporate misconduct research
8. **Dynamic Search Evolution**: AI-generated search queries based on discoveries
9. **Gap Analysis & Completion**: Systematic research gap identification and filling
10. **Final Exhaustive Documentation Hunt**: Comprehensive final search phase
11. **Content Analysis & Insight Extraction**: Document analysis for ESG patterns and insights

**Agent Assignments:**
- **ResearchAgent**: Primary web crawling and document discovery (Tasks 1, 3, 4, 5, 8, 9, 10)
- **AnalysisAgent**: Strategic analysis and content evaluation (Tasks 2, 11)
- **JournalistAgent**: Investigative journalism and media research (Tasks 6, 7)

### `create_analysis_tasks(memory_manager: CrewMemoryManager, company_name: str, max_iterations: int = 50) -> List[Task]`
Creates **2 specialized analysis tasks** for content analysis and document organization:
- **Pattern Analysis Task**: Identifies ESG patterns and generates search strategies
- **Document Inventory Task**: Creates comprehensive document organization and gap analysis

### `create_summary_task(memory_manager: CrewMemoryManager, company_name: str, max_iterations: int = 50) -> Task`
Creates the **final reporting task** for professional ESG report generation with:
- Executive summary with key findings
- Research methodology documentation
- Comprehensive document inventory by category
- ESG insights and initial observations
- Gap analysis and recommendations
- Complete search query documentation

## CrewAI Framework Integration

### Task Orchestration
Tasks are designed for CrewAI's collaborative framework:
- **Sequential Execution**: Tasks build upon previous discoveries and insights
- **Memory Persistence**: All tasks integrate with `CrewMemoryManager` for session continuity
- **Agent Specialization**: Tasks assigned to agents based on expertise and capabilities
- **Tool Integration**: Tasks leverage comprehensive tool collections from `get_web_tools()` and `get_analyst_tools()`

### Memory Management Integration
Each task integrates with the persistent memory system:
- **Session Tracking**: Progress persisted across research interruptions
- **Document Tracking**: All discovered documents logged with metadata
- **Insight Accumulation**: ESG insights captured and categorized during research
- **Progress Monitoring**: Task completion and discovery metrics tracked

## Database Integration

### Analytics Database Tables
Tasks interface with the EkoIntelligence analytics database:
- **agent_sessions**: Session metadata and task progress tracking
- **agent_insights**: ESG insights categorized by environmental, social, governance themes
- **agent_reports**: Final research reports with aggregated statistics and findings
- **agent_tool_usage**: Tool usage analytics for performance optimization

### Research Output Storage
Task results are systematically stored:
- **Document Metadata**: URLs, titles, sources, and download timestamps
- **ESG Categorization**: Insights classified by category, behavior type, and confidence
- **Search Intelligence**: Query evolution and research strategy documentation
- **Quality Metrics**: Coverage analysis and research completeness indicators

## Advanced Features

### Intelligent Search Evolution
Tasks implement sophisticated search strategy evolution:
- **Dynamic Query Generation**: AI creates targeted queries based on discoveries
- **Source Diversification**: Systematic coverage across corporate and third-party sources
- **Gap Detection**: Automatic identification of research gaps and missing document types
- **Iterative Refinement**: Search strategies continuously improved based on results

### Comprehensive Coverage Assurance
Tasks ensure exhaustive research through:
- **Multi-Source Verification**: Same information verified across multiple sources
- **Depth Requirements**: Minimum 3-level website exploration for thorough coverage
- **Quality Thresholds**: Minimum document counts and diversity requirements
- **Final Verification**: Comprehensive review tasks to identify missed opportunities

### Professional Reporting Standards
Final reporting tasks ensure high-quality outputs:
- **Stakeholder-Ready Reports**: Professional ESG reports suitable for investors and regulators
- **Evidence-Based Analysis**: All findings supported by documented sources
- **Comprehensive Documentation**: Complete inventory of all research activities and discoveries
- **Balanced Assessment**: Both positive ESG initiatives and concerns documented

## System Architecture Context

### EkoIntelligence Platform Integration
Tasks operate within the broader ESG analysis ecosystem:
- **Research Foundation**: Task outputs feed ESG scoring and greenwashing detection algorithms
- **Database Pipeline**: Research data flows into analytics database for further processing
- **Customer Reporting**: Final reports accessible through customer dashboard via `xfer_` tables
- **Regulatory Compliance**: Research methodology supports audit trails and compliance requirements

### Multi-Agent Workflow
Tasks enable sophisticated agent collaboration:
- **Research Coordination**: Multiple agents work simultaneously on different research aspects
- **Expertise Utilization**: Tasks matched to agent capabilities (research, journalism, analysis)
- **Progress Synchronization**: Shared memory enables coordinated research across agent teams
- **Quality Assurance**: Cross-agent validation and verification of research findings

## Performance Characteristics

### Scalability & Efficiency
- **Parallel Execution**: Multiple agents can execute different task phases simultaneously
- **Resource Optimization**: Tasks designed to minimize redundant web requests and processing
- **Memory Efficiency**: Large document collections streamed rather than fully loaded
- **Database Optimization**: Batch operations for efficient insight and document storage

### Quality & Reliability
- **Error Recovery**: Tasks continue with available resources when individual sources fail
- **Comprehensive Logging**: All task activities logged via Loguru for debugging and monitoring
- **Validation Mechanisms**: Built-in checks ensure research quality and completeness
- **Audit Trails**: Complete documentation of research methodology and source verification

## Technology Stack Dependencies

### Core Framework Integration
- **CrewAI**: Multi-agent task orchestration and collaboration system
- **Google Gemini Models**: LLM providers for intelligent content analysis and decision making
- **PostgreSQL**: Persistent storage for research data and session management
- **Loguru**: Structured logging for task monitoring and debugging

### EkoIntelligence Platform
- **Memory Management**: `CrewMemoryManager` for session persistence and coordination
- **Tool Integration**: Comprehensive web research tool collections for specialized capabilities
- **Database Layer**: Analytics database integration for research storage and retrieval
- **ESG Pipeline**: Integration with broader corporate sustainability analysis system

## Usage Examples

### Basic Research Workflow
```python
from eko.agent.crewai.crawl.tasks import create_research_tasks, create_analysis_tasks, create_summary_task
from eko.agent.crewai.crawl.memory import CrewMemoryManager

# Initialize session memory
memory_manager = CrewMemoryManager("ACME Corporation", "research_session_001")

# Create comprehensive research task collection
research_tasks = create_research_tasks(memory_manager, "ACME Corporation")

# Create analysis and reporting tasks
analysis_tasks = create_analysis_tasks(memory_manager, "ACME Corporation") 
summary_task = create_summary_task(memory_manager, "ACME Corporation")

# Combine for complete workflow
all_tasks = research_tasks + analysis_tasks + [summary_task]
```

### Multi-Agent Task Assignment
```python
from crewai import Crew
from eko.agent.crewai.crawl.agents import ResearchAgent, JournalistAgent, AnalysisAgent

# Initialize specialized agents
research_agent = ResearchAgent(tools=get_web_tools(memory_manager))
journalist_agent = JournalistAgent(tools=get_web_tools(memory_manager))
analysis_agent = AnalysisAgent(tools=get_analyst_tools(memory_manager))

# Create collaborative crew with task assignment
crew = Crew(
    agents=[research_agent, journalist_agent, analysis_agent],
    tasks=all_tasks,
    verbose=True,
    memory=True
)

# Execute comprehensive ESG research workflow
results = crew.kickoff()
```

## Integration with EkoIntelligence ESG Analysis

This task system serves as the foundation for EkoIntelligence's comprehensive ESG analysis platform:
- **Corporate Research**: Systematic investigation of corporate ESG practices and claims
- **Greenwashing Detection**: Research foundation for identifying misleading sustainability claims
- **Regulatory Compliance**: Audit trail generation for ESG assessment methodologies
- **Customer Intelligence**: Research insights displayed in customer dashboards and reports
- **Risk Assessment**: Document discoveries inform ESG risk scoring and investment analysis

@see https://docs.crewai.com/concepts/tasks CrewAI Task Framework Documentation
@see https://docs.crewai.com/concepts/crews CrewAI Crew Orchestration System
@see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation
@see ./agents.py Multi-agent system specialized for ESG research
@see ./memory.py CrewMemoryManager for persistent session tracking
@see ./tools_new/all_tools.py Comprehensive web research tool collections
@see ../../../analysis_v2/ ESG Analysis Pipeline integration
@see ../../../db/data/ Database access objects for research persistence
<AUTHOR>
@updated 2025-07-23
@description CrewAI task factory for comprehensive multi-agent ESG web research with intelligent document discovery and analysis workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from typing import List

from crewai import Task

from eko.agent.crewai.crawl.agents import JournalistAgent, ResearchAgent, AnalysisAgent, SummaryAgent, ReportAgent
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_new import company_wrong_doing
from eko.agent.crewai.crawl.tools_new.all_tools import get_web_tools, get_analyst_tools
from eko.domains.domain_queries import get_search_journalism_allow_domains


def journalist_site_list():
    return [ f"site:{domain}" for domain in get_search_journalism_allow_domains()[:5]]

def create_research_tasks(memory_manager: CrewMemoryManager, company_name: str, max_iterations: int = 50, ) -> List[
    Task]:
    """
    Create research tasks for gathering information about the target company.

    Args:
        company_name: The name of the target company

    Returns:
        List[Task]: The research tasks
    """
    tasks = [Task(
        description=(
            f"Look for third party reports {company_name} from activist, pressure groups and NGOs. "
            f"Use the `search_web` tool with specific queries targeting: "
            f" {','.join(company_wrong_doing(company_name))} "
            f"For each relevant result, use `fetch_webpage` to visit the page and look for downloadable documents. "
            f"Pay special attention to sustainability sections of the company website and ESG data providers "
            f"or reporting frameworks like GRI, SASB, CDP, etc. "
            f"For any PDF links you find, use the download_pdf tool to retrieve and process them. "
            f"Document all your findings, especially noting any downloaded documents."
        ),
        expected_output=(
            "A comprehensive inventory of all sustainability reports, ESG reports, and CSR publications "
            "found for the company, including the URL of each document, its title/description, the source "
            "where it was found, and a brief summary of its content if available."
        ),
        agent=ResearchAgent(get_web_tools(memory_manager)),
        async_execution=False
    ),
        Task(
            description=(
                f"Conduct an exhaustive search for information about {company_name}. "
                f"Use search_ch, search_sec, search_gleif, search_wikipedia to get key business information, "
                f"Track this with track_insight."
                f"Then start with general searches about the company, then progressively refine your searches "
                f"based on what you learn. Your primary goal is to identify as many potential sources of "
                f"documents and reports about the company and it's activities as possible. "
                f"Use the search_web tool extensively with different queries to find various sources. "
                f"For each promising page, use the fetch_webpage tool to visit it and extract_links to "
                f"find more potential document sources. "
                f"Pay special attention to the company's official website, investor relations section, "
                f"sustainability pages, annual reports, and any regulatory filings. "
                f"Document all your findings, especially noting any potential document repositories "
                f"or pages that might contain downloadable files. "
                f"Be relentless and thorough - try at least 15-20 different search queries to ensure "
                f"comprehensive coverage. Use information you discover to generate new search ideas."
            ),
            expected_output=(
                "A comprehensive list of potential document sources, including URLs to the company's "
                "website sections, investor relations pages, sustainability reports, regulatory filings, "
                "and any other pages that might contain downloadable documents. Include a list of at least "
                "15-20 different search queries you used and what you discovered from each. Please run download"
            ),
            agent=AnalysisAgent(get_analyst_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Explore {company_name}'s official website thoroughly to find all downloadable documents. "
                f"Start by searching for the company's main website and navigating to it using the fetch_webpage tool. "
                f"Use extract_links to identify all sections of the website, paying special attention to: "
                f"- Investor Relations section "
                f"- About Us pages "
                f"- Sustainability/ESG/CSR sections "
                f"- Media/Press/News sections "
                f"- Publications/Resources sections "
                f"- Annual Reports pages "
                f"For each relevant section, use `fetch_webpage` to visit it and `extract_links` again to find "
                f"deeper pages. Look specifically for links that might be PDFs or articles. "
                f"For any PDF or article links you find, use the `download_pdf_or_article` tool to retrieve and process them. "
                f"Be extremely thorough - explore every section of the website that might contain documents. "
                f"Document all your findings, especially noting any downloaded documents."
            ),
            expected_output=(
                "A comprehensive inventory of all documents found on the company's official website, "
                "including the URL of each document, its title/description, the section of the website "
                "where it was found, and a brief summary of its content if available."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Search for {company_name}'s regulatory filings, annual reports, and investor presentations. "
                f"Use the `search_web` tool with specific queries targeting: "
                f"- \"{company_name} annual report\" "
                f"- \"{company_name} investor presentation\" "
                f"- \"{company_name} financial results\" "
                f"- \"{company_name} SEC filings\" (if a US company) "
                f"- \"{company_name} regulatory filings\" "
                f"- \"{company_name} earnings call transcript\" "
                f"- \"{company_name} investor day\" "
                f"For each relevant result, use `fetch_webpage` to visit the page and look for downloadable documents. "
                f"Pay special attention to investor relations sections of the company website and financial "
                f"data providers like SEC (for US companies), Companies House (for UK companies), or "
                f"similar regulatory databases. "
                f"For any PDF links you find, use the `download_pdf_or_article` tool to retrieve and process them. "
                f"Document all your findings, especially noting any downloaded documents."
            ),
            expected_output=(
                "A comprehensive inventory of all regulatory filings, annual reports, and investor presentations "
                "found for the company, including the URL of each document, its title/description, the source "
                "where it was found, and a brief summary of its content if available."
            ),
            agent=ResearchAgent(get_analyst_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Search for {company_name}'s sustainability reports, ESG reports, and CSR publications. "
                f"Use the `search_web` tool with specific queries targeting: "
                f"- \"{company_name} sustainability report\" "
                f"- \"{company_name} ESG report\" "
                f"- \"{company_name} corporate social responsibility\" "
                f"- \"{company_name} environmental report\" "
                f"- \"{company_name} social impact\" "
                f"- \"{company_name} climate change\" "
                f"- \"{company_name} carbon footprint\" "
                f"- \"{company_name} diversity inclusion report\" "
                f"For each relevant result, use `fetch_webpage` to visit the page and look for downloadable documents. "
                f"Pay special attention to sustainability sections of the company website and ESG data providers "
                f"or reporting frameworks like GRI, SASB, CDP, etc. "
                f"For any PDF links you find, use the download_pdf tool to retrieve and process them. "
                f"Document all your findings, especially noting any downloaded documents."
            ),
            expected_output=(
                "A comprehensive inventory of all sustainability reports, ESG reports, and CSR publications "
                "found for the company, including the URL of each document, its title/description, the source "
                "where it was found, and a brief summary of its content if available."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),

        Task(
            description=(
                f"Search for news articles, press releases, and media coverage about {company_name}'s ESG activities. "
                f"Use the `generate_search_queries` to create queries and then the `search_web tool` with the supplied queries."
                f"You can also use the site: prefix to choose the site you want to search, e.g. site:news.bbc.co.uk,site:guardian.co.uk{', '.join(journalist_site_list())} etc." 
                f"For each relevant result, use `fetch_webpage` to visit the page and extract the content. "
                f"Also use `extract_links` to find any related articles or downloadable documents. "
                f"For any PDFs or articles you find, use the `download_pdf_or_article` tool to retrieve and process them. "
                f"Document all your findings, especially noting any downloaded documents or significant news stories."
            ),
            expected_output=(
                "A list of downloaded news articles, press releases, and media coverage about "
                "the company's ESG activities and impacts, including the URL of each source, its title/headline, "
                "the publication date if available, and a brief summary of the content."
            ),
            agent=JournalistAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Look for third party reports {company_name} from activist, pressure groups and NGOs. "
                f"Use the `search_web` tool with specific queries targeting: "
                f" {','.join(company_wrong_doing(company_name))} "
                f"For each relevant result, use `fetch_webpage` to visit the page and look for downloadable documents. "
                f"Pay special attention to sustainability sections of the company website and ESG data providers "
                f"or reporting frameworks like GRI, SASB, CDP, etc. "
                f"For any PDF links you find, use the download_pdf tool to retrieve and process them. "
                f"Document all your findings, especially noting any downloaded documents."
            ),
            expected_output=(
                "A comprehensive inventory of all sustainability reports, ESG reports, and CSR publications "
                "found for the company, including the URL of each document, its title/description, the source "
                "where it was found, and a brief summary of its content if available."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Based on all the information you've gathered so far about {company_name}, generate at least "
                f"10 new, highly specific search queries that might lead to additional documents or information. "
                f"Look for patterns, names of specific initiatives, executive names, partnerships, subsidiaries, "
                f"or unique terminology used by the company that could lead to more targeted searches. "
                f"For each new query you generate, use the search_web tool to execute it, then use fetch_webpage "
                f"to visit the most promising results. "
                f"For any new pages you visit, use extract_links to find potential document links, and "
                f"download_pdf for any PDF links you discover. "
                f"Document all your findings, especially noting any new documents or information sources "
                f"that weren't found in previous searches."
            ),
            expected_output=(
                "A list of at least 10 new, highly specific search queries generated based on initial research, "
                "along with the results of each query, including any new documents or information sources discovered. "
                "Include the reasoning behind each new query and how it led to new discoveries."
            ),
            agent=JournalistAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Conduct a final, EXTREMELY THOROUGH search for any remaining documents or information about {company_name} "
                f"that might have been missed in previous searches. "
                f"Review all the information and documents you've gathered so far, and identify any potential gaps "
                f"or areas that might benefit from additional research. "
                f"Generate at least 15 more search queries targeting these gaps, and use the search_web tool to "
                f"execute them. Be creative and exhaustive in your search queries. "
                f"Also, revisit the most promising document sources you've identified and explore them more deeply "
                f"to ensure no documents were missed. Go at least 3 levels deep in any promising website. "
                f"For any new pages you visit, use extract_links to find potential document links, and "
                f"download_pdf for any PDF links you discover. "
                f"Try different search engines and specialized document repositories if available. "
                f"Your goal is to find EVERY POSSIBLE DOCUMENT related to {company_name}. "
                f"Compile a final, comprehensive inventory of all documents and information sources discovered "
                f"throughout the entire research process."
            ),
            expected_output=(
                "A final, comprehensive inventory of all documents and information sources discovered throughout "
                "the entire research process, organized by type (annual reports, sustainability reports, press releases, etc.) "
                "and including the URL, title/description, and a brief summary of each. Also include a list of any "
                "potential gaps or areas where additional research might be beneficial."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"FINAL DOCUMENT HUNT: This is your last chance to find any documents about {company_name} that might have been missed. "
                f"Use everything you've learned about the company to generate at least 10 highly specific, creative search queries "
                f"that might uncover hidden documents. Think about:"
                f"- Specific project names or initiatives mentioned in other documents"
                f"- Names of executives or board members + 'presentation' or 'report'"
                f"- Subsidiary companies or divisions + 'annual report' or 'sustainability'"
                f"- Industry-specific terms + company name + 'pdf'"
                f"- Regulatory filing codes or standards specific to their industry"
                f"- Partner organizations or joint ventures + company name + 'report'"
                f"For each query, use search_web and thoroughly explore the results, going at least 3 pages deep in search results. "
                f"For any promising page, use fetch_webpage and extract_links to find document links. "
                f"Download EVERY PDF you find using download_pdf. "
                f"Be absolutely relentless - your only goal is to find documents that others would miss."
            ),
            expected_output=(
                "A detailed report of your final document hunt, including all search queries used, "
                "all new documents discovered, and a complete inventory of ALL documents found throughout "
                "the entire research process. Include URLs, document titles, and brief descriptions for each."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        )
    ]

    return tasks


def create_analysis_tasks(memory_manager: CrewMemoryManager, company_name: str, max_iterations: int = 50) -> List[Task]:
    """
    Create analysis tasks for analyzing the gathered information for ESG insights.

    Args:
        company_name: The name of the target company

    Returns:
        List[Task]: The analysis tasks
    """
    tasks = [
        Task(
            description=(
                f"Analyze all the documents and information gathered about {company_name} to identify "
                f"patterns and generate ideas for additional document searches. "
                f"Review the content of all downloaded documents and extracted information, looking for: "
                f"- Names of specific ESG initiatives or programs "
                f"- Names of executives or teams responsible for ESG "
                f"- Partnerships with sustainability organizations "
                f"- Subsidiaries or affiliated companies that might have their own reports "
                f"- Specific terminology or frameworks used in ESG reporting "
                f"- References to other documents or reports that might not have been found yet "
                f"Based on this analysis, generate at least 10 new, highly specific search queries "
                f"that might lead to additional documents. "
                f"For each new query, provide a clear rationale based on the information discovered "
                f"in the existing documents."
            ),
            expected_output=(
                "A list of at least 10 new, highly specific search queries generated based on analysis "
                "of the existing documents, along with a clear rationale for each query explaining how "
                "it relates to information found in the existing documents and why it might lead to "
                "additional valuable documents."
            ),
            agent=AnalysisAgent(get_analyst_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Create a comprehensive document inventory for {company_name} based on all the research conducted. "
                f"Review all the documents that have been discovered and downloaded, and organize them into a "
                f"structured inventory with the following information for each document: "
                f"1. Document Title/Name "
                f"2. Document Type (Annual Report, Sustainability Report, Press Release, etc.) "
                f"3. Publication Date (if available) "
                f"4. URL/Source "
                f"5. Brief Description of Content "
                f"6. Key ESG Topics Covered "
                f"Organize the inventory by document type and publication date (if available). "
                f"Also identify any notable gaps in the document collection - are there any types of "
                f"documents or time periods that appear to be missing?"
            ),
            expected_output=(
                "A comprehensive, well-organized inventory of all documents discovered for the company, "
                "structured by document type and including all the requested information for each document. "
                "Also include an analysis of any notable gaps in the document collection and recommendations "
                "for additional document searches that might fill these gaps."
            ),
            agent=SummaryAgent(get_analyst_tools(memory_manager)),
            async_execution=False
        )
    ]

    return tasks


def create_summary_task(memory_manager: CrewMemoryManager, company_name: str, max_iterations: int = 50) -> Task:
    """
    Create a summary task for generating a final ESG report.

    Args:
        company_name: The name of the target company

    Returns:
        Task: The summary task
    """
    return Task(
        description=(
            f"Create a comprehensive document discovery report for {company_name} based on all the research "
            f"and analysis conducted. The report should include the following sections:\n"
            f"1. Executive Summary: A brief overview of the document discovery process and key findings\n"
            f"2. Research Methodology: Description of the search strategies and tools used\n"
            f"3. Document Inventory: Comprehensive listing of all documents discovered, organized by type\n"
            f"   - Annual Reports and Financial Filings\n"
            f"   - Sustainability and ESG Reports\n"
            f"   - Press Releases and News Articles\n"
            f"   - Corporate Presentations and Fact Sheets\n"
            f"   - Other Documents\n"
            f"4. Key Insights: Initial observations about the company's ESG practices based on the documents\n"
            f"5. Document Gaps: Analysis of any notable gaps in the document collection\n"
            f"6. Search Queries: List of all search queries used during the research process\n"
            f"7. Recommendations: Suggestions for additional research or document sources\n\n"
            f"For each document in the inventory, include the title, publication date, URL, and a brief "
            f"description of the content. The report should be well-structured, comprehensive, and provide "
            f"a clear overview of all documents discovered during the research process."
        ),
        expected_output=(
            "A comprehensive, well-structured document discovery report that provides a complete inventory "
            "of all documents found during the research process, along with analysis of the document collection "
            "and recommendations for further research."
        ),
        agent=ReportAgent(get_analyst_tools(memory_manager)),
        async_execution=False
    )
