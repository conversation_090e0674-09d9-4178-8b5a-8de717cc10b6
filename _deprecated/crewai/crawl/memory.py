"""
AI Agent Memory Management System for Persistent ESG Research Sessions

This module provides sophisticated memory management and session persistence capabilities for
EkoIntelligence's CrewAI-powered multi-agent ESG research system. It implements a dual-storage
architecture (file-based and database-backed) that enables autonomous AI agents to maintain
research continuity across long-running corporate analysis sessions, with full resumability
from interruption points and comprehensive progress tracking.

## Core Purpose
The memory management system serves as the **persistence backbone** for the multi-agent ESG
research platform, enabling AI research crews to conduct extended corporate sustainability
analysis that can span hours or days while maintaining full session state, research progress,
and discovered insights across system restarts, network interruptions, or planned maintenance.

## Architecture & Design

### Dual Storage System
- **File-based Memory**: Local JSON files provide fast access and development flexibility
- **Database Persistence**: PostgreSQL storage ensures durability and multi-session querying
- **Automatic Synchronization**: Bidirectional sync maintains consistency between storage layers
- **Fallback Mechanisms**: Graceful degradation when database connectivity is unavailable

### Session Management Framework
The system implements comprehensive session lifecycle management through structured data models:

#### Session State Structure
```json
{
    "company_name": "Target Company Ltd",
    "session_id": "unique_session_identifier",
    "status": "initialized|in_progress|completed",
    "visited_urls": ["url1", "url2", "..."],
    "downloaded_files": [{"path": "/path/to/file", "source_url": "...", "downloaded_at": "..."}],
    "insights": [{"category": "sustainability", "behavior_type": "greenwashing", "confidence": 0.85, "..."}],
    "tasks_completed": [{"task_name": "initial_search", "task_result": {...}, "task_index": 0}],
    "last_position": {
        "task_index": 5,
        "url_index": 23,
        "search_queries": ["ESG report", "sustainability claims"],
        "search_query_index": 1
    },
    "created_at": "2025-07-23T10:00:00Z",
    "updated_at": "2025-07-23T12:30:00Z"
}
```

## Database Integration

### Primary Tables
The memory system integrates with three core database tables for comprehensive session management:

#### agent_sessions
**Purpose**: Primary session state storage and memory persistence  
**Schema**: `session_id (PK), company_name, memory_data (JSONB), created_at, updated_at`  
**Role**: Stores complete session memory as structured JSONB for efficient querying and updates

#### agent_reports  
**Purpose**: Final research outcome storage and structured reporting  
**Schema**: `id (PK), session_id (FK), company_name, summary, *_count fields, report_data (JSONB)`  
**Role**: Persists completed research reports with aggregated metrics and final analysis

#### agent_insights
**Purpose**: Individual insight storage and categorization  
**Schema**: `id (PK), session_id (FK), company_name, category, behavior_type, description, source_url, confidence, metadata (JSONB)`  
**Role**: Granular storage of AI-generated insights for advanced querying and analysis

## Key Features

### Intelligent Progress Tracking
- **Multi-dimensional Progress**: Tracks task completion, URL exploration, and search query progression
- **Resumability Indicators**: Precise last position tracking enables seamless continuation from interruptions
- **Hierarchical Task Management**: Supports complex research workflows with nested task structures
- **Search Query Evolution**: Monitors search strategy development and refinement over time

### Advanced Insight Management
- **Categorized Intelligence**: Organizes insights by ESG category (environmental, social, governance)
- **Behavior Classification**: Identifies behavior types (greenwashing, compliance, transparency)
- **Confidence Scoring**: Quantifies reliability of AI-generated insights (0.0-1.0 scale)
- **Source Attribution**: Maintains complete provenance from insight to originating web source
- **Duplicate Prevention**: Intelligent deduplication prevents insight redundancy across sessions

### Memory Performance Optimization
- **Lazy Loading**: Memory data loaded only when required to minimize resource usage
- **Efficient Synchronization**: Smart sync operations minimize database transactions
- **Error Recovery**: Robust error handling with automatic fallback to alternative storage
- **Connection Pooling**: Database connections managed through centralized connection management

## System Architecture Context

This memory management system operates within EkoIntelligence's broader AI analysis pipeline:

### Integration Points
- **CrewAI Orchestration**: Provides session persistence for multi-agent research workflows
- **Web Crawling Tools**: Stores and tracks URLs, files, and content discovered during research
- **ESG Analysis Pipeline**: Feeds insights into downstream corporate sustainability analysis
- **Reporting System**: Generates comprehensive research reports from accumulated session data
- **Customer Dashboard**: Final reports and insights displayed through customer web application

### Data Flow Architecture
```
AI Agents Research → Memory Manager → Database Storage
    ↓                    ↓               ↓
Tool Interactions → Session Updates → Insight Tracking
    ↓                    ↓               ↓
Progress Updates → File Persistence → Report Generation
```

## Usage Patterns

### Session Initialization
```python
memory_manager = CrewMemoryManager("Target Company Ltd", "session_uuid")
# Automatically handles session creation/recovery and database synchronization
```

### Progress Tracking During Research
```python
memory_manager.track_visited_url("https://company.com/sustainability")
memory_manager.track_downloaded_file("/path/to/esg_report.pdf", "https://source-url.com")
memory_manager.track_insight({
    "category": "environmental",
    "behavior_type": "greenwashing",
    "description": "Claims carbon neutrality without supporting evidence",
    "confidence": 0.92,
    "source_url": "https://company.com/press-release"
})
```

### Session Completion and Reporting
```python
final_result = {
    "summary": "Comprehensive ESG analysis summary",
    "findings": [...],
    "recommendations": [...]
}
memory_manager.save_final_result(final_result)
```

## Error Handling & Resilience

### Graceful Degradation
- **Database Connectivity Loss**: Falls back to file-based storage with queued sync operations
- **File System Issues**: Attempts database-first operation with local caching disabled
- **Corrupted Memory Files**: Automatic recovery from database backup or clean initialization
- **Partial Session Data**: Intelligent merge strategies for incomplete session recovery

### Monitoring & Diagnostics
- **Comprehensive Logging**: All memory operations logged via Loguru for debugging and monitoring
- **Progress Summaries**: Rich text summaries provide human-readable session status reports
- **Performance Metrics**: Built-in counters track memory operation efficiency and database sync timing

## Dependencies & Configuration

### Key Dependencies
- **Loguru**: Structured logging with exception capture and performance monitoring
- **PostgreSQL**: Primary database backend via `get_bo_conn()` connection management  
- **JSON**: Native Python JSON handling for file-based memory serialization
- **Datetime**: Timezone-aware timestamp management for session lifecycle tracking

### File System Requirements
- **Memory Directory**: `{cwd}/var/crawl_memory/` for session file storage
- **Write Permissions**: Required for session file creation and update operations
- **Storage Space**: Scales with research session complexity and insight discovery volume

## Security Considerations

### Data Protection
- **Sensitive Information**: Memory data may contain confidential corporate analysis results
- **Access Control**: File-based storage relies on OS-level permissions for access control
- **Database Security**: Leverages application-level database connection security and RLS policies
- **Audit Trail**: Complete operation logging provides security audit capabilities

## Performance Characteristics

### Scalability
- **Session Isolation**: Individual memory managers prevent cross-session resource contention
- **Lazy Operations**: Memory loaded and synchronized only when required
- **Batch Processing**: Insight storage operations batched for database efficiency
- **Resource Cleanup**: Automatic cleanup of completed sessions and temporary files

### Memory Usage
- **Lightweight Core**: Minimal memory footprint for session metadata and progress tracking
- **Data Streaming**: Large insights and file lists streamed rather than fully loaded
- **Connection Management**: Database connections released promptly after operations
- **File I/O Optimization**: JSON files optimized for fast read/write operations with minimal parsing overhead

## Integration with EkoIntelligence Platform

The memory management system serves as a critical infrastructure component within EkoIntelligence's
comprehensive ESG analysis platform, providing the persistence layer that enables:

- **Long-Running Research**: Multi-hour/multi-day corporate sustainability analysis sessions
- **Regulatory Compliance**: Audit trail maintenance for corporate ESG assessment activities  
- **Customer Reporting**: Foundation data for customer-facing ESG analysis reports and dashboards
- **Research Continuity**: Seamless handoff between different AI agent teams and research phases
- **Quality Assurance**: Historical session data enables analysis quality improvement and validation

@see https://loguru.readthedocs.io/en/stable/ Loguru Python Logging Documentation
@see https://crew.ai/ CrewAI Multi-Agent Framework Documentation  
@see https://www.postgresql.org/docs/current/datatype-json.html PostgreSQL JSONB Documentation
@see ../tools_mem/ Memory-integrated tool collection for agent operations
@see ../crawler.py Main CrewAI orchestration system using this memory management
@see ../../../../../../apps/customer/ Customer web application consuming research reports
<AUTHOR>
@updated 2025-07-23
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import json
import os
from datetime import datetime
from typing import Dict, Any

from loguru import logger

from eko.db import get_bo_conn


class CrewMemoryManager:
    """
    Memory manager for the web crawling crew.

    This class provides persistence for the web crawling crew, allowing it to
    save and load information between sessions and track the progress of
    the research. Data is persisted both in the file system and in the database
    for reliability and easy querying.
    """

    def __init__(self, company_name: str, session_id: str):
        """
        Initialize the memory manager.

        Args:
            company_name: The name of the target company
            session_id: The session ID for persistence
        """
        self.company_name = company_name
        self.session_id = session_id
        self.memory_dir = os.path.join(os.getcwd(), "var", "crawl_memory")
        
        # Create the memory directory if it doesn't exist
        os.makedirs(self.memory_dir, exist_ok=True)
        
        # Initialize the memory file
        self.memory_file = os.path.join(self.memory_dir, f"{self.session_id}.json")
        
        # Initialize the memory if it doesn't exist in DB or file
        if not os.path.exists(self.memory_file) or not self._db_session_exists():
            self._initialize_memory()
        else:
            # Sync file-based memory with DB
            self._sync_memory_from_db()
        
        logger.info(f"Initialized CrewMemoryManager for {company_name} with session ID {session_id}")
    
    def _db_session_exists(self) -> bool:
        """
        Check if a session exists in the database.
        
        Returns:
            bool: True if the session exists, False otherwise
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        "SELECT 1 FROM agent_sessions WHERE session_id = %s AND company_name = %s",
                        (self.session_id, self.company_name)
                    )
                    return cur.fetchone() is not None
        except Exception as e:
            logger.error(f"Error checking if session exists in database: {e}")
            return False
    
    def _initialize_memory(self):
        """Initialize the memory file and database entries with default values."""
        initial_memory = {
            "company_name": self.company_name,
            "session_id": self.session_id,
            "status": "initialized",
            "visited_urls": [],
            "downloaded_files": [],
            "insights": [],
            "tasks_completed": [],
            "last_position": {
                "task_index": 0,
                "url_index": 0,
                "search_queries": [],
                "search_query_index": 0
            },
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Save to file
        self._save_memory(initial_memory)
        
        # Save to database
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_sessions (session_id, company_name, memory_data, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s)
                        ON CONFLICT (session_id) 
                        DO UPDATE SET 
                            memory_data = %s,
                            updated_at = %s
                        """,
                        (
                            self.session_id, 
                            self.company_name, 
                            json.dumps(initial_memory),
                            datetime.now(),
                            datetime.now(),
                            json.dumps(initial_memory),
                            datetime.now()
                        )
                    )
                    conn.commit()
        except Exception as e:
            logger.error(f"Error initializing memory in database: {e}")
        
        logger.info(f"Initialized memory for session {self.session_id}")
    
    def _save_memory(self, memory: Dict[str, Any]):
        """
        Save the memory to the file system.
        
        Args:
            memory: The memory to save
        """
        with open(self.memory_file, "w") as f:
            json.dump(memory, f, indent=2)
    
    def _load_memory(self) -> Dict[str, Any]:
        """
        Load the memory from the file system.
        
        Returns:
            Dict[str, Any]: The loaded memory
        """
        try:
            with open(self.memory_file, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading memory file: {e}")
            # Create a new memory file if there's an error
            self._initialize_memory()
            return self._load_memory()
    
    def _sync_memory_from_db(self):
        """Sync memory from database to file system."""
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        "SELECT memory_data FROM agent_sessions WHERE session_id = %s",
                        (self.session_id,)
                    )
                    result = cur.fetchone()
                    if result and result[0]:
                        db_memory = result[0]
                        self._save_memory(db_memory)
                        logger.info(f"Synced memory from database for session {self.session_id}")
                    else:
                        # If no data in DB, initialize
                        self._initialize_memory()
        except Exception as e:
            logger.error(f"Error syncing memory from database: {e}")
            # Fall back to file-based memory if available
            if os.path.exists(self.memory_file):
                logger.info(f"Using existing file-based memory for session {self.session_id}")
            else:
                self._initialize_memory()
    
    def _sync_memory_to_db(self, memory: Dict[str, Any]):
        """
        Sync memory from file system to database.
        
        Args:
            memory: The memory to sync
        """
        try:
            memory["updated_at"] = datetime.now().isoformat()
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE agent_sessions 
                        SET memory_data = %s, updated_at = %s
                        WHERE session_id = %s
                        """,
                        (json.dumps(memory), datetime.now(), self.session_id)
                    )
                    conn.commit()
                    logger.debug(f"Synced memory to database for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error syncing memory to database: {e}")
    
    def save_final_result(self, result: Dict[str, Any]):
        """
        Save the final result of the research.
        
        Args:
            result: The result of the research
        """
        memory = self._load_memory()
        memory["result"] = result
        memory["status"] = "completed"
        self._save_memory(memory)
        self._sync_memory_to_db(memory)
        
        # Also save to agent_reports table
        try:
            insights_count = len(memory.get("insights", []))
            visited_urls_count = len(memory.get("visited_urls", []))
            downloaded_files_count = len(memory.get("downloaded_files", []))
            summary = result.get("summary", "")
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_reports 
                        (session_id, company_name, summary, insights_count, 
                         visited_urls_count, downloaded_files_count, report_data, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (session_id) 
                        DO UPDATE SET 
                            summary = %s,
                            insights_count = %s,
                            visited_urls_count = %s,
                            downloaded_files_count = %s,
                            report_data = %s
                        """,
                        (
                            self.session_id, 
                            self.company_name,
                            summary,
                            insights_count,
                            visited_urls_count,
                            downloaded_files_count,
                            json.dumps(result),
                            datetime.now(),
                            summary,
                            insights_count,
                            visited_urls_count,
                            downloaded_files_count,
                            json.dumps(result)
                        )
                    )
                    conn.commit()
        except Exception as e:
            logger.error(f"Error saving report to database: {e}")
        
        logger.info(f"Saved final result for session {self.session_id}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the research.
        
        Returns:
            Dict[str, Any]: The current status
        """
        return self._load_memory()
    
    def track_visited_url(self, url: str):
        """
        Track a visited URL.
        
        Args:
            url: The URL that was visited
        """
        memory = self._load_memory()
        if url not in memory["visited_urls"]:
            memory["visited_urls"].append(url)
            memory["last_position"]["url_index"] = len(memory["visited_urls"]) - 1
            self._save_memory(memory)
            self._sync_memory_to_db(memory)
            logger.debug(f"Tracked visited URL: {url}")
    
    def track_downloaded_file(self, file_path: str, source_url: str):
        """
        Track a downloaded file.
        
        Args:
            file_path: The path to the downloaded file
            source_url: The URL the file was downloaded from
        """
        memory = self._load_memory()
        file_info = {"path": file_path, "source_url": source_url, "downloaded_at": datetime.now().isoformat()}
        if file_info not in memory["downloaded_files"]:
            memory["downloaded_files"].append(file_info)
            self._save_memory(memory)
            self._sync_memory_to_db(memory)
            logger.debug(f"Tracked downloaded file: {file_path}")
    
    def track_insight(self, insight: Dict[str, Any]):
        """
        Track an insight discovered during research.
        
        Args:
            insight: Information about the insight
        """
        memory = self._load_memory()
        # Check if this insight already exists (by description)
        existing_insights = [i.get("description") for i in memory["insights"]]
        if insight.get("description") not in existing_insights:
            insight["discovered_at"] = datetime.now().isoformat()
            memory["insights"].append(insight)
            self._save_memory(memory)
            self._sync_memory_to_db(memory)
            
            # Also save to agent_insights table
            try:
                category = insight.get("category", "unknown")
                behavior_type = insight.get("behavior_type", "unknown")
                description = insight.get("description", "")
                source_url = insight.get("source_url", "")
                source_title = insight.get("source_title", "")
                confidence = insight.get("confidence", 0.0)
                metadata = insight.get("metadata", {})
                
                with get_bo_conn() as conn:
                    with conn.cursor() as cur:
                        cur.execute(
                            """
                            INSERT INTO agent_insights 
                            (session_id, company_name, category, behavior_type, description, 
                             source_url, source_title, confidence, metadata, extracted_at, created_at)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """,
                            (
                                self.session_id, 
                                self.company_name,
                                category,
                                behavior_type,
                                description,
                                source_url,
                                source_title,
                                confidence,
                                json.dumps(metadata),
                                datetime.now(),
                                datetime.now()
                            )
                        )
                        conn.commit()
            except Exception as e:
                logger.error(f"Error saving insight to database: {e}")
            
            logger.debug(f"Tracked insight: {insight.get('description', '')[:50]}...")
    
    def track_task_completion(self, task_name: str, task_result: Dict[str, Any], task_index: int):
        """
        Track the completion of a task.
        
        Args:
            task_name: The name of the task
            task_result: The result of the task
            task_index: The index of the task in the task list
        """
        memory = self._load_memory()
        task_completion = {
            "task_name": task_name,
            "task_result": task_result,
            "task_index": task_index,
            "completed_at": datetime.now().isoformat()
        }
        memory["tasks_completed"].append(task_completion)
        memory["last_position"]["task_index"] = task_index
        self._save_memory(memory)
        self._sync_memory_to_db(memory)
        logger.debug(f"Tracked task completion: {task_name}")
    
    def track_search_query(self, query: str):
        """
        Track a search query.
        
        Args:
            query: The search query
        """
        memory = self._load_memory()
        if query not in memory["last_position"]["search_queries"]:
            memory["last_position"]["search_queries"].append(query)
            memory["last_position"]["search_query_index"] = len(memory["last_position"]["search_queries"]) - 1
            self._save_memory(memory)
            self._sync_memory_to_db(memory)
            logger.debug(f"Tracked search query: {query}")
    
    def get_crawl_progress_summary(self) -> str:
        """
        Get a summary of the crawl progress for the agent.
        
        Returns:
            str: A summary of the crawl progress
        """
        memory = self._load_memory()
        
        visited_urls_count = len(memory.get("visited_urls", []))
        downloaded_files_count = len(memory.get("downloaded_files", []))
        insights_count = len(memory.get("insights", []))
        tasks_completed_count = len(memory.get("tasks_completed", []))
        
        # Last positions
        current_task_index = memory.get("last_position", {}).get("task_index", 0)
        current_url_index = memory.get("last_position", {}).get("url_index", 0)
        search_queries = memory.get("last_position", {}).get("search_queries", [])
        
        # Recent URLs (last 20)
        recent_urls = memory.get("visited_urls", [])[-20:] if visited_urls_count > 0 else []
        
        # Top categories of insights
        categories = {}
        for insight in memory.get("insights", []):
            category = insight.get("category", "unknown")
            categories[category] = categories.get(category, 0) + 1
        
        top_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Format the summary
        summary = f"Crawl Progress for {self.company_name} (Session: {self.session_id})\n\n"
        summary += f"Progress Summary:\n"
        summary += f"- Visited URLs: {visited_urls_count}\n"
        summary += f"- Downloaded files: {downloaded_files_count}\n"
        summary += f"- Insights discovered: {insights_count}\n"
        summary += f"- Tasks completed: {tasks_completed_count}\n\n"
        
        summary += f"Current Position:\n"
        summary += f"- Current task index: {current_task_index}\n"
        summary += f"- Current URL index: {current_url_index}\n"
        
        if recent_urls:
            summary += f"\nRecent URLs visited:\n"
            for i, url in enumerate(recent_urls):
                summary += f"- {url}\n"
        
        if search_queries:
            summary += f"\nSearch queries used:\n"
            for i, query in enumerate(search_queries[-20:]):  # Show last 5 queries
                summary += f"- {query}\n"
        
        if top_categories:
            summary += f"\nTop insight categories:\n"
            for category, count in top_categories:
                summary += f"- {category}: {count} insights\n"

        summary += f"\nRecent insights:\n"
        for insight in memory.get("insights", [])[-20:]:
            summary += f"\nInsight: {json.dumps(insight)}"
        return summary
