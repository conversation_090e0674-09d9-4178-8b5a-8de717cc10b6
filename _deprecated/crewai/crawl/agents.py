"""
CrewAI Multi-Agent System for ESG Web Research and Document Discovery

This module defines specialized AI agents for automated ESG (Environmental, Social, Governance) 
web research within the EkoIntelligence platform's CrewAI-powered crawling system. It provides
a multi-agent architecture where different agents collaborate to systematically discover, analyze,
and report on corporate ESG practices, sustainability claims, and potential policy violations
through comprehensive web crawling and document analysis.

## Core Purpose
The module establishes a **multi-agent research framework** using CrewAI that orchestrates
specialized AI agents to conduct automated ESG research, corporate misconduct investigation,
and sustainability claim analysis. Each agent has a distinct role and expertise area, enabling
sophisticated collaborative research workflows that can systematically analyze corporate
ESG practices across multiple domains and document sources.

## Agent Architecture

### ResearchAgent - Comprehensive ESG Research Specialist
**Role**: "Relentless Web Research Specialist"  
**Purpose**: Primary research coordinator for comprehensive ESG document discovery

**Key Capabilities**:
- **Systematic Web Crawling**: Discovers ESG reports, sustainability documents, and regulatory filings
- **Multi-Source Research**: Searches company websites, news articles, NGO reports, and regulatory databases
- **Document Classification**: Identifies and categorizes ESG-relevant content with high precision
- **Persistence and Depth**: Employs relentless search strategies to uncover hidden or difficult-to-find documents
- **Query Generation**: Uses `generate_search_queries` tool to create targeted ESG research queries
- **Search Execution**: Leverages `search_web` tool for comprehensive web content discovery

**LLM Configuration**: Uses Gemini 2.5 Pro model (`llm`) for complex reasoning and decision-making tasks

**Integration**: Assigned comprehensive web research tools from `get_web_tools()` collection including
domain analysis, multi-database search, content fetching, and AI-powered analysis capabilities.

### JournalistAgent - Investigative ESG Reporter  
**Role**: "Journalist"  
**Purpose**: Specialized investigative agent focusing on third-party ESG reporting and criticism

**Key Capabilities**:
- **Independent Source Focus**: Exclusively targets non-corporate sources (news, NGOs, activist groups)
- **Investigative Methodology**: Employs journalistic research techniques for unbiased ESG analysis
- **Critical Analysis**: Identifies potential greenwashing, policy violations, and corporate misconduct
- **Media Source Expertise**: Specializes in news sources (The Guardian, Bloomberg) and watchdog organizations
- **Third-Party Validation**: Discovers external assessments and criticisms of corporate ESG claims

**Restrictions**: Explicitly prohibited from analyzing company-owned websites or corporate-produced content
to maintain investigative independence and avoid corporate bias in research findings.

**LLM Configuration**: Uses Gemini 2.5 Pro model (`llm`) for sophisticated investigative reasoning

### AnalysisAgent - ESG Domain Expert and Content Analyzer
**Role**: "ESG Analysis Expert"  
**Purpose**: Specialized content analysis and ESG insight extraction from discovered documents

**Key Capabilities**:
- **ESG Domain Expertise**: Deep knowledge of Environmental, Social, and Governance frameworks
- **Content Classification**: Categorizes documents by ESG themes, materiality, and relevance
- **Pattern Recognition**: Identifies trends, inconsistencies, and gaps in corporate ESG reporting  
- **Risk Assessment**: Evaluates ESG risks and opportunities based on document analysis
- **Insight Generation**: Extracts actionable ESG insights for further research or flag generation

**Methodology**: Employs evidence-based analysis with focus on material ESG factors and
regulatory compliance assessment across discovered documentation.

**LLM Configuration**: Uses Gemini 2.5 Pro model (`llm`) for complex ESG analysis and reasoning

### SummaryAgent - Data Consolidation Specialist
**Role**: "Summarize"  
**Purpose**: Efficient data consolidation and summary generation for collected research

**Key Capabilities**:
- **Multi-Format Processing**: Handles lists, CSV, JSON, and text data summarization
- **Research Synthesis**: Consolidates findings from multiple agents into coherent summaries
- **Data Abstraction**: Creates concise overviews without losing critical information
- **Format Flexibility**: Adapts summary style to match data format and research requirements

**Optimization**: Uses lightweight Gemini 2.0 Flash Lite model (`very_simple_llm`) for
efficient processing of summarization tasks without complex reasoning requirements.

### ReportAgent - Professional ESG Report Writer
**Role**: "ESG Report Writer"  
**Purpose**: Professional report generation and comprehensive research documentation

**Key Capabilities**:
- **Professional ESG Reporting**: Creates structured, stakeholder-ready ESG reports
- **Research Documentation**: Provides detailed inventory of all discovered documents and sources
- **Evidence-Based Writing**: Synthesizes research findings into accessible, well-structured reports
- **Multi-Audience Communication**: Tailors reports for various stakeholders (investors, regulators, public)
- **Comprehensive Coverage**: Ensures all aspects of research are properly documented and presented

**Quality Standards**: Emphasizes clear, concise, and balanced ESG reporting with strong
evidence-based foundation and comprehensive source documentation.

**LLM Configuration**: Uses lightweight Gemini 2.0 Flash Lite model (`very_simple_llm`) for
efficient report generation tasks.

## LLM Model Architecture

### Primary Research Models (Gemini 2.5 Pro)
Used by ResearchAgent, JournalistAgent, and AnalysisAgent for:
- **Complex Reasoning**: Strategic research decisions and methodology selection
- **Content Analysis**: Deep ESG content understanding and categorization  
- **Decision Making**: Research prioritization and source evaluation
- **Quality Assessment**: Document relevance and materiality evaluation

### Optimized Processing Models (Gemini 2.0 Flash Lite)  
Used by SummaryAgent and ReportAgent for:
- **Efficient Processing**: High-volume data summarization and report generation
- **Cost Optimization**: Reduces processing costs for routine summarization tasks
- **Speed Optimization**: Faster processing for final output generation stages

### Model Configuration Strategy
```python
# Primary research model (complex reasoning)
llm = LLM(model="gemini/gemini-2.5-pro", temperature=0.2)

# Optimized processing model (efficient execution) 
very_simple_llm = LLM(model="gemini/gemini-2.0-flash-lite", temperature=0.2)
```

## CrewAI Framework Integration

### Multi-Agent Orchestration
The module leverages CrewAI's collaborative AI framework for:
- **Agent Specialization**: Each agent has clearly defined roles and expertise areas
- **Workflow Coordination**: Agents collaborate through CrewAI's task delegation system
- **Tool Assignment**: Specialized tools are assigned based on agent roles and capabilities
- **Memory Management**: Shared memory system enables cross-agent information sharing

### Tool Integration Pattern
Agents are equipped with role-appropriate tools from the broader crawling system:
```python
# Example agent initialization with tools
Agent(
    role="Specialized Role", 
    tools=get_web_tools(memory_manager),  # or get_analyst_tools()
    llm=selected_llm_model
)
```

## System Architecture Integration

### Database Integration
The agents interact with the EkoIntelligence analytics database system:
- **Session Tracking**: `agent_sessions` table stores research session metadata
- **Insight Storage**: `agent_insights` table captures structured ESG findings  
- **Report Persistence**: `agent_reports` table stores final research outputs
- **Tool Usage Analytics**: `agent_tool_usage` table tracks performance metrics

### Memory Management
Agents utilize the `CrewMemoryManager` system for:
- **Session Persistence**: Research can resume across interruptions
- **Cross-Agent Coordination**: Shared memory enables collaborative research
- **Duplicate Prevention**: Prevents redundant processing of discovered content
- **Progress Tracking**: Monitors research completion and quality metrics

### ESG Analysis Pipeline Integration
Research outputs feed into the broader EkoIntelligence ESG analysis system:
- **Flag Generation**: Research findings contribute to ESG flag creation
- **Risk Scoring**: Document analysis informs risk assessment algorithms
- **Greenwashing Detection**: Agent findings support authenticity analysis
- **Regulatory Compliance**: Research supports compliance monitoring workflows

## Technology Stack

### Core Dependencies
- **CrewAI Framework**: Multi-agent orchestration and collaboration system
- **Google Gemini Models**: LLM providers for reasoning and content generation
- **LiteLLM**: Model abstraction layer supporting multiple LLM providers
- **Pydantic**: Type-safe data validation for agent inputs and outputs
- **PostgreSQL**: Database persistence for session and research data

### Integration Points
- **EkoIntelligence Platform**: Core analytics and ESG processing system
- **Web Research Tools**: Specialized crawling and analysis tool collection
- **Memory Management**: Persistent storage and cross-session coordination
- **Logging System**: Loguru-based structured logging for monitoring and debugging

## Usage Examples

### Basic Agent Initialization
```python
from eko.agent.crewai.crawl.agents import ResearchAgent
from eko.agent.crewai.crawl.tools_new.all_tools import get_web_tools
from eko.agent.crewai.crawl.memory import CrewMemoryManager

# Initialize memory manager
memory_manager = CrewMemoryManager("Target Company", "session_123")

# Create research agent with full web crawling toolkit
research_agent = ResearchAgent(tools=get_web_tools(memory_manager))
```

### Multi-Agent Workflow Setup
```python
from crewai import Crew, Task

# Initialize specialized agents
researcher = ResearchAgent(tools=get_web_tools(memory_manager))
journalist = JournalistAgent(tools=get_analyst_tools(memory_manager))
analyst = AnalysisAgent(tools=get_analyst_tools(memory_manager))

# Create collaborative research task
research_task = Task(
    description="Research ESG practices for target company",
    agents=[researcher, journalist, analyst]
)

# Execute multi-agent research workflow
crew = Crew(agents=[researcher, journalist, analyst], tasks=[research_task])
results = crew.kickoff()
```

## Performance and Reliability

### Error Handling
Agents implement the EkoIntelligence fail-fast philosophy:
- **Exception Propagation**: Errors bubble up immediately without silent failures
- **Explicit Logging**: All failures are logged with full context using loguru
- **Graceful Degradation**: Research continues with available tools when possible

### Resource Optimization
- **Model Selection**: Appropriate LLM models selected based on task complexity
- **Memory Efficiency**: Shared tool instances prevent resource duplication
- **Database Connection Pooling**: Efficient database access through connection management

### Monitoring and Analytics
- **Tool Usage Tracking**: Performance metrics stored in `agent_tool_usage` table
- **Session Analytics**: Research progress and outcomes tracked per session
- **Quality Metrics**: Document discovery rates and insight generation effectiveness

@see https://crewai.com/ CrewAI Multi-Agent Framework Documentation
@see https://ai.google.dev/gemini-api/docs Google Gemini API Documentation  
@see https://www.litellm.ai/ LiteLLM Model Abstraction Documentation
@see ./memory.py CrewMemoryManager for session persistence
@see ./tools_new/all_tools.py Tool factory and registry system
@see ../../../analysis_v2/ ESG Analysis Pipeline integration
@see ../../../db/data/ Database access objects and data persistence
<AUTHOR>
@updated 2025-07-23
@description Multi-agent CrewAI system for automated ESG web research and document discovery with specialized agent roles and collaborative workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from typing import List

from crewai import Agent
from crewai.tools import BaseTool
from dotenv import load_dotenv

load_dotenv()

# import json
#
# file_path = '/Users/<USER>/.config/gcloud/application_default_credentials.json'
#
# # Load the JSON file
# with open(file_path, 'r') as file:
#     vertex_credentials = json.load(file)
#
# # Convert the credentials to a JSON string
# vertex_credentials_json = json.dumps(vertex_credentials)
#
from crewai import LLM

# llm = LLM(
#     model="gemini/gemini-2.5-pro-preview-03-25",
#     temperature=0.2,
#     api_key="AIzaSyCffot1AZ2_Jgbrjbo60kLQvFnpKbK5_vE",
#     # vertex_credentials=vertex_credentials_json
# )

llm = LLM(model="gemini/gemini-2.5-pro", temperature=0.2)

# 
# llm = LLM(
#     model="gemini/gemini-2.5-flash-preview",
#     temperature=0.2,
#     api_key="AIzaSyCffot1AZ2_Jgbrjbo60kLQvFnpKbK5_vE",
#     # vertex_credentials=vertex_credentials_json
# )

simple_llm = LLM(
    model="gemini/gemini-2.5-flash",
    temperature=0.2,
    api_key="AIzaSyCffot1AZ2_Jgbrjbo60kLQvFnpKbK5_vE",
    # vertex_credentials=vertex_credentials_json
)



very_simple_llm = LLM(
    model="gemini/gemini-2.0-flash-lite",
    temperature=0.2,
    api_key="AIzaSyCffot1AZ2_Jgbrjbo60kLQvFnpKbK5_vE",
    # vertex_credentials=vertex_credentials_json
)


#This needs to be Claude as it makes key decisions.


class ResearchAgent(Agent):
    """
    Agent responsible for searching the web and gathering information about the target company.
    
    This agent focuses on finding relevant information about the company's ESG practices
    by searching the web, visiting company websites, and downloading relevant documents.
    """
    
    def __init__(self, tools: List[BaseTool]):
        """
        Initialize the research agent.
        
        Args:
            tools: List of tools available to the agent
        """
        super().__init__(
            role="Relentless Web Research Specialist",
            goal="Find and download as many reports, PDFs, articles and documents about the target company as possible, leaving no stone unturned",
            backstory=(
                "You are an elite web researcher with unparalleled skills in discovering hidden documents and reports. "
                "Your expertise lies in finding ALL available articles and documents from various sources including "
                "company websites, news articles, activist groups, NGOs, regulatory filings, sustainability reports, investor presentations, and annual reports. "
                "You are known for your relentless persistence and ability to uncover documents that others miss you specialise in finding the truth about what companies do, their impact and their behaviour. "
                "You can use the tools `generate_search_queries` to create queries and then the `search_web` tool with the supplied queries."
                "You use information you discover to further generate new search queries and explore deeper. "
                "You never give up until you've exhausted every possible avenue for finding documents. "
                "Your specialty is identifying and downloading articles, PDFs and reports that contain valuable information."
            ),
            verbose=True,
            allow_delegation=True,
            tools=tools,
            llm=llm
        )


class JournalistAgent(Agent):
    """
    Agent responsible for finding and downloading documents about the target company.
    
    This agent focuses exclusively on discovering and downloading PDFs, reports, presentations,
    and other documents related to the target company.
    """
    
    def __init__(self, tools: List[BaseTool]):
        """
        Initialize the document discovery agent.
        
        Args:
            tools: List of tools available to the agent
        """
        super().__init__(
            role="Journalist",
            goal="Discover and download every possible article, PDF, report, and presentation related to the target company from anywhere not directly owned by the company. Do not look at the company's website. Do not look at any documents by the company.",
            backstory=(
                "You are a specialized investigative journalist with an uncanny ability to find articles, PDFs and reports that others miss. "
                "You score news websites, activist groups, NGOs, and other sources for information. "
                "You're relentless in your pursuit of documents and use creative search strategies "
                "You can use the tools `generate_search_queries` to create queries and then the `search_web` tool with the supplied queries."
                "to uncover hidden resources. You always check theguardian.co.uk, bloomberg.com, and other news sources first. "
            "Then look at pressure groups and NGOs. YOU DO NOT LOOK AT THE COMPANY'S WEBSITE."
            ),
            verbose=True,
            allow_delegation=True,
            tools=tools,
            llm=llm
        )


class AnalysisAgent(Agent):
    """
    Agent responsible for analyzing the gathered information for ESG insights.
    
    This agent focuses on extracting ESG insights from the information gathered by the research agent,
    categorizing them, and assessing their relevance and importance.
    """
    
    def __init__(self, tools: List[BaseTool]):
        """
        Initialize the analysis agent.
        
        Args:
            tools: List of tools available to the agent
        """
        super().__init__(
            role="ESG Analysis Expert",
            goal="Analyze gathered information to extract meaningful ESG insights about the target company",
            backstory=(
                "You are an expert in ESG analysis with years of experience evaluating corporate "
                "sustainability practices. You have a keen eye for identifying meaningful ESG initiatives, "
                "risks, and opportunities from various sources of information. Your analysis is always "
                "balanced, evidence-based, and focused on material ESG factors. You're particularly skilled "
                "at identifying patterns across multiple documents and extracting key insights that might "
                "inform further document discovery."
            ),
            verbose=True,
            allow_delegation=True,
            tools=tools,
            llm=llm
        )



class SummaryAgent(Agent):
    """
    Agent responsible for summarizing the findings and generating a final report.

    This agent focuses on synthesizing the ESG insights extracted by the analysis agent
    into a coherent and comprehensive final report.
    """

    def __init__(self, tools: List[BaseTool]):
        """
        Initialize the summary agent.

        Args:
            tools: List of tools available to the agent
        """
        super().__init__(
            role="Summarize",
            goal="Summarize data supplied.",
            backstory=(
                "You are good at summarizing data, whether it be a list, csv, json or text."
            ),
            verbose=True,
            allow_delegation=True,
            tools=tools,
            llm=very_simple_llm
        )



class ReportAgent(Agent):
    """
    Agent responsible for summarizing the findings and generating a final report.
    
    This agent focuses on synthesizing the ESG insights extracted by the analysis agent
    into a coherent and comprehensive final report.
    """
    
    def __init__(self, tools: List[BaseTool]):
        """
        Initialize the summary agent.
        
        Args:
            tools: List of tools available to the agent
        """
        super().__init__(
            role="ESG Report Writer",
            goal="Create a comprehensive and well-structured ESG report based on the research findings",
            backstory=(
                "You are a skilled ESG report writer with experience creating clear, concise, and "
                "informative reports for various stakeholders. You excel at synthesizing complex "
                "information into accessible narratives that highlight the most important ESG aspects "
                "of a company. Your reports are always well-structured, evidence-based, and balanced. "
                "You also provide a detailed inventory of all documents discovered during the research process."
            ),
            verbose=True,
            allow_delegation=True,
            tools=tools,
            llm=very_simple_llm
        )
