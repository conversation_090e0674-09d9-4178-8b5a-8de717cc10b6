"""
CrewAI Document Tracking Tool for ESG Research Content Discovery and Management

This module provides the `TrackDocumentTool` class, a specialized CrewAI tool for managing document 
discovery and tracking during automated ESG (Environmental, Social, Governance) research workflows. 
The tool enables systematic cataloging of discovered corporate documents, sustainability reports, 
regulatory filings, and related content as part of the EkoIntelligence ESG analysis platform's 
multi-agent web crawling and research system.

## Core Purpose

The `TrackDocumentTool` serves as a **centralized document registry** within CrewAI multi-agent 
research workflows, providing:
- **Document Cataloging**: Systematic tracking of discovered ESG documents with metadata enrichment
- **Deduplication Management**: Prevention of redundant processing through URL-based document identification  
- **Session Coordination**: Cross-agent coordination via shared document tracking state
- **Metadata Preservation**: Structured capture of document titles, types, publication dates, and descriptions

## Key Functionality

### Document Registration System
- **URL-Based Identification**: Uses document URLs as primary keys for deduplication and tracking
- **Metadata Enrichment**: Captures comprehensive document information including titles, types, and descriptions
- **Update Capability**: Supports updating existing document records with enhanced metadata
- **Type Classification**: Categorizes documents by type (Annual Report, Sustainability Report, etc.)

### CrewAI Multi-Agent Integration  
The tool integrates seamlessly with CrewAI's agent framework:

#### Agent Collaboration Support:
- **Shared State Management**: Global `_discovered_documents` list enables cross-agent document tracking
- **Session Persistence**: Maintains document registry throughout multi-agent research sessions
- **Tool Coordination**: Works alongside other research tools (search, download, analysis) for comprehensive workflow
- **Memory Integration**: Supports integration with `CrewMemoryManager` for persistent document tracking

#### ESG Research Workflow Integration:
1. **Discovery Phase**: Research agents use search tools to identify relevant corporate documents
2. **Registration Phase**: `TrackDocumentTool` catalogs discovered documents with metadata
3. **Processing Phase**: Download and analysis tools reference tracked documents for content extraction
4. **Integration Phase**: Document metadata feeds ESG analysis and corporate assessment pipelines

## Key Classes and Components

### `TrackDocumentInput` (Pydantic BaseModel)
Input validation schema defining document tracking parameters:

#### Required Fields:
- **url** (str): The document URL serving as unique identifier for deduplication and access
- **title** (str): Human-readable document title or name for identification and cataloging  
- **doc_type** (str): Document classification (e.g., "Annual Report", "Sustainability Report", "10-K Filing")

#### Optional Fields:
- **publication_date** (str, optional): Document publication date if available for temporal analysis
- **description** (str, optional): Brief content description for context and relevance assessment

### `TrackDocumentTool` (CrewAI BaseTool)
Primary document tracking tool implementing CrewAI's tool interface:

#### Core Properties:
- **name**: "track_document" - Unique tool identifier for agent selection
- **description**: "Track a discovered document" - Agent-readable capability description  
- **args_schema**: Links to `TrackDocumentInput` for automatic parameter validation

#### Primary Method: `_run()` 
Executes document tracking operations with the following logic:
1. **Input Processing**: Validates and extracts document metadata from tool parameters
2. **Deduplication Check**: Searches existing tracked documents by URL to prevent duplicates
3. **Update Logic**: Updates existing document records or adds new entries as appropriate
4. **Result Confirmation**: Returns status message confirming successful tracking or update operation

## Technical Architecture

### Shared State Management
The tool leverages a global document tracking system:
- **Global Registry**: `_discovered_documents` list from `base.py` provides centralized document storage
- **Thread Safety**: Single-threaded CrewAI execution model ensures consistent state access
- **Memory Efficiency**: In-memory list structure optimized for research session duration
- **Persistence Support**: Designed for integration with persistent storage systems via `CrewMemoryManager`

### Document Data Structure
Each tracked document is stored as a dictionary with standardized keys:
```python
{
    "url": "https://example.com/sustainability-report-2023.pdf",
    "title": "2023 Sustainability Report", 
    "doc_type": "Sustainability Report",
    "publication_date": "2023-12-31",  # Optional
    "description": "Annual sustainability performance report"  # Optional
}
```

### Error Handling and Logging
The tool implements comprehensive error management:
- **Exception Handling**: Graceful failure management with detailed error logging
- **Loguru Integration**: Structured logging for debugging and monitoring research workflows
- **Status Reporting**: Clear feedback to agents on successful tracking or error conditions
- **Fallback Responses**: Meaningful error messages enable agent decision-making during failures

## System Architecture Integration

### EkoIntelligence Platform Context
The tool supports the broader ESG analysis ecosystem:
- **Research Orchestration**: Part of multi-agent research system for automated ESG content discovery
- **Data Pipeline Integration**: Tracked documents feed downstream ESG analysis and scoring algorithms
- **Database Integration**: Document metadata can be persisted to analytics database for long-term analysis
- **Session Management**: Integrates with agent monitoring and session tracking systems

### CrewAI Framework Integration
Designed for optimal integration with CrewAI workflows:
- **Tool Chaining**: Works in sequence with search, download, and analysis tools
- **Agent Assignment**: Can be assigned to specialized document management or coordination agents
- **Session Workflows**: Supports both single-session research and persistent multi-session projects
- **Memory Coordination**: Compatible with CrewAI memory systems and custom memory managers

## Usage Patterns

### Typical Research Workflow Integration:
1. **Search Tools** discover relevant corporate ESG documents via web search or database queries
2. **TrackDocumentTool** catalogs discovered documents with metadata and deduplication
3. **Download Tools** reference tracked documents for content extraction and processing
4. **Analysis Tools** process document content with full metadata context for ESG assessment

### Agent Coordination Scenarios:
- **Research Agent**: Discovers documents and uses tracking tool to catalog findings
- **Download Agent**: References tracked documents for systematic content retrieval
- **Analysis Agent**: Accesses document registry for comprehensive ESG content analysis
- **Coordination Agent**: Manages document workflow and ensures complete research coverage

## Related Components
- **Base Module** (`base.py`): Provides shared `_discovered_documents` registry and web utilities
- **Memory Management** (`../memory.py`): `CrewMemoryManager` for persistent document tracking
- **Download Tools** (`download_pdf.py`): Tools for retrieving tracked document content
- **Analysis Tools** (analysis modules): ESG content analysis tools that process tracked documents
- **Database Schema** (analytics DB): `agent_crawl_sessions` table for session and document persistence

## Security and Validation
- **URL Validation**: Pydantic ensures valid URL formats for document identification
- **Input Sanitization**: All text fields validated and sanitized through Pydantic models
- **Type Safety**: Strong typing throughout tool implementation prevents runtime errors
- **Error Boundaries**: Exception handling prevents tool failures from disrupting agent workflows

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation  
@see https://docs.pydantic.dev/ Pydantic Data Validation Library
@see https://loguru.readthedocs.io/ Loguru Structured Logging Framework
@see ../base.py Shared Resources and Global Document Registry
@see ../memory.py CrewAI Memory Management for Persistent Document Tracking
@see ../../crawler.py Main Crawler Orchestration for Multi-Agent ESG Research
<AUTHOR>
@updated 2025-07-22  
@description CrewAI tool for tracking and managing discovered ESG documents during multi-agent research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from typing import Type, Optional

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from .base import _discovered_documents


class TrackDocumentInput(BaseModel):
    """Input schema for the track_document tool."""
    url: str = Field(..., description="The URL of the document")
    title: str = Field(..., description="The title or name of the document")
    doc_type: str = Field(..., description="The type of document (e.g., Annual Report, Sustainability Report)")
    publication_date: Optional[str] = Field(default=None, description="The publication date of the document (if known)")
    description: Optional[str] = Field(default=None, description="A brief description of the document content")


class TrackDocumentTool(BaseTool):
    """Tool for tracking discovered documents."""

    name: str = "track_document"
    description: str = "Track a discovered document"
    args_schema: Type[BaseModel] = TrackDocumentInput

    def _run(self, url: str, title: str, doc_type: str, publication_date: Optional[str] = None,
            description: Optional[str] = None) -> str:
        """
        Track a discovered document.

        Args:
            url: The URL of the document
            title: The title or name of the document
            doc_type: The type of document (e.g., Annual Report, Sustainability Report)
            publication_date: The publication date of the document (if known)
            description: A brief description of the document content

        Returns:
            str: Confirmation message
        """
        try:
            # Create document info
            doc_info = {
                "url": url,
                "title": title,
                "doc_type": doc_type,
                "publication_date": publication_date,
                "description": description
            }

            # Check if document already exists
            for existing_doc in _discovered_documents:
                if existing_doc["url"] == url:
                    # Update existing document
                    existing_doc.update(doc_info)
                    return f"Updated document in tracker: {title} ({doc_type})"

            # Add new document
            _discovered_documents.append(doc_info)

            return f"Added document to tracker: {title} ({doc_type})"
        except Exception as e:
            logger.exception(f"Error tracking document: {e}")
            return f"Error tracking document: {str(e)}"
