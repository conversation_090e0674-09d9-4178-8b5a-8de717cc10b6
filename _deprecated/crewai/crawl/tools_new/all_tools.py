"""
CrewAI Web Crawler Tool Registry and Factory Module

This module serves as the central factory and registry for specialized web crawling tools within 
the EkoIntelligence ESG analysis platform's AI-powered research system. It provides two main 
tool collections for different agent types in the CrewAI multi-agent web crawling workflow:
comprehensive web research tools for full crawling operations and focused analyst tools for 
targeted research activities.

## Core Purpose
The module acts as a **tool factory** that assembles collections of specialized CrewAI BaseTool 
subclasses for automated ESG (Environmental, Social, Governance) research, corporate misconduct 
investigation, and sustainability claim analysis. It enables AI agents to systematically crawl 
corporate websites, extract relevant ESG documentation, and identify potential greenwashing or 
policy violations through structured web research.

## Key Functions

### `get_web_tools(memory_manager: CrewMemoryManager) -> List[BaseTool]`
Returns a comprehensive toolkit for **web crawling agents** performing extensive ESG research. 
This collection includes 13 specialized tools plus memory management capabilities:

**Search and Discovery Tools:**
- **GetWebDomainInfoTool()**: Domain analysis and website structure discovery
- **SearchWebTool()**: General web search for ESG-related content and controversies  
- **SearchCHTool()**: Companies House (UK) regulatory filings and corporate data search
- **SearchGLEIFTool()**: Global Legal Entity Identifier Foundation legal entity verification
- **SearchSECTool()**: US Securities and Exchange Commission filings and enforcement actions
- **SearchWikipediaTool()**: Corporate background research and controversy identification

**Content Processing Tools:**
- **FetchWebpageTool(memory_manager)**: Advanced webpage fetching with deduplication and cleaning
- **DownloadTool(memory_manager)**: PDF and document download with persistent tracking
- **ExtractLinksTool(memory_manager)**: URL extraction and relationship mapping from webpages

**AI-Powered Analysis Tools:**
- **GenerateSearchQueriesTool()**: Intelligent search query generation for targeted research
- **ExtractESGInsightsTool(memory_manager)**: ESG-specific content extraction and classification
- **AnalyzeTextForRelevanceTool(memory_manager)**: Relevance scoring for discovered content
- **SummarizeContentTool(memory_manager)**: Content summarization for insight generation

**Memory Integration:**
All memory-enabled tools integrate with `CrewMemoryManager` for persistent session tracking,
URL deduplication, insight accumulation, and progress coordination across agent executions.

### `get_analyst_tools(memory_manager: CrewMemoryManager) -> List[BaseTool]`
Returns a **focused toolkit for analyst agents** performing targeted research and analysis.
This streamlined collection excludes domain analysis tools and includes the same search,
content processing, and AI analysis capabilities as the web tools, optimized for analytical
workflows rather than broad discovery operations.

## CrewAI Framework Integration
The module integrates deeply with the CrewAI multi-agent framework:
- **BaseTool Compliance**: All tools inherit from `crewai.tools.BaseTool` with proper schemas
- **Memory Management**: Tools use `CrewMemoryManager` for session persistence and coordination  
- **Agent Assignment**: Tool collections are assigned to CrewAI Agent instances for specialized roles
- **Workflow Orchestration**: Tools enable complex multi-step research workflows across agent teams

## Memory Architecture  
The module leverages a sophisticated dual-persistence memory system:
- **File-Based Storage**: JSON files in `var/crawl_memory/` for rapid access and debugging
- **Database Integration**: PostgreSQL tables (`agent_sessions`, `agent_insights`, `agent_reports`)
- **Session Tracking**: Company-specific sessions with progress monitoring and result aggregation
- **Cross-Agent Coordination**: Shared memory enables multiple agents to collaborate on research

## ESG Analysis Workflow Integration
Tools support the complete ESG analysis pipeline:
1. **Discovery Phase**: Search tools identify corporate ESG documents and controversies
2. **Collection Phase**: Fetch and download tools gather relevant content systematically
3. **Processing Phase**: AI tools extract insights, analyze relevance, and summarize findings  
4. **Memory Phase**: Progress tracking and insight storage enable long-running research sessions
5. **Analysis Phase**: Results feed into ESG scoring, greenwashing detection, and risk assessment

## Database Schema Dependencies
The module interfaces with several analytics database tables:
- **agent_sessions**: Session metadata and JSON memory persistence
- **agent_insights**: Structured ESG insight storage with categorization and confidence
- **agent_reports**: Final research reports with statistics and aggregated findings
- **agent_tool_usage**: Tool usage analytics for performance monitoring and optimization

## Performance and Reliability Features
- **Lazy Tool Instantiation**: Tools created on-demand for memory efficiency
- **Connection Pooling**: Database operations use `eko.db.get_bo_conn()` connection management
- **Error Recovery**: Tools handle network failures and API rate limits gracefully
- **Duplicate Detection**: Memory system prevents redundant crawling of visited URLs
- **Progress Persistence**: Research can resume from interruption points across sessions

## Technology Stack
- **CrewAI Framework**: Multi-agent orchestration and tool management system
- **PostgreSQL**: Persistent storage for research data and session management  
- **Pydantic**: Type-safe data validation and serialization for tool inputs/outputs
- **Loguru**: Structured logging for debugging and monitoring agent activities
- **EkoIntelligence Platform**: ESG analysis pipeline and database integration layer

## Usage Pattern
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_new.all_tools import get_web_tools, get_analyst_tools

# Initialize memory manager for persistent research session
memory_manager = CrewMemoryManager("ACME Corporation", "research_session_001")

# Create comprehensive web crawling tools
web_tools = get_web_tools(memory_manager)

# Create focused analyst tools  
analyst_tools = get_analyst_tools(memory_manager)

# Assign to CrewAI agents for different research roles
web_crawler = Agent(
    role="ESG Web Researcher", 
    goal="Discover corporate ESG documents and controversies",
    tools=web_tools,
    memory=True
)

analyst = Agent(
    role="ESG Content Analyst",
    goal="Analyze discovered content for ESG insights", 
    tools=analyst_tools,
    memory=True
)
```

## System Architecture Context
This module is a critical component of the EkoIntelligence ESG analysis system:
- **Analytics Backend**: Python orchestration layer for automated ESG research
- **CrewAI Integration**: Multi-agent web crawling for corporate sustainability analysis
- **Data Pipeline**: Research findings feed ESG scoring and greenwashing detection algorithms
- **Database Layer**: Results stored in analytics database and synced to customer systems
- **Frontend Integration**: Research insights displayed in customer dashboards via `xfer_` tables

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/memory CrewAI Memory System
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository  
@see ../memory.py CrewMemoryManager Implementation
@see ../tools_mem/ Memory Tool Implementations
@see ../../analysis_v2/ ESG Analysis Pipeline
<AUTHOR>
@updated 2025-07-22
@description Central factory for CrewAI web crawling tools used in ESG research and corporate misconduct investigation
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from .analyze_text_for_relevance import AnalyzeTextForRelevanceTool
from .download_pdf import DownloadTool
from .extract_esg_insights import ExtractESGInsightsTool
from .extract_links import ExtractLinksTool
from .fetch_webpage import FetchWebpageTool
from .generate_search_queries import GenerateSearchQueriesTool
from .get_web_domain_info import GetWebDomainInfoTool
from .search_ch import SearchCHTool
from .search_gleif import SearchGLEIFTool
from .search_sec import SearchSECTool
from .search_web import SearchWebTool
from .search_wikipedia import SearchWikipediaTool
from .summarize_content import SummarizeContentTool
from ..memory import CrewMemoryManager
from ..tools_mem import get_memory_tools


def get_web_tools(memory_manager: CrewMemoryManager):
    """
    Get the tools available to the agents in the web crawling crew.

    Returns:
        List: The tools as BaseTool subclasses
    """
    return [
        # SpiderTool(),
        GetWebDomainInfoTool(),
        SearchWebTool(),
        # NewsSearchTool(),
        SearchCHTool(),
        SearchGLEIFTool(),
        SearchSECTool(),
        SearchWikipediaTool(),
        FetchWebpageTool(memory_manager=memory_manager),
        DownloadTool(memory_manager=memory_manager),
        ExtractLinksTool(memory_manager=memory_manager),
        # TrackDocumentTool(),
        # ListDiscoveredDocumentsTool(),
        GenerateSearchQueriesTool(),
        ExtractESGInsightsTool(memory_manager=memory_manager),
        AnalyzeTextForRelevanceTool(memory_manager=memory_manager),
        SummarizeContentTool(memory_manager=memory_manager),
    ] + get_memory_tools(memory_manager)


def get_analyst_tools(memory_manager: CrewMemoryManager):
    """
    Get the tools available to analyst agents

    Returns:
        List: The tools as BaseTool subclasses
    """
    return [
        SearchWebTool(),
        SearchCHTool(),
        SearchGLEIFTool(),
        SearchSECTool(),
        SearchWikipediaTool(),
        FetchWebpageTool(memory_manager=memory_manager),
        DownloadTool(memory_manager=memory_manager),
        ExtractLinksTool(memory_manager=memory_manager),
        GenerateSearchQueriesTool(),
        ExtractESGInsightsTool(memory_manager=memory_manager),
        AnalyzeTextForRelevanceTool(memory_manager=memory_manager),
        SummarizeContentTool(memory_manager=memory_manager)
    ] + get_memory_tools(memory_manager)
