# CrewAI Web Crawler ESG Research Tools Module

## Overview

The `tools_new` module provides a comprehensive ecosystem of specialized CrewAI tools for automated ESG (Environmental,
Social, Governance) research and corporate sustainability analysis within the EkoIntelligence platform. This module
serves as the core toolkit for AI-powered multi-agent web crawling workflows that systematically discover, analyze, and
extract insights from corporate ESG documents, sustainability reports, and related content across the internet.

The module implements a **factory pattern architecture** that provides two distinct tool collections optimized for
different research phases: comprehensive web crawling tools for broad discovery operations and focused analyst tools for
targeted content analysis. All tools integrate seamlessly with CrewAI's multi-agent framework and leverage sophisticated
memory management for persistent session tracking and cross-agent coordination.

**Key Capabilities:**

- **Automated ESG Discovery**: Systematic crawling of corporate websites for sustainability documents and ESG
  disclosures
- **Multi-Source Intelligence**: Integration with Companies House, GLEIF, SEC EDGAR, and other authoritative data
  sources
- **AI-Powered Analysis**: Content relevance scoring, ESG insight extraction, and greenwashing detection
- **Memory-Enabled Workflows**: Persistent session tracking with deduplication and progress coordination
- **Corporate Entity Resolution**: Advanced corporate entity identification and verification across multiple
  jurisdictions

## Specification

### Architecture Requirements

- **CrewAI Framework Integration**: All tools inherit from `crewai.tools.BaseTool` with proper Pydantic schema
  validation
- **Memory-Driven Coordination**: Dual-persistence memory system using file storage and PostgreSQL database
- **Multi-Agent Compatibility**: Tools designed for concurrent use across multiple CrewAI agents with shared session
  state
- **ESG-Specialized Processing**: Purpose-built for corporate sustainability research with domain-specific terminology
  and analysis

### Core Design Patterns

- **Factory Pattern**: Centralized tool instantiation through `get_web_tools()` and `get_analyst_tools()` functions
- **Memory Integration**: All content processing tools leverage `CrewMemoryManager` for session persistence
- **Error Resilience**: Graceful handling of network failures, API rate limits, and data availability issues
- **Incremental Discovery**: Tools build upon previous findings to create comprehensive research workflows

### Technology Stack

```mermaid
graph TB
    subgraph "CrewAI Framework"
        A[Multi-Agent Orchestration]
        B[BaseTool Integration]
        C[Memory System]
    end

    subgraph "EkoIntelligence Platform"
        D[PostgreSQL Analytics DB]
        E[Memory Management]
        F[ESG Analysis Pipeline]
    end

    subgraph "External APIs"
        G[Google Custom Search]
        H[Companies House API]
        I[GLEIF API]
        J[SEC EDGAR API]
        K[NewsAPI]
        L[Wikipedia MediaWiki]
    end

    A --> D
    B --> E
    C --> F
    G --> A
    H --> A
    I --> A
    J --> A
    K --> A
    L --> A
```

## Key Components

### Tool Factory (`all_tools.py`)

Central factory providing two main tool collections:

| Function                                | Purpose                        | Tool Count        | Target Use Case                                      |
|-----------------------------------------|--------------------------------|-------------------|------------------------------------------------------|
| **`get_web_tools(memory_manager)`**     | Comprehensive ESG web crawling | 13 tools + memory | Full-scale corporate research and document discovery |
| **`get_analyst_tools(memory_manager)`** | Focused content analysis       | 12 tools + memory | Targeted insight extraction and content processing   |

**Core Difference:** Web tools include `GetWebDomainInfoTool` for initial domain analysis, while analyst tools focus
purely on content processing and analysis.

### Search and Discovery Tools

| Tool                       | API Integration      | Primary Function                                     |
|----------------------------|----------------------|------------------------------------------------------|
| **`SearchWebTool`**        | Google Custom Search | General web search for ESG content and controversies |
| **`SearchCHTool`**         | Companies House API  | UK corporate filings and regulatory data             |
| **`SearchGLEIFTool`**      | GLEIF API            | Global Legal Entity Identifier verification          |
| **`SearchSECTool`**        | SEC EDGAR            | US securities filings and enforcement actions        |
| **`SearchWikipediaTool`**  | MediaWiki API        | Corporate background and controversy research        |
| **`GetWebDomainInfoTool`** | Custom Analysis      | Website structure and domain intelligence            |

### Content Processing Tools

| Tool                   | Memory Integration | Core Capability                                            |
|------------------------|--------------------|------------------------------------------------------------|
| **`FetchWebpageTool`** | ✅ Session tracking | Advanced webpage fetching with content cleaning            |
| **`DownloadTool`**     | ✅ Deduplication    | PDF and document download with persistent tracking         |
| **`ExtractLinksTool`** | ✅ Priority mapping | URL extraction with categorization (PDF, reports, general) |
| **`NewsSearchTool`**   | ✅ Source tracking  | ESG-focused news discovery and monitoring                  |

### AI-Powered Analysis Tools

| Tool                              | ESG Specialization                             | Output Format                                    |
|-----------------------------------|------------------------------------------------|--------------------------------------------------|
| **`ExtractESGInsightsTool`**      | Environmental/Social/Governance categorization | Structured insights with confidence scoring      |
| **`AnalyzeTextForRelevanceTool`** | Corporate misconduct detection                 | 0-10 relevance scoring with duplicate prevention |
| **`SummarizeContentTool`**        | ESG-focused summarization                      | Configurable length extractive summaries         |
| **`GenerateSearchQueriesTool`**   | Dynamic query generation                       | Context-aware search strategy development        |

### Memory Management Integration

All content processing tools integrate with the **memory tools ecosystem** from `../tools_mem/`:

```mermaid
graph LR
    subgraph "Content Tools"
        A[FetchWebpageTool]
        B[DownloadTool]
        C[ExtractLinksTool]
        D[AI Analysis Tools]
    end

    subgraph "Memory Tools"
        E[TrackURLTool]
        F[TrackFileTool]
        G[TrackInsightTool]
        H[GetProgressTool]
    end

    A --> E
    B --> F
    C --> E
    D --> G
    E --> H
    F --> H
    G --> H
```

### ESG Keyword Framework (`__init__.py`)

Comprehensive keyword system for corporate misconduct detection:

```python
# 200+ terms across categories
wrong_doing = [
    "accusation", "advertising standards", "violation", "false advertising",
    "investigation", "lawsuit", "malpractice", "misconduct", "scandal"
]

environmental_terms = [
    "carbon emissions", "environmental impact", "sustainability report",
    "renewable energy", "waste management", "water usage"
]

social_terms = [
    "labor practices", "human rights", "diversity inclusion",
    "community impact", "employee wellbeing", "supply chain ethics"
]

governance_terms = [
    "board diversity", "executive compensation", "transparency",
    "anti-corruption", "risk management", "shareholder rights"
]
```

## Dependencies

### CrewAI Framework

- **Core Framework**: `crewai` - Multi-agent orchestration and tool management
- **Tool Base Class**: `crewai.tools.BaseTool` - Foundation for all custom tools
- **Memory System**: Integration with CrewAI's built-in memory capabilities
- **Agent Integration**: Seamless assignment to CrewAI `Agent` instances
- **Workflow Orchestration**: Support for complex multi-step research workflows

### Database Technologies

- **PostgreSQL**: Primary persistence layer for session data and insights
    - `agent_sessions`: Session metadata with JSONB memory data
    - `agent_insights`: Structured ESG insights with categorization
    - `agent_reports`: Aggregated research findings and statistics
    - Connection management via `eko.db.get_bo_conn()`

### External APIs and Services

- **Google Custom Search API**: Web search functionality with ESG-focused queries
- **Companies House API**: UK corporate registry data and regulatory filings
- **GLEIF API**: Global Legal Entity Identifier database access
- **SEC EDGAR API**: US securities filings and enforcement action data
- **NewsAPI**: Real-time news monitoring for ESG-related developments
- **Wikipedia MediaWiki API**: Corporate background research and controversy identification

### Supporting Libraries

- **Pydantic**: Type-safe data validation and serialization for all tool inputs/outputs
- **Loguru**: Comprehensive logging for debugging and monitoring agent activities
- **Requests**: HTTP client for API integrations and webpage fetching
- **BeautifulSoup**: HTML parsing and content extraction from web pages
- **JSON**: File-based memory storage for rapid access and debugging capabilities

### EkoIntelligence Platform Integration

- **Memory Manager**: `eko.agent.crewai.crawl.memory.CrewMemoryManager` for session coordination
- **Database Layer**: `eko.db.get_bo_conn()` for connection pooling and transaction management
- **ESG Analysis Pipeline**: Integration with `eko.analysis_v2` for downstream processing
- **Data Sync Layer**: Results flow to customer database via `xfer_` tables

## Usage Examples

### Basic Multi-Agent ESG Research Workflow

```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_new.all_tools import get_web_tools, get_analyst_tools
from crewai import Agent, Crew, Task, Process

# Initialize memory manager for Tesla ESG research
memory_manager = CrewMemoryManager("Tesla Inc", "esg_research_2024_q3")

# Create specialized agents with appropriate tool sets
web_crawler = Agent(
    role="ESG Web Research Specialist",
    goal="Systematically discover Tesla's ESG documents and sustainability reports",
    backstory="Expert at navigating corporate websites and identifying relevant ESG content",
    tools=get_web_tools(memory_manager),
    memory=True,
    verbose=True
)

content_analyst = Agent(
    role="ESG Content Analyst",
    goal="Extract actionable insights from discovered ESG documents",
    backstory="Specialist in analyzing corporate sustainability claims and identifying greenwashing",
    tools=get_analyst_tools(memory_manager),
    memory=True,
    verbose=True
)

# Define coordinated research tasks
discovery_task = Task(
    description="Search for Tesla's latest sustainability reports, environmental impact assessments, and ESG-related news from the past 12 months",
    agent=web_crawler,
    expected_output="Comprehensive list of Tesla ESG documents with source URLs and relevance scores"
)

analysis_task = Task(
    description="Analyze discovered Tesla documents for specific ESG insights, focusing on carbon reduction claims, labor practices, and governance transparency",
    agent=content_analyst,
    expected_output="Structured ESG insights with confidence scores and evidence citations",
    context=[discovery_task]  # Depends on discovery results
)

# Execute coordinated research workflow
esg_crew = Crew(
    agents=[web_crawler, content_analyst],
    tasks=[discovery_task, analysis_task],
    process=Process.sequential,
    memory=True,
    verbose=True
)

results = esg_crew.kickoff()
```

### Targeted Corporate Investigation

```python
# Initialize investigation for specific corporate entity
memory_manager = CrewMemoryManager("Acme Corporation", "investigation_2024_07")

# Create investigation-focused agent
investigator = Agent(
    role="Corporate Investigation Specialist",
    goal="Investigate potential ESG violations and regulatory non-compliance",
    backstory="Expert in corporate misconduct detection with access to regulatory databases",
    tools=get_web_tools(memory_manager),
    memory=True
)

investigation_task = Task(
    description="""
    Investigate Acme Corporation for:
    1. Recent regulatory violations or fines related to environmental compliance
    2. Labor practice controversies or worker safety incidents  
    3. Governance issues including executive compensation or board diversity
    4. Any greenwashing allegations or sustainability claim disputes
    
    Search Companies House, SEC filings, news sources, and corporate websites.
    """,
    agent=investigator,
    expected_output="Detailed investigation report with evidence and source citations"
)

# Single-agent focused investigation
investigation_crew = Crew(
    agents=[investigator],
    tasks=[investigation_task],
    verbose=True
)

investigation_results = investigation_crew.kickoff()
```

### Progress Monitoring and Session Management

```python
from eko.agent.crewai.crawl.tools_mem import get_memory_tools

# Access memory tools for monitoring
memory_tools = get_memory_tools(memory_manager)
progress_tool = memory_tools[4]  # GetProgressTool

# Check current research session progress
progress_report = progress_tool._run()
print(f"Session Progress:\n{progress_report}")

# Example progress output:
# """
# Research Session: Tesla Inc (esg_research_2024_q3)
# URLs Visited: 47
# Files Downloaded: 12 PDFs, 8 webpages
# ESG Insights: 23 Environmental, 15 Social, 18 Governance
# Recent Activity: Downloaded sustainability-report-2024.pdf from tesla.com
# Session Duration: 2.5 hours
# """
```

## Architecture Notes

### Multi-Agent Coordination Flow

```mermaid
sequenceDiagram
    participant C as Crew Orchestrator
    participant WA as Web Agent
    participant AA as Analyst Agent
    participant MM as Memory Manager
    participant DB as PostgreSQL
    C ->> WA: Execute discovery task
    WA ->> MM: Track visited URLs
    WA ->> MM: Track downloaded files
    WA ->> DB: Sync session data
    WA ->> C: Return discovered documents
    C ->> AA: Execute analysis task
    AA ->> MM: Load discovered content
    AA ->> MM: Track ESG insights
    AA ->> DB: Store structured insights
    AA ->> C: Return analysis results
    Note over MM, DB: Continuous synchronization ensures data persistence
```

### Tool Integration Patterns

```mermaid
graph TB
    subgraph "Tool Categories"
        A[Search Tools<br/>No Memory Required]
        B[Content Tools<br/>Memory Integrated]
        C[Analysis Tools<br/>Memory + AI]
    end

    subgraph "Memory Layer"
        D[File System JSON]
        E[PostgreSQL Tables]
        F[Session Coordination]
    end

    subgraph "External Systems"
        G[APIs & Databases]
        H[AI/LLM Services]
        I[Web Content]
    end

    A --> G
    B --> D
    B --> I
    C --> E
    C --> H
    D <--> E
    E --> F
```

### Database Schema Integration

```mermaid
erDiagram
    agent_sessions {
        text session_id PK
        text company_name
        jsonb memory_data
        timestamptz created_at
        timestamptz updated_at
    }

    agent_insights {
        bigint id PK
        text session_id FK
        text company_name
        text category
        text behavior_type
        text description
        text source_url
        text source_title
        double_precision confidence
        jsonb metadata
        timestamptz extracted_at
    }

    agent_reports {
        bigint id PK
        text session_id FK
        text company_name
        jsonb report_data
        text report_type
        timestamptz created_at
    }

    agent_tool_usage {
        bigint id PK
        text session_id FK
        text tool_name
        jsonb input_data
        jsonb output_data
        integer duration_ms
        boolean success
        timestamptz executed_at
    }

    agent_sessions ||--o{ agent_insights: "generates"
    agent_sessions ||--o{ agent_reports: "produces"
    agent_sessions ||--o{ agent_tool_usage: "tracks"
```

## Known Issues

Based on code analysis and integration with EKO-279 admin pages development:

### Current Technical Limitations

- **API Rate Limiting**: Some tools may encounter rate limits with external APIs during intensive crawling sessions
- **Memory File Growth**: Long research sessions can create large JSON memory files that impact performance
- **Concurrent Access**: Potential race conditions when multiple agents update shared memory simultaneously
- **Error Recovery**: Limited recovery mechanisms for corrupted memory files or failed API responses

### Integration Challenges

- **Database Synchronization**: High-frequency tool usage may cause temporary lag between file and database storage
- **Tool Performance Monitoring**: Limited built-in analytics for tool execution times and success rates
- **Session Lifecycle Management**: No automatic cleanup of completed or abandoned research sessions

### Content Processing Issues

- **Large Document Handling**: PDFs over 50MB may cause memory issues during content extraction
- **Dynamic Content**: JavaScript-heavy websites may not be fully crawled by the current webpage fetching implementation
- **Language Detection**: Non-English ESG documents may not be properly categorized or analyzed

## Future Work

### Admin Dashboard Integration (EKO-279)

Based on the admin pages development project, the following enhancements are planned:

- **Session Monitoring**: Real-time dashboard for active CrewAI research sessions
- **Tool Usage Analytics**: Performance metrics and success rate tracking per tool
- **Memory Management**: Admin controls for session cleanup and memory optimization
- **Cost Tracking**: LLM usage and API cost monitoring per research session

### Performance Optimizations

- **Caching Layer**: Redis integration for frequently accessed memory data and API responses
- **Batch Processing**: Optimize database synchronization with batched memory updates
- **Memory Compression**: Implement compression for large session files to reduce storage impact
- **Load Balancing**: Multi-instance support for high-volume research operations

### Enhanced AI Capabilities

- **Advanced ESG Classification**: Machine learning models for more nuanced ESG insight categorization
- **Greenwashing Detection**: Specialized algorithms for identifying misleading sustainability claims
- **Trend Analysis**: Time-series analysis of corporate ESG performance and disclosure patterns
- **Multi-Language Support**: Enhanced processing for non-English ESG documents and reports

### Scalability Improvements

- **Distributed Memory**: Support for distributed memory across multiple instances
- **Session Partitioning**: Horizontal scaling through company-based data partitioning
- **API Gateway**: Centralized API management with rate limiting and retry logic
- **Archive System**: Automatic archiving of completed research sessions to cold storage

## Troubleshooting

### Common Tool Issues

#### Memory File Corruption

**Symptoms**: JSON parsing errors, tools returning empty results
**Diagnosis**:

```bash
# Validate memory file structure
python -m json.tool var/crawl_memory/session_id.json

# Check file permissions
ls -la var/crawl_memory/
```

**Solution**:

```bash
# Backup corrupted file
mv var/crawl_memory/session_id.json var/crawl_memory/session_id.json.backup

# Tools will reinitialize with empty state on next execution
# Previous insights remain in database
```

#### API Rate Limiting

**Symptoms**: HTTP 429 errors, failed search operations
**Diagnosis**:

```bash
# Check recent tool usage
cd backoffice && ./bin/run_in_db.sh "
SELECT tool_name, COUNT(*), 
       MIN(executed_at) as first_call,
       MAX(executed_at) as last_call
FROM agent_tool_usage 
WHERE executed_at > NOW() - INTERVAL '1 hour'
GROUP BY tool_name;"
```

**Solution**:

- Implement exponential backoff in tool retry logic
- Distribute API calls across multiple agents/sessions
- Configure tool-specific rate limiting in memory manager

#### Database Connection Failures

**Symptoms**: Memory synchronization errors, database timeout messages
**Diagnosis**:

```bash
# Test database connectivity
cd backoffice && ./bin/run_in_db.sh "SELECT 1"

# Check active sessions
cd backoffice && ./bin/run_in_db.sh "
SELECT session_id, company_name, updated_at 
FROM agent_sessions 
WHERE updated_at > NOW() - INTERVAL '24 hours';"
```

**Solution**:

- Tools continue with file-based memory if database unavailable
- Memory will sync automatically when connection restored
- Check PostgreSQL connection pool settings

### Performance Issues

#### Large Memory Files

**Symptoms**: Slow tool execution, high memory usage
**Diagnosis**:

```bash
# Check memory file sizes
find var/crawl_memory/ -name "*.json" -exec ls -lh {} \; | sort -k5 -hr

# Monitor database memory usage
cd backoffice && ./bin/run_in_db.sh "
SELECT session_id, 
       pg_size_pretty(length(memory_data::text)) as memory_size
FROM agent_sessions 
ORDER BY length(memory_data::text) DESC LIMIT 10;"
```

**Solution**:

```bash
# Archive large completed sessions
cd backoffice && python main.py archive-sessions --older-than-days 30

# Split long research sessions into focused sub-sessions
# Implement memory compression for active sessions
```

#### Tool Execution Timeouts

**Symptoms**: CrewAI timeouts, incomplete task execution
**Diagnosis**:

```bash
# Monitor tool performance
cd backoffice && ./bin/run_in_db.sh "
SELECT tool_name, 
       AVG(duration_ms) as avg_duration,
       MAX(duration_ms) as max_duration,
       COUNT(*) as usage_count
FROM agent_tool_usage 
WHERE executed_at > NOW() - INTERVAL '24 hours'
GROUP BY tool_name 
ORDER BY avg_duration DESC;"
```

**Solution**:

- Increase CrewAI agent timeout settings
- Implement async tool execution where possible
- Add circuit breaker pattern for unreliable external APIs

### Debug Commands

```bash
# Monitor real-time tool usage
tail -f var/logs/crewai.log | grep "Tool execution\|Memory sync\|API call"

# Check session health
cd backoffice && ./bin/run_in_db.sh "
SELECT s.session_id, s.company_name, s.updated_at,
       COUNT(i.id) as insight_count,
       COUNT(DISTINCT t.tool_name) as tools_used
FROM agent_sessions s
LEFT JOIN agent_insights i ON s.session_id = i.session_id
LEFT JOIN agent_tool_usage t ON s.session_id = t.session_id
WHERE s.updated_at > NOW() - INTERVAL '7 days'
GROUP BY s.session_id, s.company_name, s.updated_at
ORDER BY s.updated_at DESC;"

# Validate tool integration
python -c "
from eko.agent.crewai.crawl.tools_new.all_tools import get_web_tools, get_analyst_tools
from eko.agent.crewai.crawl.memory import CrewMemoryManager
mm = CrewMemoryManager('Test Company', 'debug_session')
web_tools = get_web_tools(mm)
analyst_tools = get_analyst_tools(mm)
print(f'Web tools: {len(web_tools)}, Analyst tools: {len(analyst_tools)}')
"
```

## FAQ

### User-Centric Questions and Answers

**Q: How do I start a new ESG research session for a company?**
A: Initialize a `CrewMemoryManager` with the company name and unique session ID, then use the factory functions:

```python
memory_manager = CrewMemoryManager("Company Name", "research_session_2024_q3")
web_tools = get_web_tools(memory_manager)
analyst_tools = get_analyst_tools(memory_manager)
```

**Q: Can multiple agents work on the same company research simultaneously?**
A: Yes, all agents using the same `memory_manager` instance will share session state and coordinate automatically
through the memory system. This prevents duplicate work and enables collaborative research.

**Q: How do I prevent agents from downloading the same documents multiple times?**
A: The memory system automatically handles deduplication. The `DownloadTool` checks against previously downloaded files
in the session memory before attempting new downloads.

**Q: What's the difference between web tools and analyst tools?**
A: Web tools include domain analysis capabilities for broad discovery workflows, while analyst tools focus purely on
content processing and analysis. Web tools are ideal for initial research phases, analyst tools for focused content
analysis.

**Q: How do I monitor the progress of a long-running research session?**
A: Use the `GetProgressTool` from the memory tools collection, or query the database directly:

```python
progress_tool = get_memory_tools(memory_manager)[4]
progress_report = progress_tool._run()
```

**Q: Can I customize the ESG categories and keywords?**
A: Currently, the system uses predefined ESG categories (Environmental, Social, Governance) with behavior types (
Positive, Negative, Neutral). Custom categories require modifications to the `ExtractESGInsightsTool` and keyword
definitions in `__init__.py`.

**Q: How do I handle API rate limits from external services?**
A: Tools implement basic retry logic, but for high-volume research, consider:

- Distributing work across multiple sessions
- Implementing custom rate limiting in the memory manager
- Using different API keys for different research streams

**Q: What happens if an agent crashes during research?**
A: All progress is persistently stored in both file and database systems. When agents restart, they resume from the last
known state using the stored memory. No work is lost.

**Q: How do I integrate these tools with the new admin dashboard?**
A: The tools automatically log usage to `agent_tool_usage` table and sync insights to `agent_insights`. The admin
dashboard (EKO-279) will display this data for monitoring and management.

## References

### Framework Documentation

- [CrewAI Framework Documentation](https://docs.crewai.com/)
- [CrewAI Tools Framework](https://docs.crewai.com/concepts/tools)
- [CrewAI Memory System](https://docs.crewai.com/concepts/memory)
- [CrewAI Multi-Agent Workflows](https://docs.crewai.com/concepts/agents)

### External API Documentation

- [Google Custom Search API](https://developers.google.com/custom-search/v1/overview)
- [Companies House API](https://developer.company-information.service.gov.uk/)
- [GLEIF API Documentation](https://www.gleif.org/en/lei-data/gleif-api)
- [SEC EDGAR API](https://www.sec.gov/edgar/sec-api-documentation)
- [NewsAPI Documentation](https://newsapi.org/docs)
- [Wikipedia MediaWiki API](https://www.mediawiki.org/wiki/API:Main_page)

### Related Code Files

- [`memory.py`](../memory.py) - CrewMemoryManager Implementation
- [`tools_mem/`](../tools_mem/) - Memory Tools Collection
- [`all_tools.py`](./all_tools.py) - Tool Factory Implementation
- [`base.py`](./base.py) - Shared Resources and Global State
- [`__init__.py`](./___init__.py) - ESG Keyword Definitions

### Database Schema References

- [`eko.db`](../../../db/__init__.py) - Database Connection Management
- Analytics Database Schema: `agent_sessions`, `agent_insights`, `agent_reports`, `agent_tool_usage`
- Customer Database Integration: `xfer_` tables for data synchronization

### EkoIntelligence Platform Integration

- [`eko.analysis_v2`](../../analysis_v2/) - ESG Analysis Pipeline
- [`eko.db.sync`](../../../db/sync.py) - Database Synchronization Layer
- [Admin Dashboard Project](../PLAN.md) - CrewAI Monitoring Dashboard Specification

### Third-Party Dependencies

- [Pydantic Documentation](https://pydantic-docs.helpmanual.io/)
- [Loguru Logging Framework](https://loguru.readthedocs.io/en/stable/)
- [PostgreSQL JSONB](https://www.postgresql.org/docs/current/datatype-json.html)
- [Requests HTTP Library](https://docs.python-requests.org/en/latest/)
- [BeautifulSoup HTML Parsing](https://www.crummy.com/software/BeautifulSoup/bs4/doc/)

---

## Changelog

### 2025-07-27

- **Created comprehensive README.md** with complete module documentation
- **Added architecture diagrams** using Mermaid for tool coordination and database integration
- **Documented all 17 specialized tools** with detailed specifications and usage examples
- **Included troubleshooting section** with common issues, debug commands, and performance optimization
- **Added comprehensive FAQ section** addressing user-centric questions and implementation guidance
- **Provided complete reference links** to related documentation, APIs, and platform integration
- **Documented future work alignment** with EKO-279 admin pages development project
- **Added database schema diagrams** showing integration with analytics and customer databases

---

(c) All rights reserved ekoIntelligence 2025
