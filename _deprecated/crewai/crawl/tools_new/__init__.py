"""
CrewAI Web Crawler Keyword Definitions Module

This module defines comprehensive keyword and terminology lists for ESG (Environmental, Social, Governance) 
research and corporate misconduct analysis within the EkoIntelligence platform's web crawling system.
It serves as a core vocabulary component for AI agents performing automated ESG research, providing 
structured keyword sets for identifying potential corporate violations, greenwashing activities,
and governance failures.

## Core Purpose
The module establishes a standardized taxonomy of ESG-related terms and corporate misconduct indicators
that guide AI agents in:
- **Targeted Web Research**: Focusing crawling efforts on relevant ESG controversies and issues
- **Document Classification**: Categorizing discovered content by ESG risk factors and violation types
- **Quality Filtering**: Prioritizing high-relevance content over general corporate communications
- **Greenwashing Detection**: Identifying potentially misleading environmental and social claims

## Keyword Categories

### General Misconduct Terms (`wrong_doing`)
Base set of 39 fundamental misconduct indicators including:
- Legal and regulatory violations (lawsuit, investigation, violation)
- Corporate governance failures (misconduct, malpractice, negligence)
- Communication issues (misrepresentation, misleading, false advertising)
- Enforcement actions (fine, penalty, regulatory action, settlement)

### Environmental Issues
Comprehensive environmental risk terminology covering:
- Climate impact (carbon emissions scandal, pollution incident, toxic release)
- Ecosystem damage (deforestation, ecological damage, chemical spill)
- Regulatory violations (EPA fine, environmental lawsuit, waste disposal issue)
- Greenwashing detection (sustainability controversy, environmental impact report)

### Social Issues
Social responsibility and human rights terminology including:
- Labor violations (child labor, forced labor, wage theft, unsafe working conditions)
- Human rights concerns (discrimination lawsuit, harassment allegations, human rights abuse)
- Consumer safety (consumer safety recall, privacy breach, supply chain ethics)
- Community relations (community conflict, indigenous rights, worker rights)

### Governance Issues
Corporate governance and financial misconduct terms:
- Financial irregularities (accounting irregularities, fraud investigation, insider trading)
- Regulatory compliance (SEC violation, antitrust violation, tax evasion)
- Leadership accountability (executive misconduct, board independence, conflict of interest)
- Transparency issues (bribery scandal, corruption probe, whistleblower complaint)

### Legal and Compliance Framework
Legal terminology for document identification:
- Litigation processes (alternative dispute resolution, bankruptcy litigation)
- Regulatory frameworks (securities law, environmental law, labor law)
- Corporate governance standards (fiduciary duty, merger control, proxy statement)
- International compliance (international law, transnational law, human rights)

### Shareholder Engagement
Stakeholder activism and engagement terminology:
- Shareholder activism (shareholder proposal, shareholder resolution, proxy fight)
- Engagement mechanisms (shareholder meeting, shareholder vote, shareholder campaign)
- Advocacy approaches (shareholder advocacy, shareholder divestment, shareholder protest)

### News and Reporting Sources
Media and investigative reporting indicators:
- Investigative journalism (exposé, investigative journalism, watchdog investigation)
- NGO reporting (NGO report, watchdog report, corporate watch)
- Specialized publications (ICIJ investigation, ProPublica, climate litigation)

## Key Functions

### `company_wrong_doing(company: str) -> List[str]`
Generates company-specific search queries by combining the provided company name with 
base misconduct terms. Returns a list of formatted search strings in the pattern:
`'[Company Name] [misconduct term]'`

This function enables targeted research for specific entities by creating precise search
queries that combine corporate identity with potential violation indicators.

## Technical Architecture
- **Immutable Data Structures**: Uses Python sets and lists for efficient keyword storage
- **Memory Efficiency**: Optimized for frequent access during crawling operations  
- **Integration Ready**: Designed for direct integration with web search APIs and NLP pipelines
- **Extensible Design**: Easy addition of new keyword categories and terminology

## System Integration
This module integrates with the broader EkoIntelligence ESG analysis system:
- **Analytics Backend**: Keywords drive document classification and flag generation
- **Web Crawling System**: Guides automated research and content discovery
- **NLP Pipeline**: Supports text analysis and relevance scoring algorithms
- **Database Integration**: Keywords are stored in `kg_search_queries` table for analysis tracking

## Related Components
- CrewAI Web Crawler Tools (`tools_new/` package) - Uses keywords for search query generation
- ESG Analysis Pipeline (`eko.analysis_v2`) - Processes content discovered using these keywords
- Document Classification System - Categorizes findings based on keyword matches
- Risk Scoring Engine - Weights violations based on keyword severity and frequency

@see https://crewai.com/ CrewAI Multi-Agent Framework Documentation
@see https://www.sasb.org/ Sustainability Accounting Standards Board (SASB) Framework
@see https://www.globalreporting.org/ Global Reporting Initiative (GRI) Standards
@see ../memory.py CrewMemoryManager
@see ../../../analysis_v2/ ESG Analysis Pipeline
<AUTHOR>
@updated 2025-07-22
@description Comprehensive keyword definitions for ESG research and corporate misconduct analysis in web crawling operations
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

# noinspection PySetFunctionToLiteral

wrong_doing=[ 
    # General Misconduct Terms
    "accusation",
    "advertising standards",
    "article",
    "issues",
    "controversy",
    "failure",
    "complaints",
    "violation",
    "false advertising",
    "fine",
    "illegal",
    "investigation",
    "lawsuit",
    "malpractice",
    "misconduct",
    "misrepresentation",
    "misleading",
    "negligence",
    "penalty",
    "press release",
    "regulatory action",
    "scandal",
    "settlement",
    "wrongdoing",
]

key_terms = set(wrong_doing+[
    # Environmental Issues
    "carbon emissions scandal",
    "chemical spill",
    "deforestation",
    "ecological damage",
    "environmental impact report",
    "environmental lawsuit",
    "environmental violation",
    "epa fine",
    "greenwashing",
    "pollution incident",
    "sustainability controversy",
    "toxic release",
    "waste disposal issue",

    # Social Issues
    "child labor",
    "community conflict",
    "consumer safety recall",
    "discrimination lawsuit",
    "forced labor",
    "harassment allegations",
    "human rights abuse",
    "indigenous rights",
    "labor violation",
    "privacy breach",
    "supply chain ethics",
    "unsafe working conditions",
    "union busting",
    "wage theft",
    "worker rights",

    # Governance Issues
    "accounting irregularities",
    "antitrust violation",
    "board independence",
    "bribery scandal",
    "conflict of interest",
    "corruption probe",
    "executive compensation controversy",
    "executive misconduct",
    "fraud investigation",
    "insider trading",
    "regulatory fine",
    "sec violation",
    "shareholder lawsuit",
    "tax evasion",
    "whistleblower complaint",

    # Legal Terms
    "alternative dispute resolution",
    "bankruptcy",
    "bankruptcy litigation",
    "bankruptcy settlement",
    "corporate governance",
    "copyright law",
    "cybersecurity law",
    "data privacy",
    "employment discrimination",
    "environmental law",
    "esg disclosure",
    "fiduciary duty",
    "financial regulation",
    "foreclosure",
    "foreclosure litigation",
    "foreclosure settlement",
    "human rights",
    "injunction",
    "insolvency",
    "intellectual property",
    "international law",
    "labor law",
    "liquidation",
    "liquidation litigation",
    "merger control",
    "patent law",
    "product liability",
    "proxy fight",
    "proxy statement",
    "regulatory compliance",
    "restraining order",
    "restructuring",
    "restructuring litigation",
    "securities law",
    "slapp",
    "tax law",
    "transnational law",
    "workplace safety",
  
    # Shareholder-Related Terms
    "shareholder activism",
    "shareholder advocacy",
    "shareholder boycott",
    "shareholder campaign",
    "shareholder communication",
    "shareholder complaint",
    "shareholder derivative suit",
    "shareholder divestment",
    "shareholder engagement",
    "shareholder letter",
    "shareholder meeting",
    "shareholder proposal",
    "shareholder protest",
    "shareholder relations",
    "shareholder resolution",
    "shareholder vote",

    # News and Reporting Sources
    "corporate watch",
    "exposé",
    "icij investigation",
    "investigative journalism",
    "lobbying",
    "lobbyist",
    "ngo report",
    "propublica",
    "watchdog investigation",
    "watchdog report",
    "climate litigation",
    "consumer protection"
])


def company_wrong_doing(company:str):
    return ["'"+company + "' " + term for term in wrong_doing]
