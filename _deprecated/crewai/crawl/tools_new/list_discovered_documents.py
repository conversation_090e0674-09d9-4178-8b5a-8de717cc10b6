"""
CrewAI Document Discovery Report Tool for Multi-Agent ESG Research Workflows

This module provides the `ListDiscoveredDocumentsTool` class, a specialized CrewAI tool that generates 
comprehensive discovery reports of documents identified during automated ESG (Environmental, Social, 
Governance) research sessions. The tool serves as a coordination mechanism for AI agents within the 
EkoIntelligence platform, enabling real-time visibility into discovered corporate sustainability 
documents, ESG reports, regulatory filings, and related content for strategic research planning 
and duplicate prevention across multi-agent crawling workflows.

## Core Functionality

### Document Discovery Tracking and Reporting
- **Global Discovery State**: Accesses the shared `_discovered_documents` tracker from the base module for cross-tool coordination
- **Real-Time Reporting**: Provides immediate visibility into discovered documents across all active crawling sessions
- **Categorized Output**: Organizes discovered documents by type (PDF, webpage, report, etc.) for structured analysis
- **Session Coordination**: Enables agents to understand collective progress and avoid duplicate research efforts

### Document Information Display
The tool provides comprehensive document metadata for each discovered item:
- **Document Title**: Human-readable title extracted from content or metadata
- **Source URL**: Complete URL reference for document access and verification
- **Document Type**: Categorized classification (e.g., "sustainability_report", "regulatory_filing", "webpage")
- **Publication Date**: Temporal information when available for chronological analysis
- **Description**: Brief content summary or contextual information about relevance

### Output Format and Organization
1. **Summary Header**: Total document count and discovery session overview
2. **Type-Based Grouping**: Documents organized by category with subtotals for each type
3. **Numbered Listings**: Sequential numbering within each category for easy reference
4. **Structured Metadata**: Consistent formatting with URL, date, and description details
5. **Empty State Handling**: Clear messaging when no documents have been discovered

## CrewAI Framework Integration

### Tool Architecture Design
The `ListDiscoveredDocumentsTool` implements CrewAI best practices by inheriting from `BaseTool`:
- **Pydantic Input Validation**: Uses `ListDiscoveredDocumentsInput` schema for type-safe parameter handling
- **Standardized Tool Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **Error Handling**: Comprehensive exception management with structured error reporting
- **Agent Integration**: Seamless integration into agent tool lists for automatic discovery reporting

### Multi-Agent Coordination Support
The tool enhances multi-agent ESG research workflows by providing:
- **Cross-Agent Visibility**: All agents can query discovered documents regardless of which agent found them
- **Progress Tracking**: Real-time insight into research progress for adaptive workflow planning
- **Duplicate Prevention**: Helps agents avoid re-processing already discovered content
- **Research Strategy**: Enables agents to identify gaps and adjust search strategies based on current findings

## ESG Research Workflow Integration

### Discovery Phase Support
The tool supports systematic ESG research by enabling:
1. **Content Gap Analysis**: Identify missing document types or coverage areas in current research
2. **Source Diversity Assessment**: Evaluate variety and credibility of discovered sources
3. **Temporal Coverage**: Assess chronological distribution of discovered documents
4. **Research Completion**: Determine when sufficient content has been gathered for analysis

### Document Type Categorization
The tool recognizes and categorizes various ESG-relevant document types:
- **Corporate Reports**: Annual reports, sustainability reports, CSR documents
- **Regulatory Filings**: SEC filings, environmental compliance documents, disclosure statements
- **News and Media**: Press releases, news articles, investigative journalism
- **Academic Content**: Research papers, policy documents, industry analyses
- **Web Content**: Corporate websites, blog posts, stakeholder communications

## Technical Architecture

### Shared State Management
The tool leverages the centralized document tracking system:
- **Base Module Integration**: Accesses `_discovered_documents` from the shared base module
- **Memory Efficiency**: No local state duplication, uses shared global tracking list
- **Thread Safety**: Designed for concurrent access in multi-agent environments
- **Session Persistence**: Maintains discovery state throughout long-running research sessions

### Performance Considerations
Optimization strategies for large-scale document discovery:
- **Lightweight Operations**: Simple read-only access to shared document list
- **Efficient Formatting**: Streamlined text processing for report generation
- **Categorization Speed**: Fast document type-based grouping using dictionary operations
- **Memory Management**: Minimal memory footprint with reference-based access to shared data

## System Architecture Context

### Analytics Pipeline Integration
The tool fits within the broader EkoIntelligence ESG analysis system:
- **Discovery Layer**: Part of the web crawling and document discovery phase
- **Coordination Function**: Enables agent-to-agent communication about research progress
- **Pipeline Input**: Discovered documents feed downstream ESG analysis and scoring algorithms
- **Quality Assurance**: Supports research validation and completeness assessment

### Database and Memory Integration
Research coordination supported by multiple persistence layers:
- **In-Memory Tracking**: Immediate access to current session discoveries via shared list
- **Database Storage**: Integration with analytics database for persistent research session tracking
- **Memory System**: Compatible with CrewAI memory and custom `CrewMemoryManager` for long-term storage
- **Tool Usage Analytics**: Discovery patterns tracked for research strategy optimization

## Related Components
- **Document Discovery Tools** (`tools_new/` package): Tools that populate the shared discovery tracker
- **Base Module** (`base.py`): Shared resources and global document tracking infrastructure
- **Memory Management** (`../memory.py`): `CrewMemoryManager` for persistent session state
- **Web Utilities** (`eko.entities.agent.tools`): Core web interaction capabilities for document discovery
- **ESG Analysis Pipeline** (`eko.analysis_v2/`): Downstream processing of discovered content

## Example Output Format
```
DISCOVERED DOCUMENTS:

SUSTAINABILITY_REPORT (3):
1. Microsoft 2023 Environmental Sustainability Report
   URL: https://www.microsoft.com/sustainability/report-2023
   Date: 2023-09-15
   Description: Comprehensive environmental impact assessment and carbon reduction strategies

PDF (2):
1. Tesla Q3 2023 Impact Report  
   URL: https://ir.tesla.com/static-files/impact-report-2023-q3.pdf
   Date: 2023-10-20
   Description: Quarterly sustainability metrics and environmental performance data
```

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/agents CrewAI Multi-Agent Systems
@see https://docs.python.org/3/library/typing.html Python Type Hints Documentation
@see ../base.py Shared Resources and Document Tracking Infrastructure
@see ../../memory.py CrewMemoryManager for Persistent Session Tracking
@see ../../entities/agent/tools.py WebTools Class for Document Discovery
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for generating comprehensive reports of discovered documents during multi-agent ESG research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from typing import Type

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel

from .base import _discovered_documents


class ListDiscoveredDocumentsInput(BaseModel):
    """Input schema for the list_discovered_documents tool."""
    pass


class ListDiscoveredDocumentsTool(BaseTool):
    """Tool for listing all discovered documents."""
    
    name: str = "list_discovered_documents"
    description: str = "List all discovered documents"
    args_schema: Type[BaseModel] = ListDiscoveredDocumentsInput
    
    def _run(self) -> str:
        """
        List all discovered documents.

        Returns:
            str: List of discovered documents
        """
        try:
            if not _discovered_documents:
                return "No documents have been discovered yet."

            # Group documents by type
            docs_by_type = {}
            for doc in _discovered_documents:
                doc_type = doc["doc_type"]
                if doc_type not in docs_by_type:
                    docs_by_type[doc_type] = []
                docs_by_type[doc_type].append(doc)

            formatted_docs = ["DISCOVERED DOCUMENTS:"]

            for doc_type, docs in docs_by_type.items():
                formatted_docs.append(f"\n{doc_type.upper()} ({len(docs)}):")
                for i, doc in enumerate(docs, 1):
                    formatted_docs.append(f"{i}. {doc['title']}")
                    formatted_docs.append(f"   URL: {doc['url']}")
                    if doc['publication_date']:
                        formatted_docs.append(f"   Date: {doc['publication_date']}")
                    if doc['description']:
                        formatted_docs.append(f"   Description: {doc['description']}")
                    formatted_docs.append("")

            return "\n".join(formatted_docs)
        except Exception as e:
            logger.error(f"Error listing documents: {e}")
            return f"Error listing documents: {str(e)}"
