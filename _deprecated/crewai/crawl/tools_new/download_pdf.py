"""
CrewAI PDF and Webpage Download Tool for ESG Document Processing

This module provides the `DownloadTool` class, a specialized CrewAI tool for downloading, processing, 
and extracting content from PDF documents and web pages during automated ESG (Environmental, Social, 
Governance) research workflows within the EkoIntelligence analysis platform. The tool seamlessly 
integrates with the document processing pipeline to ingest corporate reports, sustainability documents, 
and web articles for comprehensive ESG analysis and statement extraction.

## Core Functionality
- **Multi-Format Document Processing**: Downloads and processes both PDF documents and HTML web pages using intelligent content detection
- **ESG Statement Extraction**: Automatically extracts sustainability statements and ESG-related content from downloaded documents
- **Duplicate Prevention**: Implements sophisticated caching mechanisms to prevent redundant downloads of previously processed documents
- **Database Integration**: Stores processed documents in the knowledge graph with complete metadata for downstream analysis
- **Search Integration**: Supports text search functionality within downloaded documents using PostgreSQL full-text search vectors
- **Memory Management**: Integrates with optional memory manager for persistent tracking across research sessions

## ESG Research Integration

### Corporate Document Analysis
The tool is specifically designed for comprehensive ESG document ingestion and analysis:
- **Sustainability Report Processing**: Downloads annual sustainability reports, CSR documents, and ESG disclosures
- **Financial Filing Integration**: Processes SEC filings, annual reports, and investor presentations for ESG content
- **Policy Document Ingestion**: Downloads corporate policies, governance documents, and stakeholder reports
- **News Article Collection**: Captures news articles, press releases, and third-party ESG assessments
- **Research Report Access**: Downloads analyst reports, NGO assessments, and industry studies

### Document Processing Pipeline
Advanced document processing capabilities for ESG content analysis:
- **Intelligent Format Detection**: Automatically detects PDF vs. HTML content and applies appropriate processing
- **Content Extraction**: Uses specialized processors (`PDFProcessor`, `WebpageProcessor`) for optimal content extraction
- **Statement Extraction**: Leverages `extract_statements_from_doc()` for automated ESG statement identification
- **Metadata Enrichment**: Captures comprehensive document metadata including title, date, credibility, research categories
- **Database Persistence**: Stores processed documents in `kg_documents` table with full metadata and search vectors

## CrewAI Framework Integration

### Tool Architecture Design
The `DownloadTool` follows CrewAI best practices by inheriting from `BaseTool`:
- **Pydantic Input Validation**: Uses `DownloadInput` schema for type-safe parameter handling
- **Standardized Tool Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **Memory Manager Integration**: Optional memory manager integration for session persistence and tracking
- **Duplicate Detection**: Maintains both local cache and memory manager integration for comprehensive duplicate prevention
- **Error Handling**: Comprehensive exception handling with detailed logging via Loguru framework

### Multi-Agent Research Workflows
In collaborative ESG research environments, the download tool enables:
- **Document Collection**: Centralized document ingestion for multi-agent analysis workflows
- **Content Availability**: Ensures downloaded documents are available for subsequent analysis by other agents
- **Research Coordination**: Prevents redundant downloads across multiple research agents through intelligent caching
- **Quality Assurance**: Validates document accessibility and content quality before database storage

## Technical Architecture

### Document Processing Pipeline
The tool implements a sophisticated document ingestion workflow:
1. **Input Validation**: Validates URLs and parameters using Pydantic schemas with comprehensive error checking
2. **Duplicate Detection**: Checks both local cache and memory manager for previously downloaded documents
3. **URL Analysis**: Parses URLs to extract domain information and determine processing approach
4. **Format Detection**: Intelligently detects PDF vs. HTML content based on URL paths and content headers
5. **Content Processing**: Routes documents to specialized processors (`process_pdf()` or `process_webpage()`)
6. **Statement Extraction**: Triggers automated ESG statement extraction using NLP processing pipeline
7. **Database Storage**: Persists complete document information with metadata in PostgreSQL knowledge graph
8. **Search Integration**: Provides full-text search capabilities using PostgreSQL search vectors
9. **Memory Tracking**: Updates memory manager with download information for session tracking

### Performance Optimization Features
- **Intelligent Caching**: Multi-level caching prevents redundant downloads using URL-based tracking
- **Force Re-download**: Optional 'redo' parameter allows forced re-processing of previously downloaded documents
- **Memory Efficiency**: Maintains lightweight URL cache for processed document tracking
- **Database Optimization**: Leverages database indices and search vectors for efficient content retrieval

## Database Schema Integration

### Knowledge Graph Storage
The tool integrates with the EkoIntelligence knowledge graph schema:
- **kg_documents Table**: Primary storage for document metadata, content, and analysis results
- **kg_document_pages Table**: Stores individual pages with full-text search vectors for content discovery
- **Document Metadata Fields**: Comprehensive metadata including title, date, credibility scores, research categories
- **Search Vector Integration**: PostgreSQL `text_search_vector` columns enable efficient content search
- **Foreign Key Relationships**: Proper database constraints and cascading for data integrity

### Content Search and Retrieval
Advanced search capabilities for processed documents:
- **Full-Text Search**: PostgreSQL `websearch_to_tsquery()` for natural language document search
- **Page-Level Granularity**: Search operates at individual page level for precise content location
- **Relevance Scoring**: Database-level relevance scoring for search result ranking
- **Performance Optimization**: Database indices optimize search performance across large document collections

## System Integration

### File Storage and Processing
The tool operates within the EkoIntelligence processing architecture:
- **Processor Integration**: Leverages `PDFProcessor` and `WebpageProcessor` for specialized content extraction
- **Statement Extraction**: Integrates with `extract_statements_from_doc()` for automated ESG content analysis
- **Database Connectivity**: Uses `get_bo_conn()` for analytics database connections and transactions
- **Error Recovery**: Comprehensive error handling ensures reliable document processing

### Memory Management and Tracking
Advanced session management for research workflows:
- **Memory Manager Integration**: Optional integration with external memory management systems
- **Download Tracking**: Maintains persistent records of downloaded documents across sessions
- **Cache Coordination**: Coordinates between local cache and external memory systems for optimal performance
- **Session Continuity**: Enables research sessions to resume without losing download history

## Usage Patterns

### Basic Document Download
```python
# Download PDF document
result = tool._run(
    url="https://example.com/sustainability-report-2024.pdf",
    search_text="",
    redo="no"
)

# Download webpage with search
result = tool._run(
    url="https://example.com/esg-news-article",
    search_text="carbon emissions reduction",
    redo="no"
)
```

### Advanced Search Integration
```python
# Download and search for specific ESG topics
result = tool._run(
    url="https://company.com/annual-report.pdf",
    search_text="scope 3 emissions climate targets",
    redo="no"
)
```

@see https://github.com/crewaiinc/crewai/blob/main/docs/concepts/tools.md CrewAI Tools Documentation
@see https://docs.pydantic.dev/latest/concepts/models/ Pydantic BaseModel Documentation
@see https://loguru.readthedocs.io/en/stable/ Loguru Logging Framework
@see https://www.postgresql.org/docs/current/textsearch.html PostgreSQL Full-Text Search
<AUTHOR>
@updated 2025-07-22
@docgen doc-by-claude

(c) All rights reserved ekoIntelligence 2025
"""
import json
from typing import Type, Optional, Any, Set
from urllib.parse import urlparse

import jsonpickle
from crewai.tools import BaseTool
from loguru import logger
from psycopg.rows import dict_row
from pydantic import BaseModel, Field

from eko.db import get_bo_conn
from eko.scrape.reports import process_pdf, process_webpage
from eko.statements.extract import extract_statements_from_doc


class DownloadInput(BaseModel):
    """Input schema for the download_pdf tool."""
    url: str = Field(..., description="The URL of a PDF report or a webpage containing an article to download")
    search_text: str = Field(..., description="Optional text to search for in the PDF or page, if not required just provide an empty string.")
    redo: str = Field(..., description="Set to 'yes' to force re-download even if this URL was already downloaded")


class DownloadTool(BaseTool):
    """Tool for downloading and processing PDFs."""

    name: str = "download_pdf_or_article"
    description: str = "Download and process a PDF or an article. Use parameter redo=True to force re-download of previously downloaded documents."
    args_schema: Type[BaseModel] = DownloadInput
    _memory_manager: Optional[Any] = None

    def __init__(self, **kwargs):
        memory_manager = kwargs.pop('memory_manager')
        super().__init__(**kwargs)
        self._memory_manager = memory_manager
        # Store reference to memory_manager
        # Cache for URLs we've already processed
        self._downloaded_urls: Set[str] = set()

    def _run(self, url: str, search_text: str, redo: str) -> str:
        """
        Download and process a PDF.

        Args:
            url: The URL of the PDF to download
            search_text: Optional text to search for in the PDF
            redo: Whether to force re-download even if this URL was already downloaded

        Returns:
            str: The PDF content or search results
        """
        try:
            # Check if we've already downloaded this URL
            if  redo != 'yes' and url in self._downloaded_urls:
                return "We've already downloaded this document before. Use redo=True if you want to download it again."

            # Check memory_manager for downloaded files
            if redo != "yes" and self._memory_manager:
                memory = self._memory_manager._load_memory()
                downloaded_files = memory.get("downloaded_files", [])
                for file_info in downloaded_files:
                    if file_info.get("source_url") == url:
                        return "We've already downloaded this document before. Use redo=True if you want to download it again."

            # Validate URL
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                return f"Invalid URL: {url}. Please provide a complete URL including http:// or https://"

            # Try to download the PDF
            try:
                # Extract origin_domain from the URL
                origin_domain = parsed_url.netloc

                if parsed_url.path.endswith("pdf"):
                    doc_id = process_pdf(url, force_accept=True)
                else:
                    doc_id = process_webpage(url, research_types=[], force_accept=True, depth=0)
                if doc_id is None:
                    return f"Failed to download document from {url}"

                extract_statements_from_doc(doc_id, search_text)

                # Add to our downloaded URLs
                self._downloaded_urls.add(url)

                with get_bo_conn() as conn:
                    with conn.cursor(row_factory=dict_row) as cur:
                        cur.execute(
                            "SELECT id, name, url, public_url, year, extract, credibility, "
                            " research_categories, metadata, title, research_scope, research_targets, "
                            " score, file_type, owner, publish_date, origin_domain FROM kg_documents "
                            "WHERE id = %s", (doc_id,))
                        result = cur.fetchone()

                        if result is None:
                            return f"Failed to download document from {url}"

                        doc_info = jsonpickle.encode(result, unpicklable=False)

                        # Track the file download in memory manager
                        if self._memory_manager:
                            file_path = f"kg_documents/{doc_id}"
                            self._memory_manager.track_downloaded_file(file_path, url)

                        if search_text:
                            cur.execute(
                                "SELECT id as page_id, doc_id, page_text, page as page_number, text_type, score FROM kg_document_pages WHERE doc_id = %s AND text_search_vector @@ websearch_to_tsquery('english', %s) LIMIT 5",
                                (doc_id, search_text))
                            search_results = cur.fetchall()
                            return f"Successfully downloaded document:\n {doc_info}\n: Search results for '{search_text}' in: {json.dumps(search_results)}"

                        return f"Successfully downloaded document\n: {doc_info}"

            except Exception as pdf_error:
                logger.exception(f"Error processing document: {pdf_error}")
                return f"Error processing document from {url}: {str(pdf_error)}"

        except Exception as e:
            logger.error(f"Error downloading document: {e}")
            return f"Error downloading document: {str(e)}"
