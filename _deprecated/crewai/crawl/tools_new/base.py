"""
CrewAI Web Crawler Base Module and Shared Resources

This module serves as the foundational base for specialized CrewAI web crawling tools within 
the EkoIntelligence ESG analysis platform's AI-powered research system. It provides common 
imports, shared data structures, and centralized resources that support the multi-agent 
web crawling workflow for automated ESG (Environmental, Social, Governance) research,
corporate misconduct investigation, and sustainability claim analysis.

## Core Purpose
The module acts as a **shared foundation layer** for all web crawling tools in the `tools_new` 
package, providing:
- **Centralized Dependencies**: Common imports and utilities shared across all crawling tools
- **Shared Tool Instance**: A single `WebTools` instance for consistent web interaction capabilities
- **Document Tracking**: Global document discovery tracker for cross-tool coordination
- **Memory Efficiency**: Prevents duplicate instantiation of common resources across tool instances

## Key Components

### `WebTools` Instance (`web_tools`)
A shared instance of the `WebTools` class from `eko.entities.agent.tools` that provides 
comprehensive web interaction capabilities:

**Search and Discovery:**
- `search_web()`: Google Custom Search API integration for ESG-related content discovery
- `analyze_domain()`: Domain categorization and credibility assessment
- `find_statements()`: Database search for corporate sustainability statements and claims

**Content Retrieval:**
- `fetch_webpage()`: Advanced webpage fetching with cleaning and metadata extraction
- `download_pdf()`: PDF document processing with OCR and text extraction
- `download_and_search_pdf()`: Combined PDF download with full-text search capabilities

**Analysis and Processing:**
- `extract_links()`: Link extraction with relationship mapping and prioritization
- `_extract_snippet()`: Context-aware text snippet extraction for search results

### `_discovered_documents` Tracker
A global list that maintains state of discovered documents across all tool instances:
- **Cross-Tool Coordination**: Enables different tools to track collectively discovered content
- **Duplicate Prevention**: Prevents redundant processing of already identified documents
- **Session Persistence**: Maintains discovery state throughout multi-agent research sessions
- **Memory Integration**: Supports coordination with `CrewMemoryManager` for persistent tracking

## CrewAI Framework Integration

### Multi-Agent Architecture Support
The base module enables seamless integration with CrewAI's multi-agent system:
- **Tool Inheritance**: All tools in `tools_new` package inherit shared resources from this base
- **Agent Collaboration**: Shared `web_tools` instance enables consistent behavior across agents
- **Memory Coordination**: Global document tracker supports cross-agent research coordination
- **Resource Optimization**: Single instance pattern reduces memory overhead in agent systems

### ESG Research Workflow Integration
The module supports the complete ESG analysis pipeline by providing:
1. **Foundation Layer**: Common utilities for all ESG-focused web crawling operations
2. **Discovery Support**: Shared document tracking for systematic content identification
3. **Analysis Integration**: Web tools that interface with ESG analysis and scoring systems
4. **Database Connectivity**: Tools connect to analytics database for corporate data enrichment

## Technical Architecture

### Dependency Management
The module provides centralized access to core dependencies:
- **WebTools Class**: Comprehensive web interaction and analysis capabilities
- **Database Integration**: Connection to analytics database via `get_bo_conn()`
- **Content Processing**: Integration with PDF processing, text cleaning, and NLP pipelines
- **Logging Framework**: Structured logging via Loguru for debugging and monitoring

### Memory and Performance
Optimization strategies implemented in the base module:
- **Single Instance Pattern**: One `WebTools` instance shared across all tool instances
- **Lightweight State**: Minimal global state with efficient list-based document tracking
- **Lazy Loading**: Resources initialized only when first accessed by tools
- **Session Persistence**: Support for long-running research sessions with state preservation

## System Architecture Context

### Analytics Pipeline Integration
The base module supports the broader EkoIntelligence system:
- **Analytics Backend**: Python orchestration layer for automated ESG research and analysis
- **Data Pipeline**: Web crawling results feed ESG scoring and greenwashing detection algorithms  
- **Database Layer**: Tools access analytics database for corporate data and store research findings
- **Memory System**: Integration with CrewAI memory and custom `CrewMemoryManager` for persistence

### ESG Analysis Workflow
Research workflow supported by base module tools:
1. **Discovery Phase**: Shared web tools identify corporate ESG documents and controversies
2. **Collection Phase**: Document tracking prevents duplicate processing during content gathering
3. **Processing Phase**: Common analysis tools extract insights and assess relevance
4. **Integration Phase**: Results integrate with ESG effect analysis and corporate risk assessment

## Related Components
- **Tool Implementations** (`tools_new/` package): All specialized crawling tools build on this base
- **Memory Management** (`../memory.py`): `CrewMemoryManager` for persistent session tracking
- **Web Utilities** (`eko.entities.agent.tools`): Core `WebTools` class with web interaction methods
- **ESG Analysis** (`eko.analysis_v2/`): Downstream processing of discovered ESG content
- **Database Schema** (analytics DB): Storage for research sessions, insights, and tool usage

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/agents CrewAI Multi-Agent Systems  
@see https://requests.readthedocs.io/ Python Requests Library for HTTP Operations
@see ../tools_mem/ Memory Tool Implementations for Persistent State Management
@see ../../entities/agent/tools.py WebTools Class Implementation
@see ../../analysis_v2/ ESG Analysis Pipeline for Content Processing
<AUTHOR>  
@updated 2025-07-22
@description Base module providing shared resources and dependencies for CrewAI web crawling tools in ESG research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from eko.entities.agent.tools import WebTools

# Global document tracker
_discovered_documents = []

# Create a shared instance of WebTools
web_tools = WebTools()
