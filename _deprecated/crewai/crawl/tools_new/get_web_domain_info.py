"""
CrewAI Web Domain Information Retrieval Tool for ESG Corporate Intelligence Analysis

This module provides the `GetWebDomainInfoTool` class, a specialized CrewAI tool designed to retrieve comprehensive 
domain intelligence for ESG (Environmental, Social, Governance) research workflows within the EkoIntelligence 
platform. The tool integrates with the platform's domain categorization system to provide AI agents with detailed 
contextual information about web domains, including ownership details, credibility assessments, and categorical 
classifications essential for corporate sustainability analysis and research intelligence.

## Core Functionality
- **Domain Intelligence Retrieval**: Extracts comprehensive domain metadata including ownership, categorization, and credibility metrics
- **ESG Context Enhancement**: Provides domain-specific context for enhanced corporate sustainability analysis
- **Credibility Assessment**: Delivers trust scores and reliability metrics for information source validation
- **Entity Resolution**: Links domains to their owning organizations and entity types for comprehensive corporate analysis
- **Multi-Language Support**: Handles international domains with locale detection and processing capabilities
- **Cached Performance**: Leverages platform caching mechanisms for optimized domain lookup performance

## CrewAI Framework Integration

### Tool Architecture Design
The `GetWebDomainInfoTool` follows CrewAI best practices by inheriting from `BaseTool`:
- **Pydantic Input Validation**: Uses `GetWebDomainInfoInput` schema for type-safe domain parameter validation
- **Standardized Tool Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **JSON Serialization**: Returns structured domain data in JSON format for easy agent consumption
- **Error Handling**: Comprehensive exception handling with detailed logging via Loguru
- **Asynchronous Support**: Compatible with both synchronous and asynchronous CrewAI agent execution patterns

### Domain Categorization System Integration
The tool leverages EkoIntelligence's sophisticated domain categorization system:
- **Role Classification**: Categorizes domains by function (NGO, Media, Research, Commercial, Government, etc.)
- **Category Analysis**: Provides detailed domain type classification (Technology, Environmental, Finance, etc.)
- **Credibility Scoring**: Delivers 0-100 credibility scores based on source reliability and trustworthiness
- **Entity Linking**: Connects domains to their owning entities with comprehensive organizational metadata
- **Behavioral Assessment**: Provides context for understanding potential bias and information quality

## ESG Research Intelligence Applications

### Corporate Sustainability Analysis
The domain information tool supports comprehensive ESG analysis workflows:
- **Source Verification**: Validates the credibility and reliability of corporate sustainability information sources
- **Ownership Analysis**: Identifies domain owners and their potential conflicts of interest or commercial bias
- **Information Quality Assessment**: Provides credibility metrics essential for evaluating ESG claims and statements
- **Media Source Evaluation**: Distinguishes between independent journalism and corporate-sponsored content
- **NGO and Activist Assessment**: Evaluates the reliability and transparency of environmental and social advocacy sources

### Greenwashing Detection Support
Advanced domain intelligence capabilities support greenwashing detection:
- **Source Bias Identification**: Identifies commercially-motivated or sponsored content that may present biased ESG information
- **Transparency Assessment**: Evaluates the transparency and independence of information sources
- **Reliability Scoring**: Provides numerical reliability metrics for automated credibility assessment
- **Ownership Disclosure**: Reveals domain ownership and potential conflicts of interest

## Domain Information Schema

### Comprehensive Domain Metadata
The tool returns detailed domain information including:
- **Basic Identification**: Domain name, website URL, and locale information
- **Ownership Details**: Owning entity name and entity type classification
- **Categorization**: Domain role (activist, regulator, commercial, etc.) and category classification
- **Quality Metrics**: Credibility score (0-100) with detailed reasoning and confidence assessment
- **Content Analysis**: Description and summary of domain purpose and content focus
- **Visual Elements**: Associated image URLs and branding information

### Database Schema Integration
Domain information is stored and retrieved from the analytics database (`kg_domains` table):
- **Primary Storage**: Domain-based primary key with comprehensive metadata fields
- **Entity Relationships**: Foreign key relationships with `kg_base_entities` for organizational linking
- **Indexing Strategy**: Optimized indexes on domain_role, domain_category, and entity_id for efficient queries
- **Data Integrity**: Foreign key constraints ensure referential integrity with entity data
- **Temporal Tracking**: Creation timestamps and update tracking for data freshness validation

## System Architecture Context

### EkoIntelligence Platform Integration
The domain information tool operates within the broader ESG analysis ecosystem:
- **Research Pipeline**: Provides domain context for automated corporate research and analysis workflows
- **Agent Orchestration**: Enables CrewAI agents to make informed decisions about information source quality
- **Data Integration**: Domain intelligence feeds into broader ESG scoring and analysis systems
- **Caching Layer**: Utilizes platform-wide caching mechanisms for optimized performance and reduced database load

### Multi-Agent Research Workflows
In collaborative ESG research environments, the domain info tool enables:
- **Source Quality Assessment**: Multiple agents can assess information source credibility before analysis
- **Research Coordination**: Shared domain intelligence prevents duplicate credibility assessments
- **Bias Detection**: Agents can identify potentially biased sources and adjust analysis accordingly
- **Quality Control**: Systematic credibility assessment improves overall research quality and reliability

## Performance and Scalability

### Caching Strategy
- **Multi-Level Caching**: Utilizes both application-level and database-level caching for optimal performance
- **Cache Invalidation**: Intelligent cache management with configurable expiration policies
- **Memory Optimization**: Efficient memory usage through selective caching of frequently accessed domains
- **Database Optimization**: Indexed queries and connection pooling for scalable database access

### Error Handling and Reliability
- **Exception Safety**: Comprehensive exception handling with graceful degradation during failures
- **Data Validation**: Multi-layer validation ensures data integrity and prevents corruption
- **Logging Integration**: Detailed error logging via Loguru for debugging and monitoring
- **Fallback Mechanisms**: Graceful handling of database connectivity issues and data availability

## Security and Access Control

### Data Security Measures
- **Input Sanitization**: Comprehensive input validation prevents injection attacks and data corruption
- **Database Security**: Utilizes EkoIntelligence standard authentication and connection security
- **Audit Trail**: Complete logging of all domain lookup activities for security monitoring
- **Access Control**: Domain information scoped appropriately for research and analysis purposes

## Key Dependencies and Technologies

### CrewAI Framework Components
- **CrewAI BaseTool**: Foundation class providing standardized tool interface and agent integration
- **Pydantic Models**: Type-safe data validation using `BaseModel` and `Field` for input schemas
- **JSON Serialization**: JSONPickle for reliable data serialization and deserialization

### EkoIntelligence Platform Integration
- **Domain Categorization System**: Integration with `eko.domains.domain_categorizer` for comprehensive domain analysis
- **Database Access**: PostgreSQL connectivity via `get_bo_conn()` for analytics database access
- **Caching Infrastructure**: Platform-wide caching mechanisms for optimized performance

### Supporting Infrastructure
- **Loguru Logging**: Advanced logging framework providing structured logs with exception tracing
- **Type Validation**: Pydantic-based type validation ensuring data integrity and schema compliance
- **JSON Processing**: Native JSON handling for structured data serialization and agent communication

## Usage Examples and Integration Patterns

### Basic Agent Configuration
```python
from eko.agent.crewai.crawl.tools_new.get_web_domain_info import GetWebDomainInfoTool
from crewai import Agent

# Configure domain analysis tool
domain_tool = GetWebDomainInfoTool()

# Configure ESG research agent with domain intelligence
esg_researcher = Agent(
    role="ESG Information Analyst",
    goal="Analyze corporate sustainability information with source credibility assessment",
    tools=[domain_tool],
    memory=True,
    verbose=True
)
```

### Multi-Agent Source Validation Workflow
```python
# Coordinated multi-agent research with domain intelligence
source_validator = Agent(role="Information Source Validator", tools=[domain_tool])
content_analyzer = Agent(role="ESG Content Analyst", tools=[domain_tool])
greenwashing_detector = Agent(role="Greenwashing Detection Specialist", tools=[domain_tool])

# All agents leverage domain intelligence for informed analysis
crew = Crew(agents=[source_validator, content_analyzer, greenwashing_detector])
results = crew.kickoff()
```

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/agents CrewAI Agent Architecture
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see https://www.postgresql.org/docs/current/datatype-json.html PostgreSQL JSONB Documentation
@see backoffice/src/eko/domains/domain_categorizer.py Domain Categorization Implementation
@see backoffice/src/eko/db/get_bo_conn.py Database Connection Management
@see tmp/db/backoffice/schemas/public/tables/kg_domains.sql Domain Database Schema
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for retrieving comprehensive domain intelligence and credibility assessment for ESG research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""
from typing import Type

import jsonpickle
from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.domains.domain_categorizer import get_domain_info


class GetWebDomainInfoInput(BaseModel):
    """Input schema for the search_web tool."""
    domain: str = Field(..., description="The domain of a webpage or website")


class GetWebDomainInfoTool(BaseTool):
    """Tool for getting information about a web domain."""
    
    name: str = "get_domain_info"
    description: str = "Get meta information about a web domain"
    args_schema: Type[BaseModel] = GetWebDomainInfoInput
    
    def _run(self, domain: str) -> str:
        """
        Search the web for information.

        Args:
            query: The search query
            num_results: Number of search results to return

        Returns:
            str: The search results
        """
        try:
            results = get_domain_info(domain=domain)
            return jsonpickle.encode(results, unpicklable=False)
        except Exception as e:
            logger.error(f"Error searching web: {e}")
            return f"Error searching web: {str(e)}"
