"""
CrewAI Web Search Tool for ESG Research and Corporate Intelligence Workflows

This module provides the `SearchWebTool` class, a specialized CrewAI tool for performing web searches 
through the Google Custom Search API during automated ESG (Environmental, Social, Governance) research 
workflows. The tool serves as a foundational web discovery component within the EkoIntelligence ESG 
analysis platform, enabling systematic retrieval of corporate sustainability documents, governance 
reports, environmental compliance records, and related content for comprehensive ESG assessment 
and greenwashing detection.

## Core Functionality

### Google Custom Search Integration
- **Enterprise Search API**: Leverages Google Custom Search API for reliable, high-quality web search results
- **ESG-Focused Discovery**: Configured for optimal discovery of corporate ESG documents and sustainability reports
- **Structured Results**: Returns formatted search results with titles, URLs, and descriptive snippets
- **Scalable Architecture**: Supports configurable result limits (up to 10 per API constraints) for efficient resource usage

### CrewAI Tool Framework Integration
The tool implements the CrewAI `BaseTool` interface providing:

#### Tool Definition Structure:
- **Tool Identity**: Clear name and description for agent selection and utilization
- **Input Schema**: Pydantic-based validation for search parameters and result limits
- **Error Handling**: Graceful failure management with detailed logging and fallback responses
- **Agent Integration**: Seamless integration with CrewAI multi-agent research workflows

#### Search Parameters Framework:
- **Query Processing**: Flexible search query handling with support for complex ESG-related search terms
- **Result Management**: Configurable result limits (default: 20, maximum: 10 due to API limitations)
- **Response Formatting**: Structured output optimized for downstream ESG analysis processing

## Key Classes and Components

### `SearchWebInput` (Pydantic BaseModel)
Input validation schema for web search operations:
- **query** (str, required): The search query string for ESG-related content discovery
- **num_results** (int, optional): Number of search results to return (default: 20, API max: 10)

Provides automatic validation, type checking, and clear parameter descriptions for agent utilization.

### `SearchWebTool` (CrewAI BaseTool)
Primary search tool class implementing CrewAI's tool interface:

#### Core Properties:
- **name**: "search_web" - Unique identifier for agent tool selection
- **description**: "Search the web for information" - Agent-readable capability description
- **args_schema**: Links to `SearchWebInput` for automatic parameter validation

#### Primary Method: `_run(query: str, num_results: int = 20) -> str`
Executes web search operations and returns formatted results:

**Search Process:**
1. **Parameter Validation**: Validates query string and result count constraints
2. **API Integration**: Calls shared `web_tools.search_web()` method for actual search execution
3. **Result Formatting**: Processes raw API responses into agent-readable text format
4. **Error Management**: Handles search failures gracefully with detailed error reporting

**Output Format:**
Returns structured text with numbered results containing:
```
1. [Article Title]
   URL: [Article URL]
   Snippet: [Descriptive snippet from search result]
   
2. [Next Article...]
```

## Technical Architecture

### Dependency Integration
The tool integrates with core EkoIntelligence components:

#### Core Dependencies:
- **CrewAI Framework**: `crewai.tools.BaseTool` for multi-agent tool integration
- **Pydantic Models**: `pydantic.BaseModel` and `Field` for structured data validation
- **Logging System**: `loguru.logger` for comprehensive operation logging and debugging
- **Web Tools Module**: `base.web_tools` for shared Google Custom Search API functionality

#### Shared Resources:
- **WebTools Instance**: Accesses shared `web_tools` instance from base module for consistent API usage
- **Error Handling**: Leverages centralized error management and logging infrastructure
- **Resource Optimization**: Uses singleton pattern through base module for memory efficiency

### System Integration Context

#### ESG Analysis Pipeline Integration:
- **Discovery Phase**: Provides initial web content identification for ESG document processing
- **Research Workflow**: Supports multi-agent research coordination through CrewAI framework
- **Database Integration**: Search queries and results can be tracked in analytics database
- **Content Pipeline**: Results feed into downstream document processing and analysis systems

#### Multi-Agent Architecture:
- **Agent Collaboration**: Multiple agents can use the tool simultaneously through shared resource management
- **Memory Integration**: Supports coordination with CrewMemoryManager for persistent research sessions
- **Tool Composition**: Can be combined with other tools (PDF processing, content analysis) in agent workflows
- **Workflow Orchestration**: Integrates seamlessly with CrewAI task and crew management systems

## Usage Examples

### Basic Web Search for ESG Content:
```python
from crewai_tools import SearchWebTool

# Initialize the tool
search_tool = SearchWebTool()

# Search for corporate sustainability reports
results = search_tool._run("Apple Inc sustainability report 2024", 5)
```

### Multi-Agent Integration:
```python
from crewai import Agent, Task, Crew
from tools_new.search_web import SearchWebTool

# Create ESG research agent with search capabilities
esg_researcher = Agent(
    role="ESG Research Analyst",
    goal="Discover corporate ESG documents and sustainability reports",
    backstory="Expert in ESG research and corporate sustainability analysis",
    tools=[SearchWebTool()],
    verbose=True
)

# Create research task
research_task = Task(
    description="Find recent ESG reports for major tech companies",
    agent=esg_researcher,
    expected_output="List of relevant ESG documents with URLs and descriptions"
)

# Execute research workflow
crew = Crew(agents=[esg_researcher], tasks=[research_task])
results = crew.kickoff()
```

## System Architecture Context

### Analytics Backend Integration:
This tool fits within the broader EkoIntelligence ESG analysis system:
- **Python Orchestration Layer**: Part of the analytics backend for automated ESG research
- **Document Discovery Pipeline**: Initial stage of comprehensive document processing workflow  
- **Multi-Agent Research**: Enables AI agents to systematically discover and analyze corporate ESG content
- **Database Integration**: Search activities tracked in analytics database for research session management

### ESG Analysis Workflow:
The search results support downstream ESG analysis components:
1. **Content Discovery**: Web search identifies potentially relevant ESG documents and reports
2. **Document Processing**: URLs feed into PDF processing and text extraction systems
3. **Content Analysis**: Extracted content analyzed for ESG claims, greenwashing, and compliance issues
4. **Risk Assessment**: Findings contribute to corporate risk scoring and ESG rating systems

### Performance and Reliability:
- **API Rate Limiting**: Respects Google Custom Search API constraints for sustainable usage
- **Error Resilience**: Handles API failures gracefully without disrupting multi-agent workflows
- **Resource Efficiency**: Leverages shared WebTools instance for optimal memory and connection management
- **Logging Integration**: Comprehensive logging supports debugging and performance monitoring

## Related Components
- **Base Module** (`base.py`): Provides shared `web_tools` instance and common dependencies
- **WebTools Class** (`eko.entities.agent.tools.WebTools`): Core web search implementation
- **CrewMemoryManager** (`../memory.py`): Persistent session tracking for multi-turn research
- **ESG Analysis Pipeline** (`eko.analysis_v2/`): Downstream processing of discovered content
- **Document Processing** (`eko.scrape/`): PDF and document text extraction for discovered URLs

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/agents CrewAI Multi-Agent System Architecture
@see https://developers.google.com/custom-search/v1/overview Google Custom Search API Documentation
@see https://docs.pydantic.dev/ Pydantic Data Validation Documentation
@see ./base.py Shared Web Tools and Dependencies Module
@see ../../../entities/agent/tools.py WebTools Class Implementation
@see ../../../analysis_v2/ ESG Analysis and Processing Pipeline
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for web search integration in ESG research and corporate intelligence workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from typing import Type

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from .base import web_tools


class SearchWebInput(BaseModel):
    """Input schema for the search_web tool."""
    query: str = Field(..., description="The search query")
    num_results: int = Field(20, description="Number of search results to return (default: 20)")


class SearchWebTool(BaseTool):
    """Tool for searching the web."""
    
    name: str = "search_web"
    description: str = "Search the web for information"
    args_schema: Type[BaseModel] = SearchWebInput
    
    def _run(self, query: str, num_results: int = 20) -> str:
        """
        Search the web for information.

        Args:
            query: The search query
            num_results: Number of search results to return

        Returns:
            str: The search results
        """
        try:
            results = web_tools.search_web(query, num_results)
            formatted_results = []

            for i, result in enumerate(results, 1):
                formatted_results.append(f"{i}. {result['title']}")
                formatted_results.append(f"   URL: {result['link']}")
                formatted_results.append(f"   Snippet: {result['snippet']}")
                formatted_results.append("")

            return "\n".join(formatted_results)
        except Exception as e:
            logger.error(f"Error searching web: {e}")
            return f"Error searching web: {str(e)}"
