"""
CrewAI ESG Insight Extraction Tool for Multi-Agent Corporate Research Workflows

This module provides the `ExtractESGInsightsTool` class, a specialized CrewAI tool for extracting 
Environmental, Social, and Governance (ESG) insights from unstructured text content during 
automated corporate research workflows. The tool serves as an intelligent content analyzer for 
AI agents within the EkoIntelligence ESG analysis platform, enabling systematic identification 
and categorization of sustainability-related statements from corporate documents, reports, and 
web content for comprehensive ESG assessment and greenwashing detection.

## Core Functionality

### Text Analysis and Keyword-Based Extraction
- **Multi-Domain ESG Categorization**: Systematically identifies Environmental, Social, and Governance content through comprehensive keyword matching
- **Sentence-Level Analysis**: Processes text at sentence granularity to ensure precise context preservation and accurate insight extraction
- **Content Length Management**: Handles large documents through intelligent truncation (50,000 character limit) while preserving content integrity
- **Deduplication Logic**: Advanced text hash-based duplicate prevention to avoid processing identical content multiple times

### ESG Category Framework
The tool implements comprehensive ESG categorization based on industry-standard frameworks:

#### Environmental Keywords:
- **Climate Action**: "environment", "climate", "carbon", "emission", "renewable"
- **Resource Management**: "sustainable", "green", "waste", "water"
- **Pollution Control**: Environmental damage, contamination, and remediation insights

#### Social Keywords:
- **Workforce Management**: "social", "diversity", "inclusion", "employee", "labor"  
- **Community Impact**: "community", "human rights", "health", "safety"
- **Stakeholder Engagement**: Social responsibility initiatives and community programs

#### Governance Keywords:
- **Corporate Structure**: "governance", "board", "compliance", "ethics"
- **Risk Management**: "risk", "transparency", "corruption"
- **Executive Oversight**: "executive", "compensation", leadership accountability

### Content Processing Pipeline
1. **Input Validation**: Company name and text content validation with comprehensive error handling
2. **Content Preprocessing**: Text normalization and sentence segmentation for accurate analysis
3. **Keyword Matching**: Multi-category keyword detection with case-insensitive pattern matching
4. **Insight Extraction**: Context-aware sentence extraction preserving meaning and attribution
5. **Result Formatting**: Structured output with numbered insights organized by ESG category
6. **Memory Integration**: Automatic insight tracking through CrewMemoryManager for persistent storage

## CrewAI Framework Integration

### Tool Architecture Design
The `ExtractESGInsightsTool` implements CrewAI best practices by inheriting from `BaseTool`:
- **Pydantic Input Validation**: Uses `ExtractESGInsightsInput` schema for type-safe parameter validation
- **Standardized Tool Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **Flexible Input Handling**: Supports company name, text content, and redo parameter for reprocessing control
- **Hash-Based Deduplication**: MD5 hashing prevents duplicate analysis of identical text content
- **Memory Manager Integration**: Optional memory manager integration for insight persistence and session tracking

### Multi-Agent Research Coordination
In collaborative ESG research environments, the tool enables:
- **Shared Content Analysis**: Multiple agents can process different sections of corporate content simultaneously
- **Duplicate Prevention**: Cross-agent coordination prevents redundant analysis of identical text segments
- **Memory Synchronization**: Shared memory manager ensures consistent insight tracking across research agents
- **Progress Monitoring**: Text hash caching provides efficient progress tracking and resumption capabilities

### Advanced Input Processing Features
- **Text Sample Hashing**: Efficient duplicate detection using first 1000 characters for hash computation
- **Memory-Based Deduplication**: Cross-references existing insights in memory to prevent duplicate processing
- **Redo Functionality**: Override parameter allows forced reanalysis of previously processed content
- **Error Recovery**: Graceful exception handling with detailed logging for debugging and monitoring

## Memory Management and Persistence Integration

### CrewMemoryManager Integration
The tool seamlessly integrates with the broader memory management system:
- **Insight Tracking**: Automatic storage of discovered insights through `track_insight()` method calls
- **Session Persistence**: Insights linked to specific research sessions and company analysis contexts
- **Source Attribution**: Maintains complete source text samples for insight provenance and validation
- **Confidence Scoring**: Assigns standardized confidence levels (0.8) for insight reliability assessment

### Database Integration Architecture
Insights flow through the complete EkoIntelligence data pipeline:
- **Primary Storage**: `agent_insights` table in analytics database for structured ESG insight storage
- **Session Linking**: Foreign key relationships with `agent_sessions` for research workflow tracking
- **Memory Synchronization**: Dual storage in JSON memory files and PostgreSQL database for reliability
- **Data Transfer**: Integration with `xfer_` table pipeline for customer dashboard accessibility

## Advanced Text Processing Capabilities

### Content Scalability Features
- **Large Document Handling**: Processes documents up to 50,000 characters with intelligent truncation
- **Performance Optimization**: Efficient keyword matching algorithms minimize processing overhead
- **Batch Processing**: Supports sequential processing of multiple documents within single research sessions
- **Resource Management**: Hash-based caching reduces redundant text processing and improves throughput

### Natural Language Processing Integration
- **Sentence Boundary Detection**: Accurate sentence segmentation preserving contextual meaning
- **Keyword Pattern Matching**: Case-insensitive pattern matching with phrase-level recognition
- **Context Preservation**: Maintains sentence-level context for accurate insight interpretation
- **Content Quality Assurance**: Truncation indicators preserve document completeness information

## ESG Research Workflow Integration

### Corporate Analysis Pipeline
The tool supports comprehensive corporate ESG assessment workflows:
1. **Document Discovery**: Agents identify corporate sustainability reports, regulatory filings, and web content
2. **Content Preprocessing**: Large documents segmented and prepared for systematic analysis
3. **Multi-Category Analysis**: Simultaneous Environmental, Social, and Governance insight extraction
4. **Quality Assessment**: Confidence scoring and source attribution for insight reliability evaluation
5. **Memory Integration**: Persistent storage enabling cumulative insight building across research sessions
6. **Reporting Support**: Structured output feeds downstream analysis and reporting systems

### Greenwashing Detection Support
The tool contributes to systematic greenwashing identification:
- **Claim Identification**: Environmental statements extracted for verification against actual performance
- **Social Impact Analysis**: Social responsibility claims captured for evidence-based validation
- **Governance Transparency**: Corporate governance statements analyzed for accountability assessment
- **Evidence Building**: Systematic insight collection supports comprehensive greenwashing analysis

## Technical Architecture and Performance

### Hash-Based Deduplication System
- **MD5 Content Hashing**: Efficient duplicate detection using company name and text sample combination
- **In-Memory Caching**: Fast hash lookup prevents redundant processing during active research sessions
- **Memory Integration**: Cross-references existing insights for comprehensive duplicate prevention
- **Override Capability**: Redo parameter enables forced reprocessing when content updates occur

### Error Handling and Reliability
- **Exception Safety**: Comprehensive exception handling with detailed error logging via Loguru
- **Graceful Degradation**: Continues operation during memory manager failures with direct processing
- **Input Validation**: Robust parameter validation prevents processing errors and data corruption
- **Recovery Procedures**: Automatic error recovery with informative user feedback and logging

### Performance Optimization Strategies
- **Efficient Keyword Matching**: Optimized pattern matching algorithms minimize computational overhead
- **Memory-Conscious Processing**: Text sample hashing reduces memory usage for large document analysis
- **Batch Optimization**: Supports efficient processing of multiple insights through memory manager
- **Resource Management**: Proper resource cleanup and memory management for long-running research sessions

## System Architecture Context

### EkoIntelligence Platform Integration
The ESG insight extraction tool operates within the broader corporate sustainability analysis ecosystem:
- **Analytics Backend**: Python-based CrewAI orchestration for automated corporate ESG research
- **Database Layer**: Analytics database storage with structured ESG insight tables and indexing
- **Memory System**: Integration with CrewMemoryManager and persistent JSON memory files
- **Transfer Pipeline**: Insight data flows through `xfer_` tables to customer-facing dashboard systems
- **Reporting Integration**: Extracted insights feed ESG scoring algorithms and greenwashing detection systems

### Corporate Research Workflow Architecture
1. **Research Session Initialization**: CrewAI agents configured with ESG insight extraction capabilities
2. **Multi-Source Content Analysis**: Agents analyze corporate websites, sustainability reports, regulatory filings
3. **Systematic Insight Extraction**: Tool processes content across Environmental, Social, Governance categories
4. **Memory Coordination**: Shared memory system prevents duplicate processing and maintains research state
5. **Database Persistence**: Insights stored in analytics database with complete source attribution and metadata
6. **Analysis Integration**: Extracted insights feed downstream ESG scoring and corporate risk assessment systems

## Key Dependencies and Integration Points

### CrewAI Framework Components
- **CrewAI BaseTool**: Foundation class providing standardized tool interface and agent integration capabilities
- **Pydantic Models**: Type-safe data validation using `BaseModel` and `Field` for comprehensive input schemas
- **Tool Registration**: Automatic tool discovery and registration within CrewAI agent configurations

### Supporting Infrastructure
- **Python hashlib**: MD5 hashing functionality for efficient content deduplication and caching
- **Loguru**: Advanced logging framework providing structured logs with exception tracing and debugging
- **Memory Management**: Integration with `CrewMemoryManager` for persistent insight storage and session tracking

### Database and Analytics Integration
- **Analytics Database**: PostgreSQL storage via `get_bo_conn()` connection management
- **Agent Tables**: `agent_insights`, `agent_sessions`, `agent_reports` for comprehensive research tracking
- **Data Pipeline**: Integration with broader EkoIntelligence data processing and analysis workflows

## Usage Examples and Integration Patterns

### Basic Agent Configuration
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_new.extract_esg_insights import ExtractESGInsightsTool
from crewai import Agent

# Initialize memory management
memory_manager = CrewMemoryManager("Apple Inc", "esg_analysis_2024")
esg_tool = ExtractESGInsightsTool(memory_manager=memory_manager)

# Configure ESG analysis agent
esg_analyst = Agent(
    role="ESG Content Analyst",
    goal="Extract and categorize ESG insights from corporate content",
    tools=[esg_tool],
    memory=True,
    verbose=True
)
```

### Multi-Agent Research Coordination
```python
# Coordinated multi-agent ESG content analysis
environmental_agent = Agent(
    role="Environmental Analyst", 
    tools=[ExtractESGInsightsTool(memory_manager=shared_memory)]
)
social_agent = Agent(
    role="Social Impact Analyst", 
    tools=[ExtractESGInsightsTool(memory_manager=shared_memory)]
)
governance_agent = Agent(
    role="Governance Analyst", 
    tools=[ExtractESGInsightsTool(memory_manager=shared_memory)]
)

# All agents contribute to shared ESG insight repository
crew = Crew(agents=[environmental_agent, social_agent, governance_agent])
results = crew.kickoff()
```

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/memory CrewAI Memory System Architecture  
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see https://docs.python.org/3/library/hashlib.html Python Hashlib MD5 Documentation
@see backoffice/src/eko/agent/crewai/crawl/memory.py CrewMemoryManager Implementation
@see backoffice/src/eko/agent/crewai/crawl/tools_new/base.py Shared Base Module for CrewAI Tools
@see backoffice/src/eko/agent/crewai/crawl/tools_mem/track_insight.py Insight Tracking Tool Implementation
@see backoffice/src/eko/db/get_bo_conn Database Connection Management
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for extracting and categorizing ESG insights from corporate text content during automated sustainability research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import hashlib
from typing import Type, Optional, Any, Set

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field


class ExtractESGInsightsInput(BaseModel):
    """Input schema for the extract_esg_insights tool."""
    text: str = Field(..., description="The text to analyze for ESG insights")
    company_name: str = Field(..., description="The name of the company being researched")
    redo:str = Field(..., description="Set to 'yes' to force re-analysis even if this text was already analyzed")


class ExtractESGInsightsTool(BaseTool):
    """Tool for extracting ESG insights from text."""
    
    name: str = "extract_esg_insights"
    description: str = "Extract ESG insights from text. Use parameter redo=True to force re-analysis of previously analyzed text."
    args_schema: Type[BaseModel] = ExtractESGInsightsInput
    _memory_manager: Optional[Any] = None
    
    def __init__(self, **kwargs):
        memory_manager = kwargs.pop('memory_manager')
        super().__init__(**kwargs)
        self._memory_manager = memory_manager
        # Cache for text hashes we've already processed
        self._processed_text_hashes: Set[str] = set()
    
    def _get_text_hash(self, text: str, company_name: str) -> str:
        """Generate a hash for the text+company combo to identify if we've seen it before"""
        # Use first 1000 chars to keep hash computation fast while still being unique enough
        text_sample = text[:1000] if len(text) > 1000 else text
        combined = f"{company_name}:{text_sample}"
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _check_already_processed(self, text: str, company_name: str) -> bool:
        """Check if we've already processed this text"""
        text_hash = self._get_text_hash(text, company_name)
        
        # First check our in-memory cache
        if text_hash in self._processed_text_hashes:
            return True
        
        # Then check if we have insights in memory with matching descriptions
        if self._memory_manager:
            memory = self._memory_manager._load_memory()
            insights = memory.get("insights", [])
            
            # Extract a small sample of the text for comparison
            text_sample = text[:200].lower() if len(text) > 200 else text.lower()
            
            # Check if we have insights that might have come from this text
            for insight in insights:
                source_text = insight.get("source_text", "")
                if source_text and text_sample in source_text.lower():
                    # Add to our cache for future checks
                    self._processed_text_hashes.add(text_hash)
                    return True
        
        return False
    
    def _run(self, text: str, company_name: str, redo: str) -> str:
        """
        Extract ESG insights from text.

        Args:
            text: The text to analyze
            company_name: The name of the company being researched
            redo: Whether to force re-analysis even if this text was already analyzed

        Returns:
            str: The extracted ESG insights
        """
        try:
            # Check if we've already processed this text and redo is False
            if  redo != 'yes' and self._check_already_processed(text, company_name):
                return "We've already analyzed this text before. Use redo=True if you want to analyze it again."
            
            # Add to our processed texts
            text_hash = self._get_text_hash(text, company_name)
            self._processed_text_hashes.add(text_hash)
            
            logger.info(f"Extracting ESG insights for {company_name}")

            # Truncate if too long
            if len(text) > 50000:
                text = text[:50000] + "...\n[Content truncated due to length]"

            # Simple keyword-based extraction
            env_keywords = ["environment", "climate", "carbon", "emission", "renewable", "sustainable", "green", "waste", "water"]
            social_keywords = ["social", "community", "diversity", "inclusion", "employee", "human rights", "labor", "health", "safety"]
            gov_keywords = ["governance", "board", "compliance", "ethics", "risk", "transparency", "corruption", "executive", "compensation"]

            # Extract sentences containing keywords
            sentences = text.split(". ")
            env_insights = [s for s in sentences if any(k in s.lower() for k in env_keywords)]
            social_insights = [s for s in sentences if any(k in s.lower() for k in social_keywords)]
            gov_insights = [s for s in sentences if any(k in s.lower() for k in gov_keywords)]

            # Format results
            result = f"ESG Insights for {company_name}:\n\n"

            result += "Environmental Insights:\n"
            for i, insight in enumerate(env_insights[:10], 1):
                result += f"{i}. {insight}.\n"
                
                # Track the insight in memory if we have a memory manager
                if self._memory_manager:
                    insight_data = {
                        "category": "Environmental",
                        "behavior_type": "statement",
                        "description": insight,
                        "source_text": text[:200],  # Store a sample of the source text
                        "source_url": "",  # We don't have the URL here
                        "source_title": f"ESG Analysis for {company_name}",
                        "confidence": 0.8
                    }
                    self._memory_manager.track_insight(insight_data)

            result += "\nSocial Insights:\n"
            for i, insight in enumerate(social_insights[:10], 1):
                result += f"{i}. {insight}.\n"
                
                # Track the insight
                if self._memory_manager:
                    insight_data = {
                        "category": "Social",
                        "behavior_type": "statement",
                        "description": insight,
                        "source_text": text[:200],
                        "source_url": "",
                        "source_title": f"ESG Analysis for {company_name}",
                        "confidence": 0.8
                    }
                    self._memory_manager.track_insight(insight_data)

            result += "\nGovernance Insights:\n"
            for i, insight in enumerate(gov_insights[:10], 1):
                result += f"{i}. {insight}.\n"
                
                # Track the insight
                if self._memory_manager:
                    insight_data = {
                        "category": "Governance",
                        "behavior_type": "statement",
                        "description": insight,
                        "source_text": text[:200],
                        "source_url": "",
                        "source_title": f"ESG Analysis for {company_name}",
                        "confidence": 0.8
                    }
                    self._memory_manager.track_insight(insight_data)

            return result
        except Exception as e:
            logger.error(f"Error extracting ESG insights: {e}")
            return f"Error extracting ESG insights: {str(e)}"
