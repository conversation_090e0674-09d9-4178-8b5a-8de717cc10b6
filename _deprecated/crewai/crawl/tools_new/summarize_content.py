"""
CrewAI Content Summarization Tool for Multi-Agent ESG Research Workflows

This module provides the `SummarizeContentTool` class, a specialized CrewAI tool for intelligent 
content summarization during automated ESG (Environmental, Social, Governance) research operations 
within the EkoIntelligence platform. The tool serves as an extractive summarization engine for 
AI agents performing systematic corporate research, enabling efficient processing of large-scale 
textual content from corporate documents, sustainability reports, regulatory filings, and web 
content for comprehensive ESG analysis and greenwashing detection workflows.

## Core Functionality

### Extractive Summarization Engine
- **Sentence-Level Analysis**: Processes text through intelligent sentence segmentation for granular summarization control
- **Length-Based Optimization**: Configurable word-count limits (default: 500 words) with automatic sentence count calculation
- **Content Preservation**: Maintains sentence integrity and contextual meaning through careful boundary detection
- **Structured Output**: Returns formatted summaries with sentence count metadata for analysis tracking

### Advanced Content Processing Features  
- **Large Document Handling**: Supports content up to 50,000 characters with intelligent truncation and continuation markers
- **Deduplication System**: Hash-based duplicate detection prevents redundant processing of identical content
- **Memory Integration**: Seamless coordination with CrewMemoryManager for persistent session tracking
- **Force Re-processing**: Override capability for content re-analysis when source material updates

### Intelligent Selection Algorithm
The tool implements a sophisticated extractive summarization strategy:

1. **Leading Context Capture**: Always includes the first sentence to preserve primary context and document purpose
2. **Distributed Sampling**: Evenly distributes sentence selection across document length to maintain comprehensive coverage
3. **Content Density Optimization**: Calculates optimal sentence count based on target word limits (20-word average per sentence)
4. **Boundary Preservation**: Maintains sentence completeness with proper punctuation and formatting

## CrewAI Framework Integration

### Tool Architecture Design
The `SummarizeContentTool` follows CrewAI best practices by inheriting from `BaseTool`:
- **Pydantic Input Validation**: Uses `SummarizeContentInput` schema for type-safe parameter validation  
- **Standardized Tool Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **Flexible Input Handling**: Supports text content, word limits, and force re-processing parameters
- **Hash-Based Deduplication**: MD5 hashing with text sampling prevents duplicate analysis overhead
- **Memory Manager Integration**: Optional memory manager coordination for persistent session tracking

### Multi-Agent Research Coordination  
In collaborative ESG research environments, the tool enables:
- **Content Standardization**: Multiple agents can process different documents using consistent summarization methods
- **Duplicate Prevention**: Cross-agent coordination prevents redundant summarization of identical content segments
- **Memory Synchronization**: Shared memory manager ensures consistent content tracking across research sessions
- **Workflow Optimization**: Efficient content processing reduces token costs and analysis time

### Performance Optimization Features
- **Text Sample Hashing**: Efficient duplicate detection using first 1000 characters for hash computation  
- **In-Memory Caching**: Fast duplicate lookup prevents redundant processing during active research sessions
- **Large Content Handling**: Intelligent truncation maintains summary quality while managing processing limits
- **Resource Management**: Optimal memory usage through hash-based tracking and minimal state storage

## ESG Research Workflow Integration

### Corporate Document Processing
The tool supports systematic corporate ESG content analysis:
1. **Document Discovery**: AI agents identify corporate sustainability reports, regulatory filings, and ESG documentation
2. **Content Preprocessing**: Large documents segmented and prepared for efficient summarization processing  
3. **Extractive Analysis**: Key sentences extracted while preserving document structure and contextual meaning
4. **Quality Assurance**: Summary formatting with metadata supports downstream analysis and validation
5. **Memory Persistence**: Processed content tracked to prevent duplication and enable cumulative research

### Multi-Document Research Support
In comprehensive corporate analysis workflows:
- **Batch Processing**: Sequential summarization of multiple corporate documents with session persistence
- **Consistent Methodology**: Standardized extractive summarization ensures comparable analysis across documents
- **Content Deduplication**: Hash-based tracking prevents processing of duplicate content across document sources
- **Progress Tracking**: Memory integration supports research progress monitoring and session resumption

## Technical Architecture and Performance

### Hash-Based Deduplication System
- **MD5 Content Hashing**: Efficient duplicate detection combining text content with word limit parameters
- **Text Sampling Strategy**: Uses first 1000 characters for hash computation, balancing uniqueness with performance
- **In-Memory Caching**: Maintains processed content hashes for immediate duplicate detection during sessions
- **Override Capability**: Force re-processing parameter bypasses deduplication when content updates occur

### Content Length Management 
- **Scalable Processing**: Handles documents up to 50,000 characters with intelligent truncation strategies
- **Preservation Indicators**: Truncation markers maintain document completeness information for analysis context
- **Sentence Boundary Detection**: Accurate sentence segmentation preserves semantic meaning and readability
- **Word Count Estimation**: Dynamic sentence selection based on target word limits and average sentence length

### Error Handling and Reliability
- **Exception Safety**: Comprehensive exception handling with detailed error logging via Loguru framework
- **Graceful Degradation**: Continues operation during memory manager failures with direct summarization processing
- **Input Validation**: Robust parameter validation prevents processing errors and ensures consistent output quality
- **Recovery Procedures**: Automatic error recovery with informative user feedback and structured logging

## System Architecture Context

### EkoIntelligence Platform Integration  
The content summarization tool operates within the broader ESG analysis ecosystem:
- **Analytics Backend**: Python-based CrewAI orchestration system for automated corporate ESG research
- **Database Layer**: Analytics database storage with structured content processing and research session tracking
- **Memory System**: Integration with CrewMemoryManager and persistent JSON memory files for session coordination
- **Processing Pipeline**: Summarized content feeds ESG scoring algorithms and greenwashing detection systems
- **Reporting Integration**: Summary outputs support customer-facing ESG analysis dashboards and reporting

### CrewAI Research Workflow Architecture
1. **Content Discovery**: AI agents identify corporate ESG documentation, sustainability reports, regulatory filings
2. **Document Processing**: Large documents segmented and prepared for systematic summarization analysis
3. **Extractive Summarization**: Tool processes content using intelligent sentence selection and length optimization
4. **Memory Coordination**: Shared memory system prevents duplicate processing and maintains research state consistency
5. **Analysis Integration**: Summarized content feeds downstream ESG analysis, scoring, and detection algorithms
6. **Quality Assurance**: Summary metadata and formatting support validation and analysis workflow integration

## Key Dependencies and Integration Points

### CrewAI Framework Components
- **CrewAI BaseTool**: Foundation class providing standardized tool interface and multi-agent integration
- **Pydantic Models**: Type-safe input validation using `BaseModel` and `Field` for comprehensive parameter schemas
- **Tool Registration**: Automatic tool discovery and registration within CrewAI agent configuration systems

### Supporting Infrastructure
- **Python hashlib**: MD5 hashing functionality for efficient content deduplication and session caching
- **Loguru**: Advanced logging framework providing structured logs with exception tracing and debugging capabilities
- **Memory Management**: Integration with `CrewMemoryManager` for persistent content tracking and session coordination

### Content Processing Architecture
- **Text Processing**: Sentence boundary detection and content segmentation for extractive summarization
- **Length Optimization**: Dynamic word count calculation and sentence selection for optimal summary generation
- **Quality Control**: Content truncation indicators and formatting preservation for downstream analysis integration

## Usage Examples and Integration Patterns

### Basic Agent Configuration
```python
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_new.summarize_content import SummarizeContentTool
from crewai import Agent

# Initialize memory management
memory_manager = CrewMemoryManager("Tesla Inc", "esg_research_2024")
summarizer_tool = SummarizeContentTool(memory_manager=memory_manager)

# Configure content processing agent
content_processor = Agent(
    role="Content Summarization Specialist",
    goal="Generate concise summaries from corporate ESG documentation",
    tools=[summarizer_tool],
    memory=True,
    verbose=True
)
```

### Multi-Agent Research Coordination
```python  
# Coordinated multi-agent content processing workflow
doc_analyzer = Agent(
    role="Document Analyst",
    tools=[SummarizeContentTool(memory_manager=shared_memory)]
)
report_processor = Agent(
    role="Report Processor",
    tools=[SummarizeContentTool(memory_manager=shared_memory)]
)
content_curator = Agent(
    role="Content Curator", 
    tools=[SummarizeContentTool(memory_manager=shared_memory)]
)

# All agents contribute to shared content summarization with deduplication
crew = Crew(agents=[doc_analyzer, report_processor, content_curator])
results = crew.kickoff()
```

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/memory CrewAI Memory System Architecture
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see https://docs.python.org/3/library/hashlib.html Python Hashlib MD5 Documentation
@see backoffice/src/eko/agent/crewai/crawl/memory.py CrewMemoryManager Implementation
@see backoffice/src/eko/agent/crewai/crawl/tools_new/base.py Shared Base Module for CrewAI Tools
@see backoffice/src/eko/agent/crewai/crawl/tools_new/all_tools.py Tool Registry and Factory Module
@see backoffice/src/eko/analysis_v2/ ESG Analysis Pipeline for Content Processing
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for intelligent extractive summarization of corporate ESG content during automated research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

import hashlib
from typing import Type, Optional, Any, Set

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field


class SummarizeContentInput(BaseModel):
    """Input schema for the summarize_content tool."""
    text: str = Field(..., description="The text to summarize")
    max_length: int = Field(default=500, description="Maximum length of the summary in words")
    redo: Optional[bool] = Field(False, description="Set to True to force re-summarization even if this text was already summarized")


class SummarizeContentTool(BaseTool):
    """Tool for summarizing content to a specified maximum length."""

    name: str = "summarize_content"
    description: str = "Summarize content to a specified maximum length. Use parameter redo=True to force re-summarization of previously summarized text."
    args_schema: Type[BaseModel] = SummarizeContentInput
    memory_manager: Optional[Any] = None

    def __init__(self, **kwargs):
        memory_manager = kwargs.pop('memory_manager', None) if 'memory_manager' in kwargs else None
        super().__init__(**kwargs)
        # Store reference to memory_manager
        self._memory_manager = memory_manager
        # Cache for text hashes we've already processed
        self._processed_text_hashes: Set[str] = set()

    def _get_text_hash(self, text: str, max_length: int) -> str:
        """Generate a hash for the text+max_length combo to identify if we've seen it before"""
        # Use first 1000 chars to keep hash computation fast while still being unique enough
        text_sample = text[:1000] if len(text) > 1000 else text
        combined = f"{text_sample}:{max_length}"
        return hashlib.md5(combined.encode()).hexdigest()

    def _run(self, text: str, max_length: int = 500, redo: bool = False) -> str:
        """
        Summarize content to a specified maximum length.

        Args:
            text: The text to summarize
            max_length: Maximum length of the summary in words
            redo: Whether to force re-summarization even if this text was already summarized

        Returns:
            str: The summarized content
        """
        try:
            # Check if we've already processed this text with the same parameters
            text_hash = self._get_text_hash(text, max_length)
            if not redo and text_hash in self._processed_text_hashes:
                return "We've already summarized this text before. Use redo=True if you want to summarize it again."
            
            # Add to our processed texts
            self._processed_text_hashes.add(text_hash)
            
            logger.info(f"Summarizing content to {max_length} words")

            # Truncate if too long
            if len(text) > 50000:
                text = text[:50000] + "...\n[Content truncated due to length]"

            # Simple extractive summarization
            sentences = text.split(". ")

            # Calculate the number of sentences to include based on max_length
            # Assuming average sentence length of 20 words
            num_sentences = min(len(sentences), max(1, max_length // 20))

            # Take the first sentence (often contains the main point)
            summary = [sentences[0]]

            # Take evenly distributed sentences from the rest of the text
            if len(sentences) > 1 and num_sentences > 1:
                step = len(sentences) // (num_sentences - 1)
                for i in range(1, num_sentences):
                    idx = min(i * step, len(sentences) - 1)
                    summary.append(sentences[idx])

            # Join the sentences
            summary_text = ". ".join(summary)

            # Add a period at the end if needed
            if not summary_text.endswith("."):
                summary_text += "."

            return f"Summary ({len(summary)} sentences):\n\n{summary_text}"
        except Exception as e:
            logger.error(f"Error summarizing content: {e}")
            return f"Error summarizing content: {str(e)}"
