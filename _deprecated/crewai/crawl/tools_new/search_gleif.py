"""
CrewAI GLEIF Legal Entity Identifier (LEI) Search Tool for Corporate Research Workflows

This module provides the `SearchGLEIFTool` class, a specialized CrewAI tool for searching and retrieving 
Legal Entity Identifier (LEI) data from the Global Legal Entity Identifier Foundation (GLEIF) database 
during automated corporate research workflows. The tool enables AI agents within the EkoIntelligence ESG 
analysis platform to systematically identify and gather corporate entity information, enabling accurate 
entity resolution and corporate structure mapping essential for comprehensive ESG assessment and 
regulatory compliance analysis.

## Core Functionality

### GLEIF Database Integration
The tool provides seamless integration with GLEIF's comprehensive legal entity database:
- **Company Name Resolution**: Searches GLEIF database using company names to identify corresponding LEI records
- **Corporate Entity Mapping**: Retrieves detailed legal entity information including jurisdictions, addresses, and legal forms
- **Relationship Discovery**: Enables identification of corporate hierarchies and subsidiary relationships
- **Regulatory Compliance**: Supports KYC (Know Your Customer) and AML (Anti-Money Laundering) compliance requirements

### Legal Entity Identifier (LEI) System
LEI codes provide standardized identification for legal entities engaging in financial transactions:
- **Global Uniqueness**: Each LEI code uniquely identifies a legal entity worldwide (ISO 17442 standard)
- **Regulatory Transparency**: Facilitates regulatory reporting and risk management across jurisdictions
- **Corporate Structure**: Enables mapping of complex corporate ownership structures and relationships
- **Entity Verification**: Provides authoritative source for legal entity status and registration information

## CrewAI Framework Integration

### Tool Architecture Design
The `SearchGLEIFTool` implements CrewAI best practices by inheriting from `BaseTool`:
- **Pydantic Input Validation**: Uses `SearchGLEIFInput` schema for type-safe parameter validation
- **Standardized Tool Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **Flexible Query Parameters**: Supports company name queries with configurable result limits (default: 20 results)
- **JSON Serialization**: Returns structured data via jsonpickle encoding for agent consumption and processing

### Multi-Agent Research Coordination
In collaborative corporate research environments, the tool enables:
- **Shared Entity Resolution**: Multiple agents can resolve different entities simultaneously for comprehensive analysis
- **Corporate Hierarchy Mapping**: Supports systematic analysis of complex corporate structures and subsidiaries
- **Due Diligence Workflows**: Enables automated KYC and entity verification processes across research teams
- **Data Standardization**: Provides consistent entity identification across different research workflows and agents

## GLEIF API Integration Architecture

### Company Name Search Functionality
The tool leverages the GLEIF entity lookup system through the `lookup_leis_by_company_name` function:
- **Fuzzy Matching**: Searches using company names with intelligent matching algorithms
- **Paginated Results**: Handles large result sets efficiently through pagination control
- **Result Filtering**: Configurable result limits (up to 100 results per query) for performance optimization
- **Error Handling**: Comprehensive exception handling with graceful degradation and retry logic

### Data Structure and Processing
- **LEI Record Objects**: Returns structured `LEIRecord` Pydantic models with comprehensive entity information
- **JSON Serialization**: Uses jsonpickle for safe serialization preserving data integrity and type information
- **Memory Efficiency**: Optimized for processing large numbers of entity records during bulk analysis operations
- **Type Safety**: Full type hints and Pydantic validation ensure data consistency and error prevention

## Corporate Research Workflow Integration

### ESG Analysis Support
The GLEIF search tool supports comprehensive ESG research workflows:
1. **Entity Identification**: Resolves company names to authoritative LEI identifiers for accurate entity tracking
2. **Corporate Structure Analysis**: Maps parent-subsidiary relationships for comprehensive ESG scope assessment
3. **Jurisdictional Analysis**: Identifies regulatory jurisdictions and compliance requirements for ESG reporting
4. **Verification Support**: Provides authoritative entity information for greenwashing detection and claim verification

### Regulatory Compliance Enhancement
- **KYC Automation**: Supports automated Know Your Customer processes with authoritative entity data
- **AML Compliance**: Enables Anti-Money Laundering compliance through corporate structure transparency
- **Regulatory Reporting**: Provides standardized entity identifiers required for various regulatory submissions
- **Due Diligence Support**: Facilitates comprehensive due diligence processes with verified entity information

## Technical Implementation Details

### Search Parameter Configuration
- **Query Parameter**: Company name string for entity resolution (required field)
- **Result Limits**: Configurable number of search results (1-100, default: 20) for performance optimization
- **Pagination Handling**: Automatic pagination management for comprehensive result retrieval
- **Performance Optimization**: Intelligent result limiting balances completeness with processing efficiency

### Error Handling and Reliability
- **Exception Safety**: Comprehensive exception handling with detailed error logging via Loguru
- **Graceful Degradation**: Returns informative error messages when GLEIF API is unavailable or rate-limited
- **Input Validation**: Robust parameter validation prevents API errors and ensures data quality
- **Timeout Management**: Appropriate timeout handling for network requests and API response processing

### Data Format and Serialization
- **JSONPickle Encoding**: Safe serialization format preserving Python object types and structures
- **Agent Consumption**: Output format optimized for consumption by other CrewAI tools and agents
- **Data Integrity**: Maintains data type information and structure through serialization process
- **Memory Efficiency**: Optimized encoding reduces memory overhead during large-scale entity processing

## System Architecture Context

### EkoIntelligence Platform Integration
The GLEIF search tool operates within the broader corporate intelligence and ESG analysis ecosystem:
- **Analytics Backend**: Python-based CrewAI orchestration for automated corporate entity research
- **Entity Resolution Pipeline**: Integration with corporate entity identification and deduplication systems
- **ESG Data Enrichment**: Provides authoritative entity data for ESG analysis and reporting systems
- **Corporate Structure Analysis**: Supports complex corporate hierarchy analysis and ownership mapping

### Database and Memory Integration
- **LEI Data Caching**: Integration with `eko.entities.gleif` module provides efficient data caching and retrieval
- **Entity Knowledge Graph**: Contributes to comprehensive corporate entity knowledge graph construction
- **Research Session Tracking**: Supports integration with CrewMemoryManager for research workflow persistence
- **Data Pipeline Integration**: Entity data flows through complete analytics pipeline for comprehensive analysis

## Advanced Features and Capabilities

### Corporate Structure Discovery
The tool enables systematic analysis of complex corporate relationships:
- **Parent-Child Relationships**: Identifies corporate ownership structures and subsidiary relationships
- **Ultimate Parent Identification**: Supports discovery of ultimate beneficial ownership and control structures  
- **Cross-Jurisdictional Analysis**: Maps entities across different regulatory jurisdictions and legal frameworks
- **Merger and Acquisition Tracking**: Historical entity relationship data supports M&A analysis and tracking

### Research Quality Assurance
- **Authoritative Data Source**: GLEIF provides globally recognized authoritative entity identification
- **Data Freshness**: Access to regularly updated LEI database ensures current entity information
- **Standardized Identifiers**: ISO 17442 compliant LEI codes provide globally unique entity identification
- **Verification Support**: Enables cross-reference verification of entity information across multiple sources

## Key Dependencies and Integration Points

### GLEIF API and Data Services
- **GLEIF API Access**: Integration with Global Legal Entity Identifier Foundation public API services
- **ISO 17442 Standard**: Compliance with international standard for legal entity identification
- **Regulatory Framework**: Alignment with FSB (Financial Stability Board) LEI requirements and regulations

### CrewAI Framework Components
- **CrewAI BaseTool**: Foundation class providing standardized tool interface and agent integration capabilities
- **Pydantic Models**: Type-safe data validation using `BaseModel` and `Field` for comprehensive input schemas
- **Tool Registration**: Automatic tool discovery and registration within CrewAI agent configurations

### Supporting Infrastructure
- **jsonpickle**: Advanced Python object serialization maintaining type information and object structures
- **Loguru**: Structured logging framework providing detailed error tracking and debugging capabilities
- **requests**: HTTP client library for reliable API communication and error handling

### EkoIntelligence Integration
- **Entity Resolution**: Integration with `eko.entities.gleif` module providing comprehensive GLEIF data access
- **Caching Systems**: Multi-level caching for performance optimization and API rate limit management
- **Corporate Database**: Integration with corporate entity knowledge graph and relationship mapping systems

## Usage Examples and Integration Patterns

### Basic Agent Configuration
```python
from eko.agent.crewai.crawl.tools_new.search_gleif import SearchGLEIFTool
from crewai import Agent

# Initialize GLEIF search capabilities
gleif_tool = SearchGLEIFTool()

# Configure entity resolution agent
entity_resolver = Agent(
    role="Corporate Entity Analyst",
    goal="Resolve corporate entities and map organizational structures",
    tools=[gleif_tool],
    memory=True,
    verbose=True
)
```

### Multi-Agent Corporate Structure Analysis
```python
# Coordinated multi-agent corporate structure research
parent_company_agent = Agent(
    role="Parent Company Analyst", 
    tools=[SearchGLEIFTool()]
)
subsidiary_agent = Agent(
    role="Subsidiary Analyst", 
    tools=[SearchGLEIFTool()]
)
compliance_agent = Agent(
    role="Regulatory Compliance Analyst", 
    tools=[SearchGLEIFTool()]
)

# Comprehensive corporate structure analysis workflow
crew = Crew(agents=[parent_company_agent, subsidiary_agent, compliance_agent])
results = crew.kickoff()
```

@see https://www.gleif.org/ Global Legal Entity Identifier Foundation Official Website
@see https://www.gleif.org/en/lei-data/gleif-lei-look-up-api/access-the-api GLEIF API Documentation
@see https://www.iso.org/standard/59771.html ISO 17442:2019 Legal Entity Identifier (LEI) Standard
@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://jsonpickle.readthedocs.io/ JSONPickle Documentation
@see backoffice/src/eko/entities/gleif.py GLEIF Entity Resolution and Data Access Module
@see backoffice/src/eko/agent/crewai/crawl/tools_new/base.py Shared Base Module for CrewAI Tools
@see backoffice/src/eko/db/get_bo_conn Database Connection Management
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for searching and retrieving Legal Entity Identifier (LEI) data from GLEIF database during automated corporate research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""
from typing import Type

import jsonpickle
from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.entities.gleif import lookup_leis_by_company_name


class SearchGLEIFInput(BaseModel):
    """Input schema for the search_web tool."""
    query: str = Field(..., description="The company name to search for")
    num_results: int = Field(20, description="Number of search results to return (default: 20)")


class SearchGLEIFTool(BaseTool):
    """Tool for searching GLEIF."""
    
    name: str = "search_gleif"
    description: str = "Search GLEIF (LEI) database for company information"
    args_schema: Type[BaseModel] = SearchGLEIFInput
    
    def _run(self, query: str, num_results: int = 20) -> str:
        """
        Search the web for information.

        Args:
            query: The search query
            num_results: Number of search results to return

        Returns:
            str: The search results
        """
        try:
            return jsonpickle.encode((lookup_leis_by_company_name(query, 3,min(100, num_results), 1)), unpicklable=False)
        except (Exception, KeyboardInterrupt) as e:
            logger.error(f"Error searching GLEIF: {e}")
            return f"Error searching GLEIF: {str(e)}"
