"""
CrewAI Link Extraction Tool for ESG Web Research and Document Discovery

This module provides the `ExtractLinksTool` class, a specialized CrewAI tool designed for intelligent 
link extraction and categorization from web pages during automated ESG (Environmental, Social, Governance) 
research workflows within the EkoIntelligence analysis platform. The tool serves as a critical component 
in multi-agent web crawling operations, identifying and prioritizing links to corporate documents, 
sustainability reports, and ESG-relevant content for comprehensive corporate research and analysis.

## Core Functionality
- **Intelligent Link Categorization**: Automatically categorizes discovered links by priority - PDF documents (high), potential reports (medium), general content (low)
- **ESG-Focused Link Filtering**: Uses specialized ESG terminology and keywords to identify sustainability-related content and corporate misconduct indicators
- **Duplicate Prevention System**: Implements URL-based caching to prevent redundant processing of previously analyzed web pages
- **Priority-Based Link Organization**: Organizes extracted links into structured categories for targeted follow-up by downstream analysis agents
- **Memory Manager Integration**: Coordinates with CrewMemoryManager for persistent tracking across multi-agent research sessions
- **Comprehensive Link Analysis**: Provides detailed link text, URLs, and relevance categorization for informed agent decision-making

## ESG Research Integration

### Corporate Document Discovery
The tool specifically targets ESG-relevant documents and content:
- **PDF Document Detection**: Identifies sustainability reports, annual reports, ESG disclosures, and corporate policies
- **ESG Content Prioritization**: Uses specialized keyword matching against ESG terminology including "sustainability", "esg", "annual", "environmental", "social", "governance"
- **Corporate Misconduct Research**: Leverages extensive keyword database from key_terms module for identifying controversies, violations, and greenwashing
- **Document Type Classification**: Distinguishes between high-value documents (PDFs), potential reports (keyword-matched pages), and general content
- **Regulatory Filing Discovery**: Identifies links to SEC filings, financial statements, and regulatory documents

### Multi-Agent Research Coordination
Essential component of the collaborative ESG research workflow:
- **Cross-Agent Link Sharing**: Extracted links are available to other agents in the CrewAI research team
- **Targeted Content Discovery**: Enables focused follow-up by specialized document processing and analysis agents  
- **Research Efficiency**: Prevents duplicate link extraction efforts across multiple research agents
- **Priority-Based Workflow**: High-priority links (PDFs) are processed first, optimizing research time and resources
- **Session Continuity**: Maintains link discovery state across extended research sessions through memory integration

## CrewAI Framework Integration

### BaseTool Implementation
The `ExtractLinksTool` follows CrewAI architectural patterns:
- **Pydantic Schema Integration**: Uses `ExtractLinksInput` BaseModel for type-safe parameter validation with Field descriptors
- **StandardTool Interface**: Implements required `name`, `description`, and `args_schema` attributes for agent tool integration
- **Memory Manager Support**: Optional memory_manager parameter enables integration with persistent session tracking
- **Error-Safe Execution**: Comprehensive exception handling with Loguru logging for reliable multi-agent operations
- **Cache Management**: Maintains internal `_processed_urls` set for duplicate prevention across tool instances

### Multi-Agent System Architecture
Designed for seamless integration in collaborative agent environments:
- **Shared Resource Access**: Uses common `web_tools` instance from base module for consistent web interaction
- **Inter-Agent Communication**: Processed URLs are tracked globally to coordinate efforts across research agents
- **Tool Chain Integration**: Extracted links feed downstream tools like DownloadTool and AnalyzeTextTool
- **Research Workflow Support**: Enables systematic document discovery workflows with priority-based processing
- **Memory Persistence**: Integrates with CrewMemoryManager for long-term research session state management

## Technical Architecture

### Link Processing Pipeline
Sophisticated link extraction and analysis workflow:
1. **URL Validation**: Validates input URL format and accessibility requirements
2. **Duplicate Detection**: Checks _processed_urls cache and optional memory manager for previous processing
3. **Webpage Retrieval**: Uses shared WebTools instance to fetch and clean webpage content  
4. **Link Extraction**: Leverages WebTools.extract_links() for comprehensive link discovery with metadata
5. **Content Analysis**: Analyzes link text and URLs against ESG terminology and keyword databases
6. **Priority Categorization**: Sorts links into PDF documents, potential reports, and general content categories
7. **Formatted Output**: Returns structured, human-readable link listings with priority indicators
8. **Cache Update**: Updates processed URL cache and memory manager for future duplicate prevention
9. **Error Recovery**: Handles network errors, parsing failures, and content access issues gracefully

### ESG Keyword Integration
Advanced keyword matching for ESG content identification:
- **Specialized Terminology**: Uses key_terms module containing 200+ ESG-related keywords and misconduct indicators
- **Report Identification**: Targets corporate report keywords including "report", "annual", "sustainability", "esg", "financial", "results"
- **Document Format Detection**: Prioritizes PDF links and document formats containing structured ESG data
- **Corporate Research Focus**: Incorporates misconduct terminology for controversy and greenwashing investigation
- **Multi-Language Support**: Keyword matching designed to work across different document languages and formats

## Database Schema Integration

### Analytics Database Connection
The tool operates within EkoIntelligence's analytics framework:
- **Knowledge Graph Integration**: Discovered links contribute to comprehensive corporate knowledge graphs in kg_* tables
- **Session Tracking**: Link extraction activities are logged in agent_crawl_sessions table for research audit trails
- **Document Pipeline**: Extracted links feed into document processing pipeline via kg_discovered_pages tracking
- **Research Coordination**: Database-level coordination ensures no duplicate processing across research sessions
- **Performance Monitoring**: Tool usage and effectiveness metrics stored for research workflow optimization

### Content Discovery Tracking
Integration with document discovery and processing systems:
- **Discovered Documents**: Links identified by the tool populate document discovery queues for processing agents
- **Research Session State**: Long-term persistence of link discovery state across multi-session research projects
- **Content Prioritization**: Database-level priority scores guide subsequent document processing and analysis
- **Audit Trail**: Complete tracking of link discovery source, timestamp, and processing status for research transparency

## Web Tools Integration

### Shared Resource Architecture
Leverages centralized web interaction capabilities:
- **WebTools Instance**: Uses shared web_tools instance from base module for consistent HTTP operations
- **Content Fetching**: Reliable webpage content retrieval with cleaning and error handling via WebTools.fetch_webpage()
- **Link Processing**: Advanced link extraction with metadata and relationship analysis via WebTools.extract_links()
- **Domain Analysis**: Integration with domain categorization for credibility assessment and source evaluation
- **Search Integration**: Potential integration with web search APIs for expanded link discovery capabilities

### Performance and Reliability
Optimized for high-volume ESG research operations:
- **Connection Reuse**: Shared WebTools instance minimizes connection overhead across multiple link extractions
- **Error Resilience**: Robust error handling for network timeouts, parsing failures, and content access restrictions
- **Resource Management**: Efficient memory usage through shared instances and lightweight caching mechanisms
- **Rate Limiting**: Built-in request throttling prevents overwhelming target websites during intensive research

## Usage Patterns and Examples

### Basic Link Extraction
```python
# Extract links from corporate sustainability page
result = tool._run(
    url="https://company.com/sustainability/reports", 
    redo="no"
)

# Force re-extraction from previously processed page
result = tool._run(
    url="https://company.com/investor-relations", 
    redo="yes"
)
```

### Agent Integration Example
```python
from crewai import Agent
from crewai.tools import ExtractLinksTool

# Create research agent with link extraction capabilities
research_agent = Agent(
    role='ESG Document Discovery Specialist',
    goal='Identify and catalog ESG documents for analysis',
    tools=[ExtractLinksTool()],
    backstory='Expert at finding corporate sustainability documents'
)
```

## System Architecture Context

### EkoIntelligence Platform Integration
The tool fits within the broader ESG analysis ecosystem:
- **Analytics Backend**: Python orchestration layer for automated ESG research and corporate analysis
- **Multi-Agent Research**: CrewAI framework enables collaborative research with specialized agents for different tasks
- **Document Processing**: Discovered links feed comprehensive document ingestion and analysis pipelines
- **Knowledge Graph**: Link discovery contributes to comprehensive corporate relationship mapping and analysis
- **Risk Assessment**: ESG content discovery supports automated corporate risk scoring and greenwashing detection

### Research Workflow Position
Critical early-stage component in ESG research workflows:
1. **Discovery Phase**: Link extraction identifies potential corporate documents and ESG content sources
2. **Prioritization Phase**: Intelligent categorization guides subsequent document collection and processing efforts
3. **Collection Phase**: High-priority links are processed by specialized document download and analysis agents
4. **Analysis Phase**: Discovered content feeds ESG analysis, statement extraction, and risk assessment systems
5. **Integration Phase**: Research results integrate with corporate profiles, risk scores, and regulatory compliance assessments

## Related Components
- **WebTools Class** (`eko.entities.agent.tools`): Core web interaction capabilities for content retrieval and link processing
- **Base Module** (`tools_new/base.py`): Shared resources and web_tools instance for consistent tool behavior
- **Download Tool** (`tools_new/download_pdf.py`): Processes high-priority PDF links identified by extraction tool
- **Memory Manager** (`../memory.py`): CrewMemoryManager for persistent session tracking and coordination
- **ESG Keywords** (`tools_new/__init__.py`): Comprehensive keyword databases for ESG content identification
- **Analytics Database**: PostgreSQL schema storing research sessions, discovered documents, and analysis results

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation  
@see https://docs.crewai.com/concepts/agents CrewAI Multi-Agent Systems
@see https://docs.pydantic.dev/latest/concepts/models/ Pydantic BaseModel for Input Validation
@see https://loguru.readthedocs.io/en/stable/ Loguru Logging Framework
@see https://requests.readthedocs.io/ Python Requests Library for HTTP Operations
@see ../tools_mem/ Memory Tool Implementations for Session Persistence
@see ../../entities/agent/tools.py WebTools Class Implementation  
@see ../../analysis_v2/ ESG Analysis Pipeline for Content Processing
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for intelligent link extraction and categorization during ESG web research workflows, with priority-based organization and multi-agent coordination
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from typing import Type, Optional, Any, Set

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from . import key_terms
from .base import web_tools


class ExtractLinksInput(BaseModel):
    """Input schema for the extract_links tool."""
    url: str = Field(..., description="URL of the webpage to extract links from")
    redo: str = Field(..., description="Set to 'yes' to force re-extraction even if this URL was already processed")


class ExtractLinksTool(BaseTool):
    """Tool for extracting links from a webpage."""
    
    name: str = "extract_links"
    description: str = "Extract links from a webpage. Use parameter redo=True to force re-extraction from a previously processed URL."
    args_schema: Type[BaseModel] = ExtractLinksInput
    _memory_manager: Optional[Any] = None
    
    def __init__(self, **kwargs):
        memory_manager = kwargs.pop('memory_manager')
        super().__init__(**kwargs)
        self._memory_manager = memory_manager
        # Cache for URLs we've already processed
        self._processed_urls: Set[str] = set()
    
    def _run(self, url: str, redo: str ) -> str:
        """
        Extract links from a webpage.

        Args:
            url: URL of the webpage to extract links from
            redo: Whether to force re-extraction even if this URL was already processed

        Returns:
            str: The extracted links
        """
        try:
            if not url:
                return "Error: You must provide a URL to extract links from"
            
            # Check if we've already processed this URL and redo is False
            if  redo != "yes" and url in self._processed_urls:
                return "We've already extracted links from this URL before. Use redo=True if you want to extract them again."
            
            # Add to our processed URLs
            self._processed_urls.add(url)
            
            # First fetch the webpage
            page_content = web_tools.fetch_webpage(url)
            if not page_content:
                return f"Failed to fetch content from {url}"
            
            # Then extract links
            links = web_tools.extract_links(page_content)
            
            if not links:
                return "No links found on the page."

            # Categorize links
            pdf_links = []
            doc_links = []
            potential_report_links = []
            other_links = []

            report_keywords = ['report', 'annual', 'sustainability', 'esg', 'financial', 'results',
                              'presentation', 'investor', 'filing', 'statement', 'disclosure'] + list(key_terms)

            for link in links:
                link_url = link.url.lower()
                text = (link.text or "").lower()

                if link_url.endswith('.pdf') or 'pdf' in link_url:
                    pdf_links.append(link)
                # elif any(ext in link_url for ext in ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']):
                #     doc_links.append(link)
                elif any(keyword in text or keyword in link_url for keyword in report_keywords):
                    potential_report_links.append(link)
                else:
                    other_links.append(link)

            formatted_links = []

            # Format PDF links (highest priority)
            if pdf_links:
                formatted_links.append("PDF DOCUMENTS (HIGH PRIORITY):")
                for i, link in enumerate(pdf_links, 1):
                    formatted_links.append(f"{i}. {link.text}")
                    formatted_links.append(f"   URL: {link.url}")
                    formatted_links.append("")

            # Format document links (high priority)
            if doc_links:
                formatted_links.append("OTHER DOCUMENTS (HIGH PRIORITY):")
                for i, link in enumerate(doc_links, 1):
                    formatted_links.append(f"{i}. {link.text}")
                    formatted_links.append(f"   URL: {link.url}")
                    formatted_links.append("")

            # Format potential report links (medium priority)
            if potential_report_links:
                formatted_links.append("POTENTIAL REPORT PAGES (MEDIUM PRIORITY):")
                for i, link in enumerate(potential_report_links, 1):
                    formatted_links.append(f"{i}. {link.text}")
                    formatted_links.append(f"   URL: {link.url}")
                    formatted_links.append("")

            # Format other links (limited to 20)
            if other_links:
                formatted_links.append("OTHER LINKS (LOW PRIORITY):")
                for i, link in enumerate(other_links[:20], 1):
                    formatted_links.append(f"{i}. {link.text}")
                    formatted_links.append(f"   URL: {link.url}")
                    formatted_links.append("")

                if len(other_links) > 20:
                    formatted_links.append(f"[Showing 20 of {len(other_links)} other links]")

            return "\n".join(formatted_links)
        except Exception as e:
            logger.error(f"Error extracting links: {e}")
            return f"Error extracting links: {str(e)}"
