"""
CrewAI Wikipedia Search Tool for ESG Entity Research and Corporate Intelligence Workflows

This module provides the `SearchWikipediaTool` class, a specialized CrewAI tool that interfaces with
Wikipedia's MediaWiki API to retrieve factual background information about companies, organizations,
and other entities during automated ESG (Environmental, Social, Governance) research workflows.
The tool serves as a complementary information source within the EkoIntelligence ESG analysis platform,
providing authoritative entity context to enrich corporate sustainability assessments, greenwashing
detection analyses, and ESG risk profiling activities.

## Core Functionality

### Wikipedia MediaWiki API Integration
- **Authoritative Data Source**: Leverages Wikipedia's comprehensive database for reliable entity information
- **Entity Background Research**: Retrieves company profiles, organizational descriptions, and contextual data
- **ESG Context Enhancement**: Provides foundational entity information to support ESG analysis workflows
- **Research Validation**: Enables fact-checking and baseline validation for corporate claims analysis

### CrewAI Tool Framework Integration
The tool implements the CrewAI `BaseTool` interface providing:

#### Tool Definition Structure:
- **Tool Identity**: Clear "search_wikipedia" identifier for agent selection and utilization
- **Input Schema**: Pydantic-based validation for search parameters and result configuration
- **Error Handling**: Graceful failure management with detailed logging and informative error responses  
- **Agent Integration**: Seamless integration with CrewAI multi-agent ESG research workflows

#### Search Parameters Framework:
- **Query Processing**: Flexible entity name handling optimized for corporate and organizational searches
- **Result Configuration**: Configurable result limits (default: 20) for efficient information retrieval
- **Response Formatting**: Structured text output optimized for downstream ESG analysis processing

## Key Classes and Components

### `SearchWPInput` (Pydantic BaseModel)
Input validation schema for Wikipedia search operations:
- **query** (str, required): The search query targeting entity names, company identifiers, or organizational terms
- **num_results** (int, optional): Number of search results to return (default: 20, primarily for API consistency)

Provides automatic validation, type checking, and clear parameter descriptions for agent utilization.

### `SearchWikipediaTool` (CrewAI BaseTool)  
Primary Wikipedia search tool class implementing CrewAI's tool interface:

#### Core Properties:
- **name**: "search_wikipedia" - Unique identifier for agent tool selection
- **description**: "Search wikipedia for information" - Agent-readable capability description
- **args_schema**: Links to `SearchWPInput` for automatic parameter validation

#### Primary Method: `_run(query: str, num_results: int = 20) -> str`
Core search execution method that:
1. **Validates Input**: Ensures query parameters meet Wikipedia API requirements
2. **Executes Search**: Calls `search_wikipedia()` function from `eko.entities.wikipedia`
3. **Handles Errors**: Captures and logs API exceptions with informative error responses
4. **Returns Results**: Provides formatted text output suitable for agent consumption

## Technical Implementation

### Wikipedia API Integration
The tool leverages the `search_wikipedia()` function from `eko.entities.wikipedia`, which:
- **MediaWiki API Access**: Uses Wikipedia's official MediaWiki API for reliable data retrieval
- **Caching Strategy**: Implements 30-day disk caching via `diskcache` for performance optimization
- **Rate Limiting**: Includes respectful API throttling (0.5s delays) to comply with usage policies
- **Extract Processing**: Retrieves article introductory sections for concise entity information
- **Redirect Handling**: Automatically follows Wikipedia redirects for accurate entity resolution

### Error Management and Reliability
Robust error handling ensures research workflow continuity:
- **API Exception Capture**: Graceful handling of network timeouts, API errors, and service interruptions
- **Informative Error Responses**: Returns descriptive error messages for agent decision-making
- **Logging Integration**: Uses Loguru logging for debugging, monitoring, and research session tracking
- **Fallback Behavior**: Returns structured error messages rather than causing workflow failures

## System Architecture Integration

### EkoIntelligence ESG Research Pipeline
The Wikipedia search tool integrates within the broader ESG analysis system:

#### Analytics Backend Integration:
- **Multi-Agent Orchestration**: Works alongside web search, document analysis, and claim verification agents
- **Entity Enrichment**: Provides authoritative background data for companies under ESG assessment
- **Research Validation**: Supports fact-checking workflows for corporate sustainability claims
- **Database Integration**: Entity information feeds into analytics database for comprehensive risk profiling

#### ESG Analysis Workflow Support:
1. **Entity Discovery Phase**: Provides foundational information about companies and organizations
2. **Context Building Phase**: Enriches entity profiles with authoritative biographical and operational data
3. **Validation Phase**: Supports cross-referencing of corporate claims against established entity facts
4. **Risk Assessment Phase**: Contributes background context for comprehensive ESG risk evaluation

### CrewAI Multi-Agent Research Coordination
The tool supports collaborative agent research through:
- **Agent Specialization**: Focused Wikipedia expertise complements web search and document analysis tools
- **Information Synthesis**: Wikipedia data combines with scraped documents for comprehensive entity profiles
- **Research Continuity**: Reliable caching ensures consistent entity information across research sessions
- **Workflow Integration**: Seamless cooperation with document discovery, claims analysis, and verification agents

## Related Components and Dependencies

### Core Dependencies:
- **CrewAI Framework** (`crewai.tools.BaseTool`): Tool foundation and agent integration framework
- **Pydantic** (`pydantic.BaseModel`, `pydantic.Field`): Input validation and schema management
- **Loguru** (`loguru.logger`): Structured logging for debugging and monitoring
- **Wikipedia Integration** (`eko.entities.wikipedia.search_wikipedia`): Core Wikipedia API functionality

### Related EkoIntelligence Components:
- **Web Search Tools** (`search_web.py`): Complementary web discovery for current information
- **Document Analysis Tools**: PDF processing and content extraction for comprehensive research
- **ESG Analysis Pipeline** (`eko.analysis_v2/`): Downstream processing of entity information
- **Database Schema** (Analytics DB): Storage for entity profiles and research session data

## Usage Context and Applications

### Primary Use Cases:
1. **Entity Background Research**: Authoritative information about companies, organizations, and key individuals
2. **Claim Verification Support**: Baseline facts for validating corporate sustainability statements
3. **ESG Context Building**: Foundational entity data for comprehensive ESG risk assessments
4. **Research Quality Assurance**: Reliable information source for multi-agent research validation

### Integration Benefits:
- **Authoritative Data**: Wikipedia provides peer-reviewed, reliable entity information
- **Research Efficiency**: Cached results improve agent performance and reduce API dependencies
- **Cross-Validation**: Enables comparison between claimed corporate information and established facts
- **Comprehensive Profiling**: Combines with other tools for complete entity intelligence gathering

@see https://en.wikipedia.org/w/api.php Wikipedia MediaWiki API Documentation
@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://github.com/diskcache/diskcache Python Diskcache Library for Performance Optimization
@see ../base.py Shared Resources and Dependencies for CrewAI Web Crawling Tools
@see ../../entities/wikipedia.py Wikipedia API Integration Functions
@see ../../analysis_v2/ ESG Analysis Pipeline for Entity Assessment
<AUTHOR>
@updated 2025-07-22
@description CrewAI Wikipedia search tool for entity background research in ESG analysis workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""

from typing import Type

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.entities.wikipedia import search_wikipedia


class SearchWPInput(BaseModel):
    """Input schema for the search_web tool."""
    query: str = Field(..., description="The search query")
    num_results: int = Field(20, description="Number of search results to return (default: 20)")


class SearchWikipediaTool(BaseTool):
    """Tool for searching the web."""
    
    name: str = "search_wikipedia"
    description: str = "Search wikipedia for information"
    args_schema: Type[BaseModel] = SearchWPInput

    def _run(self, query: str, num_results: int = 20) -> str:
        """
        Search the web for information.

        Args:
            query: The search query
            num_results: Number of search results to return

        Returns:
            str: The search results
        """
        try:
            return search_wikipedia(query)

        except Exception as e:
            logger.error(f"Error searching web: {e}")
            return f"Error searching web: {str(e)}"
