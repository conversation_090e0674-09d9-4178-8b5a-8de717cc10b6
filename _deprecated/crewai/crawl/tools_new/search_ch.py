"""
CrewAI Companies House Search Tool for UK Corporate Entity Discovery and ESG Analysis

This module provides the `SearchCHTool` class, a specialized CrewAI tool for searching UK Companies House 
records during automated ESG (Environmental, Social, Governance) research workflows. The tool serves as an 
intelligent corporate entity discovery agent within the EkoIntelligence ESG analysis platform, enabling 
systematic retrieval of UK company registration information, corporate hierarchies, and legal entity data 
for comprehensive ESG assessment and greenwashing detection workflows.

## Core Functionality

### UK Companies House API Integration
- **Official Government Data Access**: Integrates with the UK Companies House API to provide authoritative company registration data
- **Comprehensive Search Capabilities**: Searches across all registered UK companies using flexible name matching
- **Structured Data Response**: Returns standardized company information including registration numbers, official names, and status
- **Cached Performance**: Leverages backend caching system for optimized API usage and reduced latency

### Corporate Entity Discovery Framework
The tool implements intelligent company search functionality through the Companies House API:

#### Search Parameters and Configuration:
- **Flexible Query Matching**: Supports partial name matching, exact company names, and variations in corporate naming
- **Pagination Control**: Configurable result limits with intelligent pagination handling (default: 20 results, max 10 pages)
- **Rate Limiting Compliance**: Built-in rate limiting and retry logic to comply with Companies House API constraints
- **Data Validation**: Comprehensive input validation using Pydantic schemas for type-safe parameter handling

#### Result Processing and Output:
- **JSON Serialization**: Returns results in JSON format using jsonpickle for consistent data interchange
- **Error Resilience**: Graceful error handling with informative error messages returned as strings
- **Performance Optimization**: Multi-level caching system reduces API calls and improves response times

### CrewAI Multi-Agent ESG Workflow Integration
The tool supports coordinated multi-agent ESG research scenarios:
- **Entity Identification**: Agents can systematically identify UK companies for ESG analysis from various data sources
- **Corporate Structure Mapping**: Discovery of holding companies, subsidiaries, and corporate group relationships
- **Regulatory Compliance Verification**: Cross-referencing company registration status with ESG disclosure requirements
- **Supply Chain Analysis**: Identification of UK-registered suppliers and business partners in ESG supply chain mapping

## ESG Research Applications

### Corporate Governance Intelligence
- **Board Composition Research**: Discovery of UK companies for director network analysis and governance assessment
- **Ownership Structure Mapping**: Identification of parent companies and ultimate beneficial owners for ESG accountability
- **Regulatory Status Verification**: Cross-referencing active company status with ESG reporting obligations

### Environmental Compliance Investigation
- **Industrial Classification Analysis**: Using SIC codes from company records to identify environmentally sensitive industries
- **Subsidiary Discovery**: Mapping corporate structures to identify all entities within environmental reporting scope
- **Regulatory Entity Verification**: Ensuring ESG analysis targets are correctly matched to registered legal entities

### Social Responsibility Research
- **Employment Data Context**: Connecting company registration data with employment and social responsibility metrics
- **Community Impact Assessment**: Identifying local companies for community ESG impact analysis
- **Supply Chain Mapping**: Discovery of UK suppliers and contractors for comprehensive ESG supply chain analysis

## Technical Implementation

### Companies House API Architecture
- **Official UK Government API**: Direct integration with the authoritative UK Companies House database
- **RESTful API Design**: Standard HTTP request/response patterns with JSON data exchange
- **Authentication Management**: Secure API key handling via backend authentication system
- **Compliance Framework**: Adherence to Companies House API terms of service and usage limits

### Data Processing Pipeline
1. **Query Validation**: Input sanitization and validation using SearchCHInput Pydantic schema
2. **API Request Formation**: Dynamic parameter construction for Companies House search endpoint
3. **Response Processing**: JSON parsing and data structure validation from API responses
4. **Result Serialization**: JSON encoding using jsonpickle for consistent agent data interchange
5. **Error Handling**: Comprehensive exception management with fallback responses

### Performance and Reliability Features
- **Multi-Level Caching**: Leverages backend MultiLevelCache system for reduced API calls and improved performance
- **Retry Logic**: Intelligent retry mechanisms for handling temporary API failures and rate limiting
- **Error Logging**: Comprehensive error logging via Loguru for debugging and system monitoring
- **Graceful Degradation**: Tool continues to function even when API errors occur, returning error messages instead of failing

## System Architecture Integration

### EkoIntelligence Platform Context
The SearchCHTool integrates with the broader EkoIntelligence ESG analysis ecosystem:
- **Agent Crawling Framework**: Part of the tools_new generation of CrewAI tools for enhanced web crawling capabilities
- **Entity Management System**: Integrates with eko.entities.companies_house module for comprehensive UK corporate data management
- **Database Integration**: Company discoveries feed into the analytics database for entity relationship mapping
- **ESG Analysis Pipeline**: Discovered companies become targets for ESG statement extraction and analysis workflows

### Multi-Agent Coordination
The tool supports sophisticated multi-agent research workflows:
- **Entity Discovery Agents**: Agents use the tool to identify UK companies from various data sources and contexts
- **Verification Agents**: Secondary agents can cross-reference discovered entities against official Companies House records
- **Analysis Agents**: Downstream agents receive validated company data for comprehensive ESG analysis
- **Reporting Agents**: Final agents incorporate official company data into ESG reports and risk assessments

### Data Flow and Storage
Company discovery workflow within the platform:
1. **Discovery Phase**: Agents identify potential UK companies from documents, news, or other sources
2. **Verification Phase**: SearchCHTool validates and enriches company names with official registration data
3. **Storage Phase**: Verified company data stored in analytics database for relationship mapping
4. **Analysis Phase**: Companies become targets for ESG document analysis and greenwashing detection

## Usage Examples and Configuration

### Basic Company Search
```python
# Search for a UK company by name
tool = SearchCHTool()
results = tool._run(
    query="Tesco PLC",
    num_results=10
)
```

### Bulk Entity Discovery
```python
# Discover multiple potential matches for entity identification
tool = SearchCHTool()
results = tool._run(
    query="British Petroleum",
    num_results=50
)
```

### Agent Integration Example
```python
from crewai import Agent
from tools_new.search_ch import SearchCHTool

# Create an entity discovery agent with Companies House search capability
entity_agent = Agent(
    role="UK Corporate Entity Researcher",
    goal="Identify and verify UK company registrations for ESG analysis",
    backstory="Expert in UK corporate law and company registration systems",
    tools=[SearchCHTool()],
    verbose=True
)
```

## Related Components and Dependencies

### Core Dependencies
- **CrewAI Framework** (`crewai.tools.BaseTool`): Multi-agent workflow orchestration and standardized tool interface
- **Pydantic** (`pydantic.BaseModel`, `pydantic.Field`): Type-safe input validation and data structure definition
- **JSONPickle** (`jsonpickle`): Advanced JSON serialization supporting complex Python objects
- **Loguru** (`loguru.logger`): Structured logging for debugging, monitoring, and error tracking

### System Integration Points
- **Companies House Module** (`eko.entities.companies_house`): Backend API integration with comprehensive UK corporate data access
- **Caching System** (`eko.cache.pg_cache.MultiLevelCache`): Performance optimization through intelligent caching
- **Agent Framework** (`crewai.tools`): Integration with CrewAI agent orchestration and tool management
- **Database Layer**: Analytics database for storing discovered entities and relationship mapping

### External API Dependencies
- **UK Companies House API**: Official UK government API providing authoritative company registration data
- **Authentication System**: Secure API key management through backend configuration system

@see https://developer.company-information.service.gov.uk/ UK Companies House API Documentation
@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.pydantic.dev/ Pydantic Data Validation Library
@see https://jsonpickle.github.io/ JSONPickle Serialization Documentation
@see https://loguru.readthedocs.io/ Loguru Logging Framework
@see ../../entities/companies_house.py UK Companies House API Integration Module
@see ../base.py Base Module for Shared Web Crawling Resources
@see ../../../analysis_v2/ ESG Analysis Pipeline for Corporate Entity Processing
<AUTHOR>  
@updated 2025-07-22
@description CrewAI tool for UK Companies House search and corporate entity discovery in automated ESG research workflows
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""
from typing import Type

import jsonpickle
from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.entities.companies_house import search_companies_by_name


class SearchCHInput(BaseModel):
    """Input schema for the search_web tool."""
    query: str = Field(..., description="The company name to search for")
    num_results: int = Field(20, description="Number of search results to return (default: 20)")


class SearchCHTool(BaseTool):
    """Tool for searching Companies House."""
    
    name: str = "search_companies_house"
    description: str = "Search companies house for information"
    args_schema: Type[BaseModel] = SearchCHInput
    
    def _run(self, query: str, num_results: int = 20) -> str:
        """
        Search the web for information.

        Args:
            query: The search query
            num_results: Number of search results to return

        Returns:
            str: The search results
        """
        try:
            return jsonpickle.encode(search_companies_by_name(query, 10, int(num_results/10)), unpicklable=False)
        except Exception as e:
            logger.error(f"Error searching companies house: {e}")
            return f"Error searching companies house: {str(e)}"
