"""
CrewAI Search Query Generation Tool for Automated ESG Research

This module provides the `GenerateSearchQueriesTool` class, a specialized CrewAI tool for dynamically
generating targeted search queries based on discovered corporate information during automated ESG 
(Environmental, Social, Governance) research workflows within the EkoIntelligence analysis platform.
The tool combines human-curated ESG keywords with AI-driven query generation to create comprehensive 
search strategies for corporate sustainability analysis, greenwashing detection, and ESG compliance 
investigation through systematic web crawling and document discovery.

## Core Purpose
The tool addresses the critical challenge of **adaptive search strategy generation** in ESG research 
by analyzing discovered corporate information and generating contextually relevant search queries:
- **Dynamic Query Generation**: Creates search queries based on previously discovered corporate insights
- **ESG-Focused Targeting**: Integrates predefined ESG misconduct and sustainability terminology 
- **Corporate Context Analysis**: Analyzes discovered company information to identify research gaps
- **Multi-Source Integration**: Combines database-stored search queries with generated ones for comprehensive coverage
- **Document Discovery Optimization**: Prioritizes queries that target reports, filings, and official documents

## Key Functionality

### Intelligent Query Generation Algorithm
The tool implements a **multi-stage query generation process**:
1. **Text Analysis**: Parses discovered corporate information for relevant keywords
2. **Frequency Analysis**: Identifies most significant terms through word frequency analysis  
3. **ESG Integration**: Combines discovered keywords with predefined ESG terminology from `key_terms`
4. **Document Targeting**: Adds document-specific qualifiers (report, filing, annual, disclosure)
5. **Database Integration**: Retrieves historical negative and positive search queries via `get_searches_for_entity()`
6. **Query Randomization**: Shuffles results to ensure diverse search coverage across research sessions

### ESG Research Vocabulary Integration
The tool leverages the comprehensive ESG keyword taxonomy from the `key_terms` module:
- **Misconduct Detection**: Integrates 39+ core wrongdoing terms (lawsuit, violation, scandal, fine)
- **Environmental Issues**: Climate, pollution, waste, and sustainability violation terminology
- **Social Issues**: Labor rights, human rights, consumer safety, and community impact terms
- **Governance Issues**: Financial irregularities, regulatory compliance, and transparency concerns
- **Legal Framework**: Litigation, compliance, and regulatory enforcement terminology

### Database-Driven Query Enhancement
Integration with the analytics database (`kg_search_queries` table) provides:
- **Historical Query Retrieval**: Accesses previously successful search strategies for entities
- **Positive/Negative Polarity**: Balances search across positive corporate actions and negative controversies
- **Entity-Specific Optimization**: Tailors queries based on company-specific past research patterns
- **Search Performance Tracking**: Leverages historical query success metrics for optimization

## CrewAI Framework Integration

### Tool Architecture Design
The `GenerateSearchQueriesTool` follows CrewAI best practices:
- **BaseTool Inheritance**: Inherits from `crewai.tools.BaseTool` for standardized framework integration
- **Pydantic Schema Validation**: Uses `GenerateSearchQueriesInput` for type-safe parameter validation
- **Flexible Input Parameters**: Supports company name, discovered info, and configurable query count
- **Standardized Output**: Returns formatted string with numbered query list for agent consumption
- **Error Handling**: Comprehensive exception handling with Loguru logging for debugging

### Multi-Agent Research Workflows
In collaborative ESG research environments:
- **Adaptive Research Strategy**: Agents adjust search strategies based on intermediate discoveries
- **Knowledge Building**: Each query generation builds on previous research findings
- **Research Gap Identification**: Tool identifies unexplored areas in corporate ESG analysis
- **Systematic Coverage**: Ensures comprehensive coverage of ESG topics and corporate activities

## Technical Implementation

### Query Generation Algorithm Details
**Keyword Extraction Process:**
```python
# Filter common words, focus on substantive terms
keywords = [word for word in words if len(word) > 3 and word not in common_words]
# Frequency-based prioritization
sorted_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)
```

**ESG Query Construction:**
- Company name + ESG key terms + document type qualifiers
- Database retrieval for entity-specific historical queries
- Randomization to ensure diverse research coverage
- Configurable output count (default: 5 queries)

### Database Integration Architecture
**Analytics Database Access:**
- **Connection Management**: Uses `get_bo_conn()` for database connectivity
- **Query Storage**: Results may be stored in `kg_search_queries` table for tracking
- **Historical Analysis**: Retrieves past search strategies via `get_searches_for_entity()`
- **Performance Metrics**: Supports tracking of query effectiveness and result relevance

## System Architecture Context

### EkoIntelligence Platform Integration
The tool fits within the broader ESG analysis system:
- **Web Crawling Pipeline**: Generated queries feed automated web discovery systems
- **Document Processing**: Query results drive PDF and webpage content extraction
- **ESG Analysis Engine**: Discovered content flows to greenwashing detection and ESG scoring
- **Corporate Intelligence**: Builds comprehensive corporate ESG profiles through targeted research

### Data Flow and Processing
**Research Workflow Integration:**
1. **Initial Discovery**: Agents discover preliminary corporate information
2. **Query Generation**: This tool creates targeted follow-up search strategies
3. **Content Discovery**: Generated queries drive web crawling and document identification
4. **Content Analysis**: Discovered content feeds ESG analysis and corporate risk assessment
5. **Intelligence Synthesis**: Results integrate into comprehensive corporate sustainability profiles

## Related Components
- **ESG Keyword Definitions** (`key_terms` module): Comprehensive ESG and misconduct terminology
- **Database Query Functions** (`eko.commands.scrape`): Corporate search query management and retrieval
- **Web Crawling Tools** (`tools_new/` package): Tools that consume generated search queries
- **ESG Analysis Pipeline** (`eko.analysis_v2/`): Downstream processing of research discoveries
- **Analytics Database Schema** (`kg_search_queries`): Storage for search strategy tracking and optimization

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.pydantic.dev/ Pydantic Data Validation and Settings Management
@see https://loguru.readthedocs.io/ Loguru Logging Library Documentation
@see https://www.globalreporting.org/ Global Reporting Initiative (GRI) ESG Standards
@see key_terms.py ESG Keyword Definitions and Corporate Misconduct Terminology
@see ../../../commands/scrape.py Database Search Query Management Functions
@see ../../../analysis_v2/ ESG Analysis and Corporate Risk Assessment Pipeline
<AUTHOR>
@updated 2025-07-22
@description Dynamic search query generation tool for adaptive ESG research and corporate intelligence gathering
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""
from random import shuffle
from typing import Type

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.agent.crewai.crawl.tools_new import key_terms
from eko.commands.scrape import get_searches_for_entity
from eko.db import get_bo_conn


class GenerateSearchQueriesInput(BaseModel):
    """Input schema for the generate_search_queries tool."""
    company_name: str = Field(..., description="The name of the company being researched")
    discovered_info: str = Field(..., description="Information already discovered about the company")
    num_queries: int = Field(default=5, description="Number of search queries to generate (default: 5)")


class GenerateSearchQueriesTool(BaseTool):
    """Tool for generating search queries based on discovered information."""

    name: str = "generate_search_queries"
    description: str = "Generate new search queries based on discovered information"
    args_schema: Type[BaseModel] = GenerateSearchQueriesInput

    def _run(self, company_name: str, discovered_info: str, num_queries: int = 5) -> str:
        """
        Generate new search queries based on discovered information.

        Args:
            company_name: The name of the company being researched
            discovered_info: Information already discovered about the company
            num_queries: Number of search queries to generate

        Returns:
            str: Generated search queries
        """
        try:
            # Extract potential keywords from discovered info
            words = discovered_info.lower().split()
            # Filter out common words
            common_words = ['the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'of', 'a', 'an']
            keywords = [word for word in words if len(word) > 3 and word not in common_words]

            # Count keyword frequency
            keyword_counts = {}
            for keyword in keywords:
                if keyword in keyword_counts:
                    keyword_counts[keyword] += 1
                else:
                    keyword_counts[keyword] = 1

            # Sort keywords by frequency
            sorted_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)

            # Generate queries
            queries = []

            doc_terms = ['report', 'article', 'research paper', 'pdf', 'annual', 'financial', 'presentation', 'filing', 'statement']

            
            
            

                 # Generate company + ESG + document type queries
            for key_term in key_terms:
                for doc_term in doc_terms:
                    query = f"\"{company_name}\" {key_term} {doc_term}"
                    if query not in queries:
                        queries.append(query)

            with get_bo_conn() as conn:
                negative=get_searches_for_entity(conn, company_name,company_name, True)
                positive=get_searches_for_entity(conn, company_name,company_name, False)
            all_queries=(negative+positive+queries)
            shuffle(all_queries)
            # Select the top queries
            selected_queries = all_queries[:num_queries]

            # Format the result
            result = f"Generated {len(selected_queries)} search queries for {company_name}:\n\n"
            for i, query in enumerate(selected_queries, 1):
                result += f"{i}. {query}\n"

            return result
        except Exception as e:
            logger.error(f"Error generating search queries: {e}")
            return f"Error generating search queries: {str(e)}"
