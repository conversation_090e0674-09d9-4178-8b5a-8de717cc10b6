
"""
CrewAI SEC Database Search Tool for Multi-Agent Corporate Research Workflows

This module provides the `SearchSECTool` class, a specialized CrewAI tool for searching SEC EDGAR 
database filings during automated corporate research workflows. The tool serves as a bridge between 
AI agents and the Securities and Exchange Commission's comprehensive corporate filing database, 
enabling systematic retrieval of regulatory filings, financial statements, and corporate disclosure 
documents for ESG analysis and corporate research within the EkoIntelligence platform.

## Core Functionality

### SEC EDGAR Database Integration
- **Company Name Search**: Searches SEC database using approximate company name matching with intelligent query parsing
- **Pagination Support**: Handles large result sets through configurable pagination with up to 200 results per search
- **Filing Retrieval**: Returns structured filing data including CIK numbers, form types, filing dates, and document URLs
- **Rate Limiting**: Implements automatic retry logic with exponential backoff to handle SEC API rate limits

### Data Processing and Serialization
- **JSON Serialization**: Uses jsonpickle for efficient serialization of complex Pydantic models into JSON format
- **Type Safety**: Leverages Pydantic BaseModel validation for robust input parameter handling
- **Error Handling**: Comprehensive exception handling with detailed logging for debugging and monitoring

### Search Query Processing
The tool implements intelligent search query processing:
1. **Input Validation**: Company name and result count validation through Pydantic schema
2. **Query Transformation**: Converts company names into SEC API-compatible search queries
3. **Pagination Logic**: Automatically calculates page sizes and limits based on requested result counts
4. **Response Processing**: Transforms SEC API responses into standardized Filing objects

## CrewAI Framework Integration

### Tool Architecture Design
The `SearchSECTool` follows CrewAI best practices by inheriting from `BaseTool`:
- **Standardized Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **Pydantic Input Validation**: Uses `SearchSECInput` schema for type-safe parameter validation
- **Flexible Configuration**: Supports configurable result limits (default: 20, maximum: 200+ via pagination)
- **Agent Integration**: Seamlessly integrates with CrewAI agents for automated corporate research tasks

### Multi-Agent Research Coordination
In collaborative corporate research environments, the tool enables:
- **Shared Database Access**: Multiple agents can search SEC database simultaneously without conflicts
- **Consistent Data Format**: Standardized Filing objects ensure compatibility across different research agents
- **Resource Management**: Built-in rate limiting prevents API quota exhaustion during intensive research
- **Error Recovery**: Robust exception handling ensures research workflows continue despite API issues

## SEC Filing Data Integration

### Filing Information Retrieval
The tool retrieves comprehensive SEC filing metadata:
- **Corporate Identifiers**: CIK (Central Index Key), company names, and ticker symbols
- **Document Metadata**: Filing dates, form types (10-K, 10-Q, 8-K, etc.), and accession numbers
- **Access URLs**: Direct links to SEC EDGAR filing documents for content analysis
- **Company Details**: Long-form company names and additional corporate identifiers

### Database Integration Architecture
Filing data flows through the complete EkoIntelligence data pipeline:
- **Primary Storage**: `kg_entity_sec` table in analytics database for comprehensive SEC data
- **Cross-Reference**: Links with `kg_base_entities` for unified entity management
- **Data Enrichment**: SEC data enriches corporate profiles with regulatory filing history
- **Analysis Pipeline**: Filing metadata feeds downstream ESG analysis and risk assessment systems

## Technical Implementation Details

### SEC API Integration Layer
- **sec-api Library**: Leverages professional SEC API service with pagination and rate limiting
- **Query Optimization**: Intelligent query construction using company name tokenization
- **Caching Layer**: 30-day multi-level caching reduces API calls and improves performance
- **Retry Logic**: Three-attempt retry mechanism with progressive delays for transient failures

### Data Processing Pipeline
1. **Input Processing**: Validates company name and result count parameters
2. **Query Construction**: Transforms user input into SEC API-compatible search queries  
3. **API Interaction**: Executes paginated searches with automatic retry on rate limits
4. **Data Transformation**: Converts SEC API responses into structured Filing objects
5. **Serialization**: Uses jsonpickle to create agent-consumable JSON responses
6. **Error Handling**: Comprehensive exception handling with structured logging

### Performance Optimization Features
- **Efficient Pagination**: Calculates optimal page sizes to minimize API calls
- **Memory Management**: Streaming processing of large result sets to minimize memory usage
- **Caching Integration**: Multi-level caching system reduces redundant API calls
- **Rate Limit Handling**: Automatic backoff and retry logic prevents API quota exhaustion

## System Architecture Context

### EkoIntelligence Platform Integration
The SEC search tool operates within the broader corporate sustainability analysis ecosystem:
- **Analytics Backend**: Python-based CrewAI orchestration for automated SEC filing research
- **Database Layer**: Analytics database storage with comprehensive SEC entity tables
- **Entity Management**: Integration with unified entity system for corporate identity resolution
- **Research Workflows**: Supports multi-agent ESG research requiring regulatory filing analysis

### Corporate Research Workflow Integration
1. **Research Session Initialization**: CrewAI agents configured with SEC search capabilities
2. **Company Identification**: Agents search SEC database to identify corporate entities and filings
3. **Filing Discovery**: Systematic retrieval of regulatory documents for content analysis
4. **Data Enrichment**: SEC filing metadata enriches corporate profiles and entity databases
5. **Analysis Pipeline**: Retrieved filing information feeds ESG scoring and risk assessment systems

## Usage Examples and Integration Patterns

### Basic Agent Configuration
```python
from eko.agent.crewai.crawl.tools_new.search_sec import SearchSECTool
from crewai import Agent

# Initialize SEC search tool
sec_tool = SearchSECTool()

# Configure SEC research agent
sec_analyst = Agent(
    role="SEC Filing Researcher",
    goal="Search and analyze SEC filings for corporate disclosure analysis",
    tools=[sec_tool],
    memory=True,
    verbose=True
)
```

### Multi-Agent Research Coordination
```python
# Coordinated multi-agent SEC research workflow
financial_agent = Agent(
    role="Financial Analyst",
    tools=[SearchSECTool()],
    goal="Analyze 10-K and 10-Q filings for financial risk assessment"
)

governance_agent = Agent(
    role="Governance Specialist", 
    tools=[SearchSECTool()],
    goal="Review proxy statements and governance-related SEC filings"
)

# Agents collaborate on comprehensive SEC filing analysis
crew = Crew(agents=[financial_agent, governance_agent])
results = crew.kickoff(inputs={"company": "Apple Inc"})
```

### Advanced Search Configuration
```python
# High-volume SEC database research
sec_tool = SearchSECTool()

# Search with custom result limits
large_company_search = sec_tool._run(
    query="Microsoft Corporation",
    num_results=100  # Retrieves up to 100 filing records
)

# Parse results for downstream analysis
filings_data = jsonpickle.decode(large_company_search)
for filing in filings_data:
    print(f"Form: {filing.form_type}, Date: {filing.filing_date}")
```

## Error Handling and Reliability

### Exception Management
- **API Failures**: Graceful handling of SEC API outages and rate limits
- **Data Validation**: Pydantic model validation prevents malformed data processing
- **Network Issues**: Automatic retry logic handles transient network connectivity problems
- **Logging Integration**: Comprehensive error logging via Loguru for debugging and monitoring

### Operational Reliability Features
- **Rate Limit Compliance**: Built-in respect for SEC API rate limits and usage quotas
- **Caching Strategy**: Multi-level caching reduces API dependency and improves response times
- **Fallback Handling**: Graceful degradation when SEC API is unavailable or returns errors
- **Memory Safety**: Efficient handling of large filing datasets without memory exhaustion

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://www.sec.gov/edgar SEC EDGAR Database Official Documentation
@see https://sec-api.io/ SEC-API Service Documentation
@see https://github.com/crewaiinc/crewai CrewAI Framework Repository
@see backoffice/src/eko/entities/sec.py SEC Entity Integration Module
@see backoffice/src/eko/cache/pg_cache.py Multi-Level Caching Implementation
@see backoffice/src/eko/db/get_bo_conn Database Connection Management
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for searching SEC EDGAR database filings during automated corporate research workflows
@docgen doc-by-claude  
@copyright (c) All rights reserved ekoIntelligence 2025
"""
from typing import Type

import jsonpickle
from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.entities.sec import get_sec_company_data_by_name


class SearchSECInput(BaseModel):
    """Input schema for the search_web tool."""
    query: str = Field(..., description="The company name to search for")
    num_results: int = Field(20, description="Number of search results to return (default: 20)")


class SearchSECTool(BaseTool):
    """Tool for searching SEC."""
    
    name: str = "search_sec"
    description: str = "Search SEC database for company information"
    args_schema: Type[BaseModel] = SearchSECInput
    
    def _run(self, query: str, num_results: int = 20) -> str:
        """
        Search the web for information.

        Args:
            query: The search query
            num_results: Number of search results to return

        Returns:
            str: The search results
        """
        try:
            return jsonpickle.encode(get_sec_company_data_by_name(query, 10, int(num_results/10)+1), unpicklable=False)
        except Exception as e:
            logger.error(f"Error searching SEC {e}")
            return f"Error searching SEC: {str(e)}"
