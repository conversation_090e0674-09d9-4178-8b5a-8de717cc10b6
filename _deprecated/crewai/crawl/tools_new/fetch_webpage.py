"""
CrewAI Webpage Content Extraction Tool for ESG Research and Document Discovery

This module provides the `FetchWebpageTool` class, a specialized CrewAI tool designed for intelligent 
webpage content extraction and processing during automated ESG (Environmental, Social, Governance) 
research workflows within the EkoIntelligence analysis platform. The tool serves as a foundational 
component in multi-agent web crawling operations, fetching, cleaning, and structuring web content 
for comprehensive corporate research, sustainability analysis, and ESG content discovery.

## Core Functionality
- **Intelligent Content Extraction**: Fetches and cleans webpage content using advanced web scraping techniques with fallback strategies
- **Structured Content Processing**: Converts raw HTML into structured JSON format with title, text, metadata, and embedded links
- **Duplicate Prevention System**: Implements URL-based memory management to prevent redundant processing of previously visited pages
- **ESG-Focused Link Discovery**: Automatically extracts and categorizes PDF links and general links for downstream processing
- **Memory Manager Integration**: Coordinates with CrewMemoryManager for persistent tracking across multi-agent research sessions
- **Robust Error Handling**: Comprehensive exception management with detailed logging for reliable multi-agent operations

## ESG Research Integration

### Corporate Document Discovery Pipeline
The tool specifically supports ESG research workflows by providing:
- **Corporate Website Analysis**: Fetches sustainability pages, investor relations content, and ESG disclosures
- **News Article Processing**: Extracts content from ESG news articles, press releases, and third-party assessments
- **Document Link Detection**: Automatically identifies PDF links to sustainability reports, annual reports, and corporate policies
- **Content Categorization**: Structures extracted content with metadata for relevance assessment and prioritization
- **Research Continuity**: Maintains session state to prevent duplicate processing across extended research workflows

### Multi-Agent Research Coordination
Essential component of collaborative ESG research ecosystems:
- **Cross-Agent Content Sharing**: Extracted content is available to other agents in the CrewAI research ecosystem
- **Targeted Content Discovery**: Enables focused follow-up by specialized document processing and analysis agents  
- **Research Efficiency**: Prevents duplicate content extraction efforts across multiple research agents through memory integration
- **Session Persistence**: Maintains content discovery state across extended research sessions for comprehensive coverage

## CrewAI Framework Integration

### BaseTool Implementation Architecture
The `FetchWebpageTool` follows CrewAI design patterns and best practices:
- **Pydantic Schema Validation**: Uses `FetchWebpageInput` BaseModel for type-safe parameter validation with Field descriptors
- **StandardTool Interface**: Implements required `name`, `description`, and `args_schema` attributes for seamless agent integration
- **Memory Manager Support**: Optional memory_manager parameter enables integration with persistent session tracking systems
- **Error-Safe Execution**: Comprehensive exception handling with Loguru logging ensures reliable multi-agent operations
- **URL Processing**: Advanced URL validation and parsing for robust web content extraction

### Multi-Agent System Integration
Designed for seamless integration in collaborative agent research environments:
- **Shared Resource Access**: Uses advanced web scraping capabilities from `eko.web.get.download_and_clean` for consistent behavior
- **Inter-Agent Communication**: Processed URLs are tracked globally to coordinate efforts across research agents
- **Tool Chain Integration**: Extracted content and links feed downstream tools like link extraction and document analysis agents
- **Research Workflow Support**: Enables systematic content discovery workflows with structured output for analysis agents

## Technical Architecture

### Content Processing Pipeline
Sophisticated webpage extraction and analysis workflow:
1. **Input Validation**: Validates URL format and parameters using Pydantic schemas with comprehensive error checking
2. **Duplicate Detection**: Checks memory manager for previous processing to prevent redundant work
3. **URL Analysis**: Parses and validates URLs ensuring proper scheme and domain structure
4. **Advanced Web Scraping**: Leverages `download_and_clean()` with multiple fallback strategies including ScrapingBee, Common Crawl, and Playwright
5. **Content Structuring**: Processes raw HTML into structured JSON with title, text, metadata, and links
6. **Link Extraction**: Uses regex patterns to identify PDF links and general webpage links with proper URL resolution
7. **Memory Tracking**: Updates memory manager with visited URL information for session coordination
8. **Error Recovery**: Handles network errors, parsing failures, and content access restrictions gracefully

### Web Scraping Integration
Advanced web content retrieval capabilities through `eko.web.get` module:
- **Multi-Strategy Scraping**: Integrates ScrapingBee, Common Crawl, and Playwright for reliable content access
- **Content Cleaning**: Advanced HTML processing removes navigation, advertising, and UI elements for clean text extraction
- **Metadata Extraction**: Captures comprehensive page metadata including title, locale, meta tags, and images
- **Error Resilience**: Robust handling of CAPTCHAs, rate limiting, and content access restrictions
- **Performance Optimization**: Intelligent caching and connection reuse for high-volume research operations

## System Architecture Context

### EkoIntelligence Platform Integration
The tool fits within the broader ESG analysis ecosystem:
- **Analytics Backend**: Python orchestration layer for automated ESG research and corporate analysis
- **Multi-Agent Research**: CrewAI framework enables collaborative research with specialized agents for different content types
- **Content Processing**: Extracted webpage content feeds comprehensive document analysis and ESG scoring pipelines
- **Knowledge Graph**: Content extraction contributes to corporate relationship mapping and ESG content databases
- **Research Workflow**: Critical early-stage component enabling systematic content discovery and analysis workflows

### Research Workflow Position
Foundational component in comprehensive ESG research workflows:
1. **Discovery Phase**: Webpage extraction identifies corporate ESG content and document sources
2. **Content Collection**: Structured content extraction provides clean text for analysis agents
3. **Link Identification**: PDF and document link discovery guides subsequent download and processing efforts
4. **Analysis Preparation**: Extracted content is formatted and structured for downstream ESG analysis systems
5. **Integration Phase**: Research results integrate with corporate profiles, risk assessment, and compliance systems

## Database Schema Integration

### Memory Management System
The tool coordinates with persistent memory systems:
- **Session Tracking**: Visited URLs are tracked in memory management systems for research continuity
- **Duplicate Prevention**: Database-level coordination ensures no redundant processing across research sessions
- **Research Audit**: Complete tracking of content extraction activities for research transparency and optimization
- **Performance Monitoring**: Content extraction metrics support research workflow optimization and agent coordination

### Content Pipeline Integration
Integration with document processing and analysis systems:
- **Extracted Content**: Webpage content feeds document analysis pipelines for ESG statement extraction
- **Link Discovery**: Identified PDF and document links populate download queues for processing agents
- **Research Session State**: Long-term persistence of content extraction state across multi-session research projects
- **Quality Assurance**: Content extraction metrics and success rates guide research strategy optimization

## Usage Patterns and Examples

### Basic Content Extraction
```python
# Extract content from corporate sustainability page
result = tool._run(
    url="https://company.com/sustainability/overview", 
    redo="no"
)

# Force re-extraction from previously processed page
result = tool._run(
    url="https://company.com/esg-report-summary", 
    redo="yes"
)
```

### Agent Integration Example
```python
from crewai import Agent
from crewai.tools import FetchWebpageTool

# Create content extraction agent with webpage fetching capabilities
content_agent = Agent(
    role='ESG Content Discovery Specialist',
    goal='Extract and structure ESG content from corporate websites',
    tools=[FetchWebpageTool()],
    backstory='Expert at extracting clean content from corporate ESG pages'
)
```

### Output Structure
The tool returns structured JSON containing:
- **URL**: Original webpage URL for reference and tracking
- **Title**: Extracted page title for content identification
- **Text**: Cleaned text content suitable for NLP analysis
- **Metadata**: Comprehensive metadata including meta tags, locale, and images
- **PDF Links**: Automatically extracted PDF document links for download prioritization
- **Links**: General webpage links for further exploration and analysis

## Related Components
- **Web Extraction Module** (`eko.web.get`): Advanced web scraping with ScrapingBee, Common Crawl, and Playwright integration
- **Base Module** (`tools_new/base.py`): Shared resources and dependencies for consistent tool behavior across the package
- **Link Extraction Tool** (`tools_new/extract_links.py`): Specialized link analysis and categorization for discovered content
- **Download Tool** (`tools_new/download_pdf.py`): Processes PDF documents and content identified by webpage extraction
- **Memory Manager** (`../memory.py`): CrewMemoryManager for persistent session tracking and multi-agent coordination
- **Analytics Database**: PostgreSQL schema supporting research sessions, content tracking, and analysis results

@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.crewai.com/concepts/agents CrewAI Multi-Agent Systems
@see https://docs.pydantic.dev/latest/concepts/models/ Pydantic BaseModel for Input Validation
@see https://loguru.readthedocs.io/en/stable/ Loguru Logging Framework
@see https://requests.readthedocs.io/ Python Requests Library for HTTP Operations
@see ../tools_mem/ Memory Tool Implementations for Session Persistence
@see ../../web/get.py Advanced Web Scraping Implementation with Multiple Fallback Strategies
@see ../../analysis_v2/ ESG Analysis Pipeline for Content Processing
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for intelligent webpage content extraction and structuring during ESG research workflows, with multi-agent coordination and memory management
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""
import json
import re
from typing import Type, Optional, Any
from urllib.parse import urlparse, urljoin

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.web.get import download_and_clean


class FetchWebpageInput(BaseModel):
    """Input schema for the fetch_webpage tool."""
    url: str = Field(..., description="The URL to fetch")
    redo:str = Field(..., description="Set to 'yes' to force re-fetching even if the URL was already visited")


class FetchWebpageTool(BaseTool):
    """Tool for fetching and cleaning a webpage."""
    
    name: str = "fetch_webpage"
    description: str = "Fetch and clean a webpage. Use parameter redo=True to force re-fetching a previously visited URL."
    args_schema: Type[BaseModel] = FetchWebpageInput
    _memory_manager: Optional[Any] = None

    def __init__(self, **kwargs):
        memory_manager = kwargs.pop('memory_manager', None)
        super().__init__(
            name="fetch_webpage",
            description="Fetch and clean a webpage. Use parameter redo=True to force re-fetching a previously visited URL.",
            args_schema=FetchWebpageInput,
            **kwargs,
        )
        self._memory_manager = memory_manager
    
    
    def _run(self, url: str, redo: str) -> str:
        """
        Fetch and clean a webpage.

        Args:
            url: The URL to fetch
            redo: Whether to force re-fetching even if the URL was already visited

        Returns:
            str: The cleaned webpage content
        """
        try:
            # Check if we've already visited this URL and redo is False
            if  redo != "yes" and self._memory_manager:
                memory = self._memory_manager._load_memory()
                if url in memory.get("visited_urls", []):
                    return "We've already visited this URL before. Use redo=True if you want to fetch it again."
            
            # Validate URL
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                return f"Invalid URL: {url}. Please provide a complete URL including http:// or https://"

            result = download_and_clean(url)
            if not result:
                logger.warning(f"Failed to download {url}")
                return "Failed to download the webpage."

            page_content = {
                'url': url,
                'title': result.get('title', ''),
                'text': result.get('cleaned_text', ''),
                'metadata': {
                    'meta_info': result.get('meta_info', {}),
                    'locale': result.get('locale', 'en-US'),
                    'image_url': result.get('image_url', None)
                }
            }

            # Check for PDF links in the content
            pdf_links = []
            # Simple regex to find PDF links
            pdf_pattern = r'href=[\'"]([^\'"]+\.pdf)[\'"]'
            pdf_link_matches = re.findall(pdf_pattern, result.get('original_html', '').lower())
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

            if pdf_link_matches:
                page_content['pdf_links'] = [urljoin(base_url, match) if not match.startswith('http') else match for match in pdf_link_matches]

            link_pattern = r'href=[\'"]([^\'"]+)[\'"]'
            link_matches = re.findall(link_pattern, result.get('original_html', '').lower())

            if link_matches:  # Fixed this condition (was checking pdf_link_matches)
                page_content['links'] = [urljoin(base_url, match) if not match.startswith('http') else match for match in link_matches]

            # Track this URL in the memory if we have a memory manager
            if self._memory_manager:
                self._memory_manager.track_visited_url(url)

            return json.dumps(page_content)
        except Exception as e:
            logger.error(f"Error fetching webpage: {e}")
            return f"Error fetching webpage: {str(e)}"
