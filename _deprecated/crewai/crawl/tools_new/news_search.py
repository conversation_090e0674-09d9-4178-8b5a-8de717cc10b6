"""
CrewAI News Search Tool for ESG Research and Corporate Intelligence Workflows

This module provides the `NewsSearchTool` class, a specialized CrewAI tool for searching news articles 
through the NewsAPI during automated ESG (Environmental, Social, Governance) research workflows. 
The tool serves as an intelligent news discovery agent within the EkoIntelligence ESG analysis platform, 
enabling systematic retrieval of current news and media coverage related to corporate sustainability, 
governance issues, environmental incidents, and social responsibility matters for comprehensive ESG 
assessment and greenwashing detection.

## Core Functionality

### NewsAPI Integration and Query Processing
- **Real-time News Access**: Integrates with NewsAPI.org to provide access to thousands of news sources worldwide
- **Advanced Query Filtering**: Supports date ranges, language selection, and content sorting for precise news discovery
- **Pagination Support**: Handles large result sets through configurable page size and pagination controls
- **Source Diversity**: Accesses articles from major news outlets, specialized ESG publications, and industry sources

### Search Parameter Framework
The tool implements comprehensive search customization through structured parameters:

#### Core Search Parameters:
- **Query Matching**: Flexible keyword and phrase matching with support for complex search expressions
- **Date Filtering**: Precise temporal filtering with `from_date` and `to_date` parameters (YYYY-MM-DD format)
- **Language Selection**: Multi-language support with configurable language codes (e.g., 'en', 'es', 'fr')
- **Content Sorting**: Multiple sorting options including relevancy, popularity, and publication date

#### Result Management:
- **Pagination Control**: Configurable page size (up to 100 articles) with page navigation support
- **Content Prioritization**: Relevancy-based sorting ensures most pertinent articles appear first
- **Result Formatting**: Structured output with article titles, sources, publication dates, URLs, and snippets

### Content Processing and Output Structure
1. **Parameter Validation**: Comprehensive input validation with NewsSearchInput schema using Pydantic
2. **API Authentication**: Secure NewsAPI key management through environment variable configuration
3. **Request Processing**: HTTP request handling with timeout controls and error recovery
4. **Response Parsing**: Structured JSON response processing with null-safe field extraction
5. **Content Formatting**: Human-readable output formatting with numbered articles and metadata
6. **Error Handling**: Graceful error management with informative error messages and logging

## CrewAI Framework Integration

### Tool Architecture Design
The `NewsSearchTool` implements CrewAI best practices by inheriting from `BaseTool`:
- **Pydantic Input Validation**: Uses `NewsSearchInput` schema for type-safe parameter validation
- **Standardized Tool Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **Async-Ready Design**: Compatible with CrewAI's asynchronous agent execution framework
- **Error Resilience**: Comprehensive error handling prevents agent workflow disruption

### Multi-Agent ESG Research Workflow Integration
The tool supports coordinated multi-agent ESG research scenarios:
- **Corporate Monitoring**: Agents can monitor ongoing news coverage of specific entities for sustainability issues
- **Event Detection**: Real-time detection of environmental incidents, social controversies, or governance failures
- **Trend Analysis**: Historical news analysis for identifying patterns in corporate ESG performance
- **Greenwashing Investigation**: Discovery of media coverage that contradicts corporate sustainability claims

## ESG Research Applications

### Environmental Intelligence Gathering
- **Climate Incident Monitoring**: Automated detection of environmental violations, spills, or climate-related controversies
- **Regulatory Enforcement Tracking**: Identification of EPA fines, environmental lawsuits, and regulatory actions
- **Sustainability Verification**: Media validation of corporate environmental claims and commitments

### Social Responsibility Research  
- **Labor Issues Detection**: Discovery of workplace safety incidents, labor disputes, and worker rights violations
- **Human Rights Monitoring**: Identification of discrimination lawsuits, harassment allegations, and human rights concerns
- **Community Impact Assessment**: Media coverage of corporate community relations and social responsibility initiatives

### Governance and Compliance Investigation
- **Financial Misconduct Detection**: News coverage of accounting irregularities, fraud investigations, and regulatory violations
- **Executive Accountability**: Media reporting on executive misconduct, compensation controversies, and board governance
- **Regulatory Compliance**: SEC violations, antitrust actions, and corporate governance failures

## Technical Implementation

### API Integration Architecture
- **NewsAPI.org Integration**: Leverages NewsAPI's comprehensive news aggregation platform
- **Authentication Management**: Secure API key handling via `NEWSAPI_API_KEY` environment variable
- **Request Optimization**: Efficient HTTP request handling with configurable timeout parameters
- **Rate Limiting Awareness**: Designed to work within NewsAPI rate limiting constraints

### Data Processing Pipeline
1. **Query Construction**: Dynamic parameter assembly for NewsAPI endpoint requests
2. **Response Validation**: JSON response structure validation and error detection
3. **Content Extraction**: Systematic extraction of article metadata, content, and source information
4. **Output Formatting**: Human-readable formatting with structured article presentation
5. **Error Recovery**: Graceful handling of API errors, network issues, and data inconsistencies

### Performance and Reliability Features
- **Timeout Management**: Configurable request timeout (10 seconds) prevents hanging operations
- **Error Logging**: Comprehensive error logging via Loguru for debugging and monitoring
- **Null-Safe Processing**: Robust handling of missing or incomplete article data
- **Content Truncation**: Intelligent handling of article descriptions and content snippets

## System Architecture Integration

### EkoIntelligence Platform Context
The NewsSearchTool integrates with the broader EkoIntelligence ESG analysis ecosystem:
- **Analytics Backend**: Python orchestration layer processes news discoveries for ESG flag generation
- **Database Integration**: News findings stored in analytics database for trend analysis and entity tracking  
- **Memory System**: Integration with CrewMemoryManager for persistent news discovery tracking
- **Agent Coordination**: Supports multi-agent research workflows with shared news intelligence

### Data Flow and Storage
News research workflow within the platform:
1. **Discovery Phase**: Agents use tool to identify relevant news coverage for target entities
2. **Collection Phase**: Article metadata and content collected for further analysis
3. **Processing Phase**: News content analyzed for ESG sentiment, controversy detection, and claim validation
4. **Integration Phase**: Findings integrated with corporate ESG profiles and risk assessment models

## Usage Examples and Configuration

### Basic News Search
```python
# Search for general corporate sustainability news
tool = NewsSearchTool()
results = tool.run(
    query="corporate sustainability greenwashing",
    page_size=20,
    sort_by="relevancy"
)
```

### Targeted Entity Investigation
```python
# Monitor specific company for recent controversies
tool = NewsSearchTool()
results = tool.run(
    query="ExxonMobil environmental lawsuit",
    from_date="2024-01-01",
    to_date="2024-12-31",
    language="en",
    sort_by="publishedAt"
)
```

### Multi-Language ESG Research
```python
# Search international ESG news coverage
tool = NewsSearchTool()
results = tool.run(
    query="ESG disclosure regulation",
    language="es",
    page_size=50,
    sort_by="popularity"
)
```

## Related Components and Dependencies

### Core Dependencies
- **CrewAI Framework** (`crewai.tools.BaseTool`): Multi-agent workflow orchestration and tool standardization
- **Pydantic** (`pydantic.BaseModel`, `pydantic.Field`): Type-safe input validation and data structure definition
- **Requests Library** (`requests`): HTTP client for NewsAPI integration with timeout and error handling
- **Loguru** (`loguru.logger`): Structured logging for debugging, monitoring, and error tracking

### System Integration Points
- **Memory Management**: CrewMemoryManager for persistent news discovery tracking across research sessions
- **Database Layer**: Analytics database for storing news findings and entity-news relationship mapping
- **ESG Analysis Pipeline**: Downstream processing of news content for ESG flag generation and risk scoring
- **Agent Coordination**: Integration with other CrewAI tools for comprehensive ESG research workflows

### External API Dependencies
- **NewsAPI.org**: Primary news aggregation service providing access to thousands of global news sources
- **Environment Configuration**: `NEWSAPI_API_KEY` environment variable for secure API authentication

@see https://newsapi.org/docs/endpoints/everything NewsAPI Everything Endpoint Documentation
@see https://docs.crewai.com/concepts/tools CrewAI Tools Framework Documentation
@see https://docs.pydantic.dev/ Pydantic Data Validation Library
@see https://requests.readthedocs.io/ Python Requests HTTP Library
@see https://loguru.readthedocs.io/ Loguru Logging Framework
@see ../base.py Base Module for Shared Web Crawling Resources
@see ../../../analysis_v2/ ESG Analysis Pipeline for News Content Processing
@see ../memory.py CrewMemoryManager for Persistent Session State
<AUTHOR>
@updated 2025-07-22
@description CrewAI tool for news article search and discovery in automated ESG research workflows using NewsAPI
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""
import os
from typing import Optional, Type

import requests
from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field


class NewsSearchInput(BaseModel):
    """Input schema for the search_news tool."""
    query: str = Field(..., description="The search query for news articles")
    from_date: Optional[str] = Field(None, description="Start date for news (YYYY-MM-DD)")
    to_date: Optional[str] = Field(None, description="End date for news (YYYY-MM-DD)")
    language: Optional[str] = Field('en', description="Language code for articles (e.g., en, es)")
    sort_by: Optional[str] = Field('relevancy', description="Sort by relevancy, popularity, or publishedAt")
    page_size: Optional[int] = Field(20, description="Number of articles to return (max 100)")
    page: Optional[int] = Field(1, description="Page number (starting at 1)")


class NewsSearchTool(BaseTool):  # type: ignore
    """Tool for searching news articles via NewsAPI."""
    name: str = "search_news"
    description: str = "Search for news articles using a news API"
    args_schema: Type[BaseModel] = NewsSearchInput

    def _run(
        self,
        query: str,
        from_date: Optional[str] = None,
        to_date: Optional[str] = None,
        language: Optional[str] = 'en',
        sort_by: Optional[str] = 'relevancy',
        page_size: Optional[int] = 20,
        page: Optional[int] = 1,
    ) -> str:
        """
        Search news articles for a given query and return formatted results.

        Args:
            query: Search query keywords
            from_date: Optional start date (YYYY-MM-DD)
            to_date: Optional end date (YYYY-MM-DD)
            language: Language code (default 'en')
            sort_by: One of 'relevancy', 'popularity', 'publishedAt'
            page_size: Number of articles to return (max 100)
            page: Page number to fetch

        Returns:
            str: Formatted list of news articles or error message
        """
        api_key = os.getenv('NEWSAPI_API_KEY')
        if not api_key:
            return "Error: NEWSAPI_API_KEY environment variable is not set"

        url = "https://newsapi.org/v2/everything"
        params = {
            'q': query,
            'apiKey': api_key,
            'language': language,
            'sortBy': sort_by,
            'pageSize': min(page_size or 20, 100),
            'page': max(page or 1, 1),
        }
        if from_date:
            params['from'] = from_date
        if to_date:
            params['to'] = to_date

        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 'ok':
                msg = data.get('message', 'Unknown error')
                return f"Error fetching news: {msg}"

            articles = data.get('articles', [])
            if not articles:
                return "No news articles found for the given query."

            formatted = []
            for idx, art in enumerate(articles, start=1):
                title = art.get('title') or ''
                source = art.get('source', {}).get('name', '')
                published = art.get('publishedAt', '')
                link = art.get('url', '')
                snippet = art.get('description') or art.get('content') or ''
                formatted.append(f"{idx}. {title}")
                if source:
                    formatted.append(f"   Source: {source}")
                if published:
                    formatted.append(f"   Published At: {published}")
                if link:
                    formatted.append(f"   URL: {link}")
                if snippet:
                    formatted.append(f"   Snippet: {snippet}")
                formatted.append("")
            return "\n".join(formatted)
        except Exception as e:
            logger.error(f"Error searching news: {e}")
            return f"Error searching news: {str(e)}"
