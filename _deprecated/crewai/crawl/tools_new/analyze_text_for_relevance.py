"""
CrewAI Text Relevance Analysis Tool for ESG Research Intelligence

This module provides the `AnalyzeTextForRelevanceTool` class, a specialized CrewAI tool for analyzing 
text content relevance against specific topics during automated ESG (Environmental, Social, Governance) 
research workflows within the EkoIntelligence analysis platform. The tool performs intelligent content 
filtering and relevance scoring to help AI agents identify and prioritize the most valuable text passages 
for corporate sustainability analysis and ESG documentation processing.

## Core Functionality
- **Intelligent Relevance Scoring**: Analyzes text content against specific topics using keyword-based scoring (0-10 scale)
- **Content Filtering**: Extracts relevant text excerpts and sentences containing topic-related keywords
- **Duplicate Prevention**: Uses MD5 hashing to prevent redundant analysis of previously processed text-topic combinations
- **Performance Optimization**: Implements text truncation (50,000 char limit) and efficient text sampling for hash generation
- **Flexible Re-analysis**: Supports forced re-analysis of previously processed content via 'redo' parameter

## ESG Research Integration

### Corporate Document Analysis
The tool is specifically designed to support comprehensive ESG content evaluation:
- **Sustainability Report Processing**: Identifies relevant sections in corporate sustainability documents
- **Financial Filing Analysis**: Extracts ESG-related content from SEC filings, annual reports, and investor materials
- **Policy Document Review**: Analyzes corporate policies for environmental, social, and governance commitments
- **News Article Filtering**: Evaluates news content for ESG relevance and materiality assessment

### Content Relevance Assessment
Advanced text analysis capabilities for ESG research workflows:
- **Topic-Specific Analysis**: Evaluates content relevance against specific ESG themes (climate, diversity, governance)
- **Keyword-Based Scoring**: Uses sophisticated keyword matching for relevance determination
- **Excerpt Extraction**: Identifies and extracts the most relevant text passages for further analysis
- **Materiality Assessment**: Helps agents prioritize content based on relevance scores for efficient processing

## CrewAI Framework Integration

### Tool Architecture Design
The `AnalyzeTextForRelevanceTool` follows CrewAI best practices by inheriting from `BaseTool`:
- **Pydantic Input Validation**: Uses `AnalyzeTextForRelevanceInput` schema for type-safe parameter handling
- **Standardized Tool Interface**: Implements required `name`, `description`, and `args_schema` attributes
- **Memory Management**: Integrates with optional memory manager for persistent caching across sessions
- **Caching Strategy**: Maintains in-memory cache of processed text hashes to prevent duplicate work
- **Error Handling**: Comprehensive exception handling with detailed logging via Loguru framework

### Multi-Agent Research Workflows
In collaborative ESG research environments, the text analysis tool enables:
- **Content Preprocessing**: Filters large document collections to identify relevant passages for agent analysis
- **Research Efficiency**: Prevents redundant text analysis across multiple agents through intelligent caching
- **Quality Control**: Provides relevance scores to help agents focus on the most valuable content
- **Workflow Optimization**: Reduces processing time by eliminating irrelevant text from analysis pipelines

## Technical Architecture

### Text Processing Pipeline
The tool implements a sophisticated text analysis workflow:
1. **Input Validation**: Validates text content and topic parameters using Pydantic schemas
2. **Duplicate Detection**: Generates MD5 hash of text sample and topic for deduplication
3. **Content Preparation**: Truncates oversized content (>50k chars) while preserving analysis quality
4. **Keyword Analysis**: Performs case-insensitive keyword matching against topic terms
5. **Relevance Scoring**: Calculates numerical relevance score based on keyword occurrence frequency
6. **Excerpt Extraction**: Identifies and extracts sentences containing relevant keywords
7. **Result Formatting**: Returns structured analysis with score, explanation, and key excerpts

### Performance Optimization Features
- **Text Sampling**: Uses first 1000 characters for hash generation to optimize performance
- **Length Management**: Automatic truncation of content exceeding 50,000 characters
- **Memory Efficiency**: Maintains lightweight hash cache for processed content tracking
- **Configurable Re-analysis**: Optional forced re-analysis for updated content or changed requirements

## System Integration

### File Storage and Caching
The tool operates within the EkoIntelligence file storage architecture:
- **Memory Files**: Interfaces with memory manager for session persistence if available
- **Cache Management**: Maintains set of processed text hashes for efficient duplicate detection
- **Content Handling**: Processes text from various sources (web pages, PDFs, documents)

### Database Independence
While designed for database-backed ESG analysis, the tool operates independently:
- **No Direct Database Access**: Focuses on text processing without database dependencies
- **Flexible Integration**: Can be combined with database tools for comprehensive document analysis
- **Stateless Operation**: Each analysis call is self-contained for reliable distributed processing

@see https://github.com/crewaiinc/crewai/blob/main/docs/concepts/tools.md CrewAI Tools Documentation
@see https://docs.pydantic.dev/latest/concepts/models/ Pydantic BaseModel Documentation  
@see https://loguru.readthedocs.io/en/stable/ Loguru Logging Framework
<AUTHOR>
@updated 2025-07-22
@docgen doc-by-claude

(c) All rights reserved ekoIntelligence 2025
"""

import hashlib
from typing import Type, Optional, Any, Set

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field


class AnalyzeTextForRelevanceInput(BaseModel):
    """Input schema for the analyze_text_for_relevance tool."""
    text: str = Field(..., description="The text to analyze")
    topic: str = Field(..., description="The topic to analyze for relevance")
    redo:str = Field(..., description="Set to 'yes'' to force re-analysis even if this text was already analyzed")


class AnalyzeTextForRelevanceTool(BaseTool):
    """Tool for analyzing text for relevance to a specific topic."""
    
    name: str = "analyze_text_for_relevance"
    description: str = "Analyze text for relevance to a specific topic. Use parameter redo=True to force re-analysis of previously analyzed text."
    args_schema: Type[BaseModel] = AnalyzeTextForRelevanceInput
    _memory_manager: Optional[Any] = None
    
    def __init__(self, **kwargs):
        memory_manager = kwargs.pop('memory_manager')
        super().__init__(**kwargs)
        self._memory_manager = memory_manager
        # Cache for text hashes we've already processed
        self._processed_text_hashes: Set[str] = set()
    
    def _get_text_hash(self, text: str, topic: str) -> str:
        """Generate a hash for the text+topic combo to identify if we've seen it before"""
        # Use first 1000 chars to keep hash computation fast while still being unique enough
        text_sample = text[:1000] if len(text) > 1000 else text
        combined = f"{text_sample}:{topic}"
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _run(self, text: str, topic: str, redo: str) -> str:
        """
        Analyze text for relevance to a specific topic.

        Args:
            text: The text to analyze
            topic: The topic to analyze for relevance
            redo: Whether to force re-analysis even if this text was already analyzed

        Returns:
            str: The analysis results
        """
        try:
            # Check if we've already processed this text with the same parameters
            text_hash = self._get_text_hash(text, topic)
            if  redo !='yes' and text_hash in self._processed_text_hashes:
                return "We've already analyzed this text for this topic before. Use redo=True if you want to analyze it again."
            
            # Add to our processed texts
            self._processed_text_hashes.add(text_hash)
            
            logger.info(f"Analyzing text relevance for topic: {topic}")

            # Truncate if too long
            if len(text) > 50000:
                text = text[:50000] + "...\n[Content truncated due to length]"

            # Simple keyword-based relevance analysis
            topic_keywords = topic.lower().split()

            # Count keyword occurrences
            keyword_count = sum(1 for keyword in topic_keywords if keyword.lower() in text.lower())

            # Calculate a simple relevance score (0-10)
            relevance_score = min(10, keyword_count * 2)

            # Find relevant excerpts
            sentences = text.split(". ")
            relevant_excerpts = [s for s in sentences if any(k.lower() in s.lower() for k in topic_keywords)]

            # Format results
            result = f"Relevance Analysis for Topic: {topic}\n\n"
            result += f"Relevance Score: {relevance_score}/10\n\n"

            if relevance_score > 5:
                result += "Explanation: The text contains multiple references to the topic keywords.\n\n"
            else:
                result += "Explanation: The text contains few or no references to the topic keywords.\n\n"

            result += "Key Excerpts:\n"
            for i, excerpt in enumerate(relevant_excerpts[:5], 1):
                result += f"{i}. {excerpt}.\n"

            return result
        except Exception as e:
            logger.error(f"Error analyzing text relevance: {e}")
            return f"Error analyzing text relevance: {str(e)}"
