"""
CrewAI Web Crawling Orchestrator for ESG Research and Corporate Analysis

This module implements the primary orchestration layer for the EkoIntelligence platform's
autonomous multi-agent web crawling system using CrewAI. It provides sophisticated coordination
of specialized AI agents that systematically discover, analyze, and report on corporate ESG
practices, sustainability claims, and potential greenwashing through comprehensive web research
and document analysis across multiple data sources.

## Core Purpose
The module establishes a **persistent multi-agent crawling framework** that orchestrates
CrewAI-powered agents to conduct autonomous ESG research with session persistence, memory
management, and comprehensive monitoring. The system is designed to handle long-running
research operations that can span hours or days, with full session recovery capabilities
and intelligent continuation from interruption points.

## Key Features

### Persistent Web Crawling Architecture
- **Session Persistence**: Full session state management with database and file-based storage
- **Resumable Operations**: Intelligent continuation from interruption points using task indexing
- **Memory Management**: Comprehensive memory manager integration for cross-session coordination
- **Progress Tracking**: Real-time monitoring of research progress with detailed status reporting

### Multi-Agent Orchestration System
- **Specialized Agent Teams**: Coordinates ResearchAgent, JournalistAgent, AnalysisAgent, and SummaryAgent
- **Hierarchical Task Management**: Uses CrewAI hierarchical process with dedicated manager LLM
- **Tool Distribution**: Assigns role-appropriate tools from web and analyst tool collections
- **Cross-Agent Communication**: Enables collaborative research through shared memory and context

### Enterprise-Grade Monitoring
- **OpenTelemetry Integration**: Full observability with performance metrics and trace analysis
- **Event Logging**: Structured event tracking for debugging and performance optimization
- **Task-Level Monitoring**: Individual task completion tracking with result persistence
- **Agent Performance Analytics**: Tool usage tracking and execution efficiency monitoring

### Intelligent Research Management
- **Adaptive Iterations**: Configurable research depth with intelligent stopping criteria
- **Rate Limiting**: Built-in request throttling to respect web service limitations
- **Error Recovery**: Comprehensive error handling with detailed logging and recovery strategies
- **Result Aggregation**: Sophisticated insight categorization and behavioral pattern analysis

## System Architecture

### PersistentWebCrawlCrew Class
The core orchestration class that manages the entire crawling lifecycle:

```python
crew = PersistentWebCrawlCrew(
    company_name="Target Corporation",
    max_iterations=50,        # Maximum research depth
    max_rpm=20,              # Rate limiting for API calls  
    session_id="custom_123"  # Optional session persistence
)
results = crew.run()  # Execute autonomous research
```

### Task Management System
- **Research Tasks**: Primary document discovery and web crawling operations
- **Analysis Tasks**: ESG insight extraction and content categorization
- **Summary Tasks**: Result consolidation and report generation
- **Adaptive Execution**: Dynamic task prioritization based on research progress

### Memory Manager Integration
Deep integration with `CrewMemoryManager` for:
- **Persistent Sessions**: Research can span multiple execution periods
- **Progress Tracking**: Detailed monitoring of URLs visited, files downloaded, insights discovered
- **Duplicate Prevention**: Intelligent deduplication to prevent redundant processing
- **Context Preservation**: Maintains research context across session interruptions

### Agent Coordination Framework
```python
# Agent initialization with specialized tools
research_agent = ResearchAgent(tools=get_web_tools(memory_manager))
journalist_agent = JournalistAgent(tools=get_web_tools(memory_manager)) 
analysis_agent = AnalysisAgent(tools=get_analyst_tools(memory_manager))
summary_agent = SummaryAgent(tools=get_web_tools(memory_manager))
```

## Technology Integration

### CrewAI Framework
- **Multi-Agent Collaboration**: Leverages CrewAI's agent orchestration system
- **Hierarchical Processing**: Manager-led task delegation with specialized agent roles
- **Tool Integration**: Seamless integration with custom ESG research tools
- **Memory Systems**: Built-in memory management with custom extensions

### LLM Model Strategy
```python
# Primary management model (complex orchestration decisions)
planning_llm = LLM(model="gpt-4.1-2025-04-14", temperature=0.2, max_tokens=16000)

# Specialized agent models configured per agent type
# - ResearchAgent: Gemini 2.5 Pro for complex research decisions
# - JournalistAgent: Gemini 2.5 Pro for investigative reasoning
# - AnalysisAgent: Gemini 2.5 Pro for ESG analysis
# - SummaryAgent: Gemini 2.0 Flash Lite for efficient summarization
```

### Database Integration
The crawler integrates deeply with the EkoIntelligence database system:
- **Analytics Database**: Session tracking in `agent_sessions`, `agent_insights` tables
- **Monitoring Tables**: Performance data in `agent_tool_usage`, `agent_execution_events`
- **Result Storage**: Final research outputs in `agent_reports` table
- **Memory Persistence**: Session state in both file system and database

### Observability and Monitoring
- **OpenTelemetry**: Full distributed tracing with custom instrumentation
- **AgentOps Integration**: Agent-specific performance monitoring and analytics
- **Event Logging**: Comprehensive structured logging with loguru integration
- **Real-time Metrics**: Live monitoring of research progress and agent performance

## Research Workflow

### Autonomous Research Process
1. **Session Initialization**: Create or resume research session with memory recovery
2. **Agent Coordination**: Deploy specialized agents with appropriate tool assignments
3. **Task Execution**: Sequential or hierarchical task processing with progress tracking
4. **Memory Management**: Continuous state persistence and context maintenance
5. **Result Aggregation**: Insight categorization and comprehensive report generation

### Progress Tracking System
```python
# Progress summary includes:
- visited_urls_count: Number of URLs processed
- downloaded_files_count: Documents discovered and analyzed
- insights_count: ESG insights extracted and categorized
- insights_by_category: Structured insight organization
- session_duration: Research session timing analytics
```

### Error Handling and Recovery
- **Fail-Fast Architecture**: Immediate error propagation following EkoIntelligence standards
- **Comprehensive Logging**: Full error context with stack traces using loguru
- **Session Recovery**: Ability to resume from any interruption point
- **Graceful Degradation**: Continue research with available tools when possible

## ESG Analysis Integration

### Research Domain Focus
- **ESG Document Discovery**: Systematic identification of sustainability reports
- **Corporate Behavior Analysis**: Investigation of actual vs. claimed corporate practices
- **Greenwashing Detection**: Analysis of potential misleading sustainability claims
- **Regulatory Compliance**: Assessment of ESG regulatory adherence
- **Stakeholder Impact**: Analysis of corporate impact on various stakeholder groups

### Result Integration Pipeline
Research outputs feed directly into the broader EkoIntelligence platform:
- **Flag Generation**: Research findings contribute to automated ESG flag creation
- **Risk Scoring**: Document analysis informs Bayesian risk assessment algorithms
- **Claims Analysis**: Corporate statement verification against discovered evidence
- **Entity Relationship Mapping**: Corporate network and subsidiary relationship discovery

## Performance and Scalability

### Resource Optimization
- **Model Selection**: Appropriate LLM models selected based on task complexity
- **Connection Pooling**: Efficient database connection management
- **Memory Efficiency**: Shared tool instances prevent resource duplication
- **Rate Limiting**: Configurable request throttling for external service protection

### Monitoring and Analytics
- **Session Analytics**: Comprehensive research session performance tracking
- **Agent Efficiency**: Individual agent performance and tool usage analytics
- **Resource Usage**: Memory, database, and API consumption monitoring
- **Quality Metrics**: Research effectiveness and insight discovery rates

## Security and Reliability

### Data Protection
- **Secure Storage**: Research data encrypted at rest and in transit
- **Access Control**: Database-level security with role-based access
- **Audit Trails**: Complete research session audit logging
- **Privacy Compliance**: Adherence to data protection regulations

### System Reliability
- **Fault Tolerance**: Robust error handling with automatic recovery
- **Data Consistency**: Transactional database operations for state management
- **Backup Systems**: Multiple persistence layers for data protection
- **Health Monitoring**: Continuous system health and performance monitoring

## Usage Examples

### Basic Autonomous Research
```python
from eko.agent.crewai.crawl.crawler import run_persistent_crew

# Execute comprehensive ESG research
results = run_persistent_crew(
    company_name="Apple Inc.",
    max_iterations=30,
    max_rpm=15
)

print(f"Discovered {results['insights_count']} ESG insights")
print(f"Analyzed {results['visited_urls_count']} URLs")
```

### Session Recovery and Continuation
```python
# Resume previous research session
results = run_persistent_crew(
    company_name="Apple Inc.",
    max_iterations=30,
    session_id="research_apple_20250123"  # Continue from previous session
)
```

### Advanced Configuration
```python
# Initialize with full control
crew = PersistentWebCrawlCrew(
    company_name="Target Corporation",
    max_iterations=100,      # Extended research depth
    max_rpm=10,             # Conservative rate limiting
    session_id="deep_research_session"
)

# Execute with monitoring
results = crew.run()

# Access detailed progress information
status = crew.get_status()
print(f"Research Progress: {status['last_position']}")
```

## System Dependencies

### Core Framework Dependencies
- **CrewAI**: Multi-agent orchestration and collaboration framework
- **LiteLLM**: Model abstraction supporting OpenAI, Google, Anthropic providers
- **OpenTelemetry**: Distributed tracing and observability infrastructure
- **AgentOps**: Agent-specific monitoring and performance analytics

### EkoIntelligence Platform Integration
- **Database Layer**: PostgreSQL with specialized ESG analysis schemas
- **Memory Management**: `CrewMemoryManager` for session persistence
- **Tool Framework**: Specialized web research and ESG analysis tools
- **Monitoring Infrastructure**: Comprehensive logging and event tracking systems

### External Service Dependencies
- **LLM Providers**: OpenAI GPT models, Google Gemini, Anthropic Claude
- **Web Services**: Search APIs, document processing services
- **Database Systems**: PostgreSQL for analytics and customer data storage

@see https://crewai.com/ CrewAI Multi-Agent Framework Documentation
@see https://opentelemetry.io/ OpenTelemetry Observability Platform
@see https://www.litellm.ai/ LiteLLM Multi-Provider Model Access
@see ./agents.py Specialized ESG research agent definitions
@see ./memory.py CrewMemoryManager session persistence system
@see ./tasks.py Task definition and management system
@see ./tools_new/all_tools.py Comprehensive ESG research tool collection
@see ./monitoring/ OpenTelemetry and AgentOps integration modules
@see ../../db/data/ Database access layer and persistence management
@see ../../analysis_v2/ Core ESG analysis pipeline integration
<AUTHOR>
@updated 2025-07-23
@description CrewAI web crawling orchestrator for autonomous ESG research with persistent sessions, multi-agent coordination, and comprehensive monitoring
@example ```python
# Execute autonomous ESG research with session persistence
crew = PersistentWebCrawlCrew("Tesla Inc.", max_iterations=50)
results = crew.run()
print(f"Research complete: {results['insights_count']} insights discovered")
```
@docgen doc-by-claude
@copyright (c) All rights reserved ekoIntelligence 2025
"""
import time
from datetime import datetime
from typing import Dict, Optional, Any

from crewai import Crew, Process, LLM
from crewai.task import Task
from crewai.utilities.constants import NOT_SPECIFIED
from loguru import logger

from eko.agent.crewai.crawl.agents import ResearchAgent, JournalistAgent, AnalysisAgent, SummaryAgent
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tasks import create_research_tasks, create_analysis_tasks, create_summary_task
from eko.agent.crewai.crawl.tools_new.all_tools import get_web_tools, get_analyst_tools
from eko.agent.crewai.observability import setup_agentops, agentops_manager
from eko.agent.crewai.monitoring.event_logger import create_event_logger

# Import directly from the monitoring.py file
from eko.agent.crewai.monitoring import AgentMonitoringService, setup_opentelemetry_monitoring, wrap_tools_with_monitoring

# planning_llm = LLM(
#     model="gemini/gemini-2.5-pro-exp-03-25",
#     temperature=0.2,
#     api_key="AIzaSyCffot1AZ2_Jgbrjbo60kLQvFnpKbK5_vE",
# )

planning_llm = LLM(model="gpt-4.1-2025-04-14", temperature=0.2, max_tokens=16000)


class PersistentWebCrawlCrew:
    """
    CrewAI-based web crawling crew for ESG research with persistence.

    This class orchestrates a multi-agent system using CrewAI for autonomous web crawling
    to gather information about target companies, with a focus on ESG behavior. It includes
    persistence mechanisms to continue from previous runs.
    """

    def __init__(self, company_name: str, max_iterations: int = 50, max_rpm: int = 20, session_id: Optional[str] = None):
        """
        Initialize the persistent web crawling crew.

        Args:
            company_name: The name of the target company
            max_iterations: Maximum number of iterations for the crew execution (default: 50)
            max_rpm: Maximum requests per minute (default: 20)
            session_id: Optional session ID for persistence
        """
        self.company_name = company_name
        self.session_id = session_id or f"crawl_{int(time.time())}"
        self.memory_manager = CrewMemoryManager(company_name, self.session_id)
        self.max_iterations = max_iterations
        self.max_rpm = max_rpm
        
        # Initialize event logger for monitoring
        self.event_logger = create_event_logger(self.session_id)
        
        # Initialize OpenTelemetry monitoring service
        self.monitoring_service = setup_opentelemetry_monitoring(self.session_id)

        # Initialize agents with tools
        logger.info(f"Creating agents")
        web_tools = get_web_tools(self.memory_manager)
        analyst_tools = get_analyst_tools(self.memory_manager)
        
        # Initialize agents with standard tools (OpenTelemetry will capture tool usage)
        self.research_agent = ResearchAgent(web_tools)
        self.journalist_agent = JournalistAgent(web_tools)
        self.analysis_agent = AnalysisAgent(analyst_tools)
        self.summary_agent = SummaryAgent(web_tools)

        # Initialize tasks with the specified max_iterations
        logger.info(f"Creating tasks with max_iterations={self.max_iterations}")
        
        # Get previous memory to determine where to start
        memory = self.memory_manager.get_status()
        last_position = memory.get("last_position", {})
        
        # Determine task starting point
        start_task_index = last_position.get("task_index", 0)
        
        # Create all tasks (to maintain structure)
        self.research_tasks = create_research_tasks(self.memory_manager, company_name, self.max_iterations)
        self.analysis_tasks = create_analysis_tasks(self.memory_manager, company_name, self.max_iterations)
        self.summary_task = create_summary_task(self.memory_manager, company_name, self.max_iterations)
        
        # Generate a progress summary to inform agents of current state
        progress_summary = self.memory_manager.get_crawl_progress_summary()
        
        # Add progress summary to agent context through task input
        self._update_task_inputs_with_progress(progress_summary, start_task_index)


        # Initialize crew
        self.crew = self._create_crew(start_task_index)

        logger.info(f"Initialized PersistentWebCrawlCrew for {company_name} with session ID {self.session_id}")
        
    def _update_task_inputs_with_progress(self, progress_summary: str, start_task_index: int):
        """
        Update task input contexts with progress summary.
        
        Args:
            progress_summary: The progress summary to add to task inputs
            start_task_index: The index of the task to start from
        """
        # For all tasks, add progress info to context
        all_tasks = self.research_tasks + self.analysis_tasks + [self.summary_task]
        
        for i, task in enumerate(all_tasks):
            # Update task description to include progress info
            original_description = task.description
            
            # Only add progress summary to the first task we'll be executing
            if i == start_task_index:
                task.description = f"""
{original_description}

IMPORTANT - CURRENT CRAWL PROGRESS:
{progress_summary}

Continue the crawl from where we left off. Do not revisit URLs we've already processed.
"""
            
            # Also store the task index for tracking
            if task.context is NOT_SPECIFIED:
                task.context = {}
            task.context["task_index"] = i

    def _create_crew(self, start_task_index: int = 0) -> Crew:
        """
        Create the CrewAI crew with agents and tasks.

        Args:
            start_task_index: The index of the task to start from (for resuming)
            
        Returns:
            Crew: The configured CrewAI crew
        """
        # Get all tasks
        all_tasks = self.research_tasks + self.analysis_tasks + [self.summary_task]
        
        # Only include tasks from the starting point onwards
        tasks_to_execute = all_tasks[start_task_index:]
        
        if start_task_index > 0:
            logger.info(f"Resuming from task {start_task_index} of {len(all_tasks)}")
        
        # Disable planning mode to avoid LLM formatting issues; execute tasks sequentially
        crew = Crew(
            agents=[
                self.research_agent,
                self.journalist_agent,
                self.analysis_agent,
                self.summary_agent
            ],
            tasks=tasks_to_execute,
            memory=False, # We're managing memory ourselves
            verbose=True,
            process=Process.hierarchical,
            manager_llm=planning_llm,
            max_rpm=self.max_rpm,
            # planning=True,
            # planning_llm=planning_llm,
            # Add a callback to track task completions
            callbacks=[self._task_callback]
        )
        return crew
    
    def _task_callback(self, task: Task, result: Dict[str, Any]):
        """
        Callback for task completion.
        
        Args:
            task: The task that was completed
            result: The result of the task
        """
        # Extract task index from context
        if task.context is NOT_SPECIFIED:
            task_index = -1
        else:
            task_index = task.context.get("task_index", -1)
        
        if task_index >= 0:
            # Track task completion in memory manager
            self.memory_manager.track_task_completion(task.description.split("\n")[0], result, task_index)
            split = task.description.split('\n')
            task_name = split[0]
            logger.info(f"Tracked completion of task {task_index}: {task_name}")
            
            # Log task completion to monitoring system
            agent_name = getattr(task, 'agent_name', 'unknown')
            self.monitoring_service.log_task_complete(
                agent_name=agent_name,
                task_name=task_name,
                result=result
            )

    def run(self) -> Dict[str, Any]:
        """
        Run the web crawling crew to research the target company.

        Returns:
            Dict[str, Any]: The results of the research
        """
        logger.info(f"Starting persistent web crawling crew for {self.company_name}")
        start_time = datetime.now()
        
        # Log session start
        self.monitoring_service.log_event(
            agent_name="WebCrawlCrew",
            event_type="task_start",
            event_data={
                "company_name": self.company_name,
                "max_iterations": self.max_iterations,
                "start_time": start_time.isoformat()
            }
        )

        try:
            # Run the crew
            result = self.crew.kickoff()

            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(f"Web crawling crew completed in {duration}")
            
            # Get insights from memory manager
            memory = self.memory_manager.get_status()
            insights = memory.get("insights", [])
            
            # Organize insights by category and behavior type
            insights_by_category = {}
            for insight in insights:
                category = insight.get("category", "unknown")
                behavior_type = insight.get("behavior_type", "unknown")
                
                if category not in insights_by_category:
                    insights_by_category[category] = {}
                    
                if behavior_type not in insights_by_category[category]:
                    insights_by_category[category][behavior_type] = []
                    
                insights_by_category[category][behavior_type].append(insight)
            
            # Build final result object with counts
            final_result = {
                "company_name": self.company_name,
                "session_id": self.session_id,
                "summary": result,
                "insights_count": len(insights),
                "visited_urls_count": len(memory.get("visited_urls", [])),
                "downloaded_files_count": len(memory.get("downloaded_files", [])),
                "insights_by_category": insights_by_category,
                "duration": str(duration),
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "status": "completed"
            }
            
            # Save final result to memory manager
            self.memory_manager.save_final_result(final_result)
            
            # Log session completion
            self.monitoring_service.log_event(
                agent_name="WebCrawlCrew",
                event_type="task_complete",
                event_data={
                    "insights_count": len(insights),
                    "visited_urls_count": len(memory.get("visited_urls", [])),
                    "downloaded_files_count": len(memory.get("downloaded_files", [])),
                    "duration_seconds": duration.total_seconds(),
                    "status": "completed"
                }
            )

            return final_result
        except Exception as e:
            logger.exception(f"Error running web crawling crew: {e}")
            
            # Log error event
            self.event_logger.log_error(
                agent_name="WebCrawlCrew",
                error_message=str(e),
                error_details={"traceback": str(e)}
            )
            raise

    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the web crawling crew.

        Returns:
            Dict[str, Any]: The current status
        """
        return self.memory_manager.get_status()


def run_persistent_crew(company_name: str, max_iterations: int = 50, max_rpm: int = 20, session_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Run a persistent web crawling crew for the specified company.

    This function creates and runs a PersistentWebCrawlCrew that can resume from previous runs.
    If session_id is provided, it will attempt to continue from the previous session.

    Args:
        company_name: The name of the target company
        max_iterations: Maximum number of iterations for the crew execution (default: 50)
        max_rpm: Maximum requests per minute (default: 20)
        session_id: Optional session ID for persistence

    Returns:
        Dict[str, Any]: The results of the research
    """
    crew = PersistentWebCrawlCrew(company_name, max_iterations, max_rpm, session_id)
    return crew.run()

def run_crew(company_name: str, max_iterations: int = 50, max_rpm: int = 20, session_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Run a web crawling crew for the specified company.

    Args:
        company_name: The name of the target company
        max_iterations: Maximum number of iterations for the crew execution (default: 50)
        max_rpm: Maximum requests per minute (default: 20)
        session_id: Optional session ID for persistence

    Returns:
        Dict[str, Any]: The results of the research
    """
    # For backward compatibility, use the persistent crew by default
    return run_persistent_crew(company_name, max_iterations, max_rpm, session_id)
