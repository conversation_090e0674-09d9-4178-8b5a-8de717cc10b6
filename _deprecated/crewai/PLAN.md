CrewAI Dashboard - Simple SSR Monitoring Dashboard

Overview

Create a Next.js dashboard app that connects directly to the analytics database to monitor CrewAI agent activities. No APIs, just SSR pages that refresh to show live data.

Database Schema (New Tables)

 1. agent_execution_events

 CREATE TABLE agent_execution_events (
     id SERIAL PRIMARY KEY,
     session_id TEXT NOT NULL,
     agent_name TEXT NOT NULL,
     event_type TEXT NOT NULL, -- 'task_start', 'task_complete', 'tool_call', 'llm_call', 'decision', 'error'
     event_timestamp TIMESTAMP DEFAULT NOW(),
     event_data JSONB, -- All detailed event info
     task_name TEXT,
     tool_name TEXT,
     run_id INTEGER
 );

 2. agent_llm_calls

 CREATE TABLE agent_llm_calls (
     id SERIAL PRIMARY KEY,
     session_id TEXT NOT NULL,
     agent_name TEXT NOT NULL,
     model_name TEXT,
     prompt_tokens INTEGER,
     completion_tokens INTEGER,
     total_tokens INTEGER,
     cost_usd DECIMAL(10,4),
     call_timestamp TIMESTAMP DEFAULT NOW(),
     request_data JSONB, -- Prompt, parameters
     response_data JSONB, -- Response content
     duration_ms INTEGER,
     run_id INTEGER
 );

 3. agent_tool_usage

 CREATE TABLE agent_tool_usage (
     id SERIAL PRIMARY KEY,
     session_id TEXT NOT NULL,
     agent_name TEXT NOT NULL,
     tool_name TEXT NOT NULL,
     tool_input JSONB,
     tool_output JSONB,
     success BOOLEAN,
     error_message TEXT,
     call_timestamp TIMESTAMP DEFAULT NOW(),
     duration_ms INTEGER,
     run_id INTEGER
 );

 Frontend App Structure

 /apps/agent-dashboard/

 - Next.js 14 app with SSR
 - Direct database connections in server components
 - Auto-refresh client components for live view

 Key Pages:

 1. /dashboard - Main overview with live session list
 2. /session/[id] - Detailed session view with event timeline
 3. /agents - Agent performance analytics
 4. /costs - LLM cost tracking

 Implementation Plan

 Phase 1: Database Integration

 1. Create new database tables with proper indexes
 2. Instrument CrewAI crawler with event logging
 3. Add LLM call tracking to existing agents
 4. Update memory manager to log structured events

 Phase 2: Dashboard App

 1. Create Next.js app in /apps/agent-dashboard/
 2. Server components for database queries
 3. Client components for auto-refresh functionality
 4. Glass-morphism UI following existing design system

 Phase 3: Event Tracking

 1. Modify PersistentWebCrawlCrew to log events
 2. Add hooks to agent actions and tool calls
 3. Track task progress and decision points
 4. Capture errors and performance metrics

 Technical Approach

 - SSR Only: No API routes, direct database access in server components
 - Auto-refresh: Client-side setInterval to refresh page data
 - Live Toggle: Switch to enable/disable auto-refresh
 - JSONB Storage: Flexible event data storage with GIN indexes
 - Drill-down: Click events to see full details in expanded view
